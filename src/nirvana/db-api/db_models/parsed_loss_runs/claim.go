// Code generated by SQLBoiler (https://github.com/volatiletech/sqlboiler). DO NOT EDIT.
// This file is meant to be re-generated in place and/or deleted at any time.

package parsed_loss_runs

import (
	"context"
	"database/sql"
	"fmt"
	"reflect"
	"strconv"
	"strings"
	"sync"
	"time"

	"github.com/friendsofgo/errors"
	"github.com/volatiletech/null/v8"
	"github.com/volatiletech/sqlboiler/v4/boil"
	"github.com/volatiletech/sqlboiler/v4/queries"
	"github.com/volatiletech/sqlboiler/v4/queries/qm"
	"github.com/volatiletech/sqlboiler/v4/queries/qmhelper"
	"github.com/volatiletech/strmangle"
)

// Claim is an object representing the database table.
type Claim struct {
	ID                     string      `boil:"id" json:"id" toml:"id" yaml:"id"`
	DocumentID             string      `boil:"document_id" json:"document_id" toml:"document_id" yaml:"document_id"`
	CreatedAt              time.Time   `boil:"created_at" json:"created_at" toml:"created_at" yaml:"created_at"`
	ClaimSN                int         `boil:"claim_sn" json:"claim_sn" toml:"claim_sn" yaml:"claim_sn"`
	PolicySN               int         `boil:"policy_sn" json:"policy_sn" toml:"policy_sn" yaml:"policy_sn"`
	ClaimID                null.String `boil:"claim_id" json:"claim_id,omitempty" toml:"claim_id" yaml:"claim_id,omitempty"`
	OccurrenceID           string      `boil:"occurrence_id" json:"occurrence_id" toml:"occurrence_id" yaml:"occurrence_id"`
	DateOfLoss             null.Time   `boil:"date_of_loss" json:"date_of_loss,omitempty" toml:"date_of_loss" yaml:"date_of_loss,omitempty"`
	DateReported           null.Time   `boil:"date_reported" json:"date_reported,omitempty" toml:"date_reported" yaml:"date_reported,omitempty"`
	TimeOfLoss             null.Time   `boil:"time_of_loss" json:"time_of_loss,omitempty" toml:"time_of_loss" yaml:"time_of_loss,omitempty"`
	ClosedDate             null.Time   `boil:"closed_date" json:"closed_date,omitempty" toml:"closed_date" yaml:"closed_date,omitempty"`
	CauseOfLossSummary     null.String `boil:"cause_of_loss_summary" json:"cause_of_loss_summary,omitempty" toml:"cause_of_loss_summary" yaml:"cause_of_loss_summary,omitempty"`
	CauseOfLossDescription null.String `boil:"cause_of_loss_description" json:"cause_of_loss_description,omitempty" toml:"cause_of_loss_description" yaml:"cause_of_loss_description,omitempty"`
	Vin                    null.String `boil:"vin" json:"vin,omitempty" toml:"vin" yaml:"vin,omitempty"`
	Type                   null.String `boil:"type" json:"type,omitempty" toml:"type" yaml:"type,omitempty"`
	DriverID               null.String `boil:"driver_id" json:"driver_id,omitempty" toml:"driver_id" yaml:"driver_id,omitempty"`
	Name                   null.String `boil:"name" json:"name,omitempty" toml:"name" yaml:"name,omitempty"`
	Age                    null.Int    `boil:"age" json:"age,omitempty" toml:"age" yaml:"age,omitempty"`
	Gender                 null.String `boil:"gender" json:"gender,omitempty" toml:"gender" yaml:"gender,omitempty"`
	Dob                    null.Time   `boil:"dob" json:"dob,omitempty" toml:"dob" yaml:"dob,omitempty"`
	HiringDate             null.Time   `boil:"hiring_date" json:"hiring_date,omitempty" toml:"hiring_date" yaml:"hiring_date,omitempty"`
	Street                 null.String `boil:"street" json:"street,omitempty" toml:"street" yaml:"street,omitempty"`
	CityCounty             null.String `boil:"city_county" json:"city_county,omitempty" toml:"city_county" yaml:"city_county,omitempty"`
	State                  null.String `boil:"state" json:"state,omitempty" toml:"state" yaml:"state,omitempty"`
	Zip                    null.String `boil:"zip" json:"zip,omitempty" toml:"zip" yaml:"zip,omitempty"`
	LossLocation           null.String `boil:"loss_location" json:"loss_location,omitempty" toml:"loss_location" yaml:"loss_location,omitempty"`
	ClaimStatus            null.String `boil:"claim_status" json:"claim_status,omitempty" toml:"claim_status" yaml:"claim_status,omitempty"`

	R *claimR `boil:"" json:"" toml:"" yaml:""`
	L claimL  `boil:"-" json:"-" toml:"-" yaml:"-"`
}

var ClaimColumns = struct {
	ID                     string
	DocumentID             string
	CreatedAt              string
	ClaimSN                string
	PolicySN               string
	ClaimID                string
	OccurrenceID           string
	DateOfLoss             string
	DateReported           string
	TimeOfLoss             string
	ClosedDate             string
	CauseOfLossSummary     string
	CauseOfLossDescription string
	Vin                    string
	Type                   string
	DriverID               string
	Name                   string
	Age                    string
	Gender                 string
	Dob                    string
	HiringDate             string
	Street                 string
	CityCounty             string
	State                  string
	Zip                    string
	LossLocation           string
	ClaimStatus            string
}{
	ID:                     "id",
	DocumentID:             "document_id",
	CreatedAt:              "created_at",
	ClaimSN:                "claim_sn",
	PolicySN:               "policy_sn",
	ClaimID:                "claim_id",
	OccurrenceID:           "occurrence_id",
	DateOfLoss:             "date_of_loss",
	DateReported:           "date_reported",
	TimeOfLoss:             "time_of_loss",
	ClosedDate:             "closed_date",
	CauseOfLossSummary:     "cause_of_loss_summary",
	CauseOfLossDescription: "cause_of_loss_description",
	Vin:                    "vin",
	Type:                   "type",
	DriverID:               "driver_id",
	Name:                   "name",
	Age:                    "age",
	Gender:                 "gender",
	Dob:                    "dob",
	HiringDate:             "hiring_date",
	Street:                 "street",
	CityCounty:             "city_county",
	State:                  "state",
	Zip:                    "zip",
	LossLocation:           "loss_location",
	ClaimStatus:            "claim_status",
}

var ClaimTableColumns = struct {
	ID                     string
	DocumentID             string
	CreatedAt              string
	ClaimSN                string
	PolicySN               string
	ClaimID                string
	OccurrenceID           string
	DateOfLoss             string
	DateReported           string
	TimeOfLoss             string
	ClosedDate             string
	CauseOfLossSummary     string
	CauseOfLossDescription string
	Vin                    string
	Type                   string
	DriverID               string
	Name                   string
	Age                    string
	Gender                 string
	Dob                    string
	HiringDate             string
	Street                 string
	CityCounty             string
	State                  string
	Zip                    string
	LossLocation           string
	ClaimStatus            string
}{
	ID:                     "claim.id",
	DocumentID:             "claim.document_id",
	CreatedAt:              "claim.created_at",
	ClaimSN:                "claim.claim_sn",
	PolicySN:               "claim.policy_sn",
	ClaimID:                "claim.claim_id",
	OccurrenceID:           "claim.occurrence_id",
	DateOfLoss:             "claim.date_of_loss",
	DateReported:           "claim.date_reported",
	TimeOfLoss:             "claim.time_of_loss",
	ClosedDate:             "claim.closed_date",
	CauseOfLossSummary:     "claim.cause_of_loss_summary",
	CauseOfLossDescription: "claim.cause_of_loss_description",
	Vin:                    "claim.vin",
	Type:                   "claim.type",
	DriverID:               "claim.driver_id",
	Name:                   "claim.name",
	Age:                    "claim.age",
	Gender:                 "claim.gender",
	Dob:                    "claim.dob",
	HiringDate:             "claim.hiring_date",
	Street:                 "claim.street",
	CityCounty:             "claim.city_county",
	State:                  "claim.state",
	Zip:                    "claim.zip",
	LossLocation:           "claim.loss_location",
	ClaimStatus:            "claim.claim_status",
}

// Generated where

type whereHelperint struct{ field string }

func (w whereHelperint) EQ(x int) qm.QueryMod  { return qmhelper.Where(w.field, qmhelper.EQ, x) }
func (w whereHelperint) NEQ(x int) qm.QueryMod { return qmhelper.Where(w.field, qmhelper.NEQ, x) }
func (w whereHelperint) LT(x int) qm.QueryMod  { return qmhelper.Where(w.field, qmhelper.LT, x) }
func (w whereHelperint) LTE(x int) qm.QueryMod { return qmhelper.Where(w.field, qmhelper.LTE, x) }
func (w whereHelperint) GT(x int) qm.QueryMod  { return qmhelper.Where(w.field, qmhelper.GT, x) }
func (w whereHelperint) GTE(x int) qm.QueryMod { return qmhelper.Where(w.field, qmhelper.GTE, x) }
func (w whereHelperint) IN(slice []int) qm.QueryMod {
	values := make([]interface{}, 0, len(slice))
	for _, value := range slice {
		values = append(values, value)
	}
	return qm.WhereIn(fmt.Sprintf("%s IN ?", w.field), values...)
}
func (w whereHelperint) NIN(slice []int) qm.QueryMod {
	values := make([]interface{}, 0, len(slice))
	for _, value := range slice {
		values = append(values, value)
	}
	return qm.WhereNotIn(fmt.Sprintf("%s NOT IN ?", w.field), values...)
}

type whereHelpernull_String struct{ field string }

func (w whereHelpernull_String) EQ(x null.String) qm.QueryMod {
	return qmhelper.WhereNullEQ(w.field, false, x)
}
func (w whereHelpernull_String) NEQ(x null.String) qm.QueryMod {
	return qmhelper.WhereNullEQ(w.field, true, x)
}
func (w whereHelpernull_String) LT(x null.String) qm.QueryMod {
	return qmhelper.Where(w.field, qmhelper.LT, x)
}
func (w whereHelpernull_String) LTE(x null.String) qm.QueryMod {
	return qmhelper.Where(w.field, qmhelper.LTE, x)
}
func (w whereHelpernull_String) GT(x null.String) qm.QueryMod {
	return qmhelper.Where(w.field, qmhelper.GT, x)
}
func (w whereHelpernull_String) GTE(x null.String) qm.QueryMod {
	return qmhelper.Where(w.field, qmhelper.GTE, x)
}

func (w whereHelpernull_String) IsNull() qm.QueryMod    { return qmhelper.WhereIsNull(w.field) }
func (w whereHelpernull_String) IsNotNull() qm.QueryMod { return qmhelper.WhereIsNotNull(w.field) }

type whereHelpernull_Int struct{ field string }

func (w whereHelpernull_Int) EQ(x null.Int) qm.QueryMod {
	return qmhelper.WhereNullEQ(w.field, false, x)
}
func (w whereHelpernull_Int) NEQ(x null.Int) qm.QueryMod {
	return qmhelper.WhereNullEQ(w.field, true, x)
}
func (w whereHelpernull_Int) LT(x null.Int) qm.QueryMod {
	return qmhelper.Where(w.field, qmhelper.LT, x)
}
func (w whereHelpernull_Int) LTE(x null.Int) qm.QueryMod {
	return qmhelper.Where(w.field, qmhelper.LTE, x)
}
func (w whereHelpernull_Int) GT(x null.Int) qm.QueryMod {
	return qmhelper.Where(w.field, qmhelper.GT, x)
}
func (w whereHelpernull_Int) GTE(x null.Int) qm.QueryMod {
	return qmhelper.Where(w.field, qmhelper.GTE, x)
}

func (w whereHelpernull_Int) IsNull() qm.QueryMod    { return qmhelper.WhereIsNull(w.field) }
func (w whereHelpernull_Int) IsNotNull() qm.QueryMod { return qmhelper.WhereIsNotNull(w.field) }

var ClaimWhere = struct {
	ID                     whereHelperstring
	DocumentID             whereHelperstring
	CreatedAt              whereHelpertime_Time
	ClaimSN                whereHelperint
	PolicySN               whereHelperint
	ClaimID                whereHelpernull_String
	OccurrenceID           whereHelperstring
	DateOfLoss             whereHelpernull_Time
	DateReported           whereHelpernull_Time
	TimeOfLoss             whereHelpernull_Time
	ClosedDate             whereHelpernull_Time
	CauseOfLossSummary     whereHelpernull_String
	CauseOfLossDescription whereHelpernull_String
	Vin                    whereHelpernull_String
	Type                   whereHelpernull_String
	DriverID               whereHelpernull_String
	Name                   whereHelpernull_String
	Age                    whereHelpernull_Int
	Gender                 whereHelpernull_String
	Dob                    whereHelpernull_Time
	HiringDate             whereHelpernull_Time
	Street                 whereHelpernull_String
	CityCounty             whereHelpernull_String
	State                  whereHelpernull_String
	Zip                    whereHelpernull_String
	LossLocation           whereHelpernull_String
	ClaimStatus            whereHelpernull_String
}{
	ID:                     whereHelperstring{field: "\"parsed_loss_runs\".\"claim\".\"id\""},
	DocumentID:             whereHelperstring{field: "\"parsed_loss_runs\".\"claim\".\"document_id\""},
	CreatedAt:              whereHelpertime_Time{field: "\"parsed_loss_runs\".\"claim\".\"created_at\""},
	ClaimSN:                whereHelperint{field: "\"parsed_loss_runs\".\"claim\".\"claim_sn\""},
	PolicySN:               whereHelperint{field: "\"parsed_loss_runs\".\"claim\".\"policy_sn\""},
	ClaimID:                whereHelpernull_String{field: "\"parsed_loss_runs\".\"claim\".\"claim_id\""},
	OccurrenceID:           whereHelperstring{field: "\"parsed_loss_runs\".\"claim\".\"occurrence_id\""},
	DateOfLoss:             whereHelpernull_Time{field: "\"parsed_loss_runs\".\"claim\".\"date_of_loss\""},
	DateReported:           whereHelpernull_Time{field: "\"parsed_loss_runs\".\"claim\".\"date_reported\""},
	TimeOfLoss:             whereHelpernull_Time{field: "\"parsed_loss_runs\".\"claim\".\"time_of_loss\""},
	ClosedDate:             whereHelpernull_Time{field: "\"parsed_loss_runs\".\"claim\".\"closed_date\""},
	CauseOfLossSummary:     whereHelpernull_String{field: "\"parsed_loss_runs\".\"claim\".\"cause_of_loss_summary\""},
	CauseOfLossDescription: whereHelpernull_String{field: "\"parsed_loss_runs\".\"claim\".\"cause_of_loss_description\""},
	Vin:                    whereHelpernull_String{field: "\"parsed_loss_runs\".\"claim\".\"vin\""},
	Type:                   whereHelpernull_String{field: "\"parsed_loss_runs\".\"claim\".\"type\""},
	DriverID:               whereHelpernull_String{field: "\"parsed_loss_runs\".\"claim\".\"driver_id\""},
	Name:                   whereHelpernull_String{field: "\"parsed_loss_runs\".\"claim\".\"name\""},
	Age:                    whereHelpernull_Int{field: "\"parsed_loss_runs\".\"claim\".\"age\""},
	Gender:                 whereHelpernull_String{field: "\"parsed_loss_runs\".\"claim\".\"gender\""},
	Dob:                    whereHelpernull_Time{field: "\"parsed_loss_runs\".\"claim\".\"dob\""},
	HiringDate:             whereHelpernull_Time{field: "\"parsed_loss_runs\".\"claim\".\"hiring_date\""},
	Street:                 whereHelpernull_String{field: "\"parsed_loss_runs\".\"claim\".\"street\""},
	CityCounty:             whereHelpernull_String{field: "\"parsed_loss_runs\".\"claim\".\"city_county\""},
	State:                  whereHelpernull_String{field: "\"parsed_loss_runs\".\"claim\".\"state\""},
	Zip:                    whereHelpernull_String{field: "\"parsed_loss_runs\".\"claim\".\"zip\""},
	LossLocation:           whereHelpernull_String{field: "\"parsed_loss_runs\".\"claim\".\"loss_location\""},
	ClaimStatus:            whereHelpernull_String{field: "\"parsed_loss_runs\".\"claim\".\"claim_status\""},
}

// ClaimRels is where relationship names are stored.
var ClaimRels = struct {
	Document string
}{
	Document: "Document",
}

// claimR is where relationships are stored.
type claimR struct {
	Document *Document `boil:"Document" json:"Document" toml:"Document" yaml:"Document"`
}

// NewStruct creates a new relationship struct
func (*claimR) NewStruct() *claimR {
	return &claimR{}
}

// claimL is where Load methods for each relationship are stored.
type claimL struct{}

var (
	claimAllColumns            = []string{"id", "document_id", "created_at", "claim_sn", "policy_sn", "claim_id", "occurrence_id", "date_of_loss", "date_reported", "time_of_loss", "closed_date", "cause_of_loss_summary", "cause_of_loss_description", "vin", "type", "driver_id", "name", "age", "gender", "dob", "hiring_date", "street", "city_county", "state", "zip", "loss_location", "claim_status"}
	claimColumnsWithoutDefault = []string{"id", "document_id", "created_at", "claim_sn", "policy_sn", "occurrence_id"}
	claimColumnsWithDefault    = []string{"claim_id", "date_of_loss", "date_reported", "time_of_loss", "closed_date", "cause_of_loss_summary", "cause_of_loss_description", "vin", "type", "driver_id", "name", "age", "gender", "dob", "hiring_date", "street", "city_county", "state", "zip", "loss_location", "claim_status"}
	claimPrimaryKeyColumns     = []string{"id"}
	claimGeneratedColumns      = []string{}
)

type (
	// ClaimSlice is an alias for a slice of pointers to Claim.
	// This should almost always be used instead of []Claim.
	ClaimSlice []*Claim
	// ClaimHook is the signature for custom Claim hook methods
	ClaimHook func(context.Context, boil.ContextExecutor, *Claim) error

	claimQuery struct {
		*queries.Query
	}
)

// Cache for insert, update and upsert
var (
	claimType                 = reflect.TypeOf(&Claim{})
	claimMapping              = queries.MakeStructMapping(claimType)
	claimPrimaryKeyMapping, _ = queries.BindMapping(claimType, claimMapping, claimPrimaryKeyColumns)
	claimInsertCacheMut       sync.RWMutex
	claimInsertCache          = make(map[string]insertCache)
	claimUpdateCacheMut       sync.RWMutex
	claimUpdateCache          = make(map[string]updateCache)
	claimUpsertCacheMut       sync.RWMutex
	claimUpsertCache          = make(map[string]insertCache)
)

var (
	// Force time package dependency for automated UpdatedAt/CreatedAt.
	_ = time.Second
	// Force qmhelper dependency for where clause generation (which doesn't
	// always happen)
	_ = qmhelper.Where
)

var claimAfterSelectHooks []ClaimHook

var claimBeforeInsertHooks []ClaimHook
var claimAfterInsertHooks []ClaimHook

var claimBeforeUpdateHooks []ClaimHook
var claimAfterUpdateHooks []ClaimHook

var claimBeforeDeleteHooks []ClaimHook
var claimAfterDeleteHooks []ClaimHook

var claimBeforeUpsertHooks []ClaimHook
var claimAfterUpsertHooks []ClaimHook

// doAfterSelectHooks executes all "after Select" hooks.
func (o *Claim) doAfterSelectHooks(ctx context.Context, exec boil.ContextExecutor) (err error) {
	if boil.HooksAreSkipped(ctx) {
		return nil
	}

	for _, hook := range claimAfterSelectHooks {
		if err := hook(ctx, exec, o); err != nil {
			return err
		}
	}

	return nil
}

// doBeforeInsertHooks executes all "before insert" hooks.
func (o *Claim) doBeforeInsertHooks(ctx context.Context, exec boil.ContextExecutor) (err error) {
	if boil.HooksAreSkipped(ctx) {
		return nil
	}

	for _, hook := range claimBeforeInsertHooks {
		if err := hook(ctx, exec, o); err != nil {
			return err
		}
	}

	return nil
}

// doAfterInsertHooks executes all "after Insert" hooks.
func (o *Claim) doAfterInsertHooks(ctx context.Context, exec boil.ContextExecutor) (err error) {
	if boil.HooksAreSkipped(ctx) {
		return nil
	}

	for _, hook := range claimAfterInsertHooks {
		if err := hook(ctx, exec, o); err != nil {
			return err
		}
	}

	return nil
}

// doBeforeUpdateHooks executes all "before Update" hooks.
func (o *Claim) doBeforeUpdateHooks(ctx context.Context, exec boil.ContextExecutor) (err error) {
	if boil.HooksAreSkipped(ctx) {
		return nil
	}

	for _, hook := range claimBeforeUpdateHooks {
		if err := hook(ctx, exec, o); err != nil {
			return err
		}
	}

	return nil
}

// doAfterUpdateHooks executes all "after Update" hooks.
func (o *Claim) doAfterUpdateHooks(ctx context.Context, exec boil.ContextExecutor) (err error) {
	if boil.HooksAreSkipped(ctx) {
		return nil
	}

	for _, hook := range claimAfterUpdateHooks {
		if err := hook(ctx, exec, o); err != nil {
			return err
		}
	}

	return nil
}

// doBeforeDeleteHooks executes all "before Delete" hooks.
func (o *Claim) doBeforeDeleteHooks(ctx context.Context, exec boil.ContextExecutor) (err error) {
	if boil.HooksAreSkipped(ctx) {
		return nil
	}

	for _, hook := range claimBeforeDeleteHooks {
		if err := hook(ctx, exec, o); err != nil {
			return err
		}
	}

	return nil
}

// doAfterDeleteHooks executes all "after Delete" hooks.
func (o *Claim) doAfterDeleteHooks(ctx context.Context, exec boil.ContextExecutor) (err error) {
	if boil.HooksAreSkipped(ctx) {
		return nil
	}

	for _, hook := range claimAfterDeleteHooks {
		if err := hook(ctx, exec, o); err != nil {
			return err
		}
	}

	return nil
}

// doBeforeUpsertHooks executes all "before Upsert" hooks.
func (o *Claim) doBeforeUpsertHooks(ctx context.Context, exec boil.ContextExecutor) (err error) {
	if boil.HooksAreSkipped(ctx) {
		return nil
	}

	for _, hook := range claimBeforeUpsertHooks {
		if err := hook(ctx, exec, o); err != nil {
			return err
		}
	}

	return nil
}

// doAfterUpsertHooks executes all "after Upsert" hooks.
func (o *Claim) doAfterUpsertHooks(ctx context.Context, exec boil.ContextExecutor) (err error) {
	if boil.HooksAreSkipped(ctx) {
		return nil
	}

	for _, hook := range claimAfterUpsertHooks {
		if err := hook(ctx, exec, o); err != nil {
			return err
		}
	}

	return nil
}

// AddClaimHook registers your hook function for all future operations.
func AddClaimHook(hookPoint boil.HookPoint, claimHook ClaimHook) {
	switch hookPoint {
	case boil.AfterSelectHook:
		claimAfterSelectHooks = append(claimAfterSelectHooks, claimHook)
	case boil.BeforeInsertHook:
		claimBeforeInsertHooks = append(claimBeforeInsertHooks, claimHook)
	case boil.AfterInsertHook:
		claimAfterInsertHooks = append(claimAfterInsertHooks, claimHook)
	case boil.BeforeUpdateHook:
		claimBeforeUpdateHooks = append(claimBeforeUpdateHooks, claimHook)
	case boil.AfterUpdateHook:
		claimAfterUpdateHooks = append(claimAfterUpdateHooks, claimHook)
	case boil.BeforeDeleteHook:
		claimBeforeDeleteHooks = append(claimBeforeDeleteHooks, claimHook)
	case boil.AfterDeleteHook:
		claimAfterDeleteHooks = append(claimAfterDeleteHooks, claimHook)
	case boil.BeforeUpsertHook:
		claimBeforeUpsertHooks = append(claimBeforeUpsertHooks, claimHook)
	case boil.AfterUpsertHook:
		claimAfterUpsertHooks = append(claimAfterUpsertHooks, claimHook)
	}
}

// One returns a single claim record from the query.
func (q claimQuery) One(ctx context.Context, exec boil.ContextExecutor) (*Claim, error) {
	o := &Claim{}

	queries.SetLimit(q.Query, 1)

	err := q.Bind(ctx, exec, o)
	if err != nil {
		if errors.Is(err, sql.ErrNoRows) {
			return nil, sql.ErrNoRows
		}
		return nil, errors.Wrap(err, "parsed_loss_runs: failed to execute a one query for claim")
	}

	if err := o.doAfterSelectHooks(ctx, exec); err != nil {
		return o, err
	}

	return o, nil
}

// All returns all Claim records from the query.
func (q claimQuery) All(ctx context.Context, exec boil.ContextExecutor) (ClaimSlice, error) {
	var o []*Claim

	err := q.Bind(ctx, exec, &o)
	if err != nil {
		return nil, errors.Wrap(err, "parsed_loss_runs: failed to assign all query results to Claim slice")
	}

	if len(claimAfterSelectHooks) != 0 {
		for _, obj := range o {
			if err := obj.doAfterSelectHooks(ctx, exec); err != nil {
				return o, err
			}
		}
	}

	return o, nil
}

// Count returns the count of all Claim records in the query.
func (q claimQuery) Count(ctx context.Context, exec boil.ContextExecutor) (int64, error) {
	var count int64

	queries.SetSelect(q.Query, nil)
	queries.SetCount(q.Query)

	err := q.Query.QueryRowContext(ctx, exec).Scan(&count)
	if err != nil {
		return 0, errors.Wrap(err, "parsed_loss_runs: failed to count claim rows")
	}

	return count, nil
}

// Exists checks if the row exists in the table.
func (q claimQuery) Exists(ctx context.Context, exec boil.ContextExecutor) (bool, error) {
	var count int64

	queries.SetSelect(q.Query, nil)
	queries.SetCount(q.Query)
	queries.SetLimit(q.Query, 1)

	err := q.Query.QueryRowContext(ctx, exec).Scan(&count)
	if err != nil {
		return false, errors.Wrap(err, "parsed_loss_runs: failed to check if claim exists")
	}

	return count > 0, nil
}

// Document pointed to by the foreign key.
func (o *Claim) Document(mods ...qm.QueryMod) documentQuery {
	queryMods := []qm.QueryMod{
		qm.Where("\"id\" = ?", o.DocumentID),
	}

	queryMods = append(queryMods, mods...)

	return Documents(queryMods...)
}

// LoadDocument allows an eager lookup of values, cached into the
// loaded structs of the objects. This is for an N-1 relationship.
func (claimL) LoadDocument(ctx context.Context, e boil.ContextExecutor, singular bool, maybeClaim interface{}, mods queries.Applicator) error {
	var slice []*Claim
	var object *Claim

	if singular {
		object = maybeClaim.(*Claim)
	} else {
		slice = *maybeClaim.(*[]*Claim)
	}

	args := make([]interface{}, 0, 1)
	if singular {
		if object.R == nil {
			object.R = &claimR{}
		}
		args = append(args, object.DocumentID)

	} else {
	Outer:
		for _, obj := range slice {
			if obj.R == nil {
				obj.R = &claimR{}
			}

			for _, a := range args {
				if a == obj.DocumentID {
					continue Outer
				}
			}

			args = append(args, obj.DocumentID)

		}
	}

	if len(args) == 0 {
		return nil
	}

	query := NewQuery(
		qm.From(`parsed_loss_runs.document`),
		qm.WhereIn(`parsed_loss_runs.document.id in ?`, args...),
	)
	if mods != nil {
		mods.Apply(query)
	}

	results, err := query.QueryContext(ctx, e)
	if err != nil {
		return errors.Wrap(err, "failed to eager load Document")
	}

	var resultSlice []*Document
	if err = queries.Bind(results, &resultSlice); err != nil {
		return errors.Wrap(err, "failed to bind eager loaded slice Document")
	}

	if err = results.Close(); err != nil {
		return errors.Wrap(err, "failed to close results of eager load for document")
	}
	if err = results.Err(); err != nil {
		return errors.Wrap(err, "error occurred during iteration of eager loaded relations for document")
	}

	if len(claimAfterSelectHooks) != 0 {
		for _, obj := range resultSlice {
			if err := obj.doAfterSelectHooks(ctx, e); err != nil {
				return err
			}
		}
	}

	if len(resultSlice) == 0 {
		return nil
	}

	if singular {
		foreign := resultSlice[0]
		object.R.Document = foreign
		if foreign.R == nil {
			foreign.R = &documentR{}
		}
		foreign.R.Claims = append(foreign.R.Claims, object)
		return nil
	}

	for _, local := range slice {
		for _, foreign := range resultSlice {
			if local.DocumentID == foreign.ID {
				local.R.Document = foreign
				if foreign.R == nil {
					foreign.R = &documentR{}
				}
				foreign.R.Claims = append(foreign.R.Claims, local)
				break
			}
		}
	}

	return nil
}

// SetDocument of the claim to the related item.
// Sets o.R.Document to related.
// Adds o to related.R.Claims.
func (o *Claim) SetDocument(ctx context.Context, exec boil.ContextExecutor, insert bool, related *Document) error {
	var err error
	if insert {
		if err = related.Insert(ctx, exec, boil.Infer()); err != nil {
			return errors.Wrap(err, "failed to insert into foreign table")
		}
	}

	updateQuery := fmt.Sprintf(
		"UPDATE \"parsed_loss_runs\".\"claim\" SET %s WHERE %s",
		strmangle.SetParamNames("\"", "\"", 1, []string{"document_id"}),
		strmangle.WhereClause("\"", "\"", 2, claimPrimaryKeyColumns),
	)
	values := []interface{}{related.ID, o.ID}

	if boil.IsDebug(ctx) {
		writer := boil.DebugWriterFrom(ctx)
		fmt.Fprintln(writer, updateQuery)
		fmt.Fprintln(writer, values)
	}
	if _, err = exec.ExecContext(ctx, updateQuery, values...); err != nil {
		return errors.Wrap(err, "failed to update local table")
	}

	o.DocumentID = related.ID
	if o.R == nil {
		o.R = &claimR{
			Document: related,
		}
	} else {
		o.R.Document = related
	}

	if related.R == nil {
		related.R = &documentR{
			Claims: ClaimSlice{o},
		}
	} else {
		related.R.Claims = append(related.R.Claims, o)
	}

	return nil
}

// Claims retrieves all the records using an executor.
func Claims(mods ...qm.QueryMod) claimQuery {
	mods = append(mods, qm.From("\"parsed_loss_runs\".\"claim\""))
	q := NewQuery(mods...)
	if len(queries.GetSelect(q)) == 0 {
		queries.SetSelect(q, []string{"\"parsed_loss_runs\".\"claim\".*"})
	}

	return claimQuery{q}
}

// FindClaim retrieves a single record by ID with an executor.
// If selectCols is empty Find will return all columns.
func FindClaim(ctx context.Context, exec boil.ContextExecutor, iD string, selectCols ...string) (*Claim, error) {
	claimObj := &Claim{}

	sel := "*"
	if len(selectCols) > 0 {
		sel = strings.Join(strmangle.IdentQuoteSlice(dialect.LQ, dialect.RQ, selectCols), ",")
	}
	query := fmt.Sprintf(
		"select %s from \"parsed_loss_runs\".\"claim\" where \"id\"=$1", sel,
	)

	q := queries.Raw(query, iD)

	err := q.Bind(ctx, exec, claimObj)
	if err != nil {
		if errors.Is(err, sql.ErrNoRows) {
			return nil, sql.ErrNoRows
		}
		return nil, errors.Wrap(err, "parsed_loss_runs: unable to select from claim")
	}

	if err = claimObj.doAfterSelectHooks(ctx, exec); err != nil {
		return claimObj, err
	}

	return claimObj, nil
}

// Insert a single record using an executor.
// See boil.Columns.InsertColumnSet documentation to understand column list inference for inserts.
func (o *Claim) Insert(ctx context.Context, exec boil.ContextExecutor, columns boil.Columns) error {
	if o == nil {
		return errors.New("parsed_loss_runs: no claim provided for insertion")
	}

	var err error
	if !boil.TimestampsAreSkipped(ctx) {
		currTime := time.Now().In(boil.GetLocation())

		if o.CreatedAt.IsZero() {
			o.CreatedAt = currTime
		}
	}

	if err := o.doBeforeInsertHooks(ctx, exec); err != nil {
		return err
	}

	nzDefaults := queries.NonZeroDefaultSet(claimColumnsWithDefault, o)

	key := makeCacheKey(columns, nzDefaults)
	claimInsertCacheMut.RLock()
	cache, cached := claimInsertCache[key]
	claimInsertCacheMut.RUnlock()

	if !cached {
		wl, returnColumns := columns.InsertColumnSet(
			claimAllColumns,
			claimColumnsWithDefault,
			claimColumnsWithoutDefault,
			nzDefaults,
		)

		cache.valueMapping, err = queries.BindMapping(claimType, claimMapping, wl)
		if err != nil {
			return err
		}
		cache.retMapping, err = queries.BindMapping(claimType, claimMapping, returnColumns)
		if err != nil {
			return err
		}
		if len(wl) != 0 {
			cache.query = fmt.Sprintf("INSERT INTO \"parsed_loss_runs\".\"claim\" (\"%s\") %%sVALUES (%s)%%s", strings.Join(wl, "\",\""), strmangle.Placeholders(dialect.UseIndexPlaceholders, len(wl), 1, 1))
		} else {
			cache.query = "INSERT INTO \"parsed_loss_runs\".\"claim\" %sDEFAULT VALUES%s"
		}

		var queryOutput, queryReturning string

		if len(cache.retMapping) != 0 {
			queryReturning = fmt.Sprintf(" RETURNING \"%s\"", strings.Join(returnColumns, "\",\""))
		}

		cache.query = fmt.Sprintf(cache.query, queryOutput, queryReturning)
	}

	value := reflect.Indirect(reflect.ValueOf(o))
	vals := queries.ValuesFromMapping(value, cache.valueMapping)

	if boil.IsDebug(ctx) {
		writer := boil.DebugWriterFrom(ctx)
		fmt.Fprintln(writer, cache.query)
		fmt.Fprintln(writer, vals)
	}

	if len(cache.retMapping) != 0 {
		err = exec.QueryRowContext(ctx, cache.query, vals...).Scan(queries.PtrsFromMapping(value, cache.retMapping)...)
	} else {
		_, err = exec.ExecContext(ctx, cache.query, vals...)
	}

	if err != nil {
		return errors.Wrap(err, "parsed_loss_runs: unable to insert into claim")
	}

	if !cached {
		claimInsertCacheMut.Lock()
		claimInsertCache[key] = cache
		claimInsertCacheMut.Unlock()
	}

	return o.doAfterInsertHooks(ctx, exec)
}

// Update uses an executor to update the Claim.
// See boil.Columns.UpdateColumnSet documentation to understand column list inference for updates.
// Update does not automatically update the record in case of default values. Use .Reload() to refresh the records.
func (o *Claim) Update(ctx context.Context, exec boil.ContextExecutor, columns boil.Columns) (int64, error) {
	var err error
	if err = o.doBeforeUpdateHooks(ctx, exec); err != nil {
		return 0, err
	}
	key := makeCacheKey(columns, nil)
	claimUpdateCacheMut.RLock()
	cache, cached := claimUpdateCache[key]
	claimUpdateCacheMut.RUnlock()

	if !cached {
		wl := columns.UpdateColumnSet(
			claimAllColumns,
			claimPrimaryKeyColumns,
		)

		if !columns.IsWhitelist() {
			wl = strmangle.SetComplement(wl, []string{"created_at"})
		}
		if len(wl) == 0 {
			return 0, errors.New("parsed_loss_runs: unable to update claim, could not build whitelist")
		}

		cache.query = fmt.Sprintf("UPDATE \"parsed_loss_runs\".\"claim\" SET %s WHERE %s",
			strmangle.SetParamNames("\"", "\"", 1, wl),
			strmangle.WhereClause("\"", "\"", len(wl)+1, claimPrimaryKeyColumns),
		)
		cache.valueMapping, err = queries.BindMapping(claimType, claimMapping, append(wl, claimPrimaryKeyColumns...))
		if err != nil {
			return 0, err
		}
	}

	values := queries.ValuesFromMapping(reflect.Indirect(reflect.ValueOf(o)), cache.valueMapping)

	if boil.IsDebug(ctx) {
		writer := boil.DebugWriterFrom(ctx)
		fmt.Fprintln(writer, cache.query)
		fmt.Fprintln(writer, values)
	}
	var result sql.Result
	result, err = exec.ExecContext(ctx, cache.query, values...)
	if err != nil {
		return 0, errors.Wrap(err, "parsed_loss_runs: unable to update claim row")
	}

	rowsAff, err := result.RowsAffected()
	if err != nil {
		return 0, errors.Wrap(err, "parsed_loss_runs: failed to get rows affected by update for claim")
	}

	if !cached {
		claimUpdateCacheMut.Lock()
		claimUpdateCache[key] = cache
		claimUpdateCacheMut.Unlock()
	}

	return rowsAff, o.doAfterUpdateHooks(ctx, exec)
}

// UpdateAll updates all rows with the specified column values.
func (q claimQuery) UpdateAll(ctx context.Context, exec boil.ContextExecutor, cols M) (int64, error) {
	queries.SetUpdate(q.Query, cols)

	result, err := q.Query.ExecContext(ctx, exec)
	if err != nil {
		return 0, errors.Wrap(err, "parsed_loss_runs: unable to update all for claim")
	}

	rowsAff, err := result.RowsAffected()
	if err != nil {
		return 0, errors.Wrap(err, "parsed_loss_runs: unable to retrieve rows affected for claim")
	}

	return rowsAff, nil
}

// UpdateAll updates all rows with the specified column values, using an executor.
func (o ClaimSlice) UpdateAll(ctx context.Context, exec boil.ContextExecutor, cols M) (int64, error) {
	ln := int64(len(o))
	if ln == 0 {
		return 0, nil
	}

	if len(cols) == 0 {
		return 0, errors.New("parsed_loss_runs: update all requires at least one column argument")
	}

	colNames := make([]string, len(cols))
	args := make([]interface{}, len(cols))

	i := 0
	for name, value := range cols {
		colNames[i] = name
		args[i] = value
		i++
	}

	// Append all of the primary key values for each column
	for _, obj := range o {
		pkeyArgs := queries.ValuesFromMapping(reflect.Indirect(reflect.ValueOf(obj)), claimPrimaryKeyMapping)
		args = append(args, pkeyArgs...)
	}

	sql := fmt.Sprintf("UPDATE \"parsed_loss_runs\".\"claim\" SET %s WHERE %s",
		strmangle.SetParamNames("\"", "\"", 1, colNames),
		strmangle.WhereClauseRepeated(string(dialect.LQ), string(dialect.RQ), len(colNames)+1, claimPrimaryKeyColumns, len(o)))

	if boil.IsDebug(ctx) {
		writer := boil.DebugWriterFrom(ctx)
		fmt.Fprintln(writer, sql)
		fmt.Fprintln(writer, args...)
	}
	result, err := exec.ExecContext(ctx, sql, args...)
	if err != nil {
		return 0, errors.Wrap(err, "parsed_loss_runs: unable to update all in claim slice")
	}

	rowsAff, err := result.RowsAffected()
	if err != nil {
		return 0, errors.Wrap(err, "parsed_loss_runs: unable to retrieve rows affected all in update all claim")
	}
	return rowsAff, nil
}

// Upsert attempts an insert using an executor, and does an update or ignore on conflict.
// See boil.Columns documentation for how to properly use updateColumns and insertColumns.
func (o *Claim) Upsert(ctx context.Context, exec boil.ContextExecutor, updateOnConflict bool, conflictColumns []string, updateColumns, insertColumns boil.Columns) error {
	if o == nil {
		return errors.New("parsed_loss_runs: no claim provided for upsert")
	}
	if !boil.TimestampsAreSkipped(ctx) {
		currTime := time.Now().In(boil.GetLocation())

		if o.CreatedAt.IsZero() {
			o.CreatedAt = currTime
		}
	}

	if err := o.doBeforeUpsertHooks(ctx, exec); err != nil {
		return err
	}

	nzDefaults := queries.NonZeroDefaultSet(claimColumnsWithDefault, o)

	// Build cache key in-line uglily - mysql vs psql problems
	buf := strmangle.GetBuffer()
	if updateOnConflict {
		buf.WriteByte('t')
	} else {
		buf.WriteByte('f')
	}
	buf.WriteByte('.')
	for _, c := range conflictColumns {
		buf.WriteString(c)
	}
	buf.WriteByte('.')
	buf.WriteString(strconv.Itoa(updateColumns.Kind))
	for _, c := range updateColumns.Cols {
		buf.WriteString(c)
	}
	buf.WriteByte('.')
	buf.WriteString(strconv.Itoa(insertColumns.Kind))
	for _, c := range insertColumns.Cols {
		buf.WriteString(c)
	}
	buf.WriteByte('.')
	for _, c := range nzDefaults {
		buf.WriteString(c)
	}
	key := buf.String()
	strmangle.PutBuffer(buf)

	claimUpsertCacheMut.RLock()
	cache, cached := claimUpsertCache[key]
	claimUpsertCacheMut.RUnlock()

	var err error

	if !cached {
		insert, ret := insertColumns.InsertColumnSet(
			claimAllColumns,
			claimColumnsWithDefault,
			claimColumnsWithoutDefault,
			nzDefaults,
		)

		update := updateColumns.UpdateColumnSet(
			claimAllColumns,
			claimPrimaryKeyColumns,
		)

		if updateOnConflict && len(update) == 0 {
			return errors.New("parsed_loss_runs: unable to upsert claim, could not build update column list")
		}

		conflict := conflictColumns
		if len(conflict) == 0 {
			conflict = make([]string, len(claimPrimaryKeyColumns))
			copy(conflict, claimPrimaryKeyColumns)
		}
		cache.query = buildUpsertQueryPostgres(dialect, "\"parsed_loss_runs\".\"claim\"", updateOnConflict, ret, update, conflict, insert)

		cache.valueMapping, err = queries.BindMapping(claimType, claimMapping, insert)
		if err != nil {
			return err
		}
		if len(ret) != 0 {
			cache.retMapping, err = queries.BindMapping(claimType, claimMapping, ret)
			if err != nil {
				return err
			}
		}
	}

	value := reflect.Indirect(reflect.ValueOf(o))
	vals := queries.ValuesFromMapping(value, cache.valueMapping)
	var returns []interface{}
	if len(cache.retMapping) != 0 {
		returns = queries.PtrsFromMapping(value, cache.retMapping)
	}

	if boil.IsDebug(ctx) {
		writer := boil.DebugWriterFrom(ctx)
		fmt.Fprintln(writer, cache.query)
		fmt.Fprintln(writer, vals)
	}
	if len(cache.retMapping) != 0 {
		err = exec.QueryRowContext(ctx, cache.query, vals...).Scan(returns...)
		if errors.Is(err, sql.ErrNoRows) {
			err = nil // Postgres doesn't return anything when there's no update
		}
	} else {
		_, err = exec.ExecContext(ctx, cache.query, vals...)
	}
	if err != nil {
		return errors.Wrap(err, "parsed_loss_runs: unable to upsert claim")
	}

	if !cached {
		claimUpsertCacheMut.Lock()
		claimUpsertCache[key] = cache
		claimUpsertCacheMut.Unlock()
	}

	return o.doAfterUpsertHooks(ctx, exec)
}

// Delete deletes a single Claim record with an executor.
// Delete will match against the primary key column to find the record to delete.
func (o *Claim) Delete(ctx context.Context, exec boil.ContextExecutor) (int64, error) {
	if o == nil {
		return 0, errors.New("parsed_loss_runs: no Claim provided for delete")
	}

	if err := o.doBeforeDeleteHooks(ctx, exec); err != nil {
		return 0, err
	}

	args := queries.ValuesFromMapping(reflect.Indirect(reflect.ValueOf(o)), claimPrimaryKeyMapping)
	sql := "DELETE FROM \"parsed_loss_runs\".\"claim\" WHERE \"id\"=$1"

	if boil.IsDebug(ctx) {
		writer := boil.DebugWriterFrom(ctx)
		fmt.Fprintln(writer, sql)
		fmt.Fprintln(writer, args...)
	}
	result, err := exec.ExecContext(ctx, sql, args...)
	if err != nil {
		return 0, errors.Wrap(err, "parsed_loss_runs: unable to delete from claim")
	}

	rowsAff, err := result.RowsAffected()
	if err != nil {
		return 0, errors.Wrap(err, "parsed_loss_runs: failed to get rows affected by delete for claim")
	}

	if err := o.doAfterDeleteHooks(ctx, exec); err != nil {
		return 0, err
	}

	return rowsAff, nil
}

// DeleteAll deletes all matching rows.
func (q claimQuery) DeleteAll(ctx context.Context, exec boil.ContextExecutor) (int64, error) {
	if q.Query == nil {
		return 0, errors.New("parsed_loss_runs: no claimQuery provided for delete all")
	}

	queries.SetDelete(q.Query)

	result, err := q.Query.ExecContext(ctx, exec)
	if err != nil {
		return 0, errors.Wrap(err, "parsed_loss_runs: unable to delete all from claim")
	}

	rowsAff, err := result.RowsAffected()
	if err != nil {
		return 0, errors.Wrap(err, "parsed_loss_runs: failed to get rows affected by deleteall for claim")
	}

	return rowsAff, nil
}

// DeleteAll deletes all rows in the slice, using an executor.
func (o ClaimSlice) DeleteAll(ctx context.Context, exec boil.ContextExecutor) (int64, error) {
	if len(o) == 0 {
		return 0, nil
	}

	if len(claimBeforeDeleteHooks) != 0 {
		for _, obj := range o {
			if err := obj.doBeforeDeleteHooks(ctx, exec); err != nil {
				return 0, err
			}
		}
	}

	var args []interface{}
	for _, obj := range o {
		pkeyArgs := queries.ValuesFromMapping(reflect.Indirect(reflect.ValueOf(obj)), claimPrimaryKeyMapping)
		args = append(args, pkeyArgs...)
	}

	sql := "DELETE FROM \"parsed_loss_runs\".\"claim\" WHERE " +
		strmangle.WhereClauseRepeated(string(dialect.LQ), string(dialect.RQ), 1, claimPrimaryKeyColumns, len(o))

	if boil.IsDebug(ctx) {
		writer := boil.DebugWriterFrom(ctx)
		fmt.Fprintln(writer, sql)
		fmt.Fprintln(writer, args)
	}
	result, err := exec.ExecContext(ctx, sql, args...)
	if err != nil {
		return 0, errors.Wrap(err, "parsed_loss_runs: unable to delete all from claim slice")
	}

	rowsAff, err := result.RowsAffected()
	if err != nil {
		return 0, errors.Wrap(err, "parsed_loss_runs: failed to get rows affected by deleteall for claim")
	}

	if len(claimAfterDeleteHooks) != 0 {
		for _, obj := range o {
			if err := obj.doAfterDeleteHooks(ctx, exec); err != nil {
				return 0, err
			}
		}
	}

	return rowsAff, nil
}

// Reload refetches the object from the database
// using the primary keys with an executor.
func (o *Claim) Reload(ctx context.Context, exec boil.ContextExecutor) error {
	ret, err := FindClaim(ctx, exec, o.ID)
	if err != nil {
		return err
	}

	*o = *ret
	return nil
}

// ReloadAll refetches every row with matching primary key column values
// and overwrites the original object slice with the newly updated slice.
func (o *ClaimSlice) ReloadAll(ctx context.Context, exec boil.ContextExecutor) error {
	if o == nil || len(*o) == 0 {
		return nil
	}

	slice := ClaimSlice{}
	var args []interface{}
	for _, obj := range *o {
		pkeyArgs := queries.ValuesFromMapping(reflect.Indirect(reflect.ValueOf(obj)), claimPrimaryKeyMapping)
		args = append(args, pkeyArgs...)
	}

	sql := "SELECT \"parsed_loss_runs\".\"claim\".* FROM \"parsed_loss_runs\".\"claim\" WHERE " +
		strmangle.WhereClauseRepeated(string(dialect.LQ), string(dialect.RQ), 1, claimPrimaryKeyColumns, len(*o))

	q := queries.Raw(sql, args...)

	err := q.Bind(ctx, exec, &slice)
	if err != nil {
		return errors.Wrap(err, "parsed_loss_runs: unable to reload all in ClaimSlice")
	}

	*o = slice

	return nil
}

// ClaimExists checks if the Claim row exists.
func ClaimExists(ctx context.Context, exec boil.ContextExecutor, iD string) (bool, error) {
	var exists bool
	sql := "select exists(select 1 from \"parsed_loss_runs\".\"claim\" where \"id\"=$1 limit 1)"

	if boil.IsDebug(ctx) {
		writer := boil.DebugWriterFrom(ctx)
		fmt.Fprintln(writer, sql)
		fmt.Fprintln(writer, iD)
	}
	row := exec.QueryRowContext(ctx, sql, iD)

	err := row.Scan(&exists)
	if err != nil {
		return false, errors.Wrap(err, "parsed_loss_runs: unable to check if claim exists")
	}

	return exists, nil
}
