load("@io_bazel_rules_go//go:def.bzl", "go_library")

go_library(
    name = "account",
    srcs = [
        "account_aliases.go",
        "account_identifiers.go",
        "account_merge_history.go",
        "account_possible_matches.go",
        "account_programs.go",
        "accounts.go",
        "boil_queries.go",
        "boil_table_names.go",
        "boil_types.go",
        "boil_view_names.go",
        "contacts.go",
        "psql_upsert.go",
    ],
    importpath = "nirvanatech.com/nirvana/db-api/db_models/account",
    visibility = ["//visibility:public"],
    deps = [
        "@com_github_friendsofgo_errors//:errors",
        "@com_github_volatiletech_null_v8//:null",
        "@com_github_volatiletech_sqlboiler_v4//boil",
        "@com_github_volatiletech_sqlboiler_v4//drivers",
        "@com_github_volatiletech_sqlboiler_v4//queries",
        "@com_github_volatiletech_sqlboiler_v4//queries/qm",
        "@com_github_volatiletech_sqlboiler_v4//queries/qmhelper",
        "@com_github_volatiletech_sqlboiler_v4//types",
        "@com_github_volatiletech_strmangle//:strmangle",
    ],
)
