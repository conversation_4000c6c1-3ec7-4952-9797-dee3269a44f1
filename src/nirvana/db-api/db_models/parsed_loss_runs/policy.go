// Code generated by SQLBoiler (https://github.com/volatiletech/sqlboiler). DO NOT EDIT.
// This file is meant to be re-generated in place and/or deleted at any time.

package parsed_loss_runs

import (
	"context"
	"database/sql"
	"fmt"
	"reflect"
	"strconv"
	"strings"
	"sync"
	"time"

	"github.com/friendsofgo/errors"
	"github.com/volatiletech/null/v8"
	"github.com/volatiletech/sqlboiler/v4/boil"
	"github.com/volatiletech/sqlboiler/v4/queries"
	"github.com/volatiletech/sqlboiler/v4/queries/qm"
	"github.com/volatiletech/sqlboiler/v4/queries/qmhelper"
	"github.com/volatiletech/strmangle"
)

// Policy is an object representing the database table.
type Policy struct {
	ID                   string       `boil:"id" json:"id" toml:"id" yaml:"id"`
	DocumentID           string       `boil:"document_id" json:"document_id" toml:"document_id" yaml:"document_id"`
	CreatedAt            time.Time    `boil:"created_at" json:"created_at" toml:"created_at" yaml:"created_at"`
	PolicySN             int          `boil:"policy_sn" json:"policy_sn" toml:"policy_sn" yaml:"policy_sn"`
	Lob                  null.String  `boil:"lob" json:"lob,omitempty" toml:"lob" yaml:"lob,omitempty"`
	Insurer              null.String  `boil:"insurer" json:"insurer,omitempty" toml:"insurer" yaml:"insurer,omitempty"`
	Insured              null.String  `boil:"insured" json:"insured,omitempty" toml:"insured" yaml:"insured,omitempty"`
	Agent                null.String  `boil:"agent" json:"agent,omitempty" toml:"agent" yaml:"agent,omitempty"`
	PolicyNo             null.String  `boil:"policy_no" json:"policy_no,omitempty" toml:"policy_no" yaml:"policy_no,omitempty"`
	EffDate              null.Time    `boil:"eff_date" json:"eff_date,omitempty" toml:"eff_date" yaml:"eff_date,omitempty"`
	ExpDate              null.Time    `boil:"exp_date" json:"exp_date,omitempty" toml:"exp_date" yaml:"exp_date,omitempty"`
	ReportGenerationDate null.Time    `boil:"report_generation_date" json:"report_generation_date,omitempty" toml:"report_generation_date" yaml:"report_generation_date,omitempty"`
	CancelDate           null.Time    `boil:"cancel_date" json:"cancel_date,omitempty" toml:"cancel_date" yaml:"cancel_date,omitempty"`
	NoLoss               int          `boil:"no_loss" json:"no_loss" toml:"no_loss" yaml:"no_loss"`
	TotalPaid            null.Float64 `boil:"total_paid" json:"total_paid,omitempty" toml:"total_paid" yaml:"total_paid,omitempty"`
	TotalReserve         null.Float64 `boil:"total_reserve" json:"total_reserve,omitempty" toml:"total_reserve" yaml:"total_reserve,omitempty"`
	TotalRecovered       null.Float64 `boil:"total_recovered" json:"total_recovered,omitempty" toml:"total_recovered" yaml:"total_recovered,omitempty"`
	TotalIncurred        float64      `boil:"total_incurred" json:"total_incurred" toml:"total_incurred" yaml:"total_incurred"`

	R *policyR `boil:"" json:"" toml:"" yaml:""`
	L policyL  `boil:"-" json:"-" toml:"-" yaml:"-"`
}

var PolicyColumns = struct {
	ID                   string
	DocumentID           string
	CreatedAt            string
	PolicySN             string
	Lob                  string
	Insurer              string
	Insured              string
	Agent                string
	PolicyNo             string
	EffDate              string
	ExpDate              string
	ReportGenerationDate string
	CancelDate           string
	NoLoss               string
	TotalPaid            string
	TotalReserve         string
	TotalRecovered       string
	TotalIncurred        string
}{
	ID:                   "id",
	DocumentID:           "document_id",
	CreatedAt:            "created_at",
	PolicySN:             "policy_sn",
	Lob:                  "lob",
	Insurer:              "insurer",
	Insured:              "insured",
	Agent:                "agent",
	PolicyNo:             "policy_no",
	EffDate:              "eff_date",
	ExpDate:              "exp_date",
	ReportGenerationDate: "report_generation_date",
	CancelDate:           "cancel_date",
	NoLoss:               "no_loss",
	TotalPaid:            "total_paid",
	TotalReserve:         "total_reserve",
	TotalRecovered:       "total_recovered",
	TotalIncurred:        "total_incurred",
}

var PolicyTableColumns = struct {
	ID                   string
	DocumentID           string
	CreatedAt            string
	PolicySN             string
	Lob                  string
	Insurer              string
	Insured              string
	Agent                string
	PolicyNo             string
	EffDate              string
	ExpDate              string
	ReportGenerationDate string
	CancelDate           string
	NoLoss               string
	TotalPaid            string
	TotalReserve         string
	TotalRecovered       string
	TotalIncurred        string
}{
	ID:                   "policy.id",
	DocumentID:           "policy.document_id",
	CreatedAt:            "policy.created_at",
	PolicySN:             "policy.policy_sn",
	Lob:                  "policy.lob",
	Insurer:              "policy.insurer",
	Insured:              "policy.insured",
	Agent:                "policy.agent",
	PolicyNo:             "policy.policy_no",
	EffDate:              "policy.eff_date",
	ExpDate:              "policy.exp_date",
	ReportGenerationDate: "policy.report_generation_date",
	CancelDate:           "policy.cancel_date",
	NoLoss:               "policy.no_loss",
	TotalPaid:            "policy.total_paid",
	TotalReserve:         "policy.total_reserve",
	TotalRecovered:       "policy.total_recovered",
	TotalIncurred:        "policy.total_incurred",
}

// Generated where

type whereHelperfloat64 struct{ field string }

func (w whereHelperfloat64) EQ(x float64) qm.QueryMod { return qmhelper.Where(w.field, qmhelper.EQ, x) }
func (w whereHelperfloat64) NEQ(x float64) qm.QueryMod {
	return qmhelper.Where(w.field, qmhelper.NEQ, x)
}
func (w whereHelperfloat64) LT(x float64) qm.QueryMod { return qmhelper.Where(w.field, qmhelper.LT, x) }
func (w whereHelperfloat64) LTE(x float64) qm.QueryMod {
	return qmhelper.Where(w.field, qmhelper.LTE, x)
}
func (w whereHelperfloat64) GT(x float64) qm.QueryMod { return qmhelper.Where(w.field, qmhelper.GT, x) }
func (w whereHelperfloat64) GTE(x float64) qm.QueryMod {
	return qmhelper.Where(w.field, qmhelper.GTE, x)
}
func (w whereHelperfloat64) IN(slice []float64) qm.QueryMod {
	values := make([]interface{}, 0, len(slice))
	for _, value := range slice {
		values = append(values, value)
	}
	return qm.WhereIn(fmt.Sprintf("%s IN ?", w.field), values...)
}
func (w whereHelperfloat64) NIN(slice []float64) qm.QueryMod {
	values := make([]interface{}, 0, len(slice))
	for _, value := range slice {
		values = append(values, value)
	}
	return qm.WhereNotIn(fmt.Sprintf("%s NOT IN ?", w.field), values...)
}

var PolicyWhere = struct {
	ID                   whereHelperstring
	DocumentID           whereHelperstring
	CreatedAt            whereHelpertime_Time
	PolicySN             whereHelperint
	Lob                  whereHelpernull_String
	Insurer              whereHelpernull_String
	Insured              whereHelpernull_String
	Agent                whereHelpernull_String
	PolicyNo             whereHelpernull_String
	EffDate              whereHelpernull_Time
	ExpDate              whereHelpernull_Time
	ReportGenerationDate whereHelpernull_Time
	CancelDate           whereHelpernull_Time
	NoLoss               whereHelperint
	TotalPaid            whereHelpernull_Float64
	TotalReserve         whereHelpernull_Float64
	TotalRecovered       whereHelpernull_Float64
	TotalIncurred        whereHelperfloat64
}{
	ID:                   whereHelperstring{field: "\"parsed_loss_runs\".\"policy\".\"id\""},
	DocumentID:           whereHelperstring{field: "\"parsed_loss_runs\".\"policy\".\"document_id\""},
	CreatedAt:            whereHelpertime_Time{field: "\"parsed_loss_runs\".\"policy\".\"created_at\""},
	PolicySN:             whereHelperint{field: "\"parsed_loss_runs\".\"policy\".\"policy_sn\""},
	Lob:                  whereHelpernull_String{field: "\"parsed_loss_runs\".\"policy\".\"lob\""},
	Insurer:              whereHelpernull_String{field: "\"parsed_loss_runs\".\"policy\".\"insurer\""},
	Insured:              whereHelpernull_String{field: "\"parsed_loss_runs\".\"policy\".\"insured\""},
	Agent:                whereHelpernull_String{field: "\"parsed_loss_runs\".\"policy\".\"agent\""},
	PolicyNo:             whereHelpernull_String{field: "\"parsed_loss_runs\".\"policy\".\"policy_no\""},
	EffDate:              whereHelpernull_Time{field: "\"parsed_loss_runs\".\"policy\".\"eff_date\""},
	ExpDate:              whereHelpernull_Time{field: "\"parsed_loss_runs\".\"policy\".\"exp_date\""},
	ReportGenerationDate: whereHelpernull_Time{field: "\"parsed_loss_runs\".\"policy\".\"report_generation_date\""},
	CancelDate:           whereHelpernull_Time{field: "\"parsed_loss_runs\".\"policy\".\"cancel_date\""},
	NoLoss:               whereHelperint{field: "\"parsed_loss_runs\".\"policy\".\"no_loss\""},
	TotalPaid:            whereHelpernull_Float64{field: "\"parsed_loss_runs\".\"policy\".\"total_paid\""},
	TotalReserve:         whereHelpernull_Float64{field: "\"parsed_loss_runs\".\"policy\".\"total_reserve\""},
	TotalRecovered:       whereHelpernull_Float64{field: "\"parsed_loss_runs\".\"policy\".\"total_recovered\""},
	TotalIncurred:        whereHelperfloat64{field: "\"parsed_loss_runs\".\"policy\".\"total_incurred\""},
}

// PolicyRels is where relationship names are stored.
var PolicyRels = struct {
	Document string
}{
	Document: "Document",
}

// policyR is where relationships are stored.
type policyR struct {
	Document *Document `boil:"Document" json:"Document" toml:"Document" yaml:"Document"`
}

// NewStruct creates a new relationship struct
func (*policyR) NewStruct() *policyR {
	return &policyR{}
}

// policyL is where Load methods for each relationship are stored.
type policyL struct{}

var (
	policyAllColumns            = []string{"id", "document_id", "created_at", "policy_sn", "lob", "insurer", "insured", "agent", "policy_no", "eff_date", "exp_date", "report_generation_date", "cancel_date", "no_loss", "total_paid", "total_reserve", "total_recovered", "total_incurred"}
	policyColumnsWithoutDefault = []string{"id", "document_id", "created_at", "policy_sn", "no_loss", "total_incurred"}
	policyColumnsWithDefault    = []string{"lob", "insurer", "insured", "agent", "policy_no", "eff_date", "exp_date", "report_generation_date", "cancel_date", "total_paid", "total_reserve", "total_recovered"}
	policyPrimaryKeyColumns     = []string{"id"}
	policyGeneratedColumns      = []string{}
)

type (
	// PolicySlice is an alias for a slice of pointers to Policy.
	// This should almost always be used instead of []Policy.
	PolicySlice []*Policy
	// PolicyHook is the signature for custom Policy hook methods
	PolicyHook func(context.Context, boil.ContextExecutor, *Policy) error

	policyQuery struct {
		*queries.Query
	}
)

// Cache for insert, update and upsert
var (
	policyType                 = reflect.TypeOf(&Policy{})
	policyMapping              = queries.MakeStructMapping(policyType)
	policyPrimaryKeyMapping, _ = queries.BindMapping(policyType, policyMapping, policyPrimaryKeyColumns)
	policyInsertCacheMut       sync.RWMutex
	policyInsertCache          = make(map[string]insertCache)
	policyUpdateCacheMut       sync.RWMutex
	policyUpdateCache          = make(map[string]updateCache)
	policyUpsertCacheMut       sync.RWMutex
	policyUpsertCache          = make(map[string]insertCache)
)

var (
	// Force time package dependency for automated UpdatedAt/CreatedAt.
	_ = time.Second
	// Force qmhelper dependency for where clause generation (which doesn't
	// always happen)
	_ = qmhelper.Where
)

var policyAfterSelectHooks []PolicyHook

var policyBeforeInsertHooks []PolicyHook
var policyAfterInsertHooks []PolicyHook

var policyBeforeUpdateHooks []PolicyHook
var policyAfterUpdateHooks []PolicyHook

var policyBeforeDeleteHooks []PolicyHook
var policyAfterDeleteHooks []PolicyHook

var policyBeforeUpsertHooks []PolicyHook
var policyAfterUpsertHooks []PolicyHook

// doAfterSelectHooks executes all "after Select" hooks.
func (o *Policy) doAfterSelectHooks(ctx context.Context, exec boil.ContextExecutor) (err error) {
	if boil.HooksAreSkipped(ctx) {
		return nil
	}

	for _, hook := range policyAfterSelectHooks {
		if err := hook(ctx, exec, o); err != nil {
			return err
		}
	}

	return nil
}

// doBeforeInsertHooks executes all "before insert" hooks.
func (o *Policy) doBeforeInsertHooks(ctx context.Context, exec boil.ContextExecutor) (err error) {
	if boil.HooksAreSkipped(ctx) {
		return nil
	}

	for _, hook := range policyBeforeInsertHooks {
		if err := hook(ctx, exec, o); err != nil {
			return err
		}
	}

	return nil
}

// doAfterInsertHooks executes all "after Insert" hooks.
func (o *Policy) doAfterInsertHooks(ctx context.Context, exec boil.ContextExecutor) (err error) {
	if boil.HooksAreSkipped(ctx) {
		return nil
	}

	for _, hook := range policyAfterInsertHooks {
		if err := hook(ctx, exec, o); err != nil {
			return err
		}
	}

	return nil
}

// doBeforeUpdateHooks executes all "before Update" hooks.
func (o *Policy) doBeforeUpdateHooks(ctx context.Context, exec boil.ContextExecutor) (err error) {
	if boil.HooksAreSkipped(ctx) {
		return nil
	}

	for _, hook := range policyBeforeUpdateHooks {
		if err := hook(ctx, exec, o); err != nil {
			return err
		}
	}

	return nil
}

// doAfterUpdateHooks executes all "after Update" hooks.
func (o *Policy) doAfterUpdateHooks(ctx context.Context, exec boil.ContextExecutor) (err error) {
	if boil.HooksAreSkipped(ctx) {
		return nil
	}

	for _, hook := range policyAfterUpdateHooks {
		if err := hook(ctx, exec, o); err != nil {
			return err
		}
	}

	return nil
}

// doBeforeDeleteHooks executes all "before Delete" hooks.
func (o *Policy) doBeforeDeleteHooks(ctx context.Context, exec boil.ContextExecutor) (err error) {
	if boil.HooksAreSkipped(ctx) {
		return nil
	}

	for _, hook := range policyBeforeDeleteHooks {
		if err := hook(ctx, exec, o); err != nil {
			return err
		}
	}

	return nil
}

// doAfterDeleteHooks executes all "after Delete" hooks.
func (o *Policy) doAfterDeleteHooks(ctx context.Context, exec boil.ContextExecutor) (err error) {
	if boil.HooksAreSkipped(ctx) {
		return nil
	}

	for _, hook := range policyAfterDeleteHooks {
		if err := hook(ctx, exec, o); err != nil {
			return err
		}
	}

	return nil
}

// doBeforeUpsertHooks executes all "before Upsert" hooks.
func (o *Policy) doBeforeUpsertHooks(ctx context.Context, exec boil.ContextExecutor) (err error) {
	if boil.HooksAreSkipped(ctx) {
		return nil
	}

	for _, hook := range policyBeforeUpsertHooks {
		if err := hook(ctx, exec, o); err != nil {
			return err
		}
	}

	return nil
}

// doAfterUpsertHooks executes all "after Upsert" hooks.
func (o *Policy) doAfterUpsertHooks(ctx context.Context, exec boil.ContextExecutor) (err error) {
	if boil.HooksAreSkipped(ctx) {
		return nil
	}

	for _, hook := range policyAfterUpsertHooks {
		if err := hook(ctx, exec, o); err != nil {
			return err
		}
	}

	return nil
}

// AddPolicyHook registers your hook function for all future operations.
func AddPolicyHook(hookPoint boil.HookPoint, policyHook PolicyHook) {
	switch hookPoint {
	case boil.AfterSelectHook:
		policyAfterSelectHooks = append(policyAfterSelectHooks, policyHook)
	case boil.BeforeInsertHook:
		policyBeforeInsertHooks = append(policyBeforeInsertHooks, policyHook)
	case boil.AfterInsertHook:
		policyAfterInsertHooks = append(policyAfterInsertHooks, policyHook)
	case boil.BeforeUpdateHook:
		policyBeforeUpdateHooks = append(policyBeforeUpdateHooks, policyHook)
	case boil.AfterUpdateHook:
		policyAfterUpdateHooks = append(policyAfterUpdateHooks, policyHook)
	case boil.BeforeDeleteHook:
		policyBeforeDeleteHooks = append(policyBeforeDeleteHooks, policyHook)
	case boil.AfterDeleteHook:
		policyAfterDeleteHooks = append(policyAfterDeleteHooks, policyHook)
	case boil.BeforeUpsertHook:
		policyBeforeUpsertHooks = append(policyBeforeUpsertHooks, policyHook)
	case boil.AfterUpsertHook:
		policyAfterUpsertHooks = append(policyAfterUpsertHooks, policyHook)
	}
}

// One returns a single policy record from the query.
func (q policyQuery) One(ctx context.Context, exec boil.ContextExecutor) (*Policy, error) {
	o := &Policy{}

	queries.SetLimit(q.Query, 1)

	err := q.Bind(ctx, exec, o)
	if err != nil {
		if errors.Is(err, sql.ErrNoRows) {
			return nil, sql.ErrNoRows
		}
		return nil, errors.Wrap(err, "parsed_loss_runs: failed to execute a one query for policy")
	}

	if err := o.doAfterSelectHooks(ctx, exec); err != nil {
		return o, err
	}

	return o, nil
}

// All returns all Policy records from the query.
func (q policyQuery) All(ctx context.Context, exec boil.ContextExecutor) (PolicySlice, error) {
	var o []*Policy

	err := q.Bind(ctx, exec, &o)
	if err != nil {
		return nil, errors.Wrap(err, "parsed_loss_runs: failed to assign all query results to Policy slice")
	}

	if len(policyAfterSelectHooks) != 0 {
		for _, obj := range o {
			if err := obj.doAfterSelectHooks(ctx, exec); err != nil {
				return o, err
			}
		}
	}

	return o, nil
}

// Count returns the count of all Policy records in the query.
func (q policyQuery) Count(ctx context.Context, exec boil.ContextExecutor) (int64, error) {
	var count int64

	queries.SetSelect(q.Query, nil)
	queries.SetCount(q.Query)

	err := q.Query.QueryRowContext(ctx, exec).Scan(&count)
	if err != nil {
		return 0, errors.Wrap(err, "parsed_loss_runs: failed to count policy rows")
	}

	return count, nil
}

// Exists checks if the row exists in the table.
func (q policyQuery) Exists(ctx context.Context, exec boil.ContextExecutor) (bool, error) {
	var count int64

	queries.SetSelect(q.Query, nil)
	queries.SetCount(q.Query)
	queries.SetLimit(q.Query, 1)

	err := q.Query.QueryRowContext(ctx, exec).Scan(&count)
	if err != nil {
		return false, errors.Wrap(err, "parsed_loss_runs: failed to check if policy exists")
	}

	return count > 0, nil
}

// Document pointed to by the foreign key.
func (o *Policy) Document(mods ...qm.QueryMod) documentQuery {
	queryMods := []qm.QueryMod{
		qm.Where("\"id\" = ?", o.DocumentID),
	}

	queryMods = append(queryMods, mods...)

	return Documents(queryMods...)
}

// LoadDocument allows an eager lookup of values, cached into the
// loaded structs of the objects. This is for an N-1 relationship.
func (policyL) LoadDocument(ctx context.Context, e boil.ContextExecutor, singular bool, maybePolicy interface{}, mods queries.Applicator) error {
	var slice []*Policy
	var object *Policy

	if singular {
		object = maybePolicy.(*Policy)
	} else {
		slice = *maybePolicy.(*[]*Policy)
	}

	args := make([]interface{}, 0, 1)
	if singular {
		if object.R == nil {
			object.R = &policyR{}
		}
		args = append(args, object.DocumentID)

	} else {
	Outer:
		for _, obj := range slice {
			if obj.R == nil {
				obj.R = &policyR{}
			}

			for _, a := range args {
				if a == obj.DocumentID {
					continue Outer
				}
			}

			args = append(args, obj.DocumentID)

		}
	}

	if len(args) == 0 {
		return nil
	}

	query := NewQuery(
		qm.From(`parsed_loss_runs.document`),
		qm.WhereIn(`parsed_loss_runs.document.id in ?`, args...),
	)
	if mods != nil {
		mods.Apply(query)
	}

	results, err := query.QueryContext(ctx, e)
	if err != nil {
		return errors.Wrap(err, "failed to eager load Document")
	}

	var resultSlice []*Document
	if err = queries.Bind(results, &resultSlice); err != nil {
		return errors.Wrap(err, "failed to bind eager loaded slice Document")
	}

	if err = results.Close(); err != nil {
		return errors.Wrap(err, "failed to close results of eager load for document")
	}
	if err = results.Err(); err != nil {
		return errors.Wrap(err, "error occurred during iteration of eager loaded relations for document")
	}

	if len(policyAfterSelectHooks) != 0 {
		for _, obj := range resultSlice {
			if err := obj.doAfterSelectHooks(ctx, e); err != nil {
				return err
			}
		}
	}

	if len(resultSlice) == 0 {
		return nil
	}

	if singular {
		foreign := resultSlice[0]
		object.R.Document = foreign
		if foreign.R == nil {
			foreign.R = &documentR{}
		}
		foreign.R.Policies = append(foreign.R.Policies, object)
		return nil
	}

	for _, local := range slice {
		for _, foreign := range resultSlice {
			if local.DocumentID == foreign.ID {
				local.R.Document = foreign
				if foreign.R == nil {
					foreign.R = &documentR{}
				}
				foreign.R.Policies = append(foreign.R.Policies, local)
				break
			}
		}
	}

	return nil
}

// SetDocument of the policy to the related item.
// Sets o.R.Document to related.
// Adds o to related.R.Policies.
func (o *Policy) SetDocument(ctx context.Context, exec boil.ContextExecutor, insert bool, related *Document) error {
	var err error
	if insert {
		if err = related.Insert(ctx, exec, boil.Infer()); err != nil {
			return errors.Wrap(err, "failed to insert into foreign table")
		}
	}

	updateQuery := fmt.Sprintf(
		"UPDATE \"parsed_loss_runs\".\"policy\" SET %s WHERE %s",
		strmangle.SetParamNames("\"", "\"", 1, []string{"document_id"}),
		strmangle.WhereClause("\"", "\"", 2, policyPrimaryKeyColumns),
	)
	values := []interface{}{related.ID, o.ID}

	if boil.IsDebug(ctx) {
		writer := boil.DebugWriterFrom(ctx)
		fmt.Fprintln(writer, updateQuery)
		fmt.Fprintln(writer, values)
	}
	if _, err = exec.ExecContext(ctx, updateQuery, values...); err != nil {
		return errors.Wrap(err, "failed to update local table")
	}

	o.DocumentID = related.ID
	if o.R == nil {
		o.R = &policyR{
			Document: related,
		}
	} else {
		o.R.Document = related
	}

	if related.R == nil {
		related.R = &documentR{
			Policies: PolicySlice{o},
		}
	} else {
		related.R.Policies = append(related.R.Policies, o)
	}

	return nil
}

// Policies retrieves all the records using an executor.
func Policies(mods ...qm.QueryMod) policyQuery {
	mods = append(mods, qm.From("\"parsed_loss_runs\".\"policy\""))
	q := NewQuery(mods...)
	if len(queries.GetSelect(q)) == 0 {
		queries.SetSelect(q, []string{"\"parsed_loss_runs\".\"policy\".*"})
	}

	return policyQuery{q}
}

// FindPolicy retrieves a single record by ID with an executor.
// If selectCols is empty Find will return all columns.
func FindPolicy(ctx context.Context, exec boil.ContextExecutor, iD string, selectCols ...string) (*Policy, error) {
	policyObj := &Policy{}

	sel := "*"
	if len(selectCols) > 0 {
		sel = strings.Join(strmangle.IdentQuoteSlice(dialect.LQ, dialect.RQ, selectCols), ",")
	}
	query := fmt.Sprintf(
		"select %s from \"parsed_loss_runs\".\"policy\" where \"id\"=$1", sel,
	)

	q := queries.Raw(query, iD)

	err := q.Bind(ctx, exec, policyObj)
	if err != nil {
		if errors.Is(err, sql.ErrNoRows) {
			return nil, sql.ErrNoRows
		}
		return nil, errors.Wrap(err, "parsed_loss_runs: unable to select from policy")
	}

	if err = policyObj.doAfterSelectHooks(ctx, exec); err != nil {
		return policyObj, err
	}

	return policyObj, nil
}

// Insert a single record using an executor.
// See boil.Columns.InsertColumnSet documentation to understand column list inference for inserts.
func (o *Policy) Insert(ctx context.Context, exec boil.ContextExecutor, columns boil.Columns) error {
	if o == nil {
		return errors.New("parsed_loss_runs: no policy provided for insertion")
	}

	var err error
	if !boil.TimestampsAreSkipped(ctx) {
		currTime := time.Now().In(boil.GetLocation())

		if o.CreatedAt.IsZero() {
			o.CreatedAt = currTime
		}
	}

	if err := o.doBeforeInsertHooks(ctx, exec); err != nil {
		return err
	}

	nzDefaults := queries.NonZeroDefaultSet(policyColumnsWithDefault, o)

	key := makeCacheKey(columns, nzDefaults)
	policyInsertCacheMut.RLock()
	cache, cached := policyInsertCache[key]
	policyInsertCacheMut.RUnlock()

	if !cached {
		wl, returnColumns := columns.InsertColumnSet(
			policyAllColumns,
			policyColumnsWithDefault,
			policyColumnsWithoutDefault,
			nzDefaults,
		)

		cache.valueMapping, err = queries.BindMapping(policyType, policyMapping, wl)
		if err != nil {
			return err
		}
		cache.retMapping, err = queries.BindMapping(policyType, policyMapping, returnColumns)
		if err != nil {
			return err
		}
		if len(wl) != 0 {
			cache.query = fmt.Sprintf("INSERT INTO \"parsed_loss_runs\".\"policy\" (\"%s\") %%sVALUES (%s)%%s", strings.Join(wl, "\",\""), strmangle.Placeholders(dialect.UseIndexPlaceholders, len(wl), 1, 1))
		} else {
			cache.query = "INSERT INTO \"parsed_loss_runs\".\"policy\" %sDEFAULT VALUES%s"
		}

		var queryOutput, queryReturning string

		if len(cache.retMapping) != 0 {
			queryReturning = fmt.Sprintf(" RETURNING \"%s\"", strings.Join(returnColumns, "\",\""))
		}

		cache.query = fmt.Sprintf(cache.query, queryOutput, queryReturning)
	}

	value := reflect.Indirect(reflect.ValueOf(o))
	vals := queries.ValuesFromMapping(value, cache.valueMapping)

	if boil.IsDebug(ctx) {
		writer := boil.DebugWriterFrom(ctx)
		fmt.Fprintln(writer, cache.query)
		fmt.Fprintln(writer, vals)
	}

	if len(cache.retMapping) != 0 {
		err = exec.QueryRowContext(ctx, cache.query, vals...).Scan(queries.PtrsFromMapping(value, cache.retMapping)...)
	} else {
		_, err = exec.ExecContext(ctx, cache.query, vals...)
	}

	if err != nil {
		return errors.Wrap(err, "parsed_loss_runs: unable to insert into policy")
	}

	if !cached {
		policyInsertCacheMut.Lock()
		policyInsertCache[key] = cache
		policyInsertCacheMut.Unlock()
	}

	return o.doAfterInsertHooks(ctx, exec)
}

// Update uses an executor to update the Policy.
// See boil.Columns.UpdateColumnSet documentation to understand column list inference for updates.
// Update does not automatically update the record in case of default values. Use .Reload() to refresh the records.
func (o *Policy) Update(ctx context.Context, exec boil.ContextExecutor, columns boil.Columns) (int64, error) {
	var err error
	if err = o.doBeforeUpdateHooks(ctx, exec); err != nil {
		return 0, err
	}
	key := makeCacheKey(columns, nil)
	policyUpdateCacheMut.RLock()
	cache, cached := policyUpdateCache[key]
	policyUpdateCacheMut.RUnlock()

	if !cached {
		wl := columns.UpdateColumnSet(
			policyAllColumns,
			policyPrimaryKeyColumns,
		)

		if !columns.IsWhitelist() {
			wl = strmangle.SetComplement(wl, []string{"created_at"})
		}
		if len(wl) == 0 {
			return 0, errors.New("parsed_loss_runs: unable to update policy, could not build whitelist")
		}

		cache.query = fmt.Sprintf("UPDATE \"parsed_loss_runs\".\"policy\" SET %s WHERE %s",
			strmangle.SetParamNames("\"", "\"", 1, wl),
			strmangle.WhereClause("\"", "\"", len(wl)+1, policyPrimaryKeyColumns),
		)
		cache.valueMapping, err = queries.BindMapping(policyType, policyMapping, append(wl, policyPrimaryKeyColumns...))
		if err != nil {
			return 0, err
		}
	}

	values := queries.ValuesFromMapping(reflect.Indirect(reflect.ValueOf(o)), cache.valueMapping)

	if boil.IsDebug(ctx) {
		writer := boil.DebugWriterFrom(ctx)
		fmt.Fprintln(writer, cache.query)
		fmt.Fprintln(writer, values)
	}
	var result sql.Result
	result, err = exec.ExecContext(ctx, cache.query, values...)
	if err != nil {
		return 0, errors.Wrap(err, "parsed_loss_runs: unable to update policy row")
	}

	rowsAff, err := result.RowsAffected()
	if err != nil {
		return 0, errors.Wrap(err, "parsed_loss_runs: failed to get rows affected by update for policy")
	}

	if !cached {
		policyUpdateCacheMut.Lock()
		policyUpdateCache[key] = cache
		policyUpdateCacheMut.Unlock()
	}

	return rowsAff, o.doAfterUpdateHooks(ctx, exec)
}

// UpdateAll updates all rows with the specified column values.
func (q policyQuery) UpdateAll(ctx context.Context, exec boil.ContextExecutor, cols M) (int64, error) {
	queries.SetUpdate(q.Query, cols)

	result, err := q.Query.ExecContext(ctx, exec)
	if err != nil {
		return 0, errors.Wrap(err, "parsed_loss_runs: unable to update all for policy")
	}

	rowsAff, err := result.RowsAffected()
	if err != nil {
		return 0, errors.Wrap(err, "parsed_loss_runs: unable to retrieve rows affected for policy")
	}

	return rowsAff, nil
}

// UpdateAll updates all rows with the specified column values, using an executor.
func (o PolicySlice) UpdateAll(ctx context.Context, exec boil.ContextExecutor, cols M) (int64, error) {
	ln := int64(len(o))
	if ln == 0 {
		return 0, nil
	}

	if len(cols) == 0 {
		return 0, errors.New("parsed_loss_runs: update all requires at least one column argument")
	}

	colNames := make([]string, len(cols))
	args := make([]interface{}, len(cols))

	i := 0
	for name, value := range cols {
		colNames[i] = name
		args[i] = value
		i++
	}

	// Append all of the primary key values for each column
	for _, obj := range o {
		pkeyArgs := queries.ValuesFromMapping(reflect.Indirect(reflect.ValueOf(obj)), policyPrimaryKeyMapping)
		args = append(args, pkeyArgs...)
	}

	sql := fmt.Sprintf("UPDATE \"parsed_loss_runs\".\"policy\" SET %s WHERE %s",
		strmangle.SetParamNames("\"", "\"", 1, colNames),
		strmangle.WhereClauseRepeated(string(dialect.LQ), string(dialect.RQ), len(colNames)+1, policyPrimaryKeyColumns, len(o)))

	if boil.IsDebug(ctx) {
		writer := boil.DebugWriterFrom(ctx)
		fmt.Fprintln(writer, sql)
		fmt.Fprintln(writer, args...)
	}
	result, err := exec.ExecContext(ctx, sql, args...)
	if err != nil {
		return 0, errors.Wrap(err, "parsed_loss_runs: unable to update all in policy slice")
	}

	rowsAff, err := result.RowsAffected()
	if err != nil {
		return 0, errors.Wrap(err, "parsed_loss_runs: unable to retrieve rows affected all in update all policy")
	}
	return rowsAff, nil
}

// Upsert attempts an insert using an executor, and does an update or ignore on conflict.
// See boil.Columns documentation for how to properly use updateColumns and insertColumns.
func (o *Policy) Upsert(ctx context.Context, exec boil.ContextExecutor, updateOnConflict bool, conflictColumns []string, updateColumns, insertColumns boil.Columns) error {
	if o == nil {
		return errors.New("parsed_loss_runs: no policy provided for upsert")
	}
	if !boil.TimestampsAreSkipped(ctx) {
		currTime := time.Now().In(boil.GetLocation())

		if o.CreatedAt.IsZero() {
			o.CreatedAt = currTime
		}
	}

	if err := o.doBeforeUpsertHooks(ctx, exec); err != nil {
		return err
	}

	nzDefaults := queries.NonZeroDefaultSet(policyColumnsWithDefault, o)

	// Build cache key in-line uglily - mysql vs psql problems
	buf := strmangle.GetBuffer()
	if updateOnConflict {
		buf.WriteByte('t')
	} else {
		buf.WriteByte('f')
	}
	buf.WriteByte('.')
	for _, c := range conflictColumns {
		buf.WriteString(c)
	}
	buf.WriteByte('.')
	buf.WriteString(strconv.Itoa(updateColumns.Kind))
	for _, c := range updateColumns.Cols {
		buf.WriteString(c)
	}
	buf.WriteByte('.')
	buf.WriteString(strconv.Itoa(insertColumns.Kind))
	for _, c := range insertColumns.Cols {
		buf.WriteString(c)
	}
	buf.WriteByte('.')
	for _, c := range nzDefaults {
		buf.WriteString(c)
	}
	key := buf.String()
	strmangle.PutBuffer(buf)

	policyUpsertCacheMut.RLock()
	cache, cached := policyUpsertCache[key]
	policyUpsertCacheMut.RUnlock()

	var err error

	if !cached {
		insert, ret := insertColumns.InsertColumnSet(
			policyAllColumns,
			policyColumnsWithDefault,
			policyColumnsWithoutDefault,
			nzDefaults,
		)

		update := updateColumns.UpdateColumnSet(
			policyAllColumns,
			policyPrimaryKeyColumns,
		)

		if updateOnConflict && len(update) == 0 {
			return errors.New("parsed_loss_runs: unable to upsert policy, could not build update column list")
		}

		conflict := conflictColumns
		if len(conflict) == 0 {
			conflict = make([]string, len(policyPrimaryKeyColumns))
			copy(conflict, policyPrimaryKeyColumns)
		}
		cache.query = buildUpsertQueryPostgres(dialect, "\"parsed_loss_runs\".\"policy\"", updateOnConflict, ret, update, conflict, insert)

		cache.valueMapping, err = queries.BindMapping(policyType, policyMapping, insert)
		if err != nil {
			return err
		}
		if len(ret) != 0 {
			cache.retMapping, err = queries.BindMapping(policyType, policyMapping, ret)
			if err != nil {
				return err
			}
		}
	}

	value := reflect.Indirect(reflect.ValueOf(o))
	vals := queries.ValuesFromMapping(value, cache.valueMapping)
	var returns []interface{}
	if len(cache.retMapping) != 0 {
		returns = queries.PtrsFromMapping(value, cache.retMapping)
	}

	if boil.IsDebug(ctx) {
		writer := boil.DebugWriterFrom(ctx)
		fmt.Fprintln(writer, cache.query)
		fmt.Fprintln(writer, vals)
	}
	if len(cache.retMapping) != 0 {
		err = exec.QueryRowContext(ctx, cache.query, vals...).Scan(returns...)
		if errors.Is(err, sql.ErrNoRows) {
			err = nil // Postgres doesn't return anything when there's no update
		}
	} else {
		_, err = exec.ExecContext(ctx, cache.query, vals...)
	}
	if err != nil {
		return errors.Wrap(err, "parsed_loss_runs: unable to upsert policy")
	}

	if !cached {
		policyUpsertCacheMut.Lock()
		policyUpsertCache[key] = cache
		policyUpsertCacheMut.Unlock()
	}

	return o.doAfterUpsertHooks(ctx, exec)
}

// Delete deletes a single Policy record with an executor.
// Delete will match against the primary key column to find the record to delete.
func (o *Policy) Delete(ctx context.Context, exec boil.ContextExecutor) (int64, error) {
	if o == nil {
		return 0, errors.New("parsed_loss_runs: no Policy provided for delete")
	}

	if err := o.doBeforeDeleteHooks(ctx, exec); err != nil {
		return 0, err
	}

	args := queries.ValuesFromMapping(reflect.Indirect(reflect.ValueOf(o)), policyPrimaryKeyMapping)
	sql := "DELETE FROM \"parsed_loss_runs\".\"policy\" WHERE \"id\"=$1"

	if boil.IsDebug(ctx) {
		writer := boil.DebugWriterFrom(ctx)
		fmt.Fprintln(writer, sql)
		fmt.Fprintln(writer, args...)
	}
	result, err := exec.ExecContext(ctx, sql, args...)
	if err != nil {
		return 0, errors.Wrap(err, "parsed_loss_runs: unable to delete from policy")
	}

	rowsAff, err := result.RowsAffected()
	if err != nil {
		return 0, errors.Wrap(err, "parsed_loss_runs: failed to get rows affected by delete for policy")
	}

	if err := o.doAfterDeleteHooks(ctx, exec); err != nil {
		return 0, err
	}

	return rowsAff, nil
}

// DeleteAll deletes all matching rows.
func (q policyQuery) DeleteAll(ctx context.Context, exec boil.ContextExecutor) (int64, error) {
	if q.Query == nil {
		return 0, errors.New("parsed_loss_runs: no policyQuery provided for delete all")
	}

	queries.SetDelete(q.Query)

	result, err := q.Query.ExecContext(ctx, exec)
	if err != nil {
		return 0, errors.Wrap(err, "parsed_loss_runs: unable to delete all from policy")
	}

	rowsAff, err := result.RowsAffected()
	if err != nil {
		return 0, errors.Wrap(err, "parsed_loss_runs: failed to get rows affected by deleteall for policy")
	}

	return rowsAff, nil
}

// DeleteAll deletes all rows in the slice, using an executor.
func (o PolicySlice) DeleteAll(ctx context.Context, exec boil.ContextExecutor) (int64, error) {
	if len(o) == 0 {
		return 0, nil
	}

	if len(policyBeforeDeleteHooks) != 0 {
		for _, obj := range o {
			if err := obj.doBeforeDeleteHooks(ctx, exec); err != nil {
				return 0, err
			}
		}
	}

	var args []interface{}
	for _, obj := range o {
		pkeyArgs := queries.ValuesFromMapping(reflect.Indirect(reflect.ValueOf(obj)), policyPrimaryKeyMapping)
		args = append(args, pkeyArgs...)
	}

	sql := "DELETE FROM \"parsed_loss_runs\".\"policy\" WHERE " +
		strmangle.WhereClauseRepeated(string(dialect.LQ), string(dialect.RQ), 1, policyPrimaryKeyColumns, len(o))

	if boil.IsDebug(ctx) {
		writer := boil.DebugWriterFrom(ctx)
		fmt.Fprintln(writer, sql)
		fmt.Fprintln(writer, args)
	}
	result, err := exec.ExecContext(ctx, sql, args...)
	if err != nil {
		return 0, errors.Wrap(err, "parsed_loss_runs: unable to delete all from policy slice")
	}

	rowsAff, err := result.RowsAffected()
	if err != nil {
		return 0, errors.Wrap(err, "parsed_loss_runs: failed to get rows affected by deleteall for policy")
	}

	if len(policyAfterDeleteHooks) != 0 {
		for _, obj := range o {
			if err := obj.doAfterDeleteHooks(ctx, exec); err != nil {
				return 0, err
			}
		}
	}

	return rowsAff, nil
}

// Reload refetches the object from the database
// using the primary keys with an executor.
func (o *Policy) Reload(ctx context.Context, exec boil.ContextExecutor) error {
	ret, err := FindPolicy(ctx, exec, o.ID)
	if err != nil {
		return err
	}

	*o = *ret
	return nil
}

// ReloadAll refetches every row with matching primary key column values
// and overwrites the original object slice with the newly updated slice.
func (o *PolicySlice) ReloadAll(ctx context.Context, exec boil.ContextExecutor) error {
	if o == nil || len(*o) == 0 {
		return nil
	}

	slice := PolicySlice{}
	var args []interface{}
	for _, obj := range *o {
		pkeyArgs := queries.ValuesFromMapping(reflect.Indirect(reflect.ValueOf(obj)), policyPrimaryKeyMapping)
		args = append(args, pkeyArgs...)
	}

	sql := "SELECT \"parsed_loss_runs\".\"policy\".* FROM \"parsed_loss_runs\".\"policy\" WHERE " +
		strmangle.WhereClauseRepeated(string(dialect.LQ), string(dialect.RQ), 1, policyPrimaryKeyColumns, len(*o))

	q := queries.Raw(sql, args...)

	err := q.Bind(ctx, exec, &slice)
	if err != nil {
		return errors.Wrap(err, "parsed_loss_runs: unable to reload all in PolicySlice")
	}

	*o = slice

	return nil
}

// PolicyExists checks if the Policy row exists.
func PolicyExists(ctx context.Context, exec boil.ContextExecutor, iD string) (bool, error) {
	var exists bool
	sql := "select exists(select 1 from \"parsed_loss_runs\".\"policy\" where \"id\"=$1 limit 1)"

	if boil.IsDebug(ctx) {
		writer := boil.DebugWriterFrom(ctx)
		fmt.Fprintln(writer, sql)
		fmt.Fprintln(writer, iD)
	}
	row := exec.QueryRowContext(ctx, sql, iD)

	err := row.Scan(&exists)
	if err != nil {
		return false, errors.Wrap(err, "parsed_loss_runs: unable to check if policy exists")
	}

	return exists, nil
}
