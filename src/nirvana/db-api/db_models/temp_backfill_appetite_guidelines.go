// Code generated by SQLBoiler (https://github.com/volatiletech/sqlboiler). DO NOT EDIT.
// This file is meant to be re-generated in place and/or deleted at any time.

package db_models

import (
	"context"
	"database/sql"
	"fmt"
	"reflect"
	"strconv"
	"strings"
	"sync"
	"time"

	"github.com/friendsofgo/errors"
	"github.com/volatiletech/null/v8"
	"github.com/volatiletech/sqlboiler/v4/boil"
	"github.com/volatiletech/sqlboiler/v4/queries"
	"github.com/volatiletech/sqlboiler/v4/queries/qm"
	"github.com/volatiletech/sqlboiler/v4/queries/qmhelper"
	"github.com/volatiletech/sqlboiler/v4/types"
	"github.com/volatiletech/strmangle"
)

// TempBackfillAppetiteGuideline is an object representing the database table.
type TempBackfillAppetiteGuideline struct {
	ID                string      `boil:"id" json:"id" toml:"id" yaml:"id"`
	ApplicationReview string      `boil:"application_review" json:"application_review" toml:"application_review" yaml:"application_review"`
	RiskFactor        string      `boil:"risk_factor" json:"risk_factor" toml:"risk_factor" yaml:"risk_factor"`
	Value             types.JSON  `boil:"value" json:"value" toml:"value" yaml:"value"`
	DeclineThreshold  string      `boil:"decline_threshold" json:"decline_threshold" toml:"decline_threshold" yaml:"decline_threshold"`
	DeclineReason     null.String `boil:"decline_reason" json:"decline_reason,omitempty" toml:"decline_reason" yaml:"decline_reason,omitempty"`
	CreatedAt         time.Time   `boil:"created_at" json:"created_at" toml:"created_at" yaml:"created_at"`

	R *tempBackfillAppetiteGuidelineR `boil:"" json:"" toml:"" yaml:""`
	L tempBackfillAppetiteGuidelineL  `boil:"-" json:"-" toml:"-" yaml:"-"`
}

var TempBackfillAppetiteGuidelineColumns = struct {
	ID                string
	ApplicationReview string
	RiskFactor        string
	Value             string
	DeclineThreshold  string
	DeclineReason     string
	CreatedAt         string
}{
	ID:                "id",
	ApplicationReview: "application_review",
	RiskFactor:        "risk_factor",
	Value:             "value",
	DeclineThreshold:  "decline_threshold",
	DeclineReason:     "decline_reason",
	CreatedAt:         "created_at",
}

var TempBackfillAppetiteGuidelineTableColumns = struct {
	ID                string
	ApplicationReview string
	RiskFactor        string
	Value             string
	DeclineThreshold  string
	DeclineReason     string
	CreatedAt         string
}{
	ID:                "temp_backfill_appetite_guidelines.id",
	ApplicationReview: "temp_backfill_appetite_guidelines.application_review",
	RiskFactor:        "temp_backfill_appetite_guidelines.risk_factor",
	Value:             "temp_backfill_appetite_guidelines.value",
	DeclineThreshold:  "temp_backfill_appetite_guidelines.decline_threshold",
	DeclineReason:     "temp_backfill_appetite_guidelines.decline_reason",
	CreatedAt:         "temp_backfill_appetite_guidelines.created_at",
}

// Generated where

var TempBackfillAppetiteGuidelineWhere = struct {
	ID                whereHelperstring
	ApplicationReview whereHelperstring
	RiskFactor        whereHelperstring
	Value             whereHelpertypes_JSON
	DeclineThreshold  whereHelperstring
	DeclineReason     whereHelpernull_String
	CreatedAt         whereHelpertime_Time
}{
	ID:                whereHelperstring{field: "\"temp_backfill_appetite_guidelines\".\"id\""},
	ApplicationReview: whereHelperstring{field: "\"temp_backfill_appetite_guidelines\".\"application_review\""},
	RiskFactor:        whereHelperstring{field: "\"temp_backfill_appetite_guidelines\".\"risk_factor\""},
	Value:             whereHelpertypes_JSON{field: "\"temp_backfill_appetite_guidelines\".\"value\""},
	DeclineThreshold:  whereHelperstring{field: "\"temp_backfill_appetite_guidelines\".\"decline_threshold\""},
	DeclineReason:     whereHelpernull_String{field: "\"temp_backfill_appetite_guidelines\".\"decline_reason\""},
	CreatedAt:         whereHelpertime_Time{field: "\"temp_backfill_appetite_guidelines\".\"created_at\""},
}

// TempBackfillAppetiteGuidelineRels is where relationship names are stored.
var TempBackfillAppetiteGuidelineRels = struct {
}{}

// tempBackfillAppetiteGuidelineR is where relationships are stored.
type tempBackfillAppetiteGuidelineR struct {
}

// NewStruct creates a new relationship struct
func (*tempBackfillAppetiteGuidelineR) NewStruct() *tempBackfillAppetiteGuidelineR {
	return &tempBackfillAppetiteGuidelineR{}
}

// tempBackfillAppetiteGuidelineL is where Load methods for each relationship are stored.
type tempBackfillAppetiteGuidelineL struct{}

var (
	tempBackfillAppetiteGuidelineAllColumns            = []string{"id", "application_review", "risk_factor", "value", "decline_threshold", "decline_reason", "created_at"}
	tempBackfillAppetiteGuidelineColumnsWithoutDefault = []string{"id", "application_review", "risk_factor", "value", "decline_threshold"}
	tempBackfillAppetiteGuidelineColumnsWithDefault    = []string{"decline_reason", "created_at"}
	tempBackfillAppetiteGuidelinePrimaryKeyColumns     = []string{"id"}
	tempBackfillAppetiteGuidelineGeneratedColumns      = []string{}
)

type (
	// TempBackfillAppetiteGuidelineSlice is an alias for a slice of pointers to TempBackfillAppetiteGuideline.
	// This should almost always be used instead of []TempBackfillAppetiteGuideline.
	TempBackfillAppetiteGuidelineSlice []*TempBackfillAppetiteGuideline
	// TempBackfillAppetiteGuidelineHook is the signature for custom TempBackfillAppetiteGuideline hook methods
	TempBackfillAppetiteGuidelineHook func(context.Context, boil.ContextExecutor, *TempBackfillAppetiteGuideline) error

	tempBackfillAppetiteGuidelineQuery struct {
		*queries.Query
	}
)

// Cache for insert, update and upsert
var (
	tempBackfillAppetiteGuidelineType                 = reflect.TypeOf(&TempBackfillAppetiteGuideline{})
	tempBackfillAppetiteGuidelineMapping              = queries.MakeStructMapping(tempBackfillAppetiteGuidelineType)
	tempBackfillAppetiteGuidelinePrimaryKeyMapping, _ = queries.BindMapping(tempBackfillAppetiteGuidelineType, tempBackfillAppetiteGuidelineMapping, tempBackfillAppetiteGuidelinePrimaryKeyColumns)
	tempBackfillAppetiteGuidelineInsertCacheMut       sync.RWMutex
	tempBackfillAppetiteGuidelineInsertCache          = make(map[string]insertCache)
	tempBackfillAppetiteGuidelineUpdateCacheMut       sync.RWMutex
	tempBackfillAppetiteGuidelineUpdateCache          = make(map[string]updateCache)
	tempBackfillAppetiteGuidelineUpsertCacheMut       sync.RWMutex
	tempBackfillAppetiteGuidelineUpsertCache          = make(map[string]insertCache)
)

var (
	// Force time package dependency for automated UpdatedAt/CreatedAt.
	_ = time.Second
	// Force qmhelper dependency for where clause generation (which doesn't
	// always happen)
	_ = qmhelper.Where
)

var tempBackfillAppetiteGuidelineAfterSelectHooks []TempBackfillAppetiteGuidelineHook

var tempBackfillAppetiteGuidelineBeforeInsertHooks []TempBackfillAppetiteGuidelineHook
var tempBackfillAppetiteGuidelineAfterInsertHooks []TempBackfillAppetiteGuidelineHook

var tempBackfillAppetiteGuidelineBeforeUpdateHooks []TempBackfillAppetiteGuidelineHook
var tempBackfillAppetiteGuidelineAfterUpdateHooks []TempBackfillAppetiteGuidelineHook

var tempBackfillAppetiteGuidelineBeforeDeleteHooks []TempBackfillAppetiteGuidelineHook
var tempBackfillAppetiteGuidelineAfterDeleteHooks []TempBackfillAppetiteGuidelineHook

var tempBackfillAppetiteGuidelineBeforeUpsertHooks []TempBackfillAppetiteGuidelineHook
var tempBackfillAppetiteGuidelineAfterUpsertHooks []TempBackfillAppetiteGuidelineHook

// doAfterSelectHooks executes all "after Select" hooks.
func (o *TempBackfillAppetiteGuideline) doAfterSelectHooks(ctx context.Context, exec boil.ContextExecutor) (err error) {
	if boil.HooksAreSkipped(ctx) {
		return nil
	}

	for _, hook := range tempBackfillAppetiteGuidelineAfterSelectHooks {
		if err := hook(ctx, exec, o); err != nil {
			return err
		}
	}

	return nil
}

// doBeforeInsertHooks executes all "before insert" hooks.
func (o *TempBackfillAppetiteGuideline) doBeforeInsertHooks(ctx context.Context, exec boil.ContextExecutor) (err error) {
	if boil.HooksAreSkipped(ctx) {
		return nil
	}

	for _, hook := range tempBackfillAppetiteGuidelineBeforeInsertHooks {
		if err := hook(ctx, exec, o); err != nil {
			return err
		}
	}

	return nil
}

// doAfterInsertHooks executes all "after Insert" hooks.
func (o *TempBackfillAppetiteGuideline) doAfterInsertHooks(ctx context.Context, exec boil.ContextExecutor) (err error) {
	if boil.HooksAreSkipped(ctx) {
		return nil
	}

	for _, hook := range tempBackfillAppetiteGuidelineAfterInsertHooks {
		if err := hook(ctx, exec, o); err != nil {
			return err
		}
	}

	return nil
}

// doBeforeUpdateHooks executes all "before Update" hooks.
func (o *TempBackfillAppetiteGuideline) doBeforeUpdateHooks(ctx context.Context, exec boil.ContextExecutor) (err error) {
	if boil.HooksAreSkipped(ctx) {
		return nil
	}

	for _, hook := range tempBackfillAppetiteGuidelineBeforeUpdateHooks {
		if err := hook(ctx, exec, o); err != nil {
			return err
		}
	}

	return nil
}

// doAfterUpdateHooks executes all "after Update" hooks.
func (o *TempBackfillAppetiteGuideline) doAfterUpdateHooks(ctx context.Context, exec boil.ContextExecutor) (err error) {
	if boil.HooksAreSkipped(ctx) {
		return nil
	}

	for _, hook := range tempBackfillAppetiteGuidelineAfterUpdateHooks {
		if err := hook(ctx, exec, o); err != nil {
			return err
		}
	}

	return nil
}

// doBeforeDeleteHooks executes all "before Delete" hooks.
func (o *TempBackfillAppetiteGuideline) doBeforeDeleteHooks(ctx context.Context, exec boil.ContextExecutor) (err error) {
	if boil.HooksAreSkipped(ctx) {
		return nil
	}

	for _, hook := range tempBackfillAppetiteGuidelineBeforeDeleteHooks {
		if err := hook(ctx, exec, o); err != nil {
			return err
		}
	}

	return nil
}

// doAfterDeleteHooks executes all "after Delete" hooks.
func (o *TempBackfillAppetiteGuideline) doAfterDeleteHooks(ctx context.Context, exec boil.ContextExecutor) (err error) {
	if boil.HooksAreSkipped(ctx) {
		return nil
	}

	for _, hook := range tempBackfillAppetiteGuidelineAfterDeleteHooks {
		if err := hook(ctx, exec, o); err != nil {
			return err
		}
	}

	return nil
}

// doBeforeUpsertHooks executes all "before Upsert" hooks.
func (o *TempBackfillAppetiteGuideline) doBeforeUpsertHooks(ctx context.Context, exec boil.ContextExecutor) (err error) {
	if boil.HooksAreSkipped(ctx) {
		return nil
	}

	for _, hook := range tempBackfillAppetiteGuidelineBeforeUpsertHooks {
		if err := hook(ctx, exec, o); err != nil {
			return err
		}
	}

	return nil
}

// doAfterUpsertHooks executes all "after Upsert" hooks.
func (o *TempBackfillAppetiteGuideline) doAfterUpsertHooks(ctx context.Context, exec boil.ContextExecutor) (err error) {
	if boil.HooksAreSkipped(ctx) {
		return nil
	}

	for _, hook := range tempBackfillAppetiteGuidelineAfterUpsertHooks {
		if err := hook(ctx, exec, o); err != nil {
			return err
		}
	}

	return nil
}

// AddTempBackfillAppetiteGuidelineHook registers your hook function for all future operations.
func AddTempBackfillAppetiteGuidelineHook(hookPoint boil.HookPoint, tempBackfillAppetiteGuidelineHook TempBackfillAppetiteGuidelineHook) {
	switch hookPoint {
	case boil.AfterSelectHook:
		tempBackfillAppetiteGuidelineAfterSelectHooks = append(tempBackfillAppetiteGuidelineAfterSelectHooks, tempBackfillAppetiteGuidelineHook)
	case boil.BeforeInsertHook:
		tempBackfillAppetiteGuidelineBeforeInsertHooks = append(tempBackfillAppetiteGuidelineBeforeInsertHooks, tempBackfillAppetiteGuidelineHook)
	case boil.AfterInsertHook:
		tempBackfillAppetiteGuidelineAfterInsertHooks = append(tempBackfillAppetiteGuidelineAfterInsertHooks, tempBackfillAppetiteGuidelineHook)
	case boil.BeforeUpdateHook:
		tempBackfillAppetiteGuidelineBeforeUpdateHooks = append(tempBackfillAppetiteGuidelineBeforeUpdateHooks, tempBackfillAppetiteGuidelineHook)
	case boil.AfterUpdateHook:
		tempBackfillAppetiteGuidelineAfterUpdateHooks = append(tempBackfillAppetiteGuidelineAfterUpdateHooks, tempBackfillAppetiteGuidelineHook)
	case boil.BeforeDeleteHook:
		tempBackfillAppetiteGuidelineBeforeDeleteHooks = append(tempBackfillAppetiteGuidelineBeforeDeleteHooks, tempBackfillAppetiteGuidelineHook)
	case boil.AfterDeleteHook:
		tempBackfillAppetiteGuidelineAfterDeleteHooks = append(tempBackfillAppetiteGuidelineAfterDeleteHooks, tempBackfillAppetiteGuidelineHook)
	case boil.BeforeUpsertHook:
		tempBackfillAppetiteGuidelineBeforeUpsertHooks = append(tempBackfillAppetiteGuidelineBeforeUpsertHooks, tempBackfillAppetiteGuidelineHook)
	case boil.AfterUpsertHook:
		tempBackfillAppetiteGuidelineAfterUpsertHooks = append(tempBackfillAppetiteGuidelineAfterUpsertHooks, tempBackfillAppetiteGuidelineHook)
	}
}

// One returns a single tempBackfillAppetiteGuideline record from the query.
func (q tempBackfillAppetiteGuidelineQuery) One(ctx context.Context, exec boil.ContextExecutor) (*TempBackfillAppetiteGuideline, error) {
	o := &TempBackfillAppetiteGuideline{}

	queries.SetLimit(q.Query, 1)

	err := q.Bind(ctx, exec, o)
	if err != nil {
		if errors.Is(err, sql.ErrNoRows) {
			return nil, sql.ErrNoRows
		}
		return nil, errors.Wrap(err, "db_models: failed to execute a one query for temp_backfill_appetite_guidelines")
	}

	if err := o.doAfterSelectHooks(ctx, exec); err != nil {
		return o, err
	}

	return o, nil
}

// All returns all TempBackfillAppetiteGuideline records from the query.
func (q tempBackfillAppetiteGuidelineQuery) All(ctx context.Context, exec boil.ContextExecutor) (TempBackfillAppetiteGuidelineSlice, error) {
	var o []*TempBackfillAppetiteGuideline

	err := q.Bind(ctx, exec, &o)
	if err != nil {
		return nil, errors.Wrap(err, "db_models: failed to assign all query results to TempBackfillAppetiteGuideline slice")
	}

	if len(tempBackfillAppetiteGuidelineAfterSelectHooks) != 0 {
		for _, obj := range o {
			if err := obj.doAfterSelectHooks(ctx, exec); err != nil {
				return o, err
			}
		}
	}

	return o, nil
}

// Count returns the count of all TempBackfillAppetiteGuideline records in the query.
func (q tempBackfillAppetiteGuidelineQuery) Count(ctx context.Context, exec boil.ContextExecutor) (int64, error) {
	var count int64

	queries.SetSelect(q.Query, nil)
	queries.SetCount(q.Query)

	err := q.Query.QueryRowContext(ctx, exec).Scan(&count)
	if err != nil {
		return 0, errors.Wrap(err, "db_models: failed to count temp_backfill_appetite_guidelines rows")
	}

	return count, nil
}

// Exists checks if the row exists in the table.
func (q tempBackfillAppetiteGuidelineQuery) Exists(ctx context.Context, exec boil.ContextExecutor) (bool, error) {
	var count int64

	queries.SetSelect(q.Query, nil)
	queries.SetCount(q.Query)
	queries.SetLimit(q.Query, 1)

	err := q.Query.QueryRowContext(ctx, exec).Scan(&count)
	if err != nil {
		return false, errors.Wrap(err, "db_models: failed to check if temp_backfill_appetite_guidelines exists")
	}

	return count > 0, nil
}

// TempBackfillAppetiteGuidelines retrieves all the records using an executor.
func TempBackfillAppetiteGuidelines(mods ...qm.QueryMod) tempBackfillAppetiteGuidelineQuery {
	mods = append(mods, qm.From("\"temp_backfill_appetite_guidelines\""))
	q := NewQuery(mods...)
	if len(queries.GetSelect(q)) == 0 {
		queries.SetSelect(q, []string{"\"temp_backfill_appetite_guidelines\".*"})
	}

	return tempBackfillAppetiteGuidelineQuery{q}
}

// FindTempBackfillAppetiteGuideline retrieves a single record by ID with an executor.
// If selectCols is empty Find will return all columns.
func FindTempBackfillAppetiteGuideline(ctx context.Context, exec boil.ContextExecutor, iD string, selectCols ...string) (*TempBackfillAppetiteGuideline, error) {
	tempBackfillAppetiteGuidelineObj := &TempBackfillAppetiteGuideline{}

	sel := "*"
	if len(selectCols) > 0 {
		sel = strings.Join(strmangle.IdentQuoteSlice(dialect.LQ, dialect.RQ, selectCols), ",")
	}
	query := fmt.Sprintf(
		"select %s from \"temp_backfill_appetite_guidelines\" where \"id\"=$1", sel,
	)

	q := queries.Raw(query, iD)

	err := q.Bind(ctx, exec, tempBackfillAppetiteGuidelineObj)
	if err != nil {
		if errors.Is(err, sql.ErrNoRows) {
			return nil, sql.ErrNoRows
		}
		return nil, errors.Wrap(err, "db_models: unable to select from temp_backfill_appetite_guidelines")
	}

	if err = tempBackfillAppetiteGuidelineObj.doAfterSelectHooks(ctx, exec); err != nil {
		return tempBackfillAppetiteGuidelineObj, err
	}

	return tempBackfillAppetiteGuidelineObj, nil
}

// Insert a single record using an executor.
// See boil.Columns.InsertColumnSet documentation to understand column list inference for inserts.
func (o *TempBackfillAppetiteGuideline) Insert(ctx context.Context, exec boil.ContextExecutor, columns boil.Columns) error {
	if o == nil {
		return errors.New("db_models: no temp_backfill_appetite_guidelines provided for insertion")
	}

	var err error
	if !boil.TimestampsAreSkipped(ctx) {
		currTime := time.Now().In(boil.GetLocation())

		if o.CreatedAt.IsZero() {
			o.CreatedAt = currTime
		}
	}

	if err := o.doBeforeInsertHooks(ctx, exec); err != nil {
		return err
	}

	nzDefaults := queries.NonZeroDefaultSet(tempBackfillAppetiteGuidelineColumnsWithDefault, o)

	key := makeCacheKey(columns, nzDefaults)
	tempBackfillAppetiteGuidelineInsertCacheMut.RLock()
	cache, cached := tempBackfillAppetiteGuidelineInsertCache[key]
	tempBackfillAppetiteGuidelineInsertCacheMut.RUnlock()

	if !cached {
		wl, returnColumns := columns.InsertColumnSet(
			tempBackfillAppetiteGuidelineAllColumns,
			tempBackfillAppetiteGuidelineColumnsWithDefault,
			tempBackfillAppetiteGuidelineColumnsWithoutDefault,
			nzDefaults,
		)

		cache.valueMapping, err = queries.BindMapping(tempBackfillAppetiteGuidelineType, tempBackfillAppetiteGuidelineMapping, wl)
		if err != nil {
			return err
		}
		cache.retMapping, err = queries.BindMapping(tempBackfillAppetiteGuidelineType, tempBackfillAppetiteGuidelineMapping, returnColumns)
		if err != nil {
			return err
		}
		if len(wl) != 0 {
			cache.query = fmt.Sprintf("INSERT INTO \"temp_backfill_appetite_guidelines\" (\"%s\") %%sVALUES (%s)%%s", strings.Join(wl, "\",\""), strmangle.Placeholders(dialect.UseIndexPlaceholders, len(wl), 1, 1))
		} else {
			cache.query = "INSERT INTO \"temp_backfill_appetite_guidelines\" %sDEFAULT VALUES%s"
		}

		var queryOutput, queryReturning string

		if len(cache.retMapping) != 0 {
			queryReturning = fmt.Sprintf(" RETURNING \"%s\"", strings.Join(returnColumns, "\",\""))
		}

		cache.query = fmt.Sprintf(cache.query, queryOutput, queryReturning)
	}

	value := reflect.Indirect(reflect.ValueOf(o))
	vals := queries.ValuesFromMapping(value, cache.valueMapping)

	if boil.IsDebug(ctx) {
		writer := boil.DebugWriterFrom(ctx)
		fmt.Fprintln(writer, cache.query)
		fmt.Fprintln(writer, vals)
	}

	if len(cache.retMapping) != 0 {
		err = exec.QueryRowContext(ctx, cache.query, vals...).Scan(queries.PtrsFromMapping(value, cache.retMapping)...)
	} else {
		_, err = exec.ExecContext(ctx, cache.query, vals...)
	}

	if err != nil {
		return errors.Wrap(err, "db_models: unable to insert into temp_backfill_appetite_guidelines")
	}

	if !cached {
		tempBackfillAppetiteGuidelineInsertCacheMut.Lock()
		tempBackfillAppetiteGuidelineInsertCache[key] = cache
		tempBackfillAppetiteGuidelineInsertCacheMut.Unlock()
	}

	return o.doAfterInsertHooks(ctx, exec)
}

// Update uses an executor to update the TempBackfillAppetiteGuideline.
// See boil.Columns.UpdateColumnSet documentation to understand column list inference for updates.
// Update does not automatically update the record in case of default values. Use .Reload() to refresh the records.
func (o *TempBackfillAppetiteGuideline) Update(ctx context.Context, exec boil.ContextExecutor, columns boil.Columns) (int64, error) {
	var err error
	if err = o.doBeforeUpdateHooks(ctx, exec); err != nil {
		return 0, err
	}
	key := makeCacheKey(columns, nil)
	tempBackfillAppetiteGuidelineUpdateCacheMut.RLock()
	cache, cached := tempBackfillAppetiteGuidelineUpdateCache[key]
	tempBackfillAppetiteGuidelineUpdateCacheMut.RUnlock()

	if !cached {
		wl := columns.UpdateColumnSet(
			tempBackfillAppetiteGuidelineAllColumns,
			tempBackfillAppetiteGuidelinePrimaryKeyColumns,
		)

		if !columns.IsWhitelist() {
			wl = strmangle.SetComplement(wl, []string{"created_at"})
		}
		if len(wl) == 0 {
			return 0, errors.New("db_models: unable to update temp_backfill_appetite_guidelines, could not build whitelist")
		}

		cache.query = fmt.Sprintf("UPDATE \"temp_backfill_appetite_guidelines\" SET %s WHERE %s",
			strmangle.SetParamNames("\"", "\"", 1, wl),
			strmangle.WhereClause("\"", "\"", len(wl)+1, tempBackfillAppetiteGuidelinePrimaryKeyColumns),
		)
		cache.valueMapping, err = queries.BindMapping(tempBackfillAppetiteGuidelineType, tempBackfillAppetiteGuidelineMapping, append(wl, tempBackfillAppetiteGuidelinePrimaryKeyColumns...))
		if err != nil {
			return 0, err
		}
	}

	values := queries.ValuesFromMapping(reflect.Indirect(reflect.ValueOf(o)), cache.valueMapping)

	if boil.IsDebug(ctx) {
		writer := boil.DebugWriterFrom(ctx)
		fmt.Fprintln(writer, cache.query)
		fmt.Fprintln(writer, values)
	}
	var result sql.Result
	result, err = exec.ExecContext(ctx, cache.query, values...)
	if err != nil {
		return 0, errors.Wrap(err, "db_models: unable to update temp_backfill_appetite_guidelines row")
	}

	rowsAff, err := result.RowsAffected()
	if err != nil {
		return 0, errors.Wrap(err, "db_models: failed to get rows affected by update for temp_backfill_appetite_guidelines")
	}

	if !cached {
		tempBackfillAppetiteGuidelineUpdateCacheMut.Lock()
		tempBackfillAppetiteGuidelineUpdateCache[key] = cache
		tempBackfillAppetiteGuidelineUpdateCacheMut.Unlock()
	}

	return rowsAff, o.doAfterUpdateHooks(ctx, exec)
}

// UpdateAll updates all rows with the specified column values.
func (q tempBackfillAppetiteGuidelineQuery) UpdateAll(ctx context.Context, exec boil.ContextExecutor, cols M) (int64, error) {
	queries.SetUpdate(q.Query, cols)

	result, err := q.Query.ExecContext(ctx, exec)
	if err != nil {
		return 0, errors.Wrap(err, "db_models: unable to update all for temp_backfill_appetite_guidelines")
	}

	rowsAff, err := result.RowsAffected()
	if err != nil {
		return 0, errors.Wrap(err, "db_models: unable to retrieve rows affected for temp_backfill_appetite_guidelines")
	}

	return rowsAff, nil
}

// UpdateAll updates all rows with the specified column values, using an executor.
func (o TempBackfillAppetiteGuidelineSlice) UpdateAll(ctx context.Context, exec boil.ContextExecutor, cols M) (int64, error) {
	ln := int64(len(o))
	if ln == 0 {
		return 0, nil
	}

	if len(cols) == 0 {
		return 0, errors.New("db_models: update all requires at least one column argument")
	}

	colNames := make([]string, len(cols))
	args := make([]interface{}, len(cols))

	i := 0
	for name, value := range cols {
		colNames[i] = name
		args[i] = value
		i++
	}

	// Append all of the primary key values for each column
	for _, obj := range o {
		pkeyArgs := queries.ValuesFromMapping(reflect.Indirect(reflect.ValueOf(obj)), tempBackfillAppetiteGuidelinePrimaryKeyMapping)
		args = append(args, pkeyArgs...)
	}

	sql := fmt.Sprintf("UPDATE \"temp_backfill_appetite_guidelines\" SET %s WHERE %s",
		strmangle.SetParamNames("\"", "\"", 1, colNames),
		strmangle.WhereClauseRepeated(string(dialect.LQ), string(dialect.RQ), len(colNames)+1, tempBackfillAppetiteGuidelinePrimaryKeyColumns, len(o)))

	if boil.IsDebug(ctx) {
		writer := boil.DebugWriterFrom(ctx)
		fmt.Fprintln(writer, sql)
		fmt.Fprintln(writer, args...)
	}
	result, err := exec.ExecContext(ctx, sql, args...)
	if err != nil {
		return 0, errors.Wrap(err, "db_models: unable to update all in tempBackfillAppetiteGuideline slice")
	}

	rowsAff, err := result.RowsAffected()
	if err != nil {
		return 0, errors.Wrap(err, "db_models: unable to retrieve rows affected all in update all tempBackfillAppetiteGuideline")
	}
	return rowsAff, nil
}

// Upsert attempts an insert using an executor, and does an update or ignore on conflict.
// See boil.Columns documentation for how to properly use updateColumns and insertColumns.
func (o *TempBackfillAppetiteGuideline) Upsert(ctx context.Context, exec boil.ContextExecutor, updateOnConflict bool, conflictColumns []string, updateColumns, insertColumns boil.Columns) error {
	if o == nil {
		return errors.New("db_models: no temp_backfill_appetite_guidelines provided for upsert")
	}
	if !boil.TimestampsAreSkipped(ctx) {
		currTime := time.Now().In(boil.GetLocation())

		if o.CreatedAt.IsZero() {
			o.CreatedAt = currTime
		}
	}

	if err := o.doBeforeUpsertHooks(ctx, exec); err != nil {
		return err
	}

	nzDefaults := queries.NonZeroDefaultSet(tempBackfillAppetiteGuidelineColumnsWithDefault, o)

	// Build cache key in-line uglily - mysql vs psql problems
	buf := strmangle.GetBuffer()
	if updateOnConflict {
		buf.WriteByte('t')
	} else {
		buf.WriteByte('f')
	}
	buf.WriteByte('.')
	for _, c := range conflictColumns {
		buf.WriteString(c)
	}
	buf.WriteByte('.')
	buf.WriteString(strconv.Itoa(updateColumns.Kind))
	for _, c := range updateColumns.Cols {
		buf.WriteString(c)
	}
	buf.WriteByte('.')
	buf.WriteString(strconv.Itoa(insertColumns.Kind))
	for _, c := range insertColumns.Cols {
		buf.WriteString(c)
	}
	buf.WriteByte('.')
	for _, c := range nzDefaults {
		buf.WriteString(c)
	}
	key := buf.String()
	strmangle.PutBuffer(buf)

	tempBackfillAppetiteGuidelineUpsertCacheMut.RLock()
	cache, cached := tempBackfillAppetiteGuidelineUpsertCache[key]
	tempBackfillAppetiteGuidelineUpsertCacheMut.RUnlock()

	var err error

	if !cached {
		insert, ret := insertColumns.InsertColumnSet(
			tempBackfillAppetiteGuidelineAllColumns,
			tempBackfillAppetiteGuidelineColumnsWithDefault,
			tempBackfillAppetiteGuidelineColumnsWithoutDefault,
			nzDefaults,
		)

		update := updateColumns.UpdateColumnSet(
			tempBackfillAppetiteGuidelineAllColumns,
			tempBackfillAppetiteGuidelinePrimaryKeyColumns,
		)

		if updateOnConflict && len(update) == 0 {
			return errors.New("db_models: unable to upsert temp_backfill_appetite_guidelines, could not build update column list")
		}

		conflict := conflictColumns
		if len(conflict) == 0 {
			conflict = make([]string, len(tempBackfillAppetiteGuidelinePrimaryKeyColumns))
			copy(conflict, tempBackfillAppetiteGuidelinePrimaryKeyColumns)
		}
		cache.query = buildUpsertQueryPostgres(dialect, "\"temp_backfill_appetite_guidelines\"", updateOnConflict, ret, update, conflict, insert)

		cache.valueMapping, err = queries.BindMapping(tempBackfillAppetiteGuidelineType, tempBackfillAppetiteGuidelineMapping, insert)
		if err != nil {
			return err
		}
		if len(ret) != 0 {
			cache.retMapping, err = queries.BindMapping(tempBackfillAppetiteGuidelineType, tempBackfillAppetiteGuidelineMapping, ret)
			if err != nil {
				return err
			}
		}
	}

	value := reflect.Indirect(reflect.ValueOf(o))
	vals := queries.ValuesFromMapping(value, cache.valueMapping)
	var returns []interface{}
	if len(cache.retMapping) != 0 {
		returns = queries.PtrsFromMapping(value, cache.retMapping)
	}

	if boil.IsDebug(ctx) {
		writer := boil.DebugWriterFrom(ctx)
		fmt.Fprintln(writer, cache.query)
		fmt.Fprintln(writer, vals)
	}
	if len(cache.retMapping) != 0 {
		err = exec.QueryRowContext(ctx, cache.query, vals...).Scan(returns...)
		if errors.Is(err, sql.ErrNoRows) {
			err = nil // Postgres doesn't return anything when there's no update
		}
	} else {
		_, err = exec.ExecContext(ctx, cache.query, vals...)
	}
	if err != nil {
		return errors.Wrap(err, "db_models: unable to upsert temp_backfill_appetite_guidelines")
	}

	if !cached {
		tempBackfillAppetiteGuidelineUpsertCacheMut.Lock()
		tempBackfillAppetiteGuidelineUpsertCache[key] = cache
		tempBackfillAppetiteGuidelineUpsertCacheMut.Unlock()
	}

	return o.doAfterUpsertHooks(ctx, exec)
}

// Delete deletes a single TempBackfillAppetiteGuideline record with an executor.
// Delete will match against the primary key column to find the record to delete.
func (o *TempBackfillAppetiteGuideline) Delete(ctx context.Context, exec boil.ContextExecutor) (int64, error) {
	if o == nil {
		return 0, errors.New("db_models: no TempBackfillAppetiteGuideline provided for delete")
	}

	if err := o.doBeforeDeleteHooks(ctx, exec); err != nil {
		return 0, err
	}

	args := queries.ValuesFromMapping(reflect.Indirect(reflect.ValueOf(o)), tempBackfillAppetiteGuidelinePrimaryKeyMapping)
	sql := "DELETE FROM \"temp_backfill_appetite_guidelines\" WHERE \"id\"=$1"

	if boil.IsDebug(ctx) {
		writer := boil.DebugWriterFrom(ctx)
		fmt.Fprintln(writer, sql)
		fmt.Fprintln(writer, args...)
	}
	result, err := exec.ExecContext(ctx, sql, args...)
	if err != nil {
		return 0, errors.Wrap(err, "db_models: unable to delete from temp_backfill_appetite_guidelines")
	}

	rowsAff, err := result.RowsAffected()
	if err != nil {
		return 0, errors.Wrap(err, "db_models: failed to get rows affected by delete for temp_backfill_appetite_guidelines")
	}

	if err := o.doAfterDeleteHooks(ctx, exec); err != nil {
		return 0, err
	}

	return rowsAff, nil
}

// DeleteAll deletes all matching rows.
func (q tempBackfillAppetiteGuidelineQuery) DeleteAll(ctx context.Context, exec boil.ContextExecutor) (int64, error) {
	if q.Query == nil {
		return 0, errors.New("db_models: no tempBackfillAppetiteGuidelineQuery provided for delete all")
	}

	queries.SetDelete(q.Query)

	result, err := q.Query.ExecContext(ctx, exec)
	if err != nil {
		return 0, errors.Wrap(err, "db_models: unable to delete all from temp_backfill_appetite_guidelines")
	}

	rowsAff, err := result.RowsAffected()
	if err != nil {
		return 0, errors.Wrap(err, "db_models: failed to get rows affected by deleteall for temp_backfill_appetite_guidelines")
	}

	return rowsAff, nil
}

// DeleteAll deletes all rows in the slice, using an executor.
func (o TempBackfillAppetiteGuidelineSlice) DeleteAll(ctx context.Context, exec boil.ContextExecutor) (int64, error) {
	if len(o) == 0 {
		return 0, nil
	}

	if len(tempBackfillAppetiteGuidelineBeforeDeleteHooks) != 0 {
		for _, obj := range o {
			if err := obj.doBeforeDeleteHooks(ctx, exec); err != nil {
				return 0, err
			}
		}
	}

	var args []interface{}
	for _, obj := range o {
		pkeyArgs := queries.ValuesFromMapping(reflect.Indirect(reflect.ValueOf(obj)), tempBackfillAppetiteGuidelinePrimaryKeyMapping)
		args = append(args, pkeyArgs...)
	}

	sql := "DELETE FROM \"temp_backfill_appetite_guidelines\" WHERE " +
		strmangle.WhereClauseRepeated(string(dialect.LQ), string(dialect.RQ), 1, tempBackfillAppetiteGuidelinePrimaryKeyColumns, len(o))

	if boil.IsDebug(ctx) {
		writer := boil.DebugWriterFrom(ctx)
		fmt.Fprintln(writer, sql)
		fmt.Fprintln(writer, args)
	}
	result, err := exec.ExecContext(ctx, sql, args...)
	if err != nil {
		return 0, errors.Wrap(err, "db_models: unable to delete all from tempBackfillAppetiteGuideline slice")
	}

	rowsAff, err := result.RowsAffected()
	if err != nil {
		return 0, errors.Wrap(err, "db_models: failed to get rows affected by deleteall for temp_backfill_appetite_guidelines")
	}

	if len(tempBackfillAppetiteGuidelineAfterDeleteHooks) != 0 {
		for _, obj := range o {
			if err := obj.doAfterDeleteHooks(ctx, exec); err != nil {
				return 0, err
			}
		}
	}

	return rowsAff, nil
}

// Reload refetches the object from the database
// using the primary keys with an executor.
func (o *TempBackfillAppetiteGuideline) Reload(ctx context.Context, exec boil.ContextExecutor) error {
	ret, err := FindTempBackfillAppetiteGuideline(ctx, exec, o.ID)
	if err != nil {
		return err
	}

	*o = *ret
	return nil
}

// ReloadAll refetches every row with matching primary key column values
// and overwrites the original object slice with the newly updated slice.
func (o *TempBackfillAppetiteGuidelineSlice) ReloadAll(ctx context.Context, exec boil.ContextExecutor) error {
	if o == nil || len(*o) == 0 {
		return nil
	}

	slice := TempBackfillAppetiteGuidelineSlice{}
	var args []interface{}
	for _, obj := range *o {
		pkeyArgs := queries.ValuesFromMapping(reflect.Indirect(reflect.ValueOf(obj)), tempBackfillAppetiteGuidelinePrimaryKeyMapping)
		args = append(args, pkeyArgs...)
	}

	sql := "SELECT \"temp_backfill_appetite_guidelines\".* FROM \"temp_backfill_appetite_guidelines\" WHERE " +
		strmangle.WhereClauseRepeated(string(dialect.LQ), string(dialect.RQ), 1, tempBackfillAppetiteGuidelinePrimaryKeyColumns, len(*o))

	q := queries.Raw(sql, args...)

	err := q.Bind(ctx, exec, &slice)
	if err != nil {
		return errors.Wrap(err, "db_models: unable to reload all in TempBackfillAppetiteGuidelineSlice")
	}

	*o = slice

	return nil
}

// TempBackfillAppetiteGuidelineExists checks if the TempBackfillAppetiteGuideline row exists.
func TempBackfillAppetiteGuidelineExists(ctx context.Context, exec boil.ContextExecutor, iD string) (bool, error) {
	var exists bool
	sql := "select exists(select 1 from \"temp_backfill_appetite_guidelines\" where \"id\"=$1 limit 1)"

	if boil.IsDebug(ctx) {
		writer := boil.DebugWriterFrom(ctx)
		fmt.Fprintln(writer, sql)
		fmt.Fprintln(writer, iD)
	}
	row := exec.QueryRowContext(ctx, sql, iD)

	err := row.Scan(&exists)
	if err != nil {
		return false, errors.Wrap(err, "db_models: unable to check if temp_backfill_appetite_guidelines exists")
	}

	return exists, nil
}
