// Code generated by SQLBoiler (https://github.com/volatiletech/sqlboiler). DO NOT EDIT.
// This file is meant to be re-generated in place and/or deleted at any time.

package claims

import (
	"context"
	"database/sql"
	"fmt"
	"reflect"
	"strconv"
	"strings"
	"sync"
	"time"

	"github.com/friendsofgo/errors"
	"github.com/volatiletech/sqlboiler/v4/boil"
	"github.com/volatiletech/sqlboiler/v4/queries"
	"github.com/volatiletech/sqlboiler/v4/queries/qm"
	"github.com/volatiletech/sqlboiler/v4/queries/qmhelper"
	"github.com/volatiletech/strmangle"
)

// FnolIntakeEmail is an object representing the database table.
type FnolIntakeEmail struct {
	ID        string    `boil:"id" json:"id" toml:"id" yaml:"id"`
	MessageID string    `boil:"message_id" json:"message_id" toml:"message_id" yaml:"message_id"`
	FnolID    string    `boil:"fnol_id" json:"fnol_id" toml:"fnol_id" yaml:"fnol_id"`
	SentAt    time.Time `boil:"sent_at" json:"sent_at" toml:"sent_at" yaml:"sent_at"`
	CreatedAt time.Time `boil:"created_at" json:"created_at" toml:"created_at" yaml:"created_at"`

	R *fnolIntakeEmailR `boil:"" json:"" toml:"" yaml:""`
	L fnolIntakeEmailL  `boil:"-" json:"-" toml:"-" yaml:"-"`
}

var FnolIntakeEmailColumns = struct {
	ID        string
	MessageID string
	FnolID    string
	SentAt    string
	CreatedAt string
}{
	ID:        "id",
	MessageID: "message_id",
	FnolID:    "fnol_id",
	SentAt:    "sent_at",
	CreatedAt: "created_at",
}

var FnolIntakeEmailTableColumns = struct {
	ID        string
	MessageID string
	FnolID    string
	SentAt    string
	CreatedAt string
}{
	ID:        "fnol_intake_emails.id",
	MessageID: "fnol_intake_emails.message_id",
	FnolID:    "fnol_intake_emails.fnol_id",
	SentAt:    "fnol_intake_emails.sent_at",
	CreatedAt: "fnol_intake_emails.created_at",
}

// Generated where

var FnolIntakeEmailWhere = struct {
	ID        whereHelperstring
	MessageID whereHelperstring
	FnolID    whereHelperstring
	SentAt    whereHelpertime_Time
	CreatedAt whereHelpertime_Time
}{
	ID:        whereHelperstring{field: "\"claims\".\"fnol_intake_emails\".\"id\""},
	MessageID: whereHelperstring{field: "\"claims\".\"fnol_intake_emails\".\"message_id\""},
	FnolID:    whereHelperstring{field: "\"claims\".\"fnol_intake_emails\".\"fnol_id\""},
	SentAt:    whereHelpertime_Time{field: "\"claims\".\"fnol_intake_emails\".\"sent_at\""},
	CreatedAt: whereHelpertime_Time{field: "\"claims\".\"fnol_intake_emails\".\"created_at\""},
}

// FnolIntakeEmailRels is where relationship names are stored.
var FnolIntakeEmailRels = struct {
	Fnol string
}{
	Fnol: "Fnol",
}

// fnolIntakeEmailR is where relationships are stored.
type fnolIntakeEmailR struct {
	Fnol *Fnol `boil:"Fnol" json:"Fnol" toml:"Fnol" yaml:"Fnol"`
}

// NewStruct creates a new relationship struct
func (*fnolIntakeEmailR) NewStruct() *fnolIntakeEmailR {
	return &fnolIntakeEmailR{}
}

// fnolIntakeEmailL is where Load methods for each relationship are stored.
type fnolIntakeEmailL struct{}

var (
	fnolIntakeEmailAllColumns            = []string{"id", "message_id", "fnol_id", "sent_at", "created_at"}
	fnolIntakeEmailColumnsWithoutDefault = []string{"id", "message_id", "fnol_id", "sent_at", "created_at"}
	fnolIntakeEmailColumnsWithDefault    = []string{}
	fnolIntakeEmailPrimaryKeyColumns     = []string{"id"}
	fnolIntakeEmailGeneratedColumns      = []string{}
)

type (
	// FnolIntakeEmailSlice is an alias for a slice of pointers to FnolIntakeEmail.
	// This should almost always be used instead of []FnolIntakeEmail.
	FnolIntakeEmailSlice []*FnolIntakeEmail
	// FnolIntakeEmailHook is the signature for custom FnolIntakeEmail hook methods
	FnolIntakeEmailHook func(context.Context, boil.ContextExecutor, *FnolIntakeEmail) error

	fnolIntakeEmailQuery struct {
		*queries.Query
	}
)

// Cache for insert, update and upsert
var (
	fnolIntakeEmailType                 = reflect.TypeOf(&FnolIntakeEmail{})
	fnolIntakeEmailMapping              = queries.MakeStructMapping(fnolIntakeEmailType)
	fnolIntakeEmailPrimaryKeyMapping, _ = queries.BindMapping(fnolIntakeEmailType, fnolIntakeEmailMapping, fnolIntakeEmailPrimaryKeyColumns)
	fnolIntakeEmailInsertCacheMut       sync.RWMutex
	fnolIntakeEmailInsertCache          = make(map[string]insertCache)
	fnolIntakeEmailUpdateCacheMut       sync.RWMutex
	fnolIntakeEmailUpdateCache          = make(map[string]updateCache)
	fnolIntakeEmailUpsertCacheMut       sync.RWMutex
	fnolIntakeEmailUpsertCache          = make(map[string]insertCache)
)

var (
	// Force time package dependency for automated UpdatedAt/CreatedAt.
	_ = time.Second
	// Force qmhelper dependency for where clause generation (which doesn't
	// always happen)
	_ = qmhelper.Where
)

var fnolIntakeEmailAfterSelectHooks []FnolIntakeEmailHook

var fnolIntakeEmailBeforeInsertHooks []FnolIntakeEmailHook
var fnolIntakeEmailAfterInsertHooks []FnolIntakeEmailHook

var fnolIntakeEmailBeforeUpdateHooks []FnolIntakeEmailHook
var fnolIntakeEmailAfterUpdateHooks []FnolIntakeEmailHook

var fnolIntakeEmailBeforeDeleteHooks []FnolIntakeEmailHook
var fnolIntakeEmailAfterDeleteHooks []FnolIntakeEmailHook

var fnolIntakeEmailBeforeUpsertHooks []FnolIntakeEmailHook
var fnolIntakeEmailAfterUpsertHooks []FnolIntakeEmailHook

// doAfterSelectHooks executes all "after Select" hooks.
func (o *FnolIntakeEmail) doAfterSelectHooks(ctx context.Context, exec boil.ContextExecutor) (err error) {
	if boil.HooksAreSkipped(ctx) {
		return nil
	}

	for _, hook := range fnolIntakeEmailAfterSelectHooks {
		if err := hook(ctx, exec, o); err != nil {
			return err
		}
	}

	return nil
}

// doBeforeInsertHooks executes all "before insert" hooks.
func (o *FnolIntakeEmail) doBeforeInsertHooks(ctx context.Context, exec boil.ContextExecutor) (err error) {
	if boil.HooksAreSkipped(ctx) {
		return nil
	}

	for _, hook := range fnolIntakeEmailBeforeInsertHooks {
		if err := hook(ctx, exec, o); err != nil {
			return err
		}
	}

	return nil
}

// doAfterInsertHooks executes all "after Insert" hooks.
func (o *FnolIntakeEmail) doAfterInsertHooks(ctx context.Context, exec boil.ContextExecutor) (err error) {
	if boil.HooksAreSkipped(ctx) {
		return nil
	}

	for _, hook := range fnolIntakeEmailAfterInsertHooks {
		if err := hook(ctx, exec, o); err != nil {
			return err
		}
	}

	return nil
}

// doBeforeUpdateHooks executes all "before Update" hooks.
func (o *FnolIntakeEmail) doBeforeUpdateHooks(ctx context.Context, exec boil.ContextExecutor) (err error) {
	if boil.HooksAreSkipped(ctx) {
		return nil
	}

	for _, hook := range fnolIntakeEmailBeforeUpdateHooks {
		if err := hook(ctx, exec, o); err != nil {
			return err
		}
	}

	return nil
}

// doAfterUpdateHooks executes all "after Update" hooks.
func (o *FnolIntakeEmail) doAfterUpdateHooks(ctx context.Context, exec boil.ContextExecutor) (err error) {
	if boil.HooksAreSkipped(ctx) {
		return nil
	}

	for _, hook := range fnolIntakeEmailAfterUpdateHooks {
		if err := hook(ctx, exec, o); err != nil {
			return err
		}
	}

	return nil
}

// doBeforeDeleteHooks executes all "before Delete" hooks.
func (o *FnolIntakeEmail) doBeforeDeleteHooks(ctx context.Context, exec boil.ContextExecutor) (err error) {
	if boil.HooksAreSkipped(ctx) {
		return nil
	}

	for _, hook := range fnolIntakeEmailBeforeDeleteHooks {
		if err := hook(ctx, exec, o); err != nil {
			return err
		}
	}

	return nil
}

// doAfterDeleteHooks executes all "after Delete" hooks.
func (o *FnolIntakeEmail) doAfterDeleteHooks(ctx context.Context, exec boil.ContextExecutor) (err error) {
	if boil.HooksAreSkipped(ctx) {
		return nil
	}

	for _, hook := range fnolIntakeEmailAfterDeleteHooks {
		if err := hook(ctx, exec, o); err != nil {
			return err
		}
	}

	return nil
}

// doBeforeUpsertHooks executes all "before Upsert" hooks.
func (o *FnolIntakeEmail) doBeforeUpsertHooks(ctx context.Context, exec boil.ContextExecutor) (err error) {
	if boil.HooksAreSkipped(ctx) {
		return nil
	}

	for _, hook := range fnolIntakeEmailBeforeUpsertHooks {
		if err := hook(ctx, exec, o); err != nil {
			return err
		}
	}

	return nil
}

// doAfterUpsertHooks executes all "after Upsert" hooks.
func (o *FnolIntakeEmail) doAfterUpsertHooks(ctx context.Context, exec boil.ContextExecutor) (err error) {
	if boil.HooksAreSkipped(ctx) {
		return nil
	}

	for _, hook := range fnolIntakeEmailAfterUpsertHooks {
		if err := hook(ctx, exec, o); err != nil {
			return err
		}
	}

	return nil
}

// AddFnolIntakeEmailHook registers your hook function for all future operations.
func AddFnolIntakeEmailHook(hookPoint boil.HookPoint, fnolIntakeEmailHook FnolIntakeEmailHook) {
	switch hookPoint {
	case boil.AfterSelectHook:
		fnolIntakeEmailAfterSelectHooks = append(fnolIntakeEmailAfterSelectHooks, fnolIntakeEmailHook)
	case boil.BeforeInsertHook:
		fnolIntakeEmailBeforeInsertHooks = append(fnolIntakeEmailBeforeInsertHooks, fnolIntakeEmailHook)
	case boil.AfterInsertHook:
		fnolIntakeEmailAfterInsertHooks = append(fnolIntakeEmailAfterInsertHooks, fnolIntakeEmailHook)
	case boil.BeforeUpdateHook:
		fnolIntakeEmailBeforeUpdateHooks = append(fnolIntakeEmailBeforeUpdateHooks, fnolIntakeEmailHook)
	case boil.AfterUpdateHook:
		fnolIntakeEmailAfterUpdateHooks = append(fnolIntakeEmailAfterUpdateHooks, fnolIntakeEmailHook)
	case boil.BeforeDeleteHook:
		fnolIntakeEmailBeforeDeleteHooks = append(fnolIntakeEmailBeforeDeleteHooks, fnolIntakeEmailHook)
	case boil.AfterDeleteHook:
		fnolIntakeEmailAfterDeleteHooks = append(fnolIntakeEmailAfterDeleteHooks, fnolIntakeEmailHook)
	case boil.BeforeUpsertHook:
		fnolIntakeEmailBeforeUpsertHooks = append(fnolIntakeEmailBeforeUpsertHooks, fnolIntakeEmailHook)
	case boil.AfterUpsertHook:
		fnolIntakeEmailAfterUpsertHooks = append(fnolIntakeEmailAfterUpsertHooks, fnolIntakeEmailHook)
	}
}

// One returns a single fnolIntakeEmail record from the query.
func (q fnolIntakeEmailQuery) One(ctx context.Context, exec boil.ContextExecutor) (*FnolIntakeEmail, error) {
	o := &FnolIntakeEmail{}

	queries.SetLimit(q.Query, 1)

	err := q.Bind(ctx, exec, o)
	if err != nil {
		if errors.Is(err, sql.ErrNoRows) {
			return nil, sql.ErrNoRows
		}
		return nil, errors.Wrap(err, "claims: failed to execute a one query for fnol_intake_emails")
	}

	if err := o.doAfterSelectHooks(ctx, exec); err != nil {
		return o, err
	}

	return o, nil
}

// All returns all FnolIntakeEmail records from the query.
func (q fnolIntakeEmailQuery) All(ctx context.Context, exec boil.ContextExecutor) (FnolIntakeEmailSlice, error) {
	var o []*FnolIntakeEmail

	err := q.Bind(ctx, exec, &o)
	if err != nil {
		return nil, errors.Wrap(err, "claims: failed to assign all query results to FnolIntakeEmail slice")
	}

	if len(fnolIntakeEmailAfterSelectHooks) != 0 {
		for _, obj := range o {
			if err := obj.doAfterSelectHooks(ctx, exec); err != nil {
				return o, err
			}
		}
	}

	return o, nil
}

// Count returns the count of all FnolIntakeEmail records in the query.
func (q fnolIntakeEmailQuery) Count(ctx context.Context, exec boil.ContextExecutor) (int64, error) {
	var count int64

	queries.SetSelect(q.Query, nil)
	queries.SetCount(q.Query)

	err := q.Query.QueryRowContext(ctx, exec).Scan(&count)
	if err != nil {
		return 0, errors.Wrap(err, "claims: failed to count fnol_intake_emails rows")
	}

	return count, nil
}

// Exists checks if the row exists in the table.
func (q fnolIntakeEmailQuery) Exists(ctx context.Context, exec boil.ContextExecutor) (bool, error) {
	var count int64

	queries.SetSelect(q.Query, nil)
	queries.SetCount(q.Query)
	queries.SetLimit(q.Query, 1)

	err := q.Query.QueryRowContext(ctx, exec).Scan(&count)
	if err != nil {
		return false, errors.Wrap(err, "claims: failed to check if fnol_intake_emails exists")
	}

	return count > 0, nil
}

// Fnol pointed to by the foreign key.
func (o *FnolIntakeEmail) Fnol(mods ...qm.QueryMod) fnolQuery {
	queryMods := []qm.QueryMod{
		qm.Where("\"id\" = ?", o.FnolID),
	}

	queryMods = append(queryMods, mods...)

	return Fnols(queryMods...)
}

// LoadFnol allows an eager lookup of values, cached into the
// loaded structs of the objects. This is for an N-1 relationship.
func (fnolIntakeEmailL) LoadFnol(ctx context.Context, e boil.ContextExecutor, singular bool, maybeFnolIntakeEmail interface{}, mods queries.Applicator) error {
	var slice []*FnolIntakeEmail
	var object *FnolIntakeEmail

	if singular {
		object = maybeFnolIntakeEmail.(*FnolIntakeEmail)
	} else {
		slice = *maybeFnolIntakeEmail.(*[]*FnolIntakeEmail)
	}

	args := make([]interface{}, 0, 1)
	if singular {
		if object.R == nil {
			object.R = &fnolIntakeEmailR{}
		}
		args = append(args, object.FnolID)

	} else {
	Outer:
		for _, obj := range slice {
			if obj.R == nil {
				obj.R = &fnolIntakeEmailR{}
			}

			for _, a := range args {
				if a == obj.FnolID {
					continue Outer
				}
			}

			args = append(args, obj.FnolID)

		}
	}

	if len(args) == 0 {
		return nil
	}

	query := NewQuery(
		qm.From(`claims.fnols`),
		qm.WhereIn(`claims.fnols.id in ?`, args...),
	)
	if mods != nil {
		mods.Apply(query)
	}

	results, err := query.QueryContext(ctx, e)
	if err != nil {
		return errors.Wrap(err, "failed to eager load Fnol")
	}

	var resultSlice []*Fnol
	if err = queries.Bind(results, &resultSlice); err != nil {
		return errors.Wrap(err, "failed to bind eager loaded slice Fnol")
	}

	if err = results.Close(); err != nil {
		return errors.Wrap(err, "failed to close results of eager load for fnols")
	}
	if err = results.Err(); err != nil {
		return errors.Wrap(err, "error occurred during iteration of eager loaded relations for fnols")
	}

	if len(fnolIntakeEmailAfterSelectHooks) != 0 {
		for _, obj := range resultSlice {
			if err := obj.doAfterSelectHooks(ctx, e); err != nil {
				return err
			}
		}
	}

	if len(resultSlice) == 0 {
		return nil
	}

	if singular {
		foreign := resultSlice[0]
		object.R.Fnol = foreign
		if foreign.R == nil {
			foreign.R = &fnolR{}
		}
		foreign.R.FnolIntakeEmails = append(foreign.R.FnolIntakeEmails, object)
		return nil
	}

	for _, local := range slice {
		for _, foreign := range resultSlice {
			if local.FnolID == foreign.ID {
				local.R.Fnol = foreign
				if foreign.R == nil {
					foreign.R = &fnolR{}
				}
				foreign.R.FnolIntakeEmails = append(foreign.R.FnolIntakeEmails, local)
				break
			}
		}
	}

	return nil
}

// SetFnol of the fnolIntakeEmail to the related item.
// Sets o.R.Fnol to related.
// Adds o to related.R.FnolIntakeEmails.
func (o *FnolIntakeEmail) SetFnol(ctx context.Context, exec boil.ContextExecutor, insert bool, related *Fnol) error {
	var err error
	if insert {
		if err = related.Insert(ctx, exec, boil.Infer()); err != nil {
			return errors.Wrap(err, "failed to insert into foreign table")
		}
	}

	updateQuery := fmt.Sprintf(
		"UPDATE \"claims\".\"fnol_intake_emails\" SET %s WHERE %s",
		strmangle.SetParamNames("\"", "\"", 1, []string{"fnol_id"}),
		strmangle.WhereClause("\"", "\"", 2, fnolIntakeEmailPrimaryKeyColumns),
	)
	values := []interface{}{related.ID, o.ID}

	if boil.IsDebug(ctx) {
		writer := boil.DebugWriterFrom(ctx)
		fmt.Fprintln(writer, updateQuery)
		fmt.Fprintln(writer, values)
	}
	if _, err = exec.ExecContext(ctx, updateQuery, values...); err != nil {
		return errors.Wrap(err, "failed to update local table")
	}

	o.FnolID = related.ID
	if o.R == nil {
		o.R = &fnolIntakeEmailR{
			Fnol: related,
		}
	} else {
		o.R.Fnol = related
	}

	if related.R == nil {
		related.R = &fnolR{
			FnolIntakeEmails: FnolIntakeEmailSlice{o},
		}
	} else {
		related.R.FnolIntakeEmails = append(related.R.FnolIntakeEmails, o)
	}

	return nil
}

// FnolIntakeEmails retrieves all the records using an executor.
func FnolIntakeEmails(mods ...qm.QueryMod) fnolIntakeEmailQuery {
	mods = append(mods, qm.From("\"claims\".\"fnol_intake_emails\""))
	q := NewQuery(mods...)
	if len(queries.GetSelect(q)) == 0 {
		queries.SetSelect(q, []string{"\"claims\".\"fnol_intake_emails\".*"})
	}

	return fnolIntakeEmailQuery{q}
}

// FindFnolIntakeEmail retrieves a single record by ID with an executor.
// If selectCols is empty Find will return all columns.
func FindFnolIntakeEmail(ctx context.Context, exec boil.ContextExecutor, iD string, selectCols ...string) (*FnolIntakeEmail, error) {
	fnolIntakeEmailObj := &FnolIntakeEmail{}

	sel := "*"
	if len(selectCols) > 0 {
		sel = strings.Join(strmangle.IdentQuoteSlice(dialect.LQ, dialect.RQ, selectCols), ",")
	}
	query := fmt.Sprintf(
		"select %s from \"claims\".\"fnol_intake_emails\" where \"id\"=$1", sel,
	)

	q := queries.Raw(query, iD)

	err := q.Bind(ctx, exec, fnolIntakeEmailObj)
	if err != nil {
		if errors.Is(err, sql.ErrNoRows) {
			return nil, sql.ErrNoRows
		}
		return nil, errors.Wrap(err, "claims: unable to select from fnol_intake_emails")
	}

	if err = fnolIntakeEmailObj.doAfterSelectHooks(ctx, exec); err != nil {
		return fnolIntakeEmailObj, err
	}

	return fnolIntakeEmailObj, nil
}

// Insert a single record using an executor.
// See boil.Columns.InsertColumnSet documentation to understand column list inference for inserts.
func (o *FnolIntakeEmail) Insert(ctx context.Context, exec boil.ContextExecutor, columns boil.Columns) error {
	if o == nil {
		return errors.New("claims: no fnol_intake_emails provided for insertion")
	}

	var err error
	if !boil.TimestampsAreSkipped(ctx) {
		currTime := time.Now().In(boil.GetLocation())

		if o.CreatedAt.IsZero() {
			o.CreatedAt = currTime
		}
	}

	if err := o.doBeforeInsertHooks(ctx, exec); err != nil {
		return err
	}

	nzDefaults := queries.NonZeroDefaultSet(fnolIntakeEmailColumnsWithDefault, o)

	key := makeCacheKey(columns, nzDefaults)
	fnolIntakeEmailInsertCacheMut.RLock()
	cache, cached := fnolIntakeEmailInsertCache[key]
	fnolIntakeEmailInsertCacheMut.RUnlock()

	if !cached {
		wl, returnColumns := columns.InsertColumnSet(
			fnolIntakeEmailAllColumns,
			fnolIntakeEmailColumnsWithDefault,
			fnolIntakeEmailColumnsWithoutDefault,
			nzDefaults,
		)

		cache.valueMapping, err = queries.BindMapping(fnolIntakeEmailType, fnolIntakeEmailMapping, wl)
		if err != nil {
			return err
		}
		cache.retMapping, err = queries.BindMapping(fnolIntakeEmailType, fnolIntakeEmailMapping, returnColumns)
		if err != nil {
			return err
		}
		if len(wl) != 0 {
			cache.query = fmt.Sprintf("INSERT INTO \"claims\".\"fnol_intake_emails\" (\"%s\") %%sVALUES (%s)%%s", strings.Join(wl, "\",\""), strmangle.Placeholders(dialect.UseIndexPlaceholders, len(wl), 1, 1))
		} else {
			cache.query = "INSERT INTO \"claims\".\"fnol_intake_emails\" %sDEFAULT VALUES%s"
		}

		var queryOutput, queryReturning string

		if len(cache.retMapping) != 0 {
			queryReturning = fmt.Sprintf(" RETURNING \"%s\"", strings.Join(returnColumns, "\",\""))
		}

		cache.query = fmt.Sprintf(cache.query, queryOutput, queryReturning)
	}

	value := reflect.Indirect(reflect.ValueOf(o))
	vals := queries.ValuesFromMapping(value, cache.valueMapping)

	if boil.IsDebug(ctx) {
		writer := boil.DebugWriterFrom(ctx)
		fmt.Fprintln(writer, cache.query)
		fmt.Fprintln(writer, vals)
	}

	if len(cache.retMapping) != 0 {
		err = exec.QueryRowContext(ctx, cache.query, vals...).Scan(queries.PtrsFromMapping(value, cache.retMapping)...)
	} else {
		_, err = exec.ExecContext(ctx, cache.query, vals...)
	}

	if err != nil {
		return errors.Wrap(err, "claims: unable to insert into fnol_intake_emails")
	}

	if !cached {
		fnolIntakeEmailInsertCacheMut.Lock()
		fnolIntakeEmailInsertCache[key] = cache
		fnolIntakeEmailInsertCacheMut.Unlock()
	}

	return o.doAfterInsertHooks(ctx, exec)
}

// Update uses an executor to update the FnolIntakeEmail.
// See boil.Columns.UpdateColumnSet documentation to understand column list inference for updates.
// Update does not automatically update the record in case of default values. Use .Reload() to refresh the records.
func (o *FnolIntakeEmail) Update(ctx context.Context, exec boil.ContextExecutor, columns boil.Columns) (int64, error) {
	var err error
	if err = o.doBeforeUpdateHooks(ctx, exec); err != nil {
		return 0, err
	}
	key := makeCacheKey(columns, nil)
	fnolIntakeEmailUpdateCacheMut.RLock()
	cache, cached := fnolIntakeEmailUpdateCache[key]
	fnolIntakeEmailUpdateCacheMut.RUnlock()

	if !cached {
		wl := columns.UpdateColumnSet(
			fnolIntakeEmailAllColumns,
			fnolIntakeEmailPrimaryKeyColumns,
		)

		if !columns.IsWhitelist() {
			wl = strmangle.SetComplement(wl, []string{"created_at"})
		}
		if len(wl) == 0 {
			return 0, errors.New("claims: unable to update fnol_intake_emails, could not build whitelist")
		}

		cache.query = fmt.Sprintf("UPDATE \"claims\".\"fnol_intake_emails\" SET %s WHERE %s",
			strmangle.SetParamNames("\"", "\"", 1, wl),
			strmangle.WhereClause("\"", "\"", len(wl)+1, fnolIntakeEmailPrimaryKeyColumns),
		)
		cache.valueMapping, err = queries.BindMapping(fnolIntakeEmailType, fnolIntakeEmailMapping, append(wl, fnolIntakeEmailPrimaryKeyColumns...))
		if err != nil {
			return 0, err
		}
	}

	values := queries.ValuesFromMapping(reflect.Indirect(reflect.ValueOf(o)), cache.valueMapping)

	if boil.IsDebug(ctx) {
		writer := boil.DebugWriterFrom(ctx)
		fmt.Fprintln(writer, cache.query)
		fmt.Fprintln(writer, values)
	}
	var result sql.Result
	result, err = exec.ExecContext(ctx, cache.query, values...)
	if err != nil {
		return 0, errors.Wrap(err, "claims: unable to update fnol_intake_emails row")
	}

	rowsAff, err := result.RowsAffected()
	if err != nil {
		return 0, errors.Wrap(err, "claims: failed to get rows affected by update for fnol_intake_emails")
	}

	if !cached {
		fnolIntakeEmailUpdateCacheMut.Lock()
		fnolIntakeEmailUpdateCache[key] = cache
		fnolIntakeEmailUpdateCacheMut.Unlock()
	}

	return rowsAff, o.doAfterUpdateHooks(ctx, exec)
}

// UpdateAll updates all rows with the specified column values.
func (q fnolIntakeEmailQuery) UpdateAll(ctx context.Context, exec boil.ContextExecutor, cols M) (int64, error) {
	queries.SetUpdate(q.Query, cols)

	result, err := q.Query.ExecContext(ctx, exec)
	if err != nil {
		return 0, errors.Wrap(err, "claims: unable to update all for fnol_intake_emails")
	}

	rowsAff, err := result.RowsAffected()
	if err != nil {
		return 0, errors.Wrap(err, "claims: unable to retrieve rows affected for fnol_intake_emails")
	}

	return rowsAff, nil
}

// UpdateAll updates all rows with the specified column values, using an executor.
func (o FnolIntakeEmailSlice) UpdateAll(ctx context.Context, exec boil.ContextExecutor, cols M) (int64, error) {
	ln := int64(len(o))
	if ln == 0 {
		return 0, nil
	}

	if len(cols) == 0 {
		return 0, errors.New("claims: update all requires at least one column argument")
	}

	colNames := make([]string, len(cols))
	args := make([]interface{}, len(cols))

	i := 0
	for name, value := range cols {
		colNames[i] = name
		args[i] = value
		i++
	}

	// Append all of the primary key values for each column
	for _, obj := range o {
		pkeyArgs := queries.ValuesFromMapping(reflect.Indirect(reflect.ValueOf(obj)), fnolIntakeEmailPrimaryKeyMapping)
		args = append(args, pkeyArgs...)
	}

	sql := fmt.Sprintf("UPDATE \"claims\".\"fnol_intake_emails\" SET %s WHERE %s",
		strmangle.SetParamNames("\"", "\"", 1, colNames),
		strmangle.WhereClauseRepeated(string(dialect.LQ), string(dialect.RQ), len(colNames)+1, fnolIntakeEmailPrimaryKeyColumns, len(o)))

	if boil.IsDebug(ctx) {
		writer := boil.DebugWriterFrom(ctx)
		fmt.Fprintln(writer, sql)
		fmt.Fprintln(writer, args...)
	}
	result, err := exec.ExecContext(ctx, sql, args...)
	if err != nil {
		return 0, errors.Wrap(err, "claims: unable to update all in fnolIntakeEmail slice")
	}

	rowsAff, err := result.RowsAffected()
	if err != nil {
		return 0, errors.Wrap(err, "claims: unable to retrieve rows affected all in update all fnolIntakeEmail")
	}
	return rowsAff, nil
}

// Upsert attempts an insert using an executor, and does an update or ignore on conflict.
// See boil.Columns documentation for how to properly use updateColumns and insertColumns.
func (o *FnolIntakeEmail) Upsert(ctx context.Context, exec boil.ContextExecutor, updateOnConflict bool, conflictColumns []string, updateColumns, insertColumns boil.Columns) error {
	if o == nil {
		return errors.New("claims: no fnol_intake_emails provided for upsert")
	}
	if !boil.TimestampsAreSkipped(ctx) {
		currTime := time.Now().In(boil.GetLocation())

		if o.CreatedAt.IsZero() {
			o.CreatedAt = currTime
		}
	}

	if err := o.doBeforeUpsertHooks(ctx, exec); err != nil {
		return err
	}

	nzDefaults := queries.NonZeroDefaultSet(fnolIntakeEmailColumnsWithDefault, o)

	// Build cache key in-line uglily - mysql vs psql problems
	buf := strmangle.GetBuffer()
	if updateOnConflict {
		buf.WriteByte('t')
	} else {
		buf.WriteByte('f')
	}
	buf.WriteByte('.')
	for _, c := range conflictColumns {
		buf.WriteString(c)
	}
	buf.WriteByte('.')
	buf.WriteString(strconv.Itoa(updateColumns.Kind))
	for _, c := range updateColumns.Cols {
		buf.WriteString(c)
	}
	buf.WriteByte('.')
	buf.WriteString(strconv.Itoa(insertColumns.Kind))
	for _, c := range insertColumns.Cols {
		buf.WriteString(c)
	}
	buf.WriteByte('.')
	for _, c := range nzDefaults {
		buf.WriteString(c)
	}
	key := buf.String()
	strmangle.PutBuffer(buf)

	fnolIntakeEmailUpsertCacheMut.RLock()
	cache, cached := fnolIntakeEmailUpsertCache[key]
	fnolIntakeEmailUpsertCacheMut.RUnlock()

	var err error

	if !cached {
		insert, ret := insertColumns.InsertColumnSet(
			fnolIntakeEmailAllColumns,
			fnolIntakeEmailColumnsWithDefault,
			fnolIntakeEmailColumnsWithoutDefault,
			nzDefaults,
		)

		update := updateColumns.UpdateColumnSet(
			fnolIntakeEmailAllColumns,
			fnolIntakeEmailPrimaryKeyColumns,
		)

		if updateOnConflict && len(update) == 0 {
			return errors.New("claims: unable to upsert fnol_intake_emails, could not build update column list")
		}

		conflict := conflictColumns
		if len(conflict) == 0 {
			conflict = make([]string, len(fnolIntakeEmailPrimaryKeyColumns))
			copy(conflict, fnolIntakeEmailPrimaryKeyColumns)
		}
		cache.query = buildUpsertQueryPostgres(dialect, "\"claims\".\"fnol_intake_emails\"", updateOnConflict, ret, update, conflict, insert)

		cache.valueMapping, err = queries.BindMapping(fnolIntakeEmailType, fnolIntakeEmailMapping, insert)
		if err != nil {
			return err
		}
		if len(ret) != 0 {
			cache.retMapping, err = queries.BindMapping(fnolIntakeEmailType, fnolIntakeEmailMapping, ret)
			if err != nil {
				return err
			}
		}
	}

	value := reflect.Indirect(reflect.ValueOf(o))
	vals := queries.ValuesFromMapping(value, cache.valueMapping)
	var returns []interface{}
	if len(cache.retMapping) != 0 {
		returns = queries.PtrsFromMapping(value, cache.retMapping)
	}

	if boil.IsDebug(ctx) {
		writer := boil.DebugWriterFrom(ctx)
		fmt.Fprintln(writer, cache.query)
		fmt.Fprintln(writer, vals)
	}
	if len(cache.retMapping) != 0 {
		err = exec.QueryRowContext(ctx, cache.query, vals...).Scan(returns...)
		if errors.Is(err, sql.ErrNoRows) {
			err = nil // Postgres doesn't return anything when there's no update
		}
	} else {
		_, err = exec.ExecContext(ctx, cache.query, vals...)
	}
	if err != nil {
		return errors.Wrap(err, "claims: unable to upsert fnol_intake_emails")
	}

	if !cached {
		fnolIntakeEmailUpsertCacheMut.Lock()
		fnolIntakeEmailUpsertCache[key] = cache
		fnolIntakeEmailUpsertCacheMut.Unlock()
	}

	return o.doAfterUpsertHooks(ctx, exec)
}

// Delete deletes a single FnolIntakeEmail record with an executor.
// Delete will match against the primary key column to find the record to delete.
func (o *FnolIntakeEmail) Delete(ctx context.Context, exec boil.ContextExecutor) (int64, error) {
	if o == nil {
		return 0, errors.New("claims: no FnolIntakeEmail provided for delete")
	}

	if err := o.doBeforeDeleteHooks(ctx, exec); err != nil {
		return 0, err
	}

	args := queries.ValuesFromMapping(reflect.Indirect(reflect.ValueOf(o)), fnolIntakeEmailPrimaryKeyMapping)
	sql := "DELETE FROM \"claims\".\"fnol_intake_emails\" WHERE \"id\"=$1"

	if boil.IsDebug(ctx) {
		writer := boil.DebugWriterFrom(ctx)
		fmt.Fprintln(writer, sql)
		fmt.Fprintln(writer, args...)
	}
	result, err := exec.ExecContext(ctx, sql, args...)
	if err != nil {
		return 0, errors.Wrap(err, "claims: unable to delete from fnol_intake_emails")
	}

	rowsAff, err := result.RowsAffected()
	if err != nil {
		return 0, errors.Wrap(err, "claims: failed to get rows affected by delete for fnol_intake_emails")
	}

	if err := o.doAfterDeleteHooks(ctx, exec); err != nil {
		return 0, err
	}

	return rowsAff, nil
}

// DeleteAll deletes all matching rows.
func (q fnolIntakeEmailQuery) DeleteAll(ctx context.Context, exec boil.ContextExecutor) (int64, error) {
	if q.Query == nil {
		return 0, errors.New("claims: no fnolIntakeEmailQuery provided for delete all")
	}

	queries.SetDelete(q.Query)

	result, err := q.Query.ExecContext(ctx, exec)
	if err != nil {
		return 0, errors.Wrap(err, "claims: unable to delete all from fnol_intake_emails")
	}

	rowsAff, err := result.RowsAffected()
	if err != nil {
		return 0, errors.Wrap(err, "claims: failed to get rows affected by deleteall for fnol_intake_emails")
	}

	return rowsAff, nil
}

// DeleteAll deletes all rows in the slice, using an executor.
func (o FnolIntakeEmailSlice) DeleteAll(ctx context.Context, exec boil.ContextExecutor) (int64, error) {
	if len(o) == 0 {
		return 0, nil
	}

	if len(fnolIntakeEmailBeforeDeleteHooks) != 0 {
		for _, obj := range o {
			if err := obj.doBeforeDeleteHooks(ctx, exec); err != nil {
				return 0, err
			}
		}
	}

	var args []interface{}
	for _, obj := range o {
		pkeyArgs := queries.ValuesFromMapping(reflect.Indirect(reflect.ValueOf(obj)), fnolIntakeEmailPrimaryKeyMapping)
		args = append(args, pkeyArgs...)
	}

	sql := "DELETE FROM \"claims\".\"fnol_intake_emails\" WHERE " +
		strmangle.WhereClauseRepeated(string(dialect.LQ), string(dialect.RQ), 1, fnolIntakeEmailPrimaryKeyColumns, len(o))

	if boil.IsDebug(ctx) {
		writer := boil.DebugWriterFrom(ctx)
		fmt.Fprintln(writer, sql)
		fmt.Fprintln(writer, args)
	}
	result, err := exec.ExecContext(ctx, sql, args...)
	if err != nil {
		return 0, errors.Wrap(err, "claims: unable to delete all from fnolIntakeEmail slice")
	}

	rowsAff, err := result.RowsAffected()
	if err != nil {
		return 0, errors.Wrap(err, "claims: failed to get rows affected by deleteall for fnol_intake_emails")
	}

	if len(fnolIntakeEmailAfterDeleteHooks) != 0 {
		for _, obj := range o {
			if err := obj.doAfterDeleteHooks(ctx, exec); err != nil {
				return 0, err
			}
		}
	}

	return rowsAff, nil
}

// Reload refetches the object from the database
// using the primary keys with an executor.
func (o *FnolIntakeEmail) Reload(ctx context.Context, exec boil.ContextExecutor) error {
	ret, err := FindFnolIntakeEmail(ctx, exec, o.ID)
	if err != nil {
		return err
	}

	*o = *ret
	return nil
}

// ReloadAll refetches every row with matching primary key column values
// and overwrites the original object slice with the newly updated slice.
func (o *FnolIntakeEmailSlice) ReloadAll(ctx context.Context, exec boil.ContextExecutor) error {
	if o == nil || len(*o) == 0 {
		return nil
	}

	slice := FnolIntakeEmailSlice{}
	var args []interface{}
	for _, obj := range *o {
		pkeyArgs := queries.ValuesFromMapping(reflect.Indirect(reflect.ValueOf(obj)), fnolIntakeEmailPrimaryKeyMapping)
		args = append(args, pkeyArgs...)
	}

	sql := "SELECT \"claims\".\"fnol_intake_emails\".* FROM \"claims\".\"fnol_intake_emails\" WHERE " +
		strmangle.WhereClauseRepeated(string(dialect.LQ), string(dialect.RQ), 1, fnolIntakeEmailPrimaryKeyColumns, len(*o))

	q := queries.Raw(sql, args...)

	err := q.Bind(ctx, exec, &slice)
	if err != nil {
		return errors.Wrap(err, "claims: unable to reload all in FnolIntakeEmailSlice")
	}

	*o = slice

	return nil
}

// FnolIntakeEmailExists checks if the FnolIntakeEmail row exists.
func FnolIntakeEmailExists(ctx context.Context, exec boil.ContextExecutor, iD string) (bool, error) {
	var exists bool
	sql := "select exists(select 1 from \"claims\".\"fnol_intake_emails\" where \"id\"=$1 limit 1)"

	if boil.IsDebug(ctx) {
		writer := boil.DebugWriterFrom(ctx)
		fmt.Fprintln(writer, sql)
		fmt.Fprintln(writer, iD)
	}
	row := exec.QueryRowContext(ctx, sql, iD)

	err := row.Scan(&exists)
	if err != nil {
		return false, errors.Wrap(err, "claims: unable to check if fnol_intake_emails exists")
	}

	return exists, nil
}
