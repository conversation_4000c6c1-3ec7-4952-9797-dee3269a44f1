// Code generated by SQLBoiler (https://github.com/volatiletech/sqlboiler). DO NOT EDIT.
// This file is meant to be re-generated in place and/or deleted at any time.

package risk_metrics

import (
	"context"
	"database/sql"
	"fmt"
	"reflect"
	"strconv"
	"strings"
	"sync"
	"time"

	"github.com/friendsofgo/errors"
	"github.com/volatiletech/sqlboiler/v4/boil"
	"github.com/volatiletech/sqlboiler/v4/queries"
	"github.com/volatiletech/sqlboiler/v4/queries/qm"
	"github.com/volatiletech/sqlboiler/v4/queries/qmhelper"
	"github.com/volatiletech/strmangle"
)

// BilledMileage is an object representing the database table.
type BilledMileage struct {
	ID           string    `boil:"id" json:"id" toml:"id" yaml:"id"`
	PolicyNumber string    `boil:"policy_number" json:"policy_number" toml:"policy_number" yaml:"policy_number"`
	Mileage      float64   `boil:"mileage" json:"mileage" toml:"mileage" yaml:"mileage"`
	BilledMonth  time.Time `boil:"billed_month" json:"billed_month" toml:"billed_month" yaml:"billed_month"`
	ExtractedAt  time.Time `boil:"extracted_at" json:"extracted_at" toml:"extracted_at" yaml:"extracted_at"`
	IsAudit      bool      `boil:"is_audit" json:"is_audit" toml:"is_audit" yaml:"is_audit"`

	R *billedMileageR `boil:"" json:"" toml:"" yaml:""`
	L billedMileageL  `boil:"-" json:"-" toml:"-" yaml:"-"`
}

var BilledMileageColumns = struct {
	ID           string
	PolicyNumber string
	Mileage      string
	BilledMonth  string
	ExtractedAt  string
	IsAudit      string
}{
	ID:           "id",
	PolicyNumber: "policy_number",
	Mileage:      "mileage",
	BilledMonth:  "billed_month",
	ExtractedAt:  "extracted_at",
	IsAudit:      "is_audit",
}

var BilledMileageTableColumns = struct {
	ID           string
	PolicyNumber string
	Mileage      string
	BilledMonth  string
	ExtractedAt  string
	IsAudit      string
}{
	ID:           "billed_mileage.id",
	PolicyNumber: "billed_mileage.policy_number",
	Mileage:      "billed_mileage.mileage",
	BilledMonth:  "billed_mileage.billed_month",
	ExtractedAt:  "billed_mileage.extracted_at",
	IsAudit:      "billed_mileage.is_audit",
}

// Generated where

type whereHelperstring struct{ field string }

func (w whereHelperstring) EQ(x string) qm.QueryMod  { return qmhelper.Where(w.field, qmhelper.EQ, x) }
func (w whereHelperstring) NEQ(x string) qm.QueryMod { return qmhelper.Where(w.field, qmhelper.NEQ, x) }
func (w whereHelperstring) LT(x string) qm.QueryMod  { return qmhelper.Where(w.field, qmhelper.LT, x) }
func (w whereHelperstring) LTE(x string) qm.QueryMod { return qmhelper.Where(w.field, qmhelper.LTE, x) }
func (w whereHelperstring) GT(x string) qm.QueryMod  { return qmhelper.Where(w.field, qmhelper.GT, x) }
func (w whereHelperstring) GTE(x string) qm.QueryMod { return qmhelper.Where(w.field, qmhelper.GTE, x) }
func (w whereHelperstring) IN(slice []string) qm.QueryMod {
	values := make([]interface{}, 0, len(slice))
	for _, value := range slice {
		values = append(values, value)
	}
	return qm.WhereIn(fmt.Sprintf("%s IN ?", w.field), values...)
}
func (w whereHelperstring) NIN(slice []string) qm.QueryMod {
	values := make([]interface{}, 0, len(slice))
	for _, value := range slice {
		values = append(values, value)
	}
	return qm.WhereNotIn(fmt.Sprintf("%s NOT IN ?", w.field), values...)
}

type whereHelperfloat64 struct{ field string }

func (w whereHelperfloat64) EQ(x float64) qm.QueryMod { return qmhelper.Where(w.field, qmhelper.EQ, x) }
func (w whereHelperfloat64) NEQ(x float64) qm.QueryMod {
	return qmhelper.Where(w.field, qmhelper.NEQ, x)
}
func (w whereHelperfloat64) LT(x float64) qm.QueryMod { return qmhelper.Where(w.field, qmhelper.LT, x) }
func (w whereHelperfloat64) LTE(x float64) qm.QueryMod {
	return qmhelper.Where(w.field, qmhelper.LTE, x)
}
func (w whereHelperfloat64) GT(x float64) qm.QueryMod { return qmhelper.Where(w.field, qmhelper.GT, x) }
func (w whereHelperfloat64) GTE(x float64) qm.QueryMod {
	return qmhelper.Where(w.field, qmhelper.GTE, x)
}
func (w whereHelperfloat64) IN(slice []float64) qm.QueryMod {
	values := make([]interface{}, 0, len(slice))
	for _, value := range slice {
		values = append(values, value)
	}
	return qm.WhereIn(fmt.Sprintf("%s IN ?", w.field), values...)
}
func (w whereHelperfloat64) NIN(slice []float64) qm.QueryMod {
	values := make([]interface{}, 0, len(slice))
	for _, value := range slice {
		values = append(values, value)
	}
	return qm.WhereNotIn(fmt.Sprintf("%s NOT IN ?", w.field), values...)
}

type whereHelpertime_Time struct{ field string }

func (w whereHelpertime_Time) EQ(x time.Time) qm.QueryMod {
	return qmhelper.Where(w.field, qmhelper.EQ, x)
}
func (w whereHelpertime_Time) NEQ(x time.Time) qm.QueryMod {
	return qmhelper.Where(w.field, qmhelper.NEQ, x)
}
func (w whereHelpertime_Time) LT(x time.Time) qm.QueryMod {
	return qmhelper.Where(w.field, qmhelper.LT, x)
}
func (w whereHelpertime_Time) LTE(x time.Time) qm.QueryMod {
	return qmhelper.Where(w.field, qmhelper.LTE, x)
}
func (w whereHelpertime_Time) GT(x time.Time) qm.QueryMod {
	return qmhelper.Where(w.field, qmhelper.GT, x)
}
func (w whereHelpertime_Time) GTE(x time.Time) qm.QueryMod {
	return qmhelper.Where(w.field, qmhelper.GTE, x)
}

type whereHelperbool struct{ field string }

func (w whereHelperbool) EQ(x bool) qm.QueryMod  { return qmhelper.Where(w.field, qmhelper.EQ, x) }
func (w whereHelperbool) NEQ(x bool) qm.QueryMod { return qmhelper.Where(w.field, qmhelper.NEQ, x) }
func (w whereHelperbool) LT(x bool) qm.QueryMod  { return qmhelper.Where(w.field, qmhelper.LT, x) }
func (w whereHelperbool) LTE(x bool) qm.QueryMod { return qmhelper.Where(w.field, qmhelper.LTE, x) }
func (w whereHelperbool) GT(x bool) qm.QueryMod  { return qmhelper.Where(w.field, qmhelper.GT, x) }
func (w whereHelperbool) GTE(x bool) qm.QueryMod { return qmhelper.Where(w.field, qmhelper.GTE, x) }

var BilledMileageWhere = struct {
	ID           whereHelperstring
	PolicyNumber whereHelperstring
	Mileage      whereHelperfloat64
	BilledMonth  whereHelpertime_Time
	ExtractedAt  whereHelpertime_Time
	IsAudit      whereHelperbool
}{
	ID:           whereHelperstring{field: "\"risk_metrics\".\"billed_mileage\".\"id\""},
	PolicyNumber: whereHelperstring{field: "\"risk_metrics\".\"billed_mileage\".\"policy_number\""},
	Mileage:      whereHelperfloat64{field: "\"risk_metrics\".\"billed_mileage\".\"mileage\""},
	BilledMonth:  whereHelpertime_Time{field: "\"risk_metrics\".\"billed_mileage\".\"billed_month\""},
	ExtractedAt:  whereHelpertime_Time{field: "\"risk_metrics\".\"billed_mileage\".\"extracted_at\""},
	IsAudit:      whereHelperbool{field: "\"risk_metrics\".\"billed_mileage\".\"is_audit\""},
}

// BilledMileageRels is where relationship names are stored.
var BilledMileageRels = struct {
}{}

// billedMileageR is where relationships are stored.
type billedMileageR struct {
}

// NewStruct creates a new relationship struct
func (*billedMileageR) NewStruct() *billedMileageR {
	return &billedMileageR{}
}

// billedMileageL is where Load methods for each relationship are stored.
type billedMileageL struct{}

var (
	billedMileageAllColumns            = []string{"id", "policy_number", "mileage", "billed_month", "extracted_at", "is_audit"}
	billedMileageColumnsWithoutDefault = []string{"id", "policy_number", "mileage", "billed_month", "extracted_at"}
	billedMileageColumnsWithDefault    = []string{"is_audit"}
	billedMileagePrimaryKeyColumns     = []string{"id"}
	billedMileageGeneratedColumns      = []string{}
)

type (
	// BilledMileageSlice is an alias for a slice of pointers to BilledMileage.
	// This should almost always be used instead of []BilledMileage.
	BilledMileageSlice []*BilledMileage
	// BilledMileageHook is the signature for custom BilledMileage hook methods
	BilledMileageHook func(context.Context, boil.ContextExecutor, *BilledMileage) error

	billedMileageQuery struct {
		*queries.Query
	}
)

// Cache for insert, update and upsert
var (
	billedMileageType                 = reflect.TypeOf(&BilledMileage{})
	billedMileageMapping              = queries.MakeStructMapping(billedMileageType)
	billedMileagePrimaryKeyMapping, _ = queries.BindMapping(billedMileageType, billedMileageMapping, billedMileagePrimaryKeyColumns)
	billedMileageInsertCacheMut       sync.RWMutex
	billedMileageInsertCache          = make(map[string]insertCache)
	billedMileageUpdateCacheMut       sync.RWMutex
	billedMileageUpdateCache          = make(map[string]updateCache)
	billedMileageUpsertCacheMut       sync.RWMutex
	billedMileageUpsertCache          = make(map[string]insertCache)
)

var (
	// Force time package dependency for automated UpdatedAt/CreatedAt.
	_ = time.Second
	// Force qmhelper dependency for where clause generation (which doesn't
	// always happen)
	_ = qmhelper.Where
)

var billedMileageAfterSelectHooks []BilledMileageHook

var billedMileageBeforeInsertHooks []BilledMileageHook
var billedMileageAfterInsertHooks []BilledMileageHook

var billedMileageBeforeUpdateHooks []BilledMileageHook
var billedMileageAfterUpdateHooks []BilledMileageHook

var billedMileageBeforeDeleteHooks []BilledMileageHook
var billedMileageAfterDeleteHooks []BilledMileageHook

var billedMileageBeforeUpsertHooks []BilledMileageHook
var billedMileageAfterUpsertHooks []BilledMileageHook

// doAfterSelectHooks executes all "after Select" hooks.
func (o *BilledMileage) doAfterSelectHooks(ctx context.Context, exec boil.ContextExecutor) (err error) {
	if boil.HooksAreSkipped(ctx) {
		return nil
	}

	for _, hook := range billedMileageAfterSelectHooks {
		if err := hook(ctx, exec, o); err != nil {
			return err
		}
	}

	return nil
}

// doBeforeInsertHooks executes all "before insert" hooks.
func (o *BilledMileage) doBeforeInsertHooks(ctx context.Context, exec boil.ContextExecutor) (err error) {
	if boil.HooksAreSkipped(ctx) {
		return nil
	}

	for _, hook := range billedMileageBeforeInsertHooks {
		if err := hook(ctx, exec, o); err != nil {
			return err
		}
	}

	return nil
}

// doAfterInsertHooks executes all "after Insert" hooks.
func (o *BilledMileage) doAfterInsertHooks(ctx context.Context, exec boil.ContextExecutor) (err error) {
	if boil.HooksAreSkipped(ctx) {
		return nil
	}

	for _, hook := range billedMileageAfterInsertHooks {
		if err := hook(ctx, exec, o); err != nil {
			return err
		}
	}

	return nil
}

// doBeforeUpdateHooks executes all "before Update" hooks.
func (o *BilledMileage) doBeforeUpdateHooks(ctx context.Context, exec boil.ContextExecutor) (err error) {
	if boil.HooksAreSkipped(ctx) {
		return nil
	}

	for _, hook := range billedMileageBeforeUpdateHooks {
		if err := hook(ctx, exec, o); err != nil {
			return err
		}
	}

	return nil
}

// doAfterUpdateHooks executes all "after Update" hooks.
func (o *BilledMileage) doAfterUpdateHooks(ctx context.Context, exec boil.ContextExecutor) (err error) {
	if boil.HooksAreSkipped(ctx) {
		return nil
	}

	for _, hook := range billedMileageAfterUpdateHooks {
		if err := hook(ctx, exec, o); err != nil {
			return err
		}
	}

	return nil
}

// doBeforeDeleteHooks executes all "before Delete" hooks.
func (o *BilledMileage) doBeforeDeleteHooks(ctx context.Context, exec boil.ContextExecutor) (err error) {
	if boil.HooksAreSkipped(ctx) {
		return nil
	}

	for _, hook := range billedMileageBeforeDeleteHooks {
		if err := hook(ctx, exec, o); err != nil {
			return err
		}
	}

	return nil
}

// doAfterDeleteHooks executes all "after Delete" hooks.
func (o *BilledMileage) doAfterDeleteHooks(ctx context.Context, exec boil.ContextExecutor) (err error) {
	if boil.HooksAreSkipped(ctx) {
		return nil
	}

	for _, hook := range billedMileageAfterDeleteHooks {
		if err := hook(ctx, exec, o); err != nil {
			return err
		}
	}

	return nil
}

// doBeforeUpsertHooks executes all "before Upsert" hooks.
func (o *BilledMileage) doBeforeUpsertHooks(ctx context.Context, exec boil.ContextExecutor) (err error) {
	if boil.HooksAreSkipped(ctx) {
		return nil
	}

	for _, hook := range billedMileageBeforeUpsertHooks {
		if err := hook(ctx, exec, o); err != nil {
			return err
		}
	}

	return nil
}

// doAfterUpsertHooks executes all "after Upsert" hooks.
func (o *BilledMileage) doAfterUpsertHooks(ctx context.Context, exec boil.ContextExecutor) (err error) {
	if boil.HooksAreSkipped(ctx) {
		return nil
	}

	for _, hook := range billedMileageAfterUpsertHooks {
		if err := hook(ctx, exec, o); err != nil {
			return err
		}
	}

	return nil
}

// AddBilledMileageHook registers your hook function for all future operations.
func AddBilledMileageHook(hookPoint boil.HookPoint, billedMileageHook BilledMileageHook) {
	switch hookPoint {
	case boil.AfterSelectHook:
		billedMileageAfterSelectHooks = append(billedMileageAfterSelectHooks, billedMileageHook)
	case boil.BeforeInsertHook:
		billedMileageBeforeInsertHooks = append(billedMileageBeforeInsertHooks, billedMileageHook)
	case boil.AfterInsertHook:
		billedMileageAfterInsertHooks = append(billedMileageAfterInsertHooks, billedMileageHook)
	case boil.BeforeUpdateHook:
		billedMileageBeforeUpdateHooks = append(billedMileageBeforeUpdateHooks, billedMileageHook)
	case boil.AfterUpdateHook:
		billedMileageAfterUpdateHooks = append(billedMileageAfterUpdateHooks, billedMileageHook)
	case boil.BeforeDeleteHook:
		billedMileageBeforeDeleteHooks = append(billedMileageBeforeDeleteHooks, billedMileageHook)
	case boil.AfterDeleteHook:
		billedMileageAfterDeleteHooks = append(billedMileageAfterDeleteHooks, billedMileageHook)
	case boil.BeforeUpsertHook:
		billedMileageBeforeUpsertHooks = append(billedMileageBeforeUpsertHooks, billedMileageHook)
	case boil.AfterUpsertHook:
		billedMileageAfterUpsertHooks = append(billedMileageAfterUpsertHooks, billedMileageHook)
	}
}

// One returns a single billedMileage record from the query.
func (q billedMileageQuery) One(ctx context.Context, exec boil.ContextExecutor) (*BilledMileage, error) {
	o := &BilledMileage{}

	queries.SetLimit(q.Query, 1)

	err := q.Bind(ctx, exec, o)
	if err != nil {
		if errors.Is(err, sql.ErrNoRows) {
			return nil, sql.ErrNoRows
		}
		return nil, errors.Wrap(err, "risk_metrics: failed to execute a one query for billed_mileage")
	}

	if err := o.doAfterSelectHooks(ctx, exec); err != nil {
		return o, err
	}

	return o, nil
}

// All returns all BilledMileage records from the query.
func (q billedMileageQuery) All(ctx context.Context, exec boil.ContextExecutor) (BilledMileageSlice, error) {
	var o []*BilledMileage

	err := q.Bind(ctx, exec, &o)
	if err != nil {
		return nil, errors.Wrap(err, "risk_metrics: failed to assign all query results to BilledMileage slice")
	}

	if len(billedMileageAfterSelectHooks) != 0 {
		for _, obj := range o {
			if err := obj.doAfterSelectHooks(ctx, exec); err != nil {
				return o, err
			}
		}
	}

	return o, nil
}

// Count returns the count of all BilledMileage records in the query.
func (q billedMileageQuery) Count(ctx context.Context, exec boil.ContextExecutor) (int64, error) {
	var count int64

	queries.SetSelect(q.Query, nil)
	queries.SetCount(q.Query)

	err := q.Query.QueryRowContext(ctx, exec).Scan(&count)
	if err != nil {
		return 0, errors.Wrap(err, "risk_metrics: failed to count billed_mileage rows")
	}

	return count, nil
}

// Exists checks if the row exists in the table.
func (q billedMileageQuery) Exists(ctx context.Context, exec boil.ContextExecutor) (bool, error) {
	var count int64

	queries.SetSelect(q.Query, nil)
	queries.SetCount(q.Query)
	queries.SetLimit(q.Query, 1)

	err := q.Query.QueryRowContext(ctx, exec).Scan(&count)
	if err != nil {
		return false, errors.Wrap(err, "risk_metrics: failed to check if billed_mileage exists")
	}

	return count > 0, nil
}

// BilledMileages retrieves all the records using an executor.
func BilledMileages(mods ...qm.QueryMod) billedMileageQuery {
	mods = append(mods, qm.From("\"risk_metrics\".\"billed_mileage\""))
	q := NewQuery(mods...)
	if len(queries.GetSelect(q)) == 0 {
		queries.SetSelect(q, []string{"\"risk_metrics\".\"billed_mileage\".*"})
	}

	return billedMileageQuery{q}
}

// FindBilledMileage retrieves a single record by ID with an executor.
// If selectCols is empty Find will return all columns.
func FindBilledMileage(ctx context.Context, exec boil.ContextExecutor, iD string, selectCols ...string) (*BilledMileage, error) {
	billedMileageObj := &BilledMileage{}

	sel := "*"
	if len(selectCols) > 0 {
		sel = strings.Join(strmangle.IdentQuoteSlice(dialect.LQ, dialect.RQ, selectCols), ",")
	}
	query := fmt.Sprintf(
		"select %s from \"risk_metrics\".\"billed_mileage\" where \"id\"=$1", sel,
	)

	q := queries.Raw(query, iD)

	err := q.Bind(ctx, exec, billedMileageObj)
	if err != nil {
		if errors.Is(err, sql.ErrNoRows) {
			return nil, sql.ErrNoRows
		}
		return nil, errors.Wrap(err, "risk_metrics: unable to select from billed_mileage")
	}

	if err = billedMileageObj.doAfterSelectHooks(ctx, exec); err != nil {
		return billedMileageObj, err
	}

	return billedMileageObj, nil
}

// Insert a single record using an executor.
// See boil.Columns.InsertColumnSet documentation to understand column list inference for inserts.
func (o *BilledMileage) Insert(ctx context.Context, exec boil.ContextExecutor, columns boil.Columns) error {
	if o == nil {
		return errors.New("risk_metrics: no billed_mileage provided for insertion")
	}

	var err error

	if err := o.doBeforeInsertHooks(ctx, exec); err != nil {
		return err
	}

	nzDefaults := queries.NonZeroDefaultSet(billedMileageColumnsWithDefault, o)

	key := makeCacheKey(columns, nzDefaults)
	billedMileageInsertCacheMut.RLock()
	cache, cached := billedMileageInsertCache[key]
	billedMileageInsertCacheMut.RUnlock()

	if !cached {
		wl, returnColumns := columns.InsertColumnSet(
			billedMileageAllColumns,
			billedMileageColumnsWithDefault,
			billedMileageColumnsWithoutDefault,
			nzDefaults,
		)

		cache.valueMapping, err = queries.BindMapping(billedMileageType, billedMileageMapping, wl)
		if err != nil {
			return err
		}
		cache.retMapping, err = queries.BindMapping(billedMileageType, billedMileageMapping, returnColumns)
		if err != nil {
			return err
		}
		if len(wl) != 0 {
			cache.query = fmt.Sprintf("INSERT INTO \"risk_metrics\".\"billed_mileage\" (\"%s\") %%sVALUES (%s)%%s", strings.Join(wl, "\",\""), strmangle.Placeholders(dialect.UseIndexPlaceholders, len(wl), 1, 1))
		} else {
			cache.query = "INSERT INTO \"risk_metrics\".\"billed_mileage\" %sDEFAULT VALUES%s"
		}

		var queryOutput, queryReturning string

		if len(cache.retMapping) != 0 {
			queryReturning = fmt.Sprintf(" RETURNING \"%s\"", strings.Join(returnColumns, "\",\""))
		}

		cache.query = fmt.Sprintf(cache.query, queryOutput, queryReturning)
	}

	value := reflect.Indirect(reflect.ValueOf(o))
	vals := queries.ValuesFromMapping(value, cache.valueMapping)

	if boil.IsDebug(ctx) {
		writer := boil.DebugWriterFrom(ctx)
		fmt.Fprintln(writer, cache.query)
		fmt.Fprintln(writer, vals)
	}

	if len(cache.retMapping) != 0 {
		err = exec.QueryRowContext(ctx, cache.query, vals...).Scan(queries.PtrsFromMapping(value, cache.retMapping)...)
	} else {
		_, err = exec.ExecContext(ctx, cache.query, vals...)
	}

	if err != nil {
		return errors.Wrap(err, "risk_metrics: unable to insert into billed_mileage")
	}

	if !cached {
		billedMileageInsertCacheMut.Lock()
		billedMileageInsertCache[key] = cache
		billedMileageInsertCacheMut.Unlock()
	}

	return o.doAfterInsertHooks(ctx, exec)
}

// Update uses an executor to update the BilledMileage.
// See boil.Columns.UpdateColumnSet documentation to understand column list inference for updates.
// Update does not automatically update the record in case of default values. Use .Reload() to refresh the records.
func (o *BilledMileage) Update(ctx context.Context, exec boil.ContextExecutor, columns boil.Columns) (int64, error) {
	var err error
	if err = o.doBeforeUpdateHooks(ctx, exec); err != nil {
		return 0, err
	}
	key := makeCacheKey(columns, nil)
	billedMileageUpdateCacheMut.RLock()
	cache, cached := billedMileageUpdateCache[key]
	billedMileageUpdateCacheMut.RUnlock()

	if !cached {
		wl := columns.UpdateColumnSet(
			billedMileageAllColumns,
			billedMileagePrimaryKeyColumns,
		)

		if !columns.IsWhitelist() {
			wl = strmangle.SetComplement(wl, []string{"created_at"})
		}
		if len(wl) == 0 {
			return 0, errors.New("risk_metrics: unable to update billed_mileage, could not build whitelist")
		}

		cache.query = fmt.Sprintf("UPDATE \"risk_metrics\".\"billed_mileage\" SET %s WHERE %s",
			strmangle.SetParamNames("\"", "\"", 1, wl),
			strmangle.WhereClause("\"", "\"", len(wl)+1, billedMileagePrimaryKeyColumns),
		)
		cache.valueMapping, err = queries.BindMapping(billedMileageType, billedMileageMapping, append(wl, billedMileagePrimaryKeyColumns...))
		if err != nil {
			return 0, err
		}
	}

	values := queries.ValuesFromMapping(reflect.Indirect(reflect.ValueOf(o)), cache.valueMapping)

	if boil.IsDebug(ctx) {
		writer := boil.DebugWriterFrom(ctx)
		fmt.Fprintln(writer, cache.query)
		fmt.Fprintln(writer, values)
	}
	var result sql.Result
	result, err = exec.ExecContext(ctx, cache.query, values...)
	if err != nil {
		return 0, errors.Wrap(err, "risk_metrics: unable to update billed_mileage row")
	}

	rowsAff, err := result.RowsAffected()
	if err != nil {
		return 0, errors.Wrap(err, "risk_metrics: failed to get rows affected by update for billed_mileage")
	}

	if !cached {
		billedMileageUpdateCacheMut.Lock()
		billedMileageUpdateCache[key] = cache
		billedMileageUpdateCacheMut.Unlock()
	}

	return rowsAff, o.doAfterUpdateHooks(ctx, exec)
}

// UpdateAll updates all rows with the specified column values.
func (q billedMileageQuery) UpdateAll(ctx context.Context, exec boil.ContextExecutor, cols M) (int64, error) {
	queries.SetUpdate(q.Query, cols)

	result, err := q.Query.ExecContext(ctx, exec)
	if err != nil {
		return 0, errors.Wrap(err, "risk_metrics: unable to update all for billed_mileage")
	}

	rowsAff, err := result.RowsAffected()
	if err != nil {
		return 0, errors.Wrap(err, "risk_metrics: unable to retrieve rows affected for billed_mileage")
	}

	return rowsAff, nil
}

// UpdateAll updates all rows with the specified column values, using an executor.
func (o BilledMileageSlice) UpdateAll(ctx context.Context, exec boil.ContextExecutor, cols M) (int64, error) {
	ln := int64(len(o))
	if ln == 0 {
		return 0, nil
	}

	if len(cols) == 0 {
		return 0, errors.New("risk_metrics: update all requires at least one column argument")
	}

	colNames := make([]string, len(cols))
	args := make([]interface{}, len(cols))

	i := 0
	for name, value := range cols {
		colNames[i] = name
		args[i] = value
		i++
	}

	// Append all of the primary key values for each column
	for _, obj := range o {
		pkeyArgs := queries.ValuesFromMapping(reflect.Indirect(reflect.ValueOf(obj)), billedMileagePrimaryKeyMapping)
		args = append(args, pkeyArgs...)
	}

	sql := fmt.Sprintf("UPDATE \"risk_metrics\".\"billed_mileage\" SET %s WHERE %s",
		strmangle.SetParamNames("\"", "\"", 1, colNames),
		strmangle.WhereClauseRepeated(string(dialect.LQ), string(dialect.RQ), len(colNames)+1, billedMileagePrimaryKeyColumns, len(o)))

	if boil.IsDebug(ctx) {
		writer := boil.DebugWriterFrom(ctx)
		fmt.Fprintln(writer, sql)
		fmt.Fprintln(writer, args...)
	}
	result, err := exec.ExecContext(ctx, sql, args...)
	if err != nil {
		return 0, errors.Wrap(err, "risk_metrics: unable to update all in billedMileage slice")
	}

	rowsAff, err := result.RowsAffected()
	if err != nil {
		return 0, errors.Wrap(err, "risk_metrics: unable to retrieve rows affected all in update all billedMileage")
	}
	return rowsAff, nil
}

// Upsert attempts an insert using an executor, and does an update or ignore on conflict.
// See boil.Columns documentation for how to properly use updateColumns and insertColumns.
func (o *BilledMileage) Upsert(ctx context.Context, exec boil.ContextExecutor, updateOnConflict bool, conflictColumns []string, updateColumns, insertColumns boil.Columns) error {
	if o == nil {
		return errors.New("risk_metrics: no billed_mileage provided for upsert")
	}

	if err := o.doBeforeUpsertHooks(ctx, exec); err != nil {
		return err
	}

	nzDefaults := queries.NonZeroDefaultSet(billedMileageColumnsWithDefault, o)

	// Build cache key in-line uglily - mysql vs psql problems
	buf := strmangle.GetBuffer()
	if updateOnConflict {
		buf.WriteByte('t')
	} else {
		buf.WriteByte('f')
	}
	buf.WriteByte('.')
	for _, c := range conflictColumns {
		buf.WriteString(c)
	}
	buf.WriteByte('.')
	buf.WriteString(strconv.Itoa(updateColumns.Kind))
	for _, c := range updateColumns.Cols {
		buf.WriteString(c)
	}
	buf.WriteByte('.')
	buf.WriteString(strconv.Itoa(insertColumns.Kind))
	for _, c := range insertColumns.Cols {
		buf.WriteString(c)
	}
	buf.WriteByte('.')
	for _, c := range nzDefaults {
		buf.WriteString(c)
	}
	key := buf.String()
	strmangle.PutBuffer(buf)

	billedMileageUpsertCacheMut.RLock()
	cache, cached := billedMileageUpsertCache[key]
	billedMileageUpsertCacheMut.RUnlock()

	var err error

	if !cached {
		insert, ret := insertColumns.InsertColumnSet(
			billedMileageAllColumns,
			billedMileageColumnsWithDefault,
			billedMileageColumnsWithoutDefault,
			nzDefaults,
		)

		update := updateColumns.UpdateColumnSet(
			billedMileageAllColumns,
			billedMileagePrimaryKeyColumns,
		)

		if updateOnConflict && len(update) == 0 {
			return errors.New("risk_metrics: unable to upsert billed_mileage, could not build update column list")
		}

		conflict := conflictColumns
		if len(conflict) == 0 {
			conflict = make([]string, len(billedMileagePrimaryKeyColumns))
			copy(conflict, billedMileagePrimaryKeyColumns)
		}
		cache.query = buildUpsertQueryPostgres(dialect, "\"risk_metrics\".\"billed_mileage\"", updateOnConflict, ret, update, conflict, insert)

		cache.valueMapping, err = queries.BindMapping(billedMileageType, billedMileageMapping, insert)
		if err != nil {
			return err
		}
		if len(ret) != 0 {
			cache.retMapping, err = queries.BindMapping(billedMileageType, billedMileageMapping, ret)
			if err != nil {
				return err
			}
		}
	}

	value := reflect.Indirect(reflect.ValueOf(o))
	vals := queries.ValuesFromMapping(value, cache.valueMapping)
	var returns []interface{}
	if len(cache.retMapping) != 0 {
		returns = queries.PtrsFromMapping(value, cache.retMapping)
	}

	if boil.IsDebug(ctx) {
		writer := boil.DebugWriterFrom(ctx)
		fmt.Fprintln(writer, cache.query)
		fmt.Fprintln(writer, vals)
	}
	if len(cache.retMapping) != 0 {
		err = exec.QueryRowContext(ctx, cache.query, vals...).Scan(returns...)
		if errors.Is(err, sql.ErrNoRows) {
			err = nil // Postgres doesn't return anything when there's no update
		}
	} else {
		_, err = exec.ExecContext(ctx, cache.query, vals...)
	}
	if err != nil {
		return errors.Wrap(err, "risk_metrics: unable to upsert billed_mileage")
	}

	if !cached {
		billedMileageUpsertCacheMut.Lock()
		billedMileageUpsertCache[key] = cache
		billedMileageUpsertCacheMut.Unlock()
	}

	return o.doAfterUpsertHooks(ctx, exec)
}

// Delete deletes a single BilledMileage record with an executor.
// Delete will match against the primary key column to find the record to delete.
func (o *BilledMileage) Delete(ctx context.Context, exec boil.ContextExecutor) (int64, error) {
	if o == nil {
		return 0, errors.New("risk_metrics: no BilledMileage provided for delete")
	}

	if err := o.doBeforeDeleteHooks(ctx, exec); err != nil {
		return 0, err
	}

	args := queries.ValuesFromMapping(reflect.Indirect(reflect.ValueOf(o)), billedMileagePrimaryKeyMapping)
	sql := "DELETE FROM \"risk_metrics\".\"billed_mileage\" WHERE \"id\"=$1"

	if boil.IsDebug(ctx) {
		writer := boil.DebugWriterFrom(ctx)
		fmt.Fprintln(writer, sql)
		fmt.Fprintln(writer, args...)
	}
	result, err := exec.ExecContext(ctx, sql, args...)
	if err != nil {
		return 0, errors.Wrap(err, "risk_metrics: unable to delete from billed_mileage")
	}

	rowsAff, err := result.RowsAffected()
	if err != nil {
		return 0, errors.Wrap(err, "risk_metrics: failed to get rows affected by delete for billed_mileage")
	}

	if err := o.doAfterDeleteHooks(ctx, exec); err != nil {
		return 0, err
	}

	return rowsAff, nil
}

// DeleteAll deletes all matching rows.
func (q billedMileageQuery) DeleteAll(ctx context.Context, exec boil.ContextExecutor) (int64, error) {
	if q.Query == nil {
		return 0, errors.New("risk_metrics: no billedMileageQuery provided for delete all")
	}

	queries.SetDelete(q.Query)

	result, err := q.Query.ExecContext(ctx, exec)
	if err != nil {
		return 0, errors.Wrap(err, "risk_metrics: unable to delete all from billed_mileage")
	}

	rowsAff, err := result.RowsAffected()
	if err != nil {
		return 0, errors.Wrap(err, "risk_metrics: failed to get rows affected by deleteall for billed_mileage")
	}

	return rowsAff, nil
}

// DeleteAll deletes all rows in the slice, using an executor.
func (o BilledMileageSlice) DeleteAll(ctx context.Context, exec boil.ContextExecutor) (int64, error) {
	if len(o) == 0 {
		return 0, nil
	}

	if len(billedMileageBeforeDeleteHooks) != 0 {
		for _, obj := range o {
			if err := obj.doBeforeDeleteHooks(ctx, exec); err != nil {
				return 0, err
			}
		}
	}

	var args []interface{}
	for _, obj := range o {
		pkeyArgs := queries.ValuesFromMapping(reflect.Indirect(reflect.ValueOf(obj)), billedMileagePrimaryKeyMapping)
		args = append(args, pkeyArgs...)
	}

	sql := "DELETE FROM \"risk_metrics\".\"billed_mileage\" WHERE " +
		strmangle.WhereClauseRepeated(string(dialect.LQ), string(dialect.RQ), 1, billedMileagePrimaryKeyColumns, len(o))

	if boil.IsDebug(ctx) {
		writer := boil.DebugWriterFrom(ctx)
		fmt.Fprintln(writer, sql)
		fmt.Fprintln(writer, args)
	}
	result, err := exec.ExecContext(ctx, sql, args...)
	if err != nil {
		return 0, errors.Wrap(err, "risk_metrics: unable to delete all from billedMileage slice")
	}

	rowsAff, err := result.RowsAffected()
	if err != nil {
		return 0, errors.Wrap(err, "risk_metrics: failed to get rows affected by deleteall for billed_mileage")
	}

	if len(billedMileageAfterDeleteHooks) != 0 {
		for _, obj := range o {
			if err := obj.doAfterDeleteHooks(ctx, exec); err != nil {
				return 0, err
			}
		}
	}

	return rowsAff, nil
}

// Reload refetches the object from the database
// using the primary keys with an executor.
func (o *BilledMileage) Reload(ctx context.Context, exec boil.ContextExecutor) error {
	ret, err := FindBilledMileage(ctx, exec, o.ID)
	if err != nil {
		return err
	}

	*o = *ret
	return nil
}

// ReloadAll refetches every row with matching primary key column values
// and overwrites the original object slice with the newly updated slice.
func (o *BilledMileageSlice) ReloadAll(ctx context.Context, exec boil.ContextExecutor) error {
	if o == nil || len(*o) == 0 {
		return nil
	}

	slice := BilledMileageSlice{}
	var args []interface{}
	for _, obj := range *o {
		pkeyArgs := queries.ValuesFromMapping(reflect.Indirect(reflect.ValueOf(obj)), billedMileagePrimaryKeyMapping)
		args = append(args, pkeyArgs...)
	}

	sql := "SELECT \"risk_metrics\".\"billed_mileage\".* FROM \"risk_metrics\".\"billed_mileage\" WHERE " +
		strmangle.WhereClauseRepeated(string(dialect.LQ), string(dialect.RQ), 1, billedMileagePrimaryKeyColumns, len(*o))

	q := queries.Raw(sql, args...)

	err := q.Bind(ctx, exec, &slice)
	if err != nil {
		return errors.Wrap(err, "risk_metrics: unable to reload all in BilledMileageSlice")
	}

	*o = slice

	return nil
}

// BilledMileageExists checks if the BilledMileage row exists.
func BilledMileageExists(ctx context.Context, exec boil.ContextExecutor, iD string) (bool, error) {
	var exists bool
	sql := "select exists(select 1 from \"risk_metrics\".\"billed_mileage\" where \"id\"=$1 limit 1)"

	if boil.IsDebug(ctx) {
		writer := boil.DebugWriterFrom(ctx)
		fmt.Fprintln(writer, sql)
		fmt.Fprintln(writer, iD)
	}
	row := exec.QueryRowContext(ctx, sql, iD)

	err := row.Scan(&exists)
	if err != nil {
		return false, errors.Wrap(err, "risk_metrics: unable to check if billed_mileage exists")
	}

	return exists, nil
}
