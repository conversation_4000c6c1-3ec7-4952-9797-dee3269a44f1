// Code generated by SQLBoiler (https://github.com/volatiletech/sqlboiler). DO NOT EDIT.
// This file is meant to be re-generated in place and/or deleted at any time.

package experiments

import (
	"context"
	"database/sql"
	"fmt"
	"reflect"
	"strconv"
	"strings"
	"sync"
	"time"

	"github.com/friendsofgo/errors"
	"github.com/volatiletech/null/v8"
	"github.com/volatiletech/sqlboiler/v4/boil"
	"github.com/volatiletech/sqlboiler/v4/queries"
	"github.com/volatiletech/sqlboiler/v4/queries/qm"
	"github.com/volatiletech/sqlboiler/v4/queries/qmhelper"
	"github.com/volatiletech/strmangle"
)

// StatusHistory is an object representing the database table.
type StatusHistory struct {
	ID           string      `boil:"id" json:"id" toml:"id" yaml:"id"`
	ExperimentID string      `boil:"experiment_id" json:"experiment_id" toml:"experiment_id" yaml:"experiment_id"`
	Status       string      `boil:"status" json:"status" toml:"status" yaml:"status"`
	ChangedBy    string      `boil:"changed_by" json:"changed_by" toml:"changed_by" yaml:"changed_by"`
	ChangeReason null.String `boil:"change_reason" json:"change_reason,omitempty" toml:"change_reason" yaml:"change_reason,omitempty"`
	CreatedAt    time.Time   `boil:"created_at" json:"created_at" toml:"created_at" yaml:"created_at"`

	R *statusHistoryR `boil:"" json:"" toml:"" yaml:""`
	L statusHistoryL  `boil:"-" json:"-" toml:"-" yaml:"-"`
}

var StatusHistoryColumns = struct {
	ID           string
	ExperimentID string
	Status       string
	ChangedBy    string
	ChangeReason string
	CreatedAt    string
}{
	ID:           "id",
	ExperimentID: "experiment_id",
	Status:       "status",
	ChangedBy:    "changed_by",
	ChangeReason: "change_reason",
	CreatedAt:    "created_at",
}

var StatusHistoryTableColumns = struct {
	ID           string
	ExperimentID string
	Status       string
	ChangedBy    string
	ChangeReason string
	CreatedAt    string
}{
	ID:           "status_histories.id",
	ExperimentID: "status_histories.experiment_id",
	Status:       "status_histories.status",
	ChangedBy:    "status_histories.changed_by",
	ChangeReason: "status_histories.change_reason",
	CreatedAt:    "status_histories.created_at",
}

// Generated where

var StatusHistoryWhere = struct {
	ID           whereHelperstring
	ExperimentID whereHelperstring
	Status       whereHelperstring
	ChangedBy    whereHelperstring
	ChangeReason whereHelpernull_String
	CreatedAt    whereHelpertime_Time
}{
	ID:           whereHelperstring{field: "\"experiments\".\"status_histories\".\"id\""},
	ExperimentID: whereHelperstring{field: "\"experiments\".\"status_histories\".\"experiment_id\""},
	Status:       whereHelperstring{field: "\"experiments\".\"status_histories\".\"status\""},
	ChangedBy:    whereHelperstring{field: "\"experiments\".\"status_histories\".\"changed_by\""},
	ChangeReason: whereHelpernull_String{field: "\"experiments\".\"status_histories\".\"change_reason\""},
	CreatedAt:    whereHelpertime_Time{field: "\"experiments\".\"status_histories\".\"created_at\""},
}

// StatusHistoryRels is where relationship names are stored.
var StatusHistoryRels = struct {
	Experiment string
}{
	Experiment: "Experiment",
}

// statusHistoryR is where relationships are stored.
type statusHistoryR struct {
	Experiment *ExperimentDetail `boil:"Experiment" json:"Experiment" toml:"Experiment" yaml:"Experiment"`
}

// NewStruct creates a new relationship struct
func (*statusHistoryR) NewStruct() *statusHistoryR {
	return &statusHistoryR{}
}

// statusHistoryL is where Load methods for each relationship are stored.
type statusHistoryL struct{}

var (
	statusHistoryAllColumns            = []string{"id", "experiment_id", "status", "changed_by", "change_reason", "created_at"}
	statusHistoryColumnsWithoutDefault = []string{"id", "experiment_id", "status", "changed_by", "created_at"}
	statusHistoryColumnsWithDefault    = []string{"change_reason"}
	statusHistoryPrimaryKeyColumns     = []string{"id"}
	statusHistoryGeneratedColumns      = []string{}
)

type (
	// StatusHistorySlice is an alias for a slice of pointers to StatusHistory.
	// This should almost always be used instead of []StatusHistory.
	StatusHistorySlice []*StatusHistory
	// StatusHistoryHook is the signature for custom StatusHistory hook methods
	StatusHistoryHook func(context.Context, boil.ContextExecutor, *StatusHistory) error

	statusHistoryQuery struct {
		*queries.Query
	}
)

// Cache for insert, update and upsert
var (
	statusHistoryType                 = reflect.TypeOf(&StatusHistory{})
	statusHistoryMapping              = queries.MakeStructMapping(statusHistoryType)
	statusHistoryPrimaryKeyMapping, _ = queries.BindMapping(statusHistoryType, statusHistoryMapping, statusHistoryPrimaryKeyColumns)
	statusHistoryInsertCacheMut       sync.RWMutex
	statusHistoryInsertCache          = make(map[string]insertCache)
	statusHistoryUpdateCacheMut       sync.RWMutex
	statusHistoryUpdateCache          = make(map[string]updateCache)
	statusHistoryUpsertCacheMut       sync.RWMutex
	statusHistoryUpsertCache          = make(map[string]insertCache)
)

var (
	// Force time package dependency for automated UpdatedAt/CreatedAt.
	_ = time.Second
	// Force qmhelper dependency for where clause generation (which doesn't
	// always happen)
	_ = qmhelper.Where
)

var statusHistoryAfterSelectHooks []StatusHistoryHook

var statusHistoryBeforeInsertHooks []StatusHistoryHook
var statusHistoryAfterInsertHooks []StatusHistoryHook

var statusHistoryBeforeUpdateHooks []StatusHistoryHook
var statusHistoryAfterUpdateHooks []StatusHistoryHook

var statusHistoryBeforeDeleteHooks []StatusHistoryHook
var statusHistoryAfterDeleteHooks []StatusHistoryHook

var statusHistoryBeforeUpsertHooks []StatusHistoryHook
var statusHistoryAfterUpsertHooks []StatusHistoryHook

// doAfterSelectHooks executes all "after Select" hooks.
func (o *StatusHistory) doAfterSelectHooks(ctx context.Context, exec boil.ContextExecutor) (err error) {
	if boil.HooksAreSkipped(ctx) {
		return nil
	}

	for _, hook := range statusHistoryAfterSelectHooks {
		if err := hook(ctx, exec, o); err != nil {
			return err
		}
	}

	return nil
}

// doBeforeInsertHooks executes all "before insert" hooks.
func (o *StatusHistory) doBeforeInsertHooks(ctx context.Context, exec boil.ContextExecutor) (err error) {
	if boil.HooksAreSkipped(ctx) {
		return nil
	}

	for _, hook := range statusHistoryBeforeInsertHooks {
		if err := hook(ctx, exec, o); err != nil {
			return err
		}
	}

	return nil
}

// doAfterInsertHooks executes all "after Insert" hooks.
func (o *StatusHistory) doAfterInsertHooks(ctx context.Context, exec boil.ContextExecutor) (err error) {
	if boil.HooksAreSkipped(ctx) {
		return nil
	}

	for _, hook := range statusHistoryAfterInsertHooks {
		if err := hook(ctx, exec, o); err != nil {
			return err
		}
	}

	return nil
}

// doBeforeUpdateHooks executes all "before Update" hooks.
func (o *StatusHistory) doBeforeUpdateHooks(ctx context.Context, exec boil.ContextExecutor) (err error) {
	if boil.HooksAreSkipped(ctx) {
		return nil
	}

	for _, hook := range statusHistoryBeforeUpdateHooks {
		if err := hook(ctx, exec, o); err != nil {
			return err
		}
	}

	return nil
}

// doAfterUpdateHooks executes all "after Update" hooks.
func (o *StatusHistory) doAfterUpdateHooks(ctx context.Context, exec boil.ContextExecutor) (err error) {
	if boil.HooksAreSkipped(ctx) {
		return nil
	}

	for _, hook := range statusHistoryAfterUpdateHooks {
		if err := hook(ctx, exec, o); err != nil {
			return err
		}
	}

	return nil
}

// doBeforeDeleteHooks executes all "before Delete" hooks.
func (o *StatusHistory) doBeforeDeleteHooks(ctx context.Context, exec boil.ContextExecutor) (err error) {
	if boil.HooksAreSkipped(ctx) {
		return nil
	}

	for _, hook := range statusHistoryBeforeDeleteHooks {
		if err := hook(ctx, exec, o); err != nil {
			return err
		}
	}

	return nil
}

// doAfterDeleteHooks executes all "after Delete" hooks.
func (o *StatusHistory) doAfterDeleteHooks(ctx context.Context, exec boil.ContextExecutor) (err error) {
	if boil.HooksAreSkipped(ctx) {
		return nil
	}

	for _, hook := range statusHistoryAfterDeleteHooks {
		if err := hook(ctx, exec, o); err != nil {
			return err
		}
	}

	return nil
}

// doBeforeUpsertHooks executes all "before Upsert" hooks.
func (o *StatusHistory) doBeforeUpsertHooks(ctx context.Context, exec boil.ContextExecutor) (err error) {
	if boil.HooksAreSkipped(ctx) {
		return nil
	}

	for _, hook := range statusHistoryBeforeUpsertHooks {
		if err := hook(ctx, exec, o); err != nil {
			return err
		}
	}

	return nil
}

// doAfterUpsertHooks executes all "after Upsert" hooks.
func (o *StatusHistory) doAfterUpsertHooks(ctx context.Context, exec boil.ContextExecutor) (err error) {
	if boil.HooksAreSkipped(ctx) {
		return nil
	}

	for _, hook := range statusHistoryAfterUpsertHooks {
		if err := hook(ctx, exec, o); err != nil {
			return err
		}
	}

	return nil
}

// AddStatusHistoryHook registers your hook function for all future operations.
func AddStatusHistoryHook(hookPoint boil.HookPoint, statusHistoryHook StatusHistoryHook) {
	switch hookPoint {
	case boil.AfterSelectHook:
		statusHistoryAfterSelectHooks = append(statusHistoryAfterSelectHooks, statusHistoryHook)
	case boil.BeforeInsertHook:
		statusHistoryBeforeInsertHooks = append(statusHistoryBeforeInsertHooks, statusHistoryHook)
	case boil.AfterInsertHook:
		statusHistoryAfterInsertHooks = append(statusHistoryAfterInsertHooks, statusHistoryHook)
	case boil.BeforeUpdateHook:
		statusHistoryBeforeUpdateHooks = append(statusHistoryBeforeUpdateHooks, statusHistoryHook)
	case boil.AfterUpdateHook:
		statusHistoryAfterUpdateHooks = append(statusHistoryAfterUpdateHooks, statusHistoryHook)
	case boil.BeforeDeleteHook:
		statusHistoryBeforeDeleteHooks = append(statusHistoryBeforeDeleteHooks, statusHistoryHook)
	case boil.AfterDeleteHook:
		statusHistoryAfterDeleteHooks = append(statusHistoryAfterDeleteHooks, statusHistoryHook)
	case boil.BeforeUpsertHook:
		statusHistoryBeforeUpsertHooks = append(statusHistoryBeforeUpsertHooks, statusHistoryHook)
	case boil.AfterUpsertHook:
		statusHistoryAfterUpsertHooks = append(statusHistoryAfterUpsertHooks, statusHistoryHook)
	}
}

// One returns a single statusHistory record from the query.
func (q statusHistoryQuery) One(ctx context.Context, exec boil.ContextExecutor) (*StatusHistory, error) {
	o := &StatusHistory{}

	queries.SetLimit(q.Query, 1)

	err := q.Bind(ctx, exec, o)
	if err != nil {
		if errors.Is(err, sql.ErrNoRows) {
			return nil, sql.ErrNoRows
		}
		return nil, errors.Wrap(err, "experiments: failed to execute a one query for status_histories")
	}

	if err := o.doAfterSelectHooks(ctx, exec); err != nil {
		return o, err
	}

	return o, nil
}

// All returns all StatusHistory records from the query.
func (q statusHistoryQuery) All(ctx context.Context, exec boil.ContextExecutor) (StatusHistorySlice, error) {
	var o []*StatusHistory

	err := q.Bind(ctx, exec, &o)
	if err != nil {
		return nil, errors.Wrap(err, "experiments: failed to assign all query results to StatusHistory slice")
	}

	if len(statusHistoryAfterSelectHooks) != 0 {
		for _, obj := range o {
			if err := obj.doAfterSelectHooks(ctx, exec); err != nil {
				return o, err
			}
		}
	}

	return o, nil
}

// Count returns the count of all StatusHistory records in the query.
func (q statusHistoryQuery) Count(ctx context.Context, exec boil.ContextExecutor) (int64, error) {
	var count int64

	queries.SetSelect(q.Query, nil)
	queries.SetCount(q.Query)

	err := q.Query.QueryRowContext(ctx, exec).Scan(&count)
	if err != nil {
		return 0, errors.Wrap(err, "experiments: failed to count status_histories rows")
	}

	return count, nil
}

// Exists checks if the row exists in the table.
func (q statusHistoryQuery) Exists(ctx context.Context, exec boil.ContextExecutor) (bool, error) {
	var count int64

	queries.SetSelect(q.Query, nil)
	queries.SetCount(q.Query)
	queries.SetLimit(q.Query, 1)

	err := q.Query.QueryRowContext(ctx, exec).Scan(&count)
	if err != nil {
		return false, errors.Wrap(err, "experiments: failed to check if status_histories exists")
	}

	return count > 0, nil
}

// Experiment pointed to by the foreign key.
func (o *StatusHistory) Experiment(mods ...qm.QueryMod) experimentDetailQuery {
	queryMods := []qm.QueryMod{
		qm.Where("\"id\" = ?", o.ExperimentID),
	}

	queryMods = append(queryMods, mods...)

	return ExperimentDetails(queryMods...)
}

// LoadExperiment allows an eager lookup of values, cached into the
// loaded structs of the objects. This is for an N-1 relationship.
func (statusHistoryL) LoadExperiment(ctx context.Context, e boil.ContextExecutor, singular bool, maybeStatusHistory interface{}, mods queries.Applicator) error {
	var slice []*StatusHistory
	var object *StatusHistory

	if singular {
		object = maybeStatusHistory.(*StatusHistory)
	} else {
		slice = *maybeStatusHistory.(*[]*StatusHistory)
	}

	args := make([]interface{}, 0, 1)
	if singular {
		if object.R == nil {
			object.R = &statusHistoryR{}
		}
		args = append(args, object.ExperimentID)

	} else {
	Outer:
		for _, obj := range slice {
			if obj.R == nil {
				obj.R = &statusHistoryR{}
			}

			for _, a := range args {
				if a == obj.ExperimentID {
					continue Outer
				}
			}

			args = append(args, obj.ExperimentID)

		}
	}

	if len(args) == 0 {
		return nil
	}

	query := NewQuery(
		qm.From(`experiments.experiment_details`),
		qm.WhereIn(`experiments.experiment_details.id in ?`, args...),
	)
	if mods != nil {
		mods.Apply(query)
	}

	results, err := query.QueryContext(ctx, e)
	if err != nil {
		return errors.Wrap(err, "failed to eager load ExperimentDetail")
	}

	var resultSlice []*ExperimentDetail
	if err = queries.Bind(results, &resultSlice); err != nil {
		return errors.Wrap(err, "failed to bind eager loaded slice ExperimentDetail")
	}

	if err = results.Close(); err != nil {
		return errors.Wrap(err, "failed to close results of eager load for experiment_details")
	}
	if err = results.Err(); err != nil {
		return errors.Wrap(err, "error occurred during iteration of eager loaded relations for experiment_details")
	}

	if len(statusHistoryAfterSelectHooks) != 0 {
		for _, obj := range resultSlice {
			if err := obj.doAfterSelectHooks(ctx, e); err != nil {
				return err
			}
		}
	}

	if len(resultSlice) == 0 {
		return nil
	}

	if singular {
		foreign := resultSlice[0]
		object.R.Experiment = foreign
		if foreign.R == nil {
			foreign.R = &experimentDetailR{}
		}
		foreign.R.ExperimentStatusHistories = append(foreign.R.ExperimentStatusHistories, object)
		return nil
	}

	for _, local := range slice {
		for _, foreign := range resultSlice {
			if local.ExperimentID == foreign.ID {
				local.R.Experiment = foreign
				if foreign.R == nil {
					foreign.R = &experimentDetailR{}
				}
				foreign.R.ExperimentStatusHistories = append(foreign.R.ExperimentStatusHistories, local)
				break
			}
		}
	}

	return nil
}

// SetExperiment of the statusHistory to the related item.
// Sets o.R.Experiment to related.
// Adds o to related.R.ExperimentStatusHistories.
func (o *StatusHistory) SetExperiment(ctx context.Context, exec boil.ContextExecutor, insert bool, related *ExperimentDetail) error {
	var err error
	if insert {
		if err = related.Insert(ctx, exec, boil.Infer()); err != nil {
			return errors.Wrap(err, "failed to insert into foreign table")
		}
	}

	updateQuery := fmt.Sprintf(
		"UPDATE \"experiments\".\"status_histories\" SET %s WHERE %s",
		strmangle.SetParamNames("\"", "\"", 1, []string{"experiment_id"}),
		strmangle.WhereClause("\"", "\"", 2, statusHistoryPrimaryKeyColumns),
	)
	values := []interface{}{related.ID, o.ID}

	if boil.IsDebug(ctx) {
		writer := boil.DebugWriterFrom(ctx)
		fmt.Fprintln(writer, updateQuery)
		fmt.Fprintln(writer, values)
	}
	if _, err = exec.ExecContext(ctx, updateQuery, values...); err != nil {
		return errors.Wrap(err, "failed to update local table")
	}

	o.ExperimentID = related.ID
	if o.R == nil {
		o.R = &statusHistoryR{
			Experiment: related,
		}
	} else {
		o.R.Experiment = related
	}

	if related.R == nil {
		related.R = &experimentDetailR{
			ExperimentStatusHistories: StatusHistorySlice{o},
		}
	} else {
		related.R.ExperimentStatusHistories = append(related.R.ExperimentStatusHistories, o)
	}

	return nil
}

// StatusHistories retrieves all the records using an executor.
func StatusHistories(mods ...qm.QueryMod) statusHistoryQuery {
	mods = append(mods, qm.From("\"experiments\".\"status_histories\""))
	q := NewQuery(mods...)
	if len(queries.GetSelect(q)) == 0 {
		queries.SetSelect(q, []string{"\"experiments\".\"status_histories\".*"})
	}

	return statusHistoryQuery{q}
}

// FindStatusHistory retrieves a single record by ID with an executor.
// If selectCols is empty Find will return all columns.
func FindStatusHistory(ctx context.Context, exec boil.ContextExecutor, iD string, selectCols ...string) (*StatusHistory, error) {
	statusHistoryObj := &StatusHistory{}

	sel := "*"
	if len(selectCols) > 0 {
		sel = strings.Join(strmangle.IdentQuoteSlice(dialect.LQ, dialect.RQ, selectCols), ",")
	}
	query := fmt.Sprintf(
		"select %s from \"experiments\".\"status_histories\" where \"id\"=$1", sel,
	)

	q := queries.Raw(query, iD)

	err := q.Bind(ctx, exec, statusHistoryObj)
	if err != nil {
		if errors.Is(err, sql.ErrNoRows) {
			return nil, sql.ErrNoRows
		}
		return nil, errors.Wrap(err, "experiments: unable to select from status_histories")
	}

	if err = statusHistoryObj.doAfterSelectHooks(ctx, exec); err != nil {
		return statusHistoryObj, err
	}

	return statusHistoryObj, nil
}

// Insert a single record using an executor.
// See boil.Columns.InsertColumnSet documentation to understand column list inference for inserts.
func (o *StatusHistory) Insert(ctx context.Context, exec boil.ContextExecutor, columns boil.Columns) error {
	if o == nil {
		return errors.New("experiments: no status_histories provided for insertion")
	}

	var err error
	if !boil.TimestampsAreSkipped(ctx) {
		currTime := time.Now().In(boil.GetLocation())

		if o.CreatedAt.IsZero() {
			o.CreatedAt = currTime
		}
	}

	if err := o.doBeforeInsertHooks(ctx, exec); err != nil {
		return err
	}

	nzDefaults := queries.NonZeroDefaultSet(statusHistoryColumnsWithDefault, o)

	key := makeCacheKey(columns, nzDefaults)
	statusHistoryInsertCacheMut.RLock()
	cache, cached := statusHistoryInsertCache[key]
	statusHistoryInsertCacheMut.RUnlock()

	if !cached {
		wl, returnColumns := columns.InsertColumnSet(
			statusHistoryAllColumns,
			statusHistoryColumnsWithDefault,
			statusHistoryColumnsWithoutDefault,
			nzDefaults,
		)

		cache.valueMapping, err = queries.BindMapping(statusHistoryType, statusHistoryMapping, wl)
		if err != nil {
			return err
		}
		cache.retMapping, err = queries.BindMapping(statusHistoryType, statusHistoryMapping, returnColumns)
		if err != nil {
			return err
		}
		if len(wl) != 0 {
			cache.query = fmt.Sprintf("INSERT INTO \"experiments\".\"status_histories\" (\"%s\") %%sVALUES (%s)%%s", strings.Join(wl, "\",\""), strmangle.Placeholders(dialect.UseIndexPlaceholders, len(wl), 1, 1))
		} else {
			cache.query = "INSERT INTO \"experiments\".\"status_histories\" %sDEFAULT VALUES%s"
		}

		var queryOutput, queryReturning string

		if len(cache.retMapping) != 0 {
			queryReturning = fmt.Sprintf(" RETURNING \"%s\"", strings.Join(returnColumns, "\",\""))
		}

		cache.query = fmt.Sprintf(cache.query, queryOutput, queryReturning)
	}

	value := reflect.Indirect(reflect.ValueOf(o))
	vals := queries.ValuesFromMapping(value, cache.valueMapping)

	if boil.IsDebug(ctx) {
		writer := boil.DebugWriterFrom(ctx)
		fmt.Fprintln(writer, cache.query)
		fmt.Fprintln(writer, vals)
	}

	if len(cache.retMapping) != 0 {
		err = exec.QueryRowContext(ctx, cache.query, vals...).Scan(queries.PtrsFromMapping(value, cache.retMapping)...)
	} else {
		_, err = exec.ExecContext(ctx, cache.query, vals...)
	}

	if err != nil {
		return errors.Wrap(err, "experiments: unable to insert into status_histories")
	}

	if !cached {
		statusHistoryInsertCacheMut.Lock()
		statusHistoryInsertCache[key] = cache
		statusHistoryInsertCacheMut.Unlock()
	}

	return o.doAfterInsertHooks(ctx, exec)
}

// Update uses an executor to update the StatusHistory.
// See boil.Columns.UpdateColumnSet documentation to understand column list inference for updates.
// Update does not automatically update the record in case of default values. Use .Reload() to refresh the records.
func (o *StatusHistory) Update(ctx context.Context, exec boil.ContextExecutor, columns boil.Columns) (int64, error) {
	var err error
	if err = o.doBeforeUpdateHooks(ctx, exec); err != nil {
		return 0, err
	}
	key := makeCacheKey(columns, nil)
	statusHistoryUpdateCacheMut.RLock()
	cache, cached := statusHistoryUpdateCache[key]
	statusHistoryUpdateCacheMut.RUnlock()

	if !cached {
		wl := columns.UpdateColumnSet(
			statusHistoryAllColumns,
			statusHistoryPrimaryKeyColumns,
		)

		if !columns.IsWhitelist() {
			wl = strmangle.SetComplement(wl, []string{"created_at"})
		}
		if len(wl) == 0 {
			return 0, errors.New("experiments: unable to update status_histories, could not build whitelist")
		}

		cache.query = fmt.Sprintf("UPDATE \"experiments\".\"status_histories\" SET %s WHERE %s",
			strmangle.SetParamNames("\"", "\"", 1, wl),
			strmangle.WhereClause("\"", "\"", len(wl)+1, statusHistoryPrimaryKeyColumns),
		)
		cache.valueMapping, err = queries.BindMapping(statusHistoryType, statusHistoryMapping, append(wl, statusHistoryPrimaryKeyColumns...))
		if err != nil {
			return 0, err
		}
	}

	values := queries.ValuesFromMapping(reflect.Indirect(reflect.ValueOf(o)), cache.valueMapping)

	if boil.IsDebug(ctx) {
		writer := boil.DebugWriterFrom(ctx)
		fmt.Fprintln(writer, cache.query)
		fmt.Fprintln(writer, values)
	}
	var result sql.Result
	result, err = exec.ExecContext(ctx, cache.query, values...)
	if err != nil {
		return 0, errors.Wrap(err, "experiments: unable to update status_histories row")
	}

	rowsAff, err := result.RowsAffected()
	if err != nil {
		return 0, errors.Wrap(err, "experiments: failed to get rows affected by update for status_histories")
	}

	if !cached {
		statusHistoryUpdateCacheMut.Lock()
		statusHistoryUpdateCache[key] = cache
		statusHistoryUpdateCacheMut.Unlock()
	}

	return rowsAff, o.doAfterUpdateHooks(ctx, exec)
}

// UpdateAll updates all rows with the specified column values.
func (q statusHistoryQuery) UpdateAll(ctx context.Context, exec boil.ContextExecutor, cols M) (int64, error) {
	queries.SetUpdate(q.Query, cols)

	result, err := q.Query.ExecContext(ctx, exec)
	if err != nil {
		return 0, errors.Wrap(err, "experiments: unable to update all for status_histories")
	}

	rowsAff, err := result.RowsAffected()
	if err != nil {
		return 0, errors.Wrap(err, "experiments: unable to retrieve rows affected for status_histories")
	}

	return rowsAff, nil
}

// UpdateAll updates all rows with the specified column values, using an executor.
func (o StatusHistorySlice) UpdateAll(ctx context.Context, exec boil.ContextExecutor, cols M) (int64, error) {
	ln := int64(len(o))
	if ln == 0 {
		return 0, nil
	}

	if len(cols) == 0 {
		return 0, errors.New("experiments: update all requires at least one column argument")
	}

	colNames := make([]string, len(cols))
	args := make([]interface{}, len(cols))

	i := 0
	for name, value := range cols {
		colNames[i] = name
		args[i] = value
		i++
	}

	// Append all of the primary key values for each column
	for _, obj := range o {
		pkeyArgs := queries.ValuesFromMapping(reflect.Indirect(reflect.ValueOf(obj)), statusHistoryPrimaryKeyMapping)
		args = append(args, pkeyArgs...)
	}

	sql := fmt.Sprintf("UPDATE \"experiments\".\"status_histories\" SET %s WHERE %s",
		strmangle.SetParamNames("\"", "\"", 1, colNames),
		strmangle.WhereClauseRepeated(string(dialect.LQ), string(dialect.RQ), len(colNames)+1, statusHistoryPrimaryKeyColumns, len(o)))

	if boil.IsDebug(ctx) {
		writer := boil.DebugWriterFrom(ctx)
		fmt.Fprintln(writer, sql)
		fmt.Fprintln(writer, args...)
	}
	result, err := exec.ExecContext(ctx, sql, args...)
	if err != nil {
		return 0, errors.Wrap(err, "experiments: unable to update all in statusHistory slice")
	}

	rowsAff, err := result.RowsAffected()
	if err != nil {
		return 0, errors.Wrap(err, "experiments: unable to retrieve rows affected all in update all statusHistory")
	}
	return rowsAff, nil
}

// Upsert attempts an insert using an executor, and does an update or ignore on conflict.
// See boil.Columns documentation for how to properly use updateColumns and insertColumns.
func (o *StatusHistory) Upsert(ctx context.Context, exec boil.ContextExecutor, updateOnConflict bool, conflictColumns []string, updateColumns, insertColumns boil.Columns) error {
	if o == nil {
		return errors.New("experiments: no status_histories provided for upsert")
	}
	if !boil.TimestampsAreSkipped(ctx) {
		currTime := time.Now().In(boil.GetLocation())

		if o.CreatedAt.IsZero() {
			o.CreatedAt = currTime
		}
	}

	if err := o.doBeforeUpsertHooks(ctx, exec); err != nil {
		return err
	}

	nzDefaults := queries.NonZeroDefaultSet(statusHistoryColumnsWithDefault, o)

	// Build cache key in-line uglily - mysql vs psql problems
	buf := strmangle.GetBuffer()
	if updateOnConflict {
		buf.WriteByte('t')
	} else {
		buf.WriteByte('f')
	}
	buf.WriteByte('.')
	for _, c := range conflictColumns {
		buf.WriteString(c)
	}
	buf.WriteByte('.')
	buf.WriteString(strconv.Itoa(updateColumns.Kind))
	for _, c := range updateColumns.Cols {
		buf.WriteString(c)
	}
	buf.WriteByte('.')
	buf.WriteString(strconv.Itoa(insertColumns.Kind))
	for _, c := range insertColumns.Cols {
		buf.WriteString(c)
	}
	buf.WriteByte('.')
	for _, c := range nzDefaults {
		buf.WriteString(c)
	}
	key := buf.String()
	strmangle.PutBuffer(buf)

	statusHistoryUpsertCacheMut.RLock()
	cache, cached := statusHistoryUpsertCache[key]
	statusHistoryUpsertCacheMut.RUnlock()

	var err error

	if !cached {
		insert, ret := insertColumns.InsertColumnSet(
			statusHistoryAllColumns,
			statusHistoryColumnsWithDefault,
			statusHistoryColumnsWithoutDefault,
			nzDefaults,
		)

		update := updateColumns.UpdateColumnSet(
			statusHistoryAllColumns,
			statusHistoryPrimaryKeyColumns,
		)

		if updateOnConflict && len(update) == 0 {
			return errors.New("experiments: unable to upsert status_histories, could not build update column list")
		}

		conflict := conflictColumns
		if len(conflict) == 0 {
			conflict = make([]string, len(statusHistoryPrimaryKeyColumns))
			copy(conflict, statusHistoryPrimaryKeyColumns)
		}
		cache.query = buildUpsertQueryPostgres(dialect, "\"experiments\".\"status_histories\"", updateOnConflict, ret, update, conflict, insert)

		cache.valueMapping, err = queries.BindMapping(statusHistoryType, statusHistoryMapping, insert)
		if err != nil {
			return err
		}
		if len(ret) != 0 {
			cache.retMapping, err = queries.BindMapping(statusHistoryType, statusHistoryMapping, ret)
			if err != nil {
				return err
			}
		}
	}

	value := reflect.Indirect(reflect.ValueOf(o))
	vals := queries.ValuesFromMapping(value, cache.valueMapping)
	var returns []interface{}
	if len(cache.retMapping) != 0 {
		returns = queries.PtrsFromMapping(value, cache.retMapping)
	}

	if boil.IsDebug(ctx) {
		writer := boil.DebugWriterFrom(ctx)
		fmt.Fprintln(writer, cache.query)
		fmt.Fprintln(writer, vals)
	}
	if len(cache.retMapping) != 0 {
		err = exec.QueryRowContext(ctx, cache.query, vals...).Scan(returns...)
		if errors.Is(err, sql.ErrNoRows) {
			err = nil // Postgres doesn't return anything when there's no update
		}
	} else {
		_, err = exec.ExecContext(ctx, cache.query, vals...)
	}
	if err != nil {
		return errors.Wrap(err, "experiments: unable to upsert status_histories")
	}

	if !cached {
		statusHistoryUpsertCacheMut.Lock()
		statusHistoryUpsertCache[key] = cache
		statusHistoryUpsertCacheMut.Unlock()
	}

	return o.doAfterUpsertHooks(ctx, exec)
}

// Delete deletes a single StatusHistory record with an executor.
// Delete will match against the primary key column to find the record to delete.
func (o *StatusHistory) Delete(ctx context.Context, exec boil.ContextExecutor) (int64, error) {
	if o == nil {
		return 0, errors.New("experiments: no StatusHistory provided for delete")
	}

	if err := o.doBeforeDeleteHooks(ctx, exec); err != nil {
		return 0, err
	}

	args := queries.ValuesFromMapping(reflect.Indirect(reflect.ValueOf(o)), statusHistoryPrimaryKeyMapping)
	sql := "DELETE FROM \"experiments\".\"status_histories\" WHERE \"id\"=$1"

	if boil.IsDebug(ctx) {
		writer := boil.DebugWriterFrom(ctx)
		fmt.Fprintln(writer, sql)
		fmt.Fprintln(writer, args...)
	}
	result, err := exec.ExecContext(ctx, sql, args...)
	if err != nil {
		return 0, errors.Wrap(err, "experiments: unable to delete from status_histories")
	}

	rowsAff, err := result.RowsAffected()
	if err != nil {
		return 0, errors.Wrap(err, "experiments: failed to get rows affected by delete for status_histories")
	}

	if err := o.doAfterDeleteHooks(ctx, exec); err != nil {
		return 0, err
	}

	return rowsAff, nil
}

// DeleteAll deletes all matching rows.
func (q statusHistoryQuery) DeleteAll(ctx context.Context, exec boil.ContextExecutor) (int64, error) {
	if q.Query == nil {
		return 0, errors.New("experiments: no statusHistoryQuery provided for delete all")
	}

	queries.SetDelete(q.Query)

	result, err := q.Query.ExecContext(ctx, exec)
	if err != nil {
		return 0, errors.Wrap(err, "experiments: unable to delete all from status_histories")
	}

	rowsAff, err := result.RowsAffected()
	if err != nil {
		return 0, errors.Wrap(err, "experiments: failed to get rows affected by deleteall for status_histories")
	}

	return rowsAff, nil
}

// DeleteAll deletes all rows in the slice, using an executor.
func (o StatusHistorySlice) DeleteAll(ctx context.Context, exec boil.ContextExecutor) (int64, error) {
	if len(o) == 0 {
		return 0, nil
	}

	if len(statusHistoryBeforeDeleteHooks) != 0 {
		for _, obj := range o {
			if err := obj.doBeforeDeleteHooks(ctx, exec); err != nil {
				return 0, err
			}
		}
	}

	var args []interface{}
	for _, obj := range o {
		pkeyArgs := queries.ValuesFromMapping(reflect.Indirect(reflect.ValueOf(obj)), statusHistoryPrimaryKeyMapping)
		args = append(args, pkeyArgs...)
	}

	sql := "DELETE FROM \"experiments\".\"status_histories\" WHERE " +
		strmangle.WhereClauseRepeated(string(dialect.LQ), string(dialect.RQ), 1, statusHistoryPrimaryKeyColumns, len(o))

	if boil.IsDebug(ctx) {
		writer := boil.DebugWriterFrom(ctx)
		fmt.Fprintln(writer, sql)
		fmt.Fprintln(writer, args)
	}
	result, err := exec.ExecContext(ctx, sql, args...)
	if err != nil {
		return 0, errors.Wrap(err, "experiments: unable to delete all from statusHistory slice")
	}

	rowsAff, err := result.RowsAffected()
	if err != nil {
		return 0, errors.Wrap(err, "experiments: failed to get rows affected by deleteall for status_histories")
	}

	if len(statusHistoryAfterDeleteHooks) != 0 {
		for _, obj := range o {
			if err := obj.doAfterDeleteHooks(ctx, exec); err != nil {
				return 0, err
			}
		}
	}

	return rowsAff, nil
}

// Reload refetches the object from the database
// using the primary keys with an executor.
func (o *StatusHistory) Reload(ctx context.Context, exec boil.ContextExecutor) error {
	ret, err := FindStatusHistory(ctx, exec, o.ID)
	if err != nil {
		return err
	}

	*o = *ret
	return nil
}

// ReloadAll refetches every row with matching primary key column values
// and overwrites the original object slice with the newly updated slice.
func (o *StatusHistorySlice) ReloadAll(ctx context.Context, exec boil.ContextExecutor) error {
	if o == nil || len(*o) == 0 {
		return nil
	}

	slice := StatusHistorySlice{}
	var args []interface{}
	for _, obj := range *o {
		pkeyArgs := queries.ValuesFromMapping(reflect.Indirect(reflect.ValueOf(obj)), statusHistoryPrimaryKeyMapping)
		args = append(args, pkeyArgs...)
	}

	sql := "SELECT \"experiments\".\"status_histories\".* FROM \"experiments\".\"status_histories\" WHERE " +
		strmangle.WhereClauseRepeated(string(dialect.LQ), string(dialect.RQ), 1, statusHistoryPrimaryKeyColumns, len(*o))

	q := queries.Raw(sql, args...)

	err := q.Bind(ctx, exec, &slice)
	if err != nil {
		return errors.Wrap(err, "experiments: unable to reload all in StatusHistorySlice")
	}

	*o = slice

	return nil
}

// StatusHistoryExists checks if the StatusHistory row exists.
func StatusHistoryExists(ctx context.Context, exec boil.ContextExecutor, iD string) (bool, error) {
	var exists bool
	sql := "select exists(select 1 from \"experiments\".\"status_histories\" where \"id\"=$1 limit 1)"

	if boil.IsDebug(ctx) {
		writer := boil.DebugWriterFrom(ctx)
		fmt.Fprintln(writer, sql)
		fmt.Fprintln(writer, iD)
	}
	row := exec.QueryRowContext(ctx, sql, iD)

	err := row.Scan(&exists)
	if err != nil {
		return false, errors.Wrap(err, "experiments: unable to check if status_histories exists")
	}

	return exists, nil
}
