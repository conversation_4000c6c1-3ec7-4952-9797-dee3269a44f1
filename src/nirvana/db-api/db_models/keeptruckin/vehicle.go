// Code generated by SQLBoiler (https://github.com/volatiletech/sqlboiler). DO NOT EDIT.
// This file is meant to be re-generated in place and/or deleted at any time.

package keeptruckin

import (
	"context"
	"database/sql"
	"fmt"
	"reflect"
	"strconv"
	"strings"
	"sync"
	"time"

	"github.com/friendsofgo/errors"
	"github.com/volatiletech/null/v8"
	"github.com/volatiletech/sqlboiler/v4/boil"
	"github.com/volatiletech/sqlboiler/v4/queries"
	"github.com/volatiletech/sqlboiler/v4/queries/qm"
	"github.com/volatiletech/sqlboiler/v4/queries/qmhelper"
	"github.com/volatiletech/sqlboiler/v4/types"
	"github.com/volatiletech/strmangle"
)

// Vehicle is an object representing the database table.
type Vehicle struct {
	ID                 string           `boil:"id" json:"id" toml:"id" yaml:"id"`
	OrgID              string           `boil:"org_id" json:"org_id" toml:"org_id" yaml:"org_id"`
	HandleID           string           `boil:"handle_id" json:"handle_id" toml:"handle_id" yaml:"handle_id"`
	ProviderID         int              `boil:"provider_id" json:"provider_id" toml:"provider_id" yaml:"provider_id"`
	Vin                string           `boil:"vin" json:"vin" toml:"vin" yaml:"vin"`
	Make               string           `boil:"make" json:"make" toml:"make" yaml:"make"`
	Model              string           `boil:"model" json:"model" toml:"model" yaml:"model"`
	Year               string           `boil:"year" json:"year" toml:"year" yaml:"year"`
	LicensePlateNumber null.String      `boil:"license_plate_number" json:"license_plate_number,omitempty" toml:"license_plate_number" yaml:"license_plate_number,omitempty"`
	LicensePlateState  null.String      `boil:"license_plate_state" json:"license_plate_state,omitempty" toml:"license_plate_state" yaml:"license_plate_state,omitempty"`
	IsTracked          bool             `boil:"is_tracked" json:"is_tracked" toml:"is_tracked" yaml:"is_tracked"`
	Raw                types.JSON       `boil:"raw" json:"raw" toml:"raw" yaml:"raw"`
	CreatedAt          time.Time        `boil:"created_at" json:"created_at" toml:"created_at" yaml:"created_at"`
	UpdatedAt          time.Time        `boil:"updated_at" json:"updated_at" toml:"updated_at" yaml:"updated_at"`
	TSPCreatedAt       null.Time        `boil:"tsp_created_at" json:"tsp_created_at,omitempty" toml:"tsp_created_at" yaml:"tsp_created_at,omitempty"`
	TSPUpdatedAt       null.Time        `boil:"tsp_updated_at" json:"tsp_updated_at,omitempty" toml:"tsp_updated_at" yaml:"tsp_updated_at,omitempty"`
	Name               null.String      `boil:"name" json:"name,omitempty" toml:"name" yaml:"name,omitempty"`
	GroupIds           types.Int64Array `boil:"group_ids" json:"group_ids,omitempty" toml:"group_ids" yaml:"group_ids,omitempty"`
	CompanyID          string           `boil:"company_id" json:"company_id" toml:"company_id" yaml:"company_id"`

	R *vehicleR `boil:"" json:"" toml:"" yaml:""`
	L vehicleL  `boil:"-" json:"-" toml:"-" yaml:"-"`
}

var VehicleColumns = struct {
	ID                 string
	OrgID              string
	HandleID           string
	ProviderID         string
	Vin                string
	Make               string
	Model              string
	Year               string
	LicensePlateNumber string
	LicensePlateState  string
	IsTracked          string
	Raw                string
	CreatedAt          string
	UpdatedAt          string
	TSPCreatedAt       string
	TSPUpdatedAt       string
	Name               string
	GroupIds           string
	CompanyID          string
}{
	ID:                 "id",
	OrgID:              "org_id",
	HandleID:           "handle_id",
	ProviderID:         "provider_id",
	Vin:                "vin",
	Make:               "make",
	Model:              "model",
	Year:               "year",
	LicensePlateNumber: "license_plate_number",
	LicensePlateState:  "license_plate_state",
	IsTracked:          "is_tracked",
	Raw:                "raw",
	CreatedAt:          "created_at",
	UpdatedAt:          "updated_at",
	TSPCreatedAt:       "tsp_created_at",
	TSPUpdatedAt:       "tsp_updated_at",
	Name:               "name",
	GroupIds:           "group_ids",
	CompanyID:          "company_id",
}

var VehicleTableColumns = struct {
	ID                 string
	OrgID              string
	HandleID           string
	ProviderID         string
	Vin                string
	Make               string
	Model              string
	Year               string
	LicensePlateNumber string
	LicensePlateState  string
	IsTracked          string
	Raw                string
	CreatedAt          string
	UpdatedAt          string
	TSPCreatedAt       string
	TSPUpdatedAt       string
	Name               string
	GroupIds           string
	CompanyID          string
}{
	ID:                 "vehicle.id",
	OrgID:              "vehicle.org_id",
	HandleID:           "vehicle.handle_id",
	ProviderID:         "vehicle.provider_id",
	Vin:                "vehicle.vin",
	Make:               "vehicle.make",
	Model:              "vehicle.model",
	Year:               "vehicle.year",
	LicensePlateNumber: "vehicle.license_plate_number",
	LicensePlateState:  "vehicle.license_plate_state",
	IsTracked:          "vehicle.is_tracked",
	Raw:                "vehicle.raw",
	CreatedAt:          "vehicle.created_at",
	UpdatedAt:          "vehicle.updated_at",
	TSPCreatedAt:       "vehicle.tsp_created_at",
	TSPUpdatedAt:       "vehicle.tsp_updated_at",
	Name:               "vehicle.name",
	GroupIds:           "vehicle.group_ids",
	CompanyID:          "vehicle.company_id",
}

// Generated where

type whereHelpernull_String struct{ field string }

func (w whereHelpernull_String) EQ(x null.String) qm.QueryMod {
	return qmhelper.WhereNullEQ(w.field, false, x)
}
func (w whereHelpernull_String) NEQ(x null.String) qm.QueryMod {
	return qmhelper.WhereNullEQ(w.field, true, x)
}
func (w whereHelpernull_String) LT(x null.String) qm.QueryMod {
	return qmhelper.Where(w.field, qmhelper.LT, x)
}
func (w whereHelpernull_String) LTE(x null.String) qm.QueryMod {
	return qmhelper.Where(w.field, qmhelper.LTE, x)
}
func (w whereHelpernull_String) GT(x null.String) qm.QueryMod {
	return qmhelper.Where(w.field, qmhelper.GT, x)
}
func (w whereHelpernull_String) GTE(x null.String) qm.QueryMod {
	return qmhelper.Where(w.field, qmhelper.GTE, x)
}

func (w whereHelpernull_String) IsNull() qm.QueryMod    { return qmhelper.WhereIsNull(w.field) }
func (w whereHelpernull_String) IsNotNull() qm.QueryMod { return qmhelper.WhereIsNotNull(w.field) }

var VehicleWhere = struct {
	ID                 whereHelperstring
	OrgID              whereHelperstring
	HandleID           whereHelperstring
	ProviderID         whereHelperint
	Vin                whereHelperstring
	Make               whereHelperstring
	Model              whereHelperstring
	Year               whereHelperstring
	LicensePlateNumber whereHelpernull_String
	LicensePlateState  whereHelpernull_String
	IsTracked          whereHelperbool
	Raw                whereHelpertypes_JSON
	CreatedAt          whereHelpertime_Time
	UpdatedAt          whereHelpertime_Time
	TSPCreatedAt       whereHelpernull_Time
	TSPUpdatedAt       whereHelpernull_Time
	Name               whereHelpernull_String
	GroupIds           whereHelpertypes_Int64Array
	CompanyID          whereHelperstring
}{
	ID:                 whereHelperstring{field: "\"keeptruckin\".\"vehicle\".\"id\""},
	OrgID:              whereHelperstring{field: "\"keeptruckin\".\"vehicle\".\"org_id\""},
	HandleID:           whereHelperstring{field: "\"keeptruckin\".\"vehicle\".\"handle_id\""},
	ProviderID:         whereHelperint{field: "\"keeptruckin\".\"vehicle\".\"provider_id\""},
	Vin:                whereHelperstring{field: "\"keeptruckin\".\"vehicle\".\"vin\""},
	Make:               whereHelperstring{field: "\"keeptruckin\".\"vehicle\".\"make\""},
	Model:              whereHelperstring{field: "\"keeptruckin\".\"vehicle\".\"model\""},
	Year:               whereHelperstring{field: "\"keeptruckin\".\"vehicle\".\"year\""},
	LicensePlateNumber: whereHelpernull_String{field: "\"keeptruckin\".\"vehicle\".\"license_plate_number\""},
	LicensePlateState:  whereHelpernull_String{field: "\"keeptruckin\".\"vehicle\".\"license_plate_state\""},
	IsTracked:          whereHelperbool{field: "\"keeptruckin\".\"vehicle\".\"is_tracked\""},
	Raw:                whereHelpertypes_JSON{field: "\"keeptruckin\".\"vehicle\".\"raw\""},
	CreatedAt:          whereHelpertime_Time{field: "\"keeptruckin\".\"vehicle\".\"created_at\""},
	UpdatedAt:          whereHelpertime_Time{field: "\"keeptruckin\".\"vehicle\".\"updated_at\""},
	TSPCreatedAt:       whereHelpernull_Time{field: "\"keeptruckin\".\"vehicle\".\"tsp_created_at\""},
	TSPUpdatedAt:       whereHelpernull_Time{field: "\"keeptruckin\".\"vehicle\".\"tsp_updated_at\""},
	Name:               whereHelpernull_String{field: "\"keeptruckin\".\"vehicle\".\"name\""},
	GroupIds:           whereHelpertypes_Int64Array{field: "\"keeptruckin\".\"vehicle\".\"group_ids\""},
	CompanyID:          whereHelperstring{field: "\"keeptruckin\".\"vehicle\".\"company_id\""},
}

// VehicleRels is where relationship names are stored.
var VehicleRels = struct {
	Org string
}{
	Org: "Org",
}

// vehicleR is where relationships are stored.
type vehicleR struct {
	Org *Organization `boil:"Org" json:"Org" toml:"Org" yaml:"Org"`
}

// NewStruct creates a new relationship struct
func (*vehicleR) NewStruct() *vehicleR {
	return &vehicleR{}
}

// vehicleL is where Load methods for each relationship are stored.
type vehicleL struct{}

var (
	vehicleAllColumns            = []string{"id", "org_id", "handle_id", "provider_id", "vin", "make", "model", "year", "license_plate_number", "license_plate_state", "is_tracked", "raw", "created_at", "updated_at", "tsp_created_at", "tsp_updated_at", "name", "group_ids", "company_id"}
	vehicleColumnsWithoutDefault = []string{"id", "org_id", "handle_id", "provider_id", "vin", "make", "model", "year", "raw", "company_id"}
	vehicleColumnsWithDefault    = []string{"license_plate_number", "license_plate_state", "is_tracked", "created_at", "updated_at", "tsp_created_at", "tsp_updated_at", "name", "group_ids"}
	vehiclePrimaryKeyColumns     = []string{"id"}
	vehicleGeneratedColumns      = []string{}
)

type (
	// VehicleSlice is an alias for a slice of pointers to Vehicle.
	// This should almost always be used instead of []Vehicle.
	VehicleSlice []*Vehicle
	// VehicleHook is the signature for custom Vehicle hook methods
	VehicleHook func(context.Context, boil.ContextExecutor, *Vehicle) error

	vehicleQuery struct {
		*queries.Query
	}
)

// Cache for insert, update and upsert
var (
	vehicleType                 = reflect.TypeOf(&Vehicle{})
	vehicleMapping              = queries.MakeStructMapping(vehicleType)
	vehiclePrimaryKeyMapping, _ = queries.BindMapping(vehicleType, vehicleMapping, vehiclePrimaryKeyColumns)
	vehicleInsertCacheMut       sync.RWMutex
	vehicleInsertCache          = make(map[string]insertCache)
	vehicleUpdateCacheMut       sync.RWMutex
	vehicleUpdateCache          = make(map[string]updateCache)
	vehicleUpsertCacheMut       sync.RWMutex
	vehicleUpsertCache          = make(map[string]insertCache)
)

var (
	// Force time package dependency for automated UpdatedAt/CreatedAt.
	_ = time.Second
	// Force qmhelper dependency for where clause generation (which doesn't
	// always happen)
	_ = qmhelper.Where
)

var vehicleAfterSelectHooks []VehicleHook

var vehicleBeforeInsertHooks []VehicleHook
var vehicleAfterInsertHooks []VehicleHook

var vehicleBeforeUpdateHooks []VehicleHook
var vehicleAfterUpdateHooks []VehicleHook

var vehicleBeforeDeleteHooks []VehicleHook
var vehicleAfterDeleteHooks []VehicleHook

var vehicleBeforeUpsertHooks []VehicleHook
var vehicleAfterUpsertHooks []VehicleHook

// doAfterSelectHooks executes all "after Select" hooks.
func (o *Vehicle) doAfterSelectHooks(ctx context.Context, exec boil.ContextExecutor) (err error) {
	if boil.HooksAreSkipped(ctx) {
		return nil
	}

	for _, hook := range vehicleAfterSelectHooks {
		if err := hook(ctx, exec, o); err != nil {
			return err
		}
	}

	return nil
}

// doBeforeInsertHooks executes all "before insert" hooks.
func (o *Vehicle) doBeforeInsertHooks(ctx context.Context, exec boil.ContextExecutor) (err error) {
	if boil.HooksAreSkipped(ctx) {
		return nil
	}

	for _, hook := range vehicleBeforeInsertHooks {
		if err := hook(ctx, exec, o); err != nil {
			return err
		}
	}

	return nil
}

// doAfterInsertHooks executes all "after Insert" hooks.
func (o *Vehicle) doAfterInsertHooks(ctx context.Context, exec boil.ContextExecutor) (err error) {
	if boil.HooksAreSkipped(ctx) {
		return nil
	}

	for _, hook := range vehicleAfterInsertHooks {
		if err := hook(ctx, exec, o); err != nil {
			return err
		}
	}

	return nil
}

// doBeforeUpdateHooks executes all "before Update" hooks.
func (o *Vehicle) doBeforeUpdateHooks(ctx context.Context, exec boil.ContextExecutor) (err error) {
	if boil.HooksAreSkipped(ctx) {
		return nil
	}

	for _, hook := range vehicleBeforeUpdateHooks {
		if err := hook(ctx, exec, o); err != nil {
			return err
		}
	}

	return nil
}

// doAfterUpdateHooks executes all "after Update" hooks.
func (o *Vehicle) doAfterUpdateHooks(ctx context.Context, exec boil.ContextExecutor) (err error) {
	if boil.HooksAreSkipped(ctx) {
		return nil
	}

	for _, hook := range vehicleAfterUpdateHooks {
		if err := hook(ctx, exec, o); err != nil {
			return err
		}
	}

	return nil
}

// doBeforeDeleteHooks executes all "before Delete" hooks.
func (o *Vehicle) doBeforeDeleteHooks(ctx context.Context, exec boil.ContextExecutor) (err error) {
	if boil.HooksAreSkipped(ctx) {
		return nil
	}

	for _, hook := range vehicleBeforeDeleteHooks {
		if err := hook(ctx, exec, o); err != nil {
			return err
		}
	}

	return nil
}

// doAfterDeleteHooks executes all "after Delete" hooks.
func (o *Vehicle) doAfterDeleteHooks(ctx context.Context, exec boil.ContextExecutor) (err error) {
	if boil.HooksAreSkipped(ctx) {
		return nil
	}

	for _, hook := range vehicleAfterDeleteHooks {
		if err := hook(ctx, exec, o); err != nil {
			return err
		}
	}

	return nil
}

// doBeforeUpsertHooks executes all "before Upsert" hooks.
func (o *Vehicle) doBeforeUpsertHooks(ctx context.Context, exec boil.ContextExecutor) (err error) {
	if boil.HooksAreSkipped(ctx) {
		return nil
	}

	for _, hook := range vehicleBeforeUpsertHooks {
		if err := hook(ctx, exec, o); err != nil {
			return err
		}
	}

	return nil
}

// doAfterUpsertHooks executes all "after Upsert" hooks.
func (o *Vehicle) doAfterUpsertHooks(ctx context.Context, exec boil.ContextExecutor) (err error) {
	if boil.HooksAreSkipped(ctx) {
		return nil
	}

	for _, hook := range vehicleAfterUpsertHooks {
		if err := hook(ctx, exec, o); err != nil {
			return err
		}
	}

	return nil
}

// AddVehicleHook registers your hook function for all future operations.
func AddVehicleHook(hookPoint boil.HookPoint, vehicleHook VehicleHook) {
	switch hookPoint {
	case boil.AfterSelectHook:
		vehicleAfterSelectHooks = append(vehicleAfterSelectHooks, vehicleHook)
	case boil.BeforeInsertHook:
		vehicleBeforeInsertHooks = append(vehicleBeforeInsertHooks, vehicleHook)
	case boil.AfterInsertHook:
		vehicleAfterInsertHooks = append(vehicleAfterInsertHooks, vehicleHook)
	case boil.BeforeUpdateHook:
		vehicleBeforeUpdateHooks = append(vehicleBeforeUpdateHooks, vehicleHook)
	case boil.AfterUpdateHook:
		vehicleAfterUpdateHooks = append(vehicleAfterUpdateHooks, vehicleHook)
	case boil.BeforeDeleteHook:
		vehicleBeforeDeleteHooks = append(vehicleBeforeDeleteHooks, vehicleHook)
	case boil.AfterDeleteHook:
		vehicleAfterDeleteHooks = append(vehicleAfterDeleteHooks, vehicleHook)
	case boil.BeforeUpsertHook:
		vehicleBeforeUpsertHooks = append(vehicleBeforeUpsertHooks, vehicleHook)
	case boil.AfterUpsertHook:
		vehicleAfterUpsertHooks = append(vehicleAfterUpsertHooks, vehicleHook)
	}
}

// One returns a single vehicle record from the query.
func (q vehicleQuery) One(ctx context.Context, exec boil.ContextExecutor) (*Vehicle, error) {
	o := &Vehicle{}

	queries.SetLimit(q.Query, 1)

	err := q.Bind(ctx, exec, o)
	if err != nil {
		if errors.Is(err, sql.ErrNoRows) {
			return nil, sql.ErrNoRows
		}
		return nil, errors.Wrap(err, "keeptruckin: failed to execute a one query for vehicle")
	}

	if err := o.doAfterSelectHooks(ctx, exec); err != nil {
		return o, err
	}

	return o, nil
}

// All returns all Vehicle records from the query.
func (q vehicleQuery) All(ctx context.Context, exec boil.ContextExecutor) (VehicleSlice, error) {
	var o []*Vehicle

	err := q.Bind(ctx, exec, &o)
	if err != nil {
		return nil, errors.Wrap(err, "keeptruckin: failed to assign all query results to Vehicle slice")
	}

	if len(vehicleAfterSelectHooks) != 0 {
		for _, obj := range o {
			if err := obj.doAfterSelectHooks(ctx, exec); err != nil {
				return o, err
			}
		}
	}

	return o, nil
}

// Count returns the count of all Vehicle records in the query.
func (q vehicleQuery) Count(ctx context.Context, exec boil.ContextExecutor) (int64, error) {
	var count int64

	queries.SetSelect(q.Query, nil)
	queries.SetCount(q.Query)

	err := q.Query.QueryRowContext(ctx, exec).Scan(&count)
	if err != nil {
		return 0, errors.Wrap(err, "keeptruckin: failed to count vehicle rows")
	}

	return count, nil
}

// Exists checks if the row exists in the table.
func (q vehicleQuery) Exists(ctx context.Context, exec boil.ContextExecutor) (bool, error) {
	var count int64

	queries.SetSelect(q.Query, nil)
	queries.SetCount(q.Query)
	queries.SetLimit(q.Query, 1)

	err := q.Query.QueryRowContext(ctx, exec).Scan(&count)
	if err != nil {
		return false, errors.Wrap(err, "keeptruckin: failed to check if vehicle exists")
	}

	return count > 0, nil
}

// Org pointed to by the foreign key.
func (o *Vehicle) Org(mods ...qm.QueryMod) organizationQuery {
	queryMods := []qm.QueryMod{
		qm.Where("\"id\" = ?", o.OrgID),
	}

	queryMods = append(queryMods, mods...)

	return Organizations(queryMods...)
}

// LoadOrg allows an eager lookup of values, cached into the
// loaded structs of the objects. This is for an N-1 relationship.
func (vehicleL) LoadOrg(ctx context.Context, e boil.ContextExecutor, singular bool, maybeVehicle interface{}, mods queries.Applicator) error {
	var slice []*Vehicle
	var object *Vehicle

	if singular {
		object = maybeVehicle.(*Vehicle)
	} else {
		slice = *maybeVehicle.(*[]*Vehicle)
	}

	args := make([]interface{}, 0, 1)
	if singular {
		if object.R == nil {
			object.R = &vehicleR{}
		}
		args = append(args, object.OrgID)

	} else {
	Outer:
		for _, obj := range slice {
			if obj.R == nil {
				obj.R = &vehicleR{}
			}

			for _, a := range args {
				if a == obj.OrgID {
					continue Outer
				}
			}

			args = append(args, obj.OrgID)

		}
	}

	if len(args) == 0 {
		return nil
	}

	query := NewQuery(
		qm.From(`keeptruckin.organization`),
		qm.WhereIn(`keeptruckin.organization.id in ?`, args...),
	)
	if mods != nil {
		mods.Apply(query)
	}

	results, err := query.QueryContext(ctx, e)
	if err != nil {
		return errors.Wrap(err, "failed to eager load Organization")
	}

	var resultSlice []*Organization
	if err = queries.Bind(results, &resultSlice); err != nil {
		return errors.Wrap(err, "failed to bind eager loaded slice Organization")
	}

	if err = results.Close(); err != nil {
		return errors.Wrap(err, "failed to close results of eager load for organization")
	}
	if err = results.Err(); err != nil {
		return errors.Wrap(err, "error occurred during iteration of eager loaded relations for organization")
	}

	if len(vehicleAfterSelectHooks) != 0 {
		for _, obj := range resultSlice {
			if err := obj.doAfterSelectHooks(ctx, e); err != nil {
				return err
			}
		}
	}

	if len(resultSlice) == 0 {
		return nil
	}

	if singular {
		foreign := resultSlice[0]
		object.R.Org = foreign
		if foreign.R == nil {
			foreign.R = &organizationR{}
		}
		foreign.R.OrgVehicles = append(foreign.R.OrgVehicles, object)
		return nil
	}

	for _, local := range slice {
		for _, foreign := range resultSlice {
			if local.OrgID == foreign.ID {
				local.R.Org = foreign
				if foreign.R == nil {
					foreign.R = &organizationR{}
				}
				foreign.R.OrgVehicles = append(foreign.R.OrgVehicles, local)
				break
			}
		}
	}

	return nil
}

// SetOrg of the vehicle to the related item.
// Sets o.R.Org to related.
// Adds o to related.R.OrgVehicles.
func (o *Vehicle) SetOrg(ctx context.Context, exec boil.ContextExecutor, insert bool, related *Organization) error {
	var err error
	if insert {
		if err = related.Insert(ctx, exec, boil.Infer()); err != nil {
			return errors.Wrap(err, "failed to insert into foreign table")
		}
	}

	updateQuery := fmt.Sprintf(
		"UPDATE \"keeptruckin\".\"vehicle\" SET %s WHERE %s",
		strmangle.SetParamNames("\"", "\"", 1, []string{"org_id"}),
		strmangle.WhereClause("\"", "\"", 2, vehiclePrimaryKeyColumns),
	)
	values := []interface{}{related.ID, o.ID}

	if boil.IsDebug(ctx) {
		writer := boil.DebugWriterFrom(ctx)
		fmt.Fprintln(writer, updateQuery)
		fmt.Fprintln(writer, values)
	}
	if _, err = exec.ExecContext(ctx, updateQuery, values...); err != nil {
		return errors.Wrap(err, "failed to update local table")
	}

	o.OrgID = related.ID
	if o.R == nil {
		o.R = &vehicleR{
			Org: related,
		}
	} else {
		o.R.Org = related
	}

	if related.R == nil {
		related.R = &organizationR{
			OrgVehicles: VehicleSlice{o},
		}
	} else {
		related.R.OrgVehicles = append(related.R.OrgVehicles, o)
	}

	return nil
}

// Vehicles retrieves all the records using an executor.
func Vehicles(mods ...qm.QueryMod) vehicleQuery {
	mods = append(mods, qm.From("\"keeptruckin\".\"vehicle\""))
	q := NewQuery(mods...)
	if len(queries.GetSelect(q)) == 0 {
		queries.SetSelect(q, []string{"\"keeptruckin\".\"vehicle\".*"})
	}

	return vehicleQuery{q}
}

// FindVehicle retrieves a single record by ID with an executor.
// If selectCols is empty Find will return all columns.
func FindVehicle(ctx context.Context, exec boil.ContextExecutor, iD string, selectCols ...string) (*Vehicle, error) {
	vehicleObj := &Vehicle{}

	sel := "*"
	if len(selectCols) > 0 {
		sel = strings.Join(strmangle.IdentQuoteSlice(dialect.LQ, dialect.RQ, selectCols), ",")
	}
	query := fmt.Sprintf(
		"select %s from \"keeptruckin\".\"vehicle\" where \"id\"=$1", sel,
	)

	q := queries.Raw(query, iD)

	err := q.Bind(ctx, exec, vehicleObj)
	if err != nil {
		if errors.Is(err, sql.ErrNoRows) {
			return nil, sql.ErrNoRows
		}
		return nil, errors.Wrap(err, "keeptruckin: unable to select from vehicle")
	}

	if err = vehicleObj.doAfterSelectHooks(ctx, exec); err != nil {
		return vehicleObj, err
	}

	return vehicleObj, nil
}

// Insert a single record using an executor.
// See boil.Columns.InsertColumnSet documentation to understand column list inference for inserts.
func (o *Vehicle) Insert(ctx context.Context, exec boil.ContextExecutor, columns boil.Columns) error {
	if o == nil {
		return errors.New("keeptruckin: no vehicle provided for insertion")
	}

	var err error
	if !boil.TimestampsAreSkipped(ctx) {
		currTime := time.Now().In(boil.GetLocation())

		if o.CreatedAt.IsZero() {
			o.CreatedAt = currTime
		}
		if o.UpdatedAt.IsZero() {
			o.UpdatedAt = currTime
		}
	}

	if err := o.doBeforeInsertHooks(ctx, exec); err != nil {
		return err
	}

	nzDefaults := queries.NonZeroDefaultSet(vehicleColumnsWithDefault, o)

	key := makeCacheKey(columns, nzDefaults)
	vehicleInsertCacheMut.RLock()
	cache, cached := vehicleInsertCache[key]
	vehicleInsertCacheMut.RUnlock()

	if !cached {
		wl, returnColumns := columns.InsertColumnSet(
			vehicleAllColumns,
			vehicleColumnsWithDefault,
			vehicleColumnsWithoutDefault,
			nzDefaults,
		)

		cache.valueMapping, err = queries.BindMapping(vehicleType, vehicleMapping, wl)
		if err != nil {
			return err
		}
		cache.retMapping, err = queries.BindMapping(vehicleType, vehicleMapping, returnColumns)
		if err != nil {
			return err
		}
		if len(wl) != 0 {
			cache.query = fmt.Sprintf("INSERT INTO \"keeptruckin\".\"vehicle\" (\"%s\") %%sVALUES (%s)%%s", strings.Join(wl, "\",\""), strmangle.Placeholders(dialect.UseIndexPlaceholders, len(wl), 1, 1))
		} else {
			cache.query = "INSERT INTO \"keeptruckin\".\"vehicle\" %sDEFAULT VALUES%s"
		}

		var queryOutput, queryReturning string

		if len(cache.retMapping) != 0 {
			queryReturning = fmt.Sprintf(" RETURNING \"%s\"", strings.Join(returnColumns, "\",\""))
		}

		cache.query = fmt.Sprintf(cache.query, queryOutput, queryReturning)
	}

	value := reflect.Indirect(reflect.ValueOf(o))
	vals := queries.ValuesFromMapping(value, cache.valueMapping)

	if boil.IsDebug(ctx) {
		writer := boil.DebugWriterFrom(ctx)
		fmt.Fprintln(writer, cache.query)
		fmt.Fprintln(writer, vals)
	}

	if len(cache.retMapping) != 0 {
		err = exec.QueryRowContext(ctx, cache.query, vals...).Scan(queries.PtrsFromMapping(value, cache.retMapping)...)
	} else {
		_, err = exec.ExecContext(ctx, cache.query, vals...)
	}

	if err != nil {
		return errors.Wrap(err, "keeptruckin: unable to insert into vehicle")
	}

	if !cached {
		vehicleInsertCacheMut.Lock()
		vehicleInsertCache[key] = cache
		vehicleInsertCacheMut.Unlock()
	}

	return o.doAfterInsertHooks(ctx, exec)
}

// Update uses an executor to update the Vehicle.
// See boil.Columns.UpdateColumnSet documentation to understand column list inference for updates.
// Update does not automatically update the record in case of default values. Use .Reload() to refresh the records.
func (o *Vehicle) Update(ctx context.Context, exec boil.ContextExecutor, columns boil.Columns) (int64, error) {
	if !boil.TimestampsAreSkipped(ctx) {
		currTime := time.Now().In(boil.GetLocation())

		o.UpdatedAt = currTime
	}

	var err error
	if err = o.doBeforeUpdateHooks(ctx, exec); err != nil {
		return 0, err
	}
	key := makeCacheKey(columns, nil)
	vehicleUpdateCacheMut.RLock()
	cache, cached := vehicleUpdateCache[key]
	vehicleUpdateCacheMut.RUnlock()

	if !cached {
		wl := columns.UpdateColumnSet(
			vehicleAllColumns,
			vehiclePrimaryKeyColumns,
		)

		if !columns.IsWhitelist() {
			wl = strmangle.SetComplement(wl, []string{"created_at"})
		}
		if len(wl) == 0 {
			return 0, errors.New("keeptruckin: unable to update vehicle, could not build whitelist")
		}

		cache.query = fmt.Sprintf("UPDATE \"keeptruckin\".\"vehicle\" SET %s WHERE %s",
			strmangle.SetParamNames("\"", "\"", 1, wl),
			strmangle.WhereClause("\"", "\"", len(wl)+1, vehiclePrimaryKeyColumns),
		)
		cache.valueMapping, err = queries.BindMapping(vehicleType, vehicleMapping, append(wl, vehiclePrimaryKeyColumns...))
		if err != nil {
			return 0, err
		}
	}

	values := queries.ValuesFromMapping(reflect.Indirect(reflect.ValueOf(o)), cache.valueMapping)

	if boil.IsDebug(ctx) {
		writer := boil.DebugWriterFrom(ctx)
		fmt.Fprintln(writer, cache.query)
		fmt.Fprintln(writer, values)
	}
	var result sql.Result
	result, err = exec.ExecContext(ctx, cache.query, values...)
	if err != nil {
		return 0, errors.Wrap(err, "keeptruckin: unable to update vehicle row")
	}

	rowsAff, err := result.RowsAffected()
	if err != nil {
		return 0, errors.Wrap(err, "keeptruckin: failed to get rows affected by update for vehicle")
	}

	if !cached {
		vehicleUpdateCacheMut.Lock()
		vehicleUpdateCache[key] = cache
		vehicleUpdateCacheMut.Unlock()
	}

	return rowsAff, o.doAfterUpdateHooks(ctx, exec)
}

// UpdateAll updates all rows with the specified column values.
func (q vehicleQuery) UpdateAll(ctx context.Context, exec boil.ContextExecutor, cols M) (int64, error) {
	queries.SetUpdate(q.Query, cols)

	result, err := q.Query.ExecContext(ctx, exec)
	if err != nil {
		return 0, errors.Wrap(err, "keeptruckin: unable to update all for vehicle")
	}

	rowsAff, err := result.RowsAffected()
	if err != nil {
		return 0, errors.Wrap(err, "keeptruckin: unable to retrieve rows affected for vehicle")
	}

	return rowsAff, nil
}

// UpdateAll updates all rows with the specified column values, using an executor.
func (o VehicleSlice) UpdateAll(ctx context.Context, exec boil.ContextExecutor, cols M) (int64, error) {
	ln := int64(len(o))
	if ln == 0 {
		return 0, nil
	}

	if len(cols) == 0 {
		return 0, errors.New("keeptruckin: update all requires at least one column argument")
	}

	colNames := make([]string, len(cols))
	args := make([]interface{}, len(cols))

	i := 0
	for name, value := range cols {
		colNames[i] = name
		args[i] = value
		i++
	}

	// Append all of the primary key values for each column
	for _, obj := range o {
		pkeyArgs := queries.ValuesFromMapping(reflect.Indirect(reflect.ValueOf(obj)), vehiclePrimaryKeyMapping)
		args = append(args, pkeyArgs...)
	}

	sql := fmt.Sprintf("UPDATE \"keeptruckin\".\"vehicle\" SET %s WHERE %s",
		strmangle.SetParamNames("\"", "\"", 1, colNames),
		strmangle.WhereClauseRepeated(string(dialect.LQ), string(dialect.RQ), len(colNames)+1, vehiclePrimaryKeyColumns, len(o)))

	if boil.IsDebug(ctx) {
		writer := boil.DebugWriterFrom(ctx)
		fmt.Fprintln(writer, sql)
		fmt.Fprintln(writer, args...)
	}
	result, err := exec.ExecContext(ctx, sql, args...)
	if err != nil {
		return 0, errors.Wrap(err, "keeptruckin: unable to update all in vehicle slice")
	}

	rowsAff, err := result.RowsAffected()
	if err != nil {
		return 0, errors.Wrap(err, "keeptruckin: unable to retrieve rows affected all in update all vehicle")
	}
	return rowsAff, nil
}

// Upsert attempts an insert using an executor, and does an update or ignore on conflict.
// See boil.Columns documentation for how to properly use updateColumns and insertColumns.
func (o *Vehicle) Upsert(ctx context.Context, exec boil.ContextExecutor, updateOnConflict bool, conflictColumns []string, updateColumns, insertColumns boil.Columns) error {
	if o == nil {
		return errors.New("keeptruckin: no vehicle provided for upsert")
	}
	if !boil.TimestampsAreSkipped(ctx) {
		currTime := time.Now().In(boil.GetLocation())

		if o.CreatedAt.IsZero() {
			o.CreatedAt = currTime
		}
		o.UpdatedAt = currTime
	}

	if err := o.doBeforeUpsertHooks(ctx, exec); err != nil {
		return err
	}

	nzDefaults := queries.NonZeroDefaultSet(vehicleColumnsWithDefault, o)

	// Build cache key in-line uglily - mysql vs psql problems
	buf := strmangle.GetBuffer()
	if updateOnConflict {
		buf.WriteByte('t')
	} else {
		buf.WriteByte('f')
	}
	buf.WriteByte('.')
	for _, c := range conflictColumns {
		buf.WriteString(c)
	}
	buf.WriteByte('.')
	buf.WriteString(strconv.Itoa(updateColumns.Kind))
	for _, c := range updateColumns.Cols {
		buf.WriteString(c)
	}
	buf.WriteByte('.')
	buf.WriteString(strconv.Itoa(insertColumns.Kind))
	for _, c := range insertColumns.Cols {
		buf.WriteString(c)
	}
	buf.WriteByte('.')
	for _, c := range nzDefaults {
		buf.WriteString(c)
	}
	key := buf.String()
	strmangle.PutBuffer(buf)

	vehicleUpsertCacheMut.RLock()
	cache, cached := vehicleUpsertCache[key]
	vehicleUpsertCacheMut.RUnlock()

	var err error

	if !cached {
		insert, ret := insertColumns.InsertColumnSet(
			vehicleAllColumns,
			vehicleColumnsWithDefault,
			vehicleColumnsWithoutDefault,
			nzDefaults,
		)

		update := updateColumns.UpdateColumnSet(
			vehicleAllColumns,
			vehiclePrimaryKeyColumns,
		)

		if updateOnConflict && len(update) == 0 {
			return errors.New("keeptruckin: unable to upsert vehicle, could not build update column list")
		}

		conflict := conflictColumns
		if len(conflict) == 0 {
			conflict = make([]string, len(vehiclePrimaryKeyColumns))
			copy(conflict, vehiclePrimaryKeyColumns)
		}
		cache.query = buildUpsertQueryPostgres(dialect, "\"keeptruckin\".\"vehicle\"", updateOnConflict, ret, update, conflict, insert)

		cache.valueMapping, err = queries.BindMapping(vehicleType, vehicleMapping, insert)
		if err != nil {
			return err
		}
		if len(ret) != 0 {
			cache.retMapping, err = queries.BindMapping(vehicleType, vehicleMapping, ret)
			if err != nil {
				return err
			}
		}
	}

	value := reflect.Indirect(reflect.ValueOf(o))
	vals := queries.ValuesFromMapping(value, cache.valueMapping)
	var returns []interface{}
	if len(cache.retMapping) != 0 {
		returns = queries.PtrsFromMapping(value, cache.retMapping)
	}

	if boil.IsDebug(ctx) {
		writer := boil.DebugWriterFrom(ctx)
		fmt.Fprintln(writer, cache.query)
		fmt.Fprintln(writer, vals)
	}
	if len(cache.retMapping) != 0 {
		err = exec.QueryRowContext(ctx, cache.query, vals...).Scan(returns...)
		if errors.Is(err, sql.ErrNoRows) {
			err = nil // Postgres doesn't return anything when there's no update
		}
	} else {
		_, err = exec.ExecContext(ctx, cache.query, vals...)
	}
	if err != nil {
		return errors.Wrap(err, "keeptruckin: unable to upsert vehicle")
	}

	if !cached {
		vehicleUpsertCacheMut.Lock()
		vehicleUpsertCache[key] = cache
		vehicleUpsertCacheMut.Unlock()
	}

	return o.doAfterUpsertHooks(ctx, exec)
}

// Delete deletes a single Vehicle record with an executor.
// Delete will match against the primary key column to find the record to delete.
func (o *Vehicle) Delete(ctx context.Context, exec boil.ContextExecutor) (int64, error) {
	if o == nil {
		return 0, errors.New("keeptruckin: no Vehicle provided for delete")
	}

	if err := o.doBeforeDeleteHooks(ctx, exec); err != nil {
		return 0, err
	}

	args := queries.ValuesFromMapping(reflect.Indirect(reflect.ValueOf(o)), vehiclePrimaryKeyMapping)
	sql := "DELETE FROM \"keeptruckin\".\"vehicle\" WHERE \"id\"=$1"

	if boil.IsDebug(ctx) {
		writer := boil.DebugWriterFrom(ctx)
		fmt.Fprintln(writer, sql)
		fmt.Fprintln(writer, args...)
	}
	result, err := exec.ExecContext(ctx, sql, args...)
	if err != nil {
		return 0, errors.Wrap(err, "keeptruckin: unable to delete from vehicle")
	}

	rowsAff, err := result.RowsAffected()
	if err != nil {
		return 0, errors.Wrap(err, "keeptruckin: failed to get rows affected by delete for vehicle")
	}

	if err := o.doAfterDeleteHooks(ctx, exec); err != nil {
		return 0, err
	}

	return rowsAff, nil
}

// DeleteAll deletes all matching rows.
func (q vehicleQuery) DeleteAll(ctx context.Context, exec boil.ContextExecutor) (int64, error) {
	if q.Query == nil {
		return 0, errors.New("keeptruckin: no vehicleQuery provided for delete all")
	}

	queries.SetDelete(q.Query)

	result, err := q.Query.ExecContext(ctx, exec)
	if err != nil {
		return 0, errors.Wrap(err, "keeptruckin: unable to delete all from vehicle")
	}

	rowsAff, err := result.RowsAffected()
	if err != nil {
		return 0, errors.Wrap(err, "keeptruckin: failed to get rows affected by deleteall for vehicle")
	}

	return rowsAff, nil
}

// DeleteAll deletes all rows in the slice, using an executor.
func (o VehicleSlice) DeleteAll(ctx context.Context, exec boil.ContextExecutor) (int64, error) {
	if len(o) == 0 {
		return 0, nil
	}

	if len(vehicleBeforeDeleteHooks) != 0 {
		for _, obj := range o {
			if err := obj.doBeforeDeleteHooks(ctx, exec); err != nil {
				return 0, err
			}
		}
	}

	var args []interface{}
	for _, obj := range o {
		pkeyArgs := queries.ValuesFromMapping(reflect.Indirect(reflect.ValueOf(obj)), vehiclePrimaryKeyMapping)
		args = append(args, pkeyArgs...)
	}

	sql := "DELETE FROM \"keeptruckin\".\"vehicle\" WHERE " +
		strmangle.WhereClauseRepeated(string(dialect.LQ), string(dialect.RQ), 1, vehiclePrimaryKeyColumns, len(o))

	if boil.IsDebug(ctx) {
		writer := boil.DebugWriterFrom(ctx)
		fmt.Fprintln(writer, sql)
		fmt.Fprintln(writer, args)
	}
	result, err := exec.ExecContext(ctx, sql, args...)
	if err != nil {
		return 0, errors.Wrap(err, "keeptruckin: unable to delete all from vehicle slice")
	}

	rowsAff, err := result.RowsAffected()
	if err != nil {
		return 0, errors.Wrap(err, "keeptruckin: failed to get rows affected by deleteall for vehicle")
	}

	if len(vehicleAfterDeleteHooks) != 0 {
		for _, obj := range o {
			if err := obj.doAfterDeleteHooks(ctx, exec); err != nil {
				return 0, err
			}
		}
	}

	return rowsAff, nil
}

// Reload refetches the object from the database
// using the primary keys with an executor.
func (o *Vehicle) Reload(ctx context.Context, exec boil.ContextExecutor) error {
	ret, err := FindVehicle(ctx, exec, o.ID)
	if err != nil {
		return err
	}

	*o = *ret
	return nil
}

// ReloadAll refetches every row with matching primary key column values
// and overwrites the original object slice with the newly updated slice.
func (o *VehicleSlice) ReloadAll(ctx context.Context, exec boil.ContextExecutor) error {
	if o == nil || len(*o) == 0 {
		return nil
	}

	slice := VehicleSlice{}
	var args []interface{}
	for _, obj := range *o {
		pkeyArgs := queries.ValuesFromMapping(reflect.Indirect(reflect.ValueOf(obj)), vehiclePrimaryKeyMapping)
		args = append(args, pkeyArgs...)
	}

	sql := "SELECT \"keeptruckin\".\"vehicle\".* FROM \"keeptruckin\".\"vehicle\" WHERE " +
		strmangle.WhereClauseRepeated(string(dialect.LQ), string(dialect.RQ), 1, vehiclePrimaryKeyColumns, len(*o))

	q := queries.Raw(sql, args...)

	err := q.Bind(ctx, exec, &slice)
	if err != nil {
		return errors.Wrap(err, "keeptruckin: unable to reload all in VehicleSlice")
	}

	*o = slice

	return nil
}

// VehicleExists checks if the Vehicle row exists.
func VehicleExists(ctx context.Context, exec boil.ContextExecutor, iD string) (bool, error) {
	var exists bool
	sql := "select exists(select 1 from \"keeptruckin\".\"vehicle\" where \"id\"=$1 limit 1)"

	if boil.IsDebug(ctx) {
		writer := boil.DebugWriterFrom(ctx)
		fmt.Fprintln(writer, sql)
		fmt.Fprintln(writer, iD)
	}
	row := exec.QueryRowContext(ctx, sql, iD)

	err := row.Scan(&exists)
	if err != nil {
		return false, errors.Wrap(err, "keeptruckin: unable to check if vehicle exists")
	}

	return exists, nil
}
