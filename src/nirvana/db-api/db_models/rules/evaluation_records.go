// Code generated by SQLBoiler (https://github.com/volatiletech/sqlboiler). DO NOT EDIT.
// This file is meant to be re-generated in place and/or deleted at any time.

package rules

import (
	"context"
	"database/sql"
	"fmt"
	"reflect"
	"strconv"
	"strings"
	"sync"
	"time"

	"github.com/friendsofgo/errors"
	"github.com/volatiletech/null/v8"
	"github.com/volatiletech/sqlboiler/v4/boil"
	"github.com/volatiletech/sqlboiler/v4/queries"
	"github.com/volatiletech/sqlboiler/v4/queries/qm"
	"github.com/volatiletech/sqlboiler/v4/queries/qmhelper"
	"github.com/volatiletech/strmangle"
)

// EvaluationRecord is an object representing the database table.
type EvaluationRecord struct {
	ID               string      `boil:"id" json:"id" toml:"id" yaml:"id"`
	ObjectID         string      `boil:"object_id" json:"object_id" toml:"object_id" yaml:"object_id"`
	RuleDocumentPath string      `boil:"rule_document_path" json:"rule_document_path" toml:"rule_document_path" yaml:"rule_document_path"`
	RuleVersion      string      `boil:"rule_version" json:"rule_version" toml:"rule_version" yaml:"rule_version"`
	Timestamp        time.Time   `boil:"timestamp" json:"timestamp" toml:"timestamp" yaml:"timestamp"`
	Performance      null.String `boil:"performance" json:"performance,omitempty" toml:"performance" yaml:"performance,omitempty"`
	RequestPayload   null.JSON   `boil:"request_payload" json:"request_payload,omitempty" toml:"request_payload" yaml:"request_payload,omitempty"`
	ResponsePayload  null.JSON   `boil:"response_payload" json:"response_payload,omitempty" toml:"response_payload" yaml:"response_payload,omitempty"`
	ErrorMessage     null.String `boil:"error_message" json:"error_message,omitempty" toml:"error_message" yaml:"error_message,omitempty"`
	TraceData        null.JSON   `boil:"trace_data" json:"trace_data,omitempty" toml:"trace_data" yaml:"trace_data,omitempty"`
	Feedback         null.JSON   `boil:"feedback" json:"feedback,omitempty" toml:"feedback" yaml:"feedback,omitempty"`

	R *evaluationRecordR `boil:"" json:"" toml:"" yaml:""`
	L evaluationRecordL  `boil:"-" json:"-" toml:"-" yaml:"-"`
}

var EvaluationRecordColumns = struct {
	ID               string
	ObjectID         string
	RuleDocumentPath string
	RuleVersion      string
	Timestamp        string
	Performance      string
	RequestPayload   string
	ResponsePayload  string
	ErrorMessage     string
	TraceData        string
	Feedback         string
}{
	ID:               "id",
	ObjectID:         "object_id",
	RuleDocumentPath: "rule_document_path",
	RuleVersion:      "rule_version",
	Timestamp:        "timestamp",
	Performance:      "performance",
	RequestPayload:   "request_payload",
	ResponsePayload:  "response_payload",
	ErrorMessage:     "error_message",
	TraceData:        "trace_data",
	Feedback:         "feedback",
}

var EvaluationRecordTableColumns = struct {
	ID               string
	ObjectID         string
	RuleDocumentPath string
	RuleVersion      string
	Timestamp        string
	Performance      string
	RequestPayload   string
	ResponsePayload  string
	ErrorMessage     string
	TraceData        string
	Feedback         string
}{
	ID:               "evaluation_records.id",
	ObjectID:         "evaluation_records.object_id",
	RuleDocumentPath: "evaluation_records.rule_document_path",
	RuleVersion:      "evaluation_records.rule_version",
	Timestamp:        "evaluation_records.timestamp",
	Performance:      "evaluation_records.performance",
	RequestPayload:   "evaluation_records.request_payload",
	ResponsePayload:  "evaluation_records.response_payload",
	ErrorMessage:     "evaluation_records.error_message",
	TraceData:        "evaluation_records.trace_data",
	Feedback:         "evaluation_records.feedback",
}

// Generated where

type whereHelperstring struct{ field string }

func (w whereHelperstring) EQ(x string) qm.QueryMod  { return qmhelper.Where(w.field, qmhelper.EQ, x) }
func (w whereHelperstring) NEQ(x string) qm.QueryMod { return qmhelper.Where(w.field, qmhelper.NEQ, x) }
func (w whereHelperstring) LT(x string) qm.QueryMod  { return qmhelper.Where(w.field, qmhelper.LT, x) }
func (w whereHelperstring) LTE(x string) qm.QueryMod { return qmhelper.Where(w.field, qmhelper.LTE, x) }
func (w whereHelperstring) GT(x string) qm.QueryMod  { return qmhelper.Where(w.field, qmhelper.GT, x) }
func (w whereHelperstring) GTE(x string) qm.QueryMod { return qmhelper.Where(w.field, qmhelper.GTE, x) }
func (w whereHelperstring) IN(slice []string) qm.QueryMod {
	values := make([]interface{}, 0, len(slice))
	for _, value := range slice {
		values = append(values, value)
	}
	return qm.WhereIn(fmt.Sprintf("%s IN ?", w.field), values...)
}
func (w whereHelperstring) NIN(slice []string) qm.QueryMod {
	values := make([]interface{}, 0, len(slice))
	for _, value := range slice {
		values = append(values, value)
	}
	return qm.WhereNotIn(fmt.Sprintf("%s NOT IN ?", w.field), values...)
}

type whereHelpertime_Time struct{ field string }

func (w whereHelpertime_Time) EQ(x time.Time) qm.QueryMod {
	return qmhelper.Where(w.field, qmhelper.EQ, x)
}
func (w whereHelpertime_Time) NEQ(x time.Time) qm.QueryMod {
	return qmhelper.Where(w.field, qmhelper.NEQ, x)
}
func (w whereHelpertime_Time) LT(x time.Time) qm.QueryMod {
	return qmhelper.Where(w.field, qmhelper.LT, x)
}
func (w whereHelpertime_Time) LTE(x time.Time) qm.QueryMod {
	return qmhelper.Where(w.field, qmhelper.LTE, x)
}
func (w whereHelpertime_Time) GT(x time.Time) qm.QueryMod {
	return qmhelper.Where(w.field, qmhelper.GT, x)
}
func (w whereHelpertime_Time) GTE(x time.Time) qm.QueryMod {
	return qmhelper.Where(w.field, qmhelper.GTE, x)
}

type whereHelpernull_String struct{ field string }

func (w whereHelpernull_String) EQ(x null.String) qm.QueryMod {
	return qmhelper.WhereNullEQ(w.field, false, x)
}
func (w whereHelpernull_String) NEQ(x null.String) qm.QueryMod {
	return qmhelper.WhereNullEQ(w.field, true, x)
}
func (w whereHelpernull_String) LT(x null.String) qm.QueryMod {
	return qmhelper.Where(w.field, qmhelper.LT, x)
}
func (w whereHelpernull_String) LTE(x null.String) qm.QueryMod {
	return qmhelper.Where(w.field, qmhelper.LTE, x)
}
func (w whereHelpernull_String) GT(x null.String) qm.QueryMod {
	return qmhelper.Where(w.field, qmhelper.GT, x)
}
func (w whereHelpernull_String) GTE(x null.String) qm.QueryMod {
	return qmhelper.Where(w.field, qmhelper.GTE, x)
}

func (w whereHelpernull_String) IsNull() qm.QueryMod    { return qmhelper.WhereIsNull(w.field) }
func (w whereHelpernull_String) IsNotNull() qm.QueryMod { return qmhelper.WhereIsNotNull(w.field) }

type whereHelpernull_JSON struct{ field string }

func (w whereHelpernull_JSON) EQ(x null.JSON) qm.QueryMod {
	return qmhelper.WhereNullEQ(w.field, false, x)
}
func (w whereHelpernull_JSON) NEQ(x null.JSON) qm.QueryMod {
	return qmhelper.WhereNullEQ(w.field, true, x)
}
func (w whereHelpernull_JSON) LT(x null.JSON) qm.QueryMod {
	return qmhelper.Where(w.field, qmhelper.LT, x)
}
func (w whereHelpernull_JSON) LTE(x null.JSON) qm.QueryMod {
	return qmhelper.Where(w.field, qmhelper.LTE, x)
}
func (w whereHelpernull_JSON) GT(x null.JSON) qm.QueryMod {
	return qmhelper.Where(w.field, qmhelper.GT, x)
}
func (w whereHelpernull_JSON) GTE(x null.JSON) qm.QueryMod {
	return qmhelper.Where(w.field, qmhelper.GTE, x)
}

func (w whereHelpernull_JSON) IsNull() qm.QueryMod    { return qmhelper.WhereIsNull(w.field) }
func (w whereHelpernull_JSON) IsNotNull() qm.QueryMod { return qmhelper.WhereIsNotNull(w.field) }

var EvaluationRecordWhere = struct {
	ID               whereHelperstring
	ObjectID         whereHelperstring
	RuleDocumentPath whereHelperstring
	RuleVersion      whereHelperstring
	Timestamp        whereHelpertime_Time
	Performance      whereHelpernull_String
	RequestPayload   whereHelpernull_JSON
	ResponsePayload  whereHelpernull_JSON
	ErrorMessage     whereHelpernull_String
	TraceData        whereHelpernull_JSON
	Feedback         whereHelpernull_JSON
}{
	ID:               whereHelperstring{field: "\"rules\".\"evaluation_records\".\"id\""},
	ObjectID:         whereHelperstring{field: "\"rules\".\"evaluation_records\".\"object_id\""},
	RuleDocumentPath: whereHelperstring{field: "\"rules\".\"evaluation_records\".\"rule_document_path\""},
	RuleVersion:      whereHelperstring{field: "\"rules\".\"evaluation_records\".\"rule_version\""},
	Timestamp:        whereHelpertime_Time{field: "\"rules\".\"evaluation_records\".\"timestamp\""},
	Performance:      whereHelpernull_String{field: "\"rules\".\"evaluation_records\".\"performance\""},
	RequestPayload:   whereHelpernull_JSON{field: "\"rules\".\"evaluation_records\".\"request_payload\""},
	ResponsePayload:  whereHelpernull_JSON{field: "\"rules\".\"evaluation_records\".\"response_payload\""},
	ErrorMessage:     whereHelpernull_String{field: "\"rules\".\"evaluation_records\".\"error_message\""},
	TraceData:        whereHelpernull_JSON{field: "\"rules\".\"evaluation_records\".\"trace_data\""},
	Feedback:         whereHelpernull_JSON{field: "\"rules\".\"evaluation_records\".\"feedback\""},
}

// EvaluationRecordRels is where relationship names are stored.
var EvaluationRecordRels = struct {
}{}

// evaluationRecordR is where relationships are stored.
type evaluationRecordR struct {
}

// NewStruct creates a new relationship struct
func (*evaluationRecordR) NewStruct() *evaluationRecordR {
	return &evaluationRecordR{}
}

// evaluationRecordL is where Load methods for each relationship are stored.
type evaluationRecordL struct{}

var (
	evaluationRecordAllColumns            = []string{"id", "object_id", "rule_document_path", "rule_version", "timestamp", "performance", "request_payload", "response_payload", "error_message", "trace_data", "feedback"}
	evaluationRecordColumnsWithoutDefault = []string{"id", "object_id", "rule_document_path", "rule_version", "timestamp"}
	evaluationRecordColumnsWithDefault    = []string{"performance", "request_payload", "response_payload", "error_message", "trace_data", "feedback"}
	evaluationRecordPrimaryKeyColumns     = []string{"id"}
	evaluationRecordGeneratedColumns      = []string{}
)

type (
	// EvaluationRecordSlice is an alias for a slice of pointers to EvaluationRecord.
	// This should almost always be used instead of []EvaluationRecord.
	EvaluationRecordSlice []*EvaluationRecord
	// EvaluationRecordHook is the signature for custom EvaluationRecord hook methods
	EvaluationRecordHook func(context.Context, boil.ContextExecutor, *EvaluationRecord) error

	evaluationRecordQuery struct {
		*queries.Query
	}
)

// Cache for insert, update and upsert
var (
	evaluationRecordType                 = reflect.TypeOf(&EvaluationRecord{})
	evaluationRecordMapping              = queries.MakeStructMapping(evaluationRecordType)
	evaluationRecordPrimaryKeyMapping, _ = queries.BindMapping(evaluationRecordType, evaluationRecordMapping, evaluationRecordPrimaryKeyColumns)
	evaluationRecordInsertCacheMut       sync.RWMutex
	evaluationRecordInsertCache          = make(map[string]insertCache)
	evaluationRecordUpdateCacheMut       sync.RWMutex
	evaluationRecordUpdateCache          = make(map[string]updateCache)
	evaluationRecordUpsertCacheMut       sync.RWMutex
	evaluationRecordUpsertCache          = make(map[string]insertCache)
)

var (
	// Force time package dependency for automated UpdatedAt/CreatedAt.
	_ = time.Second
	// Force qmhelper dependency for where clause generation (which doesn't
	// always happen)
	_ = qmhelper.Where
)

var evaluationRecordAfterSelectHooks []EvaluationRecordHook

var evaluationRecordBeforeInsertHooks []EvaluationRecordHook
var evaluationRecordAfterInsertHooks []EvaluationRecordHook

var evaluationRecordBeforeUpdateHooks []EvaluationRecordHook
var evaluationRecordAfterUpdateHooks []EvaluationRecordHook

var evaluationRecordBeforeDeleteHooks []EvaluationRecordHook
var evaluationRecordAfterDeleteHooks []EvaluationRecordHook

var evaluationRecordBeforeUpsertHooks []EvaluationRecordHook
var evaluationRecordAfterUpsertHooks []EvaluationRecordHook

// doAfterSelectHooks executes all "after Select" hooks.
func (o *EvaluationRecord) doAfterSelectHooks(ctx context.Context, exec boil.ContextExecutor) (err error) {
	if boil.HooksAreSkipped(ctx) {
		return nil
	}

	for _, hook := range evaluationRecordAfterSelectHooks {
		if err := hook(ctx, exec, o); err != nil {
			return err
		}
	}

	return nil
}

// doBeforeInsertHooks executes all "before insert" hooks.
func (o *EvaluationRecord) doBeforeInsertHooks(ctx context.Context, exec boil.ContextExecutor) (err error) {
	if boil.HooksAreSkipped(ctx) {
		return nil
	}

	for _, hook := range evaluationRecordBeforeInsertHooks {
		if err := hook(ctx, exec, o); err != nil {
			return err
		}
	}

	return nil
}

// doAfterInsertHooks executes all "after Insert" hooks.
func (o *EvaluationRecord) doAfterInsertHooks(ctx context.Context, exec boil.ContextExecutor) (err error) {
	if boil.HooksAreSkipped(ctx) {
		return nil
	}

	for _, hook := range evaluationRecordAfterInsertHooks {
		if err := hook(ctx, exec, o); err != nil {
			return err
		}
	}

	return nil
}

// doBeforeUpdateHooks executes all "before Update" hooks.
func (o *EvaluationRecord) doBeforeUpdateHooks(ctx context.Context, exec boil.ContextExecutor) (err error) {
	if boil.HooksAreSkipped(ctx) {
		return nil
	}

	for _, hook := range evaluationRecordBeforeUpdateHooks {
		if err := hook(ctx, exec, o); err != nil {
			return err
		}
	}

	return nil
}

// doAfterUpdateHooks executes all "after Update" hooks.
func (o *EvaluationRecord) doAfterUpdateHooks(ctx context.Context, exec boil.ContextExecutor) (err error) {
	if boil.HooksAreSkipped(ctx) {
		return nil
	}

	for _, hook := range evaluationRecordAfterUpdateHooks {
		if err := hook(ctx, exec, o); err != nil {
			return err
		}
	}

	return nil
}

// doBeforeDeleteHooks executes all "before Delete" hooks.
func (o *EvaluationRecord) doBeforeDeleteHooks(ctx context.Context, exec boil.ContextExecutor) (err error) {
	if boil.HooksAreSkipped(ctx) {
		return nil
	}

	for _, hook := range evaluationRecordBeforeDeleteHooks {
		if err := hook(ctx, exec, o); err != nil {
			return err
		}
	}

	return nil
}

// doAfterDeleteHooks executes all "after Delete" hooks.
func (o *EvaluationRecord) doAfterDeleteHooks(ctx context.Context, exec boil.ContextExecutor) (err error) {
	if boil.HooksAreSkipped(ctx) {
		return nil
	}

	for _, hook := range evaluationRecordAfterDeleteHooks {
		if err := hook(ctx, exec, o); err != nil {
			return err
		}
	}

	return nil
}

// doBeforeUpsertHooks executes all "before Upsert" hooks.
func (o *EvaluationRecord) doBeforeUpsertHooks(ctx context.Context, exec boil.ContextExecutor) (err error) {
	if boil.HooksAreSkipped(ctx) {
		return nil
	}

	for _, hook := range evaluationRecordBeforeUpsertHooks {
		if err := hook(ctx, exec, o); err != nil {
			return err
		}
	}

	return nil
}

// doAfterUpsertHooks executes all "after Upsert" hooks.
func (o *EvaluationRecord) doAfterUpsertHooks(ctx context.Context, exec boil.ContextExecutor) (err error) {
	if boil.HooksAreSkipped(ctx) {
		return nil
	}

	for _, hook := range evaluationRecordAfterUpsertHooks {
		if err := hook(ctx, exec, o); err != nil {
			return err
		}
	}

	return nil
}

// AddEvaluationRecordHook registers your hook function for all future operations.
func AddEvaluationRecordHook(hookPoint boil.HookPoint, evaluationRecordHook EvaluationRecordHook) {
	switch hookPoint {
	case boil.AfterSelectHook:
		evaluationRecordAfterSelectHooks = append(evaluationRecordAfterSelectHooks, evaluationRecordHook)
	case boil.BeforeInsertHook:
		evaluationRecordBeforeInsertHooks = append(evaluationRecordBeforeInsertHooks, evaluationRecordHook)
	case boil.AfterInsertHook:
		evaluationRecordAfterInsertHooks = append(evaluationRecordAfterInsertHooks, evaluationRecordHook)
	case boil.BeforeUpdateHook:
		evaluationRecordBeforeUpdateHooks = append(evaluationRecordBeforeUpdateHooks, evaluationRecordHook)
	case boil.AfterUpdateHook:
		evaluationRecordAfterUpdateHooks = append(evaluationRecordAfterUpdateHooks, evaluationRecordHook)
	case boil.BeforeDeleteHook:
		evaluationRecordBeforeDeleteHooks = append(evaluationRecordBeforeDeleteHooks, evaluationRecordHook)
	case boil.AfterDeleteHook:
		evaluationRecordAfterDeleteHooks = append(evaluationRecordAfterDeleteHooks, evaluationRecordHook)
	case boil.BeforeUpsertHook:
		evaluationRecordBeforeUpsertHooks = append(evaluationRecordBeforeUpsertHooks, evaluationRecordHook)
	case boil.AfterUpsertHook:
		evaluationRecordAfterUpsertHooks = append(evaluationRecordAfterUpsertHooks, evaluationRecordHook)
	}
}

// One returns a single evaluationRecord record from the query.
func (q evaluationRecordQuery) One(ctx context.Context, exec boil.ContextExecutor) (*EvaluationRecord, error) {
	o := &EvaluationRecord{}

	queries.SetLimit(q.Query, 1)

	err := q.Bind(ctx, exec, o)
	if err != nil {
		if errors.Is(err, sql.ErrNoRows) {
			return nil, sql.ErrNoRows
		}
		return nil, errors.Wrap(err, "rules: failed to execute a one query for evaluation_records")
	}

	if err := o.doAfterSelectHooks(ctx, exec); err != nil {
		return o, err
	}

	return o, nil
}

// All returns all EvaluationRecord records from the query.
func (q evaluationRecordQuery) All(ctx context.Context, exec boil.ContextExecutor) (EvaluationRecordSlice, error) {
	var o []*EvaluationRecord

	err := q.Bind(ctx, exec, &o)
	if err != nil {
		return nil, errors.Wrap(err, "rules: failed to assign all query results to EvaluationRecord slice")
	}

	if len(evaluationRecordAfterSelectHooks) != 0 {
		for _, obj := range o {
			if err := obj.doAfterSelectHooks(ctx, exec); err != nil {
				return o, err
			}
		}
	}

	return o, nil
}

// Count returns the count of all EvaluationRecord records in the query.
func (q evaluationRecordQuery) Count(ctx context.Context, exec boil.ContextExecutor) (int64, error) {
	var count int64

	queries.SetSelect(q.Query, nil)
	queries.SetCount(q.Query)

	err := q.Query.QueryRowContext(ctx, exec).Scan(&count)
	if err != nil {
		return 0, errors.Wrap(err, "rules: failed to count evaluation_records rows")
	}

	return count, nil
}

// Exists checks if the row exists in the table.
func (q evaluationRecordQuery) Exists(ctx context.Context, exec boil.ContextExecutor) (bool, error) {
	var count int64

	queries.SetSelect(q.Query, nil)
	queries.SetCount(q.Query)
	queries.SetLimit(q.Query, 1)

	err := q.Query.QueryRowContext(ctx, exec).Scan(&count)
	if err != nil {
		return false, errors.Wrap(err, "rules: failed to check if evaluation_records exists")
	}

	return count > 0, nil
}

// EvaluationRecords retrieves all the records using an executor.
func EvaluationRecords(mods ...qm.QueryMod) evaluationRecordQuery {
	mods = append(mods, qm.From("\"rules\".\"evaluation_records\""))
	q := NewQuery(mods...)
	if len(queries.GetSelect(q)) == 0 {
		queries.SetSelect(q, []string{"\"rules\".\"evaluation_records\".*"})
	}

	return evaluationRecordQuery{q}
}

// FindEvaluationRecord retrieves a single record by ID with an executor.
// If selectCols is empty Find will return all columns.
func FindEvaluationRecord(ctx context.Context, exec boil.ContextExecutor, iD string, selectCols ...string) (*EvaluationRecord, error) {
	evaluationRecordObj := &EvaluationRecord{}

	sel := "*"
	if len(selectCols) > 0 {
		sel = strings.Join(strmangle.IdentQuoteSlice(dialect.LQ, dialect.RQ, selectCols), ",")
	}
	query := fmt.Sprintf(
		"select %s from \"rules\".\"evaluation_records\" where \"id\"=$1", sel,
	)

	q := queries.Raw(query, iD)

	err := q.Bind(ctx, exec, evaluationRecordObj)
	if err != nil {
		if errors.Is(err, sql.ErrNoRows) {
			return nil, sql.ErrNoRows
		}
		return nil, errors.Wrap(err, "rules: unable to select from evaluation_records")
	}

	if err = evaluationRecordObj.doAfterSelectHooks(ctx, exec); err != nil {
		return evaluationRecordObj, err
	}

	return evaluationRecordObj, nil
}

// Insert a single record using an executor.
// See boil.Columns.InsertColumnSet documentation to understand column list inference for inserts.
func (o *EvaluationRecord) Insert(ctx context.Context, exec boil.ContextExecutor, columns boil.Columns) error {
	if o == nil {
		return errors.New("rules: no evaluation_records provided for insertion")
	}

	var err error

	if err := o.doBeforeInsertHooks(ctx, exec); err != nil {
		return err
	}

	nzDefaults := queries.NonZeroDefaultSet(evaluationRecordColumnsWithDefault, o)

	key := makeCacheKey(columns, nzDefaults)
	evaluationRecordInsertCacheMut.RLock()
	cache, cached := evaluationRecordInsertCache[key]
	evaluationRecordInsertCacheMut.RUnlock()

	if !cached {
		wl, returnColumns := columns.InsertColumnSet(
			evaluationRecordAllColumns,
			evaluationRecordColumnsWithDefault,
			evaluationRecordColumnsWithoutDefault,
			nzDefaults,
		)

		cache.valueMapping, err = queries.BindMapping(evaluationRecordType, evaluationRecordMapping, wl)
		if err != nil {
			return err
		}
		cache.retMapping, err = queries.BindMapping(evaluationRecordType, evaluationRecordMapping, returnColumns)
		if err != nil {
			return err
		}
		if len(wl) != 0 {
			cache.query = fmt.Sprintf("INSERT INTO \"rules\".\"evaluation_records\" (\"%s\") %%sVALUES (%s)%%s", strings.Join(wl, "\",\""), strmangle.Placeholders(dialect.UseIndexPlaceholders, len(wl), 1, 1))
		} else {
			cache.query = "INSERT INTO \"rules\".\"evaluation_records\" %sDEFAULT VALUES%s"
		}

		var queryOutput, queryReturning string

		if len(cache.retMapping) != 0 {
			queryReturning = fmt.Sprintf(" RETURNING \"%s\"", strings.Join(returnColumns, "\",\""))
		}

		cache.query = fmt.Sprintf(cache.query, queryOutput, queryReturning)
	}

	value := reflect.Indirect(reflect.ValueOf(o))
	vals := queries.ValuesFromMapping(value, cache.valueMapping)

	if boil.IsDebug(ctx) {
		writer := boil.DebugWriterFrom(ctx)
		fmt.Fprintln(writer, cache.query)
		fmt.Fprintln(writer, vals)
	}

	if len(cache.retMapping) != 0 {
		err = exec.QueryRowContext(ctx, cache.query, vals...).Scan(queries.PtrsFromMapping(value, cache.retMapping)...)
	} else {
		_, err = exec.ExecContext(ctx, cache.query, vals...)
	}

	if err != nil {
		return errors.Wrap(err, "rules: unable to insert into evaluation_records")
	}

	if !cached {
		evaluationRecordInsertCacheMut.Lock()
		evaluationRecordInsertCache[key] = cache
		evaluationRecordInsertCacheMut.Unlock()
	}

	return o.doAfterInsertHooks(ctx, exec)
}

// Update uses an executor to update the EvaluationRecord.
// See boil.Columns.UpdateColumnSet documentation to understand column list inference for updates.
// Update does not automatically update the record in case of default values. Use .Reload() to refresh the records.
func (o *EvaluationRecord) Update(ctx context.Context, exec boil.ContextExecutor, columns boil.Columns) (int64, error) {
	var err error
	if err = o.doBeforeUpdateHooks(ctx, exec); err != nil {
		return 0, err
	}
	key := makeCacheKey(columns, nil)
	evaluationRecordUpdateCacheMut.RLock()
	cache, cached := evaluationRecordUpdateCache[key]
	evaluationRecordUpdateCacheMut.RUnlock()

	if !cached {
		wl := columns.UpdateColumnSet(
			evaluationRecordAllColumns,
			evaluationRecordPrimaryKeyColumns,
		)

		if !columns.IsWhitelist() {
			wl = strmangle.SetComplement(wl, []string{"created_at"})
		}
		if len(wl) == 0 {
			return 0, errors.New("rules: unable to update evaluation_records, could not build whitelist")
		}

		cache.query = fmt.Sprintf("UPDATE \"rules\".\"evaluation_records\" SET %s WHERE %s",
			strmangle.SetParamNames("\"", "\"", 1, wl),
			strmangle.WhereClause("\"", "\"", len(wl)+1, evaluationRecordPrimaryKeyColumns),
		)
		cache.valueMapping, err = queries.BindMapping(evaluationRecordType, evaluationRecordMapping, append(wl, evaluationRecordPrimaryKeyColumns...))
		if err != nil {
			return 0, err
		}
	}

	values := queries.ValuesFromMapping(reflect.Indirect(reflect.ValueOf(o)), cache.valueMapping)

	if boil.IsDebug(ctx) {
		writer := boil.DebugWriterFrom(ctx)
		fmt.Fprintln(writer, cache.query)
		fmt.Fprintln(writer, values)
	}
	var result sql.Result
	result, err = exec.ExecContext(ctx, cache.query, values...)
	if err != nil {
		return 0, errors.Wrap(err, "rules: unable to update evaluation_records row")
	}

	rowsAff, err := result.RowsAffected()
	if err != nil {
		return 0, errors.Wrap(err, "rules: failed to get rows affected by update for evaluation_records")
	}

	if !cached {
		evaluationRecordUpdateCacheMut.Lock()
		evaluationRecordUpdateCache[key] = cache
		evaluationRecordUpdateCacheMut.Unlock()
	}

	return rowsAff, o.doAfterUpdateHooks(ctx, exec)
}

// UpdateAll updates all rows with the specified column values.
func (q evaluationRecordQuery) UpdateAll(ctx context.Context, exec boil.ContextExecutor, cols M) (int64, error) {
	queries.SetUpdate(q.Query, cols)

	result, err := q.Query.ExecContext(ctx, exec)
	if err != nil {
		return 0, errors.Wrap(err, "rules: unable to update all for evaluation_records")
	}

	rowsAff, err := result.RowsAffected()
	if err != nil {
		return 0, errors.Wrap(err, "rules: unable to retrieve rows affected for evaluation_records")
	}

	return rowsAff, nil
}

// UpdateAll updates all rows with the specified column values, using an executor.
func (o EvaluationRecordSlice) UpdateAll(ctx context.Context, exec boil.ContextExecutor, cols M) (int64, error) {
	ln := int64(len(o))
	if ln == 0 {
		return 0, nil
	}

	if len(cols) == 0 {
		return 0, errors.New("rules: update all requires at least one column argument")
	}

	colNames := make([]string, len(cols))
	args := make([]interface{}, len(cols))

	i := 0
	for name, value := range cols {
		colNames[i] = name
		args[i] = value
		i++
	}

	// Append all of the primary key values for each column
	for _, obj := range o {
		pkeyArgs := queries.ValuesFromMapping(reflect.Indirect(reflect.ValueOf(obj)), evaluationRecordPrimaryKeyMapping)
		args = append(args, pkeyArgs...)
	}

	sql := fmt.Sprintf("UPDATE \"rules\".\"evaluation_records\" SET %s WHERE %s",
		strmangle.SetParamNames("\"", "\"", 1, colNames),
		strmangle.WhereClauseRepeated(string(dialect.LQ), string(dialect.RQ), len(colNames)+1, evaluationRecordPrimaryKeyColumns, len(o)))

	if boil.IsDebug(ctx) {
		writer := boil.DebugWriterFrom(ctx)
		fmt.Fprintln(writer, sql)
		fmt.Fprintln(writer, args...)
	}
	result, err := exec.ExecContext(ctx, sql, args...)
	if err != nil {
		return 0, errors.Wrap(err, "rules: unable to update all in evaluationRecord slice")
	}

	rowsAff, err := result.RowsAffected()
	if err != nil {
		return 0, errors.Wrap(err, "rules: unable to retrieve rows affected all in update all evaluationRecord")
	}
	return rowsAff, nil
}

// Upsert attempts an insert using an executor, and does an update or ignore on conflict.
// See boil.Columns documentation for how to properly use updateColumns and insertColumns.
func (o *EvaluationRecord) Upsert(ctx context.Context, exec boil.ContextExecutor, updateOnConflict bool, conflictColumns []string, updateColumns, insertColumns boil.Columns) error {
	if o == nil {
		return errors.New("rules: no evaluation_records provided for upsert")
	}

	if err := o.doBeforeUpsertHooks(ctx, exec); err != nil {
		return err
	}

	nzDefaults := queries.NonZeroDefaultSet(evaluationRecordColumnsWithDefault, o)

	// Build cache key in-line uglily - mysql vs psql problems
	buf := strmangle.GetBuffer()
	if updateOnConflict {
		buf.WriteByte('t')
	} else {
		buf.WriteByte('f')
	}
	buf.WriteByte('.')
	for _, c := range conflictColumns {
		buf.WriteString(c)
	}
	buf.WriteByte('.')
	buf.WriteString(strconv.Itoa(updateColumns.Kind))
	for _, c := range updateColumns.Cols {
		buf.WriteString(c)
	}
	buf.WriteByte('.')
	buf.WriteString(strconv.Itoa(insertColumns.Kind))
	for _, c := range insertColumns.Cols {
		buf.WriteString(c)
	}
	buf.WriteByte('.')
	for _, c := range nzDefaults {
		buf.WriteString(c)
	}
	key := buf.String()
	strmangle.PutBuffer(buf)

	evaluationRecordUpsertCacheMut.RLock()
	cache, cached := evaluationRecordUpsertCache[key]
	evaluationRecordUpsertCacheMut.RUnlock()

	var err error

	if !cached {
		insert, ret := insertColumns.InsertColumnSet(
			evaluationRecordAllColumns,
			evaluationRecordColumnsWithDefault,
			evaluationRecordColumnsWithoutDefault,
			nzDefaults,
		)

		update := updateColumns.UpdateColumnSet(
			evaluationRecordAllColumns,
			evaluationRecordPrimaryKeyColumns,
		)

		if updateOnConflict && len(update) == 0 {
			return errors.New("rules: unable to upsert evaluation_records, could not build update column list")
		}

		conflict := conflictColumns
		if len(conflict) == 0 {
			conflict = make([]string, len(evaluationRecordPrimaryKeyColumns))
			copy(conflict, evaluationRecordPrimaryKeyColumns)
		}
		cache.query = buildUpsertQueryPostgres(dialect, "\"rules\".\"evaluation_records\"", updateOnConflict, ret, update, conflict, insert)

		cache.valueMapping, err = queries.BindMapping(evaluationRecordType, evaluationRecordMapping, insert)
		if err != nil {
			return err
		}
		if len(ret) != 0 {
			cache.retMapping, err = queries.BindMapping(evaluationRecordType, evaluationRecordMapping, ret)
			if err != nil {
				return err
			}
		}
	}

	value := reflect.Indirect(reflect.ValueOf(o))
	vals := queries.ValuesFromMapping(value, cache.valueMapping)
	var returns []interface{}
	if len(cache.retMapping) != 0 {
		returns = queries.PtrsFromMapping(value, cache.retMapping)
	}

	if boil.IsDebug(ctx) {
		writer := boil.DebugWriterFrom(ctx)
		fmt.Fprintln(writer, cache.query)
		fmt.Fprintln(writer, vals)
	}
	if len(cache.retMapping) != 0 {
		err = exec.QueryRowContext(ctx, cache.query, vals...).Scan(returns...)
		if errors.Is(err, sql.ErrNoRows) {
			err = nil // Postgres doesn't return anything when there's no update
		}
	} else {
		_, err = exec.ExecContext(ctx, cache.query, vals...)
	}
	if err != nil {
		return errors.Wrap(err, "rules: unable to upsert evaluation_records")
	}

	if !cached {
		evaluationRecordUpsertCacheMut.Lock()
		evaluationRecordUpsertCache[key] = cache
		evaluationRecordUpsertCacheMut.Unlock()
	}

	return o.doAfterUpsertHooks(ctx, exec)
}

// Delete deletes a single EvaluationRecord record with an executor.
// Delete will match against the primary key column to find the record to delete.
func (o *EvaluationRecord) Delete(ctx context.Context, exec boil.ContextExecutor) (int64, error) {
	if o == nil {
		return 0, errors.New("rules: no EvaluationRecord provided for delete")
	}

	if err := o.doBeforeDeleteHooks(ctx, exec); err != nil {
		return 0, err
	}

	args := queries.ValuesFromMapping(reflect.Indirect(reflect.ValueOf(o)), evaluationRecordPrimaryKeyMapping)
	sql := "DELETE FROM \"rules\".\"evaluation_records\" WHERE \"id\"=$1"

	if boil.IsDebug(ctx) {
		writer := boil.DebugWriterFrom(ctx)
		fmt.Fprintln(writer, sql)
		fmt.Fprintln(writer, args...)
	}
	result, err := exec.ExecContext(ctx, sql, args...)
	if err != nil {
		return 0, errors.Wrap(err, "rules: unable to delete from evaluation_records")
	}

	rowsAff, err := result.RowsAffected()
	if err != nil {
		return 0, errors.Wrap(err, "rules: failed to get rows affected by delete for evaluation_records")
	}

	if err := o.doAfterDeleteHooks(ctx, exec); err != nil {
		return 0, err
	}

	return rowsAff, nil
}

// DeleteAll deletes all matching rows.
func (q evaluationRecordQuery) DeleteAll(ctx context.Context, exec boil.ContextExecutor) (int64, error) {
	if q.Query == nil {
		return 0, errors.New("rules: no evaluationRecordQuery provided for delete all")
	}

	queries.SetDelete(q.Query)

	result, err := q.Query.ExecContext(ctx, exec)
	if err != nil {
		return 0, errors.Wrap(err, "rules: unable to delete all from evaluation_records")
	}

	rowsAff, err := result.RowsAffected()
	if err != nil {
		return 0, errors.Wrap(err, "rules: failed to get rows affected by deleteall for evaluation_records")
	}

	return rowsAff, nil
}

// DeleteAll deletes all rows in the slice, using an executor.
func (o EvaluationRecordSlice) DeleteAll(ctx context.Context, exec boil.ContextExecutor) (int64, error) {
	if len(o) == 0 {
		return 0, nil
	}

	if len(evaluationRecordBeforeDeleteHooks) != 0 {
		for _, obj := range o {
			if err := obj.doBeforeDeleteHooks(ctx, exec); err != nil {
				return 0, err
			}
		}
	}

	var args []interface{}
	for _, obj := range o {
		pkeyArgs := queries.ValuesFromMapping(reflect.Indirect(reflect.ValueOf(obj)), evaluationRecordPrimaryKeyMapping)
		args = append(args, pkeyArgs...)
	}

	sql := "DELETE FROM \"rules\".\"evaluation_records\" WHERE " +
		strmangle.WhereClauseRepeated(string(dialect.LQ), string(dialect.RQ), 1, evaluationRecordPrimaryKeyColumns, len(o))

	if boil.IsDebug(ctx) {
		writer := boil.DebugWriterFrom(ctx)
		fmt.Fprintln(writer, sql)
		fmt.Fprintln(writer, args)
	}
	result, err := exec.ExecContext(ctx, sql, args...)
	if err != nil {
		return 0, errors.Wrap(err, "rules: unable to delete all from evaluationRecord slice")
	}

	rowsAff, err := result.RowsAffected()
	if err != nil {
		return 0, errors.Wrap(err, "rules: failed to get rows affected by deleteall for evaluation_records")
	}

	if len(evaluationRecordAfterDeleteHooks) != 0 {
		for _, obj := range o {
			if err := obj.doAfterDeleteHooks(ctx, exec); err != nil {
				return 0, err
			}
		}
	}

	return rowsAff, nil
}

// Reload refetches the object from the database
// using the primary keys with an executor.
func (o *EvaluationRecord) Reload(ctx context.Context, exec boil.ContextExecutor) error {
	ret, err := FindEvaluationRecord(ctx, exec, o.ID)
	if err != nil {
		return err
	}

	*o = *ret
	return nil
}

// ReloadAll refetches every row with matching primary key column values
// and overwrites the original object slice with the newly updated slice.
func (o *EvaluationRecordSlice) ReloadAll(ctx context.Context, exec boil.ContextExecutor) error {
	if o == nil || len(*o) == 0 {
		return nil
	}

	slice := EvaluationRecordSlice{}
	var args []interface{}
	for _, obj := range *o {
		pkeyArgs := queries.ValuesFromMapping(reflect.Indirect(reflect.ValueOf(obj)), evaluationRecordPrimaryKeyMapping)
		args = append(args, pkeyArgs...)
	}

	sql := "SELECT \"rules\".\"evaluation_records\".* FROM \"rules\".\"evaluation_records\" WHERE " +
		strmangle.WhereClauseRepeated(string(dialect.LQ), string(dialect.RQ), 1, evaluationRecordPrimaryKeyColumns, len(*o))

	q := queries.Raw(sql, args...)

	err := q.Bind(ctx, exec, &slice)
	if err != nil {
		return errors.Wrap(err, "rules: unable to reload all in EvaluationRecordSlice")
	}

	*o = slice

	return nil
}

// EvaluationRecordExists checks if the EvaluationRecord row exists.
func EvaluationRecordExists(ctx context.Context, exec boil.ContextExecutor, iD string) (bool, error) {
	var exists bool
	sql := "select exists(select 1 from \"rules\".\"evaluation_records\" where \"id\"=$1 limit 1)"

	if boil.IsDebug(ctx) {
		writer := boil.DebugWriterFrom(ctx)
		fmt.Fprintln(writer, sql)
		fmt.Fprintln(writer, iD)
	}
	row := exec.QueryRowContext(ctx, sql, iD)

	err := row.Scan(&exists)
	if err != nil {
		return false, errors.Wrap(err, "rules: unable to check if evaluation_records exists")
	}

	return exists, nil
}
