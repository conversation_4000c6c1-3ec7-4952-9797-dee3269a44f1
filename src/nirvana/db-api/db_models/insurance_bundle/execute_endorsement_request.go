// Code generated by SQLBoiler (https://github.com/volatiletech/sqlboiler). DO NOT EDIT.
// This file is meant to be re-generated in place and/or deleted at any time.

package insurance_bundle

import (
	"context"
	"database/sql"
	"fmt"
	"reflect"
	"strconv"
	"strings"
	"sync"
	"time"

	"github.com/friendsofgo/errors"
	"github.com/volatiletech/null/v8"
	"github.com/volatiletech/sqlboiler/v4/boil"
	"github.com/volatiletech/sqlboiler/v4/queries"
	"github.com/volatiletech/sqlboiler/v4/queries/qm"
	"github.com/volatiletech/sqlboiler/v4/queries/qmhelper"
	"github.com/volatiletech/strmangle"
)

// ExecuteEndorsementRequest is an object representing the database table.
type ExecuteEndorsementRequest struct {
	ID                  string    `boil:"id" json:"id" toml:"id" yaml:"id"`
	InsuranceBundleID   string    `boil:"insurance_bundle_id" json:"insurance_bundle_id" toml:"insurance_bundle_id" yaml:"insurance_bundle_id"`
	CreatedAt           null.Time `boil:"created_at" json:"created_at,omitempty" toml:"created_at" yaml:"created_at,omitempty"`
	UpdatedAt           null.Time `boil:"updated_at" json:"updated_at,omitempty" toml:"updated_at" yaml:"updated_at,omitempty"`
	Metadata            null.JSON `boil:"metadata" json:"metadata,omitempty" toml:"metadata" yaml:"metadata,omitempty"`
	EndorsedIbPersisted bool      `boil:"endorsed_ib_persisted" json:"endorsed_ib_persisted" toml:"endorsed_ib_persisted" yaml:"endorsed_ib_persisted"`

	R *executeEndorsementRequestR `boil:"" json:"" toml:"" yaml:""`
	L executeEndorsementRequestL  `boil:"-" json:"-" toml:"-" yaml:"-"`
}

var ExecuteEndorsementRequestColumns = struct {
	ID                  string
	InsuranceBundleID   string
	CreatedAt           string
	UpdatedAt           string
	Metadata            string
	EndorsedIbPersisted string
}{
	ID:                  "id",
	InsuranceBundleID:   "insurance_bundle_id",
	CreatedAt:           "created_at",
	UpdatedAt:           "updated_at",
	Metadata:            "metadata",
	EndorsedIbPersisted: "endorsed_ib_persisted",
}

var ExecuteEndorsementRequestTableColumns = struct {
	ID                  string
	InsuranceBundleID   string
	CreatedAt           string
	UpdatedAt           string
	Metadata            string
	EndorsedIbPersisted string
}{
	ID:                  "execute_endorsement_request.id",
	InsuranceBundleID:   "execute_endorsement_request.insurance_bundle_id",
	CreatedAt:           "execute_endorsement_request.created_at",
	UpdatedAt:           "execute_endorsement_request.updated_at",
	Metadata:            "execute_endorsement_request.metadata",
	EndorsedIbPersisted: "execute_endorsement_request.endorsed_ib_persisted",
}

// Generated where

type whereHelperbool struct{ field string }

func (w whereHelperbool) EQ(x bool) qm.QueryMod  { return qmhelper.Where(w.field, qmhelper.EQ, x) }
func (w whereHelperbool) NEQ(x bool) qm.QueryMod { return qmhelper.Where(w.field, qmhelper.NEQ, x) }
func (w whereHelperbool) LT(x bool) qm.QueryMod  { return qmhelper.Where(w.field, qmhelper.LT, x) }
func (w whereHelperbool) LTE(x bool) qm.QueryMod { return qmhelper.Where(w.field, qmhelper.LTE, x) }
func (w whereHelperbool) GT(x bool) qm.QueryMod  { return qmhelper.Where(w.field, qmhelper.GT, x) }
func (w whereHelperbool) GTE(x bool) qm.QueryMod { return qmhelper.Where(w.field, qmhelper.GTE, x) }

var ExecuteEndorsementRequestWhere = struct {
	ID                  whereHelperstring
	InsuranceBundleID   whereHelperstring
	CreatedAt           whereHelpernull_Time
	UpdatedAt           whereHelpernull_Time
	Metadata            whereHelpernull_JSON
	EndorsedIbPersisted whereHelperbool
}{
	ID:                  whereHelperstring{field: "\"insurance_bundle\".\"execute_endorsement_request\".\"id\""},
	InsuranceBundleID:   whereHelperstring{field: "\"insurance_bundle\".\"execute_endorsement_request\".\"insurance_bundle_id\""},
	CreatedAt:           whereHelpernull_Time{field: "\"insurance_bundle\".\"execute_endorsement_request\".\"created_at\""},
	UpdatedAt:           whereHelpernull_Time{field: "\"insurance_bundle\".\"execute_endorsement_request\".\"updated_at\""},
	Metadata:            whereHelpernull_JSON{field: "\"insurance_bundle\".\"execute_endorsement_request\".\"metadata\""},
	EndorsedIbPersisted: whereHelperbool{field: "\"insurance_bundle\".\"execute_endorsement_request\".\"endorsed_ib_persisted\""},
}

// ExecuteEndorsementRequestRels is where relationship names are stored.
var ExecuteEndorsementRequestRels = struct {
	InsuranceBundle                    string
	EndorsementRequestChangeContainers string
}{
	InsuranceBundle:                    "InsuranceBundle",
	EndorsementRequestChangeContainers: "EndorsementRequestChangeContainers",
}

// executeEndorsementRequestR is where relationships are stored.
type executeEndorsementRequestR struct {
	InsuranceBundle                    *InsuranceBundle     `boil:"InsuranceBundle" json:"InsuranceBundle" toml:"InsuranceBundle" yaml:"InsuranceBundle"`
	EndorsementRequestChangeContainers ChangeContainerSlice `boil:"EndorsementRequestChangeContainers" json:"EndorsementRequestChangeContainers" toml:"EndorsementRequestChangeContainers" yaml:"EndorsementRequestChangeContainers"`
}

// NewStruct creates a new relationship struct
func (*executeEndorsementRequestR) NewStruct() *executeEndorsementRequestR {
	return &executeEndorsementRequestR{}
}

// executeEndorsementRequestL is where Load methods for each relationship are stored.
type executeEndorsementRequestL struct{}

var (
	executeEndorsementRequestAllColumns            = []string{"id", "insurance_bundle_id", "created_at", "updated_at", "metadata", "endorsed_ib_persisted"}
	executeEndorsementRequestColumnsWithoutDefault = []string{"id", "insurance_bundle_id"}
	executeEndorsementRequestColumnsWithDefault    = []string{"created_at", "updated_at", "metadata", "endorsed_ib_persisted"}
	executeEndorsementRequestPrimaryKeyColumns     = []string{"id"}
	executeEndorsementRequestGeneratedColumns      = []string{}
)

type (
	// ExecuteEndorsementRequestSlice is an alias for a slice of pointers to ExecuteEndorsementRequest.
	// This should almost always be used instead of []ExecuteEndorsementRequest.
	ExecuteEndorsementRequestSlice []*ExecuteEndorsementRequest
	// ExecuteEndorsementRequestHook is the signature for custom ExecuteEndorsementRequest hook methods
	ExecuteEndorsementRequestHook func(context.Context, boil.ContextExecutor, *ExecuteEndorsementRequest) error

	executeEndorsementRequestQuery struct {
		*queries.Query
	}
)

// Cache for insert, update and upsert
var (
	executeEndorsementRequestType                 = reflect.TypeOf(&ExecuteEndorsementRequest{})
	executeEndorsementRequestMapping              = queries.MakeStructMapping(executeEndorsementRequestType)
	executeEndorsementRequestPrimaryKeyMapping, _ = queries.BindMapping(executeEndorsementRequestType, executeEndorsementRequestMapping, executeEndorsementRequestPrimaryKeyColumns)
	executeEndorsementRequestInsertCacheMut       sync.RWMutex
	executeEndorsementRequestInsertCache          = make(map[string]insertCache)
	executeEndorsementRequestUpdateCacheMut       sync.RWMutex
	executeEndorsementRequestUpdateCache          = make(map[string]updateCache)
	executeEndorsementRequestUpsertCacheMut       sync.RWMutex
	executeEndorsementRequestUpsertCache          = make(map[string]insertCache)
)

var (
	// Force time package dependency for automated UpdatedAt/CreatedAt.
	_ = time.Second
	// Force qmhelper dependency for where clause generation (which doesn't
	// always happen)
	_ = qmhelper.Where
)

var executeEndorsementRequestAfterSelectHooks []ExecuteEndorsementRequestHook

var executeEndorsementRequestBeforeInsertHooks []ExecuteEndorsementRequestHook
var executeEndorsementRequestAfterInsertHooks []ExecuteEndorsementRequestHook

var executeEndorsementRequestBeforeUpdateHooks []ExecuteEndorsementRequestHook
var executeEndorsementRequestAfterUpdateHooks []ExecuteEndorsementRequestHook

var executeEndorsementRequestBeforeDeleteHooks []ExecuteEndorsementRequestHook
var executeEndorsementRequestAfterDeleteHooks []ExecuteEndorsementRequestHook

var executeEndorsementRequestBeforeUpsertHooks []ExecuteEndorsementRequestHook
var executeEndorsementRequestAfterUpsertHooks []ExecuteEndorsementRequestHook

// doAfterSelectHooks executes all "after Select" hooks.
func (o *ExecuteEndorsementRequest) doAfterSelectHooks(ctx context.Context, exec boil.ContextExecutor) (err error) {
	if boil.HooksAreSkipped(ctx) {
		return nil
	}

	for _, hook := range executeEndorsementRequestAfterSelectHooks {
		if err := hook(ctx, exec, o); err != nil {
			return err
		}
	}

	return nil
}

// doBeforeInsertHooks executes all "before insert" hooks.
func (o *ExecuteEndorsementRequest) doBeforeInsertHooks(ctx context.Context, exec boil.ContextExecutor) (err error) {
	if boil.HooksAreSkipped(ctx) {
		return nil
	}

	for _, hook := range executeEndorsementRequestBeforeInsertHooks {
		if err := hook(ctx, exec, o); err != nil {
			return err
		}
	}

	return nil
}

// doAfterInsertHooks executes all "after Insert" hooks.
func (o *ExecuteEndorsementRequest) doAfterInsertHooks(ctx context.Context, exec boil.ContextExecutor) (err error) {
	if boil.HooksAreSkipped(ctx) {
		return nil
	}

	for _, hook := range executeEndorsementRequestAfterInsertHooks {
		if err := hook(ctx, exec, o); err != nil {
			return err
		}
	}

	return nil
}

// doBeforeUpdateHooks executes all "before Update" hooks.
func (o *ExecuteEndorsementRequest) doBeforeUpdateHooks(ctx context.Context, exec boil.ContextExecutor) (err error) {
	if boil.HooksAreSkipped(ctx) {
		return nil
	}

	for _, hook := range executeEndorsementRequestBeforeUpdateHooks {
		if err := hook(ctx, exec, o); err != nil {
			return err
		}
	}

	return nil
}

// doAfterUpdateHooks executes all "after Update" hooks.
func (o *ExecuteEndorsementRequest) doAfterUpdateHooks(ctx context.Context, exec boil.ContextExecutor) (err error) {
	if boil.HooksAreSkipped(ctx) {
		return nil
	}

	for _, hook := range executeEndorsementRequestAfterUpdateHooks {
		if err := hook(ctx, exec, o); err != nil {
			return err
		}
	}

	return nil
}

// doBeforeDeleteHooks executes all "before Delete" hooks.
func (o *ExecuteEndorsementRequest) doBeforeDeleteHooks(ctx context.Context, exec boil.ContextExecutor) (err error) {
	if boil.HooksAreSkipped(ctx) {
		return nil
	}

	for _, hook := range executeEndorsementRequestBeforeDeleteHooks {
		if err := hook(ctx, exec, o); err != nil {
			return err
		}
	}

	return nil
}

// doAfterDeleteHooks executes all "after Delete" hooks.
func (o *ExecuteEndorsementRequest) doAfterDeleteHooks(ctx context.Context, exec boil.ContextExecutor) (err error) {
	if boil.HooksAreSkipped(ctx) {
		return nil
	}

	for _, hook := range executeEndorsementRequestAfterDeleteHooks {
		if err := hook(ctx, exec, o); err != nil {
			return err
		}
	}

	return nil
}

// doBeforeUpsertHooks executes all "before Upsert" hooks.
func (o *ExecuteEndorsementRequest) doBeforeUpsertHooks(ctx context.Context, exec boil.ContextExecutor) (err error) {
	if boil.HooksAreSkipped(ctx) {
		return nil
	}

	for _, hook := range executeEndorsementRequestBeforeUpsertHooks {
		if err := hook(ctx, exec, o); err != nil {
			return err
		}
	}

	return nil
}

// doAfterUpsertHooks executes all "after Upsert" hooks.
func (o *ExecuteEndorsementRequest) doAfterUpsertHooks(ctx context.Context, exec boil.ContextExecutor) (err error) {
	if boil.HooksAreSkipped(ctx) {
		return nil
	}

	for _, hook := range executeEndorsementRequestAfterUpsertHooks {
		if err := hook(ctx, exec, o); err != nil {
			return err
		}
	}

	return nil
}

// AddExecuteEndorsementRequestHook registers your hook function for all future operations.
func AddExecuteEndorsementRequestHook(hookPoint boil.HookPoint, executeEndorsementRequestHook ExecuteEndorsementRequestHook) {
	switch hookPoint {
	case boil.AfterSelectHook:
		executeEndorsementRequestAfterSelectHooks = append(executeEndorsementRequestAfterSelectHooks, executeEndorsementRequestHook)
	case boil.BeforeInsertHook:
		executeEndorsementRequestBeforeInsertHooks = append(executeEndorsementRequestBeforeInsertHooks, executeEndorsementRequestHook)
	case boil.AfterInsertHook:
		executeEndorsementRequestAfterInsertHooks = append(executeEndorsementRequestAfterInsertHooks, executeEndorsementRequestHook)
	case boil.BeforeUpdateHook:
		executeEndorsementRequestBeforeUpdateHooks = append(executeEndorsementRequestBeforeUpdateHooks, executeEndorsementRequestHook)
	case boil.AfterUpdateHook:
		executeEndorsementRequestAfterUpdateHooks = append(executeEndorsementRequestAfterUpdateHooks, executeEndorsementRequestHook)
	case boil.BeforeDeleteHook:
		executeEndorsementRequestBeforeDeleteHooks = append(executeEndorsementRequestBeforeDeleteHooks, executeEndorsementRequestHook)
	case boil.AfterDeleteHook:
		executeEndorsementRequestAfterDeleteHooks = append(executeEndorsementRequestAfterDeleteHooks, executeEndorsementRequestHook)
	case boil.BeforeUpsertHook:
		executeEndorsementRequestBeforeUpsertHooks = append(executeEndorsementRequestBeforeUpsertHooks, executeEndorsementRequestHook)
	case boil.AfterUpsertHook:
		executeEndorsementRequestAfterUpsertHooks = append(executeEndorsementRequestAfterUpsertHooks, executeEndorsementRequestHook)
	}
}

// One returns a single executeEndorsementRequest record from the query.
func (q executeEndorsementRequestQuery) One(ctx context.Context, exec boil.ContextExecutor) (*ExecuteEndorsementRequest, error) {
	o := &ExecuteEndorsementRequest{}

	queries.SetLimit(q.Query, 1)

	err := q.Bind(ctx, exec, o)
	if err != nil {
		if errors.Is(err, sql.ErrNoRows) {
			return nil, sql.ErrNoRows
		}
		return nil, errors.Wrap(err, "insurance_bundle: failed to execute a one query for execute_endorsement_request")
	}

	if err := o.doAfterSelectHooks(ctx, exec); err != nil {
		return o, err
	}

	return o, nil
}

// All returns all ExecuteEndorsementRequest records from the query.
func (q executeEndorsementRequestQuery) All(ctx context.Context, exec boil.ContextExecutor) (ExecuteEndorsementRequestSlice, error) {
	var o []*ExecuteEndorsementRequest

	err := q.Bind(ctx, exec, &o)
	if err != nil {
		return nil, errors.Wrap(err, "insurance_bundle: failed to assign all query results to ExecuteEndorsementRequest slice")
	}

	if len(executeEndorsementRequestAfterSelectHooks) != 0 {
		for _, obj := range o {
			if err := obj.doAfterSelectHooks(ctx, exec); err != nil {
				return o, err
			}
		}
	}

	return o, nil
}

// Count returns the count of all ExecuteEndorsementRequest records in the query.
func (q executeEndorsementRequestQuery) Count(ctx context.Context, exec boil.ContextExecutor) (int64, error) {
	var count int64

	queries.SetSelect(q.Query, nil)
	queries.SetCount(q.Query)

	err := q.Query.QueryRowContext(ctx, exec).Scan(&count)
	if err != nil {
		return 0, errors.Wrap(err, "insurance_bundle: failed to count execute_endorsement_request rows")
	}

	return count, nil
}

// Exists checks if the row exists in the table.
func (q executeEndorsementRequestQuery) Exists(ctx context.Context, exec boil.ContextExecutor) (bool, error) {
	var count int64

	queries.SetSelect(q.Query, nil)
	queries.SetCount(q.Query)
	queries.SetLimit(q.Query, 1)

	err := q.Query.QueryRowContext(ctx, exec).Scan(&count)
	if err != nil {
		return false, errors.Wrap(err, "insurance_bundle: failed to check if execute_endorsement_request exists")
	}

	return count > 0, nil
}

// InsuranceBundle pointed to by the foreign key.
func (o *ExecuteEndorsementRequest) InsuranceBundle(mods ...qm.QueryMod) insuranceBundleQuery {
	queryMods := []qm.QueryMod{
		qm.Where("\"internal_id\" = ?", o.InsuranceBundleID),
	}

	queryMods = append(queryMods, mods...)

	return InsuranceBundles(queryMods...)
}

// EndorsementRequestChangeContainers retrieves all the change_container's ChangeContainers with an executor via endorsement_request_id column.
func (o *ExecuteEndorsementRequest) EndorsementRequestChangeContainers(mods ...qm.QueryMod) changeContainerQuery {
	var queryMods []qm.QueryMod
	if len(mods) != 0 {
		queryMods = append(queryMods, mods...)
	}

	queryMods = append(queryMods,
		qm.Where("\"insurance_bundle\".\"change_container\".\"endorsement_request_id\"=?", o.ID),
	)

	return ChangeContainers(queryMods...)
}

// LoadInsuranceBundle allows an eager lookup of values, cached into the
// loaded structs of the objects. This is for an N-1 relationship.
func (executeEndorsementRequestL) LoadInsuranceBundle(ctx context.Context, e boil.ContextExecutor, singular bool, maybeExecuteEndorsementRequest interface{}, mods queries.Applicator) error {
	var slice []*ExecuteEndorsementRequest
	var object *ExecuteEndorsementRequest

	if singular {
		object = maybeExecuteEndorsementRequest.(*ExecuteEndorsementRequest)
	} else {
		slice = *maybeExecuteEndorsementRequest.(*[]*ExecuteEndorsementRequest)
	}

	args := make([]interface{}, 0, 1)
	if singular {
		if object.R == nil {
			object.R = &executeEndorsementRequestR{}
		}
		args = append(args, object.InsuranceBundleID)

	} else {
	Outer:
		for _, obj := range slice {
			if obj.R == nil {
				obj.R = &executeEndorsementRequestR{}
			}

			for _, a := range args {
				if a == obj.InsuranceBundleID {
					continue Outer
				}
			}

			args = append(args, obj.InsuranceBundleID)

		}
	}

	if len(args) == 0 {
		return nil
	}

	query := NewQuery(
		qm.From(`insurance_bundle.insurance_bundle`),
		qm.WhereIn(`insurance_bundle.insurance_bundle.internal_id in ?`, args...),
	)
	if mods != nil {
		mods.Apply(query)
	}

	results, err := query.QueryContext(ctx, e)
	if err != nil {
		return errors.Wrap(err, "failed to eager load InsuranceBundle")
	}

	var resultSlice []*InsuranceBundle
	if err = queries.Bind(results, &resultSlice); err != nil {
		return errors.Wrap(err, "failed to bind eager loaded slice InsuranceBundle")
	}

	if err = results.Close(); err != nil {
		return errors.Wrap(err, "failed to close results of eager load for insurance_bundle")
	}
	if err = results.Err(); err != nil {
		return errors.Wrap(err, "error occurred during iteration of eager loaded relations for insurance_bundle")
	}

	if len(executeEndorsementRequestAfterSelectHooks) != 0 {
		for _, obj := range resultSlice {
			if err := obj.doAfterSelectHooks(ctx, e); err != nil {
				return err
			}
		}
	}

	if len(resultSlice) == 0 {
		return nil
	}

	if singular {
		foreign := resultSlice[0]
		object.R.InsuranceBundle = foreign
		if foreign.R == nil {
			foreign.R = &insuranceBundleR{}
		}
		foreign.R.ExecuteEndorsementRequests = append(foreign.R.ExecuteEndorsementRequests, object)
		return nil
	}

	for _, local := range slice {
		for _, foreign := range resultSlice {
			if local.InsuranceBundleID == foreign.InternalID {
				local.R.InsuranceBundle = foreign
				if foreign.R == nil {
					foreign.R = &insuranceBundleR{}
				}
				foreign.R.ExecuteEndorsementRequests = append(foreign.R.ExecuteEndorsementRequests, local)
				break
			}
		}
	}

	return nil
}

// LoadEndorsementRequestChangeContainers allows an eager lookup of values, cached into the
// loaded structs of the objects. This is for a 1-M or N-M relationship.
func (executeEndorsementRequestL) LoadEndorsementRequestChangeContainers(ctx context.Context, e boil.ContextExecutor, singular bool, maybeExecuteEndorsementRequest interface{}, mods queries.Applicator) error {
	var slice []*ExecuteEndorsementRequest
	var object *ExecuteEndorsementRequest

	if singular {
		object = maybeExecuteEndorsementRequest.(*ExecuteEndorsementRequest)
	} else {
		slice = *maybeExecuteEndorsementRequest.(*[]*ExecuteEndorsementRequest)
	}

	args := make([]interface{}, 0, 1)
	if singular {
		if object.R == nil {
			object.R = &executeEndorsementRequestR{}
		}
		args = append(args, object.ID)
	} else {
	Outer:
		for _, obj := range slice {
			if obj.R == nil {
				obj.R = &executeEndorsementRequestR{}
			}

			for _, a := range args {
				if a == obj.ID {
					continue Outer
				}
			}

			args = append(args, obj.ID)
		}
	}

	if len(args) == 0 {
		return nil
	}

	query := NewQuery(
		qm.From(`insurance_bundle.change_container`),
		qm.WhereIn(`insurance_bundle.change_container.endorsement_request_id in ?`, args...),
	)
	if mods != nil {
		mods.Apply(query)
	}

	results, err := query.QueryContext(ctx, e)
	if err != nil {
		return errors.Wrap(err, "failed to eager load change_container")
	}

	var resultSlice []*ChangeContainer
	if err = queries.Bind(results, &resultSlice); err != nil {
		return errors.Wrap(err, "failed to bind eager loaded slice change_container")
	}

	if err = results.Close(); err != nil {
		return errors.Wrap(err, "failed to close results in eager load on change_container")
	}
	if err = results.Err(); err != nil {
		return errors.Wrap(err, "error occurred during iteration of eager loaded relations for change_container")
	}

	if len(changeContainerAfterSelectHooks) != 0 {
		for _, obj := range resultSlice {
			if err := obj.doAfterSelectHooks(ctx, e); err != nil {
				return err
			}
		}
	}
	if singular {
		object.R.EndorsementRequestChangeContainers = resultSlice
		for _, foreign := range resultSlice {
			if foreign.R == nil {
				foreign.R = &changeContainerR{}
			}
			foreign.R.EndorsementRequest = object
		}
		return nil
	}

	for _, foreign := range resultSlice {
		for _, local := range slice {
			if local.ID == foreign.EndorsementRequestID {
				local.R.EndorsementRequestChangeContainers = append(local.R.EndorsementRequestChangeContainers, foreign)
				if foreign.R == nil {
					foreign.R = &changeContainerR{}
				}
				foreign.R.EndorsementRequest = local
				break
			}
		}
	}

	return nil
}

// SetInsuranceBundle of the executeEndorsementRequest to the related item.
// Sets o.R.InsuranceBundle to related.
// Adds o to related.R.ExecuteEndorsementRequests.
func (o *ExecuteEndorsementRequest) SetInsuranceBundle(ctx context.Context, exec boil.ContextExecutor, insert bool, related *InsuranceBundle) error {
	var err error
	if insert {
		if err = related.Insert(ctx, exec, boil.Infer()); err != nil {
			return errors.Wrap(err, "failed to insert into foreign table")
		}
	}

	updateQuery := fmt.Sprintf(
		"UPDATE \"insurance_bundle\".\"execute_endorsement_request\" SET %s WHERE %s",
		strmangle.SetParamNames("\"", "\"", 1, []string{"insurance_bundle_id"}),
		strmangle.WhereClause("\"", "\"", 2, executeEndorsementRequestPrimaryKeyColumns),
	)
	values := []interface{}{related.InternalID, o.ID}

	if boil.IsDebug(ctx) {
		writer := boil.DebugWriterFrom(ctx)
		fmt.Fprintln(writer, updateQuery)
		fmt.Fprintln(writer, values)
	}
	if _, err = exec.ExecContext(ctx, updateQuery, values...); err != nil {
		return errors.Wrap(err, "failed to update local table")
	}

	o.InsuranceBundleID = related.InternalID
	if o.R == nil {
		o.R = &executeEndorsementRequestR{
			InsuranceBundle: related,
		}
	} else {
		o.R.InsuranceBundle = related
	}

	if related.R == nil {
		related.R = &insuranceBundleR{
			ExecuteEndorsementRequests: ExecuteEndorsementRequestSlice{o},
		}
	} else {
		related.R.ExecuteEndorsementRequests = append(related.R.ExecuteEndorsementRequests, o)
	}

	return nil
}

// AddEndorsementRequestChangeContainers adds the given related objects to the existing relationships
// of the execute_endorsement_request, optionally inserting them as new records.
// Appends related to o.R.EndorsementRequestChangeContainers.
// Sets related.R.EndorsementRequest appropriately.
func (o *ExecuteEndorsementRequest) AddEndorsementRequestChangeContainers(ctx context.Context, exec boil.ContextExecutor, insert bool, related ...*ChangeContainer) error {
	var err error
	for _, rel := range related {
		if insert {
			rel.EndorsementRequestID = o.ID
			if err = rel.Insert(ctx, exec, boil.Infer()); err != nil {
				return errors.Wrap(err, "failed to insert into foreign table")
			}
		} else {
			updateQuery := fmt.Sprintf(
				"UPDATE \"insurance_bundle\".\"change_container\" SET %s WHERE %s",
				strmangle.SetParamNames("\"", "\"", 1, []string{"endorsement_request_id"}),
				strmangle.WhereClause("\"", "\"", 2, changeContainerPrimaryKeyColumns),
			)
			values := []interface{}{o.ID, rel.ID}

			if boil.IsDebug(ctx) {
				writer := boil.DebugWriterFrom(ctx)
				fmt.Fprintln(writer, updateQuery)
				fmt.Fprintln(writer, values)
			}
			if _, err = exec.ExecContext(ctx, updateQuery, values...); err != nil {
				return errors.Wrap(err, "failed to update foreign table")
			}

			rel.EndorsementRequestID = o.ID
		}
	}

	if o.R == nil {
		o.R = &executeEndorsementRequestR{
			EndorsementRequestChangeContainers: related,
		}
	} else {
		o.R.EndorsementRequestChangeContainers = append(o.R.EndorsementRequestChangeContainers, related...)
	}

	for _, rel := range related {
		if rel.R == nil {
			rel.R = &changeContainerR{
				EndorsementRequest: o,
			}
		} else {
			rel.R.EndorsementRequest = o
		}
	}
	return nil
}

// ExecuteEndorsementRequests retrieves all the records using an executor.
func ExecuteEndorsementRequests(mods ...qm.QueryMod) executeEndorsementRequestQuery {
	mods = append(mods, qm.From("\"insurance_bundle\".\"execute_endorsement_request\""))
	q := NewQuery(mods...)
	if len(queries.GetSelect(q)) == 0 {
		queries.SetSelect(q, []string{"\"insurance_bundle\".\"execute_endorsement_request\".*"})
	}

	return executeEndorsementRequestQuery{q}
}

// FindExecuteEndorsementRequest retrieves a single record by ID with an executor.
// If selectCols is empty Find will return all columns.
func FindExecuteEndorsementRequest(ctx context.Context, exec boil.ContextExecutor, iD string, selectCols ...string) (*ExecuteEndorsementRequest, error) {
	executeEndorsementRequestObj := &ExecuteEndorsementRequest{}

	sel := "*"
	if len(selectCols) > 0 {
		sel = strings.Join(strmangle.IdentQuoteSlice(dialect.LQ, dialect.RQ, selectCols), ",")
	}
	query := fmt.Sprintf(
		"select %s from \"insurance_bundle\".\"execute_endorsement_request\" where \"id\"=$1", sel,
	)

	q := queries.Raw(query, iD)

	err := q.Bind(ctx, exec, executeEndorsementRequestObj)
	if err != nil {
		if errors.Is(err, sql.ErrNoRows) {
			return nil, sql.ErrNoRows
		}
		return nil, errors.Wrap(err, "insurance_bundle: unable to select from execute_endorsement_request")
	}

	if err = executeEndorsementRequestObj.doAfterSelectHooks(ctx, exec); err != nil {
		return executeEndorsementRequestObj, err
	}

	return executeEndorsementRequestObj, nil
}

// Insert a single record using an executor.
// See boil.Columns.InsertColumnSet documentation to understand column list inference for inserts.
func (o *ExecuteEndorsementRequest) Insert(ctx context.Context, exec boil.ContextExecutor, columns boil.Columns) error {
	if o == nil {
		return errors.New("insurance_bundle: no execute_endorsement_request provided for insertion")
	}

	var err error
	if !boil.TimestampsAreSkipped(ctx) {
		currTime := time.Now().In(boil.GetLocation())

		if queries.MustTime(o.CreatedAt).IsZero() {
			queries.SetScanner(&o.CreatedAt, currTime)
		}
		if queries.MustTime(o.UpdatedAt).IsZero() {
			queries.SetScanner(&o.UpdatedAt, currTime)
		}
	}

	if err := o.doBeforeInsertHooks(ctx, exec); err != nil {
		return err
	}

	nzDefaults := queries.NonZeroDefaultSet(executeEndorsementRequestColumnsWithDefault, o)

	key := makeCacheKey(columns, nzDefaults)
	executeEndorsementRequestInsertCacheMut.RLock()
	cache, cached := executeEndorsementRequestInsertCache[key]
	executeEndorsementRequestInsertCacheMut.RUnlock()

	if !cached {
		wl, returnColumns := columns.InsertColumnSet(
			executeEndorsementRequestAllColumns,
			executeEndorsementRequestColumnsWithDefault,
			executeEndorsementRequestColumnsWithoutDefault,
			nzDefaults,
		)

		cache.valueMapping, err = queries.BindMapping(executeEndorsementRequestType, executeEndorsementRequestMapping, wl)
		if err != nil {
			return err
		}
		cache.retMapping, err = queries.BindMapping(executeEndorsementRequestType, executeEndorsementRequestMapping, returnColumns)
		if err != nil {
			return err
		}
		if len(wl) != 0 {
			cache.query = fmt.Sprintf("INSERT INTO \"insurance_bundle\".\"execute_endorsement_request\" (\"%s\") %%sVALUES (%s)%%s", strings.Join(wl, "\",\""), strmangle.Placeholders(dialect.UseIndexPlaceholders, len(wl), 1, 1))
		} else {
			cache.query = "INSERT INTO \"insurance_bundle\".\"execute_endorsement_request\" %sDEFAULT VALUES%s"
		}

		var queryOutput, queryReturning string

		if len(cache.retMapping) != 0 {
			queryReturning = fmt.Sprintf(" RETURNING \"%s\"", strings.Join(returnColumns, "\",\""))
		}

		cache.query = fmt.Sprintf(cache.query, queryOutput, queryReturning)
	}

	value := reflect.Indirect(reflect.ValueOf(o))
	vals := queries.ValuesFromMapping(value, cache.valueMapping)

	if boil.IsDebug(ctx) {
		writer := boil.DebugWriterFrom(ctx)
		fmt.Fprintln(writer, cache.query)
		fmt.Fprintln(writer, vals)
	}

	if len(cache.retMapping) != 0 {
		err = exec.QueryRowContext(ctx, cache.query, vals...).Scan(queries.PtrsFromMapping(value, cache.retMapping)...)
	} else {
		_, err = exec.ExecContext(ctx, cache.query, vals...)
	}

	if err != nil {
		return errors.Wrap(err, "insurance_bundle: unable to insert into execute_endorsement_request")
	}

	if !cached {
		executeEndorsementRequestInsertCacheMut.Lock()
		executeEndorsementRequestInsertCache[key] = cache
		executeEndorsementRequestInsertCacheMut.Unlock()
	}

	return o.doAfterInsertHooks(ctx, exec)
}

// Update uses an executor to update the ExecuteEndorsementRequest.
// See boil.Columns.UpdateColumnSet documentation to understand column list inference for updates.
// Update does not automatically update the record in case of default values. Use .Reload() to refresh the records.
func (o *ExecuteEndorsementRequest) Update(ctx context.Context, exec boil.ContextExecutor, columns boil.Columns) (int64, error) {
	if !boil.TimestampsAreSkipped(ctx) {
		currTime := time.Now().In(boil.GetLocation())

		queries.SetScanner(&o.UpdatedAt, currTime)
	}

	var err error
	if err = o.doBeforeUpdateHooks(ctx, exec); err != nil {
		return 0, err
	}
	key := makeCacheKey(columns, nil)
	executeEndorsementRequestUpdateCacheMut.RLock()
	cache, cached := executeEndorsementRequestUpdateCache[key]
	executeEndorsementRequestUpdateCacheMut.RUnlock()

	if !cached {
		wl := columns.UpdateColumnSet(
			executeEndorsementRequestAllColumns,
			executeEndorsementRequestPrimaryKeyColumns,
		)

		if !columns.IsWhitelist() {
			wl = strmangle.SetComplement(wl, []string{"created_at"})
		}
		if len(wl) == 0 {
			return 0, errors.New("insurance_bundle: unable to update execute_endorsement_request, could not build whitelist")
		}

		cache.query = fmt.Sprintf("UPDATE \"insurance_bundle\".\"execute_endorsement_request\" SET %s WHERE %s",
			strmangle.SetParamNames("\"", "\"", 1, wl),
			strmangle.WhereClause("\"", "\"", len(wl)+1, executeEndorsementRequestPrimaryKeyColumns),
		)
		cache.valueMapping, err = queries.BindMapping(executeEndorsementRequestType, executeEndorsementRequestMapping, append(wl, executeEndorsementRequestPrimaryKeyColumns...))
		if err != nil {
			return 0, err
		}
	}

	values := queries.ValuesFromMapping(reflect.Indirect(reflect.ValueOf(o)), cache.valueMapping)

	if boil.IsDebug(ctx) {
		writer := boil.DebugWriterFrom(ctx)
		fmt.Fprintln(writer, cache.query)
		fmt.Fprintln(writer, values)
	}
	var result sql.Result
	result, err = exec.ExecContext(ctx, cache.query, values...)
	if err != nil {
		return 0, errors.Wrap(err, "insurance_bundle: unable to update execute_endorsement_request row")
	}

	rowsAff, err := result.RowsAffected()
	if err != nil {
		return 0, errors.Wrap(err, "insurance_bundle: failed to get rows affected by update for execute_endorsement_request")
	}

	if !cached {
		executeEndorsementRequestUpdateCacheMut.Lock()
		executeEndorsementRequestUpdateCache[key] = cache
		executeEndorsementRequestUpdateCacheMut.Unlock()
	}

	return rowsAff, o.doAfterUpdateHooks(ctx, exec)
}

// UpdateAll updates all rows with the specified column values.
func (q executeEndorsementRequestQuery) UpdateAll(ctx context.Context, exec boil.ContextExecutor, cols M) (int64, error) {
	queries.SetUpdate(q.Query, cols)

	result, err := q.Query.ExecContext(ctx, exec)
	if err != nil {
		return 0, errors.Wrap(err, "insurance_bundle: unable to update all for execute_endorsement_request")
	}

	rowsAff, err := result.RowsAffected()
	if err != nil {
		return 0, errors.Wrap(err, "insurance_bundle: unable to retrieve rows affected for execute_endorsement_request")
	}

	return rowsAff, nil
}

// UpdateAll updates all rows with the specified column values, using an executor.
func (o ExecuteEndorsementRequestSlice) UpdateAll(ctx context.Context, exec boil.ContextExecutor, cols M) (int64, error) {
	ln := int64(len(o))
	if ln == 0 {
		return 0, nil
	}

	if len(cols) == 0 {
		return 0, errors.New("insurance_bundle: update all requires at least one column argument")
	}

	colNames := make([]string, len(cols))
	args := make([]interface{}, len(cols))

	i := 0
	for name, value := range cols {
		colNames[i] = name
		args[i] = value
		i++
	}

	// Append all of the primary key values for each column
	for _, obj := range o {
		pkeyArgs := queries.ValuesFromMapping(reflect.Indirect(reflect.ValueOf(obj)), executeEndorsementRequestPrimaryKeyMapping)
		args = append(args, pkeyArgs...)
	}

	sql := fmt.Sprintf("UPDATE \"insurance_bundle\".\"execute_endorsement_request\" SET %s WHERE %s",
		strmangle.SetParamNames("\"", "\"", 1, colNames),
		strmangle.WhereClauseRepeated(string(dialect.LQ), string(dialect.RQ), len(colNames)+1, executeEndorsementRequestPrimaryKeyColumns, len(o)))

	if boil.IsDebug(ctx) {
		writer := boil.DebugWriterFrom(ctx)
		fmt.Fprintln(writer, sql)
		fmt.Fprintln(writer, args...)
	}
	result, err := exec.ExecContext(ctx, sql, args...)
	if err != nil {
		return 0, errors.Wrap(err, "insurance_bundle: unable to update all in executeEndorsementRequest slice")
	}

	rowsAff, err := result.RowsAffected()
	if err != nil {
		return 0, errors.Wrap(err, "insurance_bundle: unable to retrieve rows affected all in update all executeEndorsementRequest")
	}
	return rowsAff, nil
}

// Upsert attempts an insert using an executor, and does an update or ignore on conflict.
// See boil.Columns documentation for how to properly use updateColumns and insertColumns.
func (o *ExecuteEndorsementRequest) Upsert(ctx context.Context, exec boil.ContextExecutor, updateOnConflict bool, conflictColumns []string, updateColumns, insertColumns boil.Columns) error {
	if o == nil {
		return errors.New("insurance_bundle: no execute_endorsement_request provided for upsert")
	}
	if !boil.TimestampsAreSkipped(ctx) {
		currTime := time.Now().In(boil.GetLocation())

		if queries.MustTime(o.CreatedAt).IsZero() {
			queries.SetScanner(&o.CreatedAt, currTime)
		}
		queries.SetScanner(&o.UpdatedAt, currTime)
	}

	if err := o.doBeforeUpsertHooks(ctx, exec); err != nil {
		return err
	}

	nzDefaults := queries.NonZeroDefaultSet(executeEndorsementRequestColumnsWithDefault, o)

	// Build cache key in-line uglily - mysql vs psql problems
	buf := strmangle.GetBuffer()
	if updateOnConflict {
		buf.WriteByte('t')
	} else {
		buf.WriteByte('f')
	}
	buf.WriteByte('.')
	for _, c := range conflictColumns {
		buf.WriteString(c)
	}
	buf.WriteByte('.')
	buf.WriteString(strconv.Itoa(updateColumns.Kind))
	for _, c := range updateColumns.Cols {
		buf.WriteString(c)
	}
	buf.WriteByte('.')
	buf.WriteString(strconv.Itoa(insertColumns.Kind))
	for _, c := range insertColumns.Cols {
		buf.WriteString(c)
	}
	buf.WriteByte('.')
	for _, c := range nzDefaults {
		buf.WriteString(c)
	}
	key := buf.String()
	strmangle.PutBuffer(buf)

	executeEndorsementRequestUpsertCacheMut.RLock()
	cache, cached := executeEndorsementRequestUpsertCache[key]
	executeEndorsementRequestUpsertCacheMut.RUnlock()

	var err error

	if !cached {
		insert, ret := insertColumns.InsertColumnSet(
			executeEndorsementRequestAllColumns,
			executeEndorsementRequestColumnsWithDefault,
			executeEndorsementRequestColumnsWithoutDefault,
			nzDefaults,
		)

		update := updateColumns.UpdateColumnSet(
			executeEndorsementRequestAllColumns,
			executeEndorsementRequestPrimaryKeyColumns,
		)

		if updateOnConflict && len(update) == 0 {
			return errors.New("insurance_bundle: unable to upsert execute_endorsement_request, could not build update column list")
		}

		conflict := conflictColumns
		if len(conflict) == 0 {
			conflict = make([]string, len(executeEndorsementRequestPrimaryKeyColumns))
			copy(conflict, executeEndorsementRequestPrimaryKeyColumns)
		}
		cache.query = buildUpsertQueryPostgres(dialect, "\"insurance_bundle\".\"execute_endorsement_request\"", updateOnConflict, ret, update, conflict, insert)

		cache.valueMapping, err = queries.BindMapping(executeEndorsementRequestType, executeEndorsementRequestMapping, insert)
		if err != nil {
			return err
		}
		if len(ret) != 0 {
			cache.retMapping, err = queries.BindMapping(executeEndorsementRequestType, executeEndorsementRequestMapping, ret)
			if err != nil {
				return err
			}
		}
	}

	value := reflect.Indirect(reflect.ValueOf(o))
	vals := queries.ValuesFromMapping(value, cache.valueMapping)
	var returns []interface{}
	if len(cache.retMapping) != 0 {
		returns = queries.PtrsFromMapping(value, cache.retMapping)
	}

	if boil.IsDebug(ctx) {
		writer := boil.DebugWriterFrom(ctx)
		fmt.Fprintln(writer, cache.query)
		fmt.Fprintln(writer, vals)
	}
	if len(cache.retMapping) != 0 {
		err = exec.QueryRowContext(ctx, cache.query, vals...).Scan(returns...)
		if errors.Is(err, sql.ErrNoRows) {
			err = nil // Postgres doesn't return anything when there's no update
		}
	} else {
		_, err = exec.ExecContext(ctx, cache.query, vals...)
	}
	if err != nil {
		return errors.Wrap(err, "insurance_bundle: unable to upsert execute_endorsement_request")
	}

	if !cached {
		executeEndorsementRequestUpsertCacheMut.Lock()
		executeEndorsementRequestUpsertCache[key] = cache
		executeEndorsementRequestUpsertCacheMut.Unlock()
	}

	return o.doAfterUpsertHooks(ctx, exec)
}

// Delete deletes a single ExecuteEndorsementRequest record with an executor.
// Delete will match against the primary key column to find the record to delete.
func (o *ExecuteEndorsementRequest) Delete(ctx context.Context, exec boil.ContextExecutor) (int64, error) {
	if o == nil {
		return 0, errors.New("insurance_bundle: no ExecuteEndorsementRequest provided for delete")
	}

	if err := o.doBeforeDeleteHooks(ctx, exec); err != nil {
		return 0, err
	}

	args := queries.ValuesFromMapping(reflect.Indirect(reflect.ValueOf(o)), executeEndorsementRequestPrimaryKeyMapping)
	sql := "DELETE FROM \"insurance_bundle\".\"execute_endorsement_request\" WHERE \"id\"=$1"

	if boil.IsDebug(ctx) {
		writer := boil.DebugWriterFrom(ctx)
		fmt.Fprintln(writer, sql)
		fmt.Fprintln(writer, args...)
	}
	result, err := exec.ExecContext(ctx, sql, args...)
	if err != nil {
		return 0, errors.Wrap(err, "insurance_bundle: unable to delete from execute_endorsement_request")
	}

	rowsAff, err := result.RowsAffected()
	if err != nil {
		return 0, errors.Wrap(err, "insurance_bundle: failed to get rows affected by delete for execute_endorsement_request")
	}

	if err := o.doAfterDeleteHooks(ctx, exec); err != nil {
		return 0, err
	}

	return rowsAff, nil
}

// DeleteAll deletes all matching rows.
func (q executeEndorsementRequestQuery) DeleteAll(ctx context.Context, exec boil.ContextExecutor) (int64, error) {
	if q.Query == nil {
		return 0, errors.New("insurance_bundle: no executeEndorsementRequestQuery provided for delete all")
	}

	queries.SetDelete(q.Query)

	result, err := q.Query.ExecContext(ctx, exec)
	if err != nil {
		return 0, errors.Wrap(err, "insurance_bundle: unable to delete all from execute_endorsement_request")
	}

	rowsAff, err := result.RowsAffected()
	if err != nil {
		return 0, errors.Wrap(err, "insurance_bundle: failed to get rows affected by deleteall for execute_endorsement_request")
	}

	return rowsAff, nil
}

// DeleteAll deletes all rows in the slice, using an executor.
func (o ExecuteEndorsementRequestSlice) DeleteAll(ctx context.Context, exec boil.ContextExecutor) (int64, error) {
	if len(o) == 0 {
		return 0, nil
	}

	if len(executeEndorsementRequestBeforeDeleteHooks) != 0 {
		for _, obj := range o {
			if err := obj.doBeforeDeleteHooks(ctx, exec); err != nil {
				return 0, err
			}
		}
	}

	var args []interface{}
	for _, obj := range o {
		pkeyArgs := queries.ValuesFromMapping(reflect.Indirect(reflect.ValueOf(obj)), executeEndorsementRequestPrimaryKeyMapping)
		args = append(args, pkeyArgs...)
	}

	sql := "DELETE FROM \"insurance_bundle\".\"execute_endorsement_request\" WHERE " +
		strmangle.WhereClauseRepeated(string(dialect.LQ), string(dialect.RQ), 1, executeEndorsementRequestPrimaryKeyColumns, len(o))

	if boil.IsDebug(ctx) {
		writer := boil.DebugWriterFrom(ctx)
		fmt.Fprintln(writer, sql)
		fmt.Fprintln(writer, args)
	}
	result, err := exec.ExecContext(ctx, sql, args...)
	if err != nil {
		return 0, errors.Wrap(err, "insurance_bundle: unable to delete all from executeEndorsementRequest slice")
	}

	rowsAff, err := result.RowsAffected()
	if err != nil {
		return 0, errors.Wrap(err, "insurance_bundle: failed to get rows affected by deleteall for execute_endorsement_request")
	}

	if len(executeEndorsementRequestAfterDeleteHooks) != 0 {
		for _, obj := range o {
			if err := obj.doAfterDeleteHooks(ctx, exec); err != nil {
				return 0, err
			}
		}
	}

	return rowsAff, nil
}

// Reload refetches the object from the database
// using the primary keys with an executor.
func (o *ExecuteEndorsementRequest) Reload(ctx context.Context, exec boil.ContextExecutor) error {
	ret, err := FindExecuteEndorsementRequest(ctx, exec, o.ID)
	if err != nil {
		return err
	}

	*o = *ret
	return nil
}

// ReloadAll refetches every row with matching primary key column values
// and overwrites the original object slice with the newly updated slice.
func (o *ExecuteEndorsementRequestSlice) ReloadAll(ctx context.Context, exec boil.ContextExecutor) error {
	if o == nil || len(*o) == 0 {
		return nil
	}

	slice := ExecuteEndorsementRequestSlice{}
	var args []interface{}
	for _, obj := range *o {
		pkeyArgs := queries.ValuesFromMapping(reflect.Indirect(reflect.ValueOf(obj)), executeEndorsementRequestPrimaryKeyMapping)
		args = append(args, pkeyArgs...)
	}

	sql := "SELECT \"insurance_bundle\".\"execute_endorsement_request\".* FROM \"insurance_bundle\".\"execute_endorsement_request\" WHERE " +
		strmangle.WhereClauseRepeated(string(dialect.LQ), string(dialect.RQ), 1, executeEndorsementRequestPrimaryKeyColumns, len(*o))

	q := queries.Raw(sql, args...)

	err := q.Bind(ctx, exec, &slice)
	if err != nil {
		return errors.Wrap(err, "insurance_bundle: unable to reload all in ExecuteEndorsementRequestSlice")
	}

	*o = slice

	return nil
}

// ExecuteEndorsementRequestExists checks if the ExecuteEndorsementRequest row exists.
func ExecuteEndorsementRequestExists(ctx context.Context, exec boil.ContextExecutor, iD string) (bool, error) {
	var exists bool
	sql := "select exists(select 1 from \"insurance_bundle\".\"execute_endorsement_request\" where \"id\"=$1 limit 1)"

	if boil.IsDebug(ctx) {
		writer := boil.DebugWriterFrom(ctx)
		fmt.Fprintln(writer, sql)
		fmt.Fprintln(writer, iD)
	}
	row := exec.QueryRowContext(ctx, sql, iD)

	err := row.Scan(&exists)
	if err != nil {
		return false, errors.Wrap(err, "insurance_bundle: unable to check if execute_endorsement_request exists")
	}

	return exists, nil
}
