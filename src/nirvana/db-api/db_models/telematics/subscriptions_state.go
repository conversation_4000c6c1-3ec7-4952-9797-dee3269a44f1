// Code generated by SQLBoiler (https://github.com/volatiletech/sqlboiler). DO NOT EDIT.
// This file is meant to be re-generated in place and/or deleted at any time.

package telematics

import (
	"context"
	"database/sql"
	"fmt"
	"reflect"
	"strconv"
	"strings"
	"sync"
	"time"

	"github.com/friendsofgo/errors"
	"github.com/volatiletech/null/v8"
	"github.com/volatiletech/sqlboiler/v4/boil"
	"github.com/volatiletech/sqlboiler/v4/queries"
	"github.com/volatiletech/sqlboiler/v4/queries/qm"
	"github.com/volatiletech/sqlboiler/v4/queries/qmhelper"
	"github.com/volatiletech/sqlboiler/v4/types"
	"github.com/volatiletech/strmangle"
)

// SubscriptionsState is an object representing the database table.
type SubscriptionsState struct {
	SubscriptionID                       string      `boil:"subscription_id" json:"subscription_id" toml:"subscription_id" yaml:"subscription_id"`
	SubscriptionType                     string      `boil:"subscription_type" json:"subscription_type" toml:"subscription_type" yaml:"subscription_type"`
	Deliverable                          types.JSON  `boil:"deliverable" json:"deliverable" toml:"deliverable" yaml:"deliverable"`
	Delivered                            bool        `boil:"delivered" json:"delivered" toml:"delivered" yaml:"delivered"`
	LastAttemptedByJobRunID              null.String `boil:"last_attempted_by_job_run_id" json:"last_attempted_by_job_run_id,omitempty" toml:"last_attempted_by_job_run_id" yaml:"last_attempted_by_job_run_id,omitempty"`
	LastAttemptedBySubscriptionProcessor null.String `boil:"last_attempted_by_subscription_processor" json:"last_attempted_by_subscription_processor,omitempty" toml:"last_attempted_by_subscription_processor" yaml:"last_attempted_by_subscription_processor,omitempty"`
	FailedAttemptsCount                  int         `boil:"failed_attempts_count" json:"failed_attempts_count" toml:"failed_attempts_count" yaml:"failed_attempts_count"`
	CreatedAt                            time.Time   `boil:"created_at" json:"created_at" toml:"created_at" yaml:"created_at"`
	UpdatedAt                            time.Time   `boil:"updated_at" json:"updated_at" toml:"updated_at" yaml:"updated_at"`
	DeletedAt                            null.Time   `boil:"deleted_at" json:"deleted_at,omitempty" toml:"deleted_at" yaml:"deleted_at,omitempty"`

	R *subscriptionsStateR `boil:"" json:"" toml:"" yaml:""`
	L subscriptionsStateL  `boil:"-" json:"-" toml:"-" yaml:"-"`
}

var SubscriptionsStateColumns = struct {
	SubscriptionID                       string
	SubscriptionType                     string
	Deliverable                          string
	Delivered                            string
	LastAttemptedByJobRunID              string
	LastAttemptedBySubscriptionProcessor string
	FailedAttemptsCount                  string
	CreatedAt                            string
	UpdatedAt                            string
	DeletedAt                            string
}{
	SubscriptionID:                       "subscription_id",
	SubscriptionType:                     "subscription_type",
	Deliverable:                          "deliverable",
	Delivered:                            "delivered",
	LastAttemptedByJobRunID:              "last_attempted_by_job_run_id",
	LastAttemptedBySubscriptionProcessor: "last_attempted_by_subscription_processor",
	FailedAttemptsCount:                  "failed_attempts_count",
	CreatedAt:                            "created_at",
	UpdatedAt:                            "updated_at",
	DeletedAt:                            "deleted_at",
}

var SubscriptionsStateTableColumns = struct {
	SubscriptionID                       string
	SubscriptionType                     string
	Deliverable                          string
	Delivered                            string
	LastAttemptedByJobRunID              string
	LastAttemptedBySubscriptionProcessor string
	FailedAttemptsCount                  string
	CreatedAt                            string
	UpdatedAt                            string
	DeletedAt                            string
}{
	SubscriptionID:                       "subscriptions_state.subscription_id",
	SubscriptionType:                     "subscriptions_state.subscription_type",
	Deliverable:                          "subscriptions_state.deliverable",
	Delivered:                            "subscriptions_state.delivered",
	LastAttemptedByJobRunID:              "subscriptions_state.last_attempted_by_job_run_id",
	LastAttemptedBySubscriptionProcessor: "subscriptions_state.last_attempted_by_subscription_processor",
	FailedAttemptsCount:                  "subscriptions_state.failed_attempts_count",
	CreatedAt:                            "subscriptions_state.created_at",
	UpdatedAt:                            "subscriptions_state.updated_at",
	DeletedAt:                            "subscriptions_state.deleted_at",
}

// Generated where

var SubscriptionsStateWhere = struct {
	SubscriptionID                       whereHelperstring
	SubscriptionType                     whereHelperstring
	Deliverable                          whereHelpertypes_JSON
	Delivered                            whereHelperbool
	LastAttemptedByJobRunID              whereHelpernull_String
	LastAttemptedBySubscriptionProcessor whereHelpernull_String
	FailedAttemptsCount                  whereHelperint
	CreatedAt                            whereHelpertime_Time
	UpdatedAt                            whereHelpertime_Time
	DeletedAt                            whereHelpernull_Time
}{
	SubscriptionID:                       whereHelperstring{field: "\"telematics\".\"subscriptions_state\".\"subscription_id\""},
	SubscriptionType:                     whereHelperstring{field: "\"telematics\".\"subscriptions_state\".\"subscription_type\""},
	Deliverable:                          whereHelpertypes_JSON{field: "\"telematics\".\"subscriptions_state\".\"deliverable\""},
	Delivered:                            whereHelperbool{field: "\"telematics\".\"subscriptions_state\".\"delivered\""},
	LastAttemptedByJobRunID:              whereHelpernull_String{field: "\"telematics\".\"subscriptions_state\".\"last_attempted_by_job_run_id\""},
	LastAttemptedBySubscriptionProcessor: whereHelpernull_String{field: "\"telematics\".\"subscriptions_state\".\"last_attempted_by_subscription_processor\""},
	FailedAttemptsCount:                  whereHelperint{field: "\"telematics\".\"subscriptions_state\".\"failed_attempts_count\""},
	CreatedAt:                            whereHelpertime_Time{field: "\"telematics\".\"subscriptions_state\".\"created_at\""},
	UpdatedAt:                            whereHelpertime_Time{field: "\"telematics\".\"subscriptions_state\".\"updated_at\""},
	DeletedAt:                            whereHelpernull_Time{field: "\"telematics\".\"subscriptions_state\".\"deleted_at\""},
}

// SubscriptionsStateRels is where relationship names are stored.
var SubscriptionsStateRels = struct {
	SubscriptionSubscriptionRequests string
}{
	SubscriptionSubscriptionRequests: "SubscriptionSubscriptionRequests",
}

// subscriptionsStateR is where relationships are stored.
type subscriptionsStateR struct {
	SubscriptionSubscriptionRequests SubscriptionRequestSlice `boil:"SubscriptionSubscriptionRequests" json:"SubscriptionSubscriptionRequests" toml:"SubscriptionSubscriptionRequests" yaml:"SubscriptionSubscriptionRequests"`
}

// NewStruct creates a new relationship struct
func (*subscriptionsStateR) NewStruct() *subscriptionsStateR {
	return &subscriptionsStateR{}
}

// subscriptionsStateL is where Load methods for each relationship are stored.
type subscriptionsStateL struct{}

var (
	subscriptionsStateAllColumns            = []string{"subscription_id", "subscription_type", "deliverable", "delivered", "last_attempted_by_job_run_id", "last_attempted_by_subscription_processor", "failed_attempts_count", "created_at", "updated_at", "deleted_at"}
	subscriptionsStateColumnsWithoutDefault = []string{"subscription_id", "subscription_type", "deliverable", "delivered", "failed_attempts_count", "created_at", "updated_at"}
	subscriptionsStateColumnsWithDefault    = []string{"last_attempted_by_job_run_id", "last_attempted_by_subscription_processor", "deleted_at"}
	subscriptionsStatePrimaryKeyColumns     = []string{"subscription_id"}
	subscriptionsStateGeneratedColumns      = []string{}
)

type (
	// SubscriptionsStateSlice is an alias for a slice of pointers to SubscriptionsState.
	// This should almost always be used instead of []SubscriptionsState.
	SubscriptionsStateSlice []*SubscriptionsState
	// SubscriptionsStateHook is the signature for custom SubscriptionsState hook methods
	SubscriptionsStateHook func(context.Context, boil.ContextExecutor, *SubscriptionsState) error

	subscriptionsStateQuery struct {
		*queries.Query
	}
)

// Cache for insert, update and upsert
var (
	subscriptionsStateType                 = reflect.TypeOf(&SubscriptionsState{})
	subscriptionsStateMapping              = queries.MakeStructMapping(subscriptionsStateType)
	subscriptionsStatePrimaryKeyMapping, _ = queries.BindMapping(subscriptionsStateType, subscriptionsStateMapping, subscriptionsStatePrimaryKeyColumns)
	subscriptionsStateInsertCacheMut       sync.RWMutex
	subscriptionsStateInsertCache          = make(map[string]insertCache)
	subscriptionsStateUpdateCacheMut       sync.RWMutex
	subscriptionsStateUpdateCache          = make(map[string]updateCache)
	subscriptionsStateUpsertCacheMut       sync.RWMutex
	subscriptionsStateUpsertCache          = make(map[string]insertCache)
)

var (
	// Force time package dependency for automated UpdatedAt/CreatedAt.
	_ = time.Second
	// Force qmhelper dependency for where clause generation (which doesn't
	// always happen)
	_ = qmhelper.Where
)

var subscriptionsStateAfterSelectHooks []SubscriptionsStateHook

var subscriptionsStateBeforeInsertHooks []SubscriptionsStateHook
var subscriptionsStateAfterInsertHooks []SubscriptionsStateHook

var subscriptionsStateBeforeUpdateHooks []SubscriptionsStateHook
var subscriptionsStateAfterUpdateHooks []SubscriptionsStateHook

var subscriptionsStateBeforeDeleteHooks []SubscriptionsStateHook
var subscriptionsStateAfterDeleteHooks []SubscriptionsStateHook

var subscriptionsStateBeforeUpsertHooks []SubscriptionsStateHook
var subscriptionsStateAfterUpsertHooks []SubscriptionsStateHook

// doAfterSelectHooks executes all "after Select" hooks.
func (o *SubscriptionsState) doAfterSelectHooks(ctx context.Context, exec boil.ContextExecutor) (err error) {
	if boil.HooksAreSkipped(ctx) {
		return nil
	}

	for _, hook := range subscriptionsStateAfterSelectHooks {
		if err := hook(ctx, exec, o); err != nil {
			return err
		}
	}

	return nil
}

// doBeforeInsertHooks executes all "before insert" hooks.
func (o *SubscriptionsState) doBeforeInsertHooks(ctx context.Context, exec boil.ContextExecutor) (err error) {
	if boil.HooksAreSkipped(ctx) {
		return nil
	}

	for _, hook := range subscriptionsStateBeforeInsertHooks {
		if err := hook(ctx, exec, o); err != nil {
			return err
		}
	}

	return nil
}

// doAfterInsertHooks executes all "after Insert" hooks.
func (o *SubscriptionsState) doAfterInsertHooks(ctx context.Context, exec boil.ContextExecutor) (err error) {
	if boil.HooksAreSkipped(ctx) {
		return nil
	}

	for _, hook := range subscriptionsStateAfterInsertHooks {
		if err := hook(ctx, exec, o); err != nil {
			return err
		}
	}

	return nil
}

// doBeforeUpdateHooks executes all "before Update" hooks.
func (o *SubscriptionsState) doBeforeUpdateHooks(ctx context.Context, exec boil.ContextExecutor) (err error) {
	if boil.HooksAreSkipped(ctx) {
		return nil
	}

	for _, hook := range subscriptionsStateBeforeUpdateHooks {
		if err := hook(ctx, exec, o); err != nil {
			return err
		}
	}

	return nil
}

// doAfterUpdateHooks executes all "after Update" hooks.
func (o *SubscriptionsState) doAfterUpdateHooks(ctx context.Context, exec boil.ContextExecutor) (err error) {
	if boil.HooksAreSkipped(ctx) {
		return nil
	}

	for _, hook := range subscriptionsStateAfterUpdateHooks {
		if err := hook(ctx, exec, o); err != nil {
			return err
		}
	}

	return nil
}

// doBeforeDeleteHooks executes all "before Delete" hooks.
func (o *SubscriptionsState) doBeforeDeleteHooks(ctx context.Context, exec boil.ContextExecutor) (err error) {
	if boil.HooksAreSkipped(ctx) {
		return nil
	}

	for _, hook := range subscriptionsStateBeforeDeleteHooks {
		if err := hook(ctx, exec, o); err != nil {
			return err
		}
	}

	return nil
}

// doAfterDeleteHooks executes all "after Delete" hooks.
func (o *SubscriptionsState) doAfterDeleteHooks(ctx context.Context, exec boil.ContextExecutor) (err error) {
	if boil.HooksAreSkipped(ctx) {
		return nil
	}

	for _, hook := range subscriptionsStateAfterDeleteHooks {
		if err := hook(ctx, exec, o); err != nil {
			return err
		}
	}

	return nil
}

// doBeforeUpsertHooks executes all "before Upsert" hooks.
func (o *SubscriptionsState) doBeforeUpsertHooks(ctx context.Context, exec boil.ContextExecutor) (err error) {
	if boil.HooksAreSkipped(ctx) {
		return nil
	}

	for _, hook := range subscriptionsStateBeforeUpsertHooks {
		if err := hook(ctx, exec, o); err != nil {
			return err
		}
	}

	return nil
}

// doAfterUpsertHooks executes all "after Upsert" hooks.
func (o *SubscriptionsState) doAfterUpsertHooks(ctx context.Context, exec boil.ContextExecutor) (err error) {
	if boil.HooksAreSkipped(ctx) {
		return nil
	}

	for _, hook := range subscriptionsStateAfterUpsertHooks {
		if err := hook(ctx, exec, o); err != nil {
			return err
		}
	}

	return nil
}

// AddSubscriptionsStateHook registers your hook function for all future operations.
func AddSubscriptionsStateHook(hookPoint boil.HookPoint, subscriptionsStateHook SubscriptionsStateHook) {
	switch hookPoint {
	case boil.AfterSelectHook:
		subscriptionsStateAfterSelectHooks = append(subscriptionsStateAfterSelectHooks, subscriptionsStateHook)
	case boil.BeforeInsertHook:
		subscriptionsStateBeforeInsertHooks = append(subscriptionsStateBeforeInsertHooks, subscriptionsStateHook)
	case boil.AfterInsertHook:
		subscriptionsStateAfterInsertHooks = append(subscriptionsStateAfterInsertHooks, subscriptionsStateHook)
	case boil.BeforeUpdateHook:
		subscriptionsStateBeforeUpdateHooks = append(subscriptionsStateBeforeUpdateHooks, subscriptionsStateHook)
	case boil.AfterUpdateHook:
		subscriptionsStateAfterUpdateHooks = append(subscriptionsStateAfterUpdateHooks, subscriptionsStateHook)
	case boil.BeforeDeleteHook:
		subscriptionsStateBeforeDeleteHooks = append(subscriptionsStateBeforeDeleteHooks, subscriptionsStateHook)
	case boil.AfterDeleteHook:
		subscriptionsStateAfterDeleteHooks = append(subscriptionsStateAfterDeleteHooks, subscriptionsStateHook)
	case boil.BeforeUpsertHook:
		subscriptionsStateBeforeUpsertHooks = append(subscriptionsStateBeforeUpsertHooks, subscriptionsStateHook)
	case boil.AfterUpsertHook:
		subscriptionsStateAfterUpsertHooks = append(subscriptionsStateAfterUpsertHooks, subscriptionsStateHook)
	}
}

// One returns a single subscriptionsState record from the query.
func (q subscriptionsStateQuery) One(ctx context.Context, exec boil.ContextExecutor) (*SubscriptionsState, error) {
	o := &SubscriptionsState{}

	queries.SetLimit(q.Query, 1)

	err := q.Bind(ctx, exec, o)
	if err != nil {
		if errors.Is(err, sql.ErrNoRows) {
			return nil, sql.ErrNoRows
		}
		return nil, errors.Wrap(err, "telematics: failed to execute a one query for subscriptions_state")
	}

	if err := o.doAfterSelectHooks(ctx, exec); err != nil {
		return o, err
	}

	return o, nil
}

// All returns all SubscriptionsState records from the query.
func (q subscriptionsStateQuery) All(ctx context.Context, exec boil.ContextExecutor) (SubscriptionsStateSlice, error) {
	var o []*SubscriptionsState

	err := q.Bind(ctx, exec, &o)
	if err != nil {
		return nil, errors.Wrap(err, "telematics: failed to assign all query results to SubscriptionsState slice")
	}

	if len(subscriptionsStateAfterSelectHooks) != 0 {
		for _, obj := range o {
			if err := obj.doAfterSelectHooks(ctx, exec); err != nil {
				return o, err
			}
		}
	}

	return o, nil
}

// Count returns the count of all SubscriptionsState records in the query.
func (q subscriptionsStateQuery) Count(ctx context.Context, exec boil.ContextExecutor) (int64, error) {
	var count int64

	queries.SetSelect(q.Query, nil)
	queries.SetCount(q.Query)

	err := q.Query.QueryRowContext(ctx, exec).Scan(&count)
	if err != nil {
		return 0, errors.Wrap(err, "telematics: failed to count subscriptions_state rows")
	}

	return count, nil
}

// Exists checks if the row exists in the table.
func (q subscriptionsStateQuery) Exists(ctx context.Context, exec boil.ContextExecutor) (bool, error) {
	var count int64

	queries.SetSelect(q.Query, nil)
	queries.SetCount(q.Query)
	queries.SetLimit(q.Query, 1)

	err := q.Query.QueryRowContext(ctx, exec).Scan(&count)
	if err != nil {
		return false, errors.Wrap(err, "telematics: failed to check if subscriptions_state exists")
	}

	return count > 0, nil
}

// SubscriptionSubscriptionRequests retrieves all the subscription_request's SubscriptionRequests with an executor via subscription_id column.
func (o *SubscriptionsState) SubscriptionSubscriptionRequests(mods ...qm.QueryMod) subscriptionRequestQuery {
	var queryMods []qm.QueryMod
	if len(mods) != 0 {
		queryMods = append(queryMods, mods...)
	}

	queryMods = append(queryMods,
		qm.Where("\"telematics\".\"subscription_requests\".\"subscription_id\"=?", o.SubscriptionID),
	)

	return SubscriptionRequests(queryMods...)
}

// LoadSubscriptionSubscriptionRequests allows an eager lookup of values, cached into the
// loaded structs of the objects. This is for a 1-M or N-M relationship.
func (subscriptionsStateL) LoadSubscriptionSubscriptionRequests(ctx context.Context, e boil.ContextExecutor, singular bool, maybeSubscriptionsState interface{}, mods queries.Applicator) error {
	var slice []*SubscriptionsState
	var object *SubscriptionsState

	if singular {
		object = maybeSubscriptionsState.(*SubscriptionsState)
	} else {
		slice = *maybeSubscriptionsState.(*[]*SubscriptionsState)
	}

	args := make([]interface{}, 0, 1)
	if singular {
		if object.R == nil {
			object.R = &subscriptionsStateR{}
		}
		args = append(args, object.SubscriptionID)
	} else {
	Outer:
		for _, obj := range slice {
			if obj.R == nil {
				obj.R = &subscriptionsStateR{}
			}

			for _, a := range args {
				if queries.Equal(a, obj.SubscriptionID) {
					continue Outer
				}
			}

			args = append(args, obj.SubscriptionID)
		}
	}

	if len(args) == 0 {
		return nil
	}

	query := NewQuery(
		qm.From(`telematics.subscription_requests`),
		qm.WhereIn(`telematics.subscription_requests.subscription_id in ?`, args...),
	)
	if mods != nil {
		mods.Apply(query)
	}

	results, err := query.QueryContext(ctx, e)
	if err != nil {
		return errors.Wrap(err, "failed to eager load subscription_requests")
	}

	var resultSlice []*SubscriptionRequest
	if err = queries.Bind(results, &resultSlice); err != nil {
		return errors.Wrap(err, "failed to bind eager loaded slice subscription_requests")
	}

	if err = results.Close(); err != nil {
		return errors.Wrap(err, "failed to close results in eager load on subscription_requests")
	}
	if err = results.Err(); err != nil {
		return errors.Wrap(err, "error occurred during iteration of eager loaded relations for subscription_requests")
	}

	if len(subscriptionRequestAfterSelectHooks) != 0 {
		for _, obj := range resultSlice {
			if err := obj.doAfterSelectHooks(ctx, e); err != nil {
				return err
			}
		}
	}
	if singular {
		object.R.SubscriptionSubscriptionRequests = resultSlice
		for _, foreign := range resultSlice {
			if foreign.R == nil {
				foreign.R = &subscriptionRequestR{}
			}
			foreign.R.Subscription = object
		}
		return nil
	}

	for _, foreign := range resultSlice {
		for _, local := range slice {
			if queries.Equal(local.SubscriptionID, foreign.SubscriptionID) {
				local.R.SubscriptionSubscriptionRequests = append(local.R.SubscriptionSubscriptionRequests, foreign)
				if foreign.R == nil {
					foreign.R = &subscriptionRequestR{}
				}
				foreign.R.Subscription = local
				break
			}
		}
	}

	return nil
}

// AddSubscriptionSubscriptionRequests adds the given related objects to the existing relationships
// of the subscriptions_state, optionally inserting them as new records.
// Appends related to o.R.SubscriptionSubscriptionRequests.
// Sets related.R.Subscription appropriately.
func (o *SubscriptionsState) AddSubscriptionSubscriptionRequests(ctx context.Context, exec boil.ContextExecutor, insert bool, related ...*SubscriptionRequest) error {
	var err error
	for _, rel := range related {
		if insert {
			queries.Assign(&rel.SubscriptionID, o.SubscriptionID)
			if err = rel.Insert(ctx, exec, boil.Infer()); err != nil {
				return errors.Wrap(err, "failed to insert into foreign table")
			}
		} else {
			updateQuery := fmt.Sprintf(
				"UPDATE \"telematics\".\"subscription_requests\" SET %s WHERE %s",
				strmangle.SetParamNames("\"", "\"", 1, []string{"subscription_id"}),
				strmangle.WhereClause("\"", "\"", 2, subscriptionRequestPrimaryKeyColumns),
			)
			values := []interface{}{o.SubscriptionID, rel.RequestID}

			if boil.IsDebug(ctx) {
				writer := boil.DebugWriterFrom(ctx)
				fmt.Fprintln(writer, updateQuery)
				fmt.Fprintln(writer, values)
			}
			if _, err = exec.ExecContext(ctx, updateQuery, values...); err != nil {
				return errors.Wrap(err, "failed to update foreign table")
			}

			queries.Assign(&rel.SubscriptionID, o.SubscriptionID)
		}
	}

	if o.R == nil {
		o.R = &subscriptionsStateR{
			SubscriptionSubscriptionRequests: related,
		}
	} else {
		o.R.SubscriptionSubscriptionRequests = append(o.R.SubscriptionSubscriptionRequests, related...)
	}

	for _, rel := range related {
		if rel.R == nil {
			rel.R = &subscriptionRequestR{
				Subscription: o,
			}
		} else {
			rel.R.Subscription = o
		}
	}
	return nil
}

// SetSubscriptionSubscriptionRequests removes all previously related items of the
// subscriptions_state replacing them completely with the passed
// in related items, optionally inserting them as new records.
// Sets o.R.Subscription's SubscriptionSubscriptionRequests accordingly.
// Replaces o.R.SubscriptionSubscriptionRequests with related.
// Sets related.R.Subscription's SubscriptionSubscriptionRequests accordingly.
func (o *SubscriptionsState) SetSubscriptionSubscriptionRequests(ctx context.Context, exec boil.ContextExecutor, insert bool, related ...*SubscriptionRequest) error {
	query := "update \"telematics\".\"subscription_requests\" set \"subscription_id\" = null where \"subscription_id\" = $1"
	values := []interface{}{o.SubscriptionID}
	if boil.IsDebug(ctx) {
		writer := boil.DebugWriterFrom(ctx)
		fmt.Fprintln(writer, query)
		fmt.Fprintln(writer, values)
	}
	_, err := exec.ExecContext(ctx, query, values...)
	if err != nil {
		return errors.Wrap(err, "failed to remove relationships before set")
	}

	if o.R != nil {
		for _, rel := range o.R.SubscriptionSubscriptionRequests {
			queries.SetScanner(&rel.SubscriptionID, nil)
			if rel.R == nil {
				continue
			}

			rel.R.Subscription = nil
		}
		o.R.SubscriptionSubscriptionRequests = nil
	}

	return o.AddSubscriptionSubscriptionRequests(ctx, exec, insert, related...)
}

// RemoveSubscriptionSubscriptionRequests relationships from objects passed in.
// Removes related items from R.SubscriptionSubscriptionRequests (uses pointer comparison, removal does not keep order)
// Sets related.R.Subscription.
func (o *SubscriptionsState) RemoveSubscriptionSubscriptionRequests(ctx context.Context, exec boil.ContextExecutor, related ...*SubscriptionRequest) error {
	if len(related) == 0 {
		return nil
	}

	var err error
	for _, rel := range related {
		queries.SetScanner(&rel.SubscriptionID, nil)
		if rel.R != nil {
			rel.R.Subscription = nil
		}
		if _, err = rel.Update(ctx, exec, boil.Whitelist("subscription_id")); err != nil {
			return err
		}
	}
	if o.R == nil {
		return nil
	}

	for _, rel := range related {
		for i, ri := range o.R.SubscriptionSubscriptionRequests {
			if rel != ri {
				continue
			}

			ln := len(o.R.SubscriptionSubscriptionRequests)
			if ln > 1 && i < ln-1 {
				o.R.SubscriptionSubscriptionRequests[i] = o.R.SubscriptionSubscriptionRequests[ln-1]
			}
			o.R.SubscriptionSubscriptionRequests = o.R.SubscriptionSubscriptionRequests[:ln-1]
			break
		}
	}

	return nil
}

// SubscriptionsStates retrieves all the records using an executor.
func SubscriptionsStates(mods ...qm.QueryMod) subscriptionsStateQuery {
	mods = append(mods, qm.From("\"telematics\".\"subscriptions_state\""))
	q := NewQuery(mods...)
	if len(queries.GetSelect(q)) == 0 {
		queries.SetSelect(q, []string{"\"telematics\".\"subscriptions_state\".*"})
	}

	return subscriptionsStateQuery{q}
}

// FindSubscriptionsState retrieves a single record by ID with an executor.
// If selectCols is empty Find will return all columns.
func FindSubscriptionsState(ctx context.Context, exec boil.ContextExecutor, subscriptionID string, selectCols ...string) (*SubscriptionsState, error) {
	subscriptionsStateObj := &SubscriptionsState{}

	sel := "*"
	if len(selectCols) > 0 {
		sel = strings.Join(strmangle.IdentQuoteSlice(dialect.LQ, dialect.RQ, selectCols), ",")
	}
	query := fmt.Sprintf(
		"select %s from \"telematics\".\"subscriptions_state\" where \"subscription_id\"=$1", sel,
	)

	q := queries.Raw(query, subscriptionID)

	err := q.Bind(ctx, exec, subscriptionsStateObj)
	if err != nil {
		if errors.Is(err, sql.ErrNoRows) {
			return nil, sql.ErrNoRows
		}
		return nil, errors.Wrap(err, "telematics: unable to select from subscriptions_state")
	}

	if err = subscriptionsStateObj.doAfterSelectHooks(ctx, exec); err != nil {
		return subscriptionsStateObj, err
	}

	return subscriptionsStateObj, nil
}

// Insert a single record using an executor.
// See boil.Columns.InsertColumnSet documentation to understand column list inference for inserts.
func (o *SubscriptionsState) Insert(ctx context.Context, exec boil.ContextExecutor, columns boil.Columns) error {
	if o == nil {
		return errors.New("telematics: no subscriptions_state provided for insertion")
	}

	var err error
	if !boil.TimestampsAreSkipped(ctx) {
		currTime := time.Now().In(boil.GetLocation())

		if o.CreatedAt.IsZero() {
			o.CreatedAt = currTime
		}
		if o.UpdatedAt.IsZero() {
			o.UpdatedAt = currTime
		}
	}

	if err := o.doBeforeInsertHooks(ctx, exec); err != nil {
		return err
	}

	nzDefaults := queries.NonZeroDefaultSet(subscriptionsStateColumnsWithDefault, o)

	key := makeCacheKey(columns, nzDefaults)
	subscriptionsStateInsertCacheMut.RLock()
	cache, cached := subscriptionsStateInsertCache[key]
	subscriptionsStateInsertCacheMut.RUnlock()

	if !cached {
		wl, returnColumns := columns.InsertColumnSet(
			subscriptionsStateAllColumns,
			subscriptionsStateColumnsWithDefault,
			subscriptionsStateColumnsWithoutDefault,
			nzDefaults,
		)

		cache.valueMapping, err = queries.BindMapping(subscriptionsStateType, subscriptionsStateMapping, wl)
		if err != nil {
			return err
		}
		cache.retMapping, err = queries.BindMapping(subscriptionsStateType, subscriptionsStateMapping, returnColumns)
		if err != nil {
			return err
		}
		if len(wl) != 0 {
			cache.query = fmt.Sprintf("INSERT INTO \"telematics\".\"subscriptions_state\" (\"%s\") %%sVALUES (%s)%%s", strings.Join(wl, "\",\""), strmangle.Placeholders(dialect.UseIndexPlaceholders, len(wl), 1, 1))
		} else {
			cache.query = "INSERT INTO \"telematics\".\"subscriptions_state\" %sDEFAULT VALUES%s"
		}

		var queryOutput, queryReturning string

		if len(cache.retMapping) != 0 {
			queryReturning = fmt.Sprintf(" RETURNING \"%s\"", strings.Join(returnColumns, "\",\""))
		}

		cache.query = fmt.Sprintf(cache.query, queryOutput, queryReturning)
	}

	value := reflect.Indirect(reflect.ValueOf(o))
	vals := queries.ValuesFromMapping(value, cache.valueMapping)

	if boil.IsDebug(ctx) {
		writer := boil.DebugWriterFrom(ctx)
		fmt.Fprintln(writer, cache.query)
		fmt.Fprintln(writer, vals)
	}

	if len(cache.retMapping) != 0 {
		err = exec.QueryRowContext(ctx, cache.query, vals...).Scan(queries.PtrsFromMapping(value, cache.retMapping)...)
	} else {
		_, err = exec.ExecContext(ctx, cache.query, vals...)
	}

	if err != nil {
		return errors.Wrap(err, "telematics: unable to insert into subscriptions_state")
	}

	if !cached {
		subscriptionsStateInsertCacheMut.Lock()
		subscriptionsStateInsertCache[key] = cache
		subscriptionsStateInsertCacheMut.Unlock()
	}

	return o.doAfterInsertHooks(ctx, exec)
}

// Update uses an executor to update the SubscriptionsState.
// See boil.Columns.UpdateColumnSet documentation to understand column list inference for updates.
// Update does not automatically update the record in case of default values. Use .Reload() to refresh the records.
func (o *SubscriptionsState) Update(ctx context.Context, exec boil.ContextExecutor, columns boil.Columns) (int64, error) {
	if !boil.TimestampsAreSkipped(ctx) {
		currTime := time.Now().In(boil.GetLocation())

		o.UpdatedAt = currTime
	}

	var err error
	if err = o.doBeforeUpdateHooks(ctx, exec); err != nil {
		return 0, err
	}
	key := makeCacheKey(columns, nil)
	subscriptionsStateUpdateCacheMut.RLock()
	cache, cached := subscriptionsStateUpdateCache[key]
	subscriptionsStateUpdateCacheMut.RUnlock()

	if !cached {
		wl := columns.UpdateColumnSet(
			subscriptionsStateAllColumns,
			subscriptionsStatePrimaryKeyColumns,
		)

		if !columns.IsWhitelist() {
			wl = strmangle.SetComplement(wl, []string{"created_at"})
		}
		if len(wl) == 0 {
			return 0, errors.New("telematics: unable to update subscriptions_state, could not build whitelist")
		}

		cache.query = fmt.Sprintf("UPDATE \"telematics\".\"subscriptions_state\" SET %s WHERE %s",
			strmangle.SetParamNames("\"", "\"", 1, wl),
			strmangle.WhereClause("\"", "\"", len(wl)+1, subscriptionsStatePrimaryKeyColumns),
		)
		cache.valueMapping, err = queries.BindMapping(subscriptionsStateType, subscriptionsStateMapping, append(wl, subscriptionsStatePrimaryKeyColumns...))
		if err != nil {
			return 0, err
		}
	}

	values := queries.ValuesFromMapping(reflect.Indirect(reflect.ValueOf(o)), cache.valueMapping)

	if boil.IsDebug(ctx) {
		writer := boil.DebugWriterFrom(ctx)
		fmt.Fprintln(writer, cache.query)
		fmt.Fprintln(writer, values)
	}
	var result sql.Result
	result, err = exec.ExecContext(ctx, cache.query, values...)
	if err != nil {
		return 0, errors.Wrap(err, "telematics: unable to update subscriptions_state row")
	}

	rowsAff, err := result.RowsAffected()
	if err != nil {
		return 0, errors.Wrap(err, "telematics: failed to get rows affected by update for subscriptions_state")
	}

	if !cached {
		subscriptionsStateUpdateCacheMut.Lock()
		subscriptionsStateUpdateCache[key] = cache
		subscriptionsStateUpdateCacheMut.Unlock()
	}

	return rowsAff, o.doAfterUpdateHooks(ctx, exec)
}

// UpdateAll updates all rows with the specified column values.
func (q subscriptionsStateQuery) UpdateAll(ctx context.Context, exec boil.ContextExecutor, cols M) (int64, error) {
	queries.SetUpdate(q.Query, cols)

	result, err := q.Query.ExecContext(ctx, exec)
	if err != nil {
		return 0, errors.Wrap(err, "telematics: unable to update all for subscriptions_state")
	}

	rowsAff, err := result.RowsAffected()
	if err != nil {
		return 0, errors.Wrap(err, "telematics: unable to retrieve rows affected for subscriptions_state")
	}

	return rowsAff, nil
}

// UpdateAll updates all rows with the specified column values, using an executor.
func (o SubscriptionsStateSlice) UpdateAll(ctx context.Context, exec boil.ContextExecutor, cols M) (int64, error) {
	ln := int64(len(o))
	if ln == 0 {
		return 0, nil
	}

	if len(cols) == 0 {
		return 0, errors.New("telematics: update all requires at least one column argument")
	}

	colNames := make([]string, len(cols))
	args := make([]interface{}, len(cols))

	i := 0
	for name, value := range cols {
		colNames[i] = name
		args[i] = value
		i++
	}

	// Append all of the primary key values for each column
	for _, obj := range o {
		pkeyArgs := queries.ValuesFromMapping(reflect.Indirect(reflect.ValueOf(obj)), subscriptionsStatePrimaryKeyMapping)
		args = append(args, pkeyArgs...)
	}

	sql := fmt.Sprintf("UPDATE \"telematics\".\"subscriptions_state\" SET %s WHERE %s",
		strmangle.SetParamNames("\"", "\"", 1, colNames),
		strmangle.WhereClauseRepeated(string(dialect.LQ), string(dialect.RQ), len(colNames)+1, subscriptionsStatePrimaryKeyColumns, len(o)))

	if boil.IsDebug(ctx) {
		writer := boil.DebugWriterFrom(ctx)
		fmt.Fprintln(writer, sql)
		fmt.Fprintln(writer, args...)
	}
	result, err := exec.ExecContext(ctx, sql, args...)
	if err != nil {
		return 0, errors.Wrap(err, "telematics: unable to update all in subscriptionsState slice")
	}

	rowsAff, err := result.RowsAffected()
	if err != nil {
		return 0, errors.Wrap(err, "telematics: unable to retrieve rows affected all in update all subscriptionsState")
	}
	return rowsAff, nil
}

// Upsert attempts an insert using an executor, and does an update or ignore on conflict.
// See boil.Columns documentation for how to properly use updateColumns and insertColumns.
func (o *SubscriptionsState) Upsert(ctx context.Context, exec boil.ContextExecutor, updateOnConflict bool, conflictColumns []string, updateColumns, insertColumns boil.Columns) error {
	if o == nil {
		return errors.New("telematics: no subscriptions_state provided for upsert")
	}
	if !boil.TimestampsAreSkipped(ctx) {
		currTime := time.Now().In(boil.GetLocation())

		if o.CreatedAt.IsZero() {
			o.CreatedAt = currTime
		}
		o.UpdatedAt = currTime
	}

	if err := o.doBeforeUpsertHooks(ctx, exec); err != nil {
		return err
	}

	nzDefaults := queries.NonZeroDefaultSet(subscriptionsStateColumnsWithDefault, o)

	// Build cache key in-line uglily - mysql vs psql problems
	buf := strmangle.GetBuffer()
	if updateOnConflict {
		buf.WriteByte('t')
	} else {
		buf.WriteByte('f')
	}
	buf.WriteByte('.')
	for _, c := range conflictColumns {
		buf.WriteString(c)
	}
	buf.WriteByte('.')
	buf.WriteString(strconv.Itoa(updateColumns.Kind))
	for _, c := range updateColumns.Cols {
		buf.WriteString(c)
	}
	buf.WriteByte('.')
	buf.WriteString(strconv.Itoa(insertColumns.Kind))
	for _, c := range insertColumns.Cols {
		buf.WriteString(c)
	}
	buf.WriteByte('.')
	for _, c := range nzDefaults {
		buf.WriteString(c)
	}
	key := buf.String()
	strmangle.PutBuffer(buf)

	subscriptionsStateUpsertCacheMut.RLock()
	cache, cached := subscriptionsStateUpsertCache[key]
	subscriptionsStateUpsertCacheMut.RUnlock()

	var err error

	if !cached {
		insert, ret := insertColumns.InsertColumnSet(
			subscriptionsStateAllColumns,
			subscriptionsStateColumnsWithDefault,
			subscriptionsStateColumnsWithoutDefault,
			nzDefaults,
		)

		update := updateColumns.UpdateColumnSet(
			subscriptionsStateAllColumns,
			subscriptionsStatePrimaryKeyColumns,
		)

		if updateOnConflict && len(update) == 0 {
			return errors.New("telematics: unable to upsert subscriptions_state, could not build update column list")
		}

		conflict := conflictColumns
		if len(conflict) == 0 {
			conflict = make([]string, len(subscriptionsStatePrimaryKeyColumns))
			copy(conflict, subscriptionsStatePrimaryKeyColumns)
		}
		cache.query = buildUpsertQueryPostgres(dialect, "\"telematics\".\"subscriptions_state\"", updateOnConflict, ret, update, conflict, insert)

		cache.valueMapping, err = queries.BindMapping(subscriptionsStateType, subscriptionsStateMapping, insert)
		if err != nil {
			return err
		}
		if len(ret) != 0 {
			cache.retMapping, err = queries.BindMapping(subscriptionsStateType, subscriptionsStateMapping, ret)
			if err != nil {
				return err
			}
		}
	}

	value := reflect.Indirect(reflect.ValueOf(o))
	vals := queries.ValuesFromMapping(value, cache.valueMapping)
	var returns []interface{}
	if len(cache.retMapping) != 0 {
		returns = queries.PtrsFromMapping(value, cache.retMapping)
	}

	if boil.IsDebug(ctx) {
		writer := boil.DebugWriterFrom(ctx)
		fmt.Fprintln(writer, cache.query)
		fmt.Fprintln(writer, vals)
	}
	if len(cache.retMapping) != 0 {
		err = exec.QueryRowContext(ctx, cache.query, vals...).Scan(returns...)
		if errors.Is(err, sql.ErrNoRows) {
			err = nil // Postgres doesn't return anything when there's no update
		}
	} else {
		_, err = exec.ExecContext(ctx, cache.query, vals...)
	}
	if err != nil {
		return errors.Wrap(err, "telematics: unable to upsert subscriptions_state")
	}

	if !cached {
		subscriptionsStateUpsertCacheMut.Lock()
		subscriptionsStateUpsertCache[key] = cache
		subscriptionsStateUpsertCacheMut.Unlock()
	}

	return o.doAfterUpsertHooks(ctx, exec)
}

// Delete deletes a single SubscriptionsState record with an executor.
// Delete will match against the primary key column to find the record to delete.
func (o *SubscriptionsState) Delete(ctx context.Context, exec boil.ContextExecutor) (int64, error) {
	if o == nil {
		return 0, errors.New("telematics: no SubscriptionsState provided for delete")
	}

	if err := o.doBeforeDeleteHooks(ctx, exec); err != nil {
		return 0, err
	}

	args := queries.ValuesFromMapping(reflect.Indirect(reflect.ValueOf(o)), subscriptionsStatePrimaryKeyMapping)
	sql := "DELETE FROM \"telematics\".\"subscriptions_state\" WHERE \"subscription_id\"=$1"

	if boil.IsDebug(ctx) {
		writer := boil.DebugWriterFrom(ctx)
		fmt.Fprintln(writer, sql)
		fmt.Fprintln(writer, args...)
	}
	result, err := exec.ExecContext(ctx, sql, args...)
	if err != nil {
		return 0, errors.Wrap(err, "telematics: unable to delete from subscriptions_state")
	}

	rowsAff, err := result.RowsAffected()
	if err != nil {
		return 0, errors.Wrap(err, "telematics: failed to get rows affected by delete for subscriptions_state")
	}

	if err := o.doAfterDeleteHooks(ctx, exec); err != nil {
		return 0, err
	}

	return rowsAff, nil
}

// DeleteAll deletes all matching rows.
func (q subscriptionsStateQuery) DeleteAll(ctx context.Context, exec boil.ContextExecutor) (int64, error) {
	if q.Query == nil {
		return 0, errors.New("telematics: no subscriptionsStateQuery provided for delete all")
	}

	queries.SetDelete(q.Query)

	result, err := q.Query.ExecContext(ctx, exec)
	if err != nil {
		return 0, errors.Wrap(err, "telematics: unable to delete all from subscriptions_state")
	}

	rowsAff, err := result.RowsAffected()
	if err != nil {
		return 0, errors.Wrap(err, "telematics: failed to get rows affected by deleteall for subscriptions_state")
	}

	return rowsAff, nil
}

// DeleteAll deletes all rows in the slice, using an executor.
func (o SubscriptionsStateSlice) DeleteAll(ctx context.Context, exec boil.ContextExecutor) (int64, error) {
	if len(o) == 0 {
		return 0, nil
	}

	if len(subscriptionsStateBeforeDeleteHooks) != 0 {
		for _, obj := range o {
			if err := obj.doBeforeDeleteHooks(ctx, exec); err != nil {
				return 0, err
			}
		}
	}

	var args []interface{}
	for _, obj := range o {
		pkeyArgs := queries.ValuesFromMapping(reflect.Indirect(reflect.ValueOf(obj)), subscriptionsStatePrimaryKeyMapping)
		args = append(args, pkeyArgs...)
	}

	sql := "DELETE FROM \"telematics\".\"subscriptions_state\" WHERE " +
		strmangle.WhereClauseRepeated(string(dialect.LQ), string(dialect.RQ), 1, subscriptionsStatePrimaryKeyColumns, len(o))

	if boil.IsDebug(ctx) {
		writer := boil.DebugWriterFrom(ctx)
		fmt.Fprintln(writer, sql)
		fmt.Fprintln(writer, args)
	}
	result, err := exec.ExecContext(ctx, sql, args...)
	if err != nil {
		return 0, errors.Wrap(err, "telematics: unable to delete all from subscriptionsState slice")
	}

	rowsAff, err := result.RowsAffected()
	if err != nil {
		return 0, errors.Wrap(err, "telematics: failed to get rows affected by deleteall for subscriptions_state")
	}

	if len(subscriptionsStateAfterDeleteHooks) != 0 {
		for _, obj := range o {
			if err := obj.doAfterDeleteHooks(ctx, exec); err != nil {
				return 0, err
			}
		}
	}

	return rowsAff, nil
}

// Reload refetches the object from the database
// using the primary keys with an executor.
func (o *SubscriptionsState) Reload(ctx context.Context, exec boil.ContextExecutor) error {
	ret, err := FindSubscriptionsState(ctx, exec, o.SubscriptionID)
	if err != nil {
		return err
	}

	*o = *ret
	return nil
}

// ReloadAll refetches every row with matching primary key column values
// and overwrites the original object slice with the newly updated slice.
func (o *SubscriptionsStateSlice) ReloadAll(ctx context.Context, exec boil.ContextExecutor) error {
	if o == nil || len(*o) == 0 {
		return nil
	}

	slice := SubscriptionsStateSlice{}
	var args []interface{}
	for _, obj := range *o {
		pkeyArgs := queries.ValuesFromMapping(reflect.Indirect(reflect.ValueOf(obj)), subscriptionsStatePrimaryKeyMapping)
		args = append(args, pkeyArgs...)
	}

	sql := "SELECT \"telematics\".\"subscriptions_state\".* FROM \"telematics\".\"subscriptions_state\" WHERE " +
		strmangle.WhereClauseRepeated(string(dialect.LQ), string(dialect.RQ), 1, subscriptionsStatePrimaryKeyColumns, len(*o))

	q := queries.Raw(sql, args...)

	err := q.Bind(ctx, exec, &slice)
	if err != nil {
		return errors.Wrap(err, "telematics: unable to reload all in SubscriptionsStateSlice")
	}

	*o = slice

	return nil
}

// SubscriptionsStateExists checks if the SubscriptionsState row exists.
func SubscriptionsStateExists(ctx context.Context, exec boil.ContextExecutor, subscriptionID string) (bool, error) {
	var exists bool
	sql := "select exists(select 1 from \"telematics\".\"subscriptions_state\" where \"subscription_id\"=$1 limit 1)"

	if boil.IsDebug(ctx) {
		writer := boil.DebugWriterFrom(ctx)
		fmt.Fprintln(writer, sql)
		fmt.Fprintln(writer, subscriptionID)
	}
	row := exec.QueryRowContext(ctx, sql, subscriptionID)

	err := row.Scan(&exists)
	if err != nil {
		return false, errors.Wrap(err, "telematics: unable to check if subscriptions_state exists")
	}

	return exists, nil
}
