// Code generated by SQLBoiler (https://github.com/volatiletech/sqlboiler). DO NOT EDIT.
// This file is meant to be re-generated in place and/or deleted at any time.

package risk_metrics

import (
	"context"
	"database/sql"
	"fmt"
	"reflect"
	"strconv"
	"strings"
	"sync"
	"time"

	"github.com/friendsofgo/errors"
	"github.com/volatiletech/sqlboiler/v4/boil"
	"github.com/volatiletech/sqlboiler/v4/queries"
	"github.com/volatiletech/sqlboiler/v4/queries/qm"
	"github.com/volatiletech/sqlboiler/v4/queries/qmhelper"
	"github.com/volatiletech/sqlboiler/v4/types"
	"github.com/volatiletech/strmangle"
)

// EarnedPremium is an object representing the database table.
type EarnedPremium struct {
	ID           string        `boil:"id" json:"id" toml:"id" yaml:"id"`
	PolicyNumber string        `boil:"policy_number" json:"policy_number" toml:"policy_number" yaml:"policy_number"`
	Amount       types.Decimal `boil:"amount" json:"amount" toml:"amount" yaml:"amount"`
	BilledMonth  time.Time     `boil:"billed_month" json:"billed_month" toml:"billed_month" yaml:"billed_month"`
	EvaluatedAt  time.Time     `boil:"evaluated_at" json:"evaluated_at" toml:"evaluated_at" yaml:"evaluated_at"`
	Coverage     string        `boil:"coverage" json:"coverage" toml:"coverage" yaml:"coverage"`

	R *earnedPremiumR `boil:"" json:"" toml:"" yaml:""`
	L earnedPremiumL  `boil:"-" json:"-" toml:"-" yaml:"-"`
}

var EarnedPremiumColumns = struct {
	ID           string
	PolicyNumber string
	Amount       string
	BilledMonth  string
	EvaluatedAt  string
	Coverage     string
}{
	ID:           "id",
	PolicyNumber: "policy_number",
	Amount:       "amount",
	BilledMonth:  "billed_month",
	EvaluatedAt:  "evaluated_at",
	Coverage:     "coverage",
}

var EarnedPremiumTableColumns = struct {
	ID           string
	PolicyNumber string
	Amount       string
	BilledMonth  string
	EvaluatedAt  string
	Coverage     string
}{
	ID:           "earned_premium.id",
	PolicyNumber: "earned_premium.policy_number",
	Amount:       "earned_premium.amount",
	BilledMonth:  "earned_premium.billed_month",
	EvaluatedAt:  "earned_premium.evaluated_at",
	Coverage:     "earned_premium.coverage",
}

// Generated where

type whereHelpertypes_Decimal struct{ field string }

func (w whereHelpertypes_Decimal) EQ(x types.Decimal) qm.QueryMod {
	return qmhelper.Where(w.field, qmhelper.EQ, x)
}
func (w whereHelpertypes_Decimal) NEQ(x types.Decimal) qm.QueryMod {
	return qmhelper.Where(w.field, qmhelper.NEQ, x)
}
func (w whereHelpertypes_Decimal) LT(x types.Decimal) qm.QueryMod {
	return qmhelper.Where(w.field, qmhelper.LT, x)
}
func (w whereHelpertypes_Decimal) LTE(x types.Decimal) qm.QueryMod {
	return qmhelper.Where(w.field, qmhelper.LTE, x)
}
func (w whereHelpertypes_Decimal) GT(x types.Decimal) qm.QueryMod {
	return qmhelper.Where(w.field, qmhelper.GT, x)
}
func (w whereHelpertypes_Decimal) GTE(x types.Decimal) qm.QueryMod {
	return qmhelper.Where(w.field, qmhelper.GTE, x)
}

var EarnedPremiumWhere = struct {
	ID           whereHelperstring
	PolicyNumber whereHelperstring
	Amount       whereHelpertypes_Decimal
	BilledMonth  whereHelpertime_Time
	EvaluatedAt  whereHelpertime_Time
	Coverage     whereHelperstring
}{
	ID:           whereHelperstring{field: "\"risk_metrics\".\"earned_premium\".\"id\""},
	PolicyNumber: whereHelperstring{field: "\"risk_metrics\".\"earned_premium\".\"policy_number\""},
	Amount:       whereHelpertypes_Decimal{field: "\"risk_metrics\".\"earned_premium\".\"amount\""},
	BilledMonth:  whereHelpertime_Time{field: "\"risk_metrics\".\"earned_premium\".\"billed_month\""},
	EvaluatedAt:  whereHelpertime_Time{field: "\"risk_metrics\".\"earned_premium\".\"evaluated_at\""},
	Coverage:     whereHelperstring{field: "\"risk_metrics\".\"earned_premium\".\"coverage\""},
}

// EarnedPremiumRels is where relationship names are stored.
var EarnedPremiumRels = struct {
}{}

// earnedPremiumR is where relationships are stored.
type earnedPremiumR struct {
}

// NewStruct creates a new relationship struct
func (*earnedPremiumR) NewStruct() *earnedPremiumR {
	return &earnedPremiumR{}
}

// earnedPremiumL is where Load methods for each relationship are stored.
type earnedPremiumL struct{}

var (
	earnedPremiumAllColumns            = []string{"id", "policy_number", "amount", "billed_month", "evaluated_at", "coverage"}
	earnedPremiumColumnsWithoutDefault = []string{"id", "policy_number", "amount", "billed_month", "evaluated_at", "coverage"}
	earnedPremiumColumnsWithDefault    = []string{}
	earnedPremiumPrimaryKeyColumns     = []string{"id"}
	earnedPremiumGeneratedColumns      = []string{}
)

type (
	// EarnedPremiumSlice is an alias for a slice of pointers to EarnedPremium.
	// This should almost always be used instead of []EarnedPremium.
	EarnedPremiumSlice []*EarnedPremium
	// EarnedPremiumHook is the signature for custom EarnedPremium hook methods
	EarnedPremiumHook func(context.Context, boil.ContextExecutor, *EarnedPremium) error

	earnedPremiumQuery struct {
		*queries.Query
	}
)

// Cache for insert, update and upsert
var (
	earnedPremiumType                 = reflect.TypeOf(&EarnedPremium{})
	earnedPremiumMapping              = queries.MakeStructMapping(earnedPremiumType)
	earnedPremiumPrimaryKeyMapping, _ = queries.BindMapping(earnedPremiumType, earnedPremiumMapping, earnedPremiumPrimaryKeyColumns)
	earnedPremiumInsertCacheMut       sync.RWMutex
	earnedPremiumInsertCache          = make(map[string]insertCache)
	earnedPremiumUpdateCacheMut       sync.RWMutex
	earnedPremiumUpdateCache          = make(map[string]updateCache)
	earnedPremiumUpsertCacheMut       sync.RWMutex
	earnedPremiumUpsertCache          = make(map[string]insertCache)
)

var (
	// Force time package dependency for automated UpdatedAt/CreatedAt.
	_ = time.Second
	// Force qmhelper dependency for where clause generation (which doesn't
	// always happen)
	_ = qmhelper.Where
)

var earnedPremiumAfterSelectHooks []EarnedPremiumHook

var earnedPremiumBeforeInsertHooks []EarnedPremiumHook
var earnedPremiumAfterInsertHooks []EarnedPremiumHook

var earnedPremiumBeforeUpdateHooks []EarnedPremiumHook
var earnedPremiumAfterUpdateHooks []EarnedPremiumHook

var earnedPremiumBeforeDeleteHooks []EarnedPremiumHook
var earnedPremiumAfterDeleteHooks []EarnedPremiumHook

var earnedPremiumBeforeUpsertHooks []EarnedPremiumHook
var earnedPremiumAfterUpsertHooks []EarnedPremiumHook

// doAfterSelectHooks executes all "after Select" hooks.
func (o *EarnedPremium) doAfterSelectHooks(ctx context.Context, exec boil.ContextExecutor) (err error) {
	if boil.HooksAreSkipped(ctx) {
		return nil
	}

	for _, hook := range earnedPremiumAfterSelectHooks {
		if err := hook(ctx, exec, o); err != nil {
			return err
		}
	}

	return nil
}

// doBeforeInsertHooks executes all "before insert" hooks.
func (o *EarnedPremium) doBeforeInsertHooks(ctx context.Context, exec boil.ContextExecutor) (err error) {
	if boil.HooksAreSkipped(ctx) {
		return nil
	}

	for _, hook := range earnedPremiumBeforeInsertHooks {
		if err := hook(ctx, exec, o); err != nil {
			return err
		}
	}

	return nil
}

// doAfterInsertHooks executes all "after Insert" hooks.
func (o *EarnedPremium) doAfterInsertHooks(ctx context.Context, exec boil.ContextExecutor) (err error) {
	if boil.HooksAreSkipped(ctx) {
		return nil
	}

	for _, hook := range earnedPremiumAfterInsertHooks {
		if err := hook(ctx, exec, o); err != nil {
			return err
		}
	}

	return nil
}

// doBeforeUpdateHooks executes all "before Update" hooks.
func (o *EarnedPremium) doBeforeUpdateHooks(ctx context.Context, exec boil.ContextExecutor) (err error) {
	if boil.HooksAreSkipped(ctx) {
		return nil
	}

	for _, hook := range earnedPremiumBeforeUpdateHooks {
		if err := hook(ctx, exec, o); err != nil {
			return err
		}
	}

	return nil
}

// doAfterUpdateHooks executes all "after Update" hooks.
func (o *EarnedPremium) doAfterUpdateHooks(ctx context.Context, exec boil.ContextExecutor) (err error) {
	if boil.HooksAreSkipped(ctx) {
		return nil
	}

	for _, hook := range earnedPremiumAfterUpdateHooks {
		if err := hook(ctx, exec, o); err != nil {
			return err
		}
	}

	return nil
}

// doBeforeDeleteHooks executes all "before Delete" hooks.
func (o *EarnedPremium) doBeforeDeleteHooks(ctx context.Context, exec boil.ContextExecutor) (err error) {
	if boil.HooksAreSkipped(ctx) {
		return nil
	}

	for _, hook := range earnedPremiumBeforeDeleteHooks {
		if err := hook(ctx, exec, o); err != nil {
			return err
		}
	}

	return nil
}

// doAfterDeleteHooks executes all "after Delete" hooks.
func (o *EarnedPremium) doAfterDeleteHooks(ctx context.Context, exec boil.ContextExecutor) (err error) {
	if boil.HooksAreSkipped(ctx) {
		return nil
	}

	for _, hook := range earnedPremiumAfterDeleteHooks {
		if err := hook(ctx, exec, o); err != nil {
			return err
		}
	}

	return nil
}

// doBeforeUpsertHooks executes all "before Upsert" hooks.
func (o *EarnedPremium) doBeforeUpsertHooks(ctx context.Context, exec boil.ContextExecutor) (err error) {
	if boil.HooksAreSkipped(ctx) {
		return nil
	}

	for _, hook := range earnedPremiumBeforeUpsertHooks {
		if err := hook(ctx, exec, o); err != nil {
			return err
		}
	}

	return nil
}

// doAfterUpsertHooks executes all "after Upsert" hooks.
func (o *EarnedPremium) doAfterUpsertHooks(ctx context.Context, exec boil.ContextExecutor) (err error) {
	if boil.HooksAreSkipped(ctx) {
		return nil
	}

	for _, hook := range earnedPremiumAfterUpsertHooks {
		if err := hook(ctx, exec, o); err != nil {
			return err
		}
	}

	return nil
}

// AddEarnedPremiumHook registers your hook function for all future operations.
func AddEarnedPremiumHook(hookPoint boil.HookPoint, earnedPremiumHook EarnedPremiumHook) {
	switch hookPoint {
	case boil.AfterSelectHook:
		earnedPremiumAfterSelectHooks = append(earnedPremiumAfterSelectHooks, earnedPremiumHook)
	case boil.BeforeInsertHook:
		earnedPremiumBeforeInsertHooks = append(earnedPremiumBeforeInsertHooks, earnedPremiumHook)
	case boil.AfterInsertHook:
		earnedPremiumAfterInsertHooks = append(earnedPremiumAfterInsertHooks, earnedPremiumHook)
	case boil.BeforeUpdateHook:
		earnedPremiumBeforeUpdateHooks = append(earnedPremiumBeforeUpdateHooks, earnedPremiumHook)
	case boil.AfterUpdateHook:
		earnedPremiumAfterUpdateHooks = append(earnedPremiumAfterUpdateHooks, earnedPremiumHook)
	case boil.BeforeDeleteHook:
		earnedPremiumBeforeDeleteHooks = append(earnedPremiumBeforeDeleteHooks, earnedPremiumHook)
	case boil.AfterDeleteHook:
		earnedPremiumAfterDeleteHooks = append(earnedPremiumAfterDeleteHooks, earnedPremiumHook)
	case boil.BeforeUpsertHook:
		earnedPremiumBeforeUpsertHooks = append(earnedPremiumBeforeUpsertHooks, earnedPremiumHook)
	case boil.AfterUpsertHook:
		earnedPremiumAfterUpsertHooks = append(earnedPremiumAfterUpsertHooks, earnedPremiumHook)
	}
}

// One returns a single earnedPremium record from the query.
func (q earnedPremiumQuery) One(ctx context.Context, exec boil.ContextExecutor) (*EarnedPremium, error) {
	o := &EarnedPremium{}

	queries.SetLimit(q.Query, 1)

	err := q.Bind(ctx, exec, o)
	if err != nil {
		if errors.Is(err, sql.ErrNoRows) {
			return nil, sql.ErrNoRows
		}
		return nil, errors.Wrap(err, "risk_metrics: failed to execute a one query for earned_premium")
	}

	if err := o.doAfterSelectHooks(ctx, exec); err != nil {
		return o, err
	}

	return o, nil
}

// All returns all EarnedPremium records from the query.
func (q earnedPremiumQuery) All(ctx context.Context, exec boil.ContextExecutor) (EarnedPremiumSlice, error) {
	var o []*EarnedPremium

	err := q.Bind(ctx, exec, &o)
	if err != nil {
		return nil, errors.Wrap(err, "risk_metrics: failed to assign all query results to EarnedPremium slice")
	}

	if len(earnedPremiumAfterSelectHooks) != 0 {
		for _, obj := range o {
			if err := obj.doAfterSelectHooks(ctx, exec); err != nil {
				return o, err
			}
		}
	}

	return o, nil
}

// Count returns the count of all EarnedPremium records in the query.
func (q earnedPremiumQuery) Count(ctx context.Context, exec boil.ContextExecutor) (int64, error) {
	var count int64

	queries.SetSelect(q.Query, nil)
	queries.SetCount(q.Query)

	err := q.Query.QueryRowContext(ctx, exec).Scan(&count)
	if err != nil {
		return 0, errors.Wrap(err, "risk_metrics: failed to count earned_premium rows")
	}

	return count, nil
}

// Exists checks if the row exists in the table.
func (q earnedPremiumQuery) Exists(ctx context.Context, exec boil.ContextExecutor) (bool, error) {
	var count int64

	queries.SetSelect(q.Query, nil)
	queries.SetCount(q.Query)
	queries.SetLimit(q.Query, 1)

	err := q.Query.QueryRowContext(ctx, exec).Scan(&count)
	if err != nil {
		return false, errors.Wrap(err, "risk_metrics: failed to check if earned_premium exists")
	}

	return count > 0, nil
}

// EarnedPremia retrieves all the records using an executor.
func EarnedPremia(mods ...qm.QueryMod) earnedPremiumQuery {
	mods = append(mods, qm.From("\"risk_metrics\".\"earned_premium\""))
	q := NewQuery(mods...)
	if len(queries.GetSelect(q)) == 0 {
		queries.SetSelect(q, []string{"\"risk_metrics\".\"earned_premium\".*"})
	}

	return earnedPremiumQuery{q}
}

// FindEarnedPremium retrieves a single record by ID with an executor.
// If selectCols is empty Find will return all columns.
func FindEarnedPremium(ctx context.Context, exec boil.ContextExecutor, iD string, selectCols ...string) (*EarnedPremium, error) {
	earnedPremiumObj := &EarnedPremium{}

	sel := "*"
	if len(selectCols) > 0 {
		sel = strings.Join(strmangle.IdentQuoteSlice(dialect.LQ, dialect.RQ, selectCols), ",")
	}
	query := fmt.Sprintf(
		"select %s from \"risk_metrics\".\"earned_premium\" where \"id\"=$1", sel,
	)

	q := queries.Raw(query, iD)

	err := q.Bind(ctx, exec, earnedPremiumObj)
	if err != nil {
		if errors.Is(err, sql.ErrNoRows) {
			return nil, sql.ErrNoRows
		}
		return nil, errors.Wrap(err, "risk_metrics: unable to select from earned_premium")
	}

	if err = earnedPremiumObj.doAfterSelectHooks(ctx, exec); err != nil {
		return earnedPremiumObj, err
	}

	return earnedPremiumObj, nil
}

// Insert a single record using an executor.
// See boil.Columns.InsertColumnSet documentation to understand column list inference for inserts.
func (o *EarnedPremium) Insert(ctx context.Context, exec boil.ContextExecutor, columns boil.Columns) error {
	if o == nil {
		return errors.New("risk_metrics: no earned_premium provided for insertion")
	}

	var err error

	if err := o.doBeforeInsertHooks(ctx, exec); err != nil {
		return err
	}

	nzDefaults := queries.NonZeroDefaultSet(earnedPremiumColumnsWithDefault, o)

	key := makeCacheKey(columns, nzDefaults)
	earnedPremiumInsertCacheMut.RLock()
	cache, cached := earnedPremiumInsertCache[key]
	earnedPremiumInsertCacheMut.RUnlock()

	if !cached {
		wl, returnColumns := columns.InsertColumnSet(
			earnedPremiumAllColumns,
			earnedPremiumColumnsWithDefault,
			earnedPremiumColumnsWithoutDefault,
			nzDefaults,
		)

		cache.valueMapping, err = queries.BindMapping(earnedPremiumType, earnedPremiumMapping, wl)
		if err != nil {
			return err
		}
		cache.retMapping, err = queries.BindMapping(earnedPremiumType, earnedPremiumMapping, returnColumns)
		if err != nil {
			return err
		}
		if len(wl) != 0 {
			cache.query = fmt.Sprintf("INSERT INTO \"risk_metrics\".\"earned_premium\" (\"%s\") %%sVALUES (%s)%%s", strings.Join(wl, "\",\""), strmangle.Placeholders(dialect.UseIndexPlaceholders, len(wl), 1, 1))
		} else {
			cache.query = "INSERT INTO \"risk_metrics\".\"earned_premium\" %sDEFAULT VALUES%s"
		}

		var queryOutput, queryReturning string

		if len(cache.retMapping) != 0 {
			queryReturning = fmt.Sprintf(" RETURNING \"%s\"", strings.Join(returnColumns, "\",\""))
		}

		cache.query = fmt.Sprintf(cache.query, queryOutput, queryReturning)
	}

	value := reflect.Indirect(reflect.ValueOf(o))
	vals := queries.ValuesFromMapping(value, cache.valueMapping)

	if boil.IsDebug(ctx) {
		writer := boil.DebugWriterFrom(ctx)
		fmt.Fprintln(writer, cache.query)
		fmt.Fprintln(writer, vals)
	}

	if len(cache.retMapping) != 0 {
		err = exec.QueryRowContext(ctx, cache.query, vals...).Scan(queries.PtrsFromMapping(value, cache.retMapping)...)
	} else {
		_, err = exec.ExecContext(ctx, cache.query, vals...)
	}

	if err != nil {
		return errors.Wrap(err, "risk_metrics: unable to insert into earned_premium")
	}

	if !cached {
		earnedPremiumInsertCacheMut.Lock()
		earnedPremiumInsertCache[key] = cache
		earnedPremiumInsertCacheMut.Unlock()
	}

	return o.doAfterInsertHooks(ctx, exec)
}

// Update uses an executor to update the EarnedPremium.
// See boil.Columns.UpdateColumnSet documentation to understand column list inference for updates.
// Update does not automatically update the record in case of default values. Use .Reload() to refresh the records.
func (o *EarnedPremium) Update(ctx context.Context, exec boil.ContextExecutor, columns boil.Columns) (int64, error) {
	var err error
	if err = o.doBeforeUpdateHooks(ctx, exec); err != nil {
		return 0, err
	}
	key := makeCacheKey(columns, nil)
	earnedPremiumUpdateCacheMut.RLock()
	cache, cached := earnedPremiumUpdateCache[key]
	earnedPremiumUpdateCacheMut.RUnlock()

	if !cached {
		wl := columns.UpdateColumnSet(
			earnedPremiumAllColumns,
			earnedPremiumPrimaryKeyColumns,
		)

		if !columns.IsWhitelist() {
			wl = strmangle.SetComplement(wl, []string{"created_at"})
		}
		if len(wl) == 0 {
			return 0, errors.New("risk_metrics: unable to update earned_premium, could not build whitelist")
		}

		cache.query = fmt.Sprintf("UPDATE \"risk_metrics\".\"earned_premium\" SET %s WHERE %s",
			strmangle.SetParamNames("\"", "\"", 1, wl),
			strmangle.WhereClause("\"", "\"", len(wl)+1, earnedPremiumPrimaryKeyColumns),
		)
		cache.valueMapping, err = queries.BindMapping(earnedPremiumType, earnedPremiumMapping, append(wl, earnedPremiumPrimaryKeyColumns...))
		if err != nil {
			return 0, err
		}
	}

	values := queries.ValuesFromMapping(reflect.Indirect(reflect.ValueOf(o)), cache.valueMapping)

	if boil.IsDebug(ctx) {
		writer := boil.DebugWriterFrom(ctx)
		fmt.Fprintln(writer, cache.query)
		fmt.Fprintln(writer, values)
	}
	var result sql.Result
	result, err = exec.ExecContext(ctx, cache.query, values...)
	if err != nil {
		return 0, errors.Wrap(err, "risk_metrics: unable to update earned_premium row")
	}

	rowsAff, err := result.RowsAffected()
	if err != nil {
		return 0, errors.Wrap(err, "risk_metrics: failed to get rows affected by update for earned_premium")
	}

	if !cached {
		earnedPremiumUpdateCacheMut.Lock()
		earnedPremiumUpdateCache[key] = cache
		earnedPremiumUpdateCacheMut.Unlock()
	}

	return rowsAff, o.doAfterUpdateHooks(ctx, exec)
}

// UpdateAll updates all rows with the specified column values.
func (q earnedPremiumQuery) UpdateAll(ctx context.Context, exec boil.ContextExecutor, cols M) (int64, error) {
	queries.SetUpdate(q.Query, cols)

	result, err := q.Query.ExecContext(ctx, exec)
	if err != nil {
		return 0, errors.Wrap(err, "risk_metrics: unable to update all for earned_premium")
	}

	rowsAff, err := result.RowsAffected()
	if err != nil {
		return 0, errors.Wrap(err, "risk_metrics: unable to retrieve rows affected for earned_premium")
	}

	return rowsAff, nil
}

// UpdateAll updates all rows with the specified column values, using an executor.
func (o EarnedPremiumSlice) UpdateAll(ctx context.Context, exec boil.ContextExecutor, cols M) (int64, error) {
	ln := int64(len(o))
	if ln == 0 {
		return 0, nil
	}

	if len(cols) == 0 {
		return 0, errors.New("risk_metrics: update all requires at least one column argument")
	}

	colNames := make([]string, len(cols))
	args := make([]interface{}, len(cols))

	i := 0
	for name, value := range cols {
		colNames[i] = name
		args[i] = value
		i++
	}

	// Append all of the primary key values for each column
	for _, obj := range o {
		pkeyArgs := queries.ValuesFromMapping(reflect.Indirect(reflect.ValueOf(obj)), earnedPremiumPrimaryKeyMapping)
		args = append(args, pkeyArgs...)
	}

	sql := fmt.Sprintf("UPDATE \"risk_metrics\".\"earned_premium\" SET %s WHERE %s",
		strmangle.SetParamNames("\"", "\"", 1, colNames),
		strmangle.WhereClauseRepeated(string(dialect.LQ), string(dialect.RQ), len(colNames)+1, earnedPremiumPrimaryKeyColumns, len(o)))

	if boil.IsDebug(ctx) {
		writer := boil.DebugWriterFrom(ctx)
		fmt.Fprintln(writer, sql)
		fmt.Fprintln(writer, args...)
	}
	result, err := exec.ExecContext(ctx, sql, args...)
	if err != nil {
		return 0, errors.Wrap(err, "risk_metrics: unable to update all in earnedPremium slice")
	}

	rowsAff, err := result.RowsAffected()
	if err != nil {
		return 0, errors.Wrap(err, "risk_metrics: unable to retrieve rows affected all in update all earnedPremium")
	}
	return rowsAff, nil
}

// Upsert attempts an insert using an executor, and does an update or ignore on conflict.
// See boil.Columns documentation for how to properly use updateColumns and insertColumns.
func (o *EarnedPremium) Upsert(ctx context.Context, exec boil.ContextExecutor, updateOnConflict bool, conflictColumns []string, updateColumns, insertColumns boil.Columns) error {
	if o == nil {
		return errors.New("risk_metrics: no earned_premium provided for upsert")
	}

	if err := o.doBeforeUpsertHooks(ctx, exec); err != nil {
		return err
	}

	nzDefaults := queries.NonZeroDefaultSet(earnedPremiumColumnsWithDefault, o)

	// Build cache key in-line uglily - mysql vs psql problems
	buf := strmangle.GetBuffer()
	if updateOnConflict {
		buf.WriteByte('t')
	} else {
		buf.WriteByte('f')
	}
	buf.WriteByte('.')
	for _, c := range conflictColumns {
		buf.WriteString(c)
	}
	buf.WriteByte('.')
	buf.WriteString(strconv.Itoa(updateColumns.Kind))
	for _, c := range updateColumns.Cols {
		buf.WriteString(c)
	}
	buf.WriteByte('.')
	buf.WriteString(strconv.Itoa(insertColumns.Kind))
	for _, c := range insertColumns.Cols {
		buf.WriteString(c)
	}
	buf.WriteByte('.')
	for _, c := range nzDefaults {
		buf.WriteString(c)
	}
	key := buf.String()
	strmangle.PutBuffer(buf)

	earnedPremiumUpsertCacheMut.RLock()
	cache, cached := earnedPremiumUpsertCache[key]
	earnedPremiumUpsertCacheMut.RUnlock()

	var err error

	if !cached {
		insert, ret := insertColumns.InsertColumnSet(
			earnedPremiumAllColumns,
			earnedPremiumColumnsWithDefault,
			earnedPremiumColumnsWithoutDefault,
			nzDefaults,
		)

		update := updateColumns.UpdateColumnSet(
			earnedPremiumAllColumns,
			earnedPremiumPrimaryKeyColumns,
		)

		if updateOnConflict && len(update) == 0 {
			return errors.New("risk_metrics: unable to upsert earned_premium, could not build update column list")
		}

		conflict := conflictColumns
		if len(conflict) == 0 {
			conflict = make([]string, len(earnedPremiumPrimaryKeyColumns))
			copy(conflict, earnedPremiumPrimaryKeyColumns)
		}
		cache.query = buildUpsertQueryPostgres(dialect, "\"risk_metrics\".\"earned_premium\"", updateOnConflict, ret, update, conflict, insert)

		cache.valueMapping, err = queries.BindMapping(earnedPremiumType, earnedPremiumMapping, insert)
		if err != nil {
			return err
		}
		if len(ret) != 0 {
			cache.retMapping, err = queries.BindMapping(earnedPremiumType, earnedPremiumMapping, ret)
			if err != nil {
				return err
			}
		}
	}

	value := reflect.Indirect(reflect.ValueOf(o))
	vals := queries.ValuesFromMapping(value, cache.valueMapping)
	var returns []interface{}
	if len(cache.retMapping) != 0 {
		returns = queries.PtrsFromMapping(value, cache.retMapping)
	}

	if boil.IsDebug(ctx) {
		writer := boil.DebugWriterFrom(ctx)
		fmt.Fprintln(writer, cache.query)
		fmt.Fprintln(writer, vals)
	}
	if len(cache.retMapping) != 0 {
		err = exec.QueryRowContext(ctx, cache.query, vals...).Scan(returns...)
		if errors.Is(err, sql.ErrNoRows) {
			err = nil // Postgres doesn't return anything when there's no update
		}
	} else {
		_, err = exec.ExecContext(ctx, cache.query, vals...)
	}
	if err != nil {
		return errors.Wrap(err, "risk_metrics: unable to upsert earned_premium")
	}

	if !cached {
		earnedPremiumUpsertCacheMut.Lock()
		earnedPremiumUpsertCache[key] = cache
		earnedPremiumUpsertCacheMut.Unlock()
	}

	return o.doAfterUpsertHooks(ctx, exec)
}

// Delete deletes a single EarnedPremium record with an executor.
// Delete will match against the primary key column to find the record to delete.
func (o *EarnedPremium) Delete(ctx context.Context, exec boil.ContextExecutor) (int64, error) {
	if o == nil {
		return 0, errors.New("risk_metrics: no EarnedPremium provided for delete")
	}

	if err := o.doBeforeDeleteHooks(ctx, exec); err != nil {
		return 0, err
	}

	args := queries.ValuesFromMapping(reflect.Indirect(reflect.ValueOf(o)), earnedPremiumPrimaryKeyMapping)
	sql := "DELETE FROM \"risk_metrics\".\"earned_premium\" WHERE \"id\"=$1"

	if boil.IsDebug(ctx) {
		writer := boil.DebugWriterFrom(ctx)
		fmt.Fprintln(writer, sql)
		fmt.Fprintln(writer, args...)
	}
	result, err := exec.ExecContext(ctx, sql, args...)
	if err != nil {
		return 0, errors.Wrap(err, "risk_metrics: unable to delete from earned_premium")
	}

	rowsAff, err := result.RowsAffected()
	if err != nil {
		return 0, errors.Wrap(err, "risk_metrics: failed to get rows affected by delete for earned_premium")
	}

	if err := o.doAfterDeleteHooks(ctx, exec); err != nil {
		return 0, err
	}

	return rowsAff, nil
}

// DeleteAll deletes all matching rows.
func (q earnedPremiumQuery) DeleteAll(ctx context.Context, exec boil.ContextExecutor) (int64, error) {
	if q.Query == nil {
		return 0, errors.New("risk_metrics: no earnedPremiumQuery provided for delete all")
	}

	queries.SetDelete(q.Query)

	result, err := q.Query.ExecContext(ctx, exec)
	if err != nil {
		return 0, errors.Wrap(err, "risk_metrics: unable to delete all from earned_premium")
	}

	rowsAff, err := result.RowsAffected()
	if err != nil {
		return 0, errors.Wrap(err, "risk_metrics: failed to get rows affected by deleteall for earned_premium")
	}

	return rowsAff, nil
}

// DeleteAll deletes all rows in the slice, using an executor.
func (o EarnedPremiumSlice) DeleteAll(ctx context.Context, exec boil.ContextExecutor) (int64, error) {
	if len(o) == 0 {
		return 0, nil
	}

	if len(earnedPremiumBeforeDeleteHooks) != 0 {
		for _, obj := range o {
			if err := obj.doBeforeDeleteHooks(ctx, exec); err != nil {
				return 0, err
			}
		}
	}

	var args []interface{}
	for _, obj := range o {
		pkeyArgs := queries.ValuesFromMapping(reflect.Indirect(reflect.ValueOf(obj)), earnedPremiumPrimaryKeyMapping)
		args = append(args, pkeyArgs...)
	}

	sql := "DELETE FROM \"risk_metrics\".\"earned_premium\" WHERE " +
		strmangle.WhereClauseRepeated(string(dialect.LQ), string(dialect.RQ), 1, earnedPremiumPrimaryKeyColumns, len(o))

	if boil.IsDebug(ctx) {
		writer := boil.DebugWriterFrom(ctx)
		fmt.Fprintln(writer, sql)
		fmt.Fprintln(writer, args)
	}
	result, err := exec.ExecContext(ctx, sql, args...)
	if err != nil {
		return 0, errors.Wrap(err, "risk_metrics: unable to delete all from earnedPremium slice")
	}

	rowsAff, err := result.RowsAffected()
	if err != nil {
		return 0, errors.Wrap(err, "risk_metrics: failed to get rows affected by deleteall for earned_premium")
	}

	if len(earnedPremiumAfterDeleteHooks) != 0 {
		for _, obj := range o {
			if err := obj.doAfterDeleteHooks(ctx, exec); err != nil {
				return 0, err
			}
		}
	}

	return rowsAff, nil
}

// Reload refetches the object from the database
// using the primary keys with an executor.
func (o *EarnedPremium) Reload(ctx context.Context, exec boil.ContextExecutor) error {
	ret, err := FindEarnedPremium(ctx, exec, o.ID)
	if err != nil {
		return err
	}

	*o = *ret
	return nil
}

// ReloadAll refetches every row with matching primary key column values
// and overwrites the original object slice with the newly updated slice.
func (o *EarnedPremiumSlice) ReloadAll(ctx context.Context, exec boil.ContextExecutor) error {
	if o == nil || len(*o) == 0 {
		return nil
	}

	slice := EarnedPremiumSlice{}
	var args []interface{}
	for _, obj := range *o {
		pkeyArgs := queries.ValuesFromMapping(reflect.Indirect(reflect.ValueOf(obj)), earnedPremiumPrimaryKeyMapping)
		args = append(args, pkeyArgs...)
	}

	sql := "SELECT \"risk_metrics\".\"earned_premium\".* FROM \"risk_metrics\".\"earned_premium\" WHERE " +
		strmangle.WhereClauseRepeated(string(dialect.LQ), string(dialect.RQ), 1, earnedPremiumPrimaryKeyColumns, len(*o))

	q := queries.Raw(sql, args...)

	err := q.Bind(ctx, exec, &slice)
	if err != nil {
		return errors.Wrap(err, "risk_metrics: unable to reload all in EarnedPremiumSlice")
	}

	*o = slice

	return nil
}

// EarnedPremiumExists checks if the EarnedPremium row exists.
func EarnedPremiumExists(ctx context.Context, exec boil.ContextExecutor, iD string) (bool, error) {
	var exists bool
	sql := "select exists(select 1 from \"risk_metrics\".\"earned_premium\" where \"id\"=$1 limit 1)"

	if boil.IsDebug(ctx) {
		writer := boil.DebugWriterFrom(ctx)
		fmt.Fprintln(writer, sql)
		fmt.Fprintln(writer, iD)
	}
	row := exec.QueryRowContext(ctx, sql, iD)

	err := row.Scan(&exists)
	if err != nil {
		return false, errors.Wrap(err, "risk_metrics: unable to check if earned_premium exists")
	}

	return exists, nil
}
