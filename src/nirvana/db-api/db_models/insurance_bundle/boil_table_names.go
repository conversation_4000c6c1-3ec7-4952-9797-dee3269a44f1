// Code generated by SQLBoiler (https://github.com/volatiletech/sqlboiler). DO NOT EDIT.
// This file is meant to be re-generated in place and/or deleted at any time.

package insurance_bundle

var TableNames = struct {
	Change                    string
	ChangeContainer           string
	ExecuteEndorsementRequest string
	InsuranceBundle           string
	InsuranceBundleSegment    string
	Policy                    string
}{
	Change:                    "change",
	ChangeContainer:           "change_container",
	ExecuteEndorsementRequest: "execute_endorsement_request",
	InsuranceBundle:           "insurance_bundle",
	InsuranceBundleSegment:    "insurance_bundle_segment",
	Policy:                    "policy",
}
