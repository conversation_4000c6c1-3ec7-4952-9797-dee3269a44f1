// Code generated by SQLBoiler (https://github.com/volatiletech/sqlboiler). DO NOT EDIT.
// This file is meant to be re-generated in place and/or deleted at any time.

package db_models

import (
	"context"
	"database/sql"
	"fmt"
	"reflect"
	"strconv"
	"strings"
	"sync"
	"time"

	"github.com/friendsofgo/errors"
	"github.com/volatiletech/sqlboiler/v4/boil"
	"github.com/volatiletech/sqlboiler/v4/queries"
	"github.com/volatiletech/sqlboiler/v4/queries/qm"
	"github.com/volatiletech/sqlboiler/v4/queries/qmhelper"
	"github.com/volatiletech/strmangle"
)

// UsDotScore is an object representing the database table.
type UsDotScore struct {
	ID           string    `boil:"id" json:"id" toml:"id" yaml:"id"`
	SubmissionID string    `boil:"submission_id" json:"submission_id" toml:"submission_id" yaml:"submission_id"`
	DotNumber    int       `boil:"dot_number" json:"dot_number" toml:"dot_number" yaml:"dot_number"`
	UsDotScore   string    `boil:"us_dot_score" json:"us_dot_score" toml:"us_dot_score" yaml:"us_dot_score"`
	UpdatedAt    time.Time `boil:"updated_at" json:"updated_at" toml:"updated_at" yaml:"updated_at"`
	CreatedAt    time.Time `boil:"created_at" json:"created_at" toml:"created_at" yaml:"created_at"`

	R *usDotScoreR `boil:"" json:"" toml:"" yaml:""`
	L usDotScoreL  `boil:"-" json:"-" toml:"-" yaml:"-"`
}

var UsDotScoreColumns = struct {
	ID           string
	SubmissionID string
	DotNumber    string
	UsDotScore   string
	UpdatedAt    string
	CreatedAt    string
}{
	ID:           "id",
	SubmissionID: "submission_id",
	DotNumber:    "dot_number",
	UsDotScore:   "us_dot_score",
	UpdatedAt:    "updated_at",
	CreatedAt:    "created_at",
}

var UsDotScoreTableColumns = struct {
	ID           string
	SubmissionID string
	DotNumber    string
	UsDotScore   string
	UpdatedAt    string
	CreatedAt    string
}{
	ID:           "us_dot_score.id",
	SubmissionID: "us_dot_score.submission_id",
	DotNumber:    "us_dot_score.dot_number",
	UsDotScore:   "us_dot_score.us_dot_score",
	UpdatedAt:    "us_dot_score.updated_at",
	CreatedAt:    "us_dot_score.created_at",
}

// Generated where

var UsDotScoreWhere = struct {
	ID           whereHelperstring
	SubmissionID whereHelperstring
	DotNumber    whereHelperint
	UsDotScore   whereHelperstring
	UpdatedAt    whereHelpertime_Time
	CreatedAt    whereHelpertime_Time
}{
	ID:           whereHelperstring{field: "\"us_dot_score\".\"id\""},
	SubmissionID: whereHelperstring{field: "\"us_dot_score\".\"submission_id\""},
	DotNumber:    whereHelperint{field: "\"us_dot_score\".\"dot_number\""},
	UsDotScore:   whereHelperstring{field: "\"us_dot_score\".\"us_dot_score\""},
	UpdatedAt:    whereHelpertime_Time{field: "\"us_dot_score\".\"updated_at\""},
	CreatedAt:    whereHelpertime_Time{field: "\"us_dot_score\".\"created_at\""},
}

// UsDotScoreRels is where relationship names are stored.
var UsDotScoreRels = struct {
}{}

// usDotScoreR is where relationships are stored.
type usDotScoreR struct {
}

// NewStruct creates a new relationship struct
func (*usDotScoreR) NewStruct() *usDotScoreR {
	return &usDotScoreR{}
}

// usDotScoreL is where Load methods for each relationship are stored.
type usDotScoreL struct{}

var (
	usDotScoreAllColumns            = []string{"id", "submission_id", "dot_number", "us_dot_score", "updated_at", "created_at"}
	usDotScoreColumnsWithoutDefault = []string{"id", "submission_id", "dot_number", "us_dot_score", "updated_at", "created_at"}
	usDotScoreColumnsWithDefault    = []string{}
	usDotScorePrimaryKeyColumns     = []string{"id"}
	usDotScoreGeneratedColumns      = []string{}
)

type (
	// UsDotScoreSlice is an alias for a slice of pointers to UsDotScore.
	// This should almost always be used instead of []UsDotScore.
	UsDotScoreSlice []*UsDotScore
	// UsDotScoreHook is the signature for custom UsDotScore hook methods
	UsDotScoreHook func(context.Context, boil.ContextExecutor, *UsDotScore) error

	usDotScoreQuery struct {
		*queries.Query
	}
)

// Cache for insert, update and upsert
var (
	usDotScoreType                 = reflect.TypeOf(&UsDotScore{})
	usDotScoreMapping              = queries.MakeStructMapping(usDotScoreType)
	usDotScorePrimaryKeyMapping, _ = queries.BindMapping(usDotScoreType, usDotScoreMapping, usDotScorePrimaryKeyColumns)
	usDotScoreInsertCacheMut       sync.RWMutex
	usDotScoreInsertCache          = make(map[string]insertCache)
	usDotScoreUpdateCacheMut       sync.RWMutex
	usDotScoreUpdateCache          = make(map[string]updateCache)
	usDotScoreUpsertCacheMut       sync.RWMutex
	usDotScoreUpsertCache          = make(map[string]insertCache)
)

var (
	// Force time package dependency for automated UpdatedAt/CreatedAt.
	_ = time.Second
	// Force qmhelper dependency for where clause generation (which doesn't
	// always happen)
	_ = qmhelper.Where
)

var usDotScoreAfterSelectHooks []UsDotScoreHook

var usDotScoreBeforeInsertHooks []UsDotScoreHook
var usDotScoreAfterInsertHooks []UsDotScoreHook

var usDotScoreBeforeUpdateHooks []UsDotScoreHook
var usDotScoreAfterUpdateHooks []UsDotScoreHook

var usDotScoreBeforeDeleteHooks []UsDotScoreHook
var usDotScoreAfterDeleteHooks []UsDotScoreHook

var usDotScoreBeforeUpsertHooks []UsDotScoreHook
var usDotScoreAfterUpsertHooks []UsDotScoreHook

// doAfterSelectHooks executes all "after Select" hooks.
func (o *UsDotScore) doAfterSelectHooks(ctx context.Context, exec boil.ContextExecutor) (err error) {
	if boil.HooksAreSkipped(ctx) {
		return nil
	}

	for _, hook := range usDotScoreAfterSelectHooks {
		if err := hook(ctx, exec, o); err != nil {
			return err
		}
	}

	return nil
}

// doBeforeInsertHooks executes all "before insert" hooks.
func (o *UsDotScore) doBeforeInsertHooks(ctx context.Context, exec boil.ContextExecutor) (err error) {
	if boil.HooksAreSkipped(ctx) {
		return nil
	}

	for _, hook := range usDotScoreBeforeInsertHooks {
		if err := hook(ctx, exec, o); err != nil {
			return err
		}
	}

	return nil
}

// doAfterInsertHooks executes all "after Insert" hooks.
func (o *UsDotScore) doAfterInsertHooks(ctx context.Context, exec boil.ContextExecutor) (err error) {
	if boil.HooksAreSkipped(ctx) {
		return nil
	}

	for _, hook := range usDotScoreAfterInsertHooks {
		if err := hook(ctx, exec, o); err != nil {
			return err
		}
	}

	return nil
}

// doBeforeUpdateHooks executes all "before Update" hooks.
func (o *UsDotScore) doBeforeUpdateHooks(ctx context.Context, exec boil.ContextExecutor) (err error) {
	if boil.HooksAreSkipped(ctx) {
		return nil
	}

	for _, hook := range usDotScoreBeforeUpdateHooks {
		if err := hook(ctx, exec, o); err != nil {
			return err
		}
	}

	return nil
}

// doAfterUpdateHooks executes all "after Update" hooks.
func (o *UsDotScore) doAfterUpdateHooks(ctx context.Context, exec boil.ContextExecutor) (err error) {
	if boil.HooksAreSkipped(ctx) {
		return nil
	}

	for _, hook := range usDotScoreAfterUpdateHooks {
		if err := hook(ctx, exec, o); err != nil {
			return err
		}
	}

	return nil
}

// doBeforeDeleteHooks executes all "before Delete" hooks.
func (o *UsDotScore) doBeforeDeleteHooks(ctx context.Context, exec boil.ContextExecutor) (err error) {
	if boil.HooksAreSkipped(ctx) {
		return nil
	}

	for _, hook := range usDotScoreBeforeDeleteHooks {
		if err := hook(ctx, exec, o); err != nil {
			return err
		}
	}

	return nil
}

// doAfterDeleteHooks executes all "after Delete" hooks.
func (o *UsDotScore) doAfterDeleteHooks(ctx context.Context, exec boil.ContextExecutor) (err error) {
	if boil.HooksAreSkipped(ctx) {
		return nil
	}

	for _, hook := range usDotScoreAfterDeleteHooks {
		if err := hook(ctx, exec, o); err != nil {
			return err
		}
	}

	return nil
}

// doBeforeUpsertHooks executes all "before Upsert" hooks.
func (o *UsDotScore) doBeforeUpsertHooks(ctx context.Context, exec boil.ContextExecutor) (err error) {
	if boil.HooksAreSkipped(ctx) {
		return nil
	}

	for _, hook := range usDotScoreBeforeUpsertHooks {
		if err := hook(ctx, exec, o); err != nil {
			return err
		}
	}

	return nil
}

// doAfterUpsertHooks executes all "after Upsert" hooks.
func (o *UsDotScore) doAfterUpsertHooks(ctx context.Context, exec boil.ContextExecutor) (err error) {
	if boil.HooksAreSkipped(ctx) {
		return nil
	}

	for _, hook := range usDotScoreAfterUpsertHooks {
		if err := hook(ctx, exec, o); err != nil {
			return err
		}
	}

	return nil
}

// AddUsDotScoreHook registers your hook function for all future operations.
func AddUsDotScoreHook(hookPoint boil.HookPoint, usDotScoreHook UsDotScoreHook) {
	switch hookPoint {
	case boil.AfterSelectHook:
		usDotScoreAfterSelectHooks = append(usDotScoreAfterSelectHooks, usDotScoreHook)
	case boil.BeforeInsertHook:
		usDotScoreBeforeInsertHooks = append(usDotScoreBeforeInsertHooks, usDotScoreHook)
	case boil.AfterInsertHook:
		usDotScoreAfterInsertHooks = append(usDotScoreAfterInsertHooks, usDotScoreHook)
	case boil.BeforeUpdateHook:
		usDotScoreBeforeUpdateHooks = append(usDotScoreBeforeUpdateHooks, usDotScoreHook)
	case boil.AfterUpdateHook:
		usDotScoreAfterUpdateHooks = append(usDotScoreAfterUpdateHooks, usDotScoreHook)
	case boil.BeforeDeleteHook:
		usDotScoreBeforeDeleteHooks = append(usDotScoreBeforeDeleteHooks, usDotScoreHook)
	case boil.AfterDeleteHook:
		usDotScoreAfterDeleteHooks = append(usDotScoreAfterDeleteHooks, usDotScoreHook)
	case boil.BeforeUpsertHook:
		usDotScoreBeforeUpsertHooks = append(usDotScoreBeforeUpsertHooks, usDotScoreHook)
	case boil.AfterUpsertHook:
		usDotScoreAfterUpsertHooks = append(usDotScoreAfterUpsertHooks, usDotScoreHook)
	}
}

// One returns a single usDotScore record from the query.
func (q usDotScoreQuery) One(ctx context.Context, exec boil.ContextExecutor) (*UsDotScore, error) {
	o := &UsDotScore{}

	queries.SetLimit(q.Query, 1)

	err := q.Bind(ctx, exec, o)
	if err != nil {
		if errors.Is(err, sql.ErrNoRows) {
			return nil, sql.ErrNoRows
		}
		return nil, errors.Wrap(err, "db_models: failed to execute a one query for us_dot_score")
	}

	if err := o.doAfterSelectHooks(ctx, exec); err != nil {
		return o, err
	}

	return o, nil
}

// All returns all UsDotScore records from the query.
func (q usDotScoreQuery) All(ctx context.Context, exec boil.ContextExecutor) (UsDotScoreSlice, error) {
	var o []*UsDotScore

	err := q.Bind(ctx, exec, &o)
	if err != nil {
		return nil, errors.Wrap(err, "db_models: failed to assign all query results to UsDotScore slice")
	}

	if len(usDotScoreAfterSelectHooks) != 0 {
		for _, obj := range o {
			if err := obj.doAfterSelectHooks(ctx, exec); err != nil {
				return o, err
			}
		}
	}

	return o, nil
}

// Count returns the count of all UsDotScore records in the query.
func (q usDotScoreQuery) Count(ctx context.Context, exec boil.ContextExecutor) (int64, error) {
	var count int64

	queries.SetSelect(q.Query, nil)
	queries.SetCount(q.Query)

	err := q.Query.QueryRowContext(ctx, exec).Scan(&count)
	if err != nil {
		return 0, errors.Wrap(err, "db_models: failed to count us_dot_score rows")
	}

	return count, nil
}

// Exists checks if the row exists in the table.
func (q usDotScoreQuery) Exists(ctx context.Context, exec boil.ContextExecutor) (bool, error) {
	var count int64

	queries.SetSelect(q.Query, nil)
	queries.SetCount(q.Query)
	queries.SetLimit(q.Query, 1)

	err := q.Query.QueryRowContext(ctx, exec).Scan(&count)
	if err != nil {
		return false, errors.Wrap(err, "db_models: failed to check if us_dot_score exists")
	}

	return count > 0, nil
}

// UsDotScores retrieves all the records using an executor.
func UsDotScores(mods ...qm.QueryMod) usDotScoreQuery {
	mods = append(mods, qm.From("\"us_dot_score\""))
	q := NewQuery(mods...)
	if len(queries.GetSelect(q)) == 0 {
		queries.SetSelect(q, []string{"\"us_dot_score\".*"})
	}

	return usDotScoreQuery{q}
}

// FindUsDotScore retrieves a single record by ID with an executor.
// If selectCols is empty Find will return all columns.
func FindUsDotScore(ctx context.Context, exec boil.ContextExecutor, iD string, selectCols ...string) (*UsDotScore, error) {
	usDotScoreObj := &UsDotScore{}

	sel := "*"
	if len(selectCols) > 0 {
		sel = strings.Join(strmangle.IdentQuoteSlice(dialect.LQ, dialect.RQ, selectCols), ",")
	}
	query := fmt.Sprintf(
		"select %s from \"us_dot_score\" where \"id\"=$1", sel,
	)

	q := queries.Raw(query, iD)

	err := q.Bind(ctx, exec, usDotScoreObj)
	if err != nil {
		if errors.Is(err, sql.ErrNoRows) {
			return nil, sql.ErrNoRows
		}
		return nil, errors.Wrap(err, "db_models: unable to select from us_dot_score")
	}

	if err = usDotScoreObj.doAfterSelectHooks(ctx, exec); err != nil {
		return usDotScoreObj, err
	}

	return usDotScoreObj, nil
}

// Insert a single record using an executor.
// See boil.Columns.InsertColumnSet documentation to understand column list inference for inserts.
func (o *UsDotScore) Insert(ctx context.Context, exec boil.ContextExecutor, columns boil.Columns) error {
	if o == nil {
		return errors.New("db_models: no us_dot_score provided for insertion")
	}

	var err error
	if !boil.TimestampsAreSkipped(ctx) {
		currTime := time.Now().In(boil.GetLocation())

		if o.UpdatedAt.IsZero() {
			o.UpdatedAt = currTime
		}
		if o.CreatedAt.IsZero() {
			o.CreatedAt = currTime
		}
	}

	if err := o.doBeforeInsertHooks(ctx, exec); err != nil {
		return err
	}

	nzDefaults := queries.NonZeroDefaultSet(usDotScoreColumnsWithDefault, o)

	key := makeCacheKey(columns, nzDefaults)
	usDotScoreInsertCacheMut.RLock()
	cache, cached := usDotScoreInsertCache[key]
	usDotScoreInsertCacheMut.RUnlock()

	if !cached {
		wl, returnColumns := columns.InsertColumnSet(
			usDotScoreAllColumns,
			usDotScoreColumnsWithDefault,
			usDotScoreColumnsWithoutDefault,
			nzDefaults,
		)

		cache.valueMapping, err = queries.BindMapping(usDotScoreType, usDotScoreMapping, wl)
		if err != nil {
			return err
		}
		cache.retMapping, err = queries.BindMapping(usDotScoreType, usDotScoreMapping, returnColumns)
		if err != nil {
			return err
		}
		if len(wl) != 0 {
			cache.query = fmt.Sprintf("INSERT INTO \"us_dot_score\" (\"%s\") %%sVALUES (%s)%%s", strings.Join(wl, "\",\""), strmangle.Placeholders(dialect.UseIndexPlaceholders, len(wl), 1, 1))
		} else {
			cache.query = "INSERT INTO \"us_dot_score\" %sDEFAULT VALUES%s"
		}

		var queryOutput, queryReturning string

		if len(cache.retMapping) != 0 {
			queryReturning = fmt.Sprintf(" RETURNING \"%s\"", strings.Join(returnColumns, "\",\""))
		}

		cache.query = fmt.Sprintf(cache.query, queryOutput, queryReturning)
	}

	value := reflect.Indirect(reflect.ValueOf(o))
	vals := queries.ValuesFromMapping(value, cache.valueMapping)

	if boil.IsDebug(ctx) {
		writer := boil.DebugWriterFrom(ctx)
		fmt.Fprintln(writer, cache.query)
		fmt.Fprintln(writer, vals)
	}

	if len(cache.retMapping) != 0 {
		err = exec.QueryRowContext(ctx, cache.query, vals...).Scan(queries.PtrsFromMapping(value, cache.retMapping)...)
	} else {
		_, err = exec.ExecContext(ctx, cache.query, vals...)
	}

	if err != nil {
		return errors.Wrap(err, "db_models: unable to insert into us_dot_score")
	}

	if !cached {
		usDotScoreInsertCacheMut.Lock()
		usDotScoreInsertCache[key] = cache
		usDotScoreInsertCacheMut.Unlock()
	}

	return o.doAfterInsertHooks(ctx, exec)
}

// Update uses an executor to update the UsDotScore.
// See boil.Columns.UpdateColumnSet documentation to understand column list inference for updates.
// Update does not automatically update the record in case of default values. Use .Reload() to refresh the records.
func (o *UsDotScore) Update(ctx context.Context, exec boil.ContextExecutor, columns boil.Columns) (int64, error) {
	if !boil.TimestampsAreSkipped(ctx) {
		currTime := time.Now().In(boil.GetLocation())

		o.UpdatedAt = currTime
	}

	var err error
	if err = o.doBeforeUpdateHooks(ctx, exec); err != nil {
		return 0, err
	}
	key := makeCacheKey(columns, nil)
	usDotScoreUpdateCacheMut.RLock()
	cache, cached := usDotScoreUpdateCache[key]
	usDotScoreUpdateCacheMut.RUnlock()

	if !cached {
		wl := columns.UpdateColumnSet(
			usDotScoreAllColumns,
			usDotScorePrimaryKeyColumns,
		)

		if !columns.IsWhitelist() {
			wl = strmangle.SetComplement(wl, []string{"created_at"})
		}
		if len(wl) == 0 {
			return 0, errors.New("db_models: unable to update us_dot_score, could not build whitelist")
		}

		cache.query = fmt.Sprintf("UPDATE \"us_dot_score\" SET %s WHERE %s",
			strmangle.SetParamNames("\"", "\"", 1, wl),
			strmangle.WhereClause("\"", "\"", len(wl)+1, usDotScorePrimaryKeyColumns),
		)
		cache.valueMapping, err = queries.BindMapping(usDotScoreType, usDotScoreMapping, append(wl, usDotScorePrimaryKeyColumns...))
		if err != nil {
			return 0, err
		}
	}

	values := queries.ValuesFromMapping(reflect.Indirect(reflect.ValueOf(o)), cache.valueMapping)

	if boil.IsDebug(ctx) {
		writer := boil.DebugWriterFrom(ctx)
		fmt.Fprintln(writer, cache.query)
		fmt.Fprintln(writer, values)
	}
	var result sql.Result
	result, err = exec.ExecContext(ctx, cache.query, values...)
	if err != nil {
		return 0, errors.Wrap(err, "db_models: unable to update us_dot_score row")
	}

	rowsAff, err := result.RowsAffected()
	if err != nil {
		return 0, errors.Wrap(err, "db_models: failed to get rows affected by update for us_dot_score")
	}

	if !cached {
		usDotScoreUpdateCacheMut.Lock()
		usDotScoreUpdateCache[key] = cache
		usDotScoreUpdateCacheMut.Unlock()
	}

	return rowsAff, o.doAfterUpdateHooks(ctx, exec)
}

// UpdateAll updates all rows with the specified column values.
func (q usDotScoreQuery) UpdateAll(ctx context.Context, exec boil.ContextExecutor, cols M) (int64, error) {
	queries.SetUpdate(q.Query, cols)

	result, err := q.Query.ExecContext(ctx, exec)
	if err != nil {
		return 0, errors.Wrap(err, "db_models: unable to update all for us_dot_score")
	}

	rowsAff, err := result.RowsAffected()
	if err != nil {
		return 0, errors.Wrap(err, "db_models: unable to retrieve rows affected for us_dot_score")
	}

	return rowsAff, nil
}

// UpdateAll updates all rows with the specified column values, using an executor.
func (o UsDotScoreSlice) UpdateAll(ctx context.Context, exec boil.ContextExecutor, cols M) (int64, error) {
	ln := int64(len(o))
	if ln == 0 {
		return 0, nil
	}

	if len(cols) == 0 {
		return 0, errors.New("db_models: update all requires at least one column argument")
	}

	colNames := make([]string, len(cols))
	args := make([]interface{}, len(cols))

	i := 0
	for name, value := range cols {
		colNames[i] = name
		args[i] = value
		i++
	}

	// Append all of the primary key values for each column
	for _, obj := range o {
		pkeyArgs := queries.ValuesFromMapping(reflect.Indirect(reflect.ValueOf(obj)), usDotScorePrimaryKeyMapping)
		args = append(args, pkeyArgs...)
	}

	sql := fmt.Sprintf("UPDATE \"us_dot_score\" SET %s WHERE %s",
		strmangle.SetParamNames("\"", "\"", 1, colNames),
		strmangle.WhereClauseRepeated(string(dialect.LQ), string(dialect.RQ), len(colNames)+1, usDotScorePrimaryKeyColumns, len(o)))

	if boil.IsDebug(ctx) {
		writer := boil.DebugWriterFrom(ctx)
		fmt.Fprintln(writer, sql)
		fmt.Fprintln(writer, args...)
	}
	result, err := exec.ExecContext(ctx, sql, args...)
	if err != nil {
		return 0, errors.Wrap(err, "db_models: unable to update all in usDotScore slice")
	}

	rowsAff, err := result.RowsAffected()
	if err != nil {
		return 0, errors.Wrap(err, "db_models: unable to retrieve rows affected all in update all usDotScore")
	}
	return rowsAff, nil
}

// Upsert attempts an insert using an executor, and does an update or ignore on conflict.
// See boil.Columns documentation for how to properly use updateColumns and insertColumns.
func (o *UsDotScore) Upsert(ctx context.Context, exec boil.ContextExecutor, updateOnConflict bool, conflictColumns []string, updateColumns, insertColumns boil.Columns) error {
	if o == nil {
		return errors.New("db_models: no us_dot_score provided for upsert")
	}
	if !boil.TimestampsAreSkipped(ctx) {
		currTime := time.Now().In(boil.GetLocation())

		o.UpdatedAt = currTime
		if o.CreatedAt.IsZero() {
			o.CreatedAt = currTime
		}
	}

	if err := o.doBeforeUpsertHooks(ctx, exec); err != nil {
		return err
	}

	nzDefaults := queries.NonZeroDefaultSet(usDotScoreColumnsWithDefault, o)

	// Build cache key in-line uglily - mysql vs psql problems
	buf := strmangle.GetBuffer()
	if updateOnConflict {
		buf.WriteByte('t')
	} else {
		buf.WriteByte('f')
	}
	buf.WriteByte('.')
	for _, c := range conflictColumns {
		buf.WriteString(c)
	}
	buf.WriteByte('.')
	buf.WriteString(strconv.Itoa(updateColumns.Kind))
	for _, c := range updateColumns.Cols {
		buf.WriteString(c)
	}
	buf.WriteByte('.')
	buf.WriteString(strconv.Itoa(insertColumns.Kind))
	for _, c := range insertColumns.Cols {
		buf.WriteString(c)
	}
	buf.WriteByte('.')
	for _, c := range nzDefaults {
		buf.WriteString(c)
	}
	key := buf.String()
	strmangle.PutBuffer(buf)

	usDotScoreUpsertCacheMut.RLock()
	cache, cached := usDotScoreUpsertCache[key]
	usDotScoreUpsertCacheMut.RUnlock()

	var err error

	if !cached {
		insert, ret := insertColumns.InsertColumnSet(
			usDotScoreAllColumns,
			usDotScoreColumnsWithDefault,
			usDotScoreColumnsWithoutDefault,
			nzDefaults,
		)

		update := updateColumns.UpdateColumnSet(
			usDotScoreAllColumns,
			usDotScorePrimaryKeyColumns,
		)

		if updateOnConflict && len(update) == 0 {
			return errors.New("db_models: unable to upsert us_dot_score, could not build update column list")
		}

		conflict := conflictColumns
		if len(conflict) == 0 {
			conflict = make([]string, len(usDotScorePrimaryKeyColumns))
			copy(conflict, usDotScorePrimaryKeyColumns)
		}
		cache.query = buildUpsertQueryPostgres(dialect, "\"us_dot_score\"", updateOnConflict, ret, update, conflict, insert)

		cache.valueMapping, err = queries.BindMapping(usDotScoreType, usDotScoreMapping, insert)
		if err != nil {
			return err
		}
		if len(ret) != 0 {
			cache.retMapping, err = queries.BindMapping(usDotScoreType, usDotScoreMapping, ret)
			if err != nil {
				return err
			}
		}
	}

	value := reflect.Indirect(reflect.ValueOf(o))
	vals := queries.ValuesFromMapping(value, cache.valueMapping)
	var returns []interface{}
	if len(cache.retMapping) != 0 {
		returns = queries.PtrsFromMapping(value, cache.retMapping)
	}

	if boil.IsDebug(ctx) {
		writer := boil.DebugWriterFrom(ctx)
		fmt.Fprintln(writer, cache.query)
		fmt.Fprintln(writer, vals)
	}
	if len(cache.retMapping) != 0 {
		err = exec.QueryRowContext(ctx, cache.query, vals...).Scan(returns...)
		if errors.Is(err, sql.ErrNoRows) {
			err = nil // Postgres doesn't return anything when there's no update
		}
	} else {
		_, err = exec.ExecContext(ctx, cache.query, vals...)
	}
	if err != nil {
		return errors.Wrap(err, "db_models: unable to upsert us_dot_score")
	}

	if !cached {
		usDotScoreUpsertCacheMut.Lock()
		usDotScoreUpsertCache[key] = cache
		usDotScoreUpsertCacheMut.Unlock()
	}

	return o.doAfterUpsertHooks(ctx, exec)
}

// Delete deletes a single UsDotScore record with an executor.
// Delete will match against the primary key column to find the record to delete.
func (o *UsDotScore) Delete(ctx context.Context, exec boil.ContextExecutor) (int64, error) {
	if o == nil {
		return 0, errors.New("db_models: no UsDotScore provided for delete")
	}

	if err := o.doBeforeDeleteHooks(ctx, exec); err != nil {
		return 0, err
	}

	args := queries.ValuesFromMapping(reflect.Indirect(reflect.ValueOf(o)), usDotScorePrimaryKeyMapping)
	sql := "DELETE FROM \"us_dot_score\" WHERE \"id\"=$1"

	if boil.IsDebug(ctx) {
		writer := boil.DebugWriterFrom(ctx)
		fmt.Fprintln(writer, sql)
		fmt.Fprintln(writer, args...)
	}
	result, err := exec.ExecContext(ctx, sql, args...)
	if err != nil {
		return 0, errors.Wrap(err, "db_models: unable to delete from us_dot_score")
	}

	rowsAff, err := result.RowsAffected()
	if err != nil {
		return 0, errors.Wrap(err, "db_models: failed to get rows affected by delete for us_dot_score")
	}

	if err := o.doAfterDeleteHooks(ctx, exec); err != nil {
		return 0, err
	}

	return rowsAff, nil
}

// DeleteAll deletes all matching rows.
func (q usDotScoreQuery) DeleteAll(ctx context.Context, exec boil.ContextExecutor) (int64, error) {
	if q.Query == nil {
		return 0, errors.New("db_models: no usDotScoreQuery provided for delete all")
	}

	queries.SetDelete(q.Query)

	result, err := q.Query.ExecContext(ctx, exec)
	if err != nil {
		return 0, errors.Wrap(err, "db_models: unable to delete all from us_dot_score")
	}

	rowsAff, err := result.RowsAffected()
	if err != nil {
		return 0, errors.Wrap(err, "db_models: failed to get rows affected by deleteall for us_dot_score")
	}

	return rowsAff, nil
}

// DeleteAll deletes all rows in the slice, using an executor.
func (o UsDotScoreSlice) DeleteAll(ctx context.Context, exec boil.ContextExecutor) (int64, error) {
	if len(o) == 0 {
		return 0, nil
	}

	if len(usDotScoreBeforeDeleteHooks) != 0 {
		for _, obj := range o {
			if err := obj.doBeforeDeleteHooks(ctx, exec); err != nil {
				return 0, err
			}
		}
	}

	var args []interface{}
	for _, obj := range o {
		pkeyArgs := queries.ValuesFromMapping(reflect.Indirect(reflect.ValueOf(obj)), usDotScorePrimaryKeyMapping)
		args = append(args, pkeyArgs...)
	}

	sql := "DELETE FROM \"us_dot_score\" WHERE " +
		strmangle.WhereClauseRepeated(string(dialect.LQ), string(dialect.RQ), 1, usDotScorePrimaryKeyColumns, len(o))

	if boil.IsDebug(ctx) {
		writer := boil.DebugWriterFrom(ctx)
		fmt.Fprintln(writer, sql)
		fmt.Fprintln(writer, args)
	}
	result, err := exec.ExecContext(ctx, sql, args...)
	if err != nil {
		return 0, errors.Wrap(err, "db_models: unable to delete all from usDotScore slice")
	}

	rowsAff, err := result.RowsAffected()
	if err != nil {
		return 0, errors.Wrap(err, "db_models: failed to get rows affected by deleteall for us_dot_score")
	}

	if len(usDotScoreAfterDeleteHooks) != 0 {
		for _, obj := range o {
			if err := obj.doAfterDeleteHooks(ctx, exec); err != nil {
				return 0, err
			}
		}
	}

	return rowsAff, nil
}

// Reload refetches the object from the database
// using the primary keys with an executor.
func (o *UsDotScore) Reload(ctx context.Context, exec boil.ContextExecutor) error {
	ret, err := FindUsDotScore(ctx, exec, o.ID)
	if err != nil {
		return err
	}

	*o = *ret
	return nil
}

// ReloadAll refetches every row with matching primary key column values
// and overwrites the original object slice with the newly updated slice.
func (o *UsDotScoreSlice) ReloadAll(ctx context.Context, exec boil.ContextExecutor) error {
	if o == nil || len(*o) == 0 {
		return nil
	}

	slice := UsDotScoreSlice{}
	var args []interface{}
	for _, obj := range *o {
		pkeyArgs := queries.ValuesFromMapping(reflect.Indirect(reflect.ValueOf(obj)), usDotScorePrimaryKeyMapping)
		args = append(args, pkeyArgs...)
	}

	sql := "SELECT \"us_dot_score\".* FROM \"us_dot_score\" WHERE " +
		strmangle.WhereClauseRepeated(string(dialect.LQ), string(dialect.RQ), 1, usDotScorePrimaryKeyColumns, len(*o))

	q := queries.Raw(sql, args...)

	err := q.Bind(ctx, exec, &slice)
	if err != nil {
		return errors.Wrap(err, "db_models: unable to reload all in UsDotScoreSlice")
	}

	*o = slice

	return nil
}

// UsDotScoreExists checks if the UsDotScore row exists.
func UsDotScoreExists(ctx context.Context, exec boil.ContextExecutor, iD string) (bool, error) {
	var exists bool
	sql := "select exists(select 1 from \"us_dot_score\" where \"id\"=$1 limit 1)"

	if boil.IsDebug(ctx) {
		writer := boil.DebugWriterFrom(ctx)
		fmt.Fprintln(writer, sql)
		fmt.Fprintln(writer, iD)
	}
	row := exec.QueryRowContext(ctx, sql, iD)

	err := row.Scan(&exists)
	if err != nil {
		return false, errors.Wrap(err, "db_models: unable to check if us_dot_score exists")
	}

	return exists, nil
}
