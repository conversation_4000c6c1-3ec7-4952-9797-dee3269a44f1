// Code generated by SQLBoiler (https://github.com/volatiletech/sqlboiler). DO NOT EDIT.
// This file is meant to be re-generated in place and/or deleted at any time.

package telematics

import (
	"context"
	"database/sql"
	"fmt"
	"reflect"
	"strconv"
	"strings"
	"sync"
	"time"

	"github.com/friendsofgo/errors"
	"github.com/volatiletech/null/v8"
	"github.com/volatiletech/sqlboiler/v4/boil"
	"github.com/volatiletech/sqlboiler/v4/queries"
	"github.com/volatiletech/sqlboiler/v4/queries/qm"
	"github.com/volatiletech/sqlboiler/v4/queries/qmhelper"
	"github.com/volatiletech/strmangle"
)

// CMTScoring is an object representing the database table.
type CMTScoring struct {
	HandleID              string      `boil:"handle_id" json:"handle_id" toml:"handle_id" yaml:"handle_id"`
	StartTime             time.Time   `boil:"start_time" json:"start_time" toml:"start_time" yaml:"start_time"`
	EndTime               time.Time   `boil:"end_time" json:"end_time" toml:"end_time" yaml:"end_time"`
	InputS3URI            string      `boil:"input_s3_uri" json:"input_s3_uri" toml:"input_s3_uri" yaml:"input_s3_uri"`
	DataUploadedBy        string      `boil:"data_uploaded_by" json:"data_uploaded_by" toml:"data_uploaded_by" yaml:"data_uploaded_by"`
	UploadJobTriggeredBy  null.String `boil:"upload_job_triggered_by" json:"upload_job_triggered_by,omitempty" toml:"upload_job_triggered_by" yaml:"upload_job_triggered_by,omitempty"`
	ScoredBy              null.String `boil:"scored_by" json:"scored_by,omitempty" toml:"scored_by" yaml:"scored_by,omitempty"`
	ScoringJobID          null.String `boil:"scoring_job_id" json:"scoring_job_id,omitempty" toml:"scoring_job_id" yaml:"scoring_job_id,omitempty"`
	ScoringState          string      `boil:"scoring_state" json:"scoring_state" toml:"scoring_state" yaml:"scoring_state"`
	FailureCount          int         `boil:"failure_count" json:"failure_count" toml:"failure_count" yaml:"failure_count"`
	OutputS3URI           null.String `boil:"output_s3_uri" json:"output_s3_uri,omitempty" toml:"output_s3_uri" yaml:"output_s3_uri,omitempty"`
	UploadJobID           null.String `boil:"upload_job_id" json:"upload_job_id,omitempty" toml:"upload_job_id" yaml:"upload_job_id,omitempty"`
	ScoringJobTriggeredBy null.String `boil:"scoring_job_triggered_by" json:"scoring_job_triggered_by,omitempty" toml:"scoring_job_triggered_by" yaml:"scoring_job_triggered_by,omitempty"`

	R *cmtScoringR `boil:"" json:"" toml:"" yaml:""`
	L cmtScoringL  `boil:"-" json:"-" toml:"-" yaml:"-"`
}

var CMTScoringColumns = struct {
	HandleID              string
	StartTime             string
	EndTime               string
	InputS3URI            string
	DataUploadedBy        string
	UploadJobTriggeredBy  string
	ScoredBy              string
	ScoringJobID          string
	ScoringState          string
	FailureCount          string
	OutputS3URI           string
	UploadJobID           string
	ScoringJobTriggeredBy string
}{
	HandleID:              "handle_id",
	StartTime:             "start_time",
	EndTime:               "end_time",
	InputS3URI:            "input_s3_uri",
	DataUploadedBy:        "data_uploaded_by",
	UploadJobTriggeredBy:  "upload_job_triggered_by",
	ScoredBy:              "scored_by",
	ScoringJobID:          "scoring_job_id",
	ScoringState:          "scoring_state",
	FailureCount:          "failure_count",
	OutputS3URI:           "output_s3_uri",
	UploadJobID:           "upload_job_id",
	ScoringJobTriggeredBy: "scoring_job_triggered_by",
}

var CMTScoringTableColumns = struct {
	HandleID              string
	StartTime             string
	EndTime               string
	InputS3URI            string
	DataUploadedBy        string
	UploadJobTriggeredBy  string
	ScoredBy              string
	ScoringJobID          string
	ScoringState          string
	FailureCount          string
	OutputS3URI           string
	UploadJobID           string
	ScoringJobTriggeredBy string
}{
	HandleID:              "cmt_scoring.handle_id",
	StartTime:             "cmt_scoring.start_time",
	EndTime:               "cmt_scoring.end_time",
	InputS3URI:            "cmt_scoring.input_s3_uri",
	DataUploadedBy:        "cmt_scoring.data_uploaded_by",
	UploadJobTriggeredBy:  "cmt_scoring.upload_job_triggered_by",
	ScoredBy:              "cmt_scoring.scored_by",
	ScoringJobID:          "cmt_scoring.scoring_job_id",
	ScoringState:          "cmt_scoring.scoring_state",
	FailureCount:          "cmt_scoring.failure_count",
	OutputS3URI:           "cmt_scoring.output_s3_uri",
	UploadJobID:           "cmt_scoring.upload_job_id",
	ScoringJobTriggeredBy: "cmt_scoring.scoring_job_triggered_by",
}

// Generated where

var CMTScoringWhere = struct {
	HandleID              whereHelperstring
	StartTime             whereHelpertime_Time
	EndTime               whereHelpertime_Time
	InputS3URI            whereHelperstring
	DataUploadedBy        whereHelperstring
	UploadJobTriggeredBy  whereHelpernull_String
	ScoredBy              whereHelpernull_String
	ScoringJobID          whereHelpernull_String
	ScoringState          whereHelperstring
	FailureCount          whereHelperint
	OutputS3URI           whereHelpernull_String
	UploadJobID           whereHelpernull_String
	ScoringJobTriggeredBy whereHelpernull_String
}{
	HandleID:              whereHelperstring{field: "\"telematics\".\"cmt_scoring\".\"handle_id\""},
	StartTime:             whereHelpertime_Time{field: "\"telematics\".\"cmt_scoring\".\"start_time\""},
	EndTime:               whereHelpertime_Time{field: "\"telematics\".\"cmt_scoring\".\"end_time\""},
	InputS3URI:            whereHelperstring{field: "\"telematics\".\"cmt_scoring\".\"input_s3_uri\""},
	DataUploadedBy:        whereHelperstring{field: "\"telematics\".\"cmt_scoring\".\"data_uploaded_by\""},
	UploadJobTriggeredBy:  whereHelpernull_String{field: "\"telematics\".\"cmt_scoring\".\"upload_job_triggered_by\""},
	ScoredBy:              whereHelpernull_String{field: "\"telematics\".\"cmt_scoring\".\"scored_by\""},
	ScoringJobID:          whereHelpernull_String{field: "\"telematics\".\"cmt_scoring\".\"scoring_job_id\""},
	ScoringState:          whereHelperstring{field: "\"telematics\".\"cmt_scoring\".\"scoring_state\""},
	FailureCount:          whereHelperint{field: "\"telematics\".\"cmt_scoring\".\"failure_count\""},
	OutputS3URI:           whereHelpernull_String{field: "\"telematics\".\"cmt_scoring\".\"output_s3_uri\""},
	UploadJobID:           whereHelpernull_String{field: "\"telematics\".\"cmt_scoring\".\"upload_job_id\""},
	ScoringJobTriggeredBy: whereHelpernull_String{field: "\"telematics\".\"cmt_scoring\".\"scoring_job_triggered_by\""},
}

// CMTScoringRels is where relationship names are stored.
var CMTScoringRels = struct {
}{}

// cmtScoringR is where relationships are stored.
type cmtScoringR struct {
}

// NewStruct creates a new relationship struct
func (*cmtScoringR) NewStruct() *cmtScoringR {
	return &cmtScoringR{}
}

// cmtScoringL is where Load methods for each relationship are stored.
type cmtScoringL struct{}

var (
	cmtScoringAllColumns            = []string{"handle_id", "start_time", "end_time", "input_s3_uri", "data_uploaded_by", "upload_job_triggered_by", "scored_by", "scoring_job_id", "scoring_state", "failure_count", "output_s3_uri", "upload_job_id", "scoring_job_triggered_by"}
	cmtScoringColumnsWithoutDefault = []string{"handle_id", "start_time", "end_time", "input_s3_uri", "data_uploaded_by", "scoring_state"}
	cmtScoringColumnsWithDefault    = []string{"upload_job_triggered_by", "scored_by", "scoring_job_id", "failure_count", "output_s3_uri", "upload_job_id", "scoring_job_triggered_by"}
	cmtScoringPrimaryKeyColumns     = []string{"handle_id", "start_time", "end_time"}
	cmtScoringGeneratedColumns      = []string{}
)

type (
	// CMTScoringSlice is an alias for a slice of pointers to CMTScoring.
	// This should almost always be used instead of []CMTScoring.
	CMTScoringSlice []*CMTScoring
	// CMTScoringHook is the signature for custom CMTScoring hook methods
	CMTScoringHook func(context.Context, boil.ContextExecutor, *CMTScoring) error

	cmtScoringQuery struct {
		*queries.Query
	}
)

// Cache for insert, update and upsert
var (
	cmtScoringType                 = reflect.TypeOf(&CMTScoring{})
	cmtScoringMapping              = queries.MakeStructMapping(cmtScoringType)
	cmtScoringPrimaryKeyMapping, _ = queries.BindMapping(cmtScoringType, cmtScoringMapping, cmtScoringPrimaryKeyColumns)
	cmtScoringInsertCacheMut       sync.RWMutex
	cmtScoringInsertCache          = make(map[string]insertCache)
	cmtScoringUpdateCacheMut       sync.RWMutex
	cmtScoringUpdateCache          = make(map[string]updateCache)
	cmtScoringUpsertCacheMut       sync.RWMutex
	cmtScoringUpsertCache          = make(map[string]insertCache)
)

var (
	// Force time package dependency for automated UpdatedAt/CreatedAt.
	_ = time.Second
	// Force qmhelper dependency for where clause generation (which doesn't
	// always happen)
	_ = qmhelper.Where
)

var cmtScoringAfterSelectHooks []CMTScoringHook

var cmtScoringBeforeInsertHooks []CMTScoringHook
var cmtScoringAfterInsertHooks []CMTScoringHook

var cmtScoringBeforeUpdateHooks []CMTScoringHook
var cmtScoringAfterUpdateHooks []CMTScoringHook

var cmtScoringBeforeDeleteHooks []CMTScoringHook
var cmtScoringAfterDeleteHooks []CMTScoringHook

var cmtScoringBeforeUpsertHooks []CMTScoringHook
var cmtScoringAfterUpsertHooks []CMTScoringHook

// doAfterSelectHooks executes all "after Select" hooks.
func (o *CMTScoring) doAfterSelectHooks(ctx context.Context, exec boil.ContextExecutor) (err error) {
	if boil.HooksAreSkipped(ctx) {
		return nil
	}

	for _, hook := range cmtScoringAfterSelectHooks {
		if err := hook(ctx, exec, o); err != nil {
			return err
		}
	}

	return nil
}

// doBeforeInsertHooks executes all "before insert" hooks.
func (o *CMTScoring) doBeforeInsertHooks(ctx context.Context, exec boil.ContextExecutor) (err error) {
	if boil.HooksAreSkipped(ctx) {
		return nil
	}

	for _, hook := range cmtScoringBeforeInsertHooks {
		if err := hook(ctx, exec, o); err != nil {
			return err
		}
	}

	return nil
}

// doAfterInsertHooks executes all "after Insert" hooks.
func (o *CMTScoring) doAfterInsertHooks(ctx context.Context, exec boil.ContextExecutor) (err error) {
	if boil.HooksAreSkipped(ctx) {
		return nil
	}

	for _, hook := range cmtScoringAfterInsertHooks {
		if err := hook(ctx, exec, o); err != nil {
			return err
		}
	}

	return nil
}

// doBeforeUpdateHooks executes all "before Update" hooks.
func (o *CMTScoring) doBeforeUpdateHooks(ctx context.Context, exec boil.ContextExecutor) (err error) {
	if boil.HooksAreSkipped(ctx) {
		return nil
	}

	for _, hook := range cmtScoringBeforeUpdateHooks {
		if err := hook(ctx, exec, o); err != nil {
			return err
		}
	}

	return nil
}

// doAfterUpdateHooks executes all "after Update" hooks.
func (o *CMTScoring) doAfterUpdateHooks(ctx context.Context, exec boil.ContextExecutor) (err error) {
	if boil.HooksAreSkipped(ctx) {
		return nil
	}

	for _, hook := range cmtScoringAfterUpdateHooks {
		if err := hook(ctx, exec, o); err != nil {
			return err
		}
	}

	return nil
}

// doBeforeDeleteHooks executes all "before Delete" hooks.
func (o *CMTScoring) doBeforeDeleteHooks(ctx context.Context, exec boil.ContextExecutor) (err error) {
	if boil.HooksAreSkipped(ctx) {
		return nil
	}

	for _, hook := range cmtScoringBeforeDeleteHooks {
		if err := hook(ctx, exec, o); err != nil {
			return err
		}
	}

	return nil
}

// doAfterDeleteHooks executes all "after Delete" hooks.
func (o *CMTScoring) doAfterDeleteHooks(ctx context.Context, exec boil.ContextExecutor) (err error) {
	if boil.HooksAreSkipped(ctx) {
		return nil
	}

	for _, hook := range cmtScoringAfterDeleteHooks {
		if err := hook(ctx, exec, o); err != nil {
			return err
		}
	}

	return nil
}

// doBeforeUpsertHooks executes all "before Upsert" hooks.
func (o *CMTScoring) doBeforeUpsertHooks(ctx context.Context, exec boil.ContextExecutor) (err error) {
	if boil.HooksAreSkipped(ctx) {
		return nil
	}

	for _, hook := range cmtScoringBeforeUpsertHooks {
		if err := hook(ctx, exec, o); err != nil {
			return err
		}
	}

	return nil
}

// doAfterUpsertHooks executes all "after Upsert" hooks.
func (o *CMTScoring) doAfterUpsertHooks(ctx context.Context, exec boil.ContextExecutor) (err error) {
	if boil.HooksAreSkipped(ctx) {
		return nil
	}

	for _, hook := range cmtScoringAfterUpsertHooks {
		if err := hook(ctx, exec, o); err != nil {
			return err
		}
	}

	return nil
}

// AddCMTScoringHook registers your hook function for all future operations.
func AddCMTScoringHook(hookPoint boil.HookPoint, cmtScoringHook CMTScoringHook) {
	switch hookPoint {
	case boil.AfterSelectHook:
		cmtScoringAfterSelectHooks = append(cmtScoringAfterSelectHooks, cmtScoringHook)
	case boil.BeforeInsertHook:
		cmtScoringBeforeInsertHooks = append(cmtScoringBeforeInsertHooks, cmtScoringHook)
	case boil.AfterInsertHook:
		cmtScoringAfterInsertHooks = append(cmtScoringAfterInsertHooks, cmtScoringHook)
	case boil.BeforeUpdateHook:
		cmtScoringBeforeUpdateHooks = append(cmtScoringBeforeUpdateHooks, cmtScoringHook)
	case boil.AfterUpdateHook:
		cmtScoringAfterUpdateHooks = append(cmtScoringAfterUpdateHooks, cmtScoringHook)
	case boil.BeforeDeleteHook:
		cmtScoringBeforeDeleteHooks = append(cmtScoringBeforeDeleteHooks, cmtScoringHook)
	case boil.AfterDeleteHook:
		cmtScoringAfterDeleteHooks = append(cmtScoringAfterDeleteHooks, cmtScoringHook)
	case boil.BeforeUpsertHook:
		cmtScoringBeforeUpsertHooks = append(cmtScoringBeforeUpsertHooks, cmtScoringHook)
	case boil.AfterUpsertHook:
		cmtScoringAfterUpsertHooks = append(cmtScoringAfterUpsertHooks, cmtScoringHook)
	}
}

// One returns a single cmtScoring record from the query.
func (q cmtScoringQuery) One(ctx context.Context, exec boil.ContextExecutor) (*CMTScoring, error) {
	o := &CMTScoring{}

	queries.SetLimit(q.Query, 1)

	err := q.Bind(ctx, exec, o)
	if err != nil {
		if errors.Is(err, sql.ErrNoRows) {
			return nil, sql.ErrNoRows
		}
		return nil, errors.Wrap(err, "telematics: failed to execute a one query for cmt_scoring")
	}

	if err := o.doAfterSelectHooks(ctx, exec); err != nil {
		return o, err
	}

	return o, nil
}

// All returns all CMTScoring records from the query.
func (q cmtScoringQuery) All(ctx context.Context, exec boil.ContextExecutor) (CMTScoringSlice, error) {
	var o []*CMTScoring

	err := q.Bind(ctx, exec, &o)
	if err != nil {
		return nil, errors.Wrap(err, "telematics: failed to assign all query results to CMTScoring slice")
	}

	if len(cmtScoringAfterSelectHooks) != 0 {
		for _, obj := range o {
			if err := obj.doAfterSelectHooks(ctx, exec); err != nil {
				return o, err
			}
		}
	}

	return o, nil
}

// Count returns the count of all CMTScoring records in the query.
func (q cmtScoringQuery) Count(ctx context.Context, exec boil.ContextExecutor) (int64, error) {
	var count int64

	queries.SetSelect(q.Query, nil)
	queries.SetCount(q.Query)

	err := q.Query.QueryRowContext(ctx, exec).Scan(&count)
	if err != nil {
		return 0, errors.Wrap(err, "telematics: failed to count cmt_scoring rows")
	}

	return count, nil
}

// Exists checks if the row exists in the table.
func (q cmtScoringQuery) Exists(ctx context.Context, exec boil.ContextExecutor) (bool, error) {
	var count int64

	queries.SetSelect(q.Query, nil)
	queries.SetCount(q.Query)
	queries.SetLimit(q.Query, 1)

	err := q.Query.QueryRowContext(ctx, exec).Scan(&count)
	if err != nil {
		return false, errors.Wrap(err, "telematics: failed to check if cmt_scoring exists")
	}

	return count > 0, nil
}

// CMTScorings retrieves all the records using an executor.
func CMTScorings(mods ...qm.QueryMod) cmtScoringQuery {
	mods = append(mods, qm.From("\"telematics\".\"cmt_scoring\""))
	q := NewQuery(mods...)
	if len(queries.GetSelect(q)) == 0 {
		queries.SetSelect(q, []string{"\"telematics\".\"cmt_scoring\".*"})
	}

	return cmtScoringQuery{q}
}

// FindCMTScoring retrieves a single record by ID with an executor.
// If selectCols is empty Find will return all columns.
func FindCMTScoring(ctx context.Context, exec boil.ContextExecutor, handleID string, startTime time.Time, endTime time.Time, selectCols ...string) (*CMTScoring, error) {
	cmtScoringObj := &CMTScoring{}

	sel := "*"
	if len(selectCols) > 0 {
		sel = strings.Join(strmangle.IdentQuoteSlice(dialect.LQ, dialect.RQ, selectCols), ",")
	}
	query := fmt.Sprintf(
		"select %s from \"telematics\".\"cmt_scoring\" where \"handle_id\"=$1 AND \"start_time\"=$2 AND \"end_time\"=$3", sel,
	)

	q := queries.Raw(query, handleID, startTime, endTime)

	err := q.Bind(ctx, exec, cmtScoringObj)
	if err != nil {
		if errors.Is(err, sql.ErrNoRows) {
			return nil, sql.ErrNoRows
		}
		return nil, errors.Wrap(err, "telematics: unable to select from cmt_scoring")
	}

	if err = cmtScoringObj.doAfterSelectHooks(ctx, exec); err != nil {
		return cmtScoringObj, err
	}

	return cmtScoringObj, nil
}

// Insert a single record using an executor.
// See boil.Columns.InsertColumnSet documentation to understand column list inference for inserts.
func (o *CMTScoring) Insert(ctx context.Context, exec boil.ContextExecutor, columns boil.Columns) error {
	if o == nil {
		return errors.New("telematics: no cmt_scoring provided for insertion")
	}

	var err error

	if err := o.doBeforeInsertHooks(ctx, exec); err != nil {
		return err
	}

	nzDefaults := queries.NonZeroDefaultSet(cmtScoringColumnsWithDefault, o)

	key := makeCacheKey(columns, nzDefaults)
	cmtScoringInsertCacheMut.RLock()
	cache, cached := cmtScoringInsertCache[key]
	cmtScoringInsertCacheMut.RUnlock()

	if !cached {
		wl, returnColumns := columns.InsertColumnSet(
			cmtScoringAllColumns,
			cmtScoringColumnsWithDefault,
			cmtScoringColumnsWithoutDefault,
			nzDefaults,
		)

		cache.valueMapping, err = queries.BindMapping(cmtScoringType, cmtScoringMapping, wl)
		if err != nil {
			return err
		}
		cache.retMapping, err = queries.BindMapping(cmtScoringType, cmtScoringMapping, returnColumns)
		if err != nil {
			return err
		}
		if len(wl) != 0 {
			cache.query = fmt.Sprintf("INSERT INTO \"telematics\".\"cmt_scoring\" (\"%s\") %%sVALUES (%s)%%s", strings.Join(wl, "\",\""), strmangle.Placeholders(dialect.UseIndexPlaceholders, len(wl), 1, 1))
		} else {
			cache.query = "INSERT INTO \"telematics\".\"cmt_scoring\" %sDEFAULT VALUES%s"
		}

		var queryOutput, queryReturning string

		if len(cache.retMapping) != 0 {
			queryReturning = fmt.Sprintf(" RETURNING \"%s\"", strings.Join(returnColumns, "\",\""))
		}

		cache.query = fmt.Sprintf(cache.query, queryOutput, queryReturning)
	}

	value := reflect.Indirect(reflect.ValueOf(o))
	vals := queries.ValuesFromMapping(value, cache.valueMapping)

	if boil.IsDebug(ctx) {
		writer := boil.DebugWriterFrom(ctx)
		fmt.Fprintln(writer, cache.query)
		fmt.Fprintln(writer, vals)
	}

	if len(cache.retMapping) != 0 {
		err = exec.QueryRowContext(ctx, cache.query, vals...).Scan(queries.PtrsFromMapping(value, cache.retMapping)...)
	} else {
		_, err = exec.ExecContext(ctx, cache.query, vals...)
	}

	if err != nil {
		return errors.Wrap(err, "telematics: unable to insert into cmt_scoring")
	}

	if !cached {
		cmtScoringInsertCacheMut.Lock()
		cmtScoringInsertCache[key] = cache
		cmtScoringInsertCacheMut.Unlock()
	}

	return o.doAfterInsertHooks(ctx, exec)
}

// Update uses an executor to update the CMTScoring.
// See boil.Columns.UpdateColumnSet documentation to understand column list inference for updates.
// Update does not automatically update the record in case of default values. Use .Reload() to refresh the records.
func (o *CMTScoring) Update(ctx context.Context, exec boil.ContextExecutor, columns boil.Columns) (int64, error) {
	var err error
	if err = o.doBeforeUpdateHooks(ctx, exec); err != nil {
		return 0, err
	}
	key := makeCacheKey(columns, nil)
	cmtScoringUpdateCacheMut.RLock()
	cache, cached := cmtScoringUpdateCache[key]
	cmtScoringUpdateCacheMut.RUnlock()

	if !cached {
		wl := columns.UpdateColumnSet(
			cmtScoringAllColumns,
			cmtScoringPrimaryKeyColumns,
		)

		if !columns.IsWhitelist() {
			wl = strmangle.SetComplement(wl, []string{"created_at"})
		}
		if len(wl) == 0 {
			return 0, errors.New("telematics: unable to update cmt_scoring, could not build whitelist")
		}

		cache.query = fmt.Sprintf("UPDATE \"telematics\".\"cmt_scoring\" SET %s WHERE %s",
			strmangle.SetParamNames("\"", "\"", 1, wl),
			strmangle.WhereClause("\"", "\"", len(wl)+1, cmtScoringPrimaryKeyColumns),
		)
		cache.valueMapping, err = queries.BindMapping(cmtScoringType, cmtScoringMapping, append(wl, cmtScoringPrimaryKeyColumns...))
		if err != nil {
			return 0, err
		}
	}

	values := queries.ValuesFromMapping(reflect.Indirect(reflect.ValueOf(o)), cache.valueMapping)

	if boil.IsDebug(ctx) {
		writer := boil.DebugWriterFrom(ctx)
		fmt.Fprintln(writer, cache.query)
		fmt.Fprintln(writer, values)
	}
	var result sql.Result
	result, err = exec.ExecContext(ctx, cache.query, values...)
	if err != nil {
		return 0, errors.Wrap(err, "telematics: unable to update cmt_scoring row")
	}

	rowsAff, err := result.RowsAffected()
	if err != nil {
		return 0, errors.Wrap(err, "telematics: failed to get rows affected by update for cmt_scoring")
	}

	if !cached {
		cmtScoringUpdateCacheMut.Lock()
		cmtScoringUpdateCache[key] = cache
		cmtScoringUpdateCacheMut.Unlock()
	}

	return rowsAff, o.doAfterUpdateHooks(ctx, exec)
}

// UpdateAll updates all rows with the specified column values.
func (q cmtScoringQuery) UpdateAll(ctx context.Context, exec boil.ContextExecutor, cols M) (int64, error) {
	queries.SetUpdate(q.Query, cols)

	result, err := q.Query.ExecContext(ctx, exec)
	if err != nil {
		return 0, errors.Wrap(err, "telematics: unable to update all for cmt_scoring")
	}

	rowsAff, err := result.RowsAffected()
	if err != nil {
		return 0, errors.Wrap(err, "telematics: unable to retrieve rows affected for cmt_scoring")
	}

	return rowsAff, nil
}

// UpdateAll updates all rows with the specified column values, using an executor.
func (o CMTScoringSlice) UpdateAll(ctx context.Context, exec boil.ContextExecutor, cols M) (int64, error) {
	ln := int64(len(o))
	if ln == 0 {
		return 0, nil
	}

	if len(cols) == 0 {
		return 0, errors.New("telematics: update all requires at least one column argument")
	}

	colNames := make([]string, len(cols))
	args := make([]interface{}, len(cols))

	i := 0
	for name, value := range cols {
		colNames[i] = name
		args[i] = value
		i++
	}

	// Append all of the primary key values for each column
	for _, obj := range o {
		pkeyArgs := queries.ValuesFromMapping(reflect.Indirect(reflect.ValueOf(obj)), cmtScoringPrimaryKeyMapping)
		args = append(args, pkeyArgs...)
	}

	sql := fmt.Sprintf("UPDATE \"telematics\".\"cmt_scoring\" SET %s WHERE %s",
		strmangle.SetParamNames("\"", "\"", 1, colNames),
		strmangle.WhereClauseRepeated(string(dialect.LQ), string(dialect.RQ), len(colNames)+1, cmtScoringPrimaryKeyColumns, len(o)))

	if boil.IsDebug(ctx) {
		writer := boil.DebugWriterFrom(ctx)
		fmt.Fprintln(writer, sql)
		fmt.Fprintln(writer, args...)
	}
	result, err := exec.ExecContext(ctx, sql, args...)
	if err != nil {
		return 0, errors.Wrap(err, "telematics: unable to update all in cmtScoring slice")
	}

	rowsAff, err := result.RowsAffected()
	if err != nil {
		return 0, errors.Wrap(err, "telematics: unable to retrieve rows affected all in update all cmtScoring")
	}
	return rowsAff, nil
}

// Upsert attempts an insert using an executor, and does an update or ignore on conflict.
// See boil.Columns documentation for how to properly use updateColumns and insertColumns.
func (o *CMTScoring) Upsert(ctx context.Context, exec boil.ContextExecutor, updateOnConflict bool, conflictColumns []string, updateColumns, insertColumns boil.Columns) error {
	if o == nil {
		return errors.New("telematics: no cmt_scoring provided for upsert")
	}

	if err := o.doBeforeUpsertHooks(ctx, exec); err != nil {
		return err
	}

	nzDefaults := queries.NonZeroDefaultSet(cmtScoringColumnsWithDefault, o)

	// Build cache key in-line uglily - mysql vs psql problems
	buf := strmangle.GetBuffer()
	if updateOnConflict {
		buf.WriteByte('t')
	} else {
		buf.WriteByte('f')
	}
	buf.WriteByte('.')
	for _, c := range conflictColumns {
		buf.WriteString(c)
	}
	buf.WriteByte('.')
	buf.WriteString(strconv.Itoa(updateColumns.Kind))
	for _, c := range updateColumns.Cols {
		buf.WriteString(c)
	}
	buf.WriteByte('.')
	buf.WriteString(strconv.Itoa(insertColumns.Kind))
	for _, c := range insertColumns.Cols {
		buf.WriteString(c)
	}
	buf.WriteByte('.')
	for _, c := range nzDefaults {
		buf.WriteString(c)
	}
	key := buf.String()
	strmangle.PutBuffer(buf)

	cmtScoringUpsertCacheMut.RLock()
	cache, cached := cmtScoringUpsertCache[key]
	cmtScoringUpsertCacheMut.RUnlock()

	var err error

	if !cached {
		insert, ret := insertColumns.InsertColumnSet(
			cmtScoringAllColumns,
			cmtScoringColumnsWithDefault,
			cmtScoringColumnsWithoutDefault,
			nzDefaults,
		)

		update := updateColumns.UpdateColumnSet(
			cmtScoringAllColumns,
			cmtScoringPrimaryKeyColumns,
		)

		if updateOnConflict && len(update) == 0 {
			return errors.New("telematics: unable to upsert cmt_scoring, could not build update column list")
		}

		conflict := conflictColumns
		if len(conflict) == 0 {
			conflict = make([]string, len(cmtScoringPrimaryKeyColumns))
			copy(conflict, cmtScoringPrimaryKeyColumns)
		}
		cache.query = buildUpsertQueryPostgres(dialect, "\"telematics\".\"cmt_scoring\"", updateOnConflict, ret, update, conflict, insert)

		cache.valueMapping, err = queries.BindMapping(cmtScoringType, cmtScoringMapping, insert)
		if err != nil {
			return err
		}
		if len(ret) != 0 {
			cache.retMapping, err = queries.BindMapping(cmtScoringType, cmtScoringMapping, ret)
			if err != nil {
				return err
			}
		}
	}

	value := reflect.Indirect(reflect.ValueOf(o))
	vals := queries.ValuesFromMapping(value, cache.valueMapping)
	var returns []interface{}
	if len(cache.retMapping) != 0 {
		returns = queries.PtrsFromMapping(value, cache.retMapping)
	}

	if boil.IsDebug(ctx) {
		writer := boil.DebugWriterFrom(ctx)
		fmt.Fprintln(writer, cache.query)
		fmt.Fprintln(writer, vals)
	}
	if len(cache.retMapping) != 0 {
		err = exec.QueryRowContext(ctx, cache.query, vals...).Scan(returns...)
		if errors.Is(err, sql.ErrNoRows) {
			err = nil // Postgres doesn't return anything when there's no update
		}
	} else {
		_, err = exec.ExecContext(ctx, cache.query, vals...)
	}
	if err != nil {
		return errors.Wrap(err, "telematics: unable to upsert cmt_scoring")
	}

	if !cached {
		cmtScoringUpsertCacheMut.Lock()
		cmtScoringUpsertCache[key] = cache
		cmtScoringUpsertCacheMut.Unlock()
	}

	return o.doAfterUpsertHooks(ctx, exec)
}

// Delete deletes a single CMTScoring record with an executor.
// Delete will match against the primary key column to find the record to delete.
func (o *CMTScoring) Delete(ctx context.Context, exec boil.ContextExecutor) (int64, error) {
	if o == nil {
		return 0, errors.New("telematics: no CMTScoring provided for delete")
	}

	if err := o.doBeforeDeleteHooks(ctx, exec); err != nil {
		return 0, err
	}

	args := queries.ValuesFromMapping(reflect.Indirect(reflect.ValueOf(o)), cmtScoringPrimaryKeyMapping)
	sql := "DELETE FROM \"telematics\".\"cmt_scoring\" WHERE \"handle_id\"=$1 AND \"start_time\"=$2 AND \"end_time\"=$3"

	if boil.IsDebug(ctx) {
		writer := boil.DebugWriterFrom(ctx)
		fmt.Fprintln(writer, sql)
		fmt.Fprintln(writer, args...)
	}
	result, err := exec.ExecContext(ctx, sql, args...)
	if err != nil {
		return 0, errors.Wrap(err, "telematics: unable to delete from cmt_scoring")
	}

	rowsAff, err := result.RowsAffected()
	if err != nil {
		return 0, errors.Wrap(err, "telematics: failed to get rows affected by delete for cmt_scoring")
	}

	if err := o.doAfterDeleteHooks(ctx, exec); err != nil {
		return 0, err
	}

	return rowsAff, nil
}

// DeleteAll deletes all matching rows.
func (q cmtScoringQuery) DeleteAll(ctx context.Context, exec boil.ContextExecutor) (int64, error) {
	if q.Query == nil {
		return 0, errors.New("telematics: no cmtScoringQuery provided for delete all")
	}

	queries.SetDelete(q.Query)

	result, err := q.Query.ExecContext(ctx, exec)
	if err != nil {
		return 0, errors.Wrap(err, "telematics: unable to delete all from cmt_scoring")
	}

	rowsAff, err := result.RowsAffected()
	if err != nil {
		return 0, errors.Wrap(err, "telematics: failed to get rows affected by deleteall for cmt_scoring")
	}

	return rowsAff, nil
}

// DeleteAll deletes all rows in the slice, using an executor.
func (o CMTScoringSlice) DeleteAll(ctx context.Context, exec boil.ContextExecutor) (int64, error) {
	if len(o) == 0 {
		return 0, nil
	}

	if len(cmtScoringBeforeDeleteHooks) != 0 {
		for _, obj := range o {
			if err := obj.doBeforeDeleteHooks(ctx, exec); err != nil {
				return 0, err
			}
		}
	}

	var args []interface{}
	for _, obj := range o {
		pkeyArgs := queries.ValuesFromMapping(reflect.Indirect(reflect.ValueOf(obj)), cmtScoringPrimaryKeyMapping)
		args = append(args, pkeyArgs...)
	}

	sql := "DELETE FROM \"telematics\".\"cmt_scoring\" WHERE " +
		strmangle.WhereClauseRepeated(string(dialect.LQ), string(dialect.RQ), 1, cmtScoringPrimaryKeyColumns, len(o))

	if boil.IsDebug(ctx) {
		writer := boil.DebugWriterFrom(ctx)
		fmt.Fprintln(writer, sql)
		fmt.Fprintln(writer, args)
	}
	result, err := exec.ExecContext(ctx, sql, args...)
	if err != nil {
		return 0, errors.Wrap(err, "telematics: unable to delete all from cmtScoring slice")
	}

	rowsAff, err := result.RowsAffected()
	if err != nil {
		return 0, errors.Wrap(err, "telematics: failed to get rows affected by deleteall for cmt_scoring")
	}

	if len(cmtScoringAfterDeleteHooks) != 0 {
		for _, obj := range o {
			if err := obj.doAfterDeleteHooks(ctx, exec); err != nil {
				return 0, err
			}
		}
	}

	return rowsAff, nil
}

// Reload refetches the object from the database
// using the primary keys with an executor.
func (o *CMTScoring) Reload(ctx context.Context, exec boil.ContextExecutor) error {
	ret, err := FindCMTScoring(ctx, exec, o.HandleID, o.StartTime, o.EndTime)
	if err != nil {
		return err
	}

	*o = *ret
	return nil
}

// ReloadAll refetches every row with matching primary key column values
// and overwrites the original object slice with the newly updated slice.
func (o *CMTScoringSlice) ReloadAll(ctx context.Context, exec boil.ContextExecutor) error {
	if o == nil || len(*o) == 0 {
		return nil
	}

	slice := CMTScoringSlice{}
	var args []interface{}
	for _, obj := range *o {
		pkeyArgs := queries.ValuesFromMapping(reflect.Indirect(reflect.ValueOf(obj)), cmtScoringPrimaryKeyMapping)
		args = append(args, pkeyArgs...)
	}

	sql := "SELECT \"telematics\".\"cmt_scoring\".* FROM \"telematics\".\"cmt_scoring\" WHERE " +
		strmangle.WhereClauseRepeated(string(dialect.LQ), string(dialect.RQ), 1, cmtScoringPrimaryKeyColumns, len(*o))

	q := queries.Raw(sql, args...)

	err := q.Bind(ctx, exec, &slice)
	if err != nil {
		return errors.Wrap(err, "telematics: unable to reload all in CMTScoringSlice")
	}

	*o = slice

	return nil
}

// CMTScoringExists checks if the CMTScoring row exists.
func CMTScoringExists(ctx context.Context, exec boil.ContextExecutor, handleID string, startTime time.Time, endTime time.Time) (bool, error) {
	var exists bool
	sql := "select exists(select 1 from \"telematics\".\"cmt_scoring\" where \"handle_id\"=$1 AND \"start_time\"=$2 AND \"end_time\"=$3 limit 1)"

	if boil.IsDebug(ctx) {
		writer := boil.DebugWriterFrom(ctx)
		fmt.Fprintln(writer, sql)
		fmt.Fprintln(writer, handleID, startTime, endTime)
	}
	row := exec.QueryRowContext(ctx, sql, handleID, startTime, endTime)

	err := row.Scan(&exists)
	if err != nil {
		return false, errors.Wrap(err, "telematics: unable to check if cmt_scoring exists")
	}

	return exists, nil
}
