// Code generated by SQLBoiler (https://github.com/volatiletech/sqlboiler). DO NOT EDIT.
// This file is meant to be re-generated in place and/or deleted at any time.

package ds

import (
	"context"
	"database/sql"
	"fmt"
	"reflect"
	"strconv"
	"strings"
	"sync"
	"time"

	"github.com/friendsofgo/errors"
	"github.com/volatiletech/null/v8"
	"github.com/volatiletech/sqlboiler/v4/boil"
	"github.com/volatiletech/sqlboiler/v4/queries"
	"github.com/volatiletech/sqlboiler/v4/queries/qm"
	"github.com/volatiletech/sqlboiler/v4/queries/qmhelper"
	"github.com/volatiletech/sqlboiler/v4/types"
	"github.com/volatiletech/strmangle"
)

// FLEET_ZONE_MILEAGE_FEATURE_3MO_V1 is an object representing the database table.
type FLEET_ZONE_MILEAGE_FEATURE_3MO_V1 struct {
	MILEAGE       types.NullDecimal `boil:"MILEAGE" json:"MILEAGE,omitempty" toml:"MILEAGE" yaml:"MILEAGE,omitempty"`
	ZONE_ID       int64             `boil:"ZONE_ID" json:"ZONE_ID" toml:"ZONE_ID" yaml:"ZONE_ID"`
	END_DATE      time.Time         `boil:"END_DATE" json:"END_DATE" toml:"END_DATE" yaml:"END_DATE"`
	COUNT_DAYS    null.Int64        `boil:"COUNT_DAYS" json:"COUNT_DAYS,omitempty" toml:"COUNT_DAYS" yaml:"COUNT_DAYS,omitempty"`
	START_DATE    time.Time         `boil:"START_DATE" json:"START_DATE" toml:"START_DATE" yaml:"START_DATE"`
	UNIQUE_KEY    null.String       `boil:"UNIQUE_KEY" json:"UNIQUE_KEY,omitempty" toml:"UNIQUE_KEY" yaml:"UNIQUE_KEY,omitempty"`
	UPDATED_AT    time.Time         `boil:"UPDATED_AT" json:"UPDATED_AT" toml:"UPDATED_AT" yaml:"UPDATED_AT"`
	GPS_MILEAGE   types.NullDecimal `boil:"GPS_MILEAGE" json:"GPS_MILEAGE,omitempty" toml:"GPS_MILEAGE" yaml:"GPS_MILEAGE,omitempty"`
	ODO_MILEAGE   types.NullDecimal `boil:"ODO_MILEAGE" json:"ODO_MILEAGE,omitempty" toml:"ODO_MILEAGE" yaml:"ODO_MILEAGE,omitempty"`
	CONNECTION_ID string            `boil:"CONNECTION_ID" json:"CONNECTION_ID" toml:"CONNECTION_ID" yaml:"CONNECTION_ID"`

	R *fLEETZONE_MILEAGE_FEATURE_3MO_V1R `boil:"" json:"" toml:"" yaml:""`
	L fLEETZONE_MILEAGE_FEATURE_3MO_V1L  `boil:"-" json:"-" toml:"-" yaml:"-"`
}

var FLEET_ZONE_MILEAGE_FEATURE_3MO_V1Columns = struct {
	MILEAGE       string
	ZONE_ID       string
	END_DATE      string
	COUNT_DAYS    string
	START_DATE    string
	UNIQUE_KEY    string
	UPDATED_AT    string
	GPS_MILEAGE   string
	ODO_MILEAGE   string
	CONNECTION_ID string
}{
	MILEAGE:       "MILEAGE",
	ZONE_ID:       "ZONE_ID",
	END_DATE:      "END_DATE",
	COUNT_DAYS:    "COUNT_DAYS",
	START_DATE:    "START_DATE",
	UNIQUE_KEY:    "UNIQUE_KEY",
	UPDATED_AT:    "UPDATED_AT",
	GPS_MILEAGE:   "GPS_MILEAGE",
	ODO_MILEAGE:   "ODO_MILEAGE",
	CONNECTION_ID: "CONNECTION_ID",
}

var FLEET_ZONE_MILEAGE_FEATURE_3MO_V1TableColumns = struct {
	MILEAGE       string
	ZONE_ID       string
	END_DATE      string
	COUNT_DAYS    string
	START_DATE    string
	UNIQUE_KEY    string
	UPDATED_AT    string
	GPS_MILEAGE   string
	ODO_MILEAGE   string
	CONNECTION_ID string
}{
	MILEAGE:       "FLEET_ZONE_MILEAGE_FEATURE_3MO_V1.MILEAGE",
	ZONE_ID:       "FLEET_ZONE_MILEAGE_FEATURE_3MO_V1.ZONE_ID",
	END_DATE:      "FLEET_ZONE_MILEAGE_FEATURE_3MO_V1.END_DATE",
	COUNT_DAYS:    "FLEET_ZONE_MILEAGE_FEATURE_3MO_V1.COUNT_DAYS",
	START_DATE:    "FLEET_ZONE_MILEAGE_FEATURE_3MO_V1.START_DATE",
	UNIQUE_KEY:    "FLEET_ZONE_MILEAGE_FEATURE_3MO_V1.UNIQUE_KEY",
	UPDATED_AT:    "FLEET_ZONE_MILEAGE_FEATURE_3MO_V1.UPDATED_AT",
	GPS_MILEAGE:   "FLEET_ZONE_MILEAGE_FEATURE_3MO_V1.GPS_MILEAGE",
	ODO_MILEAGE:   "FLEET_ZONE_MILEAGE_FEATURE_3MO_V1.ODO_MILEAGE",
	CONNECTION_ID: "FLEET_ZONE_MILEAGE_FEATURE_3MO_V1.CONNECTION_ID",
}

// Generated where

type whereHelperint64 struct{ field string }

func (w whereHelperint64) EQ(x int64) qm.QueryMod  { return qmhelper.Where(w.field, qmhelper.EQ, x) }
func (w whereHelperint64) NEQ(x int64) qm.QueryMod { return qmhelper.Where(w.field, qmhelper.NEQ, x) }
func (w whereHelperint64) LT(x int64) qm.QueryMod  { return qmhelper.Where(w.field, qmhelper.LT, x) }
func (w whereHelperint64) LTE(x int64) qm.QueryMod { return qmhelper.Where(w.field, qmhelper.LTE, x) }
func (w whereHelperint64) GT(x int64) qm.QueryMod  { return qmhelper.Where(w.field, qmhelper.GT, x) }
func (w whereHelperint64) GTE(x int64) qm.QueryMod { return qmhelper.Where(w.field, qmhelper.GTE, x) }
func (w whereHelperint64) IN(slice []int64) qm.QueryMod {
	values := make([]interface{}, 0, len(slice))
	for _, value := range slice {
		values = append(values, value)
	}
	return qm.WhereIn(fmt.Sprintf("%s IN ?", w.field), values...)
}
func (w whereHelperint64) NIN(slice []int64) qm.QueryMod {
	values := make([]interface{}, 0, len(slice))
	for _, value := range slice {
		values = append(values, value)
	}
	return qm.WhereNotIn(fmt.Sprintf("%s NOT IN ?", w.field), values...)
}

var FLEET_ZONE_MILEAGE_FEATURE_3MO_V1Where = struct {
	MILEAGE       whereHelpertypes_NullDecimal
	ZONE_ID       whereHelperint64
	END_DATE      whereHelpertime_Time
	COUNT_DAYS    whereHelpernull_Int64
	START_DATE    whereHelpertime_Time
	UNIQUE_KEY    whereHelpernull_String
	UPDATED_AT    whereHelpertime_Time
	GPS_MILEAGE   whereHelpertypes_NullDecimal
	ODO_MILEAGE   whereHelpertypes_NullDecimal
	CONNECTION_ID whereHelperstring
}{
	MILEAGE:       whereHelpertypes_NullDecimal{field: "\"FLEET_ZONE_MILEAGE_FEATURE_3MO_V1\".\"MILEAGE\""},
	ZONE_ID:       whereHelperint64{field: "\"FLEET_ZONE_MILEAGE_FEATURE_3MO_V1\".\"ZONE_ID\""},
	END_DATE:      whereHelpertime_Time{field: "\"FLEET_ZONE_MILEAGE_FEATURE_3MO_V1\".\"END_DATE\""},
	COUNT_DAYS:    whereHelpernull_Int64{field: "\"FLEET_ZONE_MILEAGE_FEATURE_3MO_V1\".\"COUNT_DAYS\""},
	START_DATE:    whereHelpertime_Time{field: "\"FLEET_ZONE_MILEAGE_FEATURE_3MO_V1\".\"START_DATE\""},
	UNIQUE_KEY:    whereHelpernull_String{field: "\"FLEET_ZONE_MILEAGE_FEATURE_3MO_V1\".\"UNIQUE_KEY\""},
	UPDATED_AT:    whereHelpertime_Time{field: "\"FLEET_ZONE_MILEAGE_FEATURE_3MO_V1\".\"UPDATED_AT\""},
	GPS_MILEAGE:   whereHelpertypes_NullDecimal{field: "\"FLEET_ZONE_MILEAGE_FEATURE_3MO_V1\".\"GPS_MILEAGE\""},
	ODO_MILEAGE:   whereHelpertypes_NullDecimal{field: "\"FLEET_ZONE_MILEAGE_FEATURE_3MO_V1\".\"ODO_MILEAGE\""},
	CONNECTION_ID: whereHelperstring{field: "\"FLEET_ZONE_MILEAGE_FEATURE_3MO_V1\".\"CONNECTION_ID\""},
}

// FLEET_ZONE_MILEAGE_FEATURE_3MO_V1Rels is where relationship names are stored.
var FLEET_ZONE_MILEAGE_FEATURE_3MO_V1Rels = struct {
}{}

// fLEETZONE_MILEAGE_FEATURE_3MO_V1R is where relationships are stored.
type fLEETZONE_MILEAGE_FEATURE_3MO_V1R struct {
}

// NewStruct creates a new relationship struct
func (*fLEETZONE_MILEAGE_FEATURE_3MO_V1R) NewStruct() *fLEETZONE_MILEAGE_FEATURE_3MO_V1R {
	return &fLEETZONE_MILEAGE_FEATURE_3MO_V1R{}
}

// fLEETZONE_MILEAGE_FEATURE_3MO_V1L is where Load methods for each relationship are stored.
type fLEETZONE_MILEAGE_FEATURE_3MO_V1L struct{}

var (
	fLEETZONE_MILEAGE_FEATURE_3MO_V1AllColumns            = []string{"MILEAGE", "ZONE_ID", "END_DATE", "COUNT_DAYS", "START_DATE", "UNIQUE_KEY", "UPDATED_AT", "GPS_MILEAGE", "ODO_MILEAGE", "CONNECTION_ID"}
	fLEETZONE_MILEAGE_FEATURE_3MO_V1ColumnsWithoutDefault = []string{"ZONE_ID", "END_DATE", "START_DATE", "UPDATED_AT", "CONNECTION_ID"}
	fLEETZONE_MILEAGE_FEATURE_3MO_V1ColumnsWithDefault    = []string{"MILEAGE", "COUNT_DAYS", "UNIQUE_KEY", "GPS_MILEAGE", "ODO_MILEAGE"}
	fLEETZONE_MILEAGE_FEATURE_3MO_V1PrimaryKeyColumns     = []string{"CONNECTION_ID", "ZONE_ID", "START_DATE", "END_DATE", "UPDATED_AT"}
	fLEETZONE_MILEAGE_FEATURE_3MO_V1GeneratedColumns      = []string{}
)

type (
	// FLEET_ZONE_MILEAGE_FEATURE_3MO_V1Slice is an alias for a slice of pointers to FLEET_ZONE_MILEAGE_FEATURE_3MO_V1.
	// This should almost always be used instead of []FLEET_ZONE_MILEAGE_FEATURE_3MO_V1.
	FLEET_ZONE_MILEAGE_FEATURE_3MO_V1Slice []*FLEET_ZONE_MILEAGE_FEATURE_3MO_V1
	// FLEET_ZONE_MILEAGE_FEATURE_3MO_V1Hook is the signature for custom FLEET_ZONE_MILEAGE_FEATURE_3MO_V1 hook methods
	FLEET_ZONE_MILEAGE_FEATURE_3MO_V1Hook func(context.Context, boil.ContextExecutor, *FLEET_ZONE_MILEAGE_FEATURE_3MO_V1) error

	fLEETZONE_MILEAGE_FEATURE_3MO_V1Query struct {
		*queries.Query
	}
)

// Cache for insert, update and upsert
var (
	fLEETZONE_MILEAGE_FEATURE_3MO_V1Type                 = reflect.TypeOf(&FLEET_ZONE_MILEAGE_FEATURE_3MO_V1{})
	fLEETZONE_MILEAGE_FEATURE_3MO_V1Mapping              = queries.MakeStructMapping(fLEETZONE_MILEAGE_FEATURE_3MO_V1Type)
	fLEETZONE_MILEAGE_FEATURE_3MO_V1PrimaryKeyMapping, _ = queries.BindMapping(fLEETZONE_MILEAGE_FEATURE_3MO_V1Type, fLEETZONE_MILEAGE_FEATURE_3MO_V1Mapping, fLEETZONE_MILEAGE_FEATURE_3MO_V1PrimaryKeyColumns)
	fLEETZONE_MILEAGE_FEATURE_3MO_V1InsertCacheMut       sync.RWMutex
	fLEETZONE_MILEAGE_FEATURE_3MO_V1InsertCache          = make(map[string]insertCache)
	fLEETZONE_MILEAGE_FEATURE_3MO_V1UpdateCacheMut       sync.RWMutex
	fLEETZONE_MILEAGE_FEATURE_3MO_V1UpdateCache          = make(map[string]updateCache)
	fLEETZONE_MILEAGE_FEATURE_3MO_V1UpsertCacheMut       sync.RWMutex
	fLEETZONE_MILEAGE_FEATURE_3MO_V1UpsertCache          = make(map[string]insertCache)
)

var (
	// Force time package dependency for automated UpdatedAt/CreatedAt.
	_ = time.Second
	// Force qmhelper dependency for where clause generation (which doesn't
	// always happen)
	_ = qmhelper.Where
)

var fLEETZONE_MILEAGE_FEATURE_3MO_V1AfterSelectHooks []FLEET_ZONE_MILEAGE_FEATURE_3MO_V1Hook

var fLEETZONE_MILEAGE_FEATURE_3MO_V1BeforeInsertHooks []FLEET_ZONE_MILEAGE_FEATURE_3MO_V1Hook
var fLEETZONE_MILEAGE_FEATURE_3MO_V1AfterInsertHooks []FLEET_ZONE_MILEAGE_FEATURE_3MO_V1Hook

var fLEETZONE_MILEAGE_FEATURE_3MO_V1BeforeUpdateHooks []FLEET_ZONE_MILEAGE_FEATURE_3MO_V1Hook
var fLEETZONE_MILEAGE_FEATURE_3MO_V1AfterUpdateHooks []FLEET_ZONE_MILEAGE_FEATURE_3MO_V1Hook

var fLEETZONE_MILEAGE_FEATURE_3MO_V1BeforeDeleteHooks []FLEET_ZONE_MILEAGE_FEATURE_3MO_V1Hook
var fLEETZONE_MILEAGE_FEATURE_3MO_V1AfterDeleteHooks []FLEET_ZONE_MILEAGE_FEATURE_3MO_V1Hook

var fLEETZONE_MILEAGE_FEATURE_3MO_V1BeforeUpsertHooks []FLEET_ZONE_MILEAGE_FEATURE_3MO_V1Hook
var fLEETZONE_MILEAGE_FEATURE_3MO_V1AfterUpsertHooks []FLEET_ZONE_MILEAGE_FEATURE_3MO_V1Hook

// doAfterSelectHooks executes all "after Select" hooks.
func (o *FLEET_ZONE_MILEAGE_FEATURE_3MO_V1) doAfterSelectHooks(ctx context.Context, exec boil.ContextExecutor) (err error) {
	if boil.HooksAreSkipped(ctx) {
		return nil
	}

	for _, hook := range fLEETZONE_MILEAGE_FEATURE_3MO_V1AfterSelectHooks {
		if err := hook(ctx, exec, o); err != nil {
			return err
		}
	}

	return nil
}

// doBeforeInsertHooks executes all "before insert" hooks.
func (o *FLEET_ZONE_MILEAGE_FEATURE_3MO_V1) doBeforeInsertHooks(ctx context.Context, exec boil.ContextExecutor) (err error) {
	if boil.HooksAreSkipped(ctx) {
		return nil
	}

	for _, hook := range fLEETZONE_MILEAGE_FEATURE_3MO_V1BeforeInsertHooks {
		if err := hook(ctx, exec, o); err != nil {
			return err
		}
	}

	return nil
}

// doAfterInsertHooks executes all "after Insert" hooks.
func (o *FLEET_ZONE_MILEAGE_FEATURE_3MO_V1) doAfterInsertHooks(ctx context.Context, exec boil.ContextExecutor) (err error) {
	if boil.HooksAreSkipped(ctx) {
		return nil
	}

	for _, hook := range fLEETZONE_MILEAGE_FEATURE_3MO_V1AfterInsertHooks {
		if err := hook(ctx, exec, o); err != nil {
			return err
		}
	}

	return nil
}

// doBeforeUpdateHooks executes all "before Update" hooks.
func (o *FLEET_ZONE_MILEAGE_FEATURE_3MO_V1) doBeforeUpdateHooks(ctx context.Context, exec boil.ContextExecutor) (err error) {
	if boil.HooksAreSkipped(ctx) {
		return nil
	}

	for _, hook := range fLEETZONE_MILEAGE_FEATURE_3MO_V1BeforeUpdateHooks {
		if err := hook(ctx, exec, o); err != nil {
			return err
		}
	}

	return nil
}

// doAfterUpdateHooks executes all "after Update" hooks.
func (o *FLEET_ZONE_MILEAGE_FEATURE_3MO_V1) doAfterUpdateHooks(ctx context.Context, exec boil.ContextExecutor) (err error) {
	if boil.HooksAreSkipped(ctx) {
		return nil
	}

	for _, hook := range fLEETZONE_MILEAGE_FEATURE_3MO_V1AfterUpdateHooks {
		if err := hook(ctx, exec, o); err != nil {
			return err
		}
	}

	return nil
}

// doBeforeDeleteHooks executes all "before Delete" hooks.
func (o *FLEET_ZONE_MILEAGE_FEATURE_3MO_V1) doBeforeDeleteHooks(ctx context.Context, exec boil.ContextExecutor) (err error) {
	if boil.HooksAreSkipped(ctx) {
		return nil
	}

	for _, hook := range fLEETZONE_MILEAGE_FEATURE_3MO_V1BeforeDeleteHooks {
		if err := hook(ctx, exec, o); err != nil {
			return err
		}
	}

	return nil
}

// doAfterDeleteHooks executes all "after Delete" hooks.
func (o *FLEET_ZONE_MILEAGE_FEATURE_3MO_V1) doAfterDeleteHooks(ctx context.Context, exec boil.ContextExecutor) (err error) {
	if boil.HooksAreSkipped(ctx) {
		return nil
	}

	for _, hook := range fLEETZONE_MILEAGE_FEATURE_3MO_V1AfterDeleteHooks {
		if err := hook(ctx, exec, o); err != nil {
			return err
		}
	}

	return nil
}

// doBeforeUpsertHooks executes all "before Upsert" hooks.
func (o *FLEET_ZONE_MILEAGE_FEATURE_3MO_V1) doBeforeUpsertHooks(ctx context.Context, exec boil.ContextExecutor) (err error) {
	if boil.HooksAreSkipped(ctx) {
		return nil
	}

	for _, hook := range fLEETZONE_MILEAGE_FEATURE_3MO_V1BeforeUpsertHooks {
		if err := hook(ctx, exec, o); err != nil {
			return err
		}
	}

	return nil
}

// doAfterUpsertHooks executes all "after Upsert" hooks.
func (o *FLEET_ZONE_MILEAGE_FEATURE_3MO_V1) doAfterUpsertHooks(ctx context.Context, exec boil.ContextExecutor) (err error) {
	if boil.HooksAreSkipped(ctx) {
		return nil
	}

	for _, hook := range fLEETZONE_MILEAGE_FEATURE_3MO_V1AfterUpsertHooks {
		if err := hook(ctx, exec, o); err != nil {
			return err
		}
	}

	return nil
}

// AddFLEET_ZONE_MILEAGE_FEATURE_3MO_V1Hook registers your hook function for all future operations.
func AddFLEET_ZONE_MILEAGE_FEATURE_3MO_V1Hook(hookPoint boil.HookPoint, fLEETZONE_MILEAGE_FEATURE_3MO_V1Hook FLEET_ZONE_MILEAGE_FEATURE_3MO_V1Hook) {
	switch hookPoint {
	case boil.AfterSelectHook:
		fLEETZONE_MILEAGE_FEATURE_3MO_V1AfterSelectHooks = append(fLEETZONE_MILEAGE_FEATURE_3MO_V1AfterSelectHooks, fLEETZONE_MILEAGE_FEATURE_3MO_V1Hook)
	case boil.BeforeInsertHook:
		fLEETZONE_MILEAGE_FEATURE_3MO_V1BeforeInsertHooks = append(fLEETZONE_MILEAGE_FEATURE_3MO_V1BeforeInsertHooks, fLEETZONE_MILEAGE_FEATURE_3MO_V1Hook)
	case boil.AfterInsertHook:
		fLEETZONE_MILEAGE_FEATURE_3MO_V1AfterInsertHooks = append(fLEETZONE_MILEAGE_FEATURE_3MO_V1AfterInsertHooks, fLEETZONE_MILEAGE_FEATURE_3MO_V1Hook)
	case boil.BeforeUpdateHook:
		fLEETZONE_MILEAGE_FEATURE_3MO_V1BeforeUpdateHooks = append(fLEETZONE_MILEAGE_FEATURE_3MO_V1BeforeUpdateHooks, fLEETZONE_MILEAGE_FEATURE_3MO_V1Hook)
	case boil.AfterUpdateHook:
		fLEETZONE_MILEAGE_FEATURE_3MO_V1AfterUpdateHooks = append(fLEETZONE_MILEAGE_FEATURE_3MO_V1AfterUpdateHooks, fLEETZONE_MILEAGE_FEATURE_3MO_V1Hook)
	case boil.BeforeDeleteHook:
		fLEETZONE_MILEAGE_FEATURE_3MO_V1BeforeDeleteHooks = append(fLEETZONE_MILEAGE_FEATURE_3MO_V1BeforeDeleteHooks, fLEETZONE_MILEAGE_FEATURE_3MO_V1Hook)
	case boil.AfterDeleteHook:
		fLEETZONE_MILEAGE_FEATURE_3MO_V1AfterDeleteHooks = append(fLEETZONE_MILEAGE_FEATURE_3MO_V1AfterDeleteHooks, fLEETZONE_MILEAGE_FEATURE_3MO_V1Hook)
	case boil.BeforeUpsertHook:
		fLEETZONE_MILEAGE_FEATURE_3MO_V1BeforeUpsertHooks = append(fLEETZONE_MILEAGE_FEATURE_3MO_V1BeforeUpsertHooks, fLEETZONE_MILEAGE_FEATURE_3MO_V1Hook)
	case boil.AfterUpsertHook:
		fLEETZONE_MILEAGE_FEATURE_3MO_V1AfterUpsertHooks = append(fLEETZONE_MILEAGE_FEATURE_3MO_V1AfterUpsertHooks, fLEETZONE_MILEAGE_FEATURE_3MO_V1Hook)
	}
}

// One returns a single fLEETZONE_MILEAGE_FEATURE_3MO_V1 record from the query.
func (q fLEETZONE_MILEAGE_FEATURE_3MO_V1Query) One(ctx context.Context, exec boil.ContextExecutor) (*FLEET_ZONE_MILEAGE_FEATURE_3MO_V1, error) {
	o := &FLEET_ZONE_MILEAGE_FEATURE_3MO_V1{}

	queries.SetLimit(q.Query, 1)

	err := q.Bind(ctx, exec, o)
	if err != nil {
		if errors.Is(err, sql.ErrNoRows) {
			return nil, sql.ErrNoRows
		}
		return nil, errors.Wrap(err, "ds: failed to execute a one query for FLEET_ZONE_MILEAGE_FEATURE_3MO_V1")
	}

	if err := o.doAfterSelectHooks(ctx, exec); err != nil {
		return o, err
	}

	return o, nil
}

// All returns all FLEET_ZONE_MILEAGE_FEATURE_3MO_V1 records from the query.
func (q fLEETZONE_MILEAGE_FEATURE_3MO_V1Query) All(ctx context.Context, exec boil.ContextExecutor) (FLEET_ZONE_MILEAGE_FEATURE_3MO_V1Slice, error) {
	var o []*FLEET_ZONE_MILEAGE_FEATURE_3MO_V1

	err := q.Bind(ctx, exec, &o)
	if err != nil {
		return nil, errors.Wrap(err, "ds: failed to assign all query results to FLEET_ZONE_MILEAGE_FEATURE_3MO_V1 slice")
	}

	if len(fLEETZONE_MILEAGE_FEATURE_3MO_V1AfterSelectHooks) != 0 {
		for _, obj := range o {
			if err := obj.doAfterSelectHooks(ctx, exec); err != nil {
				return o, err
			}
		}
	}

	return o, nil
}

// Count returns the count of all FLEET_ZONE_MILEAGE_FEATURE_3MO_V1 records in the query.
func (q fLEETZONE_MILEAGE_FEATURE_3MO_V1Query) Count(ctx context.Context, exec boil.ContextExecutor) (int64, error) {
	var count int64

	queries.SetSelect(q.Query, nil)
	queries.SetCount(q.Query)

	err := q.Query.QueryRowContext(ctx, exec).Scan(&count)
	if err != nil {
		return 0, errors.Wrap(err, "ds: failed to count FLEET_ZONE_MILEAGE_FEATURE_3MO_V1 rows")
	}

	return count, nil
}

// Exists checks if the row exists in the table.
func (q fLEETZONE_MILEAGE_FEATURE_3MO_V1Query) Exists(ctx context.Context, exec boil.ContextExecutor) (bool, error) {
	var count int64

	queries.SetSelect(q.Query, nil)
	queries.SetCount(q.Query)
	queries.SetLimit(q.Query, 1)

	err := q.Query.QueryRowContext(ctx, exec).Scan(&count)
	if err != nil {
		return false, errors.Wrap(err, "ds: failed to check if FLEET_ZONE_MILEAGE_FEATURE_3MO_V1 exists")
	}

	return count > 0, nil
}

// FLEETZONEMILEAGEFEATURE3MOV1S retrieves all the records using an executor.
func FLEETZONEMILEAGEFEATURE3MOV1S(mods ...qm.QueryMod) fLEETZONE_MILEAGE_FEATURE_3MO_V1Query {
	mods = append(mods, qm.From("\"FLEET_ZONE_MILEAGE_FEATURE_3MO_V1\""))
	q := NewQuery(mods...)
	if len(queries.GetSelect(q)) == 0 {
		queries.SetSelect(q, []string{"\"FLEET_ZONE_MILEAGE_FEATURE_3MO_V1\".*"})
	}

	return fLEETZONE_MILEAGE_FEATURE_3MO_V1Query{q}
}

// FindFLEET_ZONE_MILEAGE_FEATURE_3MO_V1 retrieves a single record by ID with an executor.
// If selectCols is empty Find will return all columns.
func FindFLEET_ZONE_MILEAGE_FEATURE_3MO_V1(ctx context.Context, exec boil.ContextExecutor, cONNECTIONID string, zONEID int64, sTARTDATE time.Time, eNDDATE time.Time, uPDATEDAT time.Time, selectCols ...string) (*FLEET_ZONE_MILEAGE_FEATURE_3MO_V1, error) {
	fLEETZONE_MILEAGE_FEATURE_3MO_V1Obj := &FLEET_ZONE_MILEAGE_FEATURE_3MO_V1{}

	sel := "*"
	if len(selectCols) > 0 {
		sel = strings.Join(strmangle.IdentQuoteSlice(dialect.LQ, dialect.RQ, selectCols), ",")
	}
	query := fmt.Sprintf(
		"select %s from \"FLEET_ZONE_MILEAGE_FEATURE_3MO_V1\" where \"CONNECTION_ID\"=$1 AND \"ZONE_ID\"=$2 AND \"START_DATE\"=$3 AND \"END_DATE\"=$4 AND \"UPDATED_AT\"=$5", sel,
	)

	q := queries.Raw(query, cONNECTIONID, zONEID, sTARTDATE, eNDDATE, uPDATEDAT)

	err := q.Bind(ctx, exec, fLEETZONE_MILEAGE_FEATURE_3MO_V1Obj)
	if err != nil {
		if errors.Is(err, sql.ErrNoRows) {
			return nil, sql.ErrNoRows
		}
		return nil, errors.Wrap(err, "ds: unable to select from FLEET_ZONE_MILEAGE_FEATURE_3MO_V1")
	}

	if err = fLEETZONE_MILEAGE_FEATURE_3MO_V1Obj.doAfterSelectHooks(ctx, exec); err != nil {
		return fLEETZONE_MILEAGE_FEATURE_3MO_V1Obj, err
	}

	return fLEETZONE_MILEAGE_FEATURE_3MO_V1Obj, nil
}

// Insert a single record using an executor.
// See boil.Columns.InsertColumnSet documentation to understand column list inference for inserts.
func (o *FLEET_ZONE_MILEAGE_FEATURE_3MO_V1) Insert(ctx context.Context, exec boil.ContextExecutor, columns boil.Columns) error {
	if o == nil {
		return errors.New("ds: no FLEET_ZONE_MILEAGE_FEATURE_3MO_V1 provided for insertion")
	}

	var err error

	if err := o.doBeforeInsertHooks(ctx, exec); err != nil {
		return err
	}

	nzDefaults := queries.NonZeroDefaultSet(fLEETZONE_MILEAGE_FEATURE_3MO_V1ColumnsWithDefault, o)

	key := makeCacheKey(columns, nzDefaults)
	fLEETZONE_MILEAGE_FEATURE_3MO_V1InsertCacheMut.RLock()
	cache, cached := fLEETZONE_MILEAGE_FEATURE_3MO_V1InsertCache[key]
	fLEETZONE_MILEAGE_FEATURE_3MO_V1InsertCacheMut.RUnlock()

	if !cached {
		wl, returnColumns := columns.InsertColumnSet(
			fLEETZONE_MILEAGE_FEATURE_3MO_V1AllColumns,
			fLEETZONE_MILEAGE_FEATURE_3MO_V1ColumnsWithDefault,
			fLEETZONE_MILEAGE_FEATURE_3MO_V1ColumnsWithoutDefault,
			nzDefaults,
		)

		cache.valueMapping, err = queries.BindMapping(fLEETZONE_MILEAGE_FEATURE_3MO_V1Type, fLEETZONE_MILEAGE_FEATURE_3MO_V1Mapping, wl)
		if err != nil {
			return err
		}
		cache.retMapping, err = queries.BindMapping(fLEETZONE_MILEAGE_FEATURE_3MO_V1Type, fLEETZONE_MILEAGE_FEATURE_3MO_V1Mapping, returnColumns)
		if err != nil {
			return err
		}
		if len(wl) != 0 {
			cache.query = fmt.Sprintf("INSERT INTO \"FLEET_ZONE_MILEAGE_FEATURE_3MO_V1\" (\"%s\") %%sVALUES (%s)%%s", strings.Join(wl, "\",\""), strmangle.Placeholders(dialect.UseIndexPlaceholders, len(wl), 1, 1))
		} else {
			cache.query = "INSERT INTO \"FLEET_ZONE_MILEAGE_FEATURE_3MO_V1\" %sDEFAULT VALUES%s"
		}

		var queryOutput, queryReturning string

		if len(cache.retMapping) != 0 {
			queryReturning = fmt.Sprintf(" RETURNING \"%s\"", strings.Join(returnColumns, "\",\""))
		}

		cache.query = fmt.Sprintf(cache.query, queryOutput, queryReturning)
	}

	value := reflect.Indirect(reflect.ValueOf(o))
	vals := queries.ValuesFromMapping(value, cache.valueMapping)

	if boil.IsDebug(ctx) {
		writer := boil.DebugWriterFrom(ctx)
		fmt.Fprintln(writer, cache.query)
		fmt.Fprintln(writer, vals)
	}

	if len(cache.retMapping) != 0 {
		err = exec.QueryRowContext(ctx, cache.query, vals...).Scan(queries.PtrsFromMapping(value, cache.retMapping)...)
	} else {
		_, err = exec.ExecContext(ctx, cache.query, vals...)
	}

	if err != nil {
		return errors.Wrap(err, "ds: unable to insert into FLEET_ZONE_MILEAGE_FEATURE_3MO_V1")
	}

	if !cached {
		fLEETZONE_MILEAGE_FEATURE_3MO_V1InsertCacheMut.Lock()
		fLEETZONE_MILEAGE_FEATURE_3MO_V1InsertCache[key] = cache
		fLEETZONE_MILEAGE_FEATURE_3MO_V1InsertCacheMut.Unlock()
	}

	return o.doAfterInsertHooks(ctx, exec)
}

// Update uses an executor to update the FLEET_ZONE_MILEAGE_FEATURE_3MO_V1.
// See boil.Columns.UpdateColumnSet documentation to understand column list inference for updates.
// Update does not automatically update the record in case of default values. Use .Reload() to refresh the records.
func (o *FLEET_ZONE_MILEAGE_FEATURE_3MO_V1) Update(ctx context.Context, exec boil.ContextExecutor, columns boil.Columns) (int64, error) {
	var err error
	if err = o.doBeforeUpdateHooks(ctx, exec); err != nil {
		return 0, err
	}
	key := makeCacheKey(columns, nil)
	fLEETZONE_MILEAGE_FEATURE_3MO_V1UpdateCacheMut.RLock()
	cache, cached := fLEETZONE_MILEAGE_FEATURE_3MO_V1UpdateCache[key]
	fLEETZONE_MILEAGE_FEATURE_3MO_V1UpdateCacheMut.RUnlock()

	if !cached {
		wl := columns.UpdateColumnSet(
			fLEETZONE_MILEAGE_FEATURE_3MO_V1AllColumns,
			fLEETZONE_MILEAGE_FEATURE_3MO_V1PrimaryKeyColumns,
		)

		if !columns.IsWhitelist() {
			wl = strmangle.SetComplement(wl, []string{"created_at"})
		}
		if len(wl) == 0 {
			return 0, errors.New("ds: unable to update FLEET_ZONE_MILEAGE_FEATURE_3MO_V1, could not build whitelist")
		}

		cache.query = fmt.Sprintf("UPDATE \"FLEET_ZONE_MILEAGE_FEATURE_3MO_V1\" SET %s WHERE %s",
			strmangle.SetParamNames("\"", "\"", 1, wl),
			strmangle.WhereClause("\"", "\"", len(wl)+1, fLEETZONE_MILEAGE_FEATURE_3MO_V1PrimaryKeyColumns),
		)
		cache.valueMapping, err = queries.BindMapping(fLEETZONE_MILEAGE_FEATURE_3MO_V1Type, fLEETZONE_MILEAGE_FEATURE_3MO_V1Mapping, append(wl, fLEETZONE_MILEAGE_FEATURE_3MO_V1PrimaryKeyColumns...))
		if err != nil {
			return 0, err
		}
	}

	values := queries.ValuesFromMapping(reflect.Indirect(reflect.ValueOf(o)), cache.valueMapping)

	if boil.IsDebug(ctx) {
		writer := boil.DebugWriterFrom(ctx)
		fmt.Fprintln(writer, cache.query)
		fmt.Fprintln(writer, values)
	}
	var result sql.Result
	result, err = exec.ExecContext(ctx, cache.query, values...)
	if err != nil {
		return 0, errors.Wrap(err, "ds: unable to update FLEET_ZONE_MILEAGE_FEATURE_3MO_V1 row")
	}

	rowsAff, err := result.RowsAffected()
	if err != nil {
		return 0, errors.Wrap(err, "ds: failed to get rows affected by update for FLEET_ZONE_MILEAGE_FEATURE_3MO_V1")
	}

	if !cached {
		fLEETZONE_MILEAGE_FEATURE_3MO_V1UpdateCacheMut.Lock()
		fLEETZONE_MILEAGE_FEATURE_3MO_V1UpdateCache[key] = cache
		fLEETZONE_MILEAGE_FEATURE_3MO_V1UpdateCacheMut.Unlock()
	}

	return rowsAff, o.doAfterUpdateHooks(ctx, exec)
}

// UpdateAll updates all rows with the specified column values.
func (q fLEETZONE_MILEAGE_FEATURE_3MO_V1Query) UpdateAll(ctx context.Context, exec boil.ContextExecutor, cols M) (int64, error) {
	queries.SetUpdate(q.Query, cols)

	result, err := q.Query.ExecContext(ctx, exec)
	if err != nil {
		return 0, errors.Wrap(err, "ds: unable to update all for FLEET_ZONE_MILEAGE_FEATURE_3MO_V1")
	}

	rowsAff, err := result.RowsAffected()
	if err != nil {
		return 0, errors.Wrap(err, "ds: unable to retrieve rows affected for FLEET_ZONE_MILEAGE_FEATURE_3MO_V1")
	}

	return rowsAff, nil
}

// UpdateAll updates all rows with the specified column values, using an executor.
func (o FLEET_ZONE_MILEAGE_FEATURE_3MO_V1Slice) UpdateAll(ctx context.Context, exec boil.ContextExecutor, cols M) (int64, error) {
	ln := int64(len(o))
	if ln == 0 {
		return 0, nil
	}

	if len(cols) == 0 {
		return 0, errors.New("ds: update all requires at least one column argument")
	}

	colNames := make([]string, len(cols))
	args := make([]interface{}, len(cols))

	i := 0
	for name, value := range cols {
		colNames[i] = name
		args[i] = value
		i++
	}

	// Append all of the primary key values for each column
	for _, obj := range o {
		pkeyArgs := queries.ValuesFromMapping(reflect.Indirect(reflect.ValueOf(obj)), fLEETZONE_MILEAGE_FEATURE_3MO_V1PrimaryKeyMapping)
		args = append(args, pkeyArgs...)
	}

	sql := fmt.Sprintf("UPDATE \"FLEET_ZONE_MILEAGE_FEATURE_3MO_V1\" SET %s WHERE %s",
		strmangle.SetParamNames("\"", "\"", 1, colNames),
		strmangle.WhereClauseRepeated(string(dialect.LQ), string(dialect.RQ), len(colNames)+1, fLEETZONE_MILEAGE_FEATURE_3MO_V1PrimaryKeyColumns, len(o)))

	if boil.IsDebug(ctx) {
		writer := boil.DebugWriterFrom(ctx)
		fmt.Fprintln(writer, sql)
		fmt.Fprintln(writer, args...)
	}
	result, err := exec.ExecContext(ctx, sql, args...)
	if err != nil {
		return 0, errors.Wrap(err, "ds: unable to update all in fLEETZONE_MILEAGE_FEATURE_3MO_V1 slice")
	}

	rowsAff, err := result.RowsAffected()
	if err != nil {
		return 0, errors.Wrap(err, "ds: unable to retrieve rows affected all in update all fLEETZONE_MILEAGE_FEATURE_3MO_V1")
	}
	return rowsAff, nil
}

// Upsert attempts an insert using an executor, and does an update or ignore on conflict.
// See boil.Columns documentation for how to properly use updateColumns and insertColumns.
func (o *FLEET_ZONE_MILEAGE_FEATURE_3MO_V1) Upsert(ctx context.Context, exec boil.ContextExecutor, updateOnConflict bool, conflictColumns []string, updateColumns, insertColumns boil.Columns) error {
	if o == nil {
		return errors.New("ds: no FLEET_ZONE_MILEAGE_FEATURE_3MO_V1 provided for upsert")
	}

	if err := o.doBeforeUpsertHooks(ctx, exec); err != nil {
		return err
	}

	nzDefaults := queries.NonZeroDefaultSet(fLEETZONE_MILEAGE_FEATURE_3MO_V1ColumnsWithDefault, o)

	// Build cache key in-line uglily - mysql vs psql problems
	buf := strmangle.GetBuffer()
	if updateOnConflict {
		buf.WriteByte('t')
	} else {
		buf.WriteByte('f')
	}
	buf.WriteByte('.')
	for _, c := range conflictColumns {
		buf.WriteString(c)
	}
	buf.WriteByte('.')
	buf.WriteString(strconv.Itoa(updateColumns.Kind))
	for _, c := range updateColumns.Cols {
		buf.WriteString(c)
	}
	buf.WriteByte('.')
	buf.WriteString(strconv.Itoa(insertColumns.Kind))
	for _, c := range insertColumns.Cols {
		buf.WriteString(c)
	}
	buf.WriteByte('.')
	for _, c := range nzDefaults {
		buf.WriteString(c)
	}
	key := buf.String()
	strmangle.PutBuffer(buf)

	fLEETZONE_MILEAGE_FEATURE_3MO_V1UpsertCacheMut.RLock()
	cache, cached := fLEETZONE_MILEAGE_FEATURE_3MO_V1UpsertCache[key]
	fLEETZONE_MILEAGE_FEATURE_3MO_V1UpsertCacheMut.RUnlock()

	var err error

	if !cached {
		insert, ret := insertColumns.InsertColumnSet(
			fLEETZONE_MILEAGE_FEATURE_3MO_V1AllColumns,
			fLEETZONE_MILEAGE_FEATURE_3MO_V1ColumnsWithDefault,
			fLEETZONE_MILEAGE_FEATURE_3MO_V1ColumnsWithoutDefault,
			nzDefaults,
		)

		update := updateColumns.UpdateColumnSet(
			fLEETZONE_MILEAGE_FEATURE_3MO_V1AllColumns,
			fLEETZONE_MILEAGE_FEATURE_3MO_V1PrimaryKeyColumns,
		)

		if updateOnConflict && len(update) == 0 {
			return errors.New("ds: unable to upsert FLEET_ZONE_MILEAGE_FEATURE_3MO_V1, could not build update column list")
		}

		conflict := conflictColumns
		if len(conflict) == 0 {
			conflict = make([]string, len(fLEETZONE_MILEAGE_FEATURE_3MO_V1PrimaryKeyColumns))
			copy(conflict, fLEETZONE_MILEAGE_FEATURE_3MO_V1PrimaryKeyColumns)
		}
		cache.query = buildUpsertQueryPostgres(dialect, "\"FLEET_ZONE_MILEAGE_FEATURE_3MO_V1\"", updateOnConflict, ret, update, conflict, insert)

		cache.valueMapping, err = queries.BindMapping(fLEETZONE_MILEAGE_FEATURE_3MO_V1Type, fLEETZONE_MILEAGE_FEATURE_3MO_V1Mapping, insert)
		if err != nil {
			return err
		}
		if len(ret) != 0 {
			cache.retMapping, err = queries.BindMapping(fLEETZONE_MILEAGE_FEATURE_3MO_V1Type, fLEETZONE_MILEAGE_FEATURE_3MO_V1Mapping, ret)
			if err != nil {
				return err
			}
		}
	}

	value := reflect.Indirect(reflect.ValueOf(o))
	vals := queries.ValuesFromMapping(value, cache.valueMapping)
	var returns []interface{}
	if len(cache.retMapping) != 0 {
		returns = queries.PtrsFromMapping(value, cache.retMapping)
	}

	if boil.IsDebug(ctx) {
		writer := boil.DebugWriterFrom(ctx)
		fmt.Fprintln(writer, cache.query)
		fmt.Fprintln(writer, vals)
	}
	if len(cache.retMapping) != 0 {
		err = exec.QueryRowContext(ctx, cache.query, vals...).Scan(returns...)
		if errors.Is(err, sql.ErrNoRows) {
			err = nil // Postgres doesn't return anything when there's no update
		}
	} else {
		_, err = exec.ExecContext(ctx, cache.query, vals...)
	}
	if err != nil {
		return errors.Wrap(err, "ds: unable to upsert FLEET_ZONE_MILEAGE_FEATURE_3MO_V1")
	}

	if !cached {
		fLEETZONE_MILEAGE_FEATURE_3MO_V1UpsertCacheMut.Lock()
		fLEETZONE_MILEAGE_FEATURE_3MO_V1UpsertCache[key] = cache
		fLEETZONE_MILEAGE_FEATURE_3MO_V1UpsertCacheMut.Unlock()
	}

	return o.doAfterUpsertHooks(ctx, exec)
}

// Delete deletes a single FLEET_ZONE_MILEAGE_FEATURE_3MO_V1 record with an executor.
// Delete will match against the primary key column to find the record to delete.
func (o *FLEET_ZONE_MILEAGE_FEATURE_3MO_V1) Delete(ctx context.Context, exec boil.ContextExecutor) (int64, error) {
	if o == nil {
		return 0, errors.New("ds: no FLEET_ZONE_MILEAGE_FEATURE_3MO_V1 provided for delete")
	}

	if err := o.doBeforeDeleteHooks(ctx, exec); err != nil {
		return 0, err
	}

	args := queries.ValuesFromMapping(reflect.Indirect(reflect.ValueOf(o)), fLEETZONE_MILEAGE_FEATURE_3MO_V1PrimaryKeyMapping)
	sql := "DELETE FROM \"FLEET_ZONE_MILEAGE_FEATURE_3MO_V1\" WHERE \"CONNECTION_ID\"=$1 AND \"ZONE_ID\"=$2 AND \"START_DATE\"=$3 AND \"END_DATE\"=$4 AND \"UPDATED_AT\"=$5"

	if boil.IsDebug(ctx) {
		writer := boil.DebugWriterFrom(ctx)
		fmt.Fprintln(writer, sql)
		fmt.Fprintln(writer, args...)
	}
	result, err := exec.ExecContext(ctx, sql, args...)
	if err != nil {
		return 0, errors.Wrap(err, "ds: unable to delete from FLEET_ZONE_MILEAGE_FEATURE_3MO_V1")
	}

	rowsAff, err := result.RowsAffected()
	if err != nil {
		return 0, errors.Wrap(err, "ds: failed to get rows affected by delete for FLEET_ZONE_MILEAGE_FEATURE_3MO_V1")
	}

	if err := o.doAfterDeleteHooks(ctx, exec); err != nil {
		return 0, err
	}

	return rowsAff, nil
}

// DeleteAll deletes all matching rows.
func (q fLEETZONE_MILEAGE_FEATURE_3MO_V1Query) DeleteAll(ctx context.Context, exec boil.ContextExecutor) (int64, error) {
	if q.Query == nil {
		return 0, errors.New("ds: no fLEETZONE_MILEAGE_FEATURE_3MO_V1Query provided for delete all")
	}

	queries.SetDelete(q.Query)

	result, err := q.Query.ExecContext(ctx, exec)
	if err != nil {
		return 0, errors.Wrap(err, "ds: unable to delete all from FLEET_ZONE_MILEAGE_FEATURE_3MO_V1")
	}

	rowsAff, err := result.RowsAffected()
	if err != nil {
		return 0, errors.Wrap(err, "ds: failed to get rows affected by deleteall for FLEET_ZONE_MILEAGE_FEATURE_3MO_V1")
	}

	return rowsAff, nil
}

// DeleteAll deletes all rows in the slice, using an executor.
func (o FLEET_ZONE_MILEAGE_FEATURE_3MO_V1Slice) DeleteAll(ctx context.Context, exec boil.ContextExecutor) (int64, error) {
	if len(o) == 0 {
		return 0, nil
	}

	if len(fLEETZONE_MILEAGE_FEATURE_3MO_V1BeforeDeleteHooks) != 0 {
		for _, obj := range o {
			if err := obj.doBeforeDeleteHooks(ctx, exec); err != nil {
				return 0, err
			}
		}
	}

	var args []interface{}
	for _, obj := range o {
		pkeyArgs := queries.ValuesFromMapping(reflect.Indirect(reflect.ValueOf(obj)), fLEETZONE_MILEAGE_FEATURE_3MO_V1PrimaryKeyMapping)
		args = append(args, pkeyArgs...)
	}

	sql := "DELETE FROM \"FLEET_ZONE_MILEAGE_FEATURE_3MO_V1\" WHERE " +
		strmangle.WhereClauseRepeated(string(dialect.LQ), string(dialect.RQ), 1, fLEETZONE_MILEAGE_FEATURE_3MO_V1PrimaryKeyColumns, len(o))

	if boil.IsDebug(ctx) {
		writer := boil.DebugWriterFrom(ctx)
		fmt.Fprintln(writer, sql)
		fmt.Fprintln(writer, args)
	}
	result, err := exec.ExecContext(ctx, sql, args...)
	if err != nil {
		return 0, errors.Wrap(err, "ds: unable to delete all from fLEETZONE_MILEAGE_FEATURE_3MO_V1 slice")
	}

	rowsAff, err := result.RowsAffected()
	if err != nil {
		return 0, errors.Wrap(err, "ds: failed to get rows affected by deleteall for FLEET_ZONE_MILEAGE_FEATURE_3MO_V1")
	}

	if len(fLEETZONE_MILEAGE_FEATURE_3MO_V1AfterDeleteHooks) != 0 {
		for _, obj := range o {
			if err := obj.doAfterDeleteHooks(ctx, exec); err != nil {
				return 0, err
			}
		}
	}

	return rowsAff, nil
}

// Reload refetches the object from the database
// using the primary keys with an executor.
func (o *FLEET_ZONE_MILEAGE_FEATURE_3MO_V1) Reload(ctx context.Context, exec boil.ContextExecutor) error {
	ret, err := FindFLEET_ZONE_MILEAGE_FEATURE_3MO_V1(ctx, exec, o.CONNECTION_ID, o.ZONE_ID, o.START_DATE, o.END_DATE, o.UPDATED_AT)
	if err != nil {
		return err
	}

	*o = *ret
	return nil
}

// ReloadAll refetches every row with matching primary key column values
// and overwrites the original object slice with the newly updated slice.
func (o *FLEET_ZONE_MILEAGE_FEATURE_3MO_V1Slice) ReloadAll(ctx context.Context, exec boil.ContextExecutor) error {
	if o == nil || len(*o) == 0 {
		return nil
	}

	slice := FLEET_ZONE_MILEAGE_FEATURE_3MO_V1Slice{}
	var args []interface{}
	for _, obj := range *o {
		pkeyArgs := queries.ValuesFromMapping(reflect.Indirect(reflect.ValueOf(obj)), fLEETZONE_MILEAGE_FEATURE_3MO_V1PrimaryKeyMapping)
		args = append(args, pkeyArgs...)
	}

	sql := "SELECT \"FLEET_ZONE_MILEAGE_FEATURE_3MO_V1\".* FROM \"FLEET_ZONE_MILEAGE_FEATURE_3MO_V1\" WHERE " +
		strmangle.WhereClauseRepeated(string(dialect.LQ), string(dialect.RQ), 1, fLEETZONE_MILEAGE_FEATURE_3MO_V1PrimaryKeyColumns, len(*o))

	q := queries.Raw(sql, args...)

	err := q.Bind(ctx, exec, &slice)
	if err != nil {
		return errors.Wrap(err, "ds: unable to reload all in FLEET_ZONE_MILEAGE_FEATURE_3MO_V1Slice")
	}

	*o = slice

	return nil
}

// FLEET_ZONE_MILEAGE_FEATURE_3MO_V1Exists checks if the FLEET_ZONE_MILEAGE_FEATURE_3MO_V1 row exists.
func FLEET_ZONE_MILEAGE_FEATURE_3MO_V1Exists(ctx context.Context, exec boil.ContextExecutor, cONNECTIONID string, zONEID int64, sTARTDATE time.Time, eNDDATE time.Time, uPDATEDAT time.Time) (bool, error) {
	var exists bool
	sql := "select exists(select 1 from \"FLEET_ZONE_MILEAGE_FEATURE_3MO_V1\" where \"CONNECTION_ID\"=$1 AND \"ZONE_ID\"=$2 AND \"START_DATE\"=$3 AND \"END_DATE\"=$4 AND \"UPDATED_AT\"=$5 limit 1)"

	if boil.IsDebug(ctx) {
		writer := boil.DebugWriterFrom(ctx)
		fmt.Fprintln(writer, sql)
		fmt.Fprintln(writer, cONNECTIONID, zONEID, sTARTDATE, eNDDATE, uPDATEDAT)
	}
	row := exec.QueryRowContext(ctx, sql, cONNECTIONID, zONEID, sTARTDATE, eNDDATE, uPDATEDAT)

	err := row.Scan(&exists)
	if err != nil {
		return false, errors.Wrap(err, "ds: unable to check if FLEET_ZONE_MILEAGE_FEATURE_3MO_V1 exists")
	}

	return exists, nil
}
