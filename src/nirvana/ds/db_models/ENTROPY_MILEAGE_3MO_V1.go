// Code generated by SQLBoiler (https://github.com/volatiletech/sqlboiler). DO NOT EDIT.
// This file is meant to be re-generated in place and/or deleted at any time.

package ds

import (
	"context"
	"database/sql"
	"fmt"
	"reflect"
	"strconv"
	"strings"
	"sync"
	"time"

	"github.com/friendsofgo/errors"
	"github.com/volatiletech/null/v8"
	"github.com/volatiletech/sqlboiler/v4/boil"
	"github.com/volatiletech/sqlboiler/v4/queries"
	"github.com/volatiletech/sqlboiler/v4/queries/qm"
	"github.com/volatiletech/sqlboiler/v4/queries/qmhelper"
	"github.com/volatiletech/sqlboiler/v4/types"
	"github.com/volatiletech/strmangle"
)

// ENTROPY_MILEAGE_3MO_V1 is an object representing the database table.
type ENTROPY_MILEAGE_3MO_V1 struct {
	END_DATE                  time.Time         `boil:"END_DATE" json:"END_DATE" toml:"END_DATE" yaml:"END_DATE"`
	START_DATE                time.Time         `boil:"START_DATE" json:"START_DATE" toml:"START_DATE" yaml:"START_DATE"`
	UNIQUE_KEY                null.String       `boil:"UNIQUE_KEY" json:"UNIQUE_KEY,omitempty" toml:"UNIQUE_KEY" yaml:"UNIQUE_KEY,omitempty"`
	UPDATED_AT                null.Time         `boil:"UPDATED_AT" json:"UPDATED_AT,omitempty" toml:"UPDATED_AT" yaml:"UPDATED_AT,omitempty"`
	CONNECTION_ID             string            `boil:"CONNECTION_ID" json:"CONNECTION_ID" toml:"CONNECTION_ID" yaml:"CONNECTION_ID"`
	DOMINANT_STATE_GAP        types.NullDecimal `boil:"DOMINANT_STATE_GAP" json:"DOMINANT_STATE_GAP,omitempty" toml:"DOMINANT_STATE_GAP" yaml:"DOMINANT_STATE_GAP,omitempty"`
	DOMINANT_STATE_PCT        types.NullDecimal `boil:"DOMINANT_STATE_PCT" json:"DOMINANT_STATE_PCT,omitempty" toml:"DOMINANT_STATE_PCT" yaml:"DOMINANT_STATE_PCT,omitempty"`
	NUM_STATES_OPERATED       null.Int64        `boil:"NUM_STATES_OPERATED" json:"NUM_STATES_OPERATED,omitempty" toml:"NUM_STATES_OPERATED" yaml:"NUM_STATES_OPERATED,omitempty"`
	STATE_MILEAGE_ENTROPY     types.NullDecimal `boil:"STATE_MILEAGE_ENTROPY" json:"STATE_MILEAGE_ENTROPY,omitempty" toml:"STATE_MILEAGE_ENTROPY" yaml:"STATE_MILEAGE_ENTROPY,omitempty"`
	SECOND_DOMINANT_STATE_PCT types.NullDecimal `boil:"SECOND_DOMINANT_STATE_PCT" json:"SECOND_DOMINANT_STATE_PCT,omitempty" toml:"SECOND_DOMINANT_STATE_PCT" yaml:"SECOND_DOMINANT_STATE_PCT,omitempty"`

	R *eNTROPYMILEAGE_3MO_V1R `boil:"" json:"" toml:"" yaml:""`
	L eNTROPYMILEAGE_3MO_V1L  `boil:"-" json:"-" toml:"-" yaml:"-"`
}

var ENTROPY_MILEAGE_3MO_V1Columns = struct {
	END_DATE                  string
	START_DATE                string
	UNIQUE_KEY                string
	UPDATED_AT                string
	CONNECTION_ID             string
	DOMINANT_STATE_GAP        string
	DOMINANT_STATE_PCT        string
	NUM_STATES_OPERATED       string
	STATE_MILEAGE_ENTROPY     string
	SECOND_DOMINANT_STATE_PCT string
}{
	END_DATE:                  "END_DATE",
	START_DATE:                "START_DATE",
	UNIQUE_KEY:                "UNIQUE_KEY",
	UPDATED_AT:                "UPDATED_AT",
	CONNECTION_ID:             "CONNECTION_ID",
	DOMINANT_STATE_GAP:        "DOMINANT_STATE_GAP",
	DOMINANT_STATE_PCT:        "DOMINANT_STATE_PCT",
	NUM_STATES_OPERATED:       "NUM_STATES_OPERATED",
	STATE_MILEAGE_ENTROPY:     "STATE_MILEAGE_ENTROPY",
	SECOND_DOMINANT_STATE_PCT: "SECOND_DOMINANT_STATE_PCT",
}

var ENTROPY_MILEAGE_3MO_V1TableColumns = struct {
	END_DATE                  string
	START_DATE                string
	UNIQUE_KEY                string
	UPDATED_AT                string
	CONNECTION_ID             string
	DOMINANT_STATE_GAP        string
	DOMINANT_STATE_PCT        string
	NUM_STATES_OPERATED       string
	STATE_MILEAGE_ENTROPY     string
	SECOND_DOMINANT_STATE_PCT string
}{
	END_DATE:                  "ENTROPY_MILEAGE_3MO_V1.END_DATE",
	START_DATE:                "ENTROPY_MILEAGE_3MO_V1.START_DATE",
	UNIQUE_KEY:                "ENTROPY_MILEAGE_3MO_V1.UNIQUE_KEY",
	UPDATED_AT:                "ENTROPY_MILEAGE_3MO_V1.UPDATED_AT",
	CONNECTION_ID:             "ENTROPY_MILEAGE_3MO_V1.CONNECTION_ID",
	DOMINANT_STATE_GAP:        "ENTROPY_MILEAGE_3MO_V1.DOMINANT_STATE_GAP",
	DOMINANT_STATE_PCT:        "ENTROPY_MILEAGE_3MO_V1.DOMINANT_STATE_PCT",
	NUM_STATES_OPERATED:       "ENTROPY_MILEAGE_3MO_V1.NUM_STATES_OPERATED",
	STATE_MILEAGE_ENTROPY:     "ENTROPY_MILEAGE_3MO_V1.STATE_MILEAGE_ENTROPY",
	SECOND_DOMINANT_STATE_PCT: "ENTROPY_MILEAGE_3MO_V1.SECOND_DOMINANT_STATE_PCT",
}

// Generated where

var ENTROPY_MILEAGE_3MO_V1Where = struct {
	END_DATE                  whereHelpertime_Time
	START_DATE                whereHelpertime_Time
	UNIQUE_KEY                whereHelpernull_String
	UPDATED_AT                whereHelpernull_Time
	CONNECTION_ID             whereHelperstring
	DOMINANT_STATE_GAP        whereHelpertypes_NullDecimal
	DOMINANT_STATE_PCT        whereHelpertypes_NullDecimal
	NUM_STATES_OPERATED       whereHelpernull_Int64
	STATE_MILEAGE_ENTROPY     whereHelpertypes_NullDecimal
	SECOND_DOMINANT_STATE_PCT whereHelpertypes_NullDecimal
}{
	END_DATE:                  whereHelpertime_Time{field: "\"ENTROPY_MILEAGE_3MO_V1\".\"END_DATE\""},
	START_DATE:                whereHelpertime_Time{field: "\"ENTROPY_MILEAGE_3MO_V1\".\"START_DATE\""},
	UNIQUE_KEY:                whereHelpernull_String{field: "\"ENTROPY_MILEAGE_3MO_V1\".\"UNIQUE_KEY\""},
	UPDATED_AT:                whereHelpernull_Time{field: "\"ENTROPY_MILEAGE_3MO_V1\".\"UPDATED_AT\""},
	CONNECTION_ID:             whereHelperstring{field: "\"ENTROPY_MILEAGE_3MO_V1\".\"CONNECTION_ID\""},
	DOMINANT_STATE_GAP:        whereHelpertypes_NullDecimal{field: "\"ENTROPY_MILEAGE_3MO_V1\".\"DOMINANT_STATE_GAP\""},
	DOMINANT_STATE_PCT:        whereHelpertypes_NullDecimal{field: "\"ENTROPY_MILEAGE_3MO_V1\".\"DOMINANT_STATE_PCT\""},
	NUM_STATES_OPERATED:       whereHelpernull_Int64{field: "\"ENTROPY_MILEAGE_3MO_V1\".\"NUM_STATES_OPERATED\""},
	STATE_MILEAGE_ENTROPY:     whereHelpertypes_NullDecimal{field: "\"ENTROPY_MILEAGE_3MO_V1\".\"STATE_MILEAGE_ENTROPY\""},
	SECOND_DOMINANT_STATE_PCT: whereHelpertypes_NullDecimal{field: "\"ENTROPY_MILEAGE_3MO_V1\".\"SECOND_DOMINANT_STATE_PCT\""},
}

// ENTROPY_MILEAGE_3MO_V1Rels is where relationship names are stored.
var ENTROPY_MILEAGE_3MO_V1Rels = struct {
}{}

// eNTROPYMILEAGE_3MO_V1R is where relationships are stored.
type eNTROPYMILEAGE_3MO_V1R struct {
}

// NewStruct creates a new relationship struct
func (*eNTROPYMILEAGE_3MO_V1R) NewStruct() *eNTROPYMILEAGE_3MO_V1R {
	return &eNTROPYMILEAGE_3MO_V1R{}
}

// eNTROPYMILEAGE_3MO_V1L is where Load methods for each relationship are stored.
type eNTROPYMILEAGE_3MO_V1L struct{}

var (
	eNTROPYMILEAGE_3MO_V1AllColumns            = []string{"END_DATE", "START_DATE", "UNIQUE_KEY", "UPDATED_AT", "CONNECTION_ID", "DOMINANT_STATE_GAP", "DOMINANT_STATE_PCT", "NUM_STATES_OPERATED", "STATE_MILEAGE_ENTROPY", "SECOND_DOMINANT_STATE_PCT"}
	eNTROPYMILEAGE_3MO_V1ColumnsWithoutDefault = []string{"END_DATE", "START_DATE", "CONNECTION_ID"}
	eNTROPYMILEAGE_3MO_V1ColumnsWithDefault    = []string{"UNIQUE_KEY", "UPDATED_AT", "DOMINANT_STATE_GAP", "DOMINANT_STATE_PCT", "NUM_STATES_OPERATED", "STATE_MILEAGE_ENTROPY", "SECOND_DOMINANT_STATE_PCT"}
	eNTROPYMILEAGE_3MO_V1PrimaryKeyColumns     = []string{"CONNECTION_ID", "START_DATE", "END_DATE"}
	eNTROPYMILEAGE_3MO_V1GeneratedColumns      = []string{}
)

type (
	// ENTROPY_MILEAGE_3MO_V1Slice is an alias for a slice of pointers to ENTROPY_MILEAGE_3MO_V1.
	// This should almost always be used instead of []ENTROPY_MILEAGE_3MO_V1.
	ENTROPY_MILEAGE_3MO_V1Slice []*ENTROPY_MILEAGE_3MO_V1
	// ENTROPY_MILEAGE_3MO_V1Hook is the signature for custom ENTROPY_MILEAGE_3MO_V1 hook methods
	ENTROPY_MILEAGE_3MO_V1Hook func(context.Context, boil.ContextExecutor, *ENTROPY_MILEAGE_3MO_V1) error

	eNTROPYMILEAGE_3MO_V1Query struct {
		*queries.Query
	}
)

// Cache for insert, update and upsert
var (
	eNTROPYMILEAGE_3MO_V1Type                 = reflect.TypeOf(&ENTROPY_MILEAGE_3MO_V1{})
	eNTROPYMILEAGE_3MO_V1Mapping              = queries.MakeStructMapping(eNTROPYMILEAGE_3MO_V1Type)
	eNTROPYMILEAGE_3MO_V1PrimaryKeyMapping, _ = queries.BindMapping(eNTROPYMILEAGE_3MO_V1Type, eNTROPYMILEAGE_3MO_V1Mapping, eNTROPYMILEAGE_3MO_V1PrimaryKeyColumns)
	eNTROPYMILEAGE_3MO_V1InsertCacheMut       sync.RWMutex
	eNTROPYMILEAGE_3MO_V1InsertCache          = make(map[string]insertCache)
	eNTROPYMILEAGE_3MO_V1UpdateCacheMut       sync.RWMutex
	eNTROPYMILEAGE_3MO_V1UpdateCache          = make(map[string]updateCache)
	eNTROPYMILEAGE_3MO_V1UpsertCacheMut       sync.RWMutex
	eNTROPYMILEAGE_3MO_V1UpsertCache          = make(map[string]insertCache)
)

var (
	// Force time package dependency for automated UpdatedAt/CreatedAt.
	_ = time.Second
	// Force qmhelper dependency for where clause generation (which doesn't
	// always happen)
	_ = qmhelper.Where
)

var eNTROPYMILEAGE_3MO_V1AfterSelectHooks []ENTROPY_MILEAGE_3MO_V1Hook

var eNTROPYMILEAGE_3MO_V1BeforeInsertHooks []ENTROPY_MILEAGE_3MO_V1Hook
var eNTROPYMILEAGE_3MO_V1AfterInsertHooks []ENTROPY_MILEAGE_3MO_V1Hook

var eNTROPYMILEAGE_3MO_V1BeforeUpdateHooks []ENTROPY_MILEAGE_3MO_V1Hook
var eNTROPYMILEAGE_3MO_V1AfterUpdateHooks []ENTROPY_MILEAGE_3MO_V1Hook

var eNTROPYMILEAGE_3MO_V1BeforeDeleteHooks []ENTROPY_MILEAGE_3MO_V1Hook
var eNTROPYMILEAGE_3MO_V1AfterDeleteHooks []ENTROPY_MILEAGE_3MO_V1Hook

var eNTROPYMILEAGE_3MO_V1BeforeUpsertHooks []ENTROPY_MILEAGE_3MO_V1Hook
var eNTROPYMILEAGE_3MO_V1AfterUpsertHooks []ENTROPY_MILEAGE_3MO_V1Hook

// doAfterSelectHooks executes all "after Select" hooks.
func (o *ENTROPY_MILEAGE_3MO_V1) doAfterSelectHooks(ctx context.Context, exec boil.ContextExecutor) (err error) {
	if boil.HooksAreSkipped(ctx) {
		return nil
	}

	for _, hook := range eNTROPYMILEAGE_3MO_V1AfterSelectHooks {
		if err := hook(ctx, exec, o); err != nil {
			return err
		}
	}

	return nil
}

// doBeforeInsertHooks executes all "before insert" hooks.
func (o *ENTROPY_MILEAGE_3MO_V1) doBeforeInsertHooks(ctx context.Context, exec boil.ContextExecutor) (err error) {
	if boil.HooksAreSkipped(ctx) {
		return nil
	}

	for _, hook := range eNTROPYMILEAGE_3MO_V1BeforeInsertHooks {
		if err := hook(ctx, exec, o); err != nil {
			return err
		}
	}

	return nil
}

// doAfterInsertHooks executes all "after Insert" hooks.
func (o *ENTROPY_MILEAGE_3MO_V1) doAfterInsertHooks(ctx context.Context, exec boil.ContextExecutor) (err error) {
	if boil.HooksAreSkipped(ctx) {
		return nil
	}

	for _, hook := range eNTROPYMILEAGE_3MO_V1AfterInsertHooks {
		if err := hook(ctx, exec, o); err != nil {
			return err
		}
	}

	return nil
}

// doBeforeUpdateHooks executes all "before Update" hooks.
func (o *ENTROPY_MILEAGE_3MO_V1) doBeforeUpdateHooks(ctx context.Context, exec boil.ContextExecutor) (err error) {
	if boil.HooksAreSkipped(ctx) {
		return nil
	}

	for _, hook := range eNTROPYMILEAGE_3MO_V1BeforeUpdateHooks {
		if err := hook(ctx, exec, o); err != nil {
			return err
		}
	}

	return nil
}

// doAfterUpdateHooks executes all "after Update" hooks.
func (o *ENTROPY_MILEAGE_3MO_V1) doAfterUpdateHooks(ctx context.Context, exec boil.ContextExecutor) (err error) {
	if boil.HooksAreSkipped(ctx) {
		return nil
	}

	for _, hook := range eNTROPYMILEAGE_3MO_V1AfterUpdateHooks {
		if err := hook(ctx, exec, o); err != nil {
			return err
		}
	}

	return nil
}

// doBeforeDeleteHooks executes all "before Delete" hooks.
func (o *ENTROPY_MILEAGE_3MO_V1) doBeforeDeleteHooks(ctx context.Context, exec boil.ContextExecutor) (err error) {
	if boil.HooksAreSkipped(ctx) {
		return nil
	}

	for _, hook := range eNTROPYMILEAGE_3MO_V1BeforeDeleteHooks {
		if err := hook(ctx, exec, o); err != nil {
			return err
		}
	}

	return nil
}

// doAfterDeleteHooks executes all "after Delete" hooks.
func (o *ENTROPY_MILEAGE_3MO_V1) doAfterDeleteHooks(ctx context.Context, exec boil.ContextExecutor) (err error) {
	if boil.HooksAreSkipped(ctx) {
		return nil
	}

	for _, hook := range eNTROPYMILEAGE_3MO_V1AfterDeleteHooks {
		if err := hook(ctx, exec, o); err != nil {
			return err
		}
	}

	return nil
}

// doBeforeUpsertHooks executes all "before Upsert" hooks.
func (o *ENTROPY_MILEAGE_3MO_V1) doBeforeUpsertHooks(ctx context.Context, exec boil.ContextExecutor) (err error) {
	if boil.HooksAreSkipped(ctx) {
		return nil
	}

	for _, hook := range eNTROPYMILEAGE_3MO_V1BeforeUpsertHooks {
		if err := hook(ctx, exec, o); err != nil {
			return err
		}
	}

	return nil
}

// doAfterUpsertHooks executes all "after Upsert" hooks.
func (o *ENTROPY_MILEAGE_3MO_V1) doAfterUpsertHooks(ctx context.Context, exec boil.ContextExecutor) (err error) {
	if boil.HooksAreSkipped(ctx) {
		return nil
	}

	for _, hook := range eNTROPYMILEAGE_3MO_V1AfterUpsertHooks {
		if err := hook(ctx, exec, o); err != nil {
			return err
		}
	}

	return nil
}

// AddENTROPY_MILEAGE_3MO_V1Hook registers your hook function for all future operations.
func AddENTROPY_MILEAGE_3MO_V1Hook(hookPoint boil.HookPoint, eNTROPYMILEAGE_3MO_V1Hook ENTROPY_MILEAGE_3MO_V1Hook) {
	switch hookPoint {
	case boil.AfterSelectHook:
		eNTROPYMILEAGE_3MO_V1AfterSelectHooks = append(eNTROPYMILEAGE_3MO_V1AfterSelectHooks, eNTROPYMILEAGE_3MO_V1Hook)
	case boil.BeforeInsertHook:
		eNTROPYMILEAGE_3MO_V1BeforeInsertHooks = append(eNTROPYMILEAGE_3MO_V1BeforeInsertHooks, eNTROPYMILEAGE_3MO_V1Hook)
	case boil.AfterInsertHook:
		eNTROPYMILEAGE_3MO_V1AfterInsertHooks = append(eNTROPYMILEAGE_3MO_V1AfterInsertHooks, eNTROPYMILEAGE_3MO_V1Hook)
	case boil.BeforeUpdateHook:
		eNTROPYMILEAGE_3MO_V1BeforeUpdateHooks = append(eNTROPYMILEAGE_3MO_V1BeforeUpdateHooks, eNTROPYMILEAGE_3MO_V1Hook)
	case boil.AfterUpdateHook:
		eNTROPYMILEAGE_3MO_V1AfterUpdateHooks = append(eNTROPYMILEAGE_3MO_V1AfterUpdateHooks, eNTROPYMILEAGE_3MO_V1Hook)
	case boil.BeforeDeleteHook:
		eNTROPYMILEAGE_3MO_V1BeforeDeleteHooks = append(eNTROPYMILEAGE_3MO_V1BeforeDeleteHooks, eNTROPYMILEAGE_3MO_V1Hook)
	case boil.AfterDeleteHook:
		eNTROPYMILEAGE_3MO_V1AfterDeleteHooks = append(eNTROPYMILEAGE_3MO_V1AfterDeleteHooks, eNTROPYMILEAGE_3MO_V1Hook)
	case boil.BeforeUpsertHook:
		eNTROPYMILEAGE_3MO_V1BeforeUpsertHooks = append(eNTROPYMILEAGE_3MO_V1BeforeUpsertHooks, eNTROPYMILEAGE_3MO_V1Hook)
	case boil.AfterUpsertHook:
		eNTROPYMILEAGE_3MO_V1AfterUpsertHooks = append(eNTROPYMILEAGE_3MO_V1AfterUpsertHooks, eNTROPYMILEAGE_3MO_V1Hook)
	}
}

// One returns a single eNTROPYMILEAGE_3MO_V1 record from the query.
func (q eNTROPYMILEAGE_3MO_V1Query) One(ctx context.Context, exec boil.ContextExecutor) (*ENTROPY_MILEAGE_3MO_V1, error) {
	o := &ENTROPY_MILEAGE_3MO_V1{}

	queries.SetLimit(q.Query, 1)

	err := q.Bind(ctx, exec, o)
	if err != nil {
		if errors.Is(err, sql.ErrNoRows) {
			return nil, sql.ErrNoRows
		}
		return nil, errors.Wrap(err, "ds: failed to execute a one query for ENTROPY_MILEAGE_3MO_V1")
	}

	if err := o.doAfterSelectHooks(ctx, exec); err != nil {
		return o, err
	}

	return o, nil
}

// All returns all ENTROPY_MILEAGE_3MO_V1 records from the query.
func (q eNTROPYMILEAGE_3MO_V1Query) All(ctx context.Context, exec boil.ContextExecutor) (ENTROPY_MILEAGE_3MO_V1Slice, error) {
	var o []*ENTROPY_MILEAGE_3MO_V1

	err := q.Bind(ctx, exec, &o)
	if err != nil {
		return nil, errors.Wrap(err, "ds: failed to assign all query results to ENTROPY_MILEAGE_3MO_V1 slice")
	}

	if len(eNTROPYMILEAGE_3MO_V1AfterSelectHooks) != 0 {
		for _, obj := range o {
			if err := obj.doAfterSelectHooks(ctx, exec); err != nil {
				return o, err
			}
		}
	}

	return o, nil
}

// Count returns the count of all ENTROPY_MILEAGE_3MO_V1 records in the query.
func (q eNTROPYMILEAGE_3MO_V1Query) Count(ctx context.Context, exec boil.ContextExecutor) (int64, error) {
	var count int64

	queries.SetSelect(q.Query, nil)
	queries.SetCount(q.Query)

	err := q.Query.QueryRowContext(ctx, exec).Scan(&count)
	if err != nil {
		return 0, errors.Wrap(err, "ds: failed to count ENTROPY_MILEAGE_3MO_V1 rows")
	}

	return count, nil
}

// Exists checks if the row exists in the table.
func (q eNTROPYMILEAGE_3MO_V1Query) Exists(ctx context.Context, exec boil.ContextExecutor) (bool, error) {
	var count int64

	queries.SetSelect(q.Query, nil)
	queries.SetCount(q.Query)
	queries.SetLimit(q.Query, 1)

	err := q.Query.QueryRowContext(ctx, exec).Scan(&count)
	if err != nil {
		return false, errors.Wrap(err, "ds: failed to check if ENTROPY_MILEAGE_3MO_V1 exists")
	}

	return count > 0, nil
}

// ENTROPYMILEAGE3MOV1S retrieves all the records using an executor.
func ENTROPYMILEAGE3MOV1S(mods ...qm.QueryMod) eNTROPYMILEAGE_3MO_V1Query {
	mods = append(mods, qm.From("\"ENTROPY_MILEAGE_3MO_V1\""))
	q := NewQuery(mods...)
	if len(queries.GetSelect(q)) == 0 {
		queries.SetSelect(q, []string{"\"ENTROPY_MILEAGE_3MO_V1\".*"})
	}

	return eNTROPYMILEAGE_3MO_V1Query{q}
}

// FindENTROPY_MILEAGE_3MO_V1 retrieves a single record by ID with an executor.
// If selectCols is empty Find will return all columns.
func FindENTROPY_MILEAGE_3MO_V1(ctx context.Context, exec boil.ContextExecutor, cONNECTIONID string, sTARTDATE time.Time, eNDDATE time.Time, selectCols ...string) (*ENTROPY_MILEAGE_3MO_V1, error) {
	eNTROPYMILEAGE_3MO_V1Obj := &ENTROPY_MILEAGE_3MO_V1{}

	sel := "*"
	if len(selectCols) > 0 {
		sel = strings.Join(strmangle.IdentQuoteSlice(dialect.LQ, dialect.RQ, selectCols), ",")
	}
	query := fmt.Sprintf(
		"select %s from \"ENTROPY_MILEAGE_3MO_V1\" where \"CONNECTION_ID\"=$1 AND \"START_DATE\"=$2 AND \"END_DATE\"=$3", sel,
	)

	q := queries.Raw(query, cONNECTIONID, sTARTDATE, eNDDATE)

	err := q.Bind(ctx, exec, eNTROPYMILEAGE_3MO_V1Obj)
	if err != nil {
		if errors.Is(err, sql.ErrNoRows) {
			return nil, sql.ErrNoRows
		}
		return nil, errors.Wrap(err, "ds: unable to select from ENTROPY_MILEAGE_3MO_V1")
	}

	if err = eNTROPYMILEAGE_3MO_V1Obj.doAfterSelectHooks(ctx, exec); err != nil {
		return eNTROPYMILEAGE_3MO_V1Obj, err
	}

	return eNTROPYMILEAGE_3MO_V1Obj, nil
}

// Insert a single record using an executor.
// See boil.Columns.InsertColumnSet documentation to understand column list inference for inserts.
func (o *ENTROPY_MILEAGE_3MO_V1) Insert(ctx context.Context, exec boil.ContextExecutor, columns boil.Columns) error {
	if o == nil {
		return errors.New("ds: no ENTROPY_MILEAGE_3MO_V1 provided for insertion")
	}

	var err error

	if err := o.doBeforeInsertHooks(ctx, exec); err != nil {
		return err
	}

	nzDefaults := queries.NonZeroDefaultSet(eNTROPYMILEAGE_3MO_V1ColumnsWithDefault, o)

	key := makeCacheKey(columns, nzDefaults)
	eNTROPYMILEAGE_3MO_V1InsertCacheMut.RLock()
	cache, cached := eNTROPYMILEAGE_3MO_V1InsertCache[key]
	eNTROPYMILEAGE_3MO_V1InsertCacheMut.RUnlock()

	if !cached {
		wl, returnColumns := columns.InsertColumnSet(
			eNTROPYMILEAGE_3MO_V1AllColumns,
			eNTROPYMILEAGE_3MO_V1ColumnsWithDefault,
			eNTROPYMILEAGE_3MO_V1ColumnsWithoutDefault,
			nzDefaults,
		)

		cache.valueMapping, err = queries.BindMapping(eNTROPYMILEAGE_3MO_V1Type, eNTROPYMILEAGE_3MO_V1Mapping, wl)
		if err != nil {
			return err
		}
		cache.retMapping, err = queries.BindMapping(eNTROPYMILEAGE_3MO_V1Type, eNTROPYMILEAGE_3MO_V1Mapping, returnColumns)
		if err != nil {
			return err
		}
		if len(wl) != 0 {
			cache.query = fmt.Sprintf("INSERT INTO \"ENTROPY_MILEAGE_3MO_V1\" (\"%s\") %%sVALUES (%s)%%s", strings.Join(wl, "\",\""), strmangle.Placeholders(dialect.UseIndexPlaceholders, len(wl), 1, 1))
		} else {
			cache.query = "INSERT INTO \"ENTROPY_MILEAGE_3MO_V1\" %sDEFAULT VALUES%s"
		}

		var queryOutput, queryReturning string

		if len(cache.retMapping) != 0 {
			queryReturning = fmt.Sprintf(" RETURNING \"%s\"", strings.Join(returnColumns, "\",\""))
		}

		cache.query = fmt.Sprintf(cache.query, queryOutput, queryReturning)
	}

	value := reflect.Indirect(reflect.ValueOf(o))
	vals := queries.ValuesFromMapping(value, cache.valueMapping)

	if boil.IsDebug(ctx) {
		writer := boil.DebugWriterFrom(ctx)
		fmt.Fprintln(writer, cache.query)
		fmt.Fprintln(writer, vals)
	}

	if len(cache.retMapping) != 0 {
		err = exec.QueryRowContext(ctx, cache.query, vals...).Scan(queries.PtrsFromMapping(value, cache.retMapping)...)
	} else {
		_, err = exec.ExecContext(ctx, cache.query, vals...)
	}

	if err != nil {
		return errors.Wrap(err, "ds: unable to insert into ENTROPY_MILEAGE_3MO_V1")
	}

	if !cached {
		eNTROPYMILEAGE_3MO_V1InsertCacheMut.Lock()
		eNTROPYMILEAGE_3MO_V1InsertCache[key] = cache
		eNTROPYMILEAGE_3MO_V1InsertCacheMut.Unlock()
	}

	return o.doAfterInsertHooks(ctx, exec)
}

// Update uses an executor to update the ENTROPY_MILEAGE_3MO_V1.
// See boil.Columns.UpdateColumnSet documentation to understand column list inference for updates.
// Update does not automatically update the record in case of default values. Use .Reload() to refresh the records.
func (o *ENTROPY_MILEAGE_3MO_V1) Update(ctx context.Context, exec boil.ContextExecutor, columns boil.Columns) (int64, error) {
	var err error
	if err = o.doBeforeUpdateHooks(ctx, exec); err != nil {
		return 0, err
	}
	key := makeCacheKey(columns, nil)
	eNTROPYMILEAGE_3MO_V1UpdateCacheMut.RLock()
	cache, cached := eNTROPYMILEAGE_3MO_V1UpdateCache[key]
	eNTROPYMILEAGE_3MO_V1UpdateCacheMut.RUnlock()

	if !cached {
		wl := columns.UpdateColumnSet(
			eNTROPYMILEAGE_3MO_V1AllColumns,
			eNTROPYMILEAGE_3MO_V1PrimaryKeyColumns,
		)

		if !columns.IsWhitelist() {
			wl = strmangle.SetComplement(wl, []string{"created_at"})
		}
		if len(wl) == 0 {
			return 0, errors.New("ds: unable to update ENTROPY_MILEAGE_3MO_V1, could not build whitelist")
		}

		cache.query = fmt.Sprintf("UPDATE \"ENTROPY_MILEAGE_3MO_V1\" SET %s WHERE %s",
			strmangle.SetParamNames("\"", "\"", 1, wl),
			strmangle.WhereClause("\"", "\"", len(wl)+1, eNTROPYMILEAGE_3MO_V1PrimaryKeyColumns),
		)
		cache.valueMapping, err = queries.BindMapping(eNTROPYMILEAGE_3MO_V1Type, eNTROPYMILEAGE_3MO_V1Mapping, append(wl, eNTROPYMILEAGE_3MO_V1PrimaryKeyColumns...))
		if err != nil {
			return 0, err
		}
	}

	values := queries.ValuesFromMapping(reflect.Indirect(reflect.ValueOf(o)), cache.valueMapping)

	if boil.IsDebug(ctx) {
		writer := boil.DebugWriterFrom(ctx)
		fmt.Fprintln(writer, cache.query)
		fmt.Fprintln(writer, values)
	}
	var result sql.Result
	result, err = exec.ExecContext(ctx, cache.query, values...)
	if err != nil {
		return 0, errors.Wrap(err, "ds: unable to update ENTROPY_MILEAGE_3MO_V1 row")
	}

	rowsAff, err := result.RowsAffected()
	if err != nil {
		return 0, errors.Wrap(err, "ds: failed to get rows affected by update for ENTROPY_MILEAGE_3MO_V1")
	}

	if !cached {
		eNTROPYMILEAGE_3MO_V1UpdateCacheMut.Lock()
		eNTROPYMILEAGE_3MO_V1UpdateCache[key] = cache
		eNTROPYMILEAGE_3MO_V1UpdateCacheMut.Unlock()
	}

	return rowsAff, o.doAfterUpdateHooks(ctx, exec)
}

// UpdateAll updates all rows with the specified column values.
func (q eNTROPYMILEAGE_3MO_V1Query) UpdateAll(ctx context.Context, exec boil.ContextExecutor, cols M) (int64, error) {
	queries.SetUpdate(q.Query, cols)

	result, err := q.Query.ExecContext(ctx, exec)
	if err != nil {
		return 0, errors.Wrap(err, "ds: unable to update all for ENTROPY_MILEAGE_3MO_V1")
	}

	rowsAff, err := result.RowsAffected()
	if err != nil {
		return 0, errors.Wrap(err, "ds: unable to retrieve rows affected for ENTROPY_MILEAGE_3MO_V1")
	}

	return rowsAff, nil
}

// UpdateAll updates all rows with the specified column values, using an executor.
func (o ENTROPY_MILEAGE_3MO_V1Slice) UpdateAll(ctx context.Context, exec boil.ContextExecutor, cols M) (int64, error) {
	ln := int64(len(o))
	if ln == 0 {
		return 0, nil
	}

	if len(cols) == 0 {
		return 0, errors.New("ds: update all requires at least one column argument")
	}

	colNames := make([]string, len(cols))
	args := make([]interface{}, len(cols))

	i := 0
	for name, value := range cols {
		colNames[i] = name
		args[i] = value
		i++
	}

	// Append all of the primary key values for each column
	for _, obj := range o {
		pkeyArgs := queries.ValuesFromMapping(reflect.Indirect(reflect.ValueOf(obj)), eNTROPYMILEAGE_3MO_V1PrimaryKeyMapping)
		args = append(args, pkeyArgs...)
	}

	sql := fmt.Sprintf("UPDATE \"ENTROPY_MILEAGE_3MO_V1\" SET %s WHERE %s",
		strmangle.SetParamNames("\"", "\"", 1, colNames),
		strmangle.WhereClauseRepeated(string(dialect.LQ), string(dialect.RQ), len(colNames)+1, eNTROPYMILEAGE_3MO_V1PrimaryKeyColumns, len(o)))

	if boil.IsDebug(ctx) {
		writer := boil.DebugWriterFrom(ctx)
		fmt.Fprintln(writer, sql)
		fmt.Fprintln(writer, args...)
	}
	result, err := exec.ExecContext(ctx, sql, args...)
	if err != nil {
		return 0, errors.Wrap(err, "ds: unable to update all in eNTROPYMILEAGE_3MO_V1 slice")
	}

	rowsAff, err := result.RowsAffected()
	if err != nil {
		return 0, errors.Wrap(err, "ds: unable to retrieve rows affected all in update all eNTROPYMILEAGE_3MO_V1")
	}
	return rowsAff, nil
}

// Upsert attempts an insert using an executor, and does an update or ignore on conflict.
// See boil.Columns documentation for how to properly use updateColumns and insertColumns.
func (o *ENTROPY_MILEAGE_3MO_V1) Upsert(ctx context.Context, exec boil.ContextExecutor, updateOnConflict bool, conflictColumns []string, updateColumns, insertColumns boil.Columns) error {
	if o == nil {
		return errors.New("ds: no ENTROPY_MILEAGE_3MO_V1 provided for upsert")
	}

	if err := o.doBeforeUpsertHooks(ctx, exec); err != nil {
		return err
	}

	nzDefaults := queries.NonZeroDefaultSet(eNTROPYMILEAGE_3MO_V1ColumnsWithDefault, o)

	// Build cache key in-line uglily - mysql vs psql problems
	buf := strmangle.GetBuffer()
	if updateOnConflict {
		buf.WriteByte('t')
	} else {
		buf.WriteByte('f')
	}
	buf.WriteByte('.')
	for _, c := range conflictColumns {
		buf.WriteString(c)
	}
	buf.WriteByte('.')
	buf.WriteString(strconv.Itoa(updateColumns.Kind))
	for _, c := range updateColumns.Cols {
		buf.WriteString(c)
	}
	buf.WriteByte('.')
	buf.WriteString(strconv.Itoa(insertColumns.Kind))
	for _, c := range insertColumns.Cols {
		buf.WriteString(c)
	}
	buf.WriteByte('.')
	for _, c := range nzDefaults {
		buf.WriteString(c)
	}
	key := buf.String()
	strmangle.PutBuffer(buf)

	eNTROPYMILEAGE_3MO_V1UpsertCacheMut.RLock()
	cache, cached := eNTROPYMILEAGE_3MO_V1UpsertCache[key]
	eNTROPYMILEAGE_3MO_V1UpsertCacheMut.RUnlock()

	var err error

	if !cached {
		insert, ret := insertColumns.InsertColumnSet(
			eNTROPYMILEAGE_3MO_V1AllColumns,
			eNTROPYMILEAGE_3MO_V1ColumnsWithDefault,
			eNTROPYMILEAGE_3MO_V1ColumnsWithoutDefault,
			nzDefaults,
		)

		update := updateColumns.UpdateColumnSet(
			eNTROPYMILEAGE_3MO_V1AllColumns,
			eNTROPYMILEAGE_3MO_V1PrimaryKeyColumns,
		)

		if updateOnConflict && len(update) == 0 {
			return errors.New("ds: unable to upsert ENTROPY_MILEAGE_3MO_V1, could not build update column list")
		}

		conflict := conflictColumns
		if len(conflict) == 0 {
			conflict = make([]string, len(eNTROPYMILEAGE_3MO_V1PrimaryKeyColumns))
			copy(conflict, eNTROPYMILEAGE_3MO_V1PrimaryKeyColumns)
		}
		cache.query = buildUpsertQueryPostgres(dialect, "\"ENTROPY_MILEAGE_3MO_V1\"", updateOnConflict, ret, update, conflict, insert)

		cache.valueMapping, err = queries.BindMapping(eNTROPYMILEAGE_3MO_V1Type, eNTROPYMILEAGE_3MO_V1Mapping, insert)
		if err != nil {
			return err
		}
		if len(ret) != 0 {
			cache.retMapping, err = queries.BindMapping(eNTROPYMILEAGE_3MO_V1Type, eNTROPYMILEAGE_3MO_V1Mapping, ret)
			if err != nil {
				return err
			}
		}
	}

	value := reflect.Indirect(reflect.ValueOf(o))
	vals := queries.ValuesFromMapping(value, cache.valueMapping)
	var returns []interface{}
	if len(cache.retMapping) != 0 {
		returns = queries.PtrsFromMapping(value, cache.retMapping)
	}

	if boil.IsDebug(ctx) {
		writer := boil.DebugWriterFrom(ctx)
		fmt.Fprintln(writer, cache.query)
		fmt.Fprintln(writer, vals)
	}
	if len(cache.retMapping) != 0 {
		err = exec.QueryRowContext(ctx, cache.query, vals...).Scan(returns...)
		if errors.Is(err, sql.ErrNoRows) {
			err = nil // Postgres doesn't return anything when there's no update
		}
	} else {
		_, err = exec.ExecContext(ctx, cache.query, vals...)
	}
	if err != nil {
		return errors.Wrap(err, "ds: unable to upsert ENTROPY_MILEAGE_3MO_V1")
	}

	if !cached {
		eNTROPYMILEAGE_3MO_V1UpsertCacheMut.Lock()
		eNTROPYMILEAGE_3MO_V1UpsertCache[key] = cache
		eNTROPYMILEAGE_3MO_V1UpsertCacheMut.Unlock()
	}

	return o.doAfterUpsertHooks(ctx, exec)
}

// Delete deletes a single ENTROPY_MILEAGE_3MO_V1 record with an executor.
// Delete will match against the primary key column to find the record to delete.
func (o *ENTROPY_MILEAGE_3MO_V1) Delete(ctx context.Context, exec boil.ContextExecutor) (int64, error) {
	if o == nil {
		return 0, errors.New("ds: no ENTROPY_MILEAGE_3MO_V1 provided for delete")
	}

	if err := o.doBeforeDeleteHooks(ctx, exec); err != nil {
		return 0, err
	}

	args := queries.ValuesFromMapping(reflect.Indirect(reflect.ValueOf(o)), eNTROPYMILEAGE_3MO_V1PrimaryKeyMapping)
	sql := "DELETE FROM \"ENTROPY_MILEAGE_3MO_V1\" WHERE \"CONNECTION_ID\"=$1 AND \"START_DATE\"=$2 AND \"END_DATE\"=$3"

	if boil.IsDebug(ctx) {
		writer := boil.DebugWriterFrom(ctx)
		fmt.Fprintln(writer, sql)
		fmt.Fprintln(writer, args...)
	}
	result, err := exec.ExecContext(ctx, sql, args...)
	if err != nil {
		return 0, errors.Wrap(err, "ds: unable to delete from ENTROPY_MILEAGE_3MO_V1")
	}

	rowsAff, err := result.RowsAffected()
	if err != nil {
		return 0, errors.Wrap(err, "ds: failed to get rows affected by delete for ENTROPY_MILEAGE_3MO_V1")
	}

	if err := o.doAfterDeleteHooks(ctx, exec); err != nil {
		return 0, err
	}

	return rowsAff, nil
}

// DeleteAll deletes all matching rows.
func (q eNTROPYMILEAGE_3MO_V1Query) DeleteAll(ctx context.Context, exec boil.ContextExecutor) (int64, error) {
	if q.Query == nil {
		return 0, errors.New("ds: no eNTROPYMILEAGE_3MO_V1Query provided for delete all")
	}

	queries.SetDelete(q.Query)

	result, err := q.Query.ExecContext(ctx, exec)
	if err != nil {
		return 0, errors.Wrap(err, "ds: unable to delete all from ENTROPY_MILEAGE_3MO_V1")
	}

	rowsAff, err := result.RowsAffected()
	if err != nil {
		return 0, errors.Wrap(err, "ds: failed to get rows affected by deleteall for ENTROPY_MILEAGE_3MO_V1")
	}

	return rowsAff, nil
}

// DeleteAll deletes all rows in the slice, using an executor.
func (o ENTROPY_MILEAGE_3MO_V1Slice) DeleteAll(ctx context.Context, exec boil.ContextExecutor) (int64, error) {
	if len(o) == 0 {
		return 0, nil
	}

	if len(eNTROPYMILEAGE_3MO_V1BeforeDeleteHooks) != 0 {
		for _, obj := range o {
			if err := obj.doBeforeDeleteHooks(ctx, exec); err != nil {
				return 0, err
			}
		}
	}

	var args []interface{}
	for _, obj := range o {
		pkeyArgs := queries.ValuesFromMapping(reflect.Indirect(reflect.ValueOf(obj)), eNTROPYMILEAGE_3MO_V1PrimaryKeyMapping)
		args = append(args, pkeyArgs...)
	}

	sql := "DELETE FROM \"ENTROPY_MILEAGE_3MO_V1\" WHERE " +
		strmangle.WhereClauseRepeated(string(dialect.LQ), string(dialect.RQ), 1, eNTROPYMILEAGE_3MO_V1PrimaryKeyColumns, len(o))

	if boil.IsDebug(ctx) {
		writer := boil.DebugWriterFrom(ctx)
		fmt.Fprintln(writer, sql)
		fmt.Fprintln(writer, args)
	}
	result, err := exec.ExecContext(ctx, sql, args...)
	if err != nil {
		return 0, errors.Wrap(err, "ds: unable to delete all from eNTROPYMILEAGE_3MO_V1 slice")
	}

	rowsAff, err := result.RowsAffected()
	if err != nil {
		return 0, errors.Wrap(err, "ds: failed to get rows affected by deleteall for ENTROPY_MILEAGE_3MO_V1")
	}

	if len(eNTROPYMILEAGE_3MO_V1AfterDeleteHooks) != 0 {
		for _, obj := range o {
			if err := obj.doAfterDeleteHooks(ctx, exec); err != nil {
				return 0, err
			}
		}
	}

	return rowsAff, nil
}

// Reload refetches the object from the database
// using the primary keys with an executor.
func (o *ENTROPY_MILEAGE_3MO_V1) Reload(ctx context.Context, exec boil.ContextExecutor) error {
	ret, err := FindENTROPY_MILEAGE_3MO_V1(ctx, exec, o.CONNECTION_ID, o.START_DATE, o.END_DATE)
	if err != nil {
		return err
	}

	*o = *ret
	return nil
}

// ReloadAll refetches every row with matching primary key column values
// and overwrites the original object slice with the newly updated slice.
func (o *ENTROPY_MILEAGE_3MO_V1Slice) ReloadAll(ctx context.Context, exec boil.ContextExecutor) error {
	if o == nil || len(*o) == 0 {
		return nil
	}

	slice := ENTROPY_MILEAGE_3MO_V1Slice{}
	var args []interface{}
	for _, obj := range *o {
		pkeyArgs := queries.ValuesFromMapping(reflect.Indirect(reflect.ValueOf(obj)), eNTROPYMILEAGE_3MO_V1PrimaryKeyMapping)
		args = append(args, pkeyArgs...)
	}

	sql := "SELECT \"ENTROPY_MILEAGE_3MO_V1\".* FROM \"ENTROPY_MILEAGE_3MO_V1\" WHERE " +
		strmangle.WhereClauseRepeated(string(dialect.LQ), string(dialect.RQ), 1, eNTROPYMILEAGE_3MO_V1PrimaryKeyColumns, len(*o))

	q := queries.Raw(sql, args...)

	err := q.Bind(ctx, exec, &slice)
	if err != nil {
		return errors.Wrap(err, "ds: unable to reload all in ENTROPY_MILEAGE_3MO_V1Slice")
	}

	*o = slice

	return nil
}

// ENTROPY_MILEAGE_3MO_V1Exists checks if the ENTROPY_MILEAGE_3MO_V1 row exists.
func ENTROPY_MILEAGE_3MO_V1Exists(ctx context.Context, exec boil.ContextExecutor, cONNECTIONID string, sTARTDATE time.Time, eNDDATE time.Time) (bool, error) {
	var exists bool
	sql := "select exists(select 1 from \"ENTROPY_MILEAGE_3MO_V1\" where \"CONNECTION_ID\"=$1 AND \"START_DATE\"=$2 AND \"END_DATE\"=$3 limit 1)"

	if boil.IsDebug(ctx) {
		writer := boil.DebugWriterFrom(ctx)
		fmt.Fprintln(writer, sql)
		fmt.Fprintln(writer, cONNECTIONID, sTARTDATE, eNDDATE)
	}
	row := exec.QueryRowContext(ctx, sql, cONNECTIONID, sTARTDATE, eNDDATE)

	err := row.Scan(&exists)
	if err != nil {
		return false, errors.Wrap(err, "ds: unable to check if ENTROPY_MILEAGE_3MO_V1 exists")
	}

	return exists, nil
}
