// Code generated by SQLBoiler (https://github.com/volatiletech/sqlboiler). DO NOT EDIT.
// This file is meant to be re-generated in place and/or deleted at any time.

package telematics

import (
	"context"
	"database/sql"
	"fmt"
	"reflect"
	"strconv"
	"strings"
	"sync"
	"time"

	"github.com/friendsofgo/errors"
	"github.com/volatiletech/null/v8"
	"github.com/volatiletech/sqlboiler/v4/boil"
	"github.com/volatiletech/sqlboiler/v4/queries"
	"github.com/volatiletech/sqlboiler/v4/queries/qm"
	"github.com/volatiletech/sqlboiler/v4/queries/qmhelper"
	"github.com/volatiletech/sqlboiler/v4/types"
	"github.com/volatiletech/strmangle"
)

// Backfill is an object representing the database table.
type Backfill struct {
	HandleID                        string      `boil:"handle_id" json:"handle_id" toml:"handle_id" yaml:"handle_id"`
	Deliverable                     types.JSON  `boil:"deliverable" json:"deliverable" toml:"deliverable" yaml:"deliverable"`
	Delivered                       bool        `boil:"delivered" json:"delivered" toml:"delivered" yaml:"delivered"`
	LastAttemptedByJobRunID         null.String `boil:"last_attempted_by_job_run_id" json:"last_attempted_by_job_run_id,omitempty" toml:"last_attempted_by_job_run_id" yaml:"last_attempted_by_job_run_id,omitempty"`
	LastAttemptedByBackfillJobRunID null.String `boil:"last_attempted_by_backfill_job_run_id" json:"last_attempted_by_backfill_job_run_id,omitempty" toml:"last_attempted_by_backfill_job_run_id" yaml:"last_attempted_by_backfill_job_run_id,omitempty"`
	FailedAttemptsCount             int         `boil:"failed_attempts_count" json:"failed_attempts_count" toml:"failed_attempts_count" yaml:"failed_attempts_count"`
	CreatedAt                       time.Time   `boil:"created_at" json:"created_at" toml:"created_at" yaml:"created_at"`
	UpdatedAt                       time.Time   `boil:"updated_at" json:"updated_at" toml:"updated_at" yaml:"updated_at"`
	DeletedAt                       null.Time   `boil:"deleted_at" json:"deleted_at,omitempty" toml:"deleted_at" yaml:"deleted_at,omitempty"`

	R *backfillR `boil:"" json:"" toml:"" yaml:""`
	L backfillL  `boil:"-" json:"-" toml:"-" yaml:"-"`
}

var BackfillColumns = struct {
	HandleID                        string
	Deliverable                     string
	Delivered                       string
	LastAttemptedByJobRunID         string
	LastAttemptedByBackfillJobRunID string
	FailedAttemptsCount             string
	CreatedAt                       string
	UpdatedAt                       string
	DeletedAt                       string
}{
	HandleID:                        "handle_id",
	Deliverable:                     "deliverable",
	Delivered:                       "delivered",
	LastAttemptedByJobRunID:         "last_attempted_by_job_run_id",
	LastAttemptedByBackfillJobRunID: "last_attempted_by_backfill_job_run_id",
	FailedAttemptsCount:             "failed_attempts_count",
	CreatedAt:                       "created_at",
	UpdatedAt:                       "updated_at",
	DeletedAt:                       "deleted_at",
}

var BackfillTableColumns = struct {
	HandleID                        string
	Deliverable                     string
	Delivered                       string
	LastAttemptedByJobRunID         string
	LastAttemptedByBackfillJobRunID string
	FailedAttemptsCount             string
	CreatedAt                       string
	UpdatedAt                       string
	DeletedAt                       string
}{
	HandleID:                        "backfills.handle_id",
	Deliverable:                     "backfills.deliverable",
	Delivered:                       "backfills.delivered",
	LastAttemptedByJobRunID:         "backfills.last_attempted_by_job_run_id",
	LastAttemptedByBackfillJobRunID: "backfills.last_attempted_by_backfill_job_run_id",
	FailedAttemptsCount:             "backfills.failed_attempts_count",
	CreatedAt:                       "backfills.created_at",
	UpdatedAt:                       "backfills.updated_at",
	DeletedAt:                       "backfills.deleted_at",
}

// Generated where

type whereHelpertypes_JSON struct{ field string }

func (w whereHelpertypes_JSON) EQ(x types.JSON) qm.QueryMod {
	return qmhelper.Where(w.field, qmhelper.EQ, x)
}
func (w whereHelpertypes_JSON) NEQ(x types.JSON) qm.QueryMod {
	return qmhelper.Where(w.field, qmhelper.NEQ, x)
}
func (w whereHelpertypes_JSON) LT(x types.JSON) qm.QueryMod {
	return qmhelper.Where(w.field, qmhelper.LT, x)
}
func (w whereHelpertypes_JSON) LTE(x types.JSON) qm.QueryMod {
	return qmhelper.Where(w.field, qmhelper.LTE, x)
}
func (w whereHelpertypes_JSON) GT(x types.JSON) qm.QueryMod {
	return qmhelper.Where(w.field, qmhelper.GT, x)
}
func (w whereHelpertypes_JSON) GTE(x types.JSON) qm.QueryMod {
	return qmhelper.Where(w.field, qmhelper.GTE, x)
}

type whereHelperbool struct{ field string }

func (w whereHelperbool) EQ(x bool) qm.QueryMod  { return qmhelper.Where(w.field, qmhelper.EQ, x) }
func (w whereHelperbool) NEQ(x bool) qm.QueryMod { return qmhelper.Where(w.field, qmhelper.NEQ, x) }
func (w whereHelperbool) LT(x bool) qm.QueryMod  { return qmhelper.Where(w.field, qmhelper.LT, x) }
func (w whereHelperbool) LTE(x bool) qm.QueryMod { return qmhelper.Where(w.field, qmhelper.LTE, x) }
func (w whereHelperbool) GT(x bool) qm.QueryMod  { return qmhelper.Where(w.field, qmhelper.GT, x) }
func (w whereHelperbool) GTE(x bool) qm.QueryMod { return qmhelper.Where(w.field, qmhelper.GTE, x) }

type whereHelpernull_String struct{ field string }

func (w whereHelpernull_String) EQ(x null.String) qm.QueryMod {
	return qmhelper.WhereNullEQ(w.field, false, x)
}
func (w whereHelpernull_String) NEQ(x null.String) qm.QueryMod {
	return qmhelper.WhereNullEQ(w.field, true, x)
}
func (w whereHelpernull_String) LT(x null.String) qm.QueryMod {
	return qmhelper.Where(w.field, qmhelper.LT, x)
}
func (w whereHelpernull_String) LTE(x null.String) qm.QueryMod {
	return qmhelper.Where(w.field, qmhelper.LTE, x)
}
func (w whereHelpernull_String) GT(x null.String) qm.QueryMod {
	return qmhelper.Where(w.field, qmhelper.GT, x)
}
func (w whereHelpernull_String) GTE(x null.String) qm.QueryMod {
	return qmhelper.Where(w.field, qmhelper.GTE, x)
}

func (w whereHelpernull_String) IsNull() qm.QueryMod    { return qmhelper.WhereIsNull(w.field) }
func (w whereHelpernull_String) IsNotNull() qm.QueryMod { return qmhelper.WhereIsNotNull(w.field) }

type whereHelperint struct{ field string }

func (w whereHelperint) EQ(x int) qm.QueryMod  { return qmhelper.Where(w.field, qmhelper.EQ, x) }
func (w whereHelperint) NEQ(x int) qm.QueryMod { return qmhelper.Where(w.field, qmhelper.NEQ, x) }
func (w whereHelperint) LT(x int) qm.QueryMod  { return qmhelper.Where(w.field, qmhelper.LT, x) }
func (w whereHelperint) LTE(x int) qm.QueryMod { return qmhelper.Where(w.field, qmhelper.LTE, x) }
func (w whereHelperint) GT(x int) qm.QueryMod  { return qmhelper.Where(w.field, qmhelper.GT, x) }
func (w whereHelperint) GTE(x int) qm.QueryMod { return qmhelper.Where(w.field, qmhelper.GTE, x) }
func (w whereHelperint) IN(slice []int) qm.QueryMod {
	values := make([]interface{}, 0, len(slice))
	for _, value := range slice {
		values = append(values, value)
	}
	return qm.WhereIn(fmt.Sprintf("%s IN ?", w.field), values...)
}
func (w whereHelperint) NIN(slice []int) qm.QueryMod {
	values := make([]interface{}, 0, len(slice))
	for _, value := range slice {
		values = append(values, value)
	}
	return qm.WhereNotIn(fmt.Sprintf("%s NOT IN ?", w.field), values...)
}

var BackfillWhere = struct {
	HandleID                        whereHelperstring
	Deliverable                     whereHelpertypes_JSON
	Delivered                       whereHelperbool
	LastAttemptedByJobRunID         whereHelpernull_String
	LastAttemptedByBackfillJobRunID whereHelpernull_String
	FailedAttemptsCount             whereHelperint
	CreatedAt                       whereHelpertime_Time
	UpdatedAt                       whereHelpertime_Time
	DeletedAt                       whereHelpernull_Time
}{
	HandleID:                        whereHelperstring{field: "\"telematics\".\"backfills\".\"handle_id\""},
	Deliverable:                     whereHelpertypes_JSON{field: "\"telematics\".\"backfills\".\"deliverable\""},
	Delivered:                       whereHelperbool{field: "\"telematics\".\"backfills\".\"delivered\""},
	LastAttemptedByJobRunID:         whereHelpernull_String{field: "\"telematics\".\"backfills\".\"last_attempted_by_job_run_id\""},
	LastAttemptedByBackfillJobRunID: whereHelpernull_String{field: "\"telematics\".\"backfills\".\"last_attempted_by_backfill_job_run_id\""},
	FailedAttemptsCount:             whereHelperint{field: "\"telematics\".\"backfills\".\"failed_attempts_count\""},
	CreatedAt:                       whereHelpertime_Time{field: "\"telematics\".\"backfills\".\"created_at\""},
	UpdatedAt:                       whereHelpertime_Time{field: "\"telematics\".\"backfills\".\"updated_at\""},
	DeletedAt:                       whereHelpernull_Time{field: "\"telematics\".\"backfills\".\"deleted_at\""},
}

// BackfillRels is where relationship names are stored.
var BackfillRels = struct {
}{}

// backfillR is where relationships are stored.
type backfillR struct {
}

// NewStruct creates a new relationship struct
func (*backfillR) NewStruct() *backfillR {
	return &backfillR{}
}

// backfillL is where Load methods for each relationship are stored.
type backfillL struct{}

var (
	backfillAllColumns            = []string{"handle_id", "deliverable", "delivered", "last_attempted_by_job_run_id", "last_attempted_by_backfill_job_run_id", "failed_attempts_count", "created_at", "updated_at", "deleted_at"}
	backfillColumnsWithoutDefault = []string{"handle_id", "deliverable", "delivered", "failed_attempts_count", "created_at", "updated_at"}
	backfillColumnsWithDefault    = []string{"last_attempted_by_job_run_id", "last_attempted_by_backfill_job_run_id", "deleted_at"}
	backfillPrimaryKeyColumns     = []string{"handle_id"}
	backfillGeneratedColumns      = []string{}
)

type (
	// BackfillSlice is an alias for a slice of pointers to Backfill.
	// This should almost always be used instead of []Backfill.
	BackfillSlice []*Backfill
	// BackfillHook is the signature for custom Backfill hook methods
	BackfillHook func(context.Context, boil.ContextExecutor, *Backfill) error

	backfillQuery struct {
		*queries.Query
	}
)

// Cache for insert, update and upsert
var (
	backfillType                 = reflect.TypeOf(&Backfill{})
	backfillMapping              = queries.MakeStructMapping(backfillType)
	backfillPrimaryKeyMapping, _ = queries.BindMapping(backfillType, backfillMapping, backfillPrimaryKeyColumns)
	backfillInsertCacheMut       sync.RWMutex
	backfillInsertCache          = make(map[string]insertCache)
	backfillUpdateCacheMut       sync.RWMutex
	backfillUpdateCache          = make(map[string]updateCache)
	backfillUpsertCacheMut       sync.RWMutex
	backfillUpsertCache          = make(map[string]insertCache)
)

var (
	// Force time package dependency for automated UpdatedAt/CreatedAt.
	_ = time.Second
	// Force qmhelper dependency for where clause generation (which doesn't
	// always happen)
	_ = qmhelper.Where
)

var backfillAfterSelectHooks []BackfillHook

var backfillBeforeInsertHooks []BackfillHook
var backfillAfterInsertHooks []BackfillHook

var backfillBeforeUpdateHooks []BackfillHook
var backfillAfterUpdateHooks []BackfillHook

var backfillBeforeDeleteHooks []BackfillHook
var backfillAfterDeleteHooks []BackfillHook

var backfillBeforeUpsertHooks []BackfillHook
var backfillAfterUpsertHooks []BackfillHook

// doAfterSelectHooks executes all "after Select" hooks.
func (o *Backfill) doAfterSelectHooks(ctx context.Context, exec boil.ContextExecutor) (err error) {
	if boil.HooksAreSkipped(ctx) {
		return nil
	}

	for _, hook := range backfillAfterSelectHooks {
		if err := hook(ctx, exec, o); err != nil {
			return err
		}
	}

	return nil
}

// doBeforeInsertHooks executes all "before insert" hooks.
func (o *Backfill) doBeforeInsertHooks(ctx context.Context, exec boil.ContextExecutor) (err error) {
	if boil.HooksAreSkipped(ctx) {
		return nil
	}

	for _, hook := range backfillBeforeInsertHooks {
		if err := hook(ctx, exec, o); err != nil {
			return err
		}
	}

	return nil
}

// doAfterInsertHooks executes all "after Insert" hooks.
func (o *Backfill) doAfterInsertHooks(ctx context.Context, exec boil.ContextExecutor) (err error) {
	if boil.HooksAreSkipped(ctx) {
		return nil
	}

	for _, hook := range backfillAfterInsertHooks {
		if err := hook(ctx, exec, o); err != nil {
			return err
		}
	}

	return nil
}

// doBeforeUpdateHooks executes all "before Update" hooks.
func (o *Backfill) doBeforeUpdateHooks(ctx context.Context, exec boil.ContextExecutor) (err error) {
	if boil.HooksAreSkipped(ctx) {
		return nil
	}

	for _, hook := range backfillBeforeUpdateHooks {
		if err := hook(ctx, exec, o); err != nil {
			return err
		}
	}

	return nil
}

// doAfterUpdateHooks executes all "after Update" hooks.
func (o *Backfill) doAfterUpdateHooks(ctx context.Context, exec boil.ContextExecutor) (err error) {
	if boil.HooksAreSkipped(ctx) {
		return nil
	}

	for _, hook := range backfillAfterUpdateHooks {
		if err := hook(ctx, exec, o); err != nil {
			return err
		}
	}

	return nil
}

// doBeforeDeleteHooks executes all "before Delete" hooks.
func (o *Backfill) doBeforeDeleteHooks(ctx context.Context, exec boil.ContextExecutor) (err error) {
	if boil.HooksAreSkipped(ctx) {
		return nil
	}

	for _, hook := range backfillBeforeDeleteHooks {
		if err := hook(ctx, exec, o); err != nil {
			return err
		}
	}

	return nil
}

// doAfterDeleteHooks executes all "after Delete" hooks.
func (o *Backfill) doAfterDeleteHooks(ctx context.Context, exec boil.ContextExecutor) (err error) {
	if boil.HooksAreSkipped(ctx) {
		return nil
	}

	for _, hook := range backfillAfterDeleteHooks {
		if err := hook(ctx, exec, o); err != nil {
			return err
		}
	}

	return nil
}

// doBeforeUpsertHooks executes all "before Upsert" hooks.
func (o *Backfill) doBeforeUpsertHooks(ctx context.Context, exec boil.ContextExecutor) (err error) {
	if boil.HooksAreSkipped(ctx) {
		return nil
	}

	for _, hook := range backfillBeforeUpsertHooks {
		if err := hook(ctx, exec, o); err != nil {
			return err
		}
	}

	return nil
}

// doAfterUpsertHooks executes all "after Upsert" hooks.
func (o *Backfill) doAfterUpsertHooks(ctx context.Context, exec boil.ContextExecutor) (err error) {
	if boil.HooksAreSkipped(ctx) {
		return nil
	}

	for _, hook := range backfillAfterUpsertHooks {
		if err := hook(ctx, exec, o); err != nil {
			return err
		}
	}

	return nil
}

// AddBackfillHook registers your hook function for all future operations.
func AddBackfillHook(hookPoint boil.HookPoint, backfillHook BackfillHook) {
	switch hookPoint {
	case boil.AfterSelectHook:
		backfillAfterSelectHooks = append(backfillAfterSelectHooks, backfillHook)
	case boil.BeforeInsertHook:
		backfillBeforeInsertHooks = append(backfillBeforeInsertHooks, backfillHook)
	case boil.AfterInsertHook:
		backfillAfterInsertHooks = append(backfillAfterInsertHooks, backfillHook)
	case boil.BeforeUpdateHook:
		backfillBeforeUpdateHooks = append(backfillBeforeUpdateHooks, backfillHook)
	case boil.AfterUpdateHook:
		backfillAfterUpdateHooks = append(backfillAfterUpdateHooks, backfillHook)
	case boil.BeforeDeleteHook:
		backfillBeforeDeleteHooks = append(backfillBeforeDeleteHooks, backfillHook)
	case boil.AfterDeleteHook:
		backfillAfterDeleteHooks = append(backfillAfterDeleteHooks, backfillHook)
	case boil.BeforeUpsertHook:
		backfillBeforeUpsertHooks = append(backfillBeforeUpsertHooks, backfillHook)
	case boil.AfterUpsertHook:
		backfillAfterUpsertHooks = append(backfillAfterUpsertHooks, backfillHook)
	}
}

// One returns a single backfill record from the query.
func (q backfillQuery) One(ctx context.Context, exec boil.ContextExecutor) (*Backfill, error) {
	o := &Backfill{}

	queries.SetLimit(q.Query, 1)

	err := q.Bind(ctx, exec, o)
	if err != nil {
		if errors.Is(err, sql.ErrNoRows) {
			return nil, sql.ErrNoRows
		}
		return nil, errors.Wrap(err, "telematics: failed to execute a one query for backfills")
	}

	if err := o.doAfterSelectHooks(ctx, exec); err != nil {
		return o, err
	}

	return o, nil
}

// All returns all Backfill records from the query.
func (q backfillQuery) All(ctx context.Context, exec boil.ContextExecutor) (BackfillSlice, error) {
	var o []*Backfill

	err := q.Bind(ctx, exec, &o)
	if err != nil {
		return nil, errors.Wrap(err, "telematics: failed to assign all query results to Backfill slice")
	}

	if len(backfillAfterSelectHooks) != 0 {
		for _, obj := range o {
			if err := obj.doAfterSelectHooks(ctx, exec); err != nil {
				return o, err
			}
		}
	}

	return o, nil
}

// Count returns the count of all Backfill records in the query.
func (q backfillQuery) Count(ctx context.Context, exec boil.ContextExecutor) (int64, error) {
	var count int64

	queries.SetSelect(q.Query, nil)
	queries.SetCount(q.Query)

	err := q.Query.QueryRowContext(ctx, exec).Scan(&count)
	if err != nil {
		return 0, errors.Wrap(err, "telematics: failed to count backfills rows")
	}

	return count, nil
}

// Exists checks if the row exists in the table.
func (q backfillQuery) Exists(ctx context.Context, exec boil.ContextExecutor) (bool, error) {
	var count int64

	queries.SetSelect(q.Query, nil)
	queries.SetCount(q.Query)
	queries.SetLimit(q.Query, 1)

	err := q.Query.QueryRowContext(ctx, exec).Scan(&count)
	if err != nil {
		return false, errors.Wrap(err, "telematics: failed to check if backfills exists")
	}

	return count > 0, nil
}

// Backfills retrieves all the records using an executor.
func Backfills(mods ...qm.QueryMod) backfillQuery {
	mods = append(mods, qm.From("\"telematics\".\"backfills\""))
	q := NewQuery(mods...)
	if len(queries.GetSelect(q)) == 0 {
		queries.SetSelect(q, []string{"\"telematics\".\"backfills\".*"})
	}

	return backfillQuery{q}
}

// FindBackfill retrieves a single record by ID with an executor.
// If selectCols is empty Find will return all columns.
func FindBackfill(ctx context.Context, exec boil.ContextExecutor, handleID string, selectCols ...string) (*Backfill, error) {
	backfillObj := &Backfill{}

	sel := "*"
	if len(selectCols) > 0 {
		sel = strings.Join(strmangle.IdentQuoteSlice(dialect.LQ, dialect.RQ, selectCols), ",")
	}
	query := fmt.Sprintf(
		"select %s from \"telematics\".\"backfills\" where \"handle_id\"=$1", sel,
	)

	q := queries.Raw(query, handleID)

	err := q.Bind(ctx, exec, backfillObj)
	if err != nil {
		if errors.Is(err, sql.ErrNoRows) {
			return nil, sql.ErrNoRows
		}
		return nil, errors.Wrap(err, "telematics: unable to select from backfills")
	}

	if err = backfillObj.doAfterSelectHooks(ctx, exec); err != nil {
		return backfillObj, err
	}

	return backfillObj, nil
}

// Insert a single record using an executor.
// See boil.Columns.InsertColumnSet documentation to understand column list inference for inserts.
func (o *Backfill) Insert(ctx context.Context, exec boil.ContextExecutor, columns boil.Columns) error {
	if o == nil {
		return errors.New("telematics: no backfills provided for insertion")
	}

	var err error
	if !boil.TimestampsAreSkipped(ctx) {
		currTime := time.Now().In(boil.GetLocation())

		if o.CreatedAt.IsZero() {
			o.CreatedAt = currTime
		}
		if o.UpdatedAt.IsZero() {
			o.UpdatedAt = currTime
		}
	}

	if err := o.doBeforeInsertHooks(ctx, exec); err != nil {
		return err
	}

	nzDefaults := queries.NonZeroDefaultSet(backfillColumnsWithDefault, o)

	key := makeCacheKey(columns, nzDefaults)
	backfillInsertCacheMut.RLock()
	cache, cached := backfillInsertCache[key]
	backfillInsertCacheMut.RUnlock()

	if !cached {
		wl, returnColumns := columns.InsertColumnSet(
			backfillAllColumns,
			backfillColumnsWithDefault,
			backfillColumnsWithoutDefault,
			nzDefaults,
		)

		cache.valueMapping, err = queries.BindMapping(backfillType, backfillMapping, wl)
		if err != nil {
			return err
		}
		cache.retMapping, err = queries.BindMapping(backfillType, backfillMapping, returnColumns)
		if err != nil {
			return err
		}
		if len(wl) != 0 {
			cache.query = fmt.Sprintf("INSERT INTO \"telematics\".\"backfills\" (\"%s\") %%sVALUES (%s)%%s", strings.Join(wl, "\",\""), strmangle.Placeholders(dialect.UseIndexPlaceholders, len(wl), 1, 1))
		} else {
			cache.query = "INSERT INTO \"telematics\".\"backfills\" %sDEFAULT VALUES%s"
		}

		var queryOutput, queryReturning string

		if len(cache.retMapping) != 0 {
			queryReturning = fmt.Sprintf(" RETURNING \"%s\"", strings.Join(returnColumns, "\",\""))
		}

		cache.query = fmt.Sprintf(cache.query, queryOutput, queryReturning)
	}

	value := reflect.Indirect(reflect.ValueOf(o))
	vals := queries.ValuesFromMapping(value, cache.valueMapping)

	if boil.IsDebug(ctx) {
		writer := boil.DebugWriterFrom(ctx)
		fmt.Fprintln(writer, cache.query)
		fmt.Fprintln(writer, vals)
	}

	if len(cache.retMapping) != 0 {
		err = exec.QueryRowContext(ctx, cache.query, vals...).Scan(queries.PtrsFromMapping(value, cache.retMapping)...)
	} else {
		_, err = exec.ExecContext(ctx, cache.query, vals...)
	}

	if err != nil {
		return errors.Wrap(err, "telematics: unable to insert into backfills")
	}

	if !cached {
		backfillInsertCacheMut.Lock()
		backfillInsertCache[key] = cache
		backfillInsertCacheMut.Unlock()
	}

	return o.doAfterInsertHooks(ctx, exec)
}

// Update uses an executor to update the Backfill.
// See boil.Columns.UpdateColumnSet documentation to understand column list inference for updates.
// Update does not automatically update the record in case of default values. Use .Reload() to refresh the records.
func (o *Backfill) Update(ctx context.Context, exec boil.ContextExecutor, columns boil.Columns) (int64, error) {
	if !boil.TimestampsAreSkipped(ctx) {
		currTime := time.Now().In(boil.GetLocation())

		o.UpdatedAt = currTime
	}

	var err error
	if err = o.doBeforeUpdateHooks(ctx, exec); err != nil {
		return 0, err
	}
	key := makeCacheKey(columns, nil)
	backfillUpdateCacheMut.RLock()
	cache, cached := backfillUpdateCache[key]
	backfillUpdateCacheMut.RUnlock()

	if !cached {
		wl := columns.UpdateColumnSet(
			backfillAllColumns,
			backfillPrimaryKeyColumns,
		)

		if !columns.IsWhitelist() {
			wl = strmangle.SetComplement(wl, []string{"created_at"})
		}
		if len(wl) == 0 {
			return 0, errors.New("telematics: unable to update backfills, could not build whitelist")
		}

		cache.query = fmt.Sprintf("UPDATE \"telematics\".\"backfills\" SET %s WHERE %s",
			strmangle.SetParamNames("\"", "\"", 1, wl),
			strmangle.WhereClause("\"", "\"", len(wl)+1, backfillPrimaryKeyColumns),
		)
		cache.valueMapping, err = queries.BindMapping(backfillType, backfillMapping, append(wl, backfillPrimaryKeyColumns...))
		if err != nil {
			return 0, err
		}
	}

	values := queries.ValuesFromMapping(reflect.Indirect(reflect.ValueOf(o)), cache.valueMapping)

	if boil.IsDebug(ctx) {
		writer := boil.DebugWriterFrom(ctx)
		fmt.Fprintln(writer, cache.query)
		fmt.Fprintln(writer, values)
	}
	var result sql.Result
	result, err = exec.ExecContext(ctx, cache.query, values...)
	if err != nil {
		return 0, errors.Wrap(err, "telematics: unable to update backfills row")
	}

	rowsAff, err := result.RowsAffected()
	if err != nil {
		return 0, errors.Wrap(err, "telematics: failed to get rows affected by update for backfills")
	}

	if !cached {
		backfillUpdateCacheMut.Lock()
		backfillUpdateCache[key] = cache
		backfillUpdateCacheMut.Unlock()
	}

	return rowsAff, o.doAfterUpdateHooks(ctx, exec)
}

// UpdateAll updates all rows with the specified column values.
func (q backfillQuery) UpdateAll(ctx context.Context, exec boil.ContextExecutor, cols M) (int64, error) {
	queries.SetUpdate(q.Query, cols)

	result, err := q.Query.ExecContext(ctx, exec)
	if err != nil {
		return 0, errors.Wrap(err, "telematics: unable to update all for backfills")
	}

	rowsAff, err := result.RowsAffected()
	if err != nil {
		return 0, errors.Wrap(err, "telematics: unable to retrieve rows affected for backfills")
	}

	return rowsAff, nil
}

// UpdateAll updates all rows with the specified column values, using an executor.
func (o BackfillSlice) UpdateAll(ctx context.Context, exec boil.ContextExecutor, cols M) (int64, error) {
	ln := int64(len(o))
	if ln == 0 {
		return 0, nil
	}

	if len(cols) == 0 {
		return 0, errors.New("telematics: update all requires at least one column argument")
	}

	colNames := make([]string, len(cols))
	args := make([]interface{}, len(cols))

	i := 0
	for name, value := range cols {
		colNames[i] = name
		args[i] = value
		i++
	}

	// Append all of the primary key values for each column
	for _, obj := range o {
		pkeyArgs := queries.ValuesFromMapping(reflect.Indirect(reflect.ValueOf(obj)), backfillPrimaryKeyMapping)
		args = append(args, pkeyArgs...)
	}

	sql := fmt.Sprintf("UPDATE \"telematics\".\"backfills\" SET %s WHERE %s",
		strmangle.SetParamNames("\"", "\"", 1, colNames),
		strmangle.WhereClauseRepeated(string(dialect.LQ), string(dialect.RQ), len(colNames)+1, backfillPrimaryKeyColumns, len(o)))

	if boil.IsDebug(ctx) {
		writer := boil.DebugWriterFrom(ctx)
		fmt.Fprintln(writer, sql)
		fmt.Fprintln(writer, args...)
	}
	result, err := exec.ExecContext(ctx, sql, args...)
	if err != nil {
		return 0, errors.Wrap(err, "telematics: unable to update all in backfill slice")
	}

	rowsAff, err := result.RowsAffected()
	if err != nil {
		return 0, errors.Wrap(err, "telematics: unable to retrieve rows affected all in update all backfill")
	}
	return rowsAff, nil
}

// Upsert attempts an insert using an executor, and does an update or ignore on conflict.
// See boil.Columns documentation for how to properly use updateColumns and insertColumns.
func (o *Backfill) Upsert(ctx context.Context, exec boil.ContextExecutor, updateOnConflict bool, conflictColumns []string, updateColumns, insertColumns boil.Columns) error {
	if o == nil {
		return errors.New("telematics: no backfills provided for upsert")
	}
	if !boil.TimestampsAreSkipped(ctx) {
		currTime := time.Now().In(boil.GetLocation())

		if o.CreatedAt.IsZero() {
			o.CreatedAt = currTime
		}
		o.UpdatedAt = currTime
	}

	if err := o.doBeforeUpsertHooks(ctx, exec); err != nil {
		return err
	}

	nzDefaults := queries.NonZeroDefaultSet(backfillColumnsWithDefault, o)

	// Build cache key in-line uglily - mysql vs psql problems
	buf := strmangle.GetBuffer()
	if updateOnConflict {
		buf.WriteByte('t')
	} else {
		buf.WriteByte('f')
	}
	buf.WriteByte('.')
	for _, c := range conflictColumns {
		buf.WriteString(c)
	}
	buf.WriteByte('.')
	buf.WriteString(strconv.Itoa(updateColumns.Kind))
	for _, c := range updateColumns.Cols {
		buf.WriteString(c)
	}
	buf.WriteByte('.')
	buf.WriteString(strconv.Itoa(insertColumns.Kind))
	for _, c := range insertColumns.Cols {
		buf.WriteString(c)
	}
	buf.WriteByte('.')
	for _, c := range nzDefaults {
		buf.WriteString(c)
	}
	key := buf.String()
	strmangle.PutBuffer(buf)

	backfillUpsertCacheMut.RLock()
	cache, cached := backfillUpsertCache[key]
	backfillUpsertCacheMut.RUnlock()

	var err error

	if !cached {
		insert, ret := insertColumns.InsertColumnSet(
			backfillAllColumns,
			backfillColumnsWithDefault,
			backfillColumnsWithoutDefault,
			nzDefaults,
		)

		update := updateColumns.UpdateColumnSet(
			backfillAllColumns,
			backfillPrimaryKeyColumns,
		)

		if updateOnConflict && len(update) == 0 {
			return errors.New("telematics: unable to upsert backfills, could not build update column list")
		}

		conflict := conflictColumns
		if len(conflict) == 0 {
			conflict = make([]string, len(backfillPrimaryKeyColumns))
			copy(conflict, backfillPrimaryKeyColumns)
		}
		cache.query = buildUpsertQueryPostgres(dialect, "\"telematics\".\"backfills\"", updateOnConflict, ret, update, conflict, insert)

		cache.valueMapping, err = queries.BindMapping(backfillType, backfillMapping, insert)
		if err != nil {
			return err
		}
		if len(ret) != 0 {
			cache.retMapping, err = queries.BindMapping(backfillType, backfillMapping, ret)
			if err != nil {
				return err
			}
		}
	}

	value := reflect.Indirect(reflect.ValueOf(o))
	vals := queries.ValuesFromMapping(value, cache.valueMapping)
	var returns []interface{}
	if len(cache.retMapping) != 0 {
		returns = queries.PtrsFromMapping(value, cache.retMapping)
	}

	if boil.IsDebug(ctx) {
		writer := boil.DebugWriterFrom(ctx)
		fmt.Fprintln(writer, cache.query)
		fmt.Fprintln(writer, vals)
	}
	if len(cache.retMapping) != 0 {
		err = exec.QueryRowContext(ctx, cache.query, vals...).Scan(returns...)
		if errors.Is(err, sql.ErrNoRows) {
			err = nil // Postgres doesn't return anything when there's no update
		}
	} else {
		_, err = exec.ExecContext(ctx, cache.query, vals...)
	}
	if err != nil {
		return errors.Wrap(err, "telematics: unable to upsert backfills")
	}

	if !cached {
		backfillUpsertCacheMut.Lock()
		backfillUpsertCache[key] = cache
		backfillUpsertCacheMut.Unlock()
	}

	return o.doAfterUpsertHooks(ctx, exec)
}

// Delete deletes a single Backfill record with an executor.
// Delete will match against the primary key column to find the record to delete.
func (o *Backfill) Delete(ctx context.Context, exec boil.ContextExecutor) (int64, error) {
	if o == nil {
		return 0, errors.New("telematics: no Backfill provided for delete")
	}

	if err := o.doBeforeDeleteHooks(ctx, exec); err != nil {
		return 0, err
	}

	args := queries.ValuesFromMapping(reflect.Indirect(reflect.ValueOf(o)), backfillPrimaryKeyMapping)
	sql := "DELETE FROM \"telematics\".\"backfills\" WHERE \"handle_id\"=$1"

	if boil.IsDebug(ctx) {
		writer := boil.DebugWriterFrom(ctx)
		fmt.Fprintln(writer, sql)
		fmt.Fprintln(writer, args...)
	}
	result, err := exec.ExecContext(ctx, sql, args...)
	if err != nil {
		return 0, errors.Wrap(err, "telematics: unable to delete from backfills")
	}

	rowsAff, err := result.RowsAffected()
	if err != nil {
		return 0, errors.Wrap(err, "telematics: failed to get rows affected by delete for backfills")
	}

	if err := o.doAfterDeleteHooks(ctx, exec); err != nil {
		return 0, err
	}

	return rowsAff, nil
}

// DeleteAll deletes all matching rows.
func (q backfillQuery) DeleteAll(ctx context.Context, exec boil.ContextExecutor) (int64, error) {
	if q.Query == nil {
		return 0, errors.New("telematics: no backfillQuery provided for delete all")
	}

	queries.SetDelete(q.Query)

	result, err := q.Query.ExecContext(ctx, exec)
	if err != nil {
		return 0, errors.Wrap(err, "telematics: unable to delete all from backfills")
	}

	rowsAff, err := result.RowsAffected()
	if err != nil {
		return 0, errors.Wrap(err, "telematics: failed to get rows affected by deleteall for backfills")
	}

	return rowsAff, nil
}

// DeleteAll deletes all rows in the slice, using an executor.
func (o BackfillSlice) DeleteAll(ctx context.Context, exec boil.ContextExecutor) (int64, error) {
	if len(o) == 0 {
		return 0, nil
	}

	if len(backfillBeforeDeleteHooks) != 0 {
		for _, obj := range o {
			if err := obj.doBeforeDeleteHooks(ctx, exec); err != nil {
				return 0, err
			}
		}
	}

	var args []interface{}
	for _, obj := range o {
		pkeyArgs := queries.ValuesFromMapping(reflect.Indirect(reflect.ValueOf(obj)), backfillPrimaryKeyMapping)
		args = append(args, pkeyArgs...)
	}

	sql := "DELETE FROM \"telematics\".\"backfills\" WHERE " +
		strmangle.WhereClauseRepeated(string(dialect.LQ), string(dialect.RQ), 1, backfillPrimaryKeyColumns, len(o))

	if boil.IsDebug(ctx) {
		writer := boil.DebugWriterFrom(ctx)
		fmt.Fprintln(writer, sql)
		fmt.Fprintln(writer, args)
	}
	result, err := exec.ExecContext(ctx, sql, args...)
	if err != nil {
		return 0, errors.Wrap(err, "telematics: unable to delete all from backfill slice")
	}

	rowsAff, err := result.RowsAffected()
	if err != nil {
		return 0, errors.Wrap(err, "telematics: failed to get rows affected by deleteall for backfills")
	}

	if len(backfillAfterDeleteHooks) != 0 {
		for _, obj := range o {
			if err := obj.doAfterDeleteHooks(ctx, exec); err != nil {
				return 0, err
			}
		}
	}

	return rowsAff, nil
}

// Reload refetches the object from the database
// using the primary keys with an executor.
func (o *Backfill) Reload(ctx context.Context, exec boil.ContextExecutor) error {
	ret, err := FindBackfill(ctx, exec, o.HandleID)
	if err != nil {
		return err
	}

	*o = *ret
	return nil
}

// ReloadAll refetches every row with matching primary key column values
// and overwrites the original object slice with the newly updated slice.
func (o *BackfillSlice) ReloadAll(ctx context.Context, exec boil.ContextExecutor) error {
	if o == nil || len(*o) == 0 {
		return nil
	}

	slice := BackfillSlice{}
	var args []interface{}
	for _, obj := range *o {
		pkeyArgs := queries.ValuesFromMapping(reflect.Indirect(reflect.ValueOf(obj)), backfillPrimaryKeyMapping)
		args = append(args, pkeyArgs...)
	}

	sql := "SELECT \"telematics\".\"backfills\".* FROM \"telematics\".\"backfills\" WHERE " +
		strmangle.WhereClauseRepeated(string(dialect.LQ), string(dialect.RQ), 1, backfillPrimaryKeyColumns, len(*o))

	q := queries.Raw(sql, args...)

	err := q.Bind(ctx, exec, &slice)
	if err != nil {
		return errors.Wrap(err, "telematics: unable to reload all in BackfillSlice")
	}

	*o = slice

	return nil
}

// BackfillExists checks if the Backfill row exists.
func BackfillExists(ctx context.Context, exec boil.ContextExecutor, handleID string) (bool, error) {
	var exists bool
	sql := "select exists(select 1 from \"telematics\".\"backfills\" where \"handle_id\"=$1 limit 1)"

	if boil.IsDebug(ctx) {
		writer := boil.DebugWriterFrom(ctx)
		fmt.Fprintln(writer, sql)
		fmt.Fprintln(writer, handleID)
	}
	row := exec.QueryRowContext(ctx, sql, handleID)

	err := row.Scan(&exists)
	if err != nil {
		return false, errors.Wrap(err, "telematics: unable to check if backfills exists")
	}

	return exists, nil
}
