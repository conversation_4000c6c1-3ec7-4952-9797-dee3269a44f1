// Code generated by SQLBoiler (https://github.com/volatiletech/sqlboiler). DO NOT EDIT.
// This file is meant to be re-generated in place and/or deleted at any time.

package parsed_loss_runs

var TableNames = struct {
	Aggregation             string
	Claim                   string
	Document                string
	DocumentStateTransition string
	Loss                    string
	Policy                  string
	ProcessedLoss           string
}{
	Aggregation:             "aggregation",
	Claim:                   "claim",
	Document:                "document",
	DocumentStateTransition: "document_state_transition",
	Loss:                    "loss",
	Policy:                  "policy",
	ProcessedLoss:           "processed_loss",
}
