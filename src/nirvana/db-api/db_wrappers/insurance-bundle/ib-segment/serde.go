package ibsegment

import (
	"github.com/volatiletech/null/v8"
	"google.golang.org/protobuf/encoding/protojson"

	"nirvanatech.com/nirvana/db-api/db_wrappers/insurance-bundle/policyv2"

	insurancecoreproto "nirvanatech.com/nirvana/insurance-core/proto"

	"github.com/cockroachdb/errors"
	"google.golang.org/protobuf/types/known/timestamppb"

	"nirvanatech.com/nirvana/common-go/proto"
	"nirvanatech.com/nirvana/db-api/db_models/insurance_bundle"
	"nirvanatech.com/nirvana/insurance-bundle/model"
)

func convertIBSegmentDBRepresentationToIBSegmentDBModel(
	ibSegmentDBRepr IBSegmentDBRepresentation,
) (*insurance_bundle.InsuranceBundleSegment, error) {
	ibSegment := ibSegmentDBRepr.IBSegment

	marshalledPrimaryInsured, err := protojson.Marshal(ibSegment.PrimaryInsured)
	if err != nil {
		return nil, errors.Wrap(err, "failed to marshal primary insured")
	}
	marshalledCoverageCriteria, err := protojson.Marshal(ibSegment.CoverageCriteria)
	if err != nil {
		return nil, errors.Wrap(err, "failed to marshal coverage criteria")
	}
	marshalledNOC, err := protojson.Marshal(ibSegment.NoticeOfCancellation)
	if err != nil {
		return nil, errors.Wrap(err, "failed to marshal notice of cancellation")
	}
	noticeOfCancellationJSON := null.JSONFrom(marshalledNOC)

	return &insurance_bundle.InsuranceBundleSegment{
		ID:                   ibSegment.GetId(),
		InsuranceBundleID:    ibSegmentDBRepr.IBID,
		IntervalStart:        ibSegment.Interval.Start.AsTime(),
		IntervalEnd:          ibSegment.Interval.End.AsTime(),
		PrimaryInsured:       marshalledPrimaryInsured,
		CoverageCriteria:     marshalledCoverageCriteria,
		PrimaryInsuredID:     ibSegment.PrimaryInsured.Id,
		NoticeOfCancellation: noticeOfCancellationJSON,
	}, nil
}

func ConvertIBSegmentDBModelToIBSegmentProto(
	ibSegmentDBModel *insurance_bundle.InsuranceBundleSegment,
) (*model.InsuranceBundleSegment, error) {
	if ibSegmentDBModel == nil {
		return nil, errors.New("nil insurance bundle segment")
	}

	var primaryInsured insurancecoreproto.Insured
	if err := protojson.Unmarshal(ibSegmentDBModel.PrimaryInsured, &primaryInsured); err != nil {
		return nil, errors.Wrap(err, "failed to unmarshal primary insured")
	}
	var coverageCriteria model.CoverageCriteria
	if err := protojson.Unmarshal(ibSegmentDBModel.CoverageCriteria, &coverageCriteria); err != nil {
		return nil, errors.Wrap(err, "failed to unmarshal coverage criteria")
	}

	policies := map[string]*model.Policy{}
	if ibSegmentDBModel.R != nil {
		for _, policyDBModel := range ibSegmentDBModel.R.Policies {
			policy, err := policyv2.ConvertPolicyDBModelToPolicyProto(policyDBModel)
			if err != nil {
				return nil, errors.Wrap(err, "failed to convert policy db model to proto")
			}
			policies[policy.GetPolicyNumber()] = policy
		}
	}

	return &model.InsuranceBundleSegment{
		Id: ibSegmentDBModel.ID,
		Interval: &proto.Interval{
			Start: timestamppb.New(ibSegmentDBModel.IntervalStart),
			End:   timestamppb.New(ibSegmentDBModel.IntervalEnd),
		},
		PrimaryInsured:   &primaryInsured,
		Policies:         policies,
		CoverageCriteria: &coverageCriteria,
	}, nil
}
