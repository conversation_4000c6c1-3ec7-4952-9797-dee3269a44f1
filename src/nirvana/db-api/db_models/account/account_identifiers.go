// Code generated by SQLBoiler (https://github.com/volatiletech/sqlboiler). DO NOT EDIT.
// This file is meant to be re-generated in place and/or deleted at any time.

package account

import (
	"context"
	"database/sql"
	"fmt"
	"reflect"
	"strconv"
	"strings"
	"sync"
	"time"

	"github.com/friendsofgo/errors"
	"github.com/volatiletech/sqlboiler/v4/boil"
	"github.com/volatiletech/sqlboiler/v4/queries"
	"github.com/volatiletech/sqlboiler/v4/queries/qm"
	"github.com/volatiletech/sqlboiler/v4/queries/qmhelper"
	"github.com/volatiletech/strmangle"
)

// AccountIdentifier is an object representing the database table.
type AccountIdentifier struct {
	AccountIdentifierID string `boil:"account_identifier_id" json:"account_identifier_id" toml:"account_identifier_id" yaml:"account_identifier_id"`
	AccountID           string `boil:"account_id" json:"account_id" toml:"account_id" yaml:"account_id"`
	IDType              string `boil:"id_type" json:"id_type" toml:"id_type" yaml:"id_type"`
	IDValue             string `boil:"id_value" json:"id_value" toml:"id_value" yaml:"id_value"`

	R *accountIdentifierR `boil:"" json:"" toml:"" yaml:""`
	L accountIdentifierL  `boil:"-" json:"-" toml:"-" yaml:"-"`
}

var AccountIdentifierColumns = struct {
	AccountIdentifierID string
	AccountID           string
	IDType              string
	IDValue             string
}{
	AccountIdentifierID: "account_identifier_id",
	AccountID:           "account_id",
	IDType:              "id_type",
	IDValue:             "id_value",
}

var AccountIdentifierTableColumns = struct {
	AccountIdentifierID string
	AccountID           string
	IDType              string
	IDValue             string
}{
	AccountIdentifierID: "account_identifiers.account_identifier_id",
	AccountID:           "account_identifiers.account_id",
	IDType:              "account_identifiers.id_type",
	IDValue:             "account_identifiers.id_value",
}

// Generated where

var AccountIdentifierWhere = struct {
	AccountIdentifierID whereHelperstring
	AccountID           whereHelperstring
	IDType              whereHelperstring
	IDValue             whereHelperstring
}{
	AccountIdentifierID: whereHelperstring{field: "\"account\".\"account_identifiers\".\"account_identifier_id\""},
	AccountID:           whereHelperstring{field: "\"account\".\"account_identifiers\".\"account_id\""},
	IDType:              whereHelperstring{field: "\"account\".\"account_identifiers\".\"id_type\""},
	IDValue:             whereHelperstring{field: "\"account\".\"account_identifiers\".\"id_value\""},
}

// AccountIdentifierRels is where relationship names are stored.
var AccountIdentifierRels = struct {
	Account string
}{
	Account: "Account",
}

// accountIdentifierR is where relationships are stored.
type accountIdentifierR struct {
	Account *Account `boil:"Account" json:"Account" toml:"Account" yaml:"Account"`
}

// NewStruct creates a new relationship struct
func (*accountIdentifierR) NewStruct() *accountIdentifierR {
	return &accountIdentifierR{}
}

// accountIdentifierL is where Load methods for each relationship are stored.
type accountIdentifierL struct{}

var (
	accountIdentifierAllColumns            = []string{"account_identifier_id", "account_id", "id_type", "id_value"}
	accountIdentifierColumnsWithoutDefault = []string{"account_identifier_id", "account_id", "id_type", "id_value"}
	accountIdentifierColumnsWithDefault    = []string{}
	accountIdentifierPrimaryKeyColumns     = []string{"account_identifier_id"}
	accountIdentifierGeneratedColumns      = []string{}
)

type (
	// AccountIdentifierSlice is an alias for a slice of pointers to AccountIdentifier.
	// This should almost always be used instead of []AccountIdentifier.
	AccountIdentifierSlice []*AccountIdentifier
	// AccountIdentifierHook is the signature for custom AccountIdentifier hook methods
	AccountIdentifierHook func(context.Context, boil.ContextExecutor, *AccountIdentifier) error

	accountIdentifierQuery struct {
		*queries.Query
	}
)

// Cache for insert, update and upsert
var (
	accountIdentifierType                 = reflect.TypeOf(&AccountIdentifier{})
	accountIdentifierMapping              = queries.MakeStructMapping(accountIdentifierType)
	accountIdentifierPrimaryKeyMapping, _ = queries.BindMapping(accountIdentifierType, accountIdentifierMapping, accountIdentifierPrimaryKeyColumns)
	accountIdentifierInsertCacheMut       sync.RWMutex
	accountIdentifierInsertCache          = make(map[string]insertCache)
	accountIdentifierUpdateCacheMut       sync.RWMutex
	accountIdentifierUpdateCache          = make(map[string]updateCache)
	accountIdentifierUpsertCacheMut       sync.RWMutex
	accountIdentifierUpsertCache          = make(map[string]insertCache)
)

var (
	// Force time package dependency for automated UpdatedAt/CreatedAt.
	_ = time.Second
	// Force qmhelper dependency for where clause generation (which doesn't
	// always happen)
	_ = qmhelper.Where
)

var accountIdentifierAfterSelectHooks []AccountIdentifierHook

var accountIdentifierBeforeInsertHooks []AccountIdentifierHook
var accountIdentifierAfterInsertHooks []AccountIdentifierHook

var accountIdentifierBeforeUpdateHooks []AccountIdentifierHook
var accountIdentifierAfterUpdateHooks []AccountIdentifierHook

var accountIdentifierBeforeDeleteHooks []AccountIdentifierHook
var accountIdentifierAfterDeleteHooks []AccountIdentifierHook

var accountIdentifierBeforeUpsertHooks []AccountIdentifierHook
var accountIdentifierAfterUpsertHooks []AccountIdentifierHook

// doAfterSelectHooks executes all "after Select" hooks.
func (o *AccountIdentifier) doAfterSelectHooks(ctx context.Context, exec boil.ContextExecutor) (err error) {
	if boil.HooksAreSkipped(ctx) {
		return nil
	}

	for _, hook := range accountIdentifierAfterSelectHooks {
		if err := hook(ctx, exec, o); err != nil {
			return err
		}
	}

	return nil
}

// doBeforeInsertHooks executes all "before insert" hooks.
func (o *AccountIdentifier) doBeforeInsertHooks(ctx context.Context, exec boil.ContextExecutor) (err error) {
	if boil.HooksAreSkipped(ctx) {
		return nil
	}

	for _, hook := range accountIdentifierBeforeInsertHooks {
		if err := hook(ctx, exec, o); err != nil {
			return err
		}
	}

	return nil
}

// doAfterInsertHooks executes all "after Insert" hooks.
func (o *AccountIdentifier) doAfterInsertHooks(ctx context.Context, exec boil.ContextExecutor) (err error) {
	if boil.HooksAreSkipped(ctx) {
		return nil
	}

	for _, hook := range accountIdentifierAfterInsertHooks {
		if err := hook(ctx, exec, o); err != nil {
			return err
		}
	}

	return nil
}

// doBeforeUpdateHooks executes all "before Update" hooks.
func (o *AccountIdentifier) doBeforeUpdateHooks(ctx context.Context, exec boil.ContextExecutor) (err error) {
	if boil.HooksAreSkipped(ctx) {
		return nil
	}

	for _, hook := range accountIdentifierBeforeUpdateHooks {
		if err := hook(ctx, exec, o); err != nil {
			return err
		}
	}

	return nil
}

// doAfterUpdateHooks executes all "after Update" hooks.
func (o *AccountIdentifier) doAfterUpdateHooks(ctx context.Context, exec boil.ContextExecutor) (err error) {
	if boil.HooksAreSkipped(ctx) {
		return nil
	}

	for _, hook := range accountIdentifierAfterUpdateHooks {
		if err := hook(ctx, exec, o); err != nil {
			return err
		}
	}

	return nil
}

// doBeforeDeleteHooks executes all "before Delete" hooks.
func (o *AccountIdentifier) doBeforeDeleteHooks(ctx context.Context, exec boil.ContextExecutor) (err error) {
	if boil.HooksAreSkipped(ctx) {
		return nil
	}

	for _, hook := range accountIdentifierBeforeDeleteHooks {
		if err := hook(ctx, exec, o); err != nil {
			return err
		}
	}

	return nil
}

// doAfterDeleteHooks executes all "after Delete" hooks.
func (o *AccountIdentifier) doAfterDeleteHooks(ctx context.Context, exec boil.ContextExecutor) (err error) {
	if boil.HooksAreSkipped(ctx) {
		return nil
	}

	for _, hook := range accountIdentifierAfterDeleteHooks {
		if err := hook(ctx, exec, o); err != nil {
			return err
		}
	}

	return nil
}

// doBeforeUpsertHooks executes all "before Upsert" hooks.
func (o *AccountIdentifier) doBeforeUpsertHooks(ctx context.Context, exec boil.ContextExecutor) (err error) {
	if boil.HooksAreSkipped(ctx) {
		return nil
	}

	for _, hook := range accountIdentifierBeforeUpsertHooks {
		if err := hook(ctx, exec, o); err != nil {
			return err
		}
	}

	return nil
}

// doAfterUpsertHooks executes all "after Upsert" hooks.
func (o *AccountIdentifier) doAfterUpsertHooks(ctx context.Context, exec boil.ContextExecutor) (err error) {
	if boil.HooksAreSkipped(ctx) {
		return nil
	}

	for _, hook := range accountIdentifierAfterUpsertHooks {
		if err := hook(ctx, exec, o); err != nil {
			return err
		}
	}

	return nil
}

// AddAccountIdentifierHook registers your hook function for all future operations.
func AddAccountIdentifierHook(hookPoint boil.HookPoint, accountIdentifierHook AccountIdentifierHook) {
	switch hookPoint {
	case boil.AfterSelectHook:
		accountIdentifierAfterSelectHooks = append(accountIdentifierAfterSelectHooks, accountIdentifierHook)
	case boil.BeforeInsertHook:
		accountIdentifierBeforeInsertHooks = append(accountIdentifierBeforeInsertHooks, accountIdentifierHook)
	case boil.AfterInsertHook:
		accountIdentifierAfterInsertHooks = append(accountIdentifierAfterInsertHooks, accountIdentifierHook)
	case boil.BeforeUpdateHook:
		accountIdentifierBeforeUpdateHooks = append(accountIdentifierBeforeUpdateHooks, accountIdentifierHook)
	case boil.AfterUpdateHook:
		accountIdentifierAfterUpdateHooks = append(accountIdentifierAfterUpdateHooks, accountIdentifierHook)
	case boil.BeforeDeleteHook:
		accountIdentifierBeforeDeleteHooks = append(accountIdentifierBeforeDeleteHooks, accountIdentifierHook)
	case boil.AfterDeleteHook:
		accountIdentifierAfterDeleteHooks = append(accountIdentifierAfterDeleteHooks, accountIdentifierHook)
	case boil.BeforeUpsertHook:
		accountIdentifierBeforeUpsertHooks = append(accountIdentifierBeforeUpsertHooks, accountIdentifierHook)
	case boil.AfterUpsertHook:
		accountIdentifierAfterUpsertHooks = append(accountIdentifierAfterUpsertHooks, accountIdentifierHook)
	}
}

// One returns a single accountIdentifier record from the query.
func (q accountIdentifierQuery) One(ctx context.Context, exec boil.ContextExecutor) (*AccountIdentifier, error) {
	o := &AccountIdentifier{}

	queries.SetLimit(q.Query, 1)

	err := q.Bind(ctx, exec, o)
	if err != nil {
		if errors.Is(err, sql.ErrNoRows) {
			return nil, sql.ErrNoRows
		}
		return nil, errors.Wrap(err, "account: failed to execute a one query for account_identifiers")
	}

	if err := o.doAfterSelectHooks(ctx, exec); err != nil {
		return o, err
	}

	return o, nil
}

// All returns all AccountIdentifier records from the query.
func (q accountIdentifierQuery) All(ctx context.Context, exec boil.ContextExecutor) (AccountIdentifierSlice, error) {
	var o []*AccountIdentifier

	err := q.Bind(ctx, exec, &o)
	if err != nil {
		return nil, errors.Wrap(err, "account: failed to assign all query results to AccountIdentifier slice")
	}

	if len(accountIdentifierAfterSelectHooks) != 0 {
		for _, obj := range o {
			if err := obj.doAfterSelectHooks(ctx, exec); err != nil {
				return o, err
			}
		}
	}

	return o, nil
}

// Count returns the count of all AccountIdentifier records in the query.
func (q accountIdentifierQuery) Count(ctx context.Context, exec boil.ContextExecutor) (int64, error) {
	var count int64

	queries.SetSelect(q.Query, nil)
	queries.SetCount(q.Query)

	err := q.Query.QueryRowContext(ctx, exec).Scan(&count)
	if err != nil {
		return 0, errors.Wrap(err, "account: failed to count account_identifiers rows")
	}

	return count, nil
}

// Exists checks if the row exists in the table.
func (q accountIdentifierQuery) Exists(ctx context.Context, exec boil.ContextExecutor) (bool, error) {
	var count int64

	queries.SetSelect(q.Query, nil)
	queries.SetCount(q.Query)
	queries.SetLimit(q.Query, 1)

	err := q.Query.QueryRowContext(ctx, exec).Scan(&count)
	if err != nil {
		return false, errors.Wrap(err, "account: failed to check if account_identifiers exists")
	}

	return count > 0, nil
}

// Account pointed to by the foreign key.
func (o *AccountIdentifier) Account(mods ...qm.QueryMod) accountQuery {
	queryMods := []qm.QueryMod{
		qm.Where("\"account_id\" = ?", o.AccountID),
	}

	queryMods = append(queryMods, mods...)

	return Accounts(queryMods...)
}

// LoadAccount allows an eager lookup of values, cached into the
// loaded structs of the objects. This is for an N-1 relationship.
func (accountIdentifierL) LoadAccount(ctx context.Context, e boil.ContextExecutor, singular bool, maybeAccountIdentifier interface{}, mods queries.Applicator) error {
	var slice []*AccountIdentifier
	var object *AccountIdentifier

	if singular {
		object = maybeAccountIdentifier.(*AccountIdentifier)
	} else {
		slice = *maybeAccountIdentifier.(*[]*AccountIdentifier)
	}

	args := make([]interface{}, 0, 1)
	if singular {
		if object.R == nil {
			object.R = &accountIdentifierR{}
		}
		args = append(args, object.AccountID)

	} else {
	Outer:
		for _, obj := range slice {
			if obj.R == nil {
				obj.R = &accountIdentifierR{}
			}

			for _, a := range args {
				if a == obj.AccountID {
					continue Outer
				}
			}

			args = append(args, obj.AccountID)

		}
	}

	if len(args) == 0 {
		return nil
	}

	query := NewQuery(
		qm.From(`account.accounts`),
		qm.WhereIn(`account.accounts.account_id in ?`, args...),
	)
	if mods != nil {
		mods.Apply(query)
	}

	results, err := query.QueryContext(ctx, e)
	if err != nil {
		return errors.Wrap(err, "failed to eager load Account")
	}

	var resultSlice []*Account
	if err = queries.Bind(results, &resultSlice); err != nil {
		return errors.Wrap(err, "failed to bind eager loaded slice Account")
	}

	if err = results.Close(); err != nil {
		return errors.Wrap(err, "failed to close results of eager load for accounts")
	}
	if err = results.Err(); err != nil {
		return errors.Wrap(err, "error occurred during iteration of eager loaded relations for accounts")
	}

	if len(accountIdentifierAfterSelectHooks) != 0 {
		for _, obj := range resultSlice {
			if err := obj.doAfterSelectHooks(ctx, e); err != nil {
				return err
			}
		}
	}

	if len(resultSlice) == 0 {
		return nil
	}

	if singular {
		foreign := resultSlice[0]
		object.R.Account = foreign
		if foreign.R == nil {
			foreign.R = &accountR{}
		}
		foreign.R.AccountIdentifiers = append(foreign.R.AccountIdentifiers, object)
		return nil
	}

	for _, local := range slice {
		for _, foreign := range resultSlice {
			if local.AccountID == foreign.AccountID {
				local.R.Account = foreign
				if foreign.R == nil {
					foreign.R = &accountR{}
				}
				foreign.R.AccountIdentifiers = append(foreign.R.AccountIdentifiers, local)
				break
			}
		}
	}

	return nil
}

// SetAccount of the accountIdentifier to the related item.
// Sets o.R.Account to related.
// Adds o to related.R.AccountIdentifiers.
func (o *AccountIdentifier) SetAccount(ctx context.Context, exec boil.ContextExecutor, insert bool, related *Account) error {
	var err error
	if insert {
		if err = related.Insert(ctx, exec, boil.Infer()); err != nil {
			return errors.Wrap(err, "failed to insert into foreign table")
		}
	}

	updateQuery := fmt.Sprintf(
		"UPDATE \"account\".\"account_identifiers\" SET %s WHERE %s",
		strmangle.SetParamNames("\"", "\"", 1, []string{"account_id"}),
		strmangle.WhereClause("\"", "\"", 2, accountIdentifierPrimaryKeyColumns),
	)
	values := []interface{}{related.AccountID, o.AccountIdentifierID}

	if boil.IsDebug(ctx) {
		writer := boil.DebugWriterFrom(ctx)
		fmt.Fprintln(writer, updateQuery)
		fmt.Fprintln(writer, values)
	}
	if _, err = exec.ExecContext(ctx, updateQuery, values...); err != nil {
		return errors.Wrap(err, "failed to update local table")
	}

	o.AccountID = related.AccountID
	if o.R == nil {
		o.R = &accountIdentifierR{
			Account: related,
		}
	} else {
		o.R.Account = related
	}

	if related.R == nil {
		related.R = &accountR{
			AccountIdentifiers: AccountIdentifierSlice{o},
		}
	} else {
		related.R.AccountIdentifiers = append(related.R.AccountIdentifiers, o)
	}

	return nil
}

// AccountIdentifiers retrieves all the records using an executor.
func AccountIdentifiers(mods ...qm.QueryMod) accountIdentifierQuery {
	mods = append(mods, qm.From("\"account\".\"account_identifiers\""))
	q := NewQuery(mods...)
	if len(queries.GetSelect(q)) == 0 {
		queries.SetSelect(q, []string{"\"account\".\"account_identifiers\".*"})
	}

	return accountIdentifierQuery{q}
}

// FindAccountIdentifier retrieves a single record by ID with an executor.
// If selectCols is empty Find will return all columns.
func FindAccountIdentifier(ctx context.Context, exec boil.ContextExecutor, accountIdentifierID string, selectCols ...string) (*AccountIdentifier, error) {
	accountIdentifierObj := &AccountIdentifier{}

	sel := "*"
	if len(selectCols) > 0 {
		sel = strings.Join(strmangle.IdentQuoteSlice(dialect.LQ, dialect.RQ, selectCols), ",")
	}
	query := fmt.Sprintf(
		"select %s from \"account\".\"account_identifiers\" where \"account_identifier_id\"=$1", sel,
	)

	q := queries.Raw(query, accountIdentifierID)

	err := q.Bind(ctx, exec, accountIdentifierObj)
	if err != nil {
		if errors.Is(err, sql.ErrNoRows) {
			return nil, sql.ErrNoRows
		}
		return nil, errors.Wrap(err, "account: unable to select from account_identifiers")
	}

	if err = accountIdentifierObj.doAfterSelectHooks(ctx, exec); err != nil {
		return accountIdentifierObj, err
	}

	return accountIdentifierObj, nil
}

// Insert a single record using an executor.
// See boil.Columns.InsertColumnSet documentation to understand column list inference for inserts.
func (o *AccountIdentifier) Insert(ctx context.Context, exec boil.ContextExecutor, columns boil.Columns) error {
	if o == nil {
		return errors.New("account: no account_identifiers provided for insertion")
	}

	var err error

	if err := o.doBeforeInsertHooks(ctx, exec); err != nil {
		return err
	}

	nzDefaults := queries.NonZeroDefaultSet(accountIdentifierColumnsWithDefault, o)

	key := makeCacheKey(columns, nzDefaults)
	accountIdentifierInsertCacheMut.RLock()
	cache, cached := accountIdentifierInsertCache[key]
	accountIdentifierInsertCacheMut.RUnlock()

	if !cached {
		wl, returnColumns := columns.InsertColumnSet(
			accountIdentifierAllColumns,
			accountIdentifierColumnsWithDefault,
			accountIdentifierColumnsWithoutDefault,
			nzDefaults,
		)

		cache.valueMapping, err = queries.BindMapping(accountIdentifierType, accountIdentifierMapping, wl)
		if err != nil {
			return err
		}
		cache.retMapping, err = queries.BindMapping(accountIdentifierType, accountIdentifierMapping, returnColumns)
		if err != nil {
			return err
		}
		if len(wl) != 0 {
			cache.query = fmt.Sprintf("INSERT INTO \"account\".\"account_identifiers\" (\"%s\") %%sVALUES (%s)%%s", strings.Join(wl, "\",\""), strmangle.Placeholders(dialect.UseIndexPlaceholders, len(wl), 1, 1))
		} else {
			cache.query = "INSERT INTO \"account\".\"account_identifiers\" %sDEFAULT VALUES%s"
		}

		var queryOutput, queryReturning string

		if len(cache.retMapping) != 0 {
			queryReturning = fmt.Sprintf(" RETURNING \"%s\"", strings.Join(returnColumns, "\",\""))
		}

		cache.query = fmt.Sprintf(cache.query, queryOutput, queryReturning)
	}

	value := reflect.Indirect(reflect.ValueOf(o))
	vals := queries.ValuesFromMapping(value, cache.valueMapping)

	if boil.IsDebug(ctx) {
		writer := boil.DebugWriterFrom(ctx)
		fmt.Fprintln(writer, cache.query)
		fmt.Fprintln(writer, vals)
	}

	if len(cache.retMapping) != 0 {
		err = exec.QueryRowContext(ctx, cache.query, vals...).Scan(queries.PtrsFromMapping(value, cache.retMapping)...)
	} else {
		_, err = exec.ExecContext(ctx, cache.query, vals...)
	}

	if err != nil {
		return errors.Wrap(err, "account: unable to insert into account_identifiers")
	}

	if !cached {
		accountIdentifierInsertCacheMut.Lock()
		accountIdentifierInsertCache[key] = cache
		accountIdentifierInsertCacheMut.Unlock()
	}

	return o.doAfterInsertHooks(ctx, exec)
}

// Update uses an executor to update the AccountIdentifier.
// See boil.Columns.UpdateColumnSet documentation to understand column list inference for updates.
// Update does not automatically update the record in case of default values. Use .Reload() to refresh the records.
func (o *AccountIdentifier) Update(ctx context.Context, exec boil.ContextExecutor, columns boil.Columns) (int64, error) {
	var err error
	if err = o.doBeforeUpdateHooks(ctx, exec); err != nil {
		return 0, err
	}
	key := makeCacheKey(columns, nil)
	accountIdentifierUpdateCacheMut.RLock()
	cache, cached := accountIdentifierUpdateCache[key]
	accountIdentifierUpdateCacheMut.RUnlock()

	if !cached {
		wl := columns.UpdateColumnSet(
			accountIdentifierAllColumns,
			accountIdentifierPrimaryKeyColumns,
		)

		if !columns.IsWhitelist() {
			wl = strmangle.SetComplement(wl, []string{"created_at"})
		}
		if len(wl) == 0 {
			return 0, errors.New("account: unable to update account_identifiers, could not build whitelist")
		}

		cache.query = fmt.Sprintf("UPDATE \"account\".\"account_identifiers\" SET %s WHERE %s",
			strmangle.SetParamNames("\"", "\"", 1, wl),
			strmangle.WhereClause("\"", "\"", len(wl)+1, accountIdentifierPrimaryKeyColumns),
		)
		cache.valueMapping, err = queries.BindMapping(accountIdentifierType, accountIdentifierMapping, append(wl, accountIdentifierPrimaryKeyColumns...))
		if err != nil {
			return 0, err
		}
	}

	values := queries.ValuesFromMapping(reflect.Indirect(reflect.ValueOf(o)), cache.valueMapping)

	if boil.IsDebug(ctx) {
		writer := boil.DebugWriterFrom(ctx)
		fmt.Fprintln(writer, cache.query)
		fmt.Fprintln(writer, values)
	}
	var result sql.Result
	result, err = exec.ExecContext(ctx, cache.query, values...)
	if err != nil {
		return 0, errors.Wrap(err, "account: unable to update account_identifiers row")
	}

	rowsAff, err := result.RowsAffected()
	if err != nil {
		return 0, errors.Wrap(err, "account: failed to get rows affected by update for account_identifiers")
	}

	if !cached {
		accountIdentifierUpdateCacheMut.Lock()
		accountIdentifierUpdateCache[key] = cache
		accountIdentifierUpdateCacheMut.Unlock()
	}

	return rowsAff, o.doAfterUpdateHooks(ctx, exec)
}

// UpdateAll updates all rows with the specified column values.
func (q accountIdentifierQuery) UpdateAll(ctx context.Context, exec boil.ContextExecutor, cols M) (int64, error) {
	queries.SetUpdate(q.Query, cols)

	result, err := q.Query.ExecContext(ctx, exec)
	if err != nil {
		return 0, errors.Wrap(err, "account: unable to update all for account_identifiers")
	}

	rowsAff, err := result.RowsAffected()
	if err != nil {
		return 0, errors.Wrap(err, "account: unable to retrieve rows affected for account_identifiers")
	}

	return rowsAff, nil
}

// UpdateAll updates all rows with the specified column values, using an executor.
func (o AccountIdentifierSlice) UpdateAll(ctx context.Context, exec boil.ContextExecutor, cols M) (int64, error) {
	ln := int64(len(o))
	if ln == 0 {
		return 0, nil
	}

	if len(cols) == 0 {
		return 0, errors.New("account: update all requires at least one column argument")
	}

	colNames := make([]string, len(cols))
	args := make([]interface{}, len(cols))

	i := 0
	for name, value := range cols {
		colNames[i] = name
		args[i] = value
		i++
	}

	// Append all of the primary key values for each column
	for _, obj := range o {
		pkeyArgs := queries.ValuesFromMapping(reflect.Indirect(reflect.ValueOf(obj)), accountIdentifierPrimaryKeyMapping)
		args = append(args, pkeyArgs...)
	}

	sql := fmt.Sprintf("UPDATE \"account\".\"account_identifiers\" SET %s WHERE %s",
		strmangle.SetParamNames("\"", "\"", 1, colNames),
		strmangle.WhereClauseRepeated(string(dialect.LQ), string(dialect.RQ), len(colNames)+1, accountIdentifierPrimaryKeyColumns, len(o)))

	if boil.IsDebug(ctx) {
		writer := boil.DebugWriterFrom(ctx)
		fmt.Fprintln(writer, sql)
		fmt.Fprintln(writer, args...)
	}
	result, err := exec.ExecContext(ctx, sql, args...)
	if err != nil {
		return 0, errors.Wrap(err, "account: unable to update all in accountIdentifier slice")
	}

	rowsAff, err := result.RowsAffected()
	if err != nil {
		return 0, errors.Wrap(err, "account: unable to retrieve rows affected all in update all accountIdentifier")
	}
	return rowsAff, nil
}

// Upsert attempts an insert using an executor, and does an update or ignore on conflict.
// See boil.Columns documentation for how to properly use updateColumns and insertColumns.
func (o *AccountIdentifier) Upsert(ctx context.Context, exec boil.ContextExecutor, updateOnConflict bool, conflictColumns []string, updateColumns, insertColumns boil.Columns) error {
	if o == nil {
		return errors.New("account: no account_identifiers provided for upsert")
	}

	if err := o.doBeforeUpsertHooks(ctx, exec); err != nil {
		return err
	}

	nzDefaults := queries.NonZeroDefaultSet(accountIdentifierColumnsWithDefault, o)

	// Build cache key in-line uglily - mysql vs psql problems
	buf := strmangle.GetBuffer()
	if updateOnConflict {
		buf.WriteByte('t')
	} else {
		buf.WriteByte('f')
	}
	buf.WriteByte('.')
	for _, c := range conflictColumns {
		buf.WriteString(c)
	}
	buf.WriteByte('.')
	buf.WriteString(strconv.Itoa(updateColumns.Kind))
	for _, c := range updateColumns.Cols {
		buf.WriteString(c)
	}
	buf.WriteByte('.')
	buf.WriteString(strconv.Itoa(insertColumns.Kind))
	for _, c := range insertColumns.Cols {
		buf.WriteString(c)
	}
	buf.WriteByte('.')
	for _, c := range nzDefaults {
		buf.WriteString(c)
	}
	key := buf.String()
	strmangle.PutBuffer(buf)

	accountIdentifierUpsertCacheMut.RLock()
	cache, cached := accountIdentifierUpsertCache[key]
	accountIdentifierUpsertCacheMut.RUnlock()

	var err error

	if !cached {
		insert, ret := insertColumns.InsertColumnSet(
			accountIdentifierAllColumns,
			accountIdentifierColumnsWithDefault,
			accountIdentifierColumnsWithoutDefault,
			nzDefaults,
		)

		update := updateColumns.UpdateColumnSet(
			accountIdentifierAllColumns,
			accountIdentifierPrimaryKeyColumns,
		)

		if updateOnConflict && len(update) == 0 {
			return errors.New("account: unable to upsert account_identifiers, could not build update column list")
		}

		conflict := conflictColumns
		if len(conflict) == 0 {
			conflict = make([]string, len(accountIdentifierPrimaryKeyColumns))
			copy(conflict, accountIdentifierPrimaryKeyColumns)
		}
		cache.query = buildUpsertQueryPostgres(dialect, "\"account\".\"account_identifiers\"", updateOnConflict, ret, update, conflict, insert)

		cache.valueMapping, err = queries.BindMapping(accountIdentifierType, accountIdentifierMapping, insert)
		if err != nil {
			return err
		}
		if len(ret) != 0 {
			cache.retMapping, err = queries.BindMapping(accountIdentifierType, accountIdentifierMapping, ret)
			if err != nil {
				return err
			}
		}
	}

	value := reflect.Indirect(reflect.ValueOf(o))
	vals := queries.ValuesFromMapping(value, cache.valueMapping)
	var returns []interface{}
	if len(cache.retMapping) != 0 {
		returns = queries.PtrsFromMapping(value, cache.retMapping)
	}

	if boil.IsDebug(ctx) {
		writer := boil.DebugWriterFrom(ctx)
		fmt.Fprintln(writer, cache.query)
		fmt.Fprintln(writer, vals)
	}
	if len(cache.retMapping) != 0 {
		err = exec.QueryRowContext(ctx, cache.query, vals...).Scan(returns...)
		if errors.Is(err, sql.ErrNoRows) {
			err = nil // Postgres doesn't return anything when there's no update
		}
	} else {
		_, err = exec.ExecContext(ctx, cache.query, vals...)
	}
	if err != nil {
		return errors.Wrap(err, "account: unable to upsert account_identifiers")
	}

	if !cached {
		accountIdentifierUpsertCacheMut.Lock()
		accountIdentifierUpsertCache[key] = cache
		accountIdentifierUpsertCacheMut.Unlock()
	}

	return o.doAfterUpsertHooks(ctx, exec)
}

// Delete deletes a single AccountIdentifier record with an executor.
// Delete will match against the primary key column to find the record to delete.
func (o *AccountIdentifier) Delete(ctx context.Context, exec boil.ContextExecutor) (int64, error) {
	if o == nil {
		return 0, errors.New("account: no AccountIdentifier provided for delete")
	}

	if err := o.doBeforeDeleteHooks(ctx, exec); err != nil {
		return 0, err
	}

	args := queries.ValuesFromMapping(reflect.Indirect(reflect.ValueOf(o)), accountIdentifierPrimaryKeyMapping)
	sql := "DELETE FROM \"account\".\"account_identifiers\" WHERE \"account_identifier_id\"=$1"

	if boil.IsDebug(ctx) {
		writer := boil.DebugWriterFrom(ctx)
		fmt.Fprintln(writer, sql)
		fmt.Fprintln(writer, args...)
	}
	result, err := exec.ExecContext(ctx, sql, args...)
	if err != nil {
		return 0, errors.Wrap(err, "account: unable to delete from account_identifiers")
	}

	rowsAff, err := result.RowsAffected()
	if err != nil {
		return 0, errors.Wrap(err, "account: failed to get rows affected by delete for account_identifiers")
	}

	if err := o.doAfterDeleteHooks(ctx, exec); err != nil {
		return 0, err
	}

	return rowsAff, nil
}

// DeleteAll deletes all matching rows.
func (q accountIdentifierQuery) DeleteAll(ctx context.Context, exec boil.ContextExecutor) (int64, error) {
	if q.Query == nil {
		return 0, errors.New("account: no accountIdentifierQuery provided for delete all")
	}

	queries.SetDelete(q.Query)

	result, err := q.Query.ExecContext(ctx, exec)
	if err != nil {
		return 0, errors.Wrap(err, "account: unable to delete all from account_identifiers")
	}

	rowsAff, err := result.RowsAffected()
	if err != nil {
		return 0, errors.Wrap(err, "account: failed to get rows affected by deleteall for account_identifiers")
	}

	return rowsAff, nil
}

// DeleteAll deletes all rows in the slice, using an executor.
func (o AccountIdentifierSlice) DeleteAll(ctx context.Context, exec boil.ContextExecutor) (int64, error) {
	if len(o) == 0 {
		return 0, nil
	}

	if len(accountIdentifierBeforeDeleteHooks) != 0 {
		for _, obj := range o {
			if err := obj.doBeforeDeleteHooks(ctx, exec); err != nil {
				return 0, err
			}
		}
	}

	var args []interface{}
	for _, obj := range o {
		pkeyArgs := queries.ValuesFromMapping(reflect.Indirect(reflect.ValueOf(obj)), accountIdentifierPrimaryKeyMapping)
		args = append(args, pkeyArgs...)
	}

	sql := "DELETE FROM \"account\".\"account_identifiers\" WHERE " +
		strmangle.WhereClauseRepeated(string(dialect.LQ), string(dialect.RQ), 1, accountIdentifierPrimaryKeyColumns, len(o))

	if boil.IsDebug(ctx) {
		writer := boil.DebugWriterFrom(ctx)
		fmt.Fprintln(writer, sql)
		fmt.Fprintln(writer, args)
	}
	result, err := exec.ExecContext(ctx, sql, args...)
	if err != nil {
		return 0, errors.Wrap(err, "account: unable to delete all from accountIdentifier slice")
	}

	rowsAff, err := result.RowsAffected()
	if err != nil {
		return 0, errors.Wrap(err, "account: failed to get rows affected by deleteall for account_identifiers")
	}

	if len(accountIdentifierAfterDeleteHooks) != 0 {
		for _, obj := range o {
			if err := obj.doAfterDeleteHooks(ctx, exec); err != nil {
				return 0, err
			}
		}
	}

	return rowsAff, nil
}

// Reload refetches the object from the database
// using the primary keys with an executor.
func (o *AccountIdentifier) Reload(ctx context.Context, exec boil.ContextExecutor) error {
	ret, err := FindAccountIdentifier(ctx, exec, o.AccountIdentifierID)
	if err != nil {
		return err
	}

	*o = *ret
	return nil
}

// ReloadAll refetches every row with matching primary key column values
// and overwrites the original object slice with the newly updated slice.
func (o *AccountIdentifierSlice) ReloadAll(ctx context.Context, exec boil.ContextExecutor) error {
	if o == nil || len(*o) == 0 {
		return nil
	}

	slice := AccountIdentifierSlice{}
	var args []interface{}
	for _, obj := range *o {
		pkeyArgs := queries.ValuesFromMapping(reflect.Indirect(reflect.ValueOf(obj)), accountIdentifierPrimaryKeyMapping)
		args = append(args, pkeyArgs...)
	}

	sql := "SELECT \"account\".\"account_identifiers\".* FROM \"account\".\"account_identifiers\" WHERE " +
		strmangle.WhereClauseRepeated(string(dialect.LQ), string(dialect.RQ), 1, accountIdentifierPrimaryKeyColumns, len(*o))

	q := queries.Raw(sql, args...)

	err := q.Bind(ctx, exec, &slice)
	if err != nil {
		return errors.Wrap(err, "account: unable to reload all in AccountIdentifierSlice")
	}

	*o = slice

	return nil
}

// AccountIdentifierExists checks if the AccountIdentifier row exists.
func AccountIdentifierExists(ctx context.Context, exec boil.ContextExecutor, accountIdentifierID string) (bool, error) {
	var exists bool
	sql := "select exists(select 1 from \"account\".\"account_identifiers\" where \"account_identifier_id\"=$1 limit 1)"

	if boil.IsDebug(ctx) {
		writer := boil.DebugWriterFrom(ctx)
		fmt.Fprintln(writer, sql)
		fmt.Fprintln(writer, accountIdentifierID)
	}
	row := exec.QueryRowContext(ctx, sql, accountIdentifierID)

	err := row.Scan(&exists)
	if err != nil {
		return false, errors.Wrap(err, "account: unable to check if account_identifiers exists")
	}

	return exists, nil
}
