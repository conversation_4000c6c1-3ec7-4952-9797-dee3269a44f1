// Code generated by SQLBoiler (https://github.com/volatiletech/sqlboiler). DO NOT EDIT.
// This file is meant to be re-generated in place and/or deleted at any time.

package ds

import (
	"context"
	"database/sql"
	"fmt"
	"reflect"
	"strconv"
	"strings"
	"sync"
	"time"

	"github.com/friendsofgo/errors"
	"github.com/volatiletech/null/v8"
	"github.com/volatiletech/sqlboiler/v4/boil"
	"github.com/volatiletech/sqlboiler/v4/queries"
	"github.com/volatiletech/sqlboiler/v4/queries/qm"
	"github.com/volatiletech/sqlboiler/v4/queries/qmhelper"
	"github.com/volatiletech/sqlboiler/v4/types"
	"github.com/volatiletech/strmangle"
)

// ZONE_ENTROPY_MILEAGE_3MO_V1 is an object representing the database table.
type ZONE_ENTROPY_MILEAGE_3MO_V1 struct {
	END_DATE                      time.Time         `boil:"END_DATE" json:"END_DATE" toml:"END_DATE" yaml:"END_DATE"`
	START_DATE                    time.Time         `boil:"START_DATE" json:"START_DATE" toml:"START_DATE" yaml:"START_DATE"`
	UNIQUE_KEY                    null.String       `boil:"UNIQUE_KEY" json:"UNIQUE_KEY,omitempty" toml:"UNIQUE_KEY" yaml:"UNIQUE_KEY,omitempty"`
	UPDATED_AT                    null.Time         `boil:"UPDATED_AT" json:"UPDATED_AT,omitempty" toml:"UPDATED_AT" yaml:"UPDATED_AT,omitempty"`
	CONNECTION_ID                 string            `boil:"CONNECTION_ID" json:"CONNECTION_ID" toml:"CONNECTION_ID" yaml:"CONNECTION_ID"`
	DOMINANT_ZONE_PCT             types.Decimal     `boil:"DOMINANT_ZONE_PCT" json:"DOMINANT_ZONE_PCT" toml:"DOMINANT_ZONE_PCT" yaml:"DOMINANT_ZONE_PCT"`
	NUM_ZONES_OPERATED            null.Int64        `boil:"NUM_ZONES_OPERATED" json:"NUM_ZONES_OPERATED,omitempty" toml:"NUM_ZONES_OPERATED" yaml:"NUM_ZONES_OPERATED,omitempty"`
	ZONE_MILEAGE_ENTROPY          types.NullDecimal `boil:"ZONE_MILEAGE_ENTROPY" json:"ZONE_MILEAGE_ENTROPY,omitempty" toml:"ZONE_MILEAGE_ENTROPY" yaml:"ZONE_MILEAGE_ENTROPY,omitempty"`
	TOP3_ZONE_CONCENTRATION_TRAIN types.NullDecimal `boil:"TOP3_ZONE_CONCENTRATION_TRAIN" json:"TOP3_ZONE_CONCENTRATION_TRAIN,omitempty" toml:"TOP3_ZONE_CONCENTRATION_TRAIN" yaml:"TOP3_ZONE_CONCENTRATION_TRAIN,omitempty"`

	R *zONEENTROPY_MILEAGE_3MO_V1R `boil:"" json:"" toml:"" yaml:""`
	L zONEENTROPY_MILEAGE_3MO_V1L  `boil:"-" json:"-" toml:"-" yaml:"-"`
}

var ZONE_ENTROPY_MILEAGE_3MO_V1Columns = struct {
	END_DATE                      string
	START_DATE                    string
	UNIQUE_KEY                    string
	UPDATED_AT                    string
	CONNECTION_ID                 string
	DOMINANT_ZONE_PCT             string
	NUM_ZONES_OPERATED            string
	ZONE_MILEAGE_ENTROPY          string
	TOP3_ZONE_CONCENTRATION_TRAIN string
}{
	END_DATE:                      "END_DATE",
	START_DATE:                    "START_DATE",
	UNIQUE_KEY:                    "UNIQUE_KEY",
	UPDATED_AT:                    "UPDATED_AT",
	CONNECTION_ID:                 "CONNECTION_ID",
	DOMINANT_ZONE_PCT:             "DOMINANT_ZONE_PCT",
	NUM_ZONES_OPERATED:            "NUM_ZONES_OPERATED",
	ZONE_MILEAGE_ENTROPY:          "ZONE_MILEAGE_ENTROPY",
	TOP3_ZONE_CONCENTRATION_TRAIN: "TOP3_ZONE_CONCENTRATION_TRAIN",
}

var ZONE_ENTROPY_MILEAGE_3MO_V1TableColumns = struct {
	END_DATE                      string
	START_DATE                    string
	UNIQUE_KEY                    string
	UPDATED_AT                    string
	CONNECTION_ID                 string
	DOMINANT_ZONE_PCT             string
	NUM_ZONES_OPERATED            string
	ZONE_MILEAGE_ENTROPY          string
	TOP3_ZONE_CONCENTRATION_TRAIN string
}{
	END_DATE:                      "ZONE_ENTROPY_MILEAGE_3MO_V1.END_DATE",
	START_DATE:                    "ZONE_ENTROPY_MILEAGE_3MO_V1.START_DATE",
	UNIQUE_KEY:                    "ZONE_ENTROPY_MILEAGE_3MO_V1.UNIQUE_KEY",
	UPDATED_AT:                    "ZONE_ENTROPY_MILEAGE_3MO_V1.UPDATED_AT",
	CONNECTION_ID:                 "ZONE_ENTROPY_MILEAGE_3MO_V1.CONNECTION_ID",
	DOMINANT_ZONE_PCT:             "ZONE_ENTROPY_MILEAGE_3MO_V1.DOMINANT_ZONE_PCT",
	NUM_ZONES_OPERATED:            "ZONE_ENTROPY_MILEAGE_3MO_V1.NUM_ZONES_OPERATED",
	ZONE_MILEAGE_ENTROPY:          "ZONE_ENTROPY_MILEAGE_3MO_V1.ZONE_MILEAGE_ENTROPY",
	TOP3_ZONE_CONCENTRATION_TRAIN: "ZONE_ENTROPY_MILEAGE_3MO_V1.TOP3_ZONE_CONCENTRATION_TRAIN",
}

// Generated where

type whereHelpertypes_Decimal struct{ field string }

func (w whereHelpertypes_Decimal) EQ(x types.Decimal) qm.QueryMod {
	return qmhelper.Where(w.field, qmhelper.EQ, x)
}
func (w whereHelpertypes_Decimal) NEQ(x types.Decimal) qm.QueryMod {
	return qmhelper.Where(w.field, qmhelper.NEQ, x)
}
func (w whereHelpertypes_Decimal) LT(x types.Decimal) qm.QueryMod {
	return qmhelper.Where(w.field, qmhelper.LT, x)
}
func (w whereHelpertypes_Decimal) LTE(x types.Decimal) qm.QueryMod {
	return qmhelper.Where(w.field, qmhelper.LTE, x)
}
func (w whereHelpertypes_Decimal) GT(x types.Decimal) qm.QueryMod {
	return qmhelper.Where(w.field, qmhelper.GT, x)
}
func (w whereHelpertypes_Decimal) GTE(x types.Decimal) qm.QueryMod {
	return qmhelper.Where(w.field, qmhelper.GTE, x)
}

var ZONE_ENTROPY_MILEAGE_3MO_V1Where = struct {
	END_DATE                      whereHelpertime_Time
	START_DATE                    whereHelpertime_Time
	UNIQUE_KEY                    whereHelpernull_String
	UPDATED_AT                    whereHelpernull_Time
	CONNECTION_ID                 whereHelperstring
	DOMINANT_ZONE_PCT             whereHelpertypes_Decimal
	NUM_ZONES_OPERATED            whereHelpernull_Int64
	ZONE_MILEAGE_ENTROPY          whereHelpertypes_NullDecimal
	TOP3_ZONE_CONCENTRATION_TRAIN whereHelpertypes_NullDecimal
}{
	END_DATE:                      whereHelpertime_Time{field: "\"ZONE_ENTROPY_MILEAGE_3MO_V1\".\"END_DATE\""},
	START_DATE:                    whereHelpertime_Time{field: "\"ZONE_ENTROPY_MILEAGE_3MO_V1\".\"START_DATE\""},
	UNIQUE_KEY:                    whereHelpernull_String{field: "\"ZONE_ENTROPY_MILEAGE_3MO_V1\".\"UNIQUE_KEY\""},
	UPDATED_AT:                    whereHelpernull_Time{field: "\"ZONE_ENTROPY_MILEAGE_3MO_V1\".\"UPDATED_AT\""},
	CONNECTION_ID:                 whereHelperstring{field: "\"ZONE_ENTROPY_MILEAGE_3MO_V1\".\"CONNECTION_ID\""},
	DOMINANT_ZONE_PCT:             whereHelpertypes_Decimal{field: "\"ZONE_ENTROPY_MILEAGE_3MO_V1\".\"DOMINANT_ZONE_PCT\""},
	NUM_ZONES_OPERATED:            whereHelpernull_Int64{field: "\"ZONE_ENTROPY_MILEAGE_3MO_V1\".\"NUM_ZONES_OPERATED\""},
	ZONE_MILEAGE_ENTROPY:          whereHelpertypes_NullDecimal{field: "\"ZONE_ENTROPY_MILEAGE_3MO_V1\".\"ZONE_MILEAGE_ENTROPY\""},
	TOP3_ZONE_CONCENTRATION_TRAIN: whereHelpertypes_NullDecimal{field: "\"ZONE_ENTROPY_MILEAGE_3MO_V1\".\"TOP3_ZONE_CONCENTRATION_TRAIN\""},
}

// ZONE_ENTROPY_MILEAGE_3MO_V1Rels is where relationship names are stored.
var ZONE_ENTROPY_MILEAGE_3MO_V1Rels = struct {
}{}

// zONEENTROPY_MILEAGE_3MO_V1R is where relationships are stored.
type zONEENTROPY_MILEAGE_3MO_V1R struct {
}

// NewStruct creates a new relationship struct
func (*zONEENTROPY_MILEAGE_3MO_V1R) NewStruct() *zONEENTROPY_MILEAGE_3MO_V1R {
	return &zONEENTROPY_MILEAGE_3MO_V1R{}
}

// zONEENTROPY_MILEAGE_3MO_V1L is where Load methods for each relationship are stored.
type zONEENTROPY_MILEAGE_3MO_V1L struct{}

var (
	zONEENTROPY_MILEAGE_3MO_V1AllColumns            = []string{"END_DATE", "START_DATE", "UNIQUE_KEY", "UPDATED_AT", "CONNECTION_ID", "DOMINANT_ZONE_PCT", "NUM_ZONES_OPERATED", "ZONE_MILEAGE_ENTROPY", "TOP3_ZONE_CONCENTRATION_TRAIN"}
	zONEENTROPY_MILEAGE_3MO_V1ColumnsWithoutDefault = []string{"END_DATE", "START_DATE", "CONNECTION_ID", "DOMINANT_ZONE_PCT"}
	zONEENTROPY_MILEAGE_3MO_V1ColumnsWithDefault    = []string{"UNIQUE_KEY", "UPDATED_AT", "NUM_ZONES_OPERATED", "ZONE_MILEAGE_ENTROPY", "TOP3_ZONE_CONCENTRATION_TRAIN"}
	zONEENTROPY_MILEAGE_3MO_V1PrimaryKeyColumns     = []string{"CONNECTION_ID", "DOMINANT_ZONE_PCT", "START_DATE", "END_DATE"}
	zONEENTROPY_MILEAGE_3MO_V1GeneratedColumns      = []string{}
)

type (
	// ZONE_ENTROPY_MILEAGE_3MO_V1Slice is an alias for a slice of pointers to ZONE_ENTROPY_MILEAGE_3MO_V1.
	// This should almost always be used instead of []ZONE_ENTROPY_MILEAGE_3MO_V1.
	ZONE_ENTROPY_MILEAGE_3MO_V1Slice []*ZONE_ENTROPY_MILEAGE_3MO_V1
	// ZONE_ENTROPY_MILEAGE_3MO_V1Hook is the signature for custom ZONE_ENTROPY_MILEAGE_3MO_V1 hook methods
	ZONE_ENTROPY_MILEAGE_3MO_V1Hook func(context.Context, boil.ContextExecutor, *ZONE_ENTROPY_MILEAGE_3MO_V1) error

	zONEENTROPY_MILEAGE_3MO_V1Query struct {
		*queries.Query
	}
)

// Cache for insert, update and upsert
var (
	zONEENTROPY_MILEAGE_3MO_V1Type                 = reflect.TypeOf(&ZONE_ENTROPY_MILEAGE_3MO_V1{})
	zONEENTROPY_MILEAGE_3MO_V1Mapping              = queries.MakeStructMapping(zONEENTROPY_MILEAGE_3MO_V1Type)
	zONEENTROPY_MILEAGE_3MO_V1PrimaryKeyMapping, _ = queries.BindMapping(zONEENTROPY_MILEAGE_3MO_V1Type, zONEENTROPY_MILEAGE_3MO_V1Mapping, zONEENTROPY_MILEAGE_3MO_V1PrimaryKeyColumns)
	zONEENTROPY_MILEAGE_3MO_V1InsertCacheMut       sync.RWMutex
	zONEENTROPY_MILEAGE_3MO_V1InsertCache          = make(map[string]insertCache)
	zONEENTROPY_MILEAGE_3MO_V1UpdateCacheMut       sync.RWMutex
	zONEENTROPY_MILEAGE_3MO_V1UpdateCache          = make(map[string]updateCache)
	zONEENTROPY_MILEAGE_3MO_V1UpsertCacheMut       sync.RWMutex
	zONEENTROPY_MILEAGE_3MO_V1UpsertCache          = make(map[string]insertCache)
)

var (
	// Force time package dependency for automated UpdatedAt/CreatedAt.
	_ = time.Second
	// Force qmhelper dependency for where clause generation (which doesn't
	// always happen)
	_ = qmhelper.Where
)

var zONEENTROPY_MILEAGE_3MO_V1AfterSelectHooks []ZONE_ENTROPY_MILEAGE_3MO_V1Hook

var zONEENTROPY_MILEAGE_3MO_V1BeforeInsertHooks []ZONE_ENTROPY_MILEAGE_3MO_V1Hook
var zONEENTROPY_MILEAGE_3MO_V1AfterInsertHooks []ZONE_ENTROPY_MILEAGE_3MO_V1Hook

var zONEENTROPY_MILEAGE_3MO_V1BeforeUpdateHooks []ZONE_ENTROPY_MILEAGE_3MO_V1Hook
var zONEENTROPY_MILEAGE_3MO_V1AfterUpdateHooks []ZONE_ENTROPY_MILEAGE_3MO_V1Hook

var zONEENTROPY_MILEAGE_3MO_V1BeforeDeleteHooks []ZONE_ENTROPY_MILEAGE_3MO_V1Hook
var zONEENTROPY_MILEAGE_3MO_V1AfterDeleteHooks []ZONE_ENTROPY_MILEAGE_3MO_V1Hook

var zONEENTROPY_MILEAGE_3MO_V1BeforeUpsertHooks []ZONE_ENTROPY_MILEAGE_3MO_V1Hook
var zONEENTROPY_MILEAGE_3MO_V1AfterUpsertHooks []ZONE_ENTROPY_MILEAGE_3MO_V1Hook

// doAfterSelectHooks executes all "after Select" hooks.
func (o *ZONE_ENTROPY_MILEAGE_3MO_V1) doAfterSelectHooks(ctx context.Context, exec boil.ContextExecutor) (err error) {
	if boil.HooksAreSkipped(ctx) {
		return nil
	}

	for _, hook := range zONEENTROPY_MILEAGE_3MO_V1AfterSelectHooks {
		if err := hook(ctx, exec, o); err != nil {
			return err
		}
	}

	return nil
}

// doBeforeInsertHooks executes all "before insert" hooks.
func (o *ZONE_ENTROPY_MILEAGE_3MO_V1) doBeforeInsertHooks(ctx context.Context, exec boil.ContextExecutor) (err error) {
	if boil.HooksAreSkipped(ctx) {
		return nil
	}

	for _, hook := range zONEENTROPY_MILEAGE_3MO_V1BeforeInsertHooks {
		if err := hook(ctx, exec, o); err != nil {
			return err
		}
	}

	return nil
}

// doAfterInsertHooks executes all "after Insert" hooks.
func (o *ZONE_ENTROPY_MILEAGE_3MO_V1) doAfterInsertHooks(ctx context.Context, exec boil.ContextExecutor) (err error) {
	if boil.HooksAreSkipped(ctx) {
		return nil
	}

	for _, hook := range zONEENTROPY_MILEAGE_3MO_V1AfterInsertHooks {
		if err := hook(ctx, exec, o); err != nil {
			return err
		}
	}

	return nil
}

// doBeforeUpdateHooks executes all "before Update" hooks.
func (o *ZONE_ENTROPY_MILEAGE_3MO_V1) doBeforeUpdateHooks(ctx context.Context, exec boil.ContextExecutor) (err error) {
	if boil.HooksAreSkipped(ctx) {
		return nil
	}

	for _, hook := range zONEENTROPY_MILEAGE_3MO_V1BeforeUpdateHooks {
		if err := hook(ctx, exec, o); err != nil {
			return err
		}
	}

	return nil
}

// doAfterUpdateHooks executes all "after Update" hooks.
func (o *ZONE_ENTROPY_MILEAGE_3MO_V1) doAfterUpdateHooks(ctx context.Context, exec boil.ContextExecutor) (err error) {
	if boil.HooksAreSkipped(ctx) {
		return nil
	}

	for _, hook := range zONEENTROPY_MILEAGE_3MO_V1AfterUpdateHooks {
		if err := hook(ctx, exec, o); err != nil {
			return err
		}
	}

	return nil
}

// doBeforeDeleteHooks executes all "before Delete" hooks.
func (o *ZONE_ENTROPY_MILEAGE_3MO_V1) doBeforeDeleteHooks(ctx context.Context, exec boil.ContextExecutor) (err error) {
	if boil.HooksAreSkipped(ctx) {
		return nil
	}

	for _, hook := range zONEENTROPY_MILEAGE_3MO_V1BeforeDeleteHooks {
		if err := hook(ctx, exec, o); err != nil {
			return err
		}
	}

	return nil
}

// doAfterDeleteHooks executes all "after Delete" hooks.
func (o *ZONE_ENTROPY_MILEAGE_3MO_V1) doAfterDeleteHooks(ctx context.Context, exec boil.ContextExecutor) (err error) {
	if boil.HooksAreSkipped(ctx) {
		return nil
	}

	for _, hook := range zONEENTROPY_MILEAGE_3MO_V1AfterDeleteHooks {
		if err := hook(ctx, exec, o); err != nil {
			return err
		}
	}

	return nil
}

// doBeforeUpsertHooks executes all "before Upsert" hooks.
func (o *ZONE_ENTROPY_MILEAGE_3MO_V1) doBeforeUpsertHooks(ctx context.Context, exec boil.ContextExecutor) (err error) {
	if boil.HooksAreSkipped(ctx) {
		return nil
	}

	for _, hook := range zONEENTROPY_MILEAGE_3MO_V1BeforeUpsertHooks {
		if err := hook(ctx, exec, o); err != nil {
			return err
		}
	}

	return nil
}

// doAfterUpsertHooks executes all "after Upsert" hooks.
func (o *ZONE_ENTROPY_MILEAGE_3MO_V1) doAfterUpsertHooks(ctx context.Context, exec boil.ContextExecutor) (err error) {
	if boil.HooksAreSkipped(ctx) {
		return nil
	}

	for _, hook := range zONEENTROPY_MILEAGE_3MO_V1AfterUpsertHooks {
		if err := hook(ctx, exec, o); err != nil {
			return err
		}
	}

	return nil
}

// AddZONE_ENTROPY_MILEAGE_3MO_V1Hook registers your hook function for all future operations.
func AddZONE_ENTROPY_MILEAGE_3MO_V1Hook(hookPoint boil.HookPoint, zONEENTROPY_MILEAGE_3MO_V1Hook ZONE_ENTROPY_MILEAGE_3MO_V1Hook) {
	switch hookPoint {
	case boil.AfterSelectHook:
		zONEENTROPY_MILEAGE_3MO_V1AfterSelectHooks = append(zONEENTROPY_MILEAGE_3MO_V1AfterSelectHooks, zONEENTROPY_MILEAGE_3MO_V1Hook)
	case boil.BeforeInsertHook:
		zONEENTROPY_MILEAGE_3MO_V1BeforeInsertHooks = append(zONEENTROPY_MILEAGE_3MO_V1BeforeInsertHooks, zONEENTROPY_MILEAGE_3MO_V1Hook)
	case boil.AfterInsertHook:
		zONEENTROPY_MILEAGE_3MO_V1AfterInsertHooks = append(zONEENTROPY_MILEAGE_3MO_V1AfterInsertHooks, zONEENTROPY_MILEAGE_3MO_V1Hook)
	case boil.BeforeUpdateHook:
		zONEENTROPY_MILEAGE_3MO_V1BeforeUpdateHooks = append(zONEENTROPY_MILEAGE_3MO_V1BeforeUpdateHooks, zONEENTROPY_MILEAGE_3MO_V1Hook)
	case boil.AfterUpdateHook:
		zONEENTROPY_MILEAGE_3MO_V1AfterUpdateHooks = append(zONEENTROPY_MILEAGE_3MO_V1AfterUpdateHooks, zONEENTROPY_MILEAGE_3MO_V1Hook)
	case boil.BeforeDeleteHook:
		zONEENTROPY_MILEAGE_3MO_V1BeforeDeleteHooks = append(zONEENTROPY_MILEAGE_3MO_V1BeforeDeleteHooks, zONEENTROPY_MILEAGE_3MO_V1Hook)
	case boil.AfterDeleteHook:
		zONEENTROPY_MILEAGE_3MO_V1AfterDeleteHooks = append(zONEENTROPY_MILEAGE_3MO_V1AfterDeleteHooks, zONEENTROPY_MILEAGE_3MO_V1Hook)
	case boil.BeforeUpsertHook:
		zONEENTROPY_MILEAGE_3MO_V1BeforeUpsertHooks = append(zONEENTROPY_MILEAGE_3MO_V1BeforeUpsertHooks, zONEENTROPY_MILEAGE_3MO_V1Hook)
	case boil.AfterUpsertHook:
		zONEENTROPY_MILEAGE_3MO_V1AfterUpsertHooks = append(zONEENTROPY_MILEAGE_3MO_V1AfterUpsertHooks, zONEENTROPY_MILEAGE_3MO_V1Hook)
	}
}

// One returns a single zONEENTROPY_MILEAGE_3MO_V1 record from the query.
func (q zONEENTROPY_MILEAGE_3MO_V1Query) One(ctx context.Context, exec boil.ContextExecutor) (*ZONE_ENTROPY_MILEAGE_3MO_V1, error) {
	o := &ZONE_ENTROPY_MILEAGE_3MO_V1{}

	queries.SetLimit(q.Query, 1)

	err := q.Bind(ctx, exec, o)
	if err != nil {
		if errors.Is(err, sql.ErrNoRows) {
			return nil, sql.ErrNoRows
		}
		return nil, errors.Wrap(err, "ds: failed to execute a one query for ZONE_ENTROPY_MILEAGE_3MO_V1")
	}

	if err := o.doAfterSelectHooks(ctx, exec); err != nil {
		return o, err
	}

	return o, nil
}

// All returns all ZONE_ENTROPY_MILEAGE_3MO_V1 records from the query.
func (q zONEENTROPY_MILEAGE_3MO_V1Query) All(ctx context.Context, exec boil.ContextExecutor) (ZONE_ENTROPY_MILEAGE_3MO_V1Slice, error) {
	var o []*ZONE_ENTROPY_MILEAGE_3MO_V1

	err := q.Bind(ctx, exec, &o)
	if err != nil {
		return nil, errors.Wrap(err, "ds: failed to assign all query results to ZONE_ENTROPY_MILEAGE_3MO_V1 slice")
	}

	if len(zONEENTROPY_MILEAGE_3MO_V1AfterSelectHooks) != 0 {
		for _, obj := range o {
			if err := obj.doAfterSelectHooks(ctx, exec); err != nil {
				return o, err
			}
		}
	}

	return o, nil
}

// Count returns the count of all ZONE_ENTROPY_MILEAGE_3MO_V1 records in the query.
func (q zONEENTROPY_MILEAGE_3MO_V1Query) Count(ctx context.Context, exec boil.ContextExecutor) (int64, error) {
	var count int64

	queries.SetSelect(q.Query, nil)
	queries.SetCount(q.Query)

	err := q.Query.QueryRowContext(ctx, exec).Scan(&count)
	if err != nil {
		return 0, errors.Wrap(err, "ds: failed to count ZONE_ENTROPY_MILEAGE_3MO_V1 rows")
	}

	return count, nil
}

// Exists checks if the row exists in the table.
func (q zONEENTROPY_MILEAGE_3MO_V1Query) Exists(ctx context.Context, exec boil.ContextExecutor) (bool, error) {
	var count int64

	queries.SetSelect(q.Query, nil)
	queries.SetCount(q.Query)
	queries.SetLimit(q.Query, 1)

	err := q.Query.QueryRowContext(ctx, exec).Scan(&count)
	if err != nil {
		return false, errors.Wrap(err, "ds: failed to check if ZONE_ENTROPY_MILEAGE_3MO_V1 exists")
	}

	return count > 0, nil
}

// ZONEENTROPYMILEAGE3MOV1S retrieves all the records using an executor.
func ZONEENTROPYMILEAGE3MOV1S(mods ...qm.QueryMod) zONEENTROPY_MILEAGE_3MO_V1Query {
	mods = append(mods, qm.From("\"ZONE_ENTROPY_MILEAGE_3MO_V1\""))
	q := NewQuery(mods...)
	if len(queries.GetSelect(q)) == 0 {
		queries.SetSelect(q, []string{"\"ZONE_ENTROPY_MILEAGE_3MO_V1\".*"})
	}

	return zONEENTROPY_MILEAGE_3MO_V1Query{q}
}

// FindZONE_ENTROPY_MILEAGE_3MO_V1 retrieves a single record by ID with an executor.
// If selectCols is empty Find will return all columns.
func FindZONE_ENTROPY_MILEAGE_3MO_V1(ctx context.Context, exec boil.ContextExecutor, cONNECTIONID string, dOMINANTZONE_PCT types.Decimal, sTARTDATE time.Time, eNDDATE time.Time, selectCols ...string) (*ZONE_ENTROPY_MILEAGE_3MO_V1, error) {
	zONEENTROPY_MILEAGE_3MO_V1Obj := &ZONE_ENTROPY_MILEAGE_3MO_V1{}

	sel := "*"
	if len(selectCols) > 0 {
		sel = strings.Join(strmangle.IdentQuoteSlice(dialect.LQ, dialect.RQ, selectCols), ",")
	}
	query := fmt.Sprintf(
		"select %s from \"ZONE_ENTROPY_MILEAGE_3MO_V1\" where \"CONNECTION_ID\"=$1 AND \"DOMINANT_ZONE_PCT\"=$2 AND \"START_DATE\"=$3 AND \"END_DATE\"=$4", sel,
	)

	q := queries.Raw(query, cONNECTIONID, dOMINANTZONE_PCT, sTARTDATE, eNDDATE)

	err := q.Bind(ctx, exec, zONEENTROPY_MILEAGE_3MO_V1Obj)
	if err != nil {
		if errors.Is(err, sql.ErrNoRows) {
			return nil, sql.ErrNoRows
		}
		return nil, errors.Wrap(err, "ds: unable to select from ZONE_ENTROPY_MILEAGE_3MO_V1")
	}

	if err = zONEENTROPY_MILEAGE_3MO_V1Obj.doAfterSelectHooks(ctx, exec); err != nil {
		return zONEENTROPY_MILEAGE_3MO_V1Obj, err
	}

	return zONEENTROPY_MILEAGE_3MO_V1Obj, nil
}

// Insert a single record using an executor.
// See boil.Columns.InsertColumnSet documentation to understand column list inference for inserts.
func (o *ZONE_ENTROPY_MILEAGE_3MO_V1) Insert(ctx context.Context, exec boil.ContextExecutor, columns boil.Columns) error {
	if o == nil {
		return errors.New("ds: no ZONE_ENTROPY_MILEAGE_3MO_V1 provided for insertion")
	}

	var err error

	if err := o.doBeforeInsertHooks(ctx, exec); err != nil {
		return err
	}

	nzDefaults := queries.NonZeroDefaultSet(zONEENTROPY_MILEAGE_3MO_V1ColumnsWithDefault, o)

	key := makeCacheKey(columns, nzDefaults)
	zONEENTROPY_MILEAGE_3MO_V1InsertCacheMut.RLock()
	cache, cached := zONEENTROPY_MILEAGE_3MO_V1InsertCache[key]
	zONEENTROPY_MILEAGE_3MO_V1InsertCacheMut.RUnlock()

	if !cached {
		wl, returnColumns := columns.InsertColumnSet(
			zONEENTROPY_MILEAGE_3MO_V1AllColumns,
			zONEENTROPY_MILEAGE_3MO_V1ColumnsWithDefault,
			zONEENTROPY_MILEAGE_3MO_V1ColumnsWithoutDefault,
			nzDefaults,
		)

		cache.valueMapping, err = queries.BindMapping(zONEENTROPY_MILEAGE_3MO_V1Type, zONEENTROPY_MILEAGE_3MO_V1Mapping, wl)
		if err != nil {
			return err
		}
		cache.retMapping, err = queries.BindMapping(zONEENTROPY_MILEAGE_3MO_V1Type, zONEENTROPY_MILEAGE_3MO_V1Mapping, returnColumns)
		if err != nil {
			return err
		}
		if len(wl) != 0 {
			cache.query = fmt.Sprintf("INSERT INTO \"ZONE_ENTROPY_MILEAGE_3MO_V1\" (\"%s\") %%sVALUES (%s)%%s", strings.Join(wl, "\",\""), strmangle.Placeholders(dialect.UseIndexPlaceholders, len(wl), 1, 1))
		} else {
			cache.query = "INSERT INTO \"ZONE_ENTROPY_MILEAGE_3MO_V1\" %sDEFAULT VALUES%s"
		}

		var queryOutput, queryReturning string

		if len(cache.retMapping) != 0 {
			queryReturning = fmt.Sprintf(" RETURNING \"%s\"", strings.Join(returnColumns, "\",\""))
		}

		cache.query = fmt.Sprintf(cache.query, queryOutput, queryReturning)
	}

	value := reflect.Indirect(reflect.ValueOf(o))
	vals := queries.ValuesFromMapping(value, cache.valueMapping)

	if boil.IsDebug(ctx) {
		writer := boil.DebugWriterFrom(ctx)
		fmt.Fprintln(writer, cache.query)
		fmt.Fprintln(writer, vals)
	}

	if len(cache.retMapping) != 0 {
		err = exec.QueryRowContext(ctx, cache.query, vals...).Scan(queries.PtrsFromMapping(value, cache.retMapping)...)
	} else {
		_, err = exec.ExecContext(ctx, cache.query, vals...)
	}

	if err != nil {
		return errors.Wrap(err, "ds: unable to insert into ZONE_ENTROPY_MILEAGE_3MO_V1")
	}

	if !cached {
		zONEENTROPY_MILEAGE_3MO_V1InsertCacheMut.Lock()
		zONEENTROPY_MILEAGE_3MO_V1InsertCache[key] = cache
		zONEENTROPY_MILEAGE_3MO_V1InsertCacheMut.Unlock()
	}

	return o.doAfterInsertHooks(ctx, exec)
}

// Update uses an executor to update the ZONE_ENTROPY_MILEAGE_3MO_V1.
// See boil.Columns.UpdateColumnSet documentation to understand column list inference for updates.
// Update does not automatically update the record in case of default values. Use .Reload() to refresh the records.
func (o *ZONE_ENTROPY_MILEAGE_3MO_V1) Update(ctx context.Context, exec boil.ContextExecutor, columns boil.Columns) (int64, error) {
	var err error
	if err = o.doBeforeUpdateHooks(ctx, exec); err != nil {
		return 0, err
	}
	key := makeCacheKey(columns, nil)
	zONEENTROPY_MILEAGE_3MO_V1UpdateCacheMut.RLock()
	cache, cached := zONEENTROPY_MILEAGE_3MO_V1UpdateCache[key]
	zONEENTROPY_MILEAGE_3MO_V1UpdateCacheMut.RUnlock()

	if !cached {
		wl := columns.UpdateColumnSet(
			zONEENTROPY_MILEAGE_3MO_V1AllColumns,
			zONEENTROPY_MILEAGE_3MO_V1PrimaryKeyColumns,
		)

		if !columns.IsWhitelist() {
			wl = strmangle.SetComplement(wl, []string{"created_at"})
		}
		if len(wl) == 0 {
			return 0, errors.New("ds: unable to update ZONE_ENTROPY_MILEAGE_3MO_V1, could not build whitelist")
		}

		cache.query = fmt.Sprintf("UPDATE \"ZONE_ENTROPY_MILEAGE_3MO_V1\" SET %s WHERE %s",
			strmangle.SetParamNames("\"", "\"", 1, wl),
			strmangle.WhereClause("\"", "\"", len(wl)+1, zONEENTROPY_MILEAGE_3MO_V1PrimaryKeyColumns),
		)
		cache.valueMapping, err = queries.BindMapping(zONEENTROPY_MILEAGE_3MO_V1Type, zONEENTROPY_MILEAGE_3MO_V1Mapping, append(wl, zONEENTROPY_MILEAGE_3MO_V1PrimaryKeyColumns...))
		if err != nil {
			return 0, err
		}
	}

	values := queries.ValuesFromMapping(reflect.Indirect(reflect.ValueOf(o)), cache.valueMapping)

	if boil.IsDebug(ctx) {
		writer := boil.DebugWriterFrom(ctx)
		fmt.Fprintln(writer, cache.query)
		fmt.Fprintln(writer, values)
	}
	var result sql.Result
	result, err = exec.ExecContext(ctx, cache.query, values...)
	if err != nil {
		return 0, errors.Wrap(err, "ds: unable to update ZONE_ENTROPY_MILEAGE_3MO_V1 row")
	}

	rowsAff, err := result.RowsAffected()
	if err != nil {
		return 0, errors.Wrap(err, "ds: failed to get rows affected by update for ZONE_ENTROPY_MILEAGE_3MO_V1")
	}

	if !cached {
		zONEENTROPY_MILEAGE_3MO_V1UpdateCacheMut.Lock()
		zONEENTROPY_MILEAGE_3MO_V1UpdateCache[key] = cache
		zONEENTROPY_MILEAGE_3MO_V1UpdateCacheMut.Unlock()
	}

	return rowsAff, o.doAfterUpdateHooks(ctx, exec)
}

// UpdateAll updates all rows with the specified column values.
func (q zONEENTROPY_MILEAGE_3MO_V1Query) UpdateAll(ctx context.Context, exec boil.ContextExecutor, cols M) (int64, error) {
	queries.SetUpdate(q.Query, cols)

	result, err := q.Query.ExecContext(ctx, exec)
	if err != nil {
		return 0, errors.Wrap(err, "ds: unable to update all for ZONE_ENTROPY_MILEAGE_3MO_V1")
	}

	rowsAff, err := result.RowsAffected()
	if err != nil {
		return 0, errors.Wrap(err, "ds: unable to retrieve rows affected for ZONE_ENTROPY_MILEAGE_3MO_V1")
	}

	return rowsAff, nil
}

// UpdateAll updates all rows with the specified column values, using an executor.
func (o ZONE_ENTROPY_MILEAGE_3MO_V1Slice) UpdateAll(ctx context.Context, exec boil.ContextExecutor, cols M) (int64, error) {
	ln := int64(len(o))
	if ln == 0 {
		return 0, nil
	}

	if len(cols) == 0 {
		return 0, errors.New("ds: update all requires at least one column argument")
	}

	colNames := make([]string, len(cols))
	args := make([]interface{}, len(cols))

	i := 0
	for name, value := range cols {
		colNames[i] = name
		args[i] = value
		i++
	}

	// Append all of the primary key values for each column
	for _, obj := range o {
		pkeyArgs := queries.ValuesFromMapping(reflect.Indirect(reflect.ValueOf(obj)), zONEENTROPY_MILEAGE_3MO_V1PrimaryKeyMapping)
		args = append(args, pkeyArgs...)
	}

	sql := fmt.Sprintf("UPDATE \"ZONE_ENTROPY_MILEAGE_3MO_V1\" SET %s WHERE %s",
		strmangle.SetParamNames("\"", "\"", 1, colNames),
		strmangle.WhereClauseRepeated(string(dialect.LQ), string(dialect.RQ), len(colNames)+1, zONEENTROPY_MILEAGE_3MO_V1PrimaryKeyColumns, len(o)))

	if boil.IsDebug(ctx) {
		writer := boil.DebugWriterFrom(ctx)
		fmt.Fprintln(writer, sql)
		fmt.Fprintln(writer, args...)
	}
	result, err := exec.ExecContext(ctx, sql, args...)
	if err != nil {
		return 0, errors.Wrap(err, "ds: unable to update all in zONEENTROPY_MILEAGE_3MO_V1 slice")
	}

	rowsAff, err := result.RowsAffected()
	if err != nil {
		return 0, errors.Wrap(err, "ds: unable to retrieve rows affected all in update all zONEENTROPY_MILEAGE_3MO_V1")
	}
	return rowsAff, nil
}

// Upsert attempts an insert using an executor, and does an update or ignore on conflict.
// See boil.Columns documentation for how to properly use updateColumns and insertColumns.
func (o *ZONE_ENTROPY_MILEAGE_3MO_V1) Upsert(ctx context.Context, exec boil.ContextExecutor, updateOnConflict bool, conflictColumns []string, updateColumns, insertColumns boil.Columns) error {
	if o == nil {
		return errors.New("ds: no ZONE_ENTROPY_MILEAGE_3MO_V1 provided for upsert")
	}

	if err := o.doBeforeUpsertHooks(ctx, exec); err != nil {
		return err
	}

	nzDefaults := queries.NonZeroDefaultSet(zONEENTROPY_MILEAGE_3MO_V1ColumnsWithDefault, o)

	// Build cache key in-line uglily - mysql vs psql problems
	buf := strmangle.GetBuffer()
	if updateOnConflict {
		buf.WriteByte('t')
	} else {
		buf.WriteByte('f')
	}
	buf.WriteByte('.')
	for _, c := range conflictColumns {
		buf.WriteString(c)
	}
	buf.WriteByte('.')
	buf.WriteString(strconv.Itoa(updateColumns.Kind))
	for _, c := range updateColumns.Cols {
		buf.WriteString(c)
	}
	buf.WriteByte('.')
	buf.WriteString(strconv.Itoa(insertColumns.Kind))
	for _, c := range insertColumns.Cols {
		buf.WriteString(c)
	}
	buf.WriteByte('.')
	for _, c := range nzDefaults {
		buf.WriteString(c)
	}
	key := buf.String()
	strmangle.PutBuffer(buf)

	zONEENTROPY_MILEAGE_3MO_V1UpsertCacheMut.RLock()
	cache, cached := zONEENTROPY_MILEAGE_3MO_V1UpsertCache[key]
	zONEENTROPY_MILEAGE_3MO_V1UpsertCacheMut.RUnlock()

	var err error

	if !cached {
		insert, ret := insertColumns.InsertColumnSet(
			zONEENTROPY_MILEAGE_3MO_V1AllColumns,
			zONEENTROPY_MILEAGE_3MO_V1ColumnsWithDefault,
			zONEENTROPY_MILEAGE_3MO_V1ColumnsWithoutDefault,
			nzDefaults,
		)

		update := updateColumns.UpdateColumnSet(
			zONEENTROPY_MILEAGE_3MO_V1AllColumns,
			zONEENTROPY_MILEAGE_3MO_V1PrimaryKeyColumns,
		)

		if updateOnConflict && len(update) == 0 {
			return errors.New("ds: unable to upsert ZONE_ENTROPY_MILEAGE_3MO_V1, could not build update column list")
		}

		conflict := conflictColumns
		if len(conflict) == 0 {
			conflict = make([]string, len(zONEENTROPY_MILEAGE_3MO_V1PrimaryKeyColumns))
			copy(conflict, zONEENTROPY_MILEAGE_3MO_V1PrimaryKeyColumns)
		}
		cache.query = buildUpsertQueryPostgres(dialect, "\"ZONE_ENTROPY_MILEAGE_3MO_V1\"", updateOnConflict, ret, update, conflict, insert)

		cache.valueMapping, err = queries.BindMapping(zONEENTROPY_MILEAGE_3MO_V1Type, zONEENTROPY_MILEAGE_3MO_V1Mapping, insert)
		if err != nil {
			return err
		}
		if len(ret) != 0 {
			cache.retMapping, err = queries.BindMapping(zONEENTROPY_MILEAGE_3MO_V1Type, zONEENTROPY_MILEAGE_3MO_V1Mapping, ret)
			if err != nil {
				return err
			}
		}
	}

	value := reflect.Indirect(reflect.ValueOf(o))
	vals := queries.ValuesFromMapping(value, cache.valueMapping)
	var returns []interface{}
	if len(cache.retMapping) != 0 {
		returns = queries.PtrsFromMapping(value, cache.retMapping)
	}

	if boil.IsDebug(ctx) {
		writer := boil.DebugWriterFrom(ctx)
		fmt.Fprintln(writer, cache.query)
		fmt.Fprintln(writer, vals)
	}
	if len(cache.retMapping) != 0 {
		err = exec.QueryRowContext(ctx, cache.query, vals...).Scan(returns...)
		if errors.Is(err, sql.ErrNoRows) {
			err = nil // Postgres doesn't return anything when there's no update
		}
	} else {
		_, err = exec.ExecContext(ctx, cache.query, vals...)
	}
	if err != nil {
		return errors.Wrap(err, "ds: unable to upsert ZONE_ENTROPY_MILEAGE_3MO_V1")
	}

	if !cached {
		zONEENTROPY_MILEAGE_3MO_V1UpsertCacheMut.Lock()
		zONEENTROPY_MILEAGE_3MO_V1UpsertCache[key] = cache
		zONEENTROPY_MILEAGE_3MO_V1UpsertCacheMut.Unlock()
	}

	return o.doAfterUpsertHooks(ctx, exec)
}

// Delete deletes a single ZONE_ENTROPY_MILEAGE_3MO_V1 record with an executor.
// Delete will match against the primary key column to find the record to delete.
func (o *ZONE_ENTROPY_MILEAGE_3MO_V1) Delete(ctx context.Context, exec boil.ContextExecutor) (int64, error) {
	if o == nil {
		return 0, errors.New("ds: no ZONE_ENTROPY_MILEAGE_3MO_V1 provided for delete")
	}

	if err := o.doBeforeDeleteHooks(ctx, exec); err != nil {
		return 0, err
	}

	args := queries.ValuesFromMapping(reflect.Indirect(reflect.ValueOf(o)), zONEENTROPY_MILEAGE_3MO_V1PrimaryKeyMapping)
	sql := "DELETE FROM \"ZONE_ENTROPY_MILEAGE_3MO_V1\" WHERE \"CONNECTION_ID\"=$1 AND \"DOMINANT_ZONE_PCT\"=$2 AND \"START_DATE\"=$3 AND \"END_DATE\"=$4"

	if boil.IsDebug(ctx) {
		writer := boil.DebugWriterFrom(ctx)
		fmt.Fprintln(writer, sql)
		fmt.Fprintln(writer, args...)
	}
	result, err := exec.ExecContext(ctx, sql, args...)
	if err != nil {
		return 0, errors.Wrap(err, "ds: unable to delete from ZONE_ENTROPY_MILEAGE_3MO_V1")
	}

	rowsAff, err := result.RowsAffected()
	if err != nil {
		return 0, errors.Wrap(err, "ds: failed to get rows affected by delete for ZONE_ENTROPY_MILEAGE_3MO_V1")
	}

	if err := o.doAfterDeleteHooks(ctx, exec); err != nil {
		return 0, err
	}

	return rowsAff, nil
}

// DeleteAll deletes all matching rows.
func (q zONEENTROPY_MILEAGE_3MO_V1Query) DeleteAll(ctx context.Context, exec boil.ContextExecutor) (int64, error) {
	if q.Query == nil {
		return 0, errors.New("ds: no zONEENTROPY_MILEAGE_3MO_V1Query provided for delete all")
	}

	queries.SetDelete(q.Query)

	result, err := q.Query.ExecContext(ctx, exec)
	if err != nil {
		return 0, errors.Wrap(err, "ds: unable to delete all from ZONE_ENTROPY_MILEAGE_3MO_V1")
	}

	rowsAff, err := result.RowsAffected()
	if err != nil {
		return 0, errors.Wrap(err, "ds: failed to get rows affected by deleteall for ZONE_ENTROPY_MILEAGE_3MO_V1")
	}

	return rowsAff, nil
}

// DeleteAll deletes all rows in the slice, using an executor.
func (o ZONE_ENTROPY_MILEAGE_3MO_V1Slice) DeleteAll(ctx context.Context, exec boil.ContextExecutor) (int64, error) {
	if len(o) == 0 {
		return 0, nil
	}

	if len(zONEENTROPY_MILEAGE_3MO_V1BeforeDeleteHooks) != 0 {
		for _, obj := range o {
			if err := obj.doBeforeDeleteHooks(ctx, exec); err != nil {
				return 0, err
			}
		}
	}

	var args []interface{}
	for _, obj := range o {
		pkeyArgs := queries.ValuesFromMapping(reflect.Indirect(reflect.ValueOf(obj)), zONEENTROPY_MILEAGE_3MO_V1PrimaryKeyMapping)
		args = append(args, pkeyArgs...)
	}

	sql := "DELETE FROM \"ZONE_ENTROPY_MILEAGE_3MO_V1\" WHERE " +
		strmangle.WhereClauseRepeated(string(dialect.LQ), string(dialect.RQ), 1, zONEENTROPY_MILEAGE_3MO_V1PrimaryKeyColumns, len(o))

	if boil.IsDebug(ctx) {
		writer := boil.DebugWriterFrom(ctx)
		fmt.Fprintln(writer, sql)
		fmt.Fprintln(writer, args)
	}
	result, err := exec.ExecContext(ctx, sql, args...)
	if err != nil {
		return 0, errors.Wrap(err, "ds: unable to delete all from zONEENTROPY_MILEAGE_3MO_V1 slice")
	}

	rowsAff, err := result.RowsAffected()
	if err != nil {
		return 0, errors.Wrap(err, "ds: failed to get rows affected by deleteall for ZONE_ENTROPY_MILEAGE_3MO_V1")
	}

	if len(zONEENTROPY_MILEAGE_3MO_V1AfterDeleteHooks) != 0 {
		for _, obj := range o {
			if err := obj.doAfterDeleteHooks(ctx, exec); err != nil {
				return 0, err
			}
		}
	}

	return rowsAff, nil
}

// Reload refetches the object from the database
// using the primary keys with an executor.
func (o *ZONE_ENTROPY_MILEAGE_3MO_V1) Reload(ctx context.Context, exec boil.ContextExecutor) error {
	ret, err := FindZONE_ENTROPY_MILEAGE_3MO_V1(ctx, exec, o.CONNECTION_ID, o.DOMINANT_ZONE_PCT, o.START_DATE, o.END_DATE)
	if err != nil {
		return err
	}

	*o = *ret
	return nil
}

// ReloadAll refetches every row with matching primary key column values
// and overwrites the original object slice with the newly updated slice.
func (o *ZONE_ENTROPY_MILEAGE_3MO_V1Slice) ReloadAll(ctx context.Context, exec boil.ContextExecutor) error {
	if o == nil || len(*o) == 0 {
		return nil
	}

	slice := ZONE_ENTROPY_MILEAGE_3MO_V1Slice{}
	var args []interface{}
	for _, obj := range *o {
		pkeyArgs := queries.ValuesFromMapping(reflect.Indirect(reflect.ValueOf(obj)), zONEENTROPY_MILEAGE_3MO_V1PrimaryKeyMapping)
		args = append(args, pkeyArgs...)
	}

	sql := "SELECT \"ZONE_ENTROPY_MILEAGE_3MO_V1\".* FROM \"ZONE_ENTROPY_MILEAGE_3MO_V1\" WHERE " +
		strmangle.WhereClauseRepeated(string(dialect.LQ), string(dialect.RQ), 1, zONEENTROPY_MILEAGE_3MO_V1PrimaryKeyColumns, len(*o))

	q := queries.Raw(sql, args...)

	err := q.Bind(ctx, exec, &slice)
	if err != nil {
		return errors.Wrap(err, "ds: unable to reload all in ZONE_ENTROPY_MILEAGE_3MO_V1Slice")
	}

	*o = slice

	return nil
}

// ZONE_ENTROPY_MILEAGE_3MO_V1Exists checks if the ZONE_ENTROPY_MILEAGE_3MO_V1 row exists.
func ZONE_ENTROPY_MILEAGE_3MO_V1Exists(ctx context.Context, exec boil.ContextExecutor, cONNECTIONID string, dOMINANTZONE_PCT types.Decimal, sTARTDATE time.Time, eNDDATE time.Time) (bool, error) {
	var exists bool
	sql := "select exists(select 1 from \"ZONE_ENTROPY_MILEAGE_3MO_V1\" where \"CONNECTION_ID\"=$1 AND \"DOMINANT_ZONE_PCT\"=$2 AND \"START_DATE\"=$3 AND \"END_DATE\"=$4 limit 1)"

	if boil.IsDebug(ctx) {
		writer := boil.DebugWriterFrom(ctx)
		fmt.Fprintln(writer, sql)
		fmt.Fprintln(writer, cONNECTIONID, dOMINANTZONE_PCT, sTARTDATE, eNDDATE)
	}
	row := exec.QueryRowContext(ctx, sql, cONNECTIONID, dOMINANTZONE_PCT, sTARTDATE, eNDDATE)

	err := row.Scan(&exists)
	if err != nil {
		return false, errors.Wrap(err, "ds: unable to check if ZONE_ENTROPY_MILEAGE_3MO_V1 exists")
	}

	return exists, nil
}
