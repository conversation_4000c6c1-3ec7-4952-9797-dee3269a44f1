// Code generated by SQLBoiler (https://github.com/volatiletech/sqlboiler). DO NOT EDIT.
// This file is meant to be re-generated in place and/or deleted at any time.

package safer_watch

import (
	"context"
	"database/sql"
	"fmt"
	"reflect"
	"strconv"
	"strings"
	"sync"
	"time"

	"github.com/friendsofgo/errors"
	"github.com/volatiletech/null/v8"
	"github.com/volatiletech/sqlboiler/v4/boil"
	"github.com/volatiletech/sqlboiler/v4/queries"
	"github.com/volatiletech/sqlboiler/v4/queries/qm"
	"github.com/volatiletech/sqlboiler/v4/queries/qmhelper"
	"github.com/volatiletech/strmangle"
)

// Registry is an object representing the database table.
type Registry struct {
	DotNumber    int         `boil:"dot_number" json:"dot_number" toml:"dot_number" yaml:"dot_number"`
	PulledAt     time.Time   `boil:"pulled_at" json:"pulled_at" toml:"pulled_at" yaml:"pulled_at"`
	Status       string      `boil:"status" json:"status" toml:"status" yaml:"status"`
	ErrorMessage null.String `boil:"error_message" json:"error_message,omitempty" toml:"error_message" yaml:"error_message,omitempty"`

	R *registryR `boil:"" json:"" toml:"" yaml:""`
	L registryL  `boil:"-" json:"-" toml:"-" yaml:"-"`
}

var RegistryColumns = struct {
	DotNumber    string
	PulledAt     string
	Status       string
	ErrorMessage string
}{
	DotNumber:    "dot_number",
	PulledAt:     "pulled_at",
	Status:       "status",
	ErrorMessage: "error_message",
}

var RegistryTableColumns = struct {
	DotNumber    string
	PulledAt     string
	Status       string
	ErrorMessage string
}{
	DotNumber:    "registry.dot_number",
	PulledAt:     "registry.pulled_at",
	Status:       "registry.status",
	ErrorMessage: "registry.error_message",
}

// Generated where

type whereHelpertime_Time struct{ field string }

func (w whereHelpertime_Time) EQ(x time.Time) qm.QueryMod {
	return qmhelper.Where(w.field, qmhelper.EQ, x)
}
func (w whereHelpertime_Time) NEQ(x time.Time) qm.QueryMod {
	return qmhelper.Where(w.field, qmhelper.NEQ, x)
}
func (w whereHelpertime_Time) LT(x time.Time) qm.QueryMod {
	return qmhelper.Where(w.field, qmhelper.LT, x)
}
func (w whereHelpertime_Time) LTE(x time.Time) qm.QueryMod {
	return qmhelper.Where(w.field, qmhelper.LTE, x)
}
func (w whereHelpertime_Time) GT(x time.Time) qm.QueryMod {
	return qmhelper.Where(w.field, qmhelper.GT, x)
}
func (w whereHelpertime_Time) GTE(x time.Time) qm.QueryMod {
	return qmhelper.Where(w.field, qmhelper.GTE, x)
}

type whereHelperstring struct{ field string }

func (w whereHelperstring) EQ(x string) qm.QueryMod  { return qmhelper.Where(w.field, qmhelper.EQ, x) }
func (w whereHelperstring) NEQ(x string) qm.QueryMod { return qmhelper.Where(w.field, qmhelper.NEQ, x) }
func (w whereHelperstring) LT(x string) qm.QueryMod  { return qmhelper.Where(w.field, qmhelper.LT, x) }
func (w whereHelperstring) LTE(x string) qm.QueryMod { return qmhelper.Where(w.field, qmhelper.LTE, x) }
func (w whereHelperstring) GT(x string) qm.QueryMod  { return qmhelper.Where(w.field, qmhelper.GT, x) }
func (w whereHelperstring) GTE(x string) qm.QueryMod { return qmhelper.Where(w.field, qmhelper.GTE, x) }
func (w whereHelperstring) IN(slice []string) qm.QueryMod {
	values := make([]interface{}, 0, len(slice))
	for _, value := range slice {
		values = append(values, value)
	}
	return qm.WhereIn(fmt.Sprintf("%s IN ?", w.field), values...)
}
func (w whereHelperstring) NIN(slice []string) qm.QueryMod {
	values := make([]interface{}, 0, len(slice))
	for _, value := range slice {
		values = append(values, value)
	}
	return qm.WhereNotIn(fmt.Sprintf("%s NOT IN ?", w.field), values...)
}

type whereHelpernull_String struct{ field string }

func (w whereHelpernull_String) EQ(x null.String) qm.QueryMod {
	return qmhelper.WhereNullEQ(w.field, false, x)
}
func (w whereHelpernull_String) NEQ(x null.String) qm.QueryMod {
	return qmhelper.WhereNullEQ(w.field, true, x)
}
func (w whereHelpernull_String) LT(x null.String) qm.QueryMod {
	return qmhelper.Where(w.field, qmhelper.LT, x)
}
func (w whereHelpernull_String) LTE(x null.String) qm.QueryMod {
	return qmhelper.Where(w.field, qmhelper.LTE, x)
}
func (w whereHelpernull_String) GT(x null.String) qm.QueryMod {
	return qmhelper.Where(w.field, qmhelper.GT, x)
}
func (w whereHelpernull_String) GTE(x null.String) qm.QueryMod {
	return qmhelper.Where(w.field, qmhelper.GTE, x)
}

func (w whereHelpernull_String) IsNull() qm.QueryMod    { return qmhelper.WhereIsNull(w.field) }
func (w whereHelpernull_String) IsNotNull() qm.QueryMod { return qmhelper.WhereIsNotNull(w.field) }

var RegistryWhere = struct {
	DotNumber    whereHelperint
	PulledAt     whereHelpertime_Time
	Status       whereHelperstring
	ErrorMessage whereHelpernull_String
}{
	DotNumber:    whereHelperint{field: "\"safer_watch\".\"registry\".\"dot_number\""},
	PulledAt:     whereHelpertime_Time{field: "\"safer_watch\".\"registry\".\"pulled_at\""},
	Status:       whereHelperstring{field: "\"safer_watch\".\"registry\".\"status\""},
	ErrorMessage: whereHelpernull_String{field: "\"safer_watch\".\"registry\".\"error_message\""},
}

// RegistryRels is where relationship names are stored.
var RegistryRels = struct {
	DotNumberReports string
}{
	DotNumberReports: "DotNumberReports",
}

// registryR is where relationships are stored.
type registryR struct {
	DotNumberReports ReportSlice `boil:"DotNumberReports" json:"DotNumberReports" toml:"DotNumberReports" yaml:"DotNumberReports"`
}

// NewStruct creates a new relationship struct
func (*registryR) NewStruct() *registryR {
	return &registryR{}
}

// registryL is where Load methods for each relationship are stored.
type registryL struct{}

var (
	registryAllColumns            = []string{"dot_number", "pulled_at", "status", "error_message"}
	registryColumnsWithoutDefault = []string{"dot_number", "pulled_at"}
	registryColumnsWithDefault    = []string{"status", "error_message"}
	registryPrimaryKeyColumns     = []string{"dot_number"}
	registryGeneratedColumns      = []string{}
)

type (
	// RegistrySlice is an alias for a slice of pointers to Registry.
	// This should almost always be used instead of []Registry.
	RegistrySlice []*Registry
	// RegistryHook is the signature for custom Registry hook methods
	RegistryHook func(context.Context, boil.ContextExecutor, *Registry) error

	registryQuery struct {
		*queries.Query
	}
)

// Cache for insert, update and upsert
var (
	registryType                 = reflect.TypeOf(&Registry{})
	registryMapping              = queries.MakeStructMapping(registryType)
	registryPrimaryKeyMapping, _ = queries.BindMapping(registryType, registryMapping, registryPrimaryKeyColumns)
	registryInsertCacheMut       sync.RWMutex
	registryInsertCache          = make(map[string]insertCache)
	registryUpdateCacheMut       sync.RWMutex
	registryUpdateCache          = make(map[string]updateCache)
	registryUpsertCacheMut       sync.RWMutex
	registryUpsertCache          = make(map[string]insertCache)
)

var (
	// Force time package dependency for automated UpdatedAt/CreatedAt.
	_ = time.Second
	// Force qmhelper dependency for where clause generation (which doesn't
	// always happen)
	_ = qmhelper.Where
)

var registryAfterSelectHooks []RegistryHook

var registryBeforeInsertHooks []RegistryHook
var registryAfterInsertHooks []RegistryHook

var registryBeforeUpdateHooks []RegistryHook
var registryAfterUpdateHooks []RegistryHook

var registryBeforeDeleteHooks []RegistryHook
var registryAfterDeleteHooks []RegistryHook

var registryBeforeUpsertHooks []RegistryHook
var registryAfterUpsertHooks []RegistryHook

// doAfterSelectHooks executes all "after Select" hooks.
func (o *Registry) doAfterSelectHooks(ctx context.Context, exec boil.ContextExecutor) (err error) {
	if boil.HooksAreSkipped(ctx) {
		return nil
	}

	for _, hook := range registryAfterSelectHooks {
		if err := hook(ctx, exec, o); err != nil {
			return err
		}
	}

	return nil
}

// doBeforeInsertHooks executes all "before insert" hooks.
func (o *Registry) doBeforeInsertHooks(ctx context.Context, exec boil.ContextExecutor) (err error) {
	if boil.HooksAreSkipped(ctx) {
		return nil
	}

	for _, hook := range registryBeforeInsertHooks {
		if err := hook(ctx, exec, o); err != nil {
			return err
		}
	}

	return nil
}

// doAfterInsertHooks executes all "after Insert" hooks.
func (o *Registry) doAfterInsertHooks(ctx context.Context, exec boil.ContextExecutor) (err error) {
	if boil.HooksAreSkipped(ctx) {
		return nil
	}

	for _, hook := range registryAfterInsertHooks {
		if err := hook(ctx, exec, o); err != nil {
			return err
		}
	}

	return nil
}

// doBeforeUpdateHooks executes all "before Update" hooks.
func (o *Registry) doBeforeUpdateHooks(ctx context.Context, exec boil.ContextExecutor) (err error) {
	if boil.HooksAreSkipped(ctx) {
		return nil
	}

	for _, hook := range registryBeforeUpdateHooks {
		if err := hook(ctx, exec, o); err != nil {
			return err
		}
	}

	return nil
}

// doAfterUpdateHooks executes all "after Update" hooks.
func (o *Registry) doAfterUpdateHooks(ctx context.Context, exec boil.ContextExecutor) (err error) {
	if boil.HooksAreSkipped(ctx) {
		return nil
	}

	for _, hook := range registryAfterUpdateHooks {
		if err := hook(ctx, exec, o); err != nil {
			return err
		}
	}

	return nil
}

// doBeforeDeleteHooks executes all "before Delete" hooks.
func (o *Registry) doBeforeDeleteHooks(ctx context.Context, exec boil.ContextExecutor) (err error) {
	if boil.HooksAreSkipped(ctx) {
		return nil
	}

	for _, hook := range registryBeforeDeleteHooks {
		if err := hook(ctx, exec, o); err != nil {
			return err
		}
	}

	return nil
}

// doAfterDeleteHooks executes all "after Delete" hooks.
func (o *Registry) doAfterDeleteHooks(ctx context.Context, exec boil.ContextExecutor) (err error) {
	if boil.HooksAreSkipped(ctx) {
		return nil
	}

	for _, hook := range registryAfterDeleteHooks {
		if err := hook(ctx, exec, o); err != nil {
			return err
		}
	}

	return nil
}

// doBeforeUpsertHooks executes all "before Upsert" hooks.
func (o *Registry) doBeforeUpsertHooks(ctx context.Context, exec boil.ContextExecutor) (err error) {
	if boil.HooksAreSkipped(ctx) {
		return nil
	}

	for _, hook := range registryBeforeUpsertHooks {
		if err := hook(ctx, exec, o); err != nil {
			return err
		}
	}

	return nil
}

// doAfterUpsertHooks executes all "after Upsert" hooks.
func (o *Registry) doAfterUpsertHooks(ctx context.Context, exec boil.ContextExecutor) (err error) {
	if boil.HooksAreSkipped(ctx) {
		return nil
	}

	for _, hook := range registryAfterUpsertHooks {
		if err := hook(ctx, exec, o); err != nil {
			return err
		}
	}

	return nil
}

// AddRegistryHook registers your hook function for all future operations.
func AddRegistryHook(hookPoint boil.HookPoint, registryHook RegistryHook) {
	switch hookPoint {
	case boil.AfterSelectHook:
		registryAfterSelectHooks = append(registryAfterSelectHooks, registryHook)
	case boil.BeforeInsertHook:
		registryBeforeInsertHooks = append(registryBeforeInsertHooks, registryHook)
	case boil.AfterInsertHook:
		registryAfterInsertHooks = append(registryAfterInsertHooks, registryHook)
	case boil.BeforeUpdateHook:
		registryBeforeUpdateHooks = append(registryBeforeUpdateHooks, registryHook)
	case boil.AfterUpdateHook:
		registryAfterUpdateHooks = append(registryAfterUpdateHooks, registryHook)
	case boil.BeforeDeleteHook:
		registryBeforeDeleteHooks = append(registryBeforeDeleteHooks, registryHook)
	case boil.AfterDeleteHook:
		registryAfterDeleteHooks = append(registryAfterDeleteHooks, registryHook)
	case boil.BeforeUpsertHook:
		registryBeforeUpsertHooks = append(registryBeforeUpsertHooks, registryHook)
	case boil.AfterUpsertHook:
		registryAfterUpsertHooks = append(registryAfterUpsertHooks, registryHook)
	}
}

// One returns a single registry record from the query.
func (q registryQuery) One(ctx context.Context, exec boil.ContextExecutor) (*Registry, error) {
	o := &Registry{}

	queries.SetLimit(q.Query, 1)

	err := q.Bind(ctx, exec, o)
	if err != nil {
		if errors.Is(err, sql.ErrNoRows) {
			return nil, sql.ErrNoRows
		}
		return nil, errors.Wrap(err, "safer_watch: failed to execute a one query for registry")
	}

	if err := o.doAfterSelectHooks(ctx, exec); err != nil {
		return o, err
	}

	return o, nil
}

// All returns all Registry records from the query.
func (q registryQuery) All(ctx context.Context, exec boil.ContextExecutor) (RegistrySlice, error) {
	var o []*Registry

	err := q.Bind(ctx, exec, &o)
	if err != nil {
		return nil, errors.Wrap(err, "safer_watch: failed to assign all query results to Registry slice")
	}

	if len(registryAfterSelectHooks) != 0 {
		for _, obj := range o {
			if err := obj.doAfterSelectHooks(ctx, exec); err != nil {
				return o, err
			}
		}
	}

	return o, nil
}

// Count returns the count of all Registry records in the query.
func (q registryQuery) Count(ctx context.Context, exec boil.ContextExecutor) (int64, error) {
	var count int64

	queries.SetSelect(q.Query, nil)
	queries.SetCount(q.Query)

	err := q.Query.QueryRowContext(ctx, exec).Scan(&count)
	if err != nil {
		return 0, errors.Wrap(err, "safer_watch: failed to count registry rows")
	}

	return count, nil
}

// Exists checks if the row exists in the table.
func (q registryQuery) Exists(ctx context.Context, exec boil.ContextExecutor) (bool, error) {
	var count int64

	queries.SetSelect(q.Query, nil)
	queries.SetCount(q.Query)
	queries.SetLimit(q.Query, 1)

	err := q.Query.QueryRowContext(ctx, exec).Scan(&count)
	if err != nil {
		return false, errors.Wrap(err, "safer_watch: failed to check if registry exists")
	}

	return count > 0, nil
}

// DotNumberReports retrieves all the report's Reports with an executor via dot_number column.
func (o *Registry) DotNumberReports(mods ...qm.QueryMod) reportQuery {
	var queryMods []qm.QueryMod
	if len(mods) != 0 {
		queryMods = append(queryMods, mods...)
	}

	queryMods = append(queryMods,
		qm.Where("\"safer_watch\".\"report\".\"dot_number\"=?", o.DotNumber),
	)

	return Reports(queryMods...)
}

// LoadDotNumberReports allows an eager lookup of values, cached into the
// loaded structs of the objects. This is for a 1-M or N-M relationship.
func (registryL) LoadDotNumberReports(ctx context.Context, e boil.ContextExecutor, singular bool, maybeRegistry interface{}, mods queries.Applicator) error {
	var slice []*Registry
	var object *Registry

	if singular {
		object = maybeRegistry.(*Registry)
	} else {
		slice = *maybeRegistry.(*[]*Registry)
	}

	args := make([]interface{}, 0, 1)
	if singular {
		if object.R == nil {
			object.R = &registryR{}
		}
		args = append(args, object.DotNumber)
	} else {
	Outer:
		for _, obj := range slice {
			if obj.R == nil {
				obj.R = &registryR{}
			}

			for _, a := range args {
				if a == obj.DotNumber {
					continue Outer
				}
			}

			args = append(args, obj.DotNumber)
		}
	}

	if len(args) == 0 {
		return nil
	}

	query := NewQuery(
		qm.From(`safer_watch.report`),
		qm.WhereIn(`safer_watch.report.dot_number in ?`, args...),
	)
	if mods != nil {
		mods.Apply(query)
	}

	results, err := query.QueryContext(ctx, e)
	if err != nil {
		return errors.Wrap(err, "failed to eager load report")
	}

	var resultSlice []*Report
	if err = queries.Bind(results, &resultSlice); err != nil {
		return errors.Wrap(err, "failed to bind eager loaded slice report")
	}

	if err = results.Close(); err != nil {
		return errors.Wrap(err, "failed to close results in eager load on report")
	}
	if err = results.Err(); err != nil {
		return errors.Wrap(err, "error occurred during iteration of eager loaded relations for report")
	}

	if len(reportAfterSelectHooks) != 0 {
		for _, obj := range resultSlice {
			if err := obj.doAfterSelectHooks(ctx, e); err != nil {
				return err
			}
		}
	}
	if singular {
		object.R.DotNumberReports = resultSlice
		for _, foreign := range resultSlice {
			if foreign.R == nil {
				foreign.R = &reportR{}
			}
			foreign.R.DotNumberRegistry = object
		}
		return nil
	}

	for _, foreign := range resultSlice {
		for _, local := range slice {
			if local.DotNumber == foreign.DotNumber {
				local.R.DotNumberReports = append(local.R.DotNumberReports, foreign)
				if foreign.R == nil {
					foreign.R = &reportR{}
				}
				foreign.R.DotNumberRegistry = local
				break
			}
		}
	}

	return nil
}

// AddDotNumberReports adds the given related objects to the existing relationships
// of the registry, optionally inserting them as new records.
// Appends related to o.R.DotNumberReports.
// Sets related.R.DotNumberRegistry appropriately.
func (o *Registry) AddDotNumberReports(ctx context.Context, exec boil.ContextExecutor, insert bool, related ...*Report) error {
	var err error
	for _, rel := range related {
		if insert {
			rel.DotNumber = o.DotNumber
			if err = rel.Insert(ctx, exec, boil.Infer()); err != nil {
				return errors.Wrap(err, "failed to insert into foreign table")
			}
		} else {
			updateQuery := fmt.Sprintf(
				"UPDATE \"safer_watch\".\"report\" SET %s WHERE %s",
				strmangle.SetParamNames("\"", "\"", 1, []string{"dot_number"}),
				strmangle.WhereClause("\"", "\"", 2, reportPrimaryKeyColumns),
			)
			values := []interface{}{o.DotNumber, rel.ID}

			if boil.IsDebug(ctx) {
				writer := boil.DebugWriterFrom(ctx)
				fmt.Fprintln(writer, updateQuery)
				fmt.Fprintln(writer, values)
			}
			if _, err = exec.ExecContext(ctx, updateQuery, values...); err != nil {
				return errors.Wrap(err, "failed to update foreign table")
			}

			rel.DotNumber = o.DotNumber
		}
	}

	if o.R == nil {
		o.R = &registryR{
			DotNumberReports: related,
		}
	} else {
		o.R.DotNumberReports = append(o.R.DotNumberReports, related...)
	}

	for _, rel := range related {
		if rel.R == nil {
			rel.R = &reportR{
				DotNumberRegistry: o,
			}
		} else {
			rel.R.DotNumberRegistry = o
		}
	}
	return nil
}

// Registries retrieves all the records using an executor.
func Registries(mods ...qm.QueryMod) registryQuery {
	mods = append(mods, qm.From("\"safer_watch\".\"registry\""))
	q := NewQuery(mods...)
	if len(queries.GetSelect(q)) == 0 {
		queries.SetSelect(q, []string{"\"safer_watch\".\"registry\".*"})
	}

	return registryQuery{q}
}

// FindRegistry retrieves a single record by ID with an executor.
// If selectCols is empty Find will return all columns.
func FindRegistry(ctx context.Context, exec boil.ContextExecutor, dotNumber int, selectCols ...string) (*Registry, error) {
	registryObj := &Registry{}

	sel := "*"
	if len(selectCols) > 0 {
		sel = strings.Join(strmangle.IdentQuoteSlice(dialect.LQ, dialect.RQ, selectCols), ",")
	}
	query := fmt.Sprintf(
		"select %s from \"safer_watch\".\"registry\" where \"dot_number\"=$1", sel,
	)

	q := queries.Raw(query, dotNumber)

	err := q.Bind(ctx, exec, registryObj)
	if err != nil {
		if errors.Is(err, sql.ErrNoRows) {
			return nil, sql.ErrNoRows
		}
		return nil, errors.Wrap(err, "safer_watch: unable to select from registry")
	}

	if err = registryObj.doAfterSelectHooks(ctx, exec); err != nil {
		return registryObj, err
	}

	return registryObj, nil
}

// Insert a single record using an executor.
// See boil.Columns.InsertColumnSet documentation to understand column list inference for inserts.
func (o *Registry) Insert(ctx context.Context, exec boil.ContextExecutor, columns boil.Columns) error {
	if o == nil {
		return errors.New("safer_watch: no registry provided for insertion")
	}

	var err error

	if err := o.doBeforeInsertHooks(ctx, exec); err != nil {
		return err
	}

	nzDefaults := queries.NonZeroDefaultSet(registryColumnsWithDefault, o)

	key := makeCacheKey(columns, nzDefaults)
	registryInsertCacheMut.RLock()
	cache, cached := registryInsertCache[key]
	registryInsertCacheMut.RUnlock()

	if !cached {
		wl, returnColumns := columns.InsertColumnSet(
			registryAllColumns,
			registryColumnsWithDefault,
			registryColumnsWithoutDefault,
			nzDefaults,
		)

		cache.valueMapping, err = queries.BindMapping(registryType, registryMapping, wl)
		if err != nil {
			return err
		}
		cache.retMapping, err = queries.BindMapping(registryType, registryMapping, returnColumns)
		if err != nil {
			return err
		}
		if len(wl) != 0 {
			cache.query = fmt.Sprintf("INSERT INTO \"safer_watch\".\"registry\" (\"%s\") %%sVALUES (%s)%%s", strings.Join(wl, "\",\""), strmangle.Placeholders(dialect.UseIndexPlaceholders, len(wl), 1, 1))
		} else {
			cache.query = "INSERT INTO \"safer_watch\".\"registry\" %sDEFAULT VALUES%s"
		}

		var queryOutput, queryReturning string

		if len(cache.retMapping) != 0 {
			queryReturning = fmt.Sprintf(" RETURNING \"%s\"", strings.Join(returnColumns, "\",\""))
		}

		cache.query = fmt.Sprintf(cache.query, queryOutput, queryReturning)
	}

	value := reflect.Indirect(reflect.ValueOf(o))
	vals := queries.ValuesFromMapping(value, cache.valueMapping)

	if boil.IsDebug(ctx) {
		writer := boil.DebugWriterFrom(ctx)
		fmt.Fprintln(writer, cache.query)
		fmt.Fprintln(writer, vals)
	}

	if len(cache.retMapping) != 0 {
		err = exec.QueryRowContext(ctx, cache.query, vals...).Scan(queries.PtrsFromMapping(value, cache.retMapping)...)
	} else {
		_, err = exec.ExecContext(ctx, cache.query, vals...)
	}

	if err != nil {
		return errors.Wrap(err, "safer_watch: unable to insert into registry")
	}

	if !cached {
		registryInsertCacheMut.Lock()
		registryInsertCache[key] = cache
		registryInsertCacheMut.Unlock()
	}

	return o.doAfterInsertHooks(ctx, exec)
}

// Update uses an executor to update the Registry.
// See boil.Columns.UpdateColumnSet documentation to understand column list inference for updates.
// Update does not automatically update the record in case of default values. Use .Reload() to refresh the records.
func (o *Registry) Update(ctx context.Context, exec boil.ContextExecutor, columns boil.Columns) (int64, error) {
	var err error
	if err = o.doBeforeUpdateHooks(ctx, exec); err != nil {
		return 0, err
	}
	key := makeCacheKey(columns, nil)
	registryUpdateCacheMut.RLock()
	cache, cached := registryUpdateCache[key]
	registryUpdateCacheMut.RUnlock()

	if !cached {
		wl := columns.UpdateColumnSet(
			registryAllColumns,
			registryPrimaryKeyColumns,
		)

		if !columns.IsWhitelist() {
			wl = strmangle.SetComplement(wl, []string{"created_at"})
		}
		if len(wl) == 0 {
			return 0, errors.New("safer_watch: unable to update registry, could not build whitelist")
		}

		cache.query = fmt.Sprintf("UPDATE \"safer_watch\".\"registry\" SET %s WHERE %s",
			strmangle.SetParamNames("\"", "\"", 1, wl),
			strmangle.WhereClause("\"", "\"", len(wl)+1, registryPrimaryKeyColumns),
		)
		cache.valueMapping, err = queries.BindMapping(registryType, registryMapping, append(wl, registryPrimaryKeyColumns...))
		if err != nil {
			return 0, err
		}
	}

	values := queries.ValuesFromMapping(reflect.Indirect(reflect.ValueOf(o)), cache.valueMapping)

	if boil.IsDebug(ctx) {
		writer := boil.DebugWriterFrom(ctx)
		fmt.Fprintln(writer, cache.query)
		fmt.Fprintln(writer, values)
	}
	var result sql.Result
	result, err = exec.ExecContext(ctx, cache.query, values...)
	if err != nil {
		return 0, errors.Wrap(err, "safer_watch: unable to update registry row")
	}

	rowsAff, err := result.RowsAffected()
	if err != nil {
		return 0, errors.Wrap(err, "safer_watch: failed to get rows affected by update for registry")
	}

	if !cached {
		registryUpdateCacheMut.Lock()
		registryUpdateCache[key] = cache
		registryUpdateCacheMut.Unlock()
	}

	return rowsAff, o.doAfterUpdateHooks(ctx, exec)
}

// UpdateAll updates all rows with the specified column values.
func (q registryQuery) UpdateAll(ctx context.Context, exec boil.ContextExecutor, cols M) (int64, error) {
	queries.SetUpdate(q.Query, cols)

	result, err := q.Query.ExecContext(ctx, exec)
	if err != nil {
		return 0, errors.Wrap(err, "safer_watch: unable to update all for registry")
	}

	rowsAff, err := result.RowsAffected()
	if err != nil {
		return 0, errors.Wrap(err, "safer_watch: unable to retrieve rows affected for registry")
	}

	return rowsAff, nil
}

// UpdateAll updates all rows with the specified column values, using an executor.
func (o RegistrySlice) UpdateAll(ctx context.Context, exec boil.ContextExecutor, cols M) (int64, error) {
	ln := int64(len(o))
	if ln == 0 {
		return 0, nil
	}

	if len(cols) == 0 {
		return 0, errors.New("safer_watch: update all requires at least one column argument")
	}

	colNames := make([]string, len(cols))
	args := make([]interface{}, len(cols))

	i := 0
	for name, value := range cols {
		colNames[i] = name
		args[i] = value
		i++
	}

	// Append all of the primary key values for each column
	for _, obj := range o {
		pkeyArgs := queries.ValuesFromMapping(reflect.Indirect(reflect.ValueOf(obj)), registryPrimaryKeyMapping)
		args = append(args, pkeyArgs...)
	}

	sql := fmt.Sprintf("UPDATE \"safer_watch\".\"registry\" SET %s WHERE %s",
		strmangle.SetParamNames("\"", "\"", 1, colNames),
		strmangle.WhereClauseRepeated(string(dialect.LQ), string(dialect.RQ), len(colNames)+1, registryPrimaryKeyColumns, len(o)))

	if boil.IsDebug(ctx) {
		writer := boil.DebugWriterFrom(ctx)
		fmt.Fprintln(writer, sql)
		fmt.Fprintln(writer, args...)
	}
	result, err := exec.ExecContext(ctx, sql, args...)
	if err != nil {
		return 0, errors.Wrap(err, "safer_watch: unable to update all in registry slice")
	}

	rowsAff, err := result.RowsAffected()
	if err != nil {
		return 0, errors.Wrap(err, "safer_watch: unable to retrieve rows affected all in update all registry")
	}
	return rowsAff, nil
}

// Upsert attempts an insert using an executor, and does an update or ignore on conflict.
// See boil.Columns documentation for how to properly use updateColumns and insertColumns.
func (o *Registry) Upsert(ctx context.Context, exec boil.ContextExecutor, updateOnConflict bool, conflictColumns []string, updateColumns, insertColumns boil.Columns) error {
	if o == nil {
		return errors.New("safer_watch: no registry provided for upsert")
	}

	if err := o.doBeforeUpsertHooks(ctx, exec); err != nil {
		return err
	}

	nzDefaults := queries.NonZeroDefaultSet(registryColumnsWithDefault, o)

	// Build cache key in-line uglily - mysql vs psql problems
	buf := strmangle.GetBuffer()
	if updateOnConflict {
		buf.WriteByte('t')
	} else {
		buf.WriteByte('f')
	}
	buf.WriteByte('.')
	for _, c := range conflictColumns {
		buf.WriteString(c)
	}
	buf.WriteByte('.')
	buf.WriteString(strconv.Itoa(updateColumns.Kind))
	for _, c := range updateColumns.Cols {
		buf.WriteString(c)
	}
	buf.WriteByte('.')
	buf.WriteString(strconv.Itoa(insertColumns.Kind))
	for _, c := range insertColumns.Cols {
		buf.WriteString(c)
	}
	buf.WriteByte('.')
	for _, c := range nzDefaults {
		buf.WriteString(c)
	}
	key := buf.String()
	strmangle.PutBuffer(buf)

	registryUpsertCacheMut.RLock()
	cache, cached := registryUpsertCache[key]
	registryUpsertCacheMut.RUnlock()

	var err error

	if !cached {
		insert, ret := insertColumns.InsertColumnSet(
			registryAllColumns,
			registryColumnsWithDefault,
			registryColumnsWithoutDefault,
			nzDefaults,
		)

		update := updateColumns.UpdateColumnSet(
			registryAllColumns,
			registryPrimaryKeyColumns,
		)

		if updateOnConflict && len(update) == 0 {
			return errors.New("safer_watch: unable to upsert registry, could not build update column list")
		}

		conflict := conflictColumns
		if len(conflict) == 0 {
			conflict = make([]string, len(registryPrimaryKeyColumns))
			copy(conflict, registryPrimaryKeyColumns)
		}
		cache.query = buildUpsertQueryPostgres(dialect, "\"safer_watch\".\"registry\"", updateOnConflict, ret, update, conflict, insert)

		cache.valueMapping, err = queries.BindMapping(registryType, registryMapping, insert)
		if err != nil {
			return err
		}
		if len(ret) != 0 {
			cache.retMapping, err = queries.BindMapping(registryType, registryMapping, ret)
			if err != nil {
				return err
			}
		}
	}

	value := reflect.Indirect(reflect.ValueOf(o))
	vals := queries.ValuesFromMapping(value, cache.valueMapping)
	var returns []interface{}
	if len(cache.retMapping) != 0 {
		returns = queries.PtrsFromMapping(value, cache.retMapping)
	}

	if boil.IsDebug(ctx) {
		writer := boil.DebugWriterFrom(ctx)
		fmt.Fprintln(writer, cache.query)
		fmt.Fprintln(writer, vals)
	}
	if len(cache.retMapping) != 0 {
		err = exec.QueryRowContext(ctx, cache.query, vals...).Scan(returns...)
		if errors.Is(err, sql.ErrNoRows) {
			err = nil // Postgres doesn't return anything when there's no update
		}
	} else {
		_, err = exec.ExecContext(ctx, cache.query, vals...)
	}
	if err != nil {
		return errors.Wrap(err, "safer_watch: unable to upsert registry")
	}

	if !cached {
		registryUpsertCacheMut.Lock()
		registryUpsertCache[key] = cache
		registryUpsertCacheMut.Unlock()
	}

	return o.doAfterUpsertHooks(ctx, exec)
}

// Delete deletes a single Registry record with an executor.
// Delete will match against the primary key column to find the record to delete.
func (o *Registry) Delete(ctx context.Context, exec boil.ContextExecutor) (int64, error) {
	if o == nil {
		return 0, errors.New("safer_watch: no Registry provided for delete")
	}

	if err := o.doBeforeDeleteHooks(ctx, exec); err != nil {
		return 0, err
	}

	args := queries.ValuesFromMapping(reflect.Indirect(reflect.ValueOf(o)), registryPrimaryKeyMapping)
	sql := "DELETE FROM \"safer_watch\".\"registry\" WHERE \"dot_number\"=$1"

	if boil.IsDebug(ctx) {
		writer := boil.DebugWriterFrom(ctx)
		fmt.Fprintln(writer, sql)
		fmt.Fprintln(writer, args...)
	}
	result, err := exec.ExecContext(ctx, sql, args...)
	if err != nil {
		return 0, errors.Wrap(err, "safer_watch: unable to delete from registry")
	}

	rowsAff, err := result.RowsAffected()
	if err != nil {
		return 0, errors.Wrap(err, "safer_watch: failed to get rows affected by delete for registry")
	}

	if err := o.doAfterDeleteHooks(ctx, exec); err != nil {
		return 0, err
	}

	return rowsAff, nil
}

// DeleteAll deletes all matching rows.
func (q registryQuery) DeleteAll(ctx context.Context, exec boil.ContextExecutor) (int64, error) {
	if q.Query == nil {
		return 0, errors.New("safer_watch: no registryQuery provided for delete all")
	}

	queries.SetDelete(q.Query)

	result, err := q.Query.ExecContext(ctx, exec)
	if err != nil {
		return 0, errors.Wrap(err, "safer_watch: unable to delete all from registry")
	}

	rowsAff, err := result.RowsAffected()
	if err != nil {
		return 0, errors.Wrap(err, "safer_watch: failed to get rows affected by deleteall for registry")
	}

	return rowsAff, nil
}

// DeleteAll deletes all rows in the slice, using an executor.
func (o RegistrySlice) DeleteAll(ctx context.Context, exec boil.ContextExecutor) (int64, error) {
	if len(o) == 0 {
		return 0, nil
	}

	if len(registryBeforeDeleteHooks) != 0 {
		for _, obj := range o {
			if err := obj.doBeforeDeleteHooks(ctx, exec); err != nil {
				return 0, err
			}
		}
	}

	var args []interface{}
	for _, obj := range o {
		pkeyArgs := queries.ValuesFromMapping(reflect.Indirect(reflect.ValueOf(obj)), registryPrimaryKeyMapping)
		args = append(args, pkeyArgs...)
	}

	sql := "DELETE FROM \"safer_watch\".\"registry\" WHERE " +
		strmangle.WhereClauseRepeated(string(dialect.LQ), string(dialect.RQ), 1, registryPrimaryKeyColumns, len(o))

	if boil.IsDebug(ctx) {
		writer := boil.DebugWriterFrom(ctx)
		fmt.Fprintln(writer, sql)
		fmt.Fprintln(writer, args)
	}
	result, err := exec.ExecContext(ctx, sql, args...)
	if err != nil {
		return 0, errors.Wrap(err, "safer_watch: unable to delete all from registry slice")
	}

	rowsAff, err := result.RowsAffected()
	if err != nil {
		return 0, errors.Wrap(err, "safer_watch: failed to get rows affected by deleteall for registry")
	}

	if len(registryAfterDeleteHooks) != 0 {
		for _, obj := range o {
			if err := obj.doAfterDeleteHooks(ctx, exec); err != nil {
				return 0, err
			}
		}
	}

	return rowsAff, nil
}

// Reload refetches the object from the database
// using the primary keys with an executor.
func (o *Registry) Reload(ctx context.Context, exec boil.ContextExecutor) error {
	ret, err := FindRegistry(ctx, exec, o.DotNumber)
	if err != nil {
		return err
	}

	*o = *ret
	return nil
}

// ReloadAll refetches every row with matching primary key column values
// and overwrites the original object slice with the newly updated slice.
func (o *RegistrySlice) ReloadAll(ctx context.Context, exec boil.ContextExecutor) error {
	if o == nil || len(*o) == 0 {
		return nil
	}

	slice := RegistrySlice{}
	var args []interface{}
	for _, obj := range *o {
		pkeyArgs := queries.ValuesFromMapping(reflect.Indirect(reflect.ValueOf(obj)), registryPrimaryKeyMapping)
		args = append(args, pkeyArgs...)
	}

	sql := "SELECT \"safer_watch\".\"registry\".* FROM \"safer_watch\".\"registry\" WHERE " +
		strmangle.WhereClauseRepeated(string(dialect.LQ), string(dialect.RQ), 1, registryPrimaryKeyColumns, len(*o))

	q := queries.Raw(sql, args...)

	err := q.Bind(ctx, exec, &slice)
	if err != nil {
		return errors.Wrap(err, "safer_watch: unable to reload all in RegistrySlice")
	}

	*o = slice

	return nil
}

// RegistryExists checks if the Registry row exists.
func RegistryExists(ctx context.Context, exec boil.ContextExecutor, dotNumber int) (bool, error) {
	var exists bool
	sql := "select exists(select 1 from \"safer_watch\".\"registry\" where \"dot_number\"=$1 limit 1)"

	if boil.IsDebug(ctx) {
		writer := boil.DebugWriterFrom(ctx)
		fmt.Fprintln(writer, sql)
		fmt.Fprintln(writer, dotNumber)
	}
	row := exec.QueryRowContext(ctx, sql, dotNumber)

	err := row.Scan(&exists)
	if err != nil {
		return false, errors.Wrap(err, "safer_watch: unable to check if registry exists")
	}

	return exists, nil
}
