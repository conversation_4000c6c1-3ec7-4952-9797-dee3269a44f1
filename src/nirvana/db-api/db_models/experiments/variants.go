// Code generated by SQLBoiler (https://github.com/volatiletech/sqlboiler). DO NOT EDIT.
// This file is meant to be re-generated in place and/or deleted at any time.

package experiments

import (
	"context"
	"database/sql"
	"fmt"
	"reflect"
	"strconv"
	"strings"
	"sync"
	"time"

	"github.com/friendsofgo/errors"
	"github.com/volatiletech/null/v8"
	"github.com/volatiletech/sqlboiler/v4/boil"
	"github.com/volatiletech/sqlboiler/v4/queries"
	"github.com/volatiletech/sqlboiler/v4/queries/qm"
	"github.com/volatiletech/sqlboiler/v4/queries/qmhelper"
	"github.com/volatiletech/strmangle"
)

// Variant is an object representing the database table.
type Variant struct {
	ID           string      `boil:"id" json:"id" toml:"id" yaml:"id"`
	ExperimentID string      `boil:"experiment_id" json:"experiment_id" toml:"experiment_id" yaml:"experiment_id"`
	Name         string      `boil:"name" json:"name" toml:"name" yaml:"name"`
	Description  null.String `boil:"description" json:"description,omitempty" toml:"description" yaml:"description,omitempty"`
	CreatedAt    time.Time   `boil:"created_at" json:"created_at" toml:"created_at" yaml:"created_at"`
	UpdatedAt    time.Time   `boil:"updated_at" json:"updated_at" toml:"updated_at" yaml:"updated_at"`

	R *variantR `boil:"" json:"" toml:"" yaml:""`
	L variantL  `boil:"-" json:"-" toml:"-" yaml:"-"`
}

var VariantColumns = struct {
	ID           string
	ExperimentID string
	Name         string
	Description  string
	CreatedAt    string
	UpdatedAt    string
}{
	ID:           "id",
	ExperimentID: "experiment_id",
	Name:         "name",
	Description:  "description",
	CreatedAt:    "created_at",
	UpdatedAt:    "updated_at",
}

var VariantTableColumns = struct {
	ID           string
	ExperimentID string
	Name         string
	Description  string
	CreatedAt    string
	UpdatedAt    string
}{
	ID:           "variants.id",
	ExperimentID: "variants.experiment_id",
	Name:         "variants.name",
	Description:  "variants.description",
	CreatedAt:    "variants.created_at",
	UpdatedAt:    "variants.updated_at",
}

// Generated where

var VariantWhere = struct {
	ID           whereHelperstring
	ExperimentID whereHelperstring
	Name         whereHelperstring
	Description  whereHelpernull_String
	CreatedAt    whereHelpertime_Time
	UpdatedAt    whereHelpertime_Time
}{
	ID:           whereHelperstring{field: "\"experiments\".\"variants\".\"id\""},
	ExperimentID: whereHelperstring{field: "\"experiments\".\"variants\".\"experiment_id\""},
	Name:         whereHelperstring{field: "\"experiments\".\"variants\".\"name\""},
	Description:  whereHelpernull_String{field: "\"experiments\".\"variants\".\"description\""},
	CreatedAt:    whereHelpertime_Time{field: "\"experiments\".\"variants\".\"created_at\""},
	UpdatedAt:    whereHelpertime_Time{field: "\"experiments\".\"variants\".\"updated_at\""},
}

// VariantRels is where relationship names are stored.
var VariantRels = struct {
	Experiment  string
	Assignments string
}{
	Experiment:  "Experiment",
	Assignments: "Assignments",
}

// variantR is where relationships are stored.
type variantR struct {
	Experiment  *ExperimentDetail `boil:"Experiment" json:"Experiment" toml:"Experiment" yaml:"Experiment"`
	Assignments AssignmentSlice   `boil:"Assignments" json:"Assignments" toml:"Assignments" yaml:"Assignments"`
}

// NewStruct creates a new relationship struct
func (*variantR) NewStruct() *variantR {
	return &variantR{}
}

// variantL is where Load methods for each relationship are stored.
type variantL struct{}

var (
	variantAllColumns            = []string{"id", "experiment_id", "name", "description", "created_at", "updated_at"}
	variantColumnsWithoutDefault = []string{"id", "experiment_id", "name", "created_at", "updated_at"}
	variantColumnsWithDefault    = []string{"description"}
	variantPrimaryKeyColumns     = []string{"id"}
	variantGeneratedColumns      = []string{}
)

type (
	// VariantSlice is an alias for a slice of pointers to Variant.
	// This should almost always be used instead of []Variant.
	VariantSlice []*Variant
	// VariantHook is the signature for custom Variant hook methods
	VariantHook func(context.Context, boil.ContextExecutor, *Variant) error

	variantQuery struct {
		*queries.Query
	}
)

// Cache for insert, update and upsert
var (
	variantType                 = reflect.TypeOf(&Variant{})
	variantMapping              = queries.MakeStructMapping(variantType)
	variantPrimaryKeyMapping, _ = queries.BindMapping(variantType, variantMapping, variantPrimaryKeyColumns)
	variantInsertCacheMut       sync.RWMutex
	variantInsertCache          = make(map[string]insertCache)
	variantUpdateCacheMut       sync.RWMutex
	variantUpdateCache          = make(map[string]updateCache)
	variantUpsertCacheMut       sync.RWMutex
	variantUpsertCache          = make(map[string]insertCache)
)

var (
	// Force time package dependency for automated UpdatedAt/CreatedAt.
	_ = time.Second
	// Force qmhelper dependency for where clause generation (which doesn't
	// always happen)
	_ = qmhelper.Where
)

var variantAfterSelectHooks []VariantHook

var variantBeforeInsertHooks []VariantHook
var variantAfterInsertHooks []VariantHook

var variantBeforeUpdateHooks []VariantHook
var variantAfterUpdateHooks []VariantHook

var variantBeforeDeleteHooks []VariantHook
var variantAfterDeleteHooks []VariantHook

var variantBeforeUpsertHooks []VariantHook
var variantAfterUpsertHooks []VariantHook

// doAfterSelectHooks executes all "after Select" hooks.
func (o *Variant) doAfterSelectHooks(ctx context.Context, exec boil.ContextExecutor) (err error) {
	if boil.HooksAreSkipped(ctx) {
		return nil
	}

	for _, hook := range variantAfterSelectHooks {
		if err := hook(ctx, exec, o); err != nil {
			return err
		}
	}

	return nil
}

// doBeforeInsertHooks executes all "before insert" hooks.
func (o *Variant) doBeforeInsertHooks(ctx context.Context, exec boil.ContextExecutor) (err error) {
	if boil.HooksAreSkipped(ctx) {
		return nil
	}

	for _, hook := range variantBeforeInsertHooks {
		if err := hook(ctx, exec, o); err != nil {
			return err
		}
	}

	return nil
}

// doAfterInsertHooks executes all "after Insert" hooks.
func (o *Variant) doAfterInsertHooks(ctx context.Context, exec boil.ContextExecutor) (err error) {
	if boil.HooksAreSkipped(ctx) {
		return nil
	}

	for _, hook := range variantAfterInsertHooks {
		if err := hook(ctx, exec, o); err != nil {
			return err
		}
	}

	return nil
}

// doBeforeUpdateHooks executes all "before Update" hooks.
func (o *Variant) doBeforeUpdateHooks(ctx context.Context, exec boil.ContextExecutor) (err error) {
	if boil.HooksAreSkipped(ctx) {
		return nil
	}

	for _, hook := range variantBeforeUpdateHooks {
		if err := hook(ctx, exec, o); err != nil {
			return err
		}
	}

	return nil
}

// doAfterUpdateHooks executes all "after Update" hooks.
func (o *Variant) doAfterUpdateHooks(ctx context.Context, exec boil.ContextExecutor) (err error) {
	if boil.HooksAreSkipped(ctx) {
		return nil
	}

	for _, hook := range variantAfterUpdateHooks {
		if err := hook(ctx, exec, o); err != nil {
			return err
		}
	}

	return nil
}

// doBeforeDeleteHooks executes all "before Delete" hooks.
func (o *Variant) doBeforeDeleteHooks(ctx context.Context, exec boil.ContextExecutor) (err error) {
	if boil.HooksAreSkipped(ctx) {
		return nil
	}

	for _, hook := range variantBeforeDeleteHooks {
		if err := hook(ctx, exec, o); err != nil {
			return err
		}
	}

	return nil
}

// doAfterDeleteHooks executes all "after Delete" hooks.
func (o *Variant) doAfterDeleteHooks(ctx context.Context, exec boil.ContextExecutor) (err error) {
	if boil.HooksAreSkipped(ctx) {
		return nil
	}

	for _, hook := range variantAfterDeleteHooks {
		if err := hook(ctx, exec, o); err != nil {
			return err
		}
	}

	return nil
}

// doBeforeUpsertHooks executes all "before Upsert" hooks.
func (o *Variant) doBeforeUpsertHooks(ctx context.Context, exec boil.ContextExecutor) (err error) {
	if boil.HooksAreSkipped(ctx) {
		return nil
	}

	for _, hook := range variantBeforeUpsertHooks {
		if err := hook(ctx, exec, o); err != nil {
			return err
		}
	}

	return nil
}

// doAfterUpsertHooks executes all "after Upsert" hooks.
func (o *Variant) doAfterUpsertHooks(ctx context.Context, exec boil.ContextExecutor) (err error) {
	if boil.HooksAreSkipped(ctx) {
		return nil
	}

	for _, hook := range variantAfterUpsertHooks {
		if err := hook(ctx, exec, o); err != nil {
			return err
		}
	}

	return nil
}

// AddVariantHook registers your hook function for all future operations.
func AddVariantHook(hookPoint boil.HookPoint, variantHook VariantHook) {
	switch hookPoint {
	case boil.AfterSelectHook:
		variantAfterSelectHooks = append(variantAfterSelectHooks, variantHook)
	case boil.BeforeInsertHook:
		variantBeforeInsertHooks = append(variantBeforeInsertHooks, variantHook)
	case boil.AfterInsertHook:
		variantAfterInsertHooks = append(variantAfterInsertHooks, variantHook)
	case boil.BeforeUpdateHook:
		variantBeforeUpdateHooks = append(variantBeforeUpdateHooks, variantHook)
	case boil.AfterUpdateHook:
		variantAfterUpdateHooks = append(variantAfterUpdateHooks, variantHook)
	case boil.BeforeDeleteHook:
		variantBeforeDeleteHooks = append(variantBeforeDeleteHooks, variantHook)
	case boil.AfterDeleteHook:
		variantAfterDeleteHooks = append(variantAfterDeleteHooks, variantHook)
	case boil.BeforeUpsertHook:
		variantBeforeUpsertHooks = append(variantBeforeUpsertHooks, variantHook)
	case boil.AfterUpsertHook:
		variantAfterUpsertHooks = append(variantAfterUpsertHooks, variantHook)
	}
}

// One returns a single variant record from the query.
func (q variantQuery) One(ctx context.Context, exec boil.ContextExecutor) (*Variant, error) {
	o := &Variant{}

	queries.SetLimit(q.Query, 1)

	err := q.Bind(ctx, exec, o)
	if err != nil {
		if errors.Is(err, sql.ErrNoRows) {
			return nil, sql.ErrNoRows
		}
		return nil, errors.Wrap(err, "experiments: failed to execute a one query for variants")
	}

	if err := o.doAfterSelectHooks(ctx, exec); err != nil {
		return o, err
	}

	return o, nil
}

// All returns all Variant records from the query.
func (q variantQuery) All(ctx context.Context, exec boil.ContextExecutor) (VariantSlice, error) {
	var o []*Variant

	err := q.Bind(ctx, exec, &o)
	if err != nil {
		return nil, errors.Wrap(err, "experiments: failed to assign all query results to Variant slice")
	}

	if len(variantAfterSelectHooks) != 0 {
		for _, obj := range o {
			if err := obj.doAfterSelectHooks(ctx, exec); err != nil {
				return o, err
			}
		}
	}

	return o, nil
}

// Count returns the count of all Variant records in the query.
func (q variantQuery) Count(ctx context.Context, exec boil.ContextExecutor) (int64, error) {
	var count int64

	queries.SetSelect(q.Query, nil)
	queries.SetCount(q.Query)

	err := q.Query.QueryRowContext(ctx, exec).Scan(&count)
	if err != nil {
		return 0, errors.Wrap(err, "experiments: failed to count variants rows")
	}

	return count, nil
}

// Exists checks if the row exists in the table.
func (q variantQuery) Exists(ctx context.Context, exec boil.ContextExecutor) (bool, error) {
	var count int64

	queries.SetSelect(q.Query, nil)
	queries.SetCount(q.Query)
	queries.SetLimit(q.Query, 1)

	err := q.Query.QueryRowContext(ctx, exec).Scan(&count)
	if err != nil {
		return false, errors.Wrap(err, "experiments: failed to check if variants exists")
	}

	return count > 0, nil
}

// Experiment pointed to by the foreign key.
func (o *Variant) Experiment(mods ...qm.QueryMod) experimentDetailQuery {
	queryMods := []qm.QueryMod{
		qm.Where("\"id\" = ?", o.ExperimentID),
	}

	queryMods = append(queryMods, mods...)

	return ExperimentDetails(queryMods...)
}

// Assignments retrieves all the assignment's Assignments with an executor.
func (o *Variant) Assignments(mods ...qm.QueryMod) assignmentQuery {
	var queryMods []qm.QueryMod
	if len(mods) != 0 {
		queryMods = append(queryMods, mods...)
	}

	queryMods = append(queryMods,
		qm.Where("\"experiments\".\"assignments\".\"variant_id\"=?", o.ID),
	)

	return Assignments(queryMods...)
}

// LoadExperiment allows an eager lookup of values, cached into the
// loaded structs of the objects. This is for an N-1 relationship.
func (variantL) LoadExperiment(ctx context.Context, e boil.ContextExecutor, singular bool, maybeVariant interface{}, mods queries.Applicator) error {
	var slice []*Variant
	var object *Variant

	if singular {
		object = maybeVariant.(*Variant)
	} else {
		slice = *maybeVariant.(*[]*Variant)
	}

	args := make([]interface{}, 0, 1)
	if singular {
		if object.R == nil {
			object.R = &variantR{}
		}
		args = append(args, object.ExperimentID)

	} else {
	Outer:
		for _, obj := range slice {
			if obj.R == nil {
				obj.R = &variantR{}
			}

			for _, a := range args {
				if a == obj.ExperimentID {
					continue Outer
				}
			}

			args = append(args, obj.ExperimentID)

		}
	}

	if len(args) == 0 {
		return nil
	}

	query := NewQuery(
		qm.From(`experiments.experiment_details`),
		qm.WhereIn(`experiments.experiment_details.id in ?`, args...),
	)
	if mods != nil {
		mods.Apply(query)
	}

	results, err := query.QueryContext(ctx, e)
	if err != nil {
		return errors.Wrap(err, "failed to eager load ExperimentDetail")
	}

	var resultSlice []*ExperimentDetail
	if err = queries.Bind(results, &resultSlice); err != nil {
		return errors.Wrap(err, "failed to bind eager loaded slice ExperimentDetail")
	}

	if err = results.Close(); err != nil {
		return errors.Wrap(err, "failed to close results of eager load for experiment_details")
	}
	if err = results.Err(); err != nil {
		return errors.Wrap(err, "error occurred during iteration of eager loaded relations for experiment_details")
	}

	if len(variantAfterSelectHooks) != 0 {
		for _, obj := range resultSlice {
			if err := obj.doAfterSelectHooks(ctx, e); err != nil {
				return err
			}
		}
	}

	if len(resultSlice) == 0 {
		return nil
	}

	if singular {
		foreign := resultSlice[0]
		object.R.Experiment = foreign
		if foreign.R == nil {
			foreign.R = &experimentDetailR{}
		}
		foreign.R.ExperimentVariants = append(foreign.R.ExperimentVariants, object)
		return nil
	}

	for _, local := range slice {
		for _, foreign := range resultSlice {
			if local.ExperimentID == foreign.ID {
				local.R.Experiment = foreign
				if foreign.R == nil {
					foreign.R = &experimentDetailR{}
				}
				foreign.R.ExperimentVariants = append(foreign.R.ExperimentVariants, local)
				break
			}
		}
	}

	return nil
}

// LoadAssignments allows an eager lookup of values, cached into the
// loaded structs of the objects. This is for a 1-M or N-M relationship.
func (variantL) LoadAssignments(ctx context.Context, e boil.ContextExecutor, singular bool, maybeVariant interface{}, mods queries.Applicator) error {
	var slice []*Variant
	var object *Variant

	if singular {
		object = maybeVariant.(*Variant)
	} else {
		slice = *maybeVariant.(*[]*Variant)
	}

	args := make([]interface{}, 0, 1)
	if singular {
		if object.R == nil {
			object.R = &variantR{}
		}
		args = append(args, object.ID)
	} else {
	Outer:
		for _, obj := range slice {
			if obj.R == nil {
				obj.R = &variantR{}
			}

			for _, a := range args {
				if a == obj.ID {
					continue Outer
				}
			}

			args = append(args, obj.ID)
		}
	}

	if len(args) == 0 {
		return nil
	}

	query := NewQuery(
		qm.From(`experiments.assignments`),
		qm.WhereIn(`experiments.assignments.variant_id in ?`, args...),
	)
	if mods != nil {
		mods.Apply(query)
	}

	results, err := query.QueryContext(ctx, e)
	if err != nil {
		return errors.Wrap(err, "failed to eager load assignments")
	}

	var resultSlice []*Assignment
	if err = queries.Bind(results, &resultSlice); err != nil {
		return errors.Wrap(err, "failed to bind eager loaded slice assignments")
	}

	if err = results.Close(); err != nil {
		return errors.Wrap(err, "failed to close results in eager load on assignments")
	}
	if err = results.Err(); err != nil {
		return errors.Wrap(err, "error occurred during iteration of eager loaded relations for assignments")
	}

	if len(assignmentAfterSelectHooks) != 0 {
		for _, obj := range resultSlice {
			if err := obj.doAfterSelectHooks(ctx, e); err != nil {
				return err
			}
		}
	}
	if singular {
		object.R.Assignments = resultSlice
		for _, foreign := range resultSlice {
			if foreign.R == nil {
				foreign.R = &assignmentR{}
			}
			foreign.R.Variant = object
		}
		return nil
	}

	for _, foreign := range resultSlice {
		for _, local := range slice {
			if local.ID == foreign.VariantID {
				local.R.Assignments = append(local.R.Assignments, foreign)
				if foreign.R == nil {
					foreign.R = &assignmentR{}
				}
				foreign.R.Variant = local
				break
			}
		}
	}

	return nil
}

// SetExperiment of the variant to the related item.
// Sets o.R.Experiment to related.
// Adds o to related.R.ExperimentVariants.
func (o *Variant) SetExperiment(ctx context.Context, exec boil.ContextExecutor, insert bool, related *ExperimentDetail) error {
	var err error
	if insert {
		if err = related.Insert(ctx, exec, boil.Infer()); err != nil {
			return errors.Wrap(err, "failed to insert into foreign table")
		}
	}

	updateQuery := fmt.Sprintf(
		"UPDATE \"experiments\".\"variants\" SET %s WHERE %s",
		strmangle.SetParamNames("\"", "\"", 1, []string{"experiment_id"}),
		strmangle.WhereClause("\"", "\"", 2, variantPrimaryKeyColumns),
	)
	values := []interface{}{related.ID, o.ID}

	if boil.IsDebug(ctx) {
		writer := boil.DebugWriterFrom(ctx)
		fmt.Fprintln(writer, updateQuery)
		fmt.Fprintln(writer, values)
	}
	if _, err = exec.ExecContext(ctx, updateQuery, values...); err != nil {
		return errors.Wrap(err, "failed to update local table")
	}

	o.ExperimentID = related.ID
	if o.R == nil {
		o.R = &variantR{
			Experiment: related,
		}
	} else {
		o.R.Experiment = related
	}

	if related.R == nil {
		related.R = &experimentDetailR{
			ExperimentVariants: VariantSlice{o},
		}
	} else {
		related.R.ExperimentVariants = append(related.R.ExperimentVariants, o)
	}

	return nil
}

// AddAssignments adds the given related objects to the existing relationships
// of the variant, optionally inserting them as new records.
// Appends related to o.R.Assignments.
// Sets related.R.Variant appropriately.
func (o *Variant) AddAssignments(ctx context.Context, exec boil.ContextExecutor, insert bool, related ...*Assignment) error {
	var err error
	for _, rel := range related {
		if insert {
			rel.VariantID = o.ID
			if err = rel.Insert(ctx, exec, boil.Infer()); err != nil {
				return errors.Wrap(err, "failed to insert into foreign table")
			}
		} else {
			updateQuery := fmt.Sprintf(
				"UPDATE \"experiments\".\"assignments\" SET %s WHERE %s",
				strmangle.SetParamNames("\"", "\"", 1, []string{"variant_id"}),
				strmangle.WhereClause("\"", "\"", 2, assignmentPrimaryKeyColumns),
			)
			values := []interface{}{o.ID, rel.ID}

			if boil.IsDebug(ctx) {
				writer := boil.DebugWriterFrom(ctx)
				fmt.Fprintln(writer, updateQuery)
				fmt.Fprintln(writer, values)
			}
			if _, err = exec.ExecContext(ctx, updateQuery, values...); err != nil {
				return errors.Wrap(err, "failed to update foreign table")
			}

			rel.VariantID = o.ID
		}
	}

	if o.R == nil {
		o.R = &variantR{
			Assignments: related,
		}
	} else {
		o.R.Assignments = append(o.R.Assignments, related...)
	}

	for _, rel := range related {
		if rel.R == nil {
			rel.R = &assignmentR{
				Variant: o,
			}
		} else {
			rel.R.Variant = o
		}
	}
	return nil
}

// Variants retrieves all the records using an executor.
func Variants(mods ...qm.QueryMod) variantQuery {
	mods = append(mods, qm.From("\"experiments\".\"variants\""))
	q := NewQuery(mods...)
	if len(queries.GetSelect(q)) == 0 {
		queries.SetSelect(q, []string{"\"experiments\".\"variants\".*"})
	}

	return variantQuery{q}
}

// FindVariant retrieves a single record by ID with an executor.
// If selectCols is empty Find will return all columns.
func FindVariant(ctx context.Context, exec boil.ContextExecutor, iD string, selectCols ...string) (*Variant, error) {
	variantObj := &Variant{}

	sel := "*"
	if len(selectCols) > 0 {
		sel = strings.Join(strmangle.IdentQuoteSlice(dialect.LQ, dialect.RQ, selectCols), ",")
	}
	query := fmt.Sprintf(
		"select %s from \"experiments\".\"variants\" where \"id\"=$1", sel,
	)

	q := queries.Raw(query, iD)

	err := q.Bind(ctx, exec, variantObj)
	if err != nil {
		if errors.Is(err, sql.ErrNoRows) {
			return nil, sql.ErrNoRows
		}
		return nil, errors.Wrap(err, "experiments: unable to select from variants")
	}

	if err = variantObj.doAfterSelectHooks(ctx, exec); err != nil {
		return variantObj, err
	}

	return variantObj, nil
}

// Insert a single record using an executor.
// See boil.Columns.InsertColumnSet documentation to understand column list inference for inserts.
func (o *Variant) Insert(ctx context.Context, exec boil.ContextExecutor, columns boil.Columns) error {
	if o == nil {
		return errors.New("experiments: no variants provided for insertion")
	}

	var err error
	if !boil.TimestampsAreSkipped(ctx) {
		currTime := time.Now().In(boil.GetLocation())

		if o.CreatedAt.IsZero() {
			o.CreatedAt = currTime
		}
		if o.UpdatedAt.IsZero() {
			o.UpdatedAt = currTime
		}
	}

	if err := o.doBeforeInsertHooks(ctx, exec); err != nil {
		return err
	}

	nzDefaults := queries.NonZeroDefaultSet(variantColumnsWithDefault, o)

	key := makeCacheKey(columns, nzDefaults)
	variantInsertCacheMut.RLock()
	cache, cached := variantInsertCache[key]
	variantInsertCacheMut.RUnlock()

	if !cached {
		wl, returnColumns := columns.InsertColumnSet(
			variantAllColumns,
			variantColumnsWithDefault,
			variantColumnsWithoutDefault,
			nzDefaults,
		)

		cache.valueMapping, err = queries.BindMapping(variantType, variantMapping, wl)
		if err != nil {
			return err
		}
		cache.retMapping, err = queries.BindMapping(variantType, variantMapping, returnColumns)
		if err != nil {
			return err
		}
		if len(wl) != 0 {
			cache.query = fmt.Sprintf("INSERT INTO \"experiments\".\"variants\" (\"%s\") %%sVALUES (%s)%%s", strings.Join(wl, "\",\""), strmangle.Placeholders(dialect.UseIndexPlaceholders, len(wl), 1, 1))
		} else {
			cache.query = "INSERT INTO \"experiments\".\"variants\" %sDEFAULT VALUES%s"
		}

		var queryOutput, queryReturning string

		if len(cache.retMapping) != 0 {
			queryReturning = fmt.Sprintf(" RETURNING \"%s\"", strings.Join(returnColumns, "\",\""))
		}

		cache.query = fmt.Sprintf(cache.query, queryOutput, queryReturning)
	}

	value := reflect.Indirect(reflect.ValueOf(o))
	vals := queries.ValuesFromMapping(value, cache.valueMapping)

	if boil.IsDebug(ctx) {
		writer := boil.DebugWriterFrom(ctx)
		fmt.Fprintln(writer, cache.query)
		fmt.Fprintln(writer, vals)
	}

	if len(cache.retMapping) != 0 {
		err = exec.QueryRowContext(ctx, cache.query, vals...).Scan(queries.PtrsFromMapping(value, cache.retMapping)...)
	} else {
		_, err = exec.ExecContext(ctx, cache.query, vals...)
	}

	if err != nil {
		return errors.Wrap(err, "experiments: unable to insert into variants")
	}

	if !cached {
		variantInsertCacheMut.Lock()
		variantInsertCache[key] = cache
		variantInsertCacheMut.Unlock()
	}

	return o.doAfterInsertHooks(ctx, exec)
}

// Update uses an executor to update the Variant.
// See boil.Columns.UpdateColumnSet documentation to understand column list inference for updates.
// Update does not automatically update the record in case of default values. Use .Reload() to refresh the records.
func (o *Variant) Update(ctx context.Context, exec boil.ContextExecutor, columns boil.Columns) (int64, error) {
	if !boil.TimestampsAreSkipped(ctx) {
		currTime := time.Now().In(boil.GetLocation())

		o.UpdatedAt = currTime
	}

	var err error
	if err = o.doBeforeUpdateHooks(ctx, exec); err != nil {
		return 0, err
	}
	key := makeCacheKey(columns, nil)
	variantUpdateCacheMut.RLock()
	cache, cached := variantUpdateCache[key]
	variantUpdateCacheMut.RUnlock()

	if !cached {
		wl := columns.UpdateColumnSet(
			variantAllColumns,
			variantPrimaryKeyColumns,
		)

		if !columns.IsWhitelist() {
			wl = strmangle.SetComplement(wl, []string{"created_at"})
		}
		if len(wl) == 0 {
			return 0, errors.New("experiments: unable to update variants, could not build whitelist")
		}

		cache.query = fmt.Sprintf("UPDATE \"experiments\".\"variants\" SET %s WHERE %s",
			strmangle.SetParamNames("\"", "\"", 1, wl),
			strmangle.WhereClause("\"", "\"", len(wl)+1, variantPrimaryKeyColumns),
		)
		cache.valueMapping, err = queries.BindMapping(variantType, variantMapping, append(wl, variantPrimaryKeyColumns...))
		if err != nil {
			return 0, err
		}
	}

	values := queries.ValuesFromMapping(reflect.Indirect(reflect.ValueOf(o)), cache.valueMapping)

	if boil.IsDebug(ctx) {
		writer := boil.DebugWriterFrom(ctx)
		fmt.Fprintln(writer, cache.query)
		fmt.Fprintln(writer, values)
	}
	var result sql.Result
	result, err = exec.ExecContext(ctx, cache.query, values...)
	if err != nil {
		return 0, errors.Wrap(err, "experiments: unable to update variants row")
	}

	rowsAff, err := result.RowsAffected()
	if err != nil {
		return 0, errors.Wrap(err, "experiments: failed to get rows affected by update for variants")
	}

	if !cached {
		variantUpdateCacheMut.Lock()
		variantUpdateCache[key] = cache
		variantUpdateCacheMut.Unlock()
	}

	return rowsAff, o.doAfterUpdateHooks(ctx, exec)
}

// UpdateAll updates all rows with the specified column values.
func (q variantQuery) UpdateAll(ctx context.Context, exec boil.ContextExecutor, cols M) (int64, error) {
	queries.SetUpdate(q.Query, cols)

	result, err := q.Query.ExecContext(ctx, exec)
	if err != nil {
		return 0, errors.Wrap(err, "experiments: unable to update all for variants")
	}

	rowsAff, err := result.RowsAffected()
	if err != nil {
		return 0, errors.Wrap(err, "experiments: unable to retrieve rows affected for variants")
	}

	return rowsAff, nil
}

// UpdateAll updates all rows with the specified column values, using an executor.
func (o VariantSlice) UpdateAll(ctx context.Context, exec boil.ContextExecutor, cols M) (int64, error) {
	ln := int64(len(o))
	if ln == 0 {
		return 0, nil
	}

	if len(cols) == 0 {
		return 0, errors.New("experiments: update all requires at least one column argument")
	}

	colNames := make([]string, len(cols))
	args := make([]interface{}, len(cols))

	i := 0
	for name, value := range cols {
		colNames[i] = name
		args[i] = value
		i++
	}

	// Append all of the primary key values for each column
	for _, obj := range o {
		pkeyArgs := queries.ValuesFromMapping(reflect.Indirect(reflect.ValueOf(obj)), variantPrimaryKeyMapping)
		args = append(args, pkeyArgs...)
	}

	sql := fmt.Sprintf("UPDATE \"experiments\".\"variants\" SET %s WHERE %s",
		strmangle.SetParamNames("\"", "\"", 1, colNames),
		strmangle.WhereClauseRepeated(string(dialect.LQ), string(dialect.RQ), len(colNames)+1, variantPrimaryKeyColumns, len(o)))

	if boil.IsDebug(ctx) {
		writer := boil.DebugWriterFrom(ctx)
		fmt.Fprintln(writer, sql)
		fmt.Fprintln(writer, args...)
	}
	result, err := exec.ExecContext(ctx, sql, args...)
	if err != nil {
		return 0, errors.Wrap(err, "experiments: unable to update all in variant slice")
	}

	rowsAff, err := result.RowsAffected()
	if err != nil {
		return 0, errors.Wrap(err, "experiments: unable to retrieve rows affected all in update all variant")
	}
	return rowsAff, nil
}

// Upsert attempts an insert using an executor, and does an update or ignore on conflict.
// See boil.Columns documentation for how to properly use updateColumns and insertColumns.
func (o *Variant) Upsert(ctx context.Context, exec boil.ContextExecutor, updateOnConflict bool, conflictColumns []string, updateColumns, insertColumns boil.Columns) error {
	if o == nil {
		return errors.New("experiments: no variants provided for upsert")
	}
	if !boil.TimestampsAreSkipped(ctx) {
		currTime := time.Now().In(boil.GetLocation())

		if o.CreatedAt.IsZero() {
			o.CreatedAt = currTime
		}
		o.UpdatedAt = currTime
	}

	if err := o.doBeforeUpsertHooks(ctx, exec); err != nil {
		return err
	}

	nzDefaults := queries.NonZeroDefaultSet(variantColumnsWithDefault, o)

	// Build cache key in-line uglily - mysql vs psql problems
	buf := strmangle.GetBuffer()
	if updateOnConflict {
		buf.WriteByte('t')
	} else {
		buf.WriteByte('f')
	}
	buf.WriteByte('.')
	for _, c := range conflictColumns {
		buf.WriteString(c)
	}
	buf.WriteByte('.')
	buf.WriteString(strconv.Itoa(updateColumns.Kind))
	for _, c := range updateColumns.Cols {
		buf.WriteString(c)
	}
	buf.WriteByte('.')
	buf.WriteString(strconv.Itoa(insertColumns.Kind))
	for _, c := range insertColumns.Cols {
		buf.WriteString(c)
	}
	buf.WriteByte('.')
	for _, c := range nzDefaults {
		buf.WriteString(c)
	}
	key := buf.String()
	strmangle.PutBuffer(buf)

	variantUpsertCacheMut.RLock()
	cache, cached := variantUpsertCache[key]
	variantUpsertCacheMut.RUnlock()

	var err error

	if !cached {
		insert, ret := insertColumns.InsertColumnSet(
			variantAllColumns,
			variantColumnsWithDefault,
			variantColumnsWithoutDefault,
			nzDefaults,
		)

		update := updateColumns.UpdateColumnSet(
			variantAllColumns,
			variantPrimaryKeyColumns,
		)

		if updateOnConflict && len(update) == 0 {
			return errors.New("experiments: unable to upsert variants, could not build update column list")
		}

		conflict := conflictColumns
		if len(conflict) == 0 {
			conflict = make([]string, len(variantPrimaryKeyColumns))
			copy(conflict, variantPrimaryKeyColumns)
		}
		cache.query = buildUpsertQueryPostgres(dialect, "\"experiments\".\"variants\"", updateOnConflict, ret, update, conflict, insert)

		cache.valueMapping, err = queries.BindMapping(variantType, variantMapping, insert)
		if err != nil {
			return err
		}
		if len(ret) != 0 {
			cache.retMapping, err = queries.BindMapping(variantType, variantMapping, ret)
			if err != nil {
				return err
			}
		}
	}

	value := reflect.Indirect(reflect.ValueOf(o))
	vals := queries.ValuesFromMapping(value, cache.valueMapping)
	var returns []interface{}
	if len(cache.retMapping) != 0 {
		returns = queries.PtrsFromMapping(value, cache.retMapping)
	}

	if boil.IsDebug(ctx) {
		writer := boil.DebugWriterFrom(ctx)
		fmt.Fprintln(writer, cache.query)
		fmt.Fprintln(writer, vals)
	}
	if len(cache.retMapping) != 0 {
		err = exec.QueryRowContext(ctx, cache.query, vals...).Scan(returns...)
		if errors.Is(err, sql.ErrNoRows) {
			err = nil // Postgres doesn't return anything when there's no update
		}
	} else {
		_, err = exec.ExecContext(ctx, cache.query, vals...)
	}
	if err != nil {
		return errors.Wrap(err, "experiments: unable to upsert variants")
	}

	if !cached {
		variantUpsertCacheMut.Lock()
		variantUpsertCache[key] = cache
		variantUpsertCacheMut.Unlock()
	}

	return o.doAfterUpsertHooks(ctx, exec)
}

// Delete deletes a single Variant record with an executor.
// Delete will match against the primary key column to find the record to delete.
func (o *Variant) Delete(ctx context.Context, exec boil.ContextExecutor) (int64, error) {
	if o == nil {
		return 0, errors.New("experiments: no Variant provided for delete")
	}

	if err := o.doBeforeDeleteHooks(ctx, exec); err != nil {
		return 0, err
	}

	args := queries.ValuesFromMapping(reflect.Indirect(reflect.ValueOf(o)), variantPrimaryKeyMapping)
	sql := "DELETE FROM \"experiments\".\"variants\" WHERE \"id\"=$1"

	if boil.IsDebug(ctx) {
		writer := boil.DebugWriterFrom(ctx)
		fmt.Fprintln(writer, sql)
		fmt.Fprintln(writer, args...)
	}
	result, err := exec.ExecContext(ctx, sql, args...)
	if err != nil {
		return 0, errors.Wrap(err, "experiments: unable to delete from variants")
	}

	rowsAff, err := result.RowsAffected()
	if err != nil {
		return 0, errors.Wrap(err, "experiments: failed to get rows affected by delete for variants")
	}

	if err := o.doAfterDeleteHooks(ctx, exec); err != nil {
		return 0, err
	}

	return rowsAff, nil
}

// DeleteAll deletes all matching rows.
func (q variantQuery) DeleteAll(ctx context.Context, exec boil.ContextExecutor) (int64, error) {
	if q.Query == nil {
		return 0, errors.New("experiments: no variantQuery provided for delete all")
	}

	queries.SetDelete(q.Query)

	result, err := q.Query.ExecContext(ctx, exec)
	if err != nil {
		return 0, errors.Wrap(err, "experiments: unable to delete all from variants")
	}

	rowsAff, err := result.RowsAffected()
	if err != nil {
		return 0, errors.Wrap(err, "experiments: failed to get rows affected by deleteall for variants")
	}

	return rowsAff, nil
}

// DeleteAll deletes all rows in the slice, using an executor.
func (o VariantSlice) DeleteAll(ctx context.Context, exec boil.ContextExecutor) (int64, error) {
	if len(o) == 0 {
		return 0, nil
	}

	if len(variantBeforeDeleteHooks) != 0 {
		for _, obj := range o {
			if err := obj.doBeforeDeleteHooks(ctx, exec); err != nil {
				return 0, err
			}
		}
	}

	var args []interface{}
	for _, obj := range o {
		pkeyArgs := queries.ValuesFromMapping(reflect.Indirect(reflect.ValueOf(obj)), variantPrimaryKeyMapping)
		args = append(args, pkeyArgs...)
	}

	sql := "DELETE FROM \"experiments\".\"variants\" WHERE " +
		strmangle.WhereClauseRepeated(string(dialect.LQ), string(dialect.RQ), 1, variantPrimaryKeyColumns, len(o))

	if boil.IsDebug(ctx) {
		writer := boil.DebugWriterFrom(ctx)
		fmt.Fprintln(writer, sql)
		fmt.Fprintln(writer, args)
	}
	result, err := exec.ExecContext(ctx, sql, args...)
	if err != nil {
		return 0, errors.Wrap(err, "experiments: unable to delete all from variant slice")
	}

	rowsAff, err := result.RowsAffected()
	if err != nil {
		return 0, errors.Wrap(err, "experiments: failed to get rows affected by deleteall for variants")
	}

	if len(variantAfterDeleteHooks) != 0 {
		for _, obj := range o {
			if err := obj.doAfterDeleteHooks(ctx, exec); err != nil {
				return 0, err
			}
		}
	}

	return rowsAff, nil
}

// Reload refetches the object from the database
// using the primary keys with an executor.
func (o *Variant) Reload(ctx context.Context, exec boil.ContextExecutor) error {
	ret, err := FindVariant(ctx, exec, o.ID)
	if err != nil {
		return err
	}

	*o = *ret
	return nil
}

// ReloadAll refetches every row with matching primary key column values
// and overwrites the original object slice with the newly updated slice.
func (o *VariantSlice) ReloadAll(ctx context.Context, exec boil.ContextExecutor) error {
	if o == nil || len(*o) == 0 {
		return nil
	}

	slice := VariantSlice{}
	var args []interface{}
	for _, obj := range *o {
		pkeyArgs := queries.ValuesFromMapping(reflect.Indirect(reflect.ValueOf(obj)), variantPrimaryKeyMapping)
		args = append(args, pkeyArgs...)
	}

	sql := "SELECT \"experiments\".\"variants\".* FROM \"experiments\".\"variants\" WHERE " +
		strmangle.WhereClauseRepeated(string(dialect.LQ), string(dialect.RQ), 1, variantPrimaryKeyColumns, len(*o))

	q := queries.Raw(sql, args...)

	err := q.Bind(ctx, exec, &slice)
	if err != nil {
		return errors.Wrap(err, "experiments: unable to reload all in VariantSlice")
	}

	*o = slice

	return nil
}

// VariantExists checks if the Variant row exists.
func VariantExists(ctx context.Context, exec boil.ContextExecutor, iD string) (bool, error) {
	var exists bool
	sql := "select exists(select 1 from \"experiments\".\"variants\" where \"id\"=$1 limit 1)"

	if boil.IsDebug(ctx) {
		writer := boil.DebugWriterFrom(ctx)
		fmt.Fprintln(writer, sql)
		fmt.Fprintln(writer, iD)
	}
	row := exec.QueryRowContext(ctx, sql, iD)

	err := row.Scan(&exists)
	if err != nil {
		return false, errors.Wrap(err, "experiments: unable to check if variants exists")
	}

	return exists, nil
}
