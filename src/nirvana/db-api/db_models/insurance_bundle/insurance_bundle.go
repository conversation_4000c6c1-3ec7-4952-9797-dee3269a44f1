// Code generated by SQLBoiler (https://github.com/volatiletech/sqlboiler). DO NOT EDIT.
// This file is meant to be re-generated in place and/or deleted at any time.

package insurance_bundle

import (
	"context"
	"database/sql"
	"fmt"
	"reflect"
	"strconv"
	"strings"
	"sync"
	"time"

	"github.com/friendsofgo/errors"
	"github.com/volatiletech/sqlboiler/v4/boil"
	"github.com/volatiletech/sqlboiler/v4/queries"
	"github.com/volatiletech/sqlboiler/v4/queries/qm"
	"github.com/volatiletech/sqlboiler/v4/queries/qmhelper"
	"github.com/volatiletech/sqlboiler/v4/types"
	"github.com/volatiletech/strmangle"
)

// InsuranceBundle is an object representing the database table.
type InsuranceBundle struct {
	ExternalID                    string     `boil:"external_id" json:"external_id" toml:"external_id" yaml:"external_id"`
	InternalID                    string     `boil:"internal_id" json:"internal_id" toml:"internal_id" yaml:"internal_id"`
	Version                       int64      `boil:"version" json:"version" toml:"version" yaml:"version"`
	DefaultCarrier                string     `boil:"default_carrier" json:"default_carrier" toml:"default_carrier" yaml:"default_carrier"`
	DefaultSellerType             string     `boil:"default_seller_type" json:"default_seller_type" toml:"default_seller_type" yaml:"default_seller_type"`
	DefaultSellerID               string     `boil:"default_seller_id" json:"default_seller_id" toml:"default_seller_id" yaml:"default_seller_id"`
	DefaultEffectiveDurationStart time.Time  `boil:"default_effective_duration_start" json:"default_effective_duration_start" toml:"default_effective_duration_start" yaml:"default_effective_duration_start"`
	DefaultEffectiveDurationEnd   time.Time  `boil:"default_effective_duration_end" json:"default_effective_duration_end" toml:"default_effective_duration_end" yaml:"default_effective_duration_end"`
	ProgramType                   string     `boil:"program_type" json:"program_type" toml:"program_type" yaml:"program_type"`
	Metadata                      types.JSON `boil:"metadata" json:"metadata" toml:"metadata" yaml:"metadata"`
	FormInfo                      types.JSON `boil:"form_info" json:"form_info" toml:"form_info" yaml:"form_info"`
	CreatedAt                     time.Time  `boil:"created_at" json:"created_at" toml:"created_at" yaml:"created_at"`
	UpdatedAt                     time.Time  `boil:"updated_at" json:"updated_at" toml:"updated_at" yaml:"updated_at"`
	State                         string     `boil:"state" json:"state" toml:"state" yaml:"state"`
	CarrierAdmittedType           string     `boil:"carrier_admitted_type" json:"carrier_admitted_type" toml:"carrier_admitted_type" yaml:"carrier_admitted_type"`

	R *insuranceBundleR `boil:"" json:"" toml:"" yaml:""`
	L insuranceBundleL  `boil:"-" json:"-" toml:"-" yaml:"-"`
}

var InsuranceBundleColumns = struct {
	ExternalID                    string
	InternalID                    string
	Version                       string
	DefaultCarrier                string
	DefaultSellerType             string
	DefaultSellerID               string
	DefaultEffectiveDurationStart string
	DefaultEffectiveDurationEnd   string
	ProgramType                   string
	Metadata                      string
	FormInfo                      string
	CreatedAt                     string
	UpdatedAt                     string
	State                         string
	CarrierAdmittedType           string
}{
	ExternalID:                    "external_id",
	InternalID:                    "internal_id",
	Version:                       "version",
	DefaultCarrier:                "default_carrier",
	DefaultSellerType:             "default_seller_type",
	DefaultSellerID:               "default_seller_id",
	DefaultEffectiveDurationStart: "default_effective_duration_start",
	DefaultEffectiveDurationEnd:   "default_effective_duration_end",
	ProgramType:                   "program_type",
	Metadata:                      "metadata",
	FormInfo:                      "form_info",
	CreatedAt:                     "created_at",
	UpdatedAt:                     "updated_at",
	State:                         "state",
	CarrierAdmittedType:           "carrier_admitted_type",
}

var InsuranceBundleTableColumns = struct {
	ExternalID                    string
	InternalID                    string
	Version                       string
	DefaultCarrier                string
	DefaultSellerType             string
	DefaultSellerID               string
	DefaultEffectiveDurationStart string
	DefaultEffectiveDurationEnd   string
	ProgramType                   string
	Metadata                      string
	FormInfo                      string
	CreatedAt                     string
	UpdatedAt                     string
	State                         string
	CarrierAdmittedType           string
}{
	ExternalID:                    "insurance_bundle.external_id",
	InternalID:                    "insurance_bundle.internal_id",
	Version:                       "insurance_bundle.version",
	DefaultCarrier:                "insurance_bundle.default_carrier",
	DefaultSellerType:             "insurance_bundle.default_seller_type",
	DefaultSellerID:               "insurance_bundle.default_seller_id",
	DefaultEffectiveDurationStart: "insurance_bundle.default_effective_duration_start",
	DefaultEffectiveDurationEnd:   "insurance_bundle.default_effective_duration_end",
	ProgramType:                   "insurance_bundle.program_type",
	Metadata:                      "insurance_bundle.metadata",
	FormInfo:                      "insurance_bundle.form_info",
	CreatedAt:                     "insurance_bundle.created_at",
	UpdatedAt:                     "insurance_bundle.updated_at",
	State:                         "insurance_bundle.state",
	CarrierAdmittedType:           "insurance_bundle.carrier_admitted_type",
}

// Generated where

type whereHelperint64 struct{ field string }

func (w whereHelperint64) EQ(x int64) qm.QueryMod  { return qmhelper.Where(w.field, qmhelper.EQ, x) }
func (w whereHelperint64) NEQ(x int64) qm.QueryMod { return qmhelper.Where(w.field, qmhelper.NEQ, x) }
func (w whereHelperint64) LT(x int64) qm.QueryMod  { return qmhelper.Where(w.field, qmhelper.LT, x) }
func (w whereHelperint64) LTE(x int64) qm.QueryMod { return qmhelper.Where(w.field, qmhelper.LTE, x) }
func (w whereHelperint64) GT(x int64) qm.QueryMod  { return qmhelper.Where(w.field, qmhelper.GT, x) }
func (w whereHelperint64) GTE(x int64) qm.QueryMod { return qmhelper.Where(w.field, qmhelper.GTE, x) }
func (w whereHelperint64) IN(slice []int64) qm.QueryMod {
	values := make([]interface{}, 0, len(slice))
	for _, value := range slice {
		values = append(values, value)
	}
	return qm.WhereIn(fmt.Sprintf("%s IN ?", w.field), values...)
}
func (w whereHelperint64) NIN(slice []int64) qm.QueryMod {
	values := make([]interface{}, 0, len(slice))
	for _, value := range slice {
		values = append(values, value)
	}
	return qm.WhereNotIn(fmt.Sprintf("%s NOT IN ?", w.field), values...)
}

type whereHelpertime_Time struct{ field string }

func (w whereHelpertime_Time) EQ(x time.Time) qm.QueryMod {
	return qmhelper.Where(w.field, qmhelper.EQ, x)
}
func (w whereHelpertime_Time) NEQ(x time.Time) qm.QueryMod {
	return qmhelper.Where(w.field, qmhelper.NEQ, x)
}
func (w whereHelpertime_Time) LT(x time.Time) qm.QueryMod {
	return qmhelper.Where(w.field, qmhelper.LT, x)
}
func (w whereHelpertime_Time) LTE(x time.Time) qm.QueryMod {
	return qmhelper.Where(w.field, qmhelper.LTE, x)
}
func (w whereHelpertime_Time) GT(x time.Time) qm.QueryMod {
	return qmhelper.Where(w.field, qmhelper.GT, x)
}
func (w whereHelpertime_Time) GTE(x time.Time) qm.QueryMod {
	return qmhelper.Where(w.field, qmhelper.GTE, x)
}

var InsuranceBundleWhere = struct {
	ExternalID                    whereHelperstring
	InternalID                    whereHelperstring
	Version                       whereHelperint64
	DefaultCarrier                whereHelperstring
	DefaultSellerType             whereHelperstring
	DefaultSellerID               whereHelperstring
	DefaultEffectiveDurationStart whereHelpertime_Time
	DefaultEffectiveDurationEnd   whereHelpertime_Time
	ProgramType                   whereHelperstring
	Metadata                      whereHelpertypes_JSON
	FormInfo                      whereHelpertypes_JSON
	CreatedAt                     whereHelpertime_Time
	UpdatedAt                     whereHelpertime_Time
	State                         whereHelperstring
	CarrierAdmittedType           whereHelperstring
}{
	ExternalID:                    whereHelperstring{field: "\"insurance_bundle\".\"insurance_bundle\".\"external_id\""},
	InternalID:                    whereHelperstring{field: "\"insurance_bundle\".\"insurance_bundle\".\"internal_id\""},
	Version:                       whereHelperint64{field: "\"insurance_bundle\".\"insurance_bundle\".\"version\""},
	DefaultCarrier:                whereHelperstring{field: "\"insurance_bundle\".\"insurance_bundle\".\"default_carrier\""},
	DefaultSellerType:             whereHelperstring{field: "\"insurance_bundle\".\"insurance_bundle\".\"default_seller_type\""},
	DefaultSellerID:               whereHelperstring{field: "\"insurance_bundle\".\"insurance_bundle\".\"default_seller_id\""},
	DefaultEffectiveDurationStart: whereHelpertime_Time{field: "\"insurance_bundle\".\"insurance_bundle\".\"default_effective_duration_start\""},
	DefaultEffectiveDurationEnd:   whereHelpertime_Time{field: "\"insurance_bundle\".\"insurance_bundle\".\"default_effective_duration_end\""},
	ProgramType:                   whereHelperstring{field: "\"insurance_bundle\".\"insurance_bundle\".\"program_type\""},
	Metadata:                      whereHelpertypes_JSON{field: "\"insurance_bundle\".\"insurance_bundle\".\"metadata\""},
	FormInfo:                      whereHelpertypes_JSON{field: "\"insurance_bundle\".\"insurance_bundle\".\"form_info\""},
	CreatedAt:                     whereHelpertime_Time{field: "\"insurance_bundle\".\"insurance_bundle\".\"created_at\""},
	UpdatedAt:                     whereHelpertime_Time{field: "\"insurance_bundle\".\"insurance_bundle\".\"updated_at\""},
	State:                         whereHelperstring{field: "\"insurance_bundle\".\"insurance_bundle\".\"state\""},
	CarrierAdmittedType:           whereHelperstring{field: "\"insurance_bundle\".\"insurance_bundle\".\"carrier_admitted_type\""},
}

// InsuranceBundleRels is where relationship names are stored.
var InsuranceBundleRels = struct {
	ExecuteEndorsementRequests string
	InsuranceBundleSegments    string
}{
	ExecuteEndorsementRequests: "ExecuteEndorsementRequests",
	InsuranceBundleSegments:    "InsuranceBundleSegments",
}

// insuranceBundleR is where relationships are stored.
type insuranceBundleR struct {
	ExecuteEndorsementRequests ExecuteEndorsementRequestSlice `boil:"ExecuteEndorsementRequests" json:"ExecuteEndorsementRequests" toml:"ExecuteEndorsementRequests" yaml:"ExecuteEndorsementRequests"`
	InsuranceBundleSegments    InsuranceBundleSegmentSlice    `boil:"InsuranceBundleSegments" json:"InsuranceBundleSegments" toml:"InsuranceBundleSegments" yaml:"InsuranceBundleSegments"`
}

// NewStruct creates a new relationship struct
func (*insuranceBundleR) NewStruct() *insuranceBundleR {
	return &insuranceBundleR{}
}

// insuranceBundleL is where Load methods for each relationship are stored.
type insuranceBundleL struct{}

var (
	insuranceBundleAllColumns            = []string{"external_id", "internal_id", "version", "default_carrier", "default_seller_type", "default_seller_id", "default_effective_duration_start", "default_effective_duration_end", "program_type", "metadata", "form_info", "created_at", "updated_at", "state", "carrier_admitted_type"}
	insuranceBundleColumnsWithoutDefault = []string{"external_id", "internal_id", "version", "default_carrier", "default_seller_type", "default_seller_id", "default_effective_duration_start", "default_effective_duration_end", "program_type", "metadata", "form_info", "created_at", "updated_at", "state"}
	insuranceBundleColumnsWithDefault    = []string{"carrier_admitted_type"}
	insuranceBundlePrimaryKeyColumns     = []string{"internal_id"}
	insuranceBundleGeneratedColumns      = []string{}
)

type (
	// InsuranceBundleSlice is an alias for a slice of pointers to InsuranceBundle.
	// This should almost always be used instead of []InsuranceBundle.
	InsuranceBundleSlice []*InsuranceBundle
	// InsuranceBundleHook is the signature for custom InsuranceBundle hook methods
	InsuranceBundleHook func(context.Context, boil.ContextExecutor, *InsuranceBundle) error

	insuranceBundleQuery struct {
		*queries.Query
	}
)

// Cache for insert, update and upsert
var (
	insuranceBundleType                 = reflect.TypeOf(&InsuranceBundle{})
	insuranceBundleMapping              = queries.MakeStructMapping(insuranceBundleType)
	insuranceBundlePrimaryKeyMapping, _ = queries.BindMapping(insuranceBundleType, insuranceBundleMapping, insuranceBundlePrimaryKeyColumns)
	insuranceBundleInsertCacheMut       sync.RWMutex
	insuranceBundleInsertCache          = make(map[string]insertCache)
	insuranceBundleUpdateCacheMut       sync.RWMutex
	insuranceBundleUpdateCache          = make(map[string]updateCache)
	insuranceBundleUpsertCacheMut       sync.RWMutex
	insuranceBundleUpsertCache          = make(map[string]insertCache)
)

var (
	// Force time package dependency for automated UpdatedAt/CreatedAt.
	_ = time.Second
	// Force qmhelper dependency for where clause generation (which doesn't
	// always happen)
	_ = qmhelper.Where
)

var insuranceBundleAfterSelectHooks []InsuranceBundleHook

var insuranceBundleBeforeInsertHooks []InsuranceBundleHook
var insuranceBundleAfterInsertHooks []InsuranceBundleHook

var insuranceBundleBeforeUpdateHooks []InsuranceBundleHook
var insuranceBundleAfterUpdateHooks []InsuranceBundleHook

var insuranceBundleBeforeDeleteHooks []InsuranceBundleHook
var insuranceBundleAfterDeleteHooks []InsuranceBundleHook

var insuranceBundleBeforeUpsertHooks []InsuranceBundleHook
var insuranceBundleAfterUpsertHooks []InsuranceBundleHook

// doAfterSelectHooks executes all "after Select" hooks.
func (o *InsuranceBundle) doAfterSelectHooks(ctx context.Context, exec boil.ContextExecutor) (err error) {
	if boil.HooksAreSkipped(ctx) {
		return nil
	}

	for _, hook := range insuranceBundleAfterSelectHooks {
		if err := hook(ctx, exec, o); err != nil {
			return err
		}
	}

	return nil
}

// doBeforeInsertHooks executes all "before insert" hooks.
func (o *InsuranceBundle) doBeforeInsertHooks(ctx context.Context, exec boil.ContextExecutor) (err error) {
	if boil.HooksAreSkipped(ctx) {
		return nil
	}

	for _, hook := range insuranceBundleBeforeInsertHooks {
		if err := hook(ctx, exec, o); err != nil {
			return err
		}
	}

	return nil
}

// doAfterInsertHooks executes all "after Insert" hooks.
func (o *InsuranceBundle) doAfterInsertHooks(ctx context.Context, exec boil.ContextExecutor) (err error) {
	if boil.HooksAreSkipped(ctx) {
		return nil
	}

	for _, hook := range insuranceBundleAfterInsertHooks {
		if err := hook(ctx, exec, o); err != nil {
			return err
		}
	}

	return nil
}

// doBeforeUpdateHooks executes all "before Update" hooks.
func (o *InsuranceBundle) doBeforeUpdateHooks(ctx context.Context, exec boil.ContextExecutor) (err error) {
	if boil.HooksAreSkipped(ctx) {
		return nil
	}

	for _, hook := range insuranceBundleBeforeUpdateHooks {
		if err := hook(ctx, exec, o); err != nil {
			return err
		}
	}

	return nil
}

// doAfterUpdateHooks executes all "after Update" hooks.
func (o *InsuranceBundle) doAfterUpdateHooks(ctx context.Context, exec boil.ContextExecutor) (err error) {
	if boil.HooksAreSkipped(ctx) {
		return nil
	}

	for _, hook := range insuranceBundleAfterUpdateHooks {
		if err := hook(ctx, exec, o); err != nil {
			return err
		}
	}

	return nil
}

// doBeforeDeleteHooks executes all "before Delete" hooks.
func (o *InsuranceBundle) doBeforeDeleteHooks(ctx context.Context, exec boil.ContextExecutor) (err error) {
	if boil.HooksAreSkipped(ctx) {
		return nil
	}

	for _, hook := range insuranceBundleBeforeDeleteHooks {
		if err := hook(ctx, exec, o); err != nil {
			return err
		}
	}

	return nil
}

// doAfterDeleteHooks executes all "after Delete" hooks.
func (o *InsuranceBundle) doAfterDeleteHooks(ctx context.Context, exec boil.ContextExecutor) (err error) {
	if boil.HooksAreSkipped(ctx) {
		return nil
	}

	for _, hook := range insuranceBundleAfterDeleteHooks {
		if err := hook(ctx, exec, o); err != nil {
			return err
		}
	}

	return nil
}

// doBeforeUpsertHooks executes all "before Upsert" hooks.
func (o *InsuranceBundle) doBeforeUpsertHooks(ctx context.Context, exec boil.ContextExecutor) (err error) {
	if boil.HooksAreSkipped(ctx) {
		return nil
	}

	for _, hook := range insuranceBundleBeforeUpsertHooks {
		if err := hook(ctx, exec, o); err != nil {
			return err
		}
	}

	return nil
}

// doAfterUpsertHooks executes all "after Upsert" hooks.
func (o *InsuranceBundle) doAfterUpsertHooks(ctx context.Context, exec boil.ContextExecutor) (err error) {
	if boil.HooksAreSkipped(ctx) {
		return nil
	}

	for _, hook := range insuranceBundleAfterUpsertHooks {
		if err := hook(ctx, exec, o); err != nil {
			return err
		}
	}

	return nil
}

// AddInsuranceBundleHook registers your hook function for all future operations.
func AddInsuranceBundleHook(hookPoint boil.HookPoint, insuranceBundleHook InsuranceBundleHook) {
	switch hookPoint {
	case boil.AfterSelectHook:
		insuranceBundleAfterSelectHooks = append(insuranceBundleAfterSelectHooks, insuranceBundleHook)
	case boil.BeforeInsertHook:
		insuranceBundleBeforeInsertHooks = append(insuranceBundleBeforeInsertHooks, insuranceBundleHook)
	case boil.AfterInsertHook:
		insuranceBundleAfterInsertHooks = append(insuranceBundleAfterInsertHooks, insuranceBundleHook)
	case boil.BeforeUpdateHook:
		insuranceBundleBeforeUpdateHooks = append(insuranceBundleBeforeUpdateHooks, insuranceBundleHook)
	case boil.AfterUpdateHook:
		insuranceBundleAfterUpdateHooks = append(insuranceBundleAfterUpdateHooks, insuranceBundleHook)
	case boil.BeforeDeleteHook:
		insuranceBundleBeforeDeleteHooks = append(insuranceBundleBeforeDeleteHooks, insuranceBundleHook)
	case boil.AfterDeleteHook:
		insuranceBundleAfterDeleteHooks = append(insuranceBundleAfterDeleteHooks, insuranceBundleHook)
	case boil.BeforeUpsertHook:
		insuranceBundleBeforeUpsertHooks = append(insuranceBundleBeforeUpsertHooks, insuranceBundleHook)
	case boil.AfterUpsertHook:
		insuranceBundleAfterUpsertHooks = append(insuranceBundleAfterUpsertHooks, insuranceBundleHook)
	}
}

// One returns a single insuranceBundle record from the query.
func (q insuranceBundleQuery) One(ctx context.Context, exec boil.ContextExecutor) (*InsuranceBundle, error) {
	o := &InsuranceBundle{}

	queries.SetLimit(q.Query, 1)

	err := q.Bind(ctx, exec, o)
	if err != nil {
		if errors.Is(err, sql.ErrNoRows) {
			return nil, sql.ErrNoRows
		}
		return nil, errors.Wrap(err, "insurance_bundle: failed to execute a one query for insurance_bundle")
	}

	if err := o.doAfterSelectHooks(ctx, exec); err != nil {
		return o, err
	}

	return o, nil
}

// All returns all InsuranceBundle records from the query.
func (q insuranceBundleQuery) All(ctx context.Context, exec boil.ContextExecutor) (InsuranceBundleSlice, error) {
	var o []*InsuranceBundle

	err := q.Bind(ctx, exec, &o)
	if err != nil {
		return nil, errors.Wrap(err, "insurance_bundle: failed to assign all query results to InsuranceBundle slice")
	}

	if len(insuranceBundleAfterSelectHooks) != 0 {
		for _, obj := range o {
			if err := obj.doAfterSelectHooks(ctx, exec); err != nil {
				return o, err
			}
		}
	}

	return o, nil
}

// Count returns the count of all InsuranceBundle records in the query.
func (q insuranceBundleQuery) Count(ctx context.Context, exec boil.ContextExecutor) (int64, error) {
	var count int64

	queries.SetSelect(q.Query, nil)
	queries.SetCount(q.Query)

	err := q.Query.QueryRowContext(ctx, exec).Scan(&count)
	if err != nil {
		return 0, errors.Wrap(err, "insurance_bundle: failed to count insurance_bundle rows")
	}

	return count, nil
}

// Exists checks if the row exists in the table.
func (q insuranceBundleQuery) Exists(ctx context.Context, exec boil.ContextExecutor) (bool, error) {
	var count int64

	queries.SetSelect(q.Query, nil)
	queries.SetCount(q.Query)
	queries.SetLimit(q.Query, 1)

	err := q.Query.QueryRowContext(ctx, exec).Scan(&count)
	if err != nil {
		return false, errors.Wrap(err, "insurance_bundle: failed to check if insurance_bundle exists")
	}

	return count > 0, nil
}

// ExecuteEndorsementRequests retrieves all the execute_endorsement_request's ExecuteEndorsementRequests with an executor.
func (o *InsuranceBundle) ExecuteEndorsementRequests(mods ...qm.QueryMod) executeEndorsementRequestQuery {
	var queryMods []qm.QueryMod
	if len(mods) != 0 {
		queryMods = append(queryMods, mods...)
	}

	queryMods = append(queryMods,
		qm.Where("\"insurance_bundle\".\"execute_endorsement_request\".\"insurance_bundle_id\"=?", o.InternalID),
	)

	return ExecuteEndorsementRequests(queryMods...)
}

// InsuranceBundleSegments retrieves all the insurance_bundle_segment's InsuranceBundleSegments with an executor.
func (o *InsuranceBundle) InsuranceBundleSegments(mods ...qm.QueryMod) insuranceBundleSegmentQuery {
	var queryMods []qm.QueryMod
	if len(mods) != 0 {
		queryMods = append(queryMods, mods...)
	}

	queryMods = append(queryMods,
		qm.Where("\"insurance_bundle\".\"insurance_bundle_segment\".\"insurance_bundle_id\"=?", o.InternalID),
	)

	return InsuranceBundleSegments(queryMods...)
}

// LoadExecuteEndorsementRequests allows an eager lookup of values, cached into the
// loaded structs of the objects. This is for a 1-M or N-M relationship.
func (insuranceBundleL) LoadExecuteEndorsementRequests(ctx context.Context, e boil.ContextExecutor, singular bool, maybeInsuranceBundle interface{}, mods queries.Applicator) error {
	var slice []*InsuranceBundle
	var object *InsuranceBundle

	if singular {
		object = maybeInsuranceBundle.(*InsuranceBundle)
	} else {
		slice = *maybeInsuranceBundle.(*[]*InsuranceBundle)
	}

	args := make([]interface{}, 0, 1)
	if singular {
		if object.R == nil {
			object.R = &insuranceBundleR{}
		}
		args = append(args, object.InternalID)
	} else {
	Outer:
		for _, obj := range slice {
			if obj.R == nil {
				obj.R = &insuranceBundleR{}
			}

			for _, a := range args {
				if a == obj.InternalID {
					continue Outer
				}
			}

			args = append(args, obj.InternalID)
		}
	}

	if len(args) == 0 {
		return nil
	}

	query := NewQuery(
		qm.From(`insurance_bundle.execute_endorsement_request`),
		qm.WhereIn(`insurance_bundle.execute_endorsement_request.insurance_bundle_id in ?`, args...),
	)
	if mods != nil {
		mods.Apply(query)
	}

	results, err := query.QueryContext(ctx, e)
	if err != nil {
		return errors.Wrap(err, "failed to eager load execute_endorsement_request")
	}

	var resultSlice []*ExecuteEndorsementRequest
	if err = queries.Bind(results, &resultSlice); err != nil {
		return errors.Wrap(err, "failed to bind eager loaded slice execute_endorsement_request")
	}

	if err = results.Close(); err != nil {
		return errors.Wrap(err, "failed to close results in eager load on execute_endorsement_request")
	}
	if err = results.Err(); err != nil {
		return errors.Wrap(err, "error occurred during iteration of eager loaded relations for execute_endorsement_request")
	}

	if len(executeEndorsementRequestAfterSelectHooks) != 0 {
		for _, obj := range resultSlice {
			if err := obj.doAfterSelectHooks(ctx, e); err != nil {
				return err
			}
		}
	}
	if singular {
		object.R.ExecuteEndorsementRequests = resultSlice
		for _, foreign := range resultSlice {
			if foreign.R == nil {
				foreign.R = &executeEndorsementRequestR{}
			}
			foreign.R.InsuranceBundle = object
		}
		return nil
	}

	for _, foreign := range resultSlice {
		for _, local := range slice {
			if local.InternalID == foreign.InsuranceBundleID {
				local.R.ExecuteEndorsementRequests = append(local.R.ExecuteEndorsementRequests, foreign)
				if foreign.R == nil {
					foreign.R = &executeEndorsementRequestR{}
				}
				foreign.R.InsuranceBundle = local
				break
			}
		}
	}

	return nil
}

// LoadInsuranceBundleSegments allows an eager lookup of values, cached into the
// loaded structs of the objects. This is for a 1-M or N-M relationship.
func (insuranceBundleL) LoadInsuranceBundleSegments(ctx context.Context, e boil.ContextExecutor, singular bool, maybeInsuranceBundle interface{}, mods queries.Applicator) error {
	var slice []*InsuranceBundle
	var object *InsuranceBundle

	if singular {
		object = maybeInsuranceBundle.(*InsuranceBundle)
	} else {
		slice = *maybeInsuranceBundle.(*[]*InsuranceBundle)
	}

	args := make([]interface{}, 0, 1)
	if singular {
		if object.R == nil {
			object.R = &insuranceBundleR{}
		}
		args = append(args, object.InternalID)
	} else {
	Outer:
		for _, obj := range slice {
			if obj.R == nil {
				obj.R = &insuranceBundleR{}
			}

			for _, a := range args {
				if a == obj.InternalID {
					continue Outer
				}
			}

			args = append(args, obj.InternalID)
		}
	}

	if len(args) == 0 {
		return nil
	}

	query := NewQuery(
		qm.From(`insurance_bundle.insurance_bundle_segment`),
		qm.WhereIn(`insurance_bundle.insurance_bundle_segment.insurance_bundle_id in ?`, args...),
	)
	if mods != nil {
		mods.Apply(query)
	}

	results, err := query.QueryContext(ctx, e)
	if err != nil {
		return errors.Wrap(err, "failed to eager load insurance_bundle_segment")
	}

	var resultSlice []*InsuranceBundleSegment
	if err = queries.Bind(results, &resultSlice); err != nil {
		return errors.Wrap(err, "failed to bind eager loaded slice insurance_bundle_segment")
	}

	if err = results.Close(); err != nil {
		return errors.Wrap(err, "failed to close results in eager load on insurance_bundle_segment")
	}
	if err = results.Err(); err != nil {
		return errors.Wrap(err, "error occurred during iteration of eager loaded relations for insurance_bundle_segment")
	}

	if len(insuranceBundleSegmentAfterSelectHooks) != 0 {
		for _, obj := range resultSlice {
			if err := obj.doAfterSelectHooks(ctx, e); err != nil {
				return err
			}
		}
	}
	if singular {
		object.R.InsuranceBundleSegments = resultSlice
		for _, foreign := range resultSlice {
			if foreign.R == nil {
				foreign.R = &insuranceBundleSegmentR{}
			}
			foreign.R.InsuranceBundle = object
		}
		return nil
	}

	for _, foreign := range resultSlice {
		for _, local := range slice {
			if local.InternalID == foreign.InsuranceBundleID {
				local.R.InsuranceBundleSegments = append(local.R.InsuranceBundleSegments, foreign)
				if foreign.R == nil {
					foreign.R = &insuranceBundleSegmentR{}
				}
				foreign.R.InsuranceBundle = local
				break
			}
		}
	}

	return nil
}

// AddExecuteEndorsementRequests adds the given related objects to the existing relationships
// of the insurance_bundle, optionally inserting them as new records.
// Appends related to o.R.ExecuteEndorsementRequests.
// Sets related.R.InsuranceBundle appropriately.
func (o *InsuranceBundle) AddExecuteEndorsementRequests(ctx context.Context, exec boil.ContextExecutor, insert bool, related ...*ExecuteEndorsementRequest) error {
	var err error
	for _, rel := range related {
		if insert {
			rel.InsuranceBundleID = o.InternalID
			if err = rel.Insert(ctx, exec, boil.Infer()); err != nil {
				return errors.Wrap(err, "failed to insert into foreign table")
			}
		} else {
			updateQuery := fmt.Sprintf(
				"UPDATE \"insurance_bundle\".\"execute_endorsement_request\" SET %s WHERE %s",
				strmangle.SetParamNames("\"", "\"", 1, []string{"insurance_bundle_id"}),
				strmangle.WhereClause("\"", "\"", 2, executeEndorsementRequestPrimaryKeyColumns),
			)
			values := []interface{}{o.InternalID, rel.ID}

			if boil.IsDebug(ctx) {
				writer := boil.DebugWriterFrom(ctx)
				fmt.Fprintln(writer, updateQuery)
				fmt.Fprintln(writer, values)
			}
			if _, err = exec.ExecContext(ctx, updateQuery, values...); err != nil {
				return errors.Wrap(err, "failed to update foreign table")
			}

			rel.InsuranceBundleID = o.InternalID
		}
	}

	if o.R == nil {
		o.R = &insuranceBundleR{
			ExecuteEndorsementRequests: related,
		}
	} else {
		o.R.ExecuteEndorsementRequests = append(o.R.ExecuteEndorsementRequests, related...)
	}

	for _, rel := range related {
		if rel.R == nil {
			rel.R = &executeEndorsementRequestR{
				InsuranceBundle: o,
			}
		} else {
			rel.R.InsuranceBundle = o
		}
	}
	return nil
}

// AddInsuranceBundleSegments adds the given related objects to the existing relationships
// of the insurance_bundle, optionally inserting them as new records.
// Appends related to o.R.InsuranceBundleSegments.
// Sets related.R.InsuranceBundle appropriately.
func (o *InsuranceBundle) AddInsuranceBundleSegments(ctx context.Context, exec boil.ContextExecutor, insert bool, related ...*InsuranceBundleSegment) error {
	var err error
	for _, rel := range related {
		if insert {
			rel.InsuranceBundleID = o.InternalID
			if err = rel.Insert(ctx, exec, boil.Infer()); err != nil {
				return errors.Wrap(err, "failed to insert into foreign table")
			}
		} else {
			updateQuery := fmt.Sprintf(
				"UPDATE \"insurance_bundle\".\"insurance_bundle_segment\" SET %s WHERE %s",
				strmangle.SetParamNames("\"", "\"", 1, []string{"insurance_bundle_id"}),
				strmangle.WhereClause("\"", "\"", 2, insuranceBundleSegmentPrimaryKeyColumns),
			)
			values := []interface{}{o.InternalID, rel.ID}

			if boil.IsDebug(ctx) {
				writer := boil.DebugWriterFrom(ctx)
				fmt.Fprintln(writer, updateQuery)
				fmt.Fprintln(writer, values)
			}
			if _, err = exec.ExecContext(ctx, updateQuery, values...); err != nil {
				return errors.Wrap(err, "failed to update foreign table")
			}

			rel.InsuranceBundleID = o.InternalID
		}
	}

	if o.R == nil {
		o.R = &insuranceBundleR{
			InsuranceBundleSegments: related,
		}
	} else {
		o.R.InsuranceBundleSegments = append(o.R.InsuranceBundleSegments, related...)
	}

	for _, rel := range related {
		if rel.R == nil {
			rel.R = &insuranceBundleSegmentR{
				InsuranceBundle: o,
			}
		} else {
			rel.R.InsuranceBundle = o
		}
	}
	return nil
}

// InsuranceBundles retrieves all the records using an executor.
func InsuranceBundles(mods ...qm.QueryMod) insuranceBundleQuery {
	mods = append(mods, qm.From("\"insurance_bundle\".\"insurance_bundle\""))
	q := NewQuery(mods...)
	if len(queries.GetSelect(q)) == 0 {
		queries.SetSelect(q, []string{"\"insurance_bundle\".\"insurance_bundle\".*"})
	}

	return insuranceBundleQuery{q}
}

// FindInsuranceBundle retrieves a single record by ID with an executor.
// If selectCols is empty Find will return all columns.
func FindInsuranceBundle(ctx context.Context, exec boil.ContextExecutor, internalID string, selectCols ...string) (*InsuranceBundle, error) {
	insuranceBundleObj := &InsuranceBundle{}

	sel := "*"
	if len(selectCols) > 0 {
		sel = strings.Join(strmangle.IdentQuoteSlice(dialect.LQ, dialect.RQ, selectCols), ",")
	}
	query := fmt.Sprintf(
		"select %s from \"insurance_bundle\".\"insurance_bundle\" where \"internal_id\"=$1", sel,
	)

	q := queries.Raw(query, internalID)

	err := q.Bind(ctx, exec, insuranceBundleObj)
	if err != nil {
		if errors.Is(err, sql.ErrNoRows) {
			return nil, sql.ErrNoRows
		}
		return nil, errors.Wrap(err, "insurance_bundle: unable to select from insurance_bundle")
	}

	if err = insuranceBundleObj.doAfterSelectHooks(ctx, exec); err != nil {
		return insuranceBundleObj, err
	}

	return insuranceBundleObj, nil
}

// Insert a single record using an executor.
// See boil.Columns.InsertColumnSet documentation to understand column list inference for inserts.
func (o *InsuranceBundle) Insert(ctx context.Context, exec boil.ContextExecutor, columns boil.Columns) error {
	if o == nil {
		return errors.New("insurance_bundle: no insurance_bundle provided for insertion")
	}

	var err error
	if !boil.TimestampsAreSkipped(ctx) {
		currTime := time.Now().In(boil.GetLocation())

		if o.CreatedAt.IsZero() {
			o.CreatedAt = currTime
		}
		if o.UpdatedAt.IsZero() {
			o.UpdatedAt = currTime
		}
	}

	if err := o.doBeforeInsertHooks(ctx, exec); err != nil {
		return err
	}

	nzDefaults := queries.NonZeroDefaultSet(insuranceBundleColumnsWithDefault, o)

	key := makeCacheKey(columns, nzDefaults)
	insuranceBundleInsertCacheMut.RLock()
	cache, cached := insuranceBundleInsertCache[key]
	insuranceBundleInsertCacheMut.RUnlock()

	if !cached {
		wl, returnColumns := columns.InsertColumnSet(
			insuranceBundleAllColumns,
			insuranceBundleColumnsWithDefault,
			insuranceBundleColumnsWithoutDefault,
			nzDefaults,
		)

		cache.valueMapping, err = queries.BindMapping(insuranceBundleType, insuranceBundleMapping, wl)
		if err != nil {
			return err
		}
		cache.retMapping, err = queries.BindMapping(insuranceBundleType, insuranceBundleMapping, returnColumns)
		if err != nil {
			return err
		}
		if len(wl) != 0 {
			cache.query = fmt.Sprintf("INSERT INTO \"insurance_bundle\".\"insurance_bundle\" (\"%s\") %%sVALUES (%s)%%s", strings.Join(wl, "\",\""), strmangle.Placeholders(dialect.UseIndexPlaceholders, len(wl), 1, 1))
		} else {
			cache.query = "INSERT INTO \"insurance_bundle\".\"insurance_bundle\" %sDEFAULT VALUES%s"
		}

		var queryOutput, queryReturning string

		if len(cache.retMapping) != 0 {
			queryReturning = fmt.Sprintf(" RETURNING \"%s\"", strings.Join(returnColumns, "\",\""))
		}

		cache.query = fmt.Sprintf(cache.query, queryOutput, queryReturning)
	}

	value := reflect.Indirect(reflect.ValueOf(o))
	vals := queries.ValuesFromMapping(value, cache.valueMapping)

	if boil.IsDebug(ctx) {
		writer := boil.DebugWriterFrom(ctx)
		fmt.Fprintln(writer, cache.query)
		fmt.Fprintln(writer, vals)
	}

	if len(cache.retMapping) != 0 {
		err = exec.QueryRowContext(ctx, cache.query, vals...).Scan(queries.PtrsFromMapping(value, cache.retMapping)...)
	} else {
		_, err = exec.ExecContext(ctx, cache.query, vals...)
	}

	if err != nil {
		return errors.Wrap(err, "insurance_bundle: unable to insert into insurance_bundle")
	}

	if !cached {
		insuranceBundleInsertCacheMut.Lock()
		insuranceBundleInsertCache[key] = cache
		insuranceBundleInsertCacheMut.Unlock()
	}

	return o.doAfterInsertHooks(ctx, exec)
}

// Update uses an executor to update the InsuranceBundle.
// See boil.Columns.UpdateColumnSet documentation to understand column list inference for updates.
// Update does not automatically update the record in case of default values. Use .Reload() to refresh the records.
func (o *InsuranceBundle) Update(ctx context.Context, exec boil.ContextExecutor, columns boil.Columns) (int64, error) {
	if !boil.TimestampsAreSkipped(ctx) {
		currTime := time.Now().In(boil.GetLocation())

		o.UpdatedAt = currTime
	}

	var err error
	if err = o.doBeforeUpdateHooks(ctx, exec); err != nil {
		return 0, err
	}
	key := makeCacheKey(columns, nil)
	insuranceBundleUpdateCacheMut.RLock()
	cache, cached := insuranceBundleUpdateCache[key]
	insuranceBundleUpdateCacheMut.RUnlock()

	if !cached {
		wl := columns.UpdateColumnSet(
			insuranceBundleAllColumns,
			insuranceBundlePrimaryKeyColumns,
		)

		if !columns.IsWhitelist() {
			wl = strmangle.SetComplement(wl, []string{"created_at"})
		}
		if len(wl) == 0 {
			return 0, errors.New("insurance_bundle: unable to update insurance_bundle, could not build whitelist")
		}

		cache.query = fmt.Sprintf("UPDATE \"insurance_bundle\".\"insurance_bundle\" SET %s WHERE %s",
			strmangle.SetParamNames("\"", "\"", 1, wl),
			strmangle.WhereClause("\"", "\"", len(wl)+1, insuranceBundlePrimaryKeyColumns),
		)
		cache.valueMapping, err = queries.BindMapping(insuranceBundleType, insuranceBundleMapping, append(wl, insuranceBundlePrimaryKeyColumns...))
		if err != nil {
			return 0, err
		}
	}

	values := queries.ValuesFromMapping(reflect.Indirect(reflect.ValueOf(o)), cache.valueMapping)

	if boil.IsDebug(ctx) {
		writer := boil.DebugWriterFrom(ctx)
		fmt.Fprintln(writer, cache.query)
		fmt.Fprintln(writer, values)
	}
	var result sql.Result
	result, err = exec.ExecContext(ctx, cache.query, values...)
	if err != nil {
		return 0, errors.Wrap(err, "insurance_bundle: unable to update insurance_bundle row")
	}

	rowsAff, err := result.RowsAffected()
	if err != nil {
		return 0, errors.Wrap(err, "insurance_bundle: failed to get rows affected by update for insurance_bundle")
	}

	if !cached {
		insuranceBundleUpdateCacheMut.Lock()
		insuranceBundleUpdateCache[key] = cache
		insuranceBundleUpdateCacheMut.Unlock()
	}

	return rowsAff, o.doAfterUpdateHooks(ctx, exec)
}

// UpdateAll updates all rows with the specified column values.
func (q insuranceBundleQuery) UpdateAll(ctx context.Context, exec boil.ContextExecutor, cols M) (int64, error) {
	queries.SetUpdate(q.Query, cols)

	result, err := q.Query.ExecContext(ctx, exec)
	if err != nil {
		return 0, errors.Wrap(err, "insurance_bundle: unable to update all for insurance_bundle")
	}

	rowsAff, err := result.RowsAffected()
	if err != nil {
		return 0, errors.Wrap(err, "insurance_bundle: unable to retrieve rows affected for insurance_bundle")
	}

	return rowsAff, nil
}

// UpdateAll updates all rows with the specified column values, using an executor.
func (o InsuranceBundleSlice) UpdateAll(ctx context.Context, exec boil.ContextExecutor, cols M) (int64, error) {
	ln := int64(len(o))
	if ln == 0 {
		return 0, nil
	}

	if len(cols) == 0 {
		return 0, errors.New("insurance_bundle: update all requires at least one column argument")
	}

	colNames := make([]string, len(cols))
	args := make([]interface{}, len(cols))

	i := 0
	for name, value := range cols {
		colNames[i] = name
		args[i] = value
		i++
	}

	// Append all of the primary key values for each column
	for _, obj := range o {
		pkeyArgs := queries.ValuesFromMapping(reflect.Indirect(reflect.ValueOf(obj)), insuranceBundlePrimaryKeyMapping)
		args = append(args, pkeyArgs...)
	}

	sql := fmt.Sprintf("UPDATE \"insurance_bundle\".\"insurance_bundle\" SET %s WHERE %s",
		strmangle.SetParamNames("\"", "\"", 1, colNames),
		strmangle.WhereClauseRepeated(string(dialect.LQ), string(dialect.RQ), len(colNames)+1, insuranceBundlePrimaryKeyColumns, len(o)))

	if boil.IsDebug(ctx) {
		writer := boil.DebugWriterFrom(ctx)
		fmt.Fprintln(writer, sql)
		fmt.Fprintln(writer, args...)
	}
	result, err := exec.ExecContext(ctx, sql, args...)
	if err != nil {
		return 0, errors.Wrap(err, "insurance_bundle: unable to update all in insuranceBundle slice")
	}

	rowsAff, err := result.RowsAffected()
	if err != nil {
		return 0, errors.Wrap(err, "insurance_bundle: unable to retrieve rows affected all in update all insuranceBundle")
	}
	return rowsAff, nil
}

// Upsert attempts an insert using an executor, and does an update or ignore on conflict.
// See boil.Columns documentation for how to properly use updateColumns and insertColumns.
func (o *InsuranceBundle) Upsert(ctx context.Context, exec boil.ContextExecutor, updateOnConflict bool, conflictColumns []string, updateColumns, insertColumns boil.Columns) error {
	if o == nil {
		return errors.New("insurance_bundle: no insurance_bundle provided for upsert")
	}
	if !boil.TimestampsAreSkipped(ctx) {
		currTime := time.Now().In(boil.GetLocation())

		if o.CreatedAt.IsZero() {
			o.CreatedAt = currTime
		}
		o.UpdatedAt = currTime
	}

	if err := o.doBeforeUpsertHooks(ctx, exec); err != nil {
		return err
	}

	nzDefaults := queries.NonZeroDefaultSet(insuranceBundleColumnsWithDefault, o)

	// Build cache key in-line uglily - mysql vs psql problems
	buf := strmangle.GetBuffer()
	if updateOnConflict {
		buf.WriteByte('t')
	} else {
		buf.WriteByte('f')
	}
	buf.WriteByte('.')
	for _, c := range conflictColumns {
		buf.WriteString(c)
	}
	buf.WriteByte('.')
	buf.WriteString(strconv.Itoa(updateColumns.Kind))
	for _, c := range updateColumns.Cols {
		buf.WriteString(c)
	}
	buf.WriteByte('.')
	buf.WriteString(strconv.Itoa(insertColumns.Kind))
	for _, c := range insertColumns.Cols {
		buf.WriteString(c)
	}
	buf.WriteByte('.')
	for _, c := range nzDefaults {
		buf.WriteString(c)
	}
	key := buf.String()
	strmangle.PutBuffer(buf)

	insuranceBundleUpsertCacheMut.RLock()
	cache, cached := insuranceBundleUpsertCache[key]
	insuranceBundleUpsertCacheMut.RUnlock()

	var err error

	if !cached {
		insert, ret := insertColumns.InsertColumnSet(
			insuranceBundleAllColumns,
			insuranceBundleColumnsWithDefault,
			insuranceBundleColumnsWithoutDefault,
			nzDefaults,
		)

		update := updateColumns.UpdateColumnSet(
			insuranceBundleAllColumns,
			insuranceBundlePrimaryKeyColumns,
		)

		if updateOnConflict && len(update) == 0 {
			return errors.New("insurance_bundle: unable to upsert insurance_bundle, could not build update column list")
		}

		conflict := conflictColumns
		if len(conflict) == 0 {
			conflict = make([]string, len(insuranceBundlePrimaryKeyColumns))
			copy(conflict, insuranceBundlePrimaryKeyColumns)
		}
		cache.query = buildUpsertQueryPostgres(dialect, "\"insurance_bundle\".\"insurance_bundle\"", updateOnConflict, ret, update, conflict, insert)

		cache.valueMapping, err = queries.BindMapping(insuranceBundleType, insuranceBundleMapping, insert)
		if err != nil {
			return err
		}
		if len(ret) != 0 {
			cache.retMapping, err = queries.BindMapping(insuranceBundleType, insuranceBundleMapping, ret)
			if err != nil {
				return err
			}
		}
	}

	value := reflect.Indirect(reflect.ValueOf(o))
	vals := queries.ValuesFromMapping(value, cache.valueMapping)
	var returns []interface{}
	if len(cache.retMapping) != 0 {
		returns = queries.PtrsFromMapping(value, cache.retMapping)
	}

	if boil.IsDebug(ctx) {
		writer := boil.DebugWriterFrom(ctx)
		fmt.Fprintln(writer, cache.query)
		fmt.Fprintln(writer, vals)
	}
	if len(cache.retMapping) != 0 {
		err = exec.QueryRowContext(ctx, cache.query, vals...).Scan(returns...)
		if errors.Is(err, sql.ErrNoRows) {
			err = nil // Postgres doesn't return anything when there's no update
		}
	} else {
		_, err = exec.ExecContext(ctx, cache.query, vals...)
	}
	if err != nil {
		return errors.Wrap(err, "insurance_bundle: unable to upsert insurance_bundle")
	}

	if !cached {
		insuranceBundleUpsertCacheMut.Lock()
		insuranceBundleUpsertCache[key] = cache
		insuranceBundleUpsertCacheMut.Unlock()
	}

	return o.doAfterUpsertHooks(ctx, exec)
}

// Delete deletes a single InsuranceBundle record with an executor.
// Delete will match against the primary key column to find the record to delete.
func (o *InsuranceBundle) Delete(ctx context.Context, exec boil.ContextExecutor) (int64, error) {
	if o == nil {
		return 0, errors.New("insurance_bundle: no InsuranceBundle provided for delete")
	}

	if err := o.doBeforeDeleteHooks(ctx, exec); err != nil {
		return 0, err
	}

	args := queries.ValuesFromMapping(reflect.Indirect(reflect.ValueOf(o)), insuranceBundlePrimaryKeyMapping)
	sql := "DELETE FROM \"insurance_bundle\".\"insurance_bundle\" WHERE \"internal_id\"=$1"

	if boil.IsDebug(ctx) {
		writer := boil.DebugWriterFrom(ctx)
		fmt.Fprintln(writer, sql)
		fmt.Fprintln(writer, args...)
	}
	result, err := exec.ExecContext(ctx, sql, args...)
	if err != nil {
		return 0, errors.Wrap(err, "insurance_bundle: unable to delete from insurance_bundle")
	}

	rowsAff, err := result.RowsAffected()
	if err != nil {
		return 0, errors.Wrap(err, "insurance_bundle: failed to get rows affected by delete for insurance_bundle")
	}

	if err := o.doAfterDeleteHooks(ctx, exec); err != nil {
		return 0, err
	}

	return rowsAff, nil
}

// DeleteAll deletes all matching rows.
func (q insuranceBundleQuery) DeleteAll(ctx context.Context, exec boil.ContextExecutor) (int64, error) {
	if q.Query == nil {
		return 0, errors.New("insurance_bundle: no insuranceBundleQuery provided for delete all")
	}

	queries.SetDelete(q.Query)

	result, err := q.Query.ExecContext(ctx, exec)
	if err != nil {
		return 0, errors.Wrap(err, "insurance_bundle: unable to delete all from insurance_bundle")
	}

	rowsAff, err := result.RowsAffected()
	if err != nil {
		return 0, errors.Wrap(err, "insurance_bundle: failed to get rows affected by deleteall for insurance_bundle")
	}

	return rowsAff, nil
}

// DeleteAll deletes all rows in the slice, using an executor.
func (o InsuranceBundleSlice) DeleteAll(ctx context.Context, exec boil.ContextExecutor) (int64, error) {
	if len(o) == 0 {
		return 0, nil
	}

	if len(insuranceBundleBeforeDeleteHooks) != 0 {
		for _, obj := range o {
			if err := obj.doBeforeDeleteHooks(ctx, exec); err != nil {
				return 0, err
			}
		}
	}

	var args []interface{}
	for _, obj := range o {
		pkeyArgs := queries.ValuesFromMapping(reflect.Indirect(reflect.ValueOf(obj)), insuranceBundlePrimaryKeyMapping)
		args = append(args, pkeyArgs...)
	}

	sql := "DELETE FROM \"insurance_bundle\".\"insurance_bundle\" WHERE " +
		strmangle.WhereClauseRepeated(string(dialect.LQ), string(dialect.RQ), 1, insuranceBundlePrimaryKeyColumns, len(o))

	if boil.IsDebug(ctx) {
		writer := boil.DebugWriterFrom(ctx)
		fmt.Fprintln(writer, sql)
		fmt.Fprintln(writer, args)
	}
	result, err := exec.ExecContext(ctx, sql, args...)
	if err != nil {
		return 0, errors.Wrap(err, "insurance_bundle: unable to delete all from insuranceBundle slice")
	}

	rowsAff, err := result.RowsAffected()
	if err != nil {
		return 0, errors.Wrap(err, "insurance_bundle: failed to get rows affected by deleteall for insurance_bundle")
	}

	if len(insuranceBundleAfterDeleteHooks) != 0 {
		for _, obj := range o {
			if err := obj.doAfterDeleteHooks(ctx, exec); err != nil {
				return 0, err
			}
		}
	}

	return rowsAff, nil
}

// Reload refetches the object from the database
// using the primary keys with an executor.
func (o *InsuranceBundle) Reload(ctx context.Context, exec boil.ContextExecutor) error {
	ret, err := FindInsuranceBundle(ctx, exec, o.InternalID)
	if err != nil {
		return err
	}

	*o = *ret
	return nil
}

// ReloadAll refetches every row with matching primary key column values
// and overwrites the original object slice with the newly updated slice.
func (o *InsuranceBundleSlice) ReloadAll(ctx context.Context, exec boil.ContextExecutor) error {
	if o == nil || len(*o) == 0 {
		return nil
	}

	slice := InsuranceBundleSlice{}
	var args []interface{}
	for _, obj := range *o {
		pkeyArgs := queries.ValuesFromMapping(reflect.Indirect(reflect.ValueOf(obj)), insuranceBundlePrimaryKeyMapping)
		args = append(args, pkeyArgs...)
	}

	sql := "SELECT \"insurance_bundle\".\"insurance_bundle\".* FROM \"insurance_bundle\".\"insurance_bundle\" WHERE " +
		strmangle.WhereClauseRepeated(string(dialect.LQ), string(dialect.RQ), 1, insuranceBundlePrimaryKeyColumns, len(*o))

	q := queries.Raw(sql, args...)

	err := q.Bind(ctx, exec, &slice)
	if err != nil {
		return errors.Wrap(err, "insurance_bundle: unable to reload all in InsuranceBundleSlice")
	}

	*o = slice

	return nil
}

// InsuranceBundleExists checks if the InsuranceBundle row exists.
func InsuranceBundleExists(ctx context.Context, exec boil.ContextExecutor, internalID string) (bool, error) {
	var exists bool
	sql := "select exists(select 1 from \"insurance_bundle\".\"insurance_bundle\" where \"internal_id\"=$1 limit 1)"

	if boil.IsDebug(ctx) {
		writer := boil.DebugWriterFrom(ctx)
		fmt.Fprintln(writer, sql)
		fmt.Fprintln(writer, internalID)
	}
	row := exec.QueryRowContext(ctx, sql, internalID)

	err := row.Scan(&exists)
	if err != nil {
		return false, errors.Wrap(err, "insurance_bundle: unable to check if insurance_bundle exists")
	}

	return exists, nil
}
