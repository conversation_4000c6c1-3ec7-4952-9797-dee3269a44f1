// Code generated by SQLBoiler (https://github.com/volatiletech/sqlboiler). DO NOT EDIT.
// This file is meant to be re-generated in place and/or deleted at any time.

package fleet_safety

import (
	"context"
	"database/sql"
	"fmt"
	"reflect"
	"strconv"
	"strings"
	"sync"
	"time"

	"github.com/friendsofgo/errors"
	"github.com/volatiletech/null/v8"
	"github.com/volatiletech/sqlboiler/v4/boil"
	"github.com/volatiletech/sqlboiler/v4/queries"
	"github.com/volatiletech/sqlboiler/v4/queries/qm"
	"github.com/volatiletech/sqlboiler/v4/queries/qmhelper"
	"github.com/volatiletech/strmangle"
)

// AgencyFleetMetadatum is an object representing the database table.
type AgencyFleetMetadatum struct {
	AgencyID string    `boil:"agency_id" json:"agency_id" toml:"agency_id" yaml:"agency_id"`
	FleetID  string    `boil:"fleet_id" json:"fleet_id" toml:"fleet_id" yaml:"fleet_id"`
	Starred  null.Bool `boil:"starred" json:"starred,omitempty" toml:"starred" yaml:"starred,omitempty"`

	R *agencyFleetMetadatumR `boil:"" json:"" toml:"" yaml:""`
	L agencyFleetMetadatumL  `boil:"-" json:"-" toml:"-" yaml:"-"`
}

var AgencyFleetMetadatumColumns = struct {
	AgencyID string
	FleetID  string
	Starred  string
}{
	AgencyID: "agency_id",
	FleetID:  "fleet_id",
	Starred:  "starred",
}

var AgencyFleetMetadatumTableColumns = struct {
	AgencyID string
	FleetID  string
	Starred  string
}{
	AgencyID: "agency_fleet_metadata.agency_id",
	FleetID:  "agency_fleet_metadata.fleet_id",
	Starred:  "agency_fleet_metadata.starred",
}

// Generated where

type whereHelperstring struct{ field string }

func (w whereHelperstring) EQ(x string) qm.QueryMod  { return qmhelper.Where(w.field, qmhelper.EQ, x) }
func (w whereHelperstring) NEQ(x string) qm.QueryMod { return qmhelper.Where(w.field, qmhelper.NEQ, x) }
func (w whereHelperstring) LT(x string) qm.QueryMod  { return qmhelper.Where(w.field, qmhelper.LT, x) }
func (w whereHelperstring) LTE(x string) qm.QueryMod { return qmhelper.Where(w.field, qmhelper.LTE, x) }
func (w whereHelperstring) GT(x string) qm.QueryMod  { return qmhelper.Where(w.field, qmhelper.GT, x) }
func (w whereHelperstring) GTE(x string) qm.QueryMod { return qmhelper.Where(w.field, qmhelper.GTE, x) }
func (w whereHelperstring) IN(slice []string) qm.QueryMod {
	values := make([]interface{}, 0, len(slice))
	for _, value := range slice {
		values = append(values, value)
	}
	return qm.WhereIn(fmt.Sprintf("%s IN ?", w.field), values...)
}
func (w whereHelperstring) NIN(slice []string) qm.QueryMod {
	values := make([]interface{}, 0, len(slice))
	for _, value := range slice {
		values = append(values, value)
	}
	return qm.WhereNotIn(fmt.Sprintf("%s NOT IN ?", w.field), values...)
}

type whereHelpernull_Bool struct{ field string }

func (w whereHelpernull_Bool) EQ(x null.Bool) qm.QueryMod {
	return qmhelper.WhereNullEQ(w.field, false, x)
}
func (w whereHelpernull_Bool) NEQ(x null.Bool) qm.QueryMod {
	return qmhelper.WhereNullEQ(w.field, true, x)
}
func (w whereHelpernull_Bool) LT(x null.Bool) qm.QueryMod {
	return qmhelper.Where(w.field, qmhelper.LT, x)
}
func (w whereHelpernull_Bool) LTE(x null.Bool) qm.QueryMod {
	return qmhelper.Where(w.field, qmhelper.LTE, x)
}
func (w whereHelpernull_Bool) GT(x null.Bool) qm.QueryMod {
	return qmhelper.Where(w.field, qmhelper.GT, x)
}
func (w whereHelpernull_Bool) GTE(x null.Bool) qm.QueryMod {
	return qmhelper.Where(w.field, qmhelper.GTE, x)
}

func (w whereHelpernull_Bool) IsNull() qm.QueryMod    { return qmhelper.WhereIsNull(w.field) }
func (w whereHelpernull_Bool) IsNotNull() qm.QueryMod { return qmhelper.WhereIsNotNull(w.field) }

var AgencyFleetMetadatumWhere = struct {
	AgencyID whereHelperstring
	FleetID  whereHelperstring
	Starred  whereHelpernull_Bool
}{
	AgencyID: whereHelperstring{field: "\"fleet_safety\".\"agency_fleet_metadata\".\"agency_id\""},
	FleetID:  whereHelperstring{field: "\"fleet_safety\".\"agency_fleet_metadata\".\"fleet_id\""},
	Starred:  whereHelpernull_Bool{field: "\"fleet_safety\".\"agency_fleet_metadata\".\"starred\""},
}

// AgencyFleetMetadatumRels is where relationship names are stored.
var AgencyFleetMetadatumRels = struct {
}{}

// agencyFleetMetadatumR is where relationships are stored.
type agencyFleetMetadatumR struct {
}

// NewStruct creates a new relationship struct
func (*agencyFleetMetadatumR) NewStruct() *agencyFleetMetadatumR {
	return &agencyFleetMetadatumR{}
}

// agencyFleetMetadatumL is where Load methods for each relationship are stored.
type agencyFleetMetadatumL struct{}

var (
	agencyFleetMetadatumAllColumns            = []string{"agency_id", "fleet_id", "starred"}
	agencyFleetMetadatumColumnsWithoutDefault = []string{"agency_id", "fleet_id"}
	agencyFleetMetadatumColumnsWithDefault    = []string{"starred"}
	agencyFleetMetadatumPrimaryKeyColumns     = []string{"agency_id", "fleet_id"}
	agencyFleetMetadatumGeneratedColumns      = []string{}
)

type (
	// AgencyFleetMetadatumSlice is an alias for a slice of pointers to AgencyFleetMetadatum.
	// This should almost always be used instead of []AgencyFleetMetadatum.
	AgencyFleetMetadatumSlice []*AgencyFleetMetadatum
	// AgencyFleetMetadatumHook is the signature for custom AgencyFleetMetadatum hook methods
	AgencyFleetMetadatumHook func(context.Context, boil.ContextExecutor, *AgencyFleetMetadatum) error

	agencyFleetMetadatumQuery struct {
		*queries.Query
	}
)

// Cache for insert, update and upsert
var (
	agencyFleetMetadatumType                 = reflect.TypeOf(&AgencyFleetMetadatum{})
	agencyFleetMetadatumMapping              = queries.MakeStructMapping(agencyFleetMetadatumType)
	agencyFleetMetadatumPrimaryKeyMapping, _ = queries.BindMapping(agencyFleetMetadatumType, agencyFleetMetadatumMapping, agencyFleetMetadatumPrimaryKeyColumns)
	agencyFleetMetadatumInsertCacheMut       sync.RWMutex
	agencyFleetMetadatumInsertCache          = make(map[string]insertCache)
	agencyFleetMetadatumUpdateCacheMut       sync.RWMutex
	agencyFleetMetadatumUpdateCache          = make(map[string]updateCache)
	agencyFleetMetadatumUpsertCacheMut       sync.RWMutex
	agencyFleetMetadatumUpsertCache          = make(map[string]insertCache)
)

var (
	// Force time package dependency for automated UpdatedAt/CreatedAt.
	_ = time.Second
	// Force qmhelper dependency for where clause generation (which doesn't
	// always happen)
	_ = qmhelper.Where
)

var agencyFleetMetadatumAfterSelectHooks []AgencyFleetMetadatumHook

var agencyFleetMetadatumBeforeInsertHooks []AgencyFleetMetadatumHook
var agencyFleetMetadatumAfterInsertHooks []AgencyFleetMetadatumHook

var agencyFleetMetadatumBeforeUpdateHooks []AgencyFleetMetadatumHook
var agencyFleetMetadatumAfterUpdateHooks []AgencyFleetMetadatumHook

var agencyFleetMetadatumBeforeDeleteHooks []AgencyFleetMetadatumHook
var agencyFleetMetadatumAfterDeleteHooks []AgencyFleetMetadatumHook

var agencyFleetMetadatumBeforeUpsertHooks []AgencyFleetMetadatumHook
var agencyFleetMetadatumAfterUpsertHooks []AgencyFleetMetadatumHook

// doAfterSelectHooks executes all "after Select" hooks.
func (o *AgencyFleetMetadatum) doAfterSelectHooks(ctx context.Context, exec boil.ContextExecutor) (err error) {
	if boil.HooksAreSkipped(ctx) {
		return nil
	}

	for _, hook := range agencyFleetMetadatumAfterSelectHooks {
		if err := hook(ctx, exec, o); err != nil {
			return err
		}
	}

	return nil
}

// doBeforeInsertHooks executes all "before insert" hooks.
func (o *AgencyFleetMetadatum) doBeforeInsertHooks(ctx context.Context, exec boil.ContextExecutor) (err error) {
	if boil.HooksAreSkipped(ctx) {
		return nil
	}

	for _, hook := range agencyFleetMetadatumBeforeInsertHooks {
		if err := hook(ctx, exec, o); err != nil {
			return err
		}
	}

	return nil
}

// doAfterInsertHooks executes all "after Insert" hooks.
func (o *AgencyFleetMetadatum) doAfterInsertHooks(ctx context.Context, exec boil.ContextExecutor) (err error) {
	if boil.HooksAreSkipped(ctx) {
		return nil
	}

	for _, hook := range agencyFleetMetadatumAfterInsertHooks {
		if err := hook(ctx, exec, o); err != nil {
			return err
		}
	}

	return nil
}

// doBeforeUpdateHooks executes all "before Update" hooks.
func (o *AgencyFleetMetadatum) doBeforeUpdateHooks(ctx context.Context, exec boil.ContextExecutor) (err error) {
	if boil.HooksAreSkipped(ctx) {
		return nil
	}

	for _, hook := range agencyFleetMetadatumBeforeUpdateHooks {
		if err := hook(ctx, exec, o); err != nil {
			return err
		}
	}

	return nil
}

// doAfterUpdateHooks executes all "after Update" hooks.
func (o *AgencyFleetMetadatum) doAfterUpdateHooks(ctx context.Context, exec boil.ContextExecutor) (err error) {
	if boil.HooksAreSkipped(ctx) {
		return nil
	}

	for _, hook := range agencyFleetMetadatumAfterUpdateHooks {
		if err := hook(ctx, exec, o); err != nil {
			return err
		}
	}

	return nil
}

// doBeforeDeleteHooks executes all "before Delete" hooks.
func (o *AgencyFleetMetadatum) doBeforeDeleteHooks(ctx context.Context, exec boil.ContextExecutor) (err error) {
	if boil.HooksAreSkipped(ctx) {
		return nil
	}

	for _, hook := range agencyFleetMetadatumBeforeDeleteHooks {
		if err := hook(ctx, exec, o); err != nil {
			return err
		}
	}

	return nil
}

// doAfterDeleteHooks executes all "after Delete" hooks.
func (o *AgencyFleetMetadatum) doAfterDeleteHooks(ctx context.Context, exec boil.ContextExecutor) (err error) {
	if boil.HooksAreSkipped(ctx) {
		return nil
	}

	for _, hook := range agencyFleetMetadatumAfterDeleteHooks {
		if err := hook(ctx, exec, o); err != nil {
			return err
		}
	}

	return nil
}

// doBeforeUpsertHooks executes all "before Upsert" hooks.
func (o *AgencyFleetMetadatum) doBeforeUpsertHooks(ctx context.Context, exec boil.ContextExecutor) (err error) {
	if boil.HooksAreSkipped(ctx) {
		return nil
	}

	for _, hook := range agencyFleetMetadatumBeforeUpsertHooks {
		if err := hook(ctx, exec, o); err != nil {
			return err
		}
	}

	return nil
}

// doAfterUpsertHooks executes all "after Upsert" hooks.
func (o *AgencyFleetMetadatum) doAfterUpsertHooks(ctx context.Context, exec boil.ContextExecutor) (err error) {
	if boil.HooksAreSkipped(ctx) {
		return nil
	}

	for _, hook := range agencyFleetMetadatumAfterUpsertHooks {
		if err := hook(ctx, exec, o); err != nil {
			return err
		}
	}

	return nil
}

// AddAgencyFleetMetadatumHook registers your hook function for all future operations.
func AddAgencyFleetMetadatumHook(hookPoint boil.HookPoint, agencyFleetMetadatumHook AgencyFleetMetadatumHook) {
	switch hookPoint {
	case boil.AfterSelectHook:
		agencyFleetMetadatumAfterSelectHooks = append(agencyFleetMetadatumAfterSelectHooks, agencyFleetMetadatumHook)
	case boil.BeforeInsertHook:
		agencyFleetMetadatumBeforeInsertHooks = append(agencyFleetMetadatumBeforeInsertHooks, agencyFleetMetadatumHook)
	case boil.AfterInsertHook:
		agencyFleetMetadatumAfterInsertHooks = append(agencyFleetMetadatumAfterInsertHooks, agencyFleetMetadatumHook)
	case boil.BeforeUpdateHook:
		agencyFleetMetadatumBeforeUpdateHooks = append(agencyFleetMetadatumBeforeUpdateHooks, agencyFleetMetadatumHook)
	case boil.AfterUpdateHook:
		agencyFleetMetadatumAfterUpdateHooks = append(agencyFleetMetadatumAfterUpdateHooks, agencyFleetMetadatumHook)
	case boil.BeforeDeleteHook:
		agencyFleetMetadatumBeforeDeleteHooks = append(agencyFleetMetadatumBeforeDeleteHooks, agencyFleetMetadatumHook)
	case boil.AfterDeleteHook:
		agencyFleetMetadatumAfterDeleteHooks = append(agencyFleetMetadatumAfterDeleteHooks, agencyFleetMetadatumHook)
	case boil.BeforeUpsertHook:
		agencyFleetMetadatumBeforeUpsertHooks = append(agencyFleetMetadatumBeforeUpsertHooks, agencyFleetMetadatumHook)
	case boil.AfterUpsertHook:
		agencyFleetMetadatumAfterUpsertHooks = append(agencyFleetMetadatumAfterUpsertHooks, agencyFleetMetadatumHook)
	}
}

// One returns a single agencyFleetMetadatum record from the query.
func (q agencyFleetMetadatumQuery) One(ctx context.Context, exec boil.ContextExecutor) (*AgencyFleetMetadatum, error) {
	o := &AgencyFleetMetadatum{}

	queries.SetLimit(q.Query, 1)

	err := q.Bind(ctx, exec, o)
	if err != nil {
		if errors.Is(err, sql.ErrNoRows) {
			return nil, sql.ErrNoRows
		}
		return nil, errors.Wrap(err, "fleet_safety: failed to execute a one query for agency_fleet_metadata")
	}

	if err := o.doAfterSelectHooks(ctx, exec); err != nil {
		return o, err
	}

	return o, nil
}

// All returns all AgencyFleetMetadatum records from the query.
func (q agencyFleetMetadatumQuery) All(ctx context.Context, exec boil.ContextExecutor) (AgencyFleetMetadatumSlice, error) {
	var o []*AgencyFleetMetadatum

	err := q.Bind(ctx, exec, &o)
	if err != nil {
		return nil, errors.Wrap(err, "fleet_safety: failed to assign all query results to AgencyFleetMetadatum slice")
	}

	if len(agencyFleetMetadatumAfterSelectHooks) != 0 {
		for _, obj := range o {
			if err := obj.doAfterSelectHooks(ctx, exec); err != nil {
				return o, err
			}
		}
	}

	return o, nil
}

// Count returns the count of all AgencyFleetMetadatum records in the query.
func (q agencyFleetMetadatumQuery) Count(ctx context.Context, exec boil.ContextExecutor) (int64, error) {
	var count int64

	queries.SetSelect(q.Query, nil)
	queries.SetCount(q.Query)

	err := q.Query.QueryRowContext(ctx, exec).Scan(&count)
	if err != nil {
		return 0, errors.Wrap(err, "fleet_safety: failed to count agency_fleet_metadata rows")
	}

	return count, nil
}

// Exists checks if the row exists in the table.
func (q agencyFleetMetadatumQuery) Exists(ctx context.Context, exec boil.ContextExecutor) (bool, error) {
	var count int64

	queries.SetSelect(q.Query, nil)
	queries.SetCount(q.Query)
	queries.SetLimit(q.Query, 1)

	err := q.Query.QueryRowContext(ctx, exec).Scan(&count)
	if err != nil {
		return false, errors.Wrap(err, "fleet_safety: failed to check if agency_fleet_metadata exists")
	}

	return count > 0, nil
}

// AgencyFleetMetadata retrieves all the records using an executor.
func AgencyFleetMetadata(mods ...qm.QueryMod) agencyFleetMetadatumQuery {
	mods = append(mods, qm.From("\"fleet_safety\".\"agency_fleet_metadata\""))
	q := NewQuery(mods...)
	if len(queries.GetSelect(q)) == 0 {
		queries.SetSelect(q, []string{"\"fleet_safety\".\"agency_fleet_metadata\".*"})
	}

	return agencyFleetMetadatumQuery{q}
}

// FindAgencyFleetMetadatum retrieves a single record by ID with an executor.
// If selectCols is empty Find will return all columns.
func FindAgencyFleetMetadatum(ctx context.Context, exec boil.ContextExecutor, agencyID string, fleetID string, selectCols ...string) (*AgencyFleetMetadatum, error) {
	agencyFleetMetadatumObj := &AgencyFleetMetadatum{}

	sel := "*"
	if len(selectCols) > 0 {
		sel = strings.Join(strmangle.IdentQuoteSlice(dialect.LQ, dialect.RQ, selectCols), ",")
	}
	query := fmt.Sprintf(
		"select %s from \"fleet_safety\".\"agency_fleet_metadata\" where \"agency_id\"=$1 AND \"fleet_id\"=$2", sel,
	)

	q := queries.Raw(query, agencyID, fleetID)

	err := q.Bind(ctx, exec, agencyFleetMetadatumObj)
	if err != nil {
		if errors.Is(err, sql.ErrNoRows) {
			return nil, sql.ErrNoRows
		}
		return nil, errors.Wrap(err, "fleet_safety: unable to select from agency_fleet_metadata")
	}

	if err = agencyFleetMetadatumObj.doAfterSelectHooks(ctx, exec); err != nil {
		return agencyFleetMetadatumObj, err
	}

	return agencyFleetMetadatumObj, nil
}

// Insert a single record using an executor.
// See boil.Columns.InsertColumnSet documentation to understand column list inference for inserts.
func (o *AgencyFleetMetadatum) Insert(ctx context.Context, exec boil.ContextExecutor, columns boil.Columns) error {
	if o == nil {
		return errors.New("fleet_safety: no agency_fleet_metadata provided for insertion")
	}

	var err error

	if err := o.doBeforeInsertHooks(ctx, exec); err != nil {
		return err
	}

	nzDefaults := queries.NonZeroDefaultSet(agencyFleetMetadatumColumnsWithDefault, o)

	key := makeCacheKey(columns, nzDefaults)
	agencyFleetMetadatumInsertCacheMut.RLock()
	cache, cached := agencyFleetMetadatumInsertCache[key]
	agencyFleetMetadatumInsertCacheMut.RUnlock()

	if !cached {
		wl, returnColumns := columns.InsertColumnSet(
			agencyFleetMetadatumAllColumns,
			agencyFleetMetadatumColumnsWithDefault,
			agencyFleetMetadatumColumnsWithoutDefault,
			nzDefaults,
		)

		cache.valueMapping, err = queries.BindMapping(agencyFleetMetadatumType, agencyFleetMetadatumMapping, wl)
		if err != nil {
			return err
		}
		cache.retMapping, err = queries.BindMapping(agencyFleetMetadatumType, agencyFleetMetadatumMapping, returnColumns)
		if err != nil {
			return err
		}
		if len(wl) != 0 {
			cache.query = fmt.Sprintf("INSERT INTO \"fleet_safety\".\"agency_fleet_metadata\" (\"%s\") %%sVALUES (%s)%%s", strings.Join(wl, "\",\""), strmangle.Placeholders(dialect.UseIndexPlaceholders, len(wl), 1, 1))
		} else {
			cache.query = "INSERT INTO \"fleet_safety\".\"agency_fleet_metadata\" %sDEFAULT VALUES%s"
		}

		var queryOutput, queryReturning string

		if len(cache.retMapping) != 0 {
			queryReturning = fmt.Sprintf(" RETURNING \"%s\"", strings.Join(returnColumns, "\",\""))
		}

		cache.query = fmt.Sprintf(cache.query, queryOutput, queryReturning)
	}

	value := reflect.Indirect(reflect.ValueOf(o))
	vals := queries.ValuesFromMapping(value, cache.valueMapping)

	if boil.IsDebug(ctx) {
		writer := boil.DebugWriterFrom(ctx)
		fmt.Fprintln(writer, cache.query)
		fmt.Fprintln(writer, vals)
	}

	if len(cache.retMapping) != 0 {
		err = exec.QueryRowContext(ctx, cache.query, vals...).Scan(queries.PtrsFromMapping(value, cache.retMapping)...)
	} else {
		_, err = exec.ExecContext(ctx, cache.query, vals...)
	}

	if err != nil {
		return errors.Wrap(err, "fleet_safety: unable to insert into agency_fleet_metadata")
	}

	if !cached {
		agencyFleetMetadatumInsertCacheMut.Lock()
		agencyFleetMetadatumInsertCache[key] = cache
		agencyFleetMetadatumInsertCacheMut.Unlock()
	}

	return o.doAfterInsertHooks(ctx, exec)
}

// Update uses an executor to update the AgencyFleetMetadatum.
// See boil.Columns.UpdateColumnSet documentation to understand column list inference for updates.
// Update does not automatically update the record in case of default values. Use .Reload() to refresh the records.
func (o *AgencyFleetMetadatum) Update(ctx context.Context, exec boil.ContextExecutor, columns boil.Columns) (int64, error) {
	var err error
	if err = o.doBeforeUpdateHooks(ctx, exec); err != nil {
		return 0, err
	}
	key := makeCacheKey(columns, nil)
	agencyFleetMetadatumUpdateCacheMut.RLock()
	cache, cached := agencyFleetMetadatumUpdateCache[key]
	agencyFleetMetadatumUpdateCacheMut.RUnlock()

	if !cached {
		wl := columns.UpdateColumnSet(
			agencyFleetMetadatumAllColumns,
			agencyFleetMetadatumPrimaryKeyColumns,
		)

		if !columns.IsWhitelist() {
			wl = strmangle.SetComplement(wl, []string{"created_at"})
		}
		if len(wl) == 0 {
			return 0, errors.New("fleet_safety: unable to update agency_fleet_metadata, could not build whitelist")
		}

		cache.query = fmt.Sprintf("UPDATE \"fleet_safety\".\"agency_fleet_metadata\" SET %s WHERE %s",
			strmangle.SetParamNames("\"", "\"", 1, wl),
			strmangle.WhereClause("\"", "\"", len(wl)+1, agencyFleetMetadatumPrimaryKeyColumns),
		)
		cache.valueMapping, err = queries.BindMapping(agencyFleetMetadatumType, agencyFleetMetadatumMapping, append(wl, agencyFleetMetadatumPrimaryKeyColumns...))
		if err != nil {
			return 0, err
		}
	}

	values := queries.ValuesFromMapping(reflect.Indirect(reflect.ValueOf(o)), cache.valueMapping)

	if boil.IsDebug(ctx) {
		writer := boil.DebugWriterFrom(ctx)
		fmt.Fprintln(writer, cache.query)
		fmt.Fprintln(writer, values)
	}
	var result sql.Result
	result, err = exec.ExecContext(ctx, cache.query, values...)
	if err != nil {
		return 0, errors.Wrap(err, "fleet_safety: unable to update agency_fleet_metadata row")
	}

	rowsAff, err := result.RowsAffected()
	if err != nil {
		return 0, errors.Wrap(err, "fleet_safety: failed to get rows affected by update for agency_fleet_metadata")
	}

	if !cached {
		agencyFleetMetadatumUpdateCacheMut.Lock()
		agencyFleetMetadatumUpdateCache[key] = cache
		agencyFleetMetadatumUpdateCacheMut.Unlock()
	}

	return rowsAff, o.doAfterUpdateHooks(ctx, exec)
}

// UpdateAll updates all rows with the specified column values.
func (q agencyFleetMetadatumQuery) UpdateAll(ctx context.Context, exec boil.ContextExecutor, cols M) (int64, error) {
	queries.SetUpdate(q.Query, cols)

	result, err := q.Query.ExecContext(ctx, exec)
	if err != nil {
		return 0, errors.Wrap(err, "fleet_safety: unable to update all for agency_fleet_metadata")
	}

	rowsAff, err := result.RowsAffected()
	if err != nil {
		return 0, errors.Wrap(err, "fleet_safety: unable to retrieve rows affected for agency_fleet_metadata")
	}

	return rowsAff, nil
}

// UpdateAll updates all rows with the specified column values, using an executor.
func (o AgencyFleetMetadatumSlice) UpdateAll(ctx context.Context, exec boil.ContextExecutor, cols M) (int64, error) {
	ln := int64(len(o))
	if ln == 0 {
		return 0, nil
	}

	if len(cols) == 0 {
		return 0, errors.New("fleet_safety: update all requires at least one column argument")
	}

	colNames := make([]string, len(cols))
	args := make([]interface{}, len(cols))

	i := 0
	for name, value := range cols {
		colNames[i] = name
		args[i] = value
		i++
	}

	// Append all of the primary key values for each column
	for _, obj := range o {
		pkeyArgs := queries.ValuesFromMapping(reflect.Indirect(reflect.ValueOf(obj)), agencyFleetMetadatumPrimaryKeyMapping)
		args = append(args, pkeyArgs...)
	}

	sql := fmt.Sprintf("UPDATE \"fleet_safety\".\"agency_fleet_metadata\" SET %s WHERE %s",
		strmangle.SetParamNames("\"", "\"", 1, colNames),
		strmangle.WhereClauseRepeated(string(dialect.LQ), string(dialect.RQ), len(colNames)+1, agencyFleetMetadatumPrimaryKeyColumns, len(o)))

	if boil.IsDebug(ctx) {
		writer := boil.DebugWriterFrom(ctx)
		fmt.Fprintln(writer, sql)
		fmt.Fprintln(writer, args...)
	}
	result, err := exec.ExecContext(ctx, sql, args...)
	if err != nil {
		return 0, errors.Wrap(err, "fleet_safety: unable to update all in agencyFleetMetadatum slice")
	}

	rowsAff, err := result.RowsAffected()
	if err != nil {
		return 0, errors.Wrap(err, "fleet_safety: unable to retrieve rows affected all in update all agencyFleetMetadatum")
	}
	return rowsAff, nil
}

// Upsert attempts an insert using an executor, and does an update or ignore on conflict.
// See boil.Columns documentation for how to properly use updateColumns and insertColumns.
func (o *AgencyFleetMetadatum) Upsert(ctx context.Context, exec boil.ContextExecutor, updateOnConflict bool, conflictColumns []string, updateColumns, insertColumns boil.Columns) error {
	if o == nil {
		return errors.New("fleet_safety: no agency_fleet_metadata provided for upsert")
	}

	if err := o.doBeforeUpsertHooks(ctx, exec); err != nil {
		return err
	}

	nzDefaults := queries.NonZeroDefaultSet(agencyFleetMetadatumColumnsWithDefault, o)

	// Build cache key in-line uglily - mysql vs psql problems
	buf := strmangle.GetBuffer()
	if updateOnConflict {
		buf.WriteByte('t')
	} else {
		buf.WriteByte('f')
	}
	buf.WriteByte('.')
	for _, c := range conflictColumns {
		buf.WriteString(c)
	}
	buf.WriteByte('.')
	buf.WriteString(strconv.Itoa(updateColumns.Kind))
	for _, c := range updateColumns.Cols {
		buf.WriteString(c)
	}
	buf.WriteByte('.')
	buf.WriteString(strconv.Itoa(insertColumns.Kind))
	for _, c := range insertColumns.Cols {
		buf.WriteString(c)
	}
	buf.WriteByte('.')
	for _, c := range nzDefaults {
		buf.WriteString(c)
	}
	key := buf.String()
	strmangle.PutBuffer(buf)

	agencyFleetMetadatumUpsertCacheMut.RLock()
	cache, cached := agencyFleetMetadatumUpsertCache[key]
	agencyFleetMetadatumUpsertCacheMut.RUnlock()

	var err error

	if !cached {
		insert, ret := insertColumns.InsertColumnSet(
			agencyFleetMetadatumAllColumns,
			agencyFleetMetadatumColumnsWithDefault,
			agencyFleetMetadatumColumnsWithoutDefault,
			nzDefaults,
		)

		update := updateColumns.UpdateColumnSet(
			agencyFleetMetadatumAllColumns,
			agencyFleetMetadatumPrimaryKeyColumns,
		)

		if updateOnConflict && len(update) == 0 {
			return errors.New("fleet_safety: unable to upsert agency_fleet_metadata, could not build update column list")
		}

		conflict := conflictColumns
		if len(conflict) == 0 {
			conflict = make([]string, len(agencyFleetMetadatumPrimaryKeyColumns))
			copy(conflict, agencyFleetMetadatumPrimaryKeyColumns)
		}
		cache.query = buildUpsertQueryPostgres(dialect, "\"fleet_safety\".\"agency_fleet_metadata\"", updateOnConflict, ret, update, conflict, insert)

		cache.valueMapping, err = queries.BindMapping(agencyFleetMetadatumType, agencyFleetMetadatumMapping, insert)
		if err != nil {
			return err
		}
		if len(ret) != 0 {
			cache.retMapping, err = queries.BindMapping(agencyFleetMetadatumType, agencyFleetMetadatumMapping, ret)
			if err != nil {
				return err
			}
		}
	}

	value := reflect.Indirect(reflect.ValueOf(o))
	vals := queries.ValuesFromMapping(value, cache.valueMapping)
	var returns []interface{}
	if len(cache.retMapping) != 0 {
		returns = queries.PtrsFromMapping(value, cache.retMapping)
	}

	if boil.IsDebug(ctx) {
		writer := boil.DebugWriterFrom(ctx)
		fmt.Fprintln(writer, cache.query)
		fmt.Fprintln(writer, vals)
	}
	if len(cache.retMapping) != 0 {
		err = exec.QueryRowContext(ctx, cache.query, vals...).Scan(returns...)
		if errors.Is(err, sql.ErrNoRows) {
			err = nil // Postgres doesn't return anything when there's no update
		}
	} else {
		_, err = exec.ExecContext(ctx, cache.query, vals...)
	}
	if err != nil {
		return errors.Wrap(err, "fleet_safety: unable to upsert agency_fleet_metadata")
	}

	if !cached {
		agencyFleetMetadatumUpsertCacheMut.Lock()
		agencyFleetMetadatumUpsertCache[key] = cache
		agencyFleetMetadatumUpsertCacheMut.Unlock()
	}

	return o.doAfterUpsertHooks(ctx, exec)
}

// Delete deletes a single AgencyFleetMetadatum record with an executor.
// Delete will match against the primary key column to find the record to delete.
func (o *AgencyFleetMetadatum) Delete(ctx context.Context, exec boil.ContextExecutor) (int64, error) {
	if o == nil {
		return 0, errors.New("fleet_safety: no AgencyFleetMetadatum provided for delete")
	}

	if err := o.doBeforeDeleteHooks(ctx, exec); err != nil {
		return 0, err
	}

	args := queries.ValuesFromMapping(reflect.Indirect(reflect.ValueOf(o)), agencyFleetMetadatumPrimaryKeyMapping)
	sql := "DELETE FROM \"fleet_safety\".\"agency_fleet_metadata\" WHERE \"agency_id\"=$1 AND \"fleet_id\"=$2"

	if boil.IsDebug(ctx) {
		writer := boil.DebugWriterFrom(ctx)
		fmt.Fprintln(writer, sql)
		fmt.Fprintln(writer, args...)
	}
	result, err := exec.ExecContext(ctx, sql, args...)
	if err != nil {
		return 0, errors.Wrap(err, "fleet_safety: unable to delete from agency_fleet_metadata")
	}

	rowsAff, err := result.RowsAffected()
	if err != nil {
		return 0, errors.Wrap(err, "fleet_safety: failed to get rows affected by delete for agency_fleet_metadata")
	}

	if err := o.doAfterDeleteHooks(ctx, exec); err != nil {
		return 0, err
	}

	return rowsAff, nil
}

// DeleteAll deletes all matching rows.
func (q agencyFleetMetadatumQuery) DeleteAll(ctx context.Context, exec boil.ContextExecutor) (int64, error) {
	if q.Query == nil {
		return 0, errors.New("fleet_safety: no agencyFleetMetadatumQuery provided for delete all")
	}

	queries.SetDelete(q.Query)

	result, err := q.Query.ExecContext(ctx, exec)
	if err != nil {
		return 0, errors.Wrap(err, "fleet_safety: unable to delete all from agency_fleet_metadata")
	}

	rowsAff, err := result.RowsAffected()
	if err != nil {
		return 0, errors.Wrap(err, "fleet_safety: failed to get rows affected by deleteall for agency_fleet_metadata")
	}

	return rowsAff, nil
}

// DeleteAll deletes all rows in the slice, using an executor.
func (o AgencyFleetMetadatumSlice) DeleteAll(ctx context.Context, exec boil.ContextExecutor) (int64, error) {
	if len(o) == 0 {
		return 0, nil
	}

	if len(agencyFleetMetadatumBeforeDeleteHooks) != 0 {
		for _, obj := range o {
			if err := obj.doBeforeDeleteHooks(ctx, exec); err != nil {
				return 0, err
			}
		}
	}

	var args []interface{}
	for _, obj := range o {
		pkeyArgs := queries.ValuesFromMapping(reflect.Indirect(reflect.ValueOf(obj)), agencyFleetMetadatumPrimaryKeyMapping)
		args = append(args, pkeyArgs...)
	}

	sql := "DELETE FROM \"fleet_safety\".\"agency_fleet_metadata\" WHERE " +
		strmangle.WhereClauseRepeated(string(dialect.LQ), string(dialect.RQ), 1, agencyFleetMetadatumPrimaryKeyColumns, len(o))

	if boil.IsDebug(ctx) {
		writer := boil.DebugWriterFrom(ctx)
		fmt.Fprintln(writer, sql)
		fmt.Fprintln(writer, args)
	}
	result, err := exec.ExecContext(ctx, sql, args...)
	if err != nil {
		return 0, errors.Wrap(err, "fleet_safety: unable to delete all from agencyFleetMetadatum slice")
	}

	rowsAff, err := result.RowsAffected()
	if err != nil {
		return 0, errors.Wrap(err, "fleet_safety: failed to get rows affected by deleteall for agency_fleet_metadata")
	}

	if len(agencyFleetMetadatumAfterDeleteHooks) != 0 {
		for _, obj := range o {
			if err := obj.doAfterDeleteHooks(ctx, exec); err != nil {
				return 0, err
			}
		}
	}

	return rowsAff, nil
}

// Reload refetches the object from the database
// using the primary keys with an executor.
func (o *AgencyFleetMetadatum) Reload(ctx context.Context, exec boil.ContextExecutor) error {
	ret, err := FindAgencyFleetMetadatum(ctx, exec, o.AgencyID, o.FleetID)
	if err != nil {
		return err
	}

	*o = *ret
	return nil
}

// ReloadAll refetches every row with matching primary key column values
// and overwrites the original object slice with the newly updated slice.
func (o *AgencyFleetMetadatumSlice) ReloadAll(ctx context.Context, exec boil.ContextExecutor) error {
	if o == nil || len(*o) == 0 {
		return nil
	}

	slice := AgencyFleetMetadatumSlice{}
	var args []interface{}
	for _, obj := range *o {
		pkeyArgs := queries.ValuesFromMapping(reflect.Indirect(reflect.ValueOf(obj)), agencyFleetMetadatumPrimaryKeyMapping)
		args = append(args, pkeyArgs...)
	}

	sql := "SELECT \"fleet_safety\".\"agency_fleet_metadata\".* FROM \"fleet_safety\".\"agency_fleet_metadata\" WHERE " +
		strmangle.WhereClauseRepeated(string(dialect.LQ), string(dialect.RQ), 1, agencyFleetMetadatumPrimaryKeyColumns, len(*o))

	q := queries.Raw(sql, args...)

	err := q.Bind(ctx, exec, &slice)
	if err != nil {
		return errors.Wrap(err, "fleet_safety: unable to reload all in AgencyFleetMetadatumSlice")
	}

	*o = slice

	return nil
}

// AgencyFleetMetadatumExists checks if the AgencyFleetMetadatum row exists.
func AgencyFleetMetadatumExists(ctx context.Context, exec boil.ContextExecutor, agencyID string, fleetID string) (bool, error) {
	var exists bool
	sql := "select exists(select 1 from \"fleet_safety\".\"agency_fleet_metadata\" where \"agency_id\"=$1 AND \"fleet_id\"=$2 limit 1)"

	if boil.IsDebug(ctx) {
		writer := boil.DebugWriterFrom(ctx)
		fmt.Fprintln(writer, sql)
		fmt.Fprintln(writer, agencyID, fleetID)
	}
	row := exec.QueryRowContext(ctx, sql, agencyID, fleetID)

	err := row.Scan(&exists)
	if err != nil {
		return false, errors.Wrap(err, "fleet_safety: unable to check if agency_fleet_metadata exists")
	}

	return exists, nil
}
