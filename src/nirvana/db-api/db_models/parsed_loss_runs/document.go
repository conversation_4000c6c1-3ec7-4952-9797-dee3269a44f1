// Code generated by SQLBoiler (https://github.com/volatiletech/sqlboiler). DO NOT EDIT.
// This file is meant to be re-generated in place and/or deleted at any time.

package parsed_loss_runs

import (
	"context"
	"database/sql"
	"fmt"
	"reflect"
	"strconv"
	"strings"
	"sync"
	"time"

	"github.com/friendsofgo/errors"
	"github.com/volatiletech/null/v8"
	"github.com/volatiletech/sqlboiler/v4/boil"
	"github.com/volatiletech/sqlboiler/v4/queries"
	"github.com/volatiletech/sqlboiler/v4/queries/qm"
	"github.com/volatiletech/sqlboiler/v4/queries/qmhelper"
	"github.com/volatiletech/strmangle"
)

// Document is an object representing the database table.
type Document struct {
	ID             string      `boil:"id" json:"id" toml:"id" yaml:"id"`
	Status         string      `boil:"status" json:"status" toml:"status" yaml:"status"`
	CreatedAt      time.Time   `boil:"created_at" json:"created_at" toml:"created_at" yaml:"created_at"`
	UpdatedAt      null.Time   `boil:"updated_at" json:"updated_at,omitempty" toml:"updated_at" yaml:"updated_at,omitempty"`
	SubmissionID   null.String `boil:"submission_id" json:"submission_id,omitempty" toml:"submission_id" yaml:"submission_id,omitempty"`
	ErrorInfo      null.JSON   `boil:"error_info" json:"error_info,omitempty" toml:"error_info" yaml:"error_info,omitempty"`
	FirstUpdatedAt null.Time   `boil:"first_updated_at" json:"first_updated_at,omitempty" toml:"first_updated_at" yaml:"first_updated_at,omitempty"`
	SummaryFileURL null.String `boil:"summary_file_url" json:"summary_file_url,omitempty" toml:"summary_file_url" yaml:"summary_file_url,omitempty"`
	RequestID      null.String `boil:"request_id" json:"request_id,omitempty" toml:"request_id" yaml:"request_id,omitempty"`

	R *documentR `boil:"" json:"" toml:"" yaml:""`
	L documentL  `boil:"-" json:"-" toml:"-" yaml:"-"`
}

var DocumentColumns = struct {
	ID             string
	Status         string
	CreatedAt      string
	UpdatedAt      string
	SubmissionID   string
	ErrorInfo      string
	FirstUpdatedAt string
	SummaryFileURL string
	RequestID      string
}{
	ID:             "id",
	Status:         "status",
	CreatedAt:      "created_at",
	UpdatedAt:      "updated_at",
	SubmissionID:   "submission_id",
	ErrorInfo:      "error_info",
	FirstUpdatedAt: "first_updated_at",
	SummaryFileURL: "summary_file_url",
	RequestID:      "request_id",
}

var DocumentTableColumns = struct {
	ID             string
	Status         string
	CreatedAt      string
	UpdatedAt      string
	SubmissionID   string
	ErrorInfo      string
	FirstUpdatedAt string
	SummaryFileURL string
	RequestID      string
}{
	ID:             "document.id",
	Status:         "document.status",
	CreatedAt:      "document.created_at",
	UpdatedAt:      "document.updated_at",
	SubmissionID:   "document.submission_id",
	ErrorInfo:      "document.error_info",
	FirstUpdatedAt: "document.first_updated_at",
	SummaryFileURL: "document.summary_file_url",
	RequestID:      "document.request_id",
}

// Generated where

var DocumentWhere = struct {
	ID             whereHelperstring
	Status         whereHelperstring
	CreatedAt      whereHelpertime_Time
	UpdatedAt      whereHelpernull_Time
	SubmissionID   whereHelpernull_String
	ErrorInfo      whereHelpernull_JSON
	FirstUpdatedAt whereHelpernull_Time
	SummaryFileURL whereHelpernull_String
	RequestID      whereHelpernull_String
}{
	ID:             whereHelperstring{field: "\"parsed_loss_runs\".\"document\".\"id\""},
	Status:         whereHelperstring{field: "\"parsed_loss_runs\".\"document\".\"status\""},
	CreatedAt:      whereHelpertime_Time{field: "\"parsed_loss_runs\".\"document\".\"created_at\""},
	UpdatedAt:      whereHelpernull_Time{field: "\"parsed_loss_runs\".\"document\".\"updated_at\""},
	SubmissionID:   whereHelpernull_String{field: "\"parsed_loss_runs\".\"document\".\"submission_id\""},
	ErrorInfo:      whereHelpernull_JSON{field: "\"parsed_loss_runs\".\"document\".\"error_info\""},
	FirstUpdatedAt: whereHelpernull_Time{field: "\"parsed_loss_runs\".\"document\".\"first_updated_at\""},
	SummaryFileURL: whereHelpernull_String{field: "\"parsed_loss_runs\".\"document\".\"summary_file_url\""},
	RequestID:      whereHelpernull_String{field: "\"parsed_loss_runs\".\"document\".\"request_id\""},
}

// DocumentRels is where relationship names are stored.
var DocumentRels = struct {
	Claims                   string
	DocumentStateTransitions string
	Losses                   string
	Policies                 string
}{
	Claims:                   "Claims",
	DocumentStateTransitions: "DocumentStateTransitions",
	Losses:                   "Losses",
	Policies:                 "Policies",
}

// documentR is where relationships are stored.
type documentR struct {
	Claims                   ClaimSlice                   `boil:"Claims" json:"Claims" toml:"Claims" yaml:"Claims"`
	DocumentStateTransitions DocumentStateTransitionSlice `boil:"DocumentStateTransitions" json:"DocumentStateTransitions" toml:"DocumentStateTransitions" yaml:"DocumentStateTransitions"`
	Losses                   LossSlice                    `boil:"Losses" json:"Losses" toml:"Losses" yaml:"Losses"`
	Policies                 PolicySlice                  `boil:"Policies" json:"Policies" toml:"Policies" yaml:"Policies"`
}

// NewStruct creates a new relationship struct
func (*documentR) NewStruct() *documentR {
	return &documentR{}
}

// documentL is where Load methods for each relationship are stored.
type documentL struct{}

var (
	documentAllColumns            = []string{"id", "status", "created_at", "updated_at", "submission_id", "error_info", "first_updated_at", "summary_file_url", "request_id"}
	documentColumnsWithoutDefault = []string{"id", "status", "created_at"}
	documentColumnsWithDefault    = []string{"updated_at", "submission_id", "error_info", "first_updated_at", "summary_file_url", "request_id"}
	documentPrimaryKeyColumns     = []string{"id"}
	documentGeneratedColumns      = []string{}
)

type (
	// DocumentSlice is an alias for a slice of pointers to Document.
	// This should almost always be used instead of []Document.
	DocumentSlice []*Document
	// DocumentHook is the signature for custom Document hook methods
	DocumentHook func(context.Context, boil.ContextExecutor, *Document) error

	documentQuery struct {
		*queries.Query
	}
)

// Cache for insert, update and upsert
var (
	documentType                 = reflect.TypeOf(&Document{})
	documentMapping              = queries.MakeStructMapping(documentType)
	documentPrimaryKeyMapping, _ = queries.BindMapping(documentType, documentMapping, documentPrimaryKeyColumns)
	documentInsertCacheMut       sync.RWMutex
	documentInsertCache          = make(map[string]insertCache)
	documentUpdateCacheMut       sync.RWMutex
	documentUpdateCache          = make(map[string]updateCache)
	documentUpsertCacheMut       sync.RWMutex
	documentUpsertCache          = make(map[string]insertCache)
)

var (
	// Force time package dependency for automated UpdatedAt/CreatedAt.
	_ = time.Second
	// Force qmhelper dependency for where clause generation (which doesn't
	// always happen)
	_ = qmhelper.Where
)

var documentAfterSelectHooks []DocumentHook

var documentBeforeInsertHooks []DocumentHook
var documentAfterInsertHooks []DocumentHook

var documentBeforeUpdateHooks []DocumentHook
var documentAfterUpdateHooks []DocumentHook

var documentBeforeDeleteHooks []DocumentHook
var documentAfterDeleteHooks []DocumentHook

var documentBeforeUpsertHooks []DocumentHook
var documentAfterUpsertHooks []DocumentHook

// doAfterSelectHooks executes all "after Select" hooks.
func (o *Document) doAfterSelectHooks(ctx context.Context, exec boil.ContextExecutor) (err error) {
	if boil.HooksAreSkipped(ctx) {
		return nil
	}

	for _, hook := range documentAfterSelectHooks {
		if err := hook(ctx, exec, o); err != nil {
			return err
		}
	}

	return nil
}

// doBeforeInsertHooks executes all "before insert" hooks.
func (o *Document) doBeforeInsertHooks(ctx context.Context, exec boil.ContextExecutor) (err error) {
	if boil.HooksAreSkipped(ctx) {
		return nil
	}

	for _, hook := range documentBeforeInsertHooks {
		if err := hook(ctx, exec, o); err != nil {
			return err
		}
	}

	return nil
}

// doAfterInsertHooks executes all "after Insert" hooks.
func (o *Document) doAfterInsertHooks(ctx context.Context, exec boil.ContextExecutor) (err error) {
	if boil.HooksAreSkipped(ctx) {
		return nil
	}

	for _, hook := range documentAfterInsertHooks {
		if err := hook(ctx, exec, o); err != nil {
			return err
		}
	}

	return nil
}

// doBeforeUpdateHooks executes all "before Update" hooks.
func (o *Document) doBeforeUpdateHooks(ctx context.Context, exec boil.ContextExecutor) (err error) {
	if boil.HooksAreSkipped(ctx) {
		return nil
	}

	for _, hook := range documentBeforeUpdateHooks {
		if err := hook(ctx, exec, o); err != nil {
			return err
		}
	}

	return nil
}

// doAfterUpdateHooks executes all "after Update" hooks.
func (o *Document) doAfterUpdateHooks(ctx context.Context, exec boil.ContextExecutor) (err error) {
	if boil.HooksAreSkipped(ctx) {
		return nil
	}

	for _, hook := range documentAfterUpdateHooks {
		if err := hook(ctx, exec, o); err != nil {
			return err
		}
	}

	return nil
}

// doBeforeDeleteHooks executes all "before Delete" hooks.
func (o *Document) doBeforeDeleteHooks(ctx context.Context, exec boil.ContextExecutor) (err error) {
	if boil.HooksAreSkipped(ctx) {
		return nil
	}

	for _, hook := range documentBeforeDeleteHooks {
		if err := hook(ctx, exec, o); err != nil {
			return err
		}
	}

	return nil
}

// doAfterDeleteHooks executes all "after Delete" hooks.
func (o *Document) doAfterDeleteHooks(ctx context.Context, exec boil.ContextExecutor) (err error) {
	if boil.HooksAreSkipped(ctx) {
		return nil
	}

	for _, hook := range documentAfterDeleteHooks {
		if err := hook(ctx, exec, o); err != nil {
			return err
		}
	}

	return nil
}

// doBeforeUpsertHooks executes all "before Upsert" hooks.
func (o *Document) doBeforeUpsertHooks(ctx context.Context, exec boil.ContextExecutor) (err error) {
	if boil.HooksAreSkipped(ctx) {
		return nil
	}

	for _, hook := range documentBeforeUpsertHooks {
		if err := hook(ctx, exec, o); err != nil {
			return err
		}
	}

	return nil
}

// doAfterUpsertHooks executes all "after Upsert" hooks.
func (o *Document) doAfterUpsertHooks(ctx context.Context, exec boil.ContextExecutor) (err error) {
	if boil.HooksAreSkipped(ctx) {
		return nil
	}

	for _, hook := range documentAfterUpsertHooks {
		if err := hook(ctx, exec, o); err != nil {
			return err
		}
	}

	return nil
}

// AddDocumentHook registers your hook function for all future operations.
func AddDocumentHook(hookPoint boil.HookPoint, documentHook DocumentHook) {
	switch hookPoint {
	case boil.AfterSelectHook:
		documentAfterSelectHooks = append(documentAfterSelectHooks, documentHook)
	case boil.BeforeInsertHook:
		documentBeforeInsertHooks = append(documentBeforeInsertHooks, documentHook)
	case boil.AfterInsertHook:
		documentAfterInsertHooks = append(documentAfterInsertHooks, documentHook)
	case boil.BeforeUpdateHook:
		documentBeforeUpdateHooks = append(documentBeforeUpdateHooks, documentHook)
	case boil.AfterUpdateHook:
		documentAfterUpdateHooks = append(documentAfterUpdateHooks, documentHook)
	case boil.BeforeDeleteHook:
		documentBeforeDeleteHooks = append(documentBeforeDeleteHooks, documentHook)
	case boil.AfterDeleteHook:
		documentAfterDeleteHooks = append(documentAfterDeleteHooks, documentHook)
	case boil.BeforeUpsertHook:
		documentBeforeUpsertHooks = append(documentBeforeUpsertHooks, documentHook)
	case boil.AfterUpsertHook:
		documentAfterUpsertHooks = append(documentAfterUpsertHooks, documentHook)
	}
}

// One returns a single document record from the query.
func (q documentQuery) One(ctx context.Context, exec boil.ContextExecutor) (*Document, error) {
	o := &Document{}

	queries.SetLimit(q.Query, 1)

	err := q.Bind(ctx, exec, o)
	if err != nil {
		if errors.Is(err, sql.ErrNoRows) {
			return nil, sql.ErrNoRows
		}
		return nil, errors.Wrap(err, "parsed_loss_runs: failed to execute a one query for document")
	}

	if err := o.doAfterSelectHooks(ctx, exec); err != nil {
		return o, err
	}

	return o, nil
}

// All returns all Document records from the query.
func (q documentQuery) All(ctx context.Context, exec boil.ContextExecutor) (DocumentSlice, error) {
	var o []*Document

	err := q.Bind(ctx, exec, &o)
	if err != nil {
		return nil, errors.Wrap(err, "parsed_loss_runs: failed to assign all query results to Document slice")
	}

	if len(documentAfterSelectHooks) != 0 {
		for _, obj := range o {
			if err := obj.doAfterSelectHooks(ctx, exec); err != nil {
				return o, err
			}
		}
	}

	return o, nil
}

// Count returns the count of all Document records in the query.
func (q documentQuery) Count(ctx context.Context, exec boil.ContextExecutor) (int64, error) {
	var count int64

	queries.SetSelect(q.Query, nil)
	queries.SetCount(q.Query)

	err := q.Query.QueryRowContext(ctx, exec).Scan(&count)
	if err != nil {
		return 0, errors.Wrap(err, "parsed_loss_runs: failed to count document rows")
	}

	return count, nil
}

// Exists checks if the row exists in the table.
func (q documentQuery) Exists(ctx context.Context, exec boil.ContextExecutor) (bool, error) {
	var count int64

	queries.SetSelect(q.Query, nil)
	queries.SetCount(q.Query)
	queries.SetLimit(q.Query, 1)

	err := q.Query.QueryRowContext(ctx, exec).Scan(&count)
	if err != nil {
		return false, errors.Wrap(err, "parsed_loss_runs: failed to check if document exists")
	}

	return count > 0, nil
}

// Claims retrieves all the claim's Claims with an executor.
func (o *Document) Claims(mods ...qm.QueryMod) claimQuery {
	var queryMods []qm.QueryMod
	if len(mods) != 0 {
		queryMods = append(queryMods, mods...)
	}

	queryMods = append(queryMods,
		qm.Where("\"parsed_loss_runs\".\"claim\".\"document_id\"=?", o.ID),
	)

	return Claims(queryMods...)
}

// DocumentStateTransitions retrieves all the document_state_transition's DocumentStateTransitions with an executor.
func (o *Document) DocumentStateTransitions(mods ...qm.QueryMod) documentStateTransitionQuery {
	var queryMods []qm.QueryMod
	if len(mods) != 0 {
		queryMods = append(queryMods, mods...)
	}

	queryMods = append(queryMods,
		qm.Where("\"parsed_loss_runs\".\"document_state_transition\".\"document_id\"=?", o.ID),
	)

	return DocumentStateTransitions(queryMods...)
}

// Losses retrieves all the loss's Losses with an executor.
func (o *Document) Losses(mods ...qm.QueryMod) lossQuery {
	var queryMods []qm.QueryMod
	if len(mods) != 0 {
		queryMods = append(queryMods, mods...)
	}

	queryMods = append(queryMods,
		qm.Where("\"parsed_loss_runs\".\"loss\".\"document_id\"=?", o.ID),
	)

	return Losses(queryMods...)
}

// Policies retrieves all the policy's Policies with an executor.
func (o *Document) Policies(mods ...qm.QueryMod) policyQuery {
	var queryMods []qm.QueryMod
	if len(mods) != 0 {
		queryMods = append(queryMods, mods...)
	}

	queryMods = append(queryMods,
		qm.Where("\"parsed_loss_runs\".\"policy\".\"document_id\"=?", o.ID),
	)

	return Policies(queryMods...)
}

// LoadClaims allows an eager lookup of values, cached into the
// loaded structs of the objects. This is for a 1-M or N-M relationship.
func (documentL) LoadClaims(ctx context.Context, e boil.ContextExecutor, singular bool, maybeDocument interface{}, mods queries.Applicator) error {
	var slice []*Document
	var object *Document

	if singular {
		object = maybeDocument.(*Document)
	} else {
		slice = *maybeDocument.(*[]*Document)
	}

	args := make([]interface{}, 0, 1)
	if singular {
		if object.R == nil {
			object.R = &documentR{}
		}
		args = append(args, object.ID)
	} else {
	Outer:
		for _, obj := range slice {
			if obj.R == nil {
				obj.R = &documentR{}
			}

			for _, a := range args {
				if a == obj.ID {
					continue Outer
				}
			}

			args = append(args, obj.ID)
		}
	}

	if len(args) == 0 {
		return nil
	}

	query := NewQuery(
		qm.From(`parsed_loss_runs.claim`),
		qm.WhereIn(`parsed_loss_runs.claim.document_id in ?`, args...),
	)
	if mods != nil {
		mods.Apply(query)
	}

	results, err := query.QueryContext(ctx, e)
	if err != nil {
		return errors.Wrap(err, "failed to eager load claim")
	}

	var resultSlice []*Claim
	if err = queries.Bind(results, &resultSlice); err != nil {
		return errors.Wrap(err, "failed to bind eager loaded slice claim")
	}

	if err = results.Close(); err != nil {
		return errors.Wrap(err, "failed to close results in eager load on claim")
	}
	if err = results.Err(); err != nil {
		return errors.Wrap(err, "error occurred during iteration of eager loaded relations for claim")
	}

	if len(claimAfterSelectHooks) != 0 {
		for _, obj := range resultSlice {
			if err := obj.doAfterSelectHooks(ctx, e); err != nil {
				return err
			}
		}
	}
	if singular {
		object.R.Claims = resultSlice
		for _, foreign := range resultSlice {
			if foreign.R == nil {
				foreign.R = &claimR{}
			}
			foreign.R.Document = object
		}
		return nil
	}

	for _, foreign := range resultSlice {
		for _, local := range slice {
			if local.ID == foreign.DocumentID {
				local.R.Claims = append(local.R.Claims, foreign)
				if foreign.R == nil {
					foreign.R = &claimR{}
				}
				foreign.R.Document = local
				break
			}
		}
	}

	return nil
}

// LoadDocumentStateTransitions allows an eager lookup of values, cached into the
// loaded structs of the objects. This is for a 1-M or N-M relationship.
func (documentL) LoadDocumentStateTransitions(ctx context.Context, e boil.ContextExecutor, singular bool, maybeDocument interface{}, mods queries.Applicator) error {
	var slice []*Document
	var object *Document

	if singular {
		object = maybeDocument.(*Document)
	} else {
		slice = *maybeDocument.(*[]*Document)
	}

	args := make([]interface{}, 0, 1)
	if singular {
		if object.R == nil {
			object.R = &documentR{}
		}
		args = append(args, object.ID)
	} else {
	Outer:
		for _, obj := range slice {
			if obj.R == nil {
				obj.R = &documentR{}
			}

			for _, a := range args {
				if a == obj.ID {
					continue Outer
				}
			}

			args = append(args, obj.ID)
		}
	}

	if len(args) == 0 {
		return nil
	}

	query := NewQuery(
		qm.From(`parsed_loss_runs.document_state_transition`),
		qm.WhereIn(`parsed_loss_runs.document_state_transition.document_id in ?`, args...),
	)
	if mods != nil {
		mods.Apply(query)
	}

	results, err := query.QueryContext(ctx, e)
	if err != nil {
		return errors.Wrap(err, "failed to eager load document_state_transition")
	}

	var resultSlice []*DocumentStateTransition
	if err = queries.Bind(results, &resultSlice); err != nil {
		return errors.Wrap(err, "failed to bind eager loaded slice document_state_transition")
	}

	if err = results.Close(); err != nil {
		return errors.Wrap(err, "failed to close results in eager load on document_state_transition")
	}
	if err = results.Err(); err != nil {
		return errors.Wrap(err, "error occurred during iteration of eager loaded relations for document_state_transition")
	}

	if len(documentStateTransitionAfterSelectHooks) != 0 {
		for _, obj := range resultSlice {
			if err := obj.doAfterSelectHooks(ctx, e); err != nil {
				return err
			}
		}
	}
	if singular {
		object.R.DocumentStateTransitions = resultSlice
		for _, foreign := range resultSlice {
			if foreign.R == nil {
				foreign.R = &documentStateTransitionR{}
			}
			foreign.R.Document = object
		}
		return nil
	}

	for _, foreign := range resultSlice {
		for _, local := range slice {
			if local.ID == foreign.DocumentID {
				local.R.DocumentStateTransitions = append(local.R.DocumentStateTransitions, foreign)
				if foreign.R == nil {
					foreign.R = &documentStateTransitionR{}
				}
				foreign.R.Document = local
				break
			}
		}
	}

	return nil
}

// LoadLosses allows an eager lookup of values, cached into the
// loaded structs of the objects. This is for a 1-M or N-M relationship.
func (documentL) LoadLosses(ctx context.Context, e boil.ContextExecutor, singular bool, maybeDocument interface{}, mods queries.Applicator) error {
	var slice []*Document
	var object *Document

	if singular {
		object = maybeDocument.(*Document)
	} else {
		slice = *maybeDocument.(*[]*Document)
	}

	args := make([]interface{}, 0, 1)
	if singular {
		if object.R == nil {
			object.R = &documentR{}
		}
		args = append(args, object.ID)
	} else {
	Outer:
		for _, obj := range slice {
			if obj.R == nil {
				obj.R = &documentR{}
			}

			for _, a := range args {
				if a == obj.ID {
					continue Outer
				}
			}

			args = append(args, obj.ID)
		}
	}

	if len(args) == 0 {
		return nil
	}

	query := NewQuery(
		qm.From(`parsed_loss_runs.loss`),
		qm.WhereIn(`parsed_loss_runs.loss.document_id in ?`, args...),
	)
	if mods != nil {
		mods.Apply(query)
	}

	results, err := query.QueryContext(ctx, e)
	if err != nil {
		return errors.Wrap(err, "failed to eager load loss")
	}

	var resultSlice []*Loss
	if err = queries.Bind(results, &resultSlice); err != nil {
		return errors.Wrap(err, "failed to bind eager loaded slice loss")
	}

	if err = results.Close(); err != nil {
		return errors.Wrap(err, "failed to close results in eager load on loss")
	}
	if err = results.Err(); err != nil {
		return errors.Wrap(err, "error occurred during iteration of eager loaded relations for loss")
	}

	if len(lossAfterSelectHooks) != 0 {
		for _, obj := range resultSlice {
			if err := obj.doAfterSelectHooks(ctx, e); err != nil {
				return err
			}
		}
	}
	if singular {
		object.R.Losses = resultSlice
		for _, foreign := range resultSlice {
			if foreign.R == nil {
				foreign.R = &lossR{}
			}
			foreign.R.Document = object
		}
		return nil
	}

	for _, foreign := range resultSlice {
		for _, local := range slice {
			if local.ID == foreign.DocumentID {
				local.R.Losses = append(local.R.Losses, foreign)
				if foreign.R == nil {
					foreign.R = &lossR{}
				}
				foreign.R.Document = local
				break
			}
		}
	}

	return nil
}

// LoadPolicies allows an eager lookup of values, cached into the
// loaded structs of the objects. This is for a 1-M or N-M relationship.
func (documentL) LoadPolicies(ctx context.Context, e boil.ContextExecutor, singular bool, maybeDocument interface{}, mods queries.Applicator) error {
	var slice []*Document
	var object *Document

	if singular {
		object = maybeDocument.(*Document)
	} else {
		slice = *maybeDocument.(*[]*Document)
	}

	args := make([]interface{}, 0, 1)
	if singular {
		if object.R == nil {
			object.R = &documentR{}
		}
		args = append(args, object.ID)
	} else {
	Outer:
		for _, obj := range slice {
			if obj.R == nil {
				obj.R = &documentR{}
			}

			for _, a := range args {
				if a == obj.ID {
					continue Outer
				}
			}

			args = append(args, obj.ID)
		}
	}

	if len(args) == 0 {
		return nil
	}

	query := NewQuery(
		qm.From(`parsed_loss_runs.policy`),
		qm.WhereIn(`parsed_loss_runs.policy.document_id in ?`, args...),
	)
	if mods != nil {
		mods.Apply(query)
	}

	results, err := query.QueryContext(ctx, e)
	if err != nil {
		return errors.Wrap(err, "failed to eager load policy")
	}

	var resultSlice []*Policy
	if err = queries.Bind(results, &resultSlice); err != nil {
		return errors.Wrap(err, "failed to bind eager loaded slice policy")
	}

	if err = results.Close(); err != nil {
		return errors.Wrap(err, "failed to close results in eager load on policy")
	}
	if err = results.Err(); err != nil {
		return errors.Wrap(err, "error occurred during iteration of eager loaded relations for policy")
	}

	if len(policyAfterSelectHooks) != 0 {
		for _, obj := range resultSlice {
			if err := obj.doAfterSelectHooks(ctx, e); err != nil {
				return err
			}
		}
	}
	if singular {
		object.R.Policies = resultSlice
		for _, foreign := range resultSlice {
			if foreign.R == nil {
				foreign.R = &policyR{}
			}
			foreign.R.Document = object
		}
		return nil
	}

	for _, foreign := range resultSlice {
		for _, local := range slice {
			if local.ID == foreign.DocumentID {
				local.R.Policies = append(local.R.Policies, foreign)
				if foreign.R == nil {
					foreign.R = &policyR{}
				}
				foreign.R.Document = local
				break
			}
		}
	}

	return nil
}

// AddClaims adds the given related objects to the existing relationships
// of the document, optionally inserting them as new records.
// Appends related to o.R.Claims.
// Sets related.R.Document appropriately.
func (o *Document) AddClaims(ctx context.Context, exec boil.ContextExecutor, insert bool, related ...*Claim) error {
	var err error
	for _, rel := range related {
		if insert {
			rel.DocumentID = o.ID
			if err = rel.Insert(ctx, exec, boil.Infer()); err != nil {
				return errors.Wrap(err, "failed to insert into foreign table")
			}
		} else {
			updateQuery := fmt.Sprintf(
				"UPDATE \"parsed_loss_runs\".\"claim\" SET %s WHERE %s",
				strmangle.SetParamNames("\"", "\"", 1, []string{"document_id"}),
				strmangle.WhereClause("\"", "\"", 2, claimPrimaryKeyColumns),
			)
			values := []interface{}{o.ID, rel.ID}

			if boil.IsDebug(ctx) {
				writer := boil.DebugWriterFrom(ctx)
				fmt.Fprintln(writer, updateQuery)
				fmt.Fprintln(writer, values)
			}
			if _, err = exec.ExecContext(ctx, updateQuery, values...); err != nil {
				return errors.Wrap(err, "failed to update foreign table")
			}

			rel.DocumentID = o.ID
		}
	}

	if o.R == nil {
		o.R = &documentR{
			Claims: related,
		}
	} else {
		o.R.Claims = append(o.R.Claims, related...)
	}

	for _, rel := range related {
		if rel.R == nil {
			rel.R = &claimR{
				Document: o,
			}
		} else {
			rel.R.Document = o
		}
	}
	return nil
}

// AddDocumentStateTransitions adds the given related objects to the existing relationships
// of the document, optionally inserting them as new records.
// Appends related to o.R.DocumentStateTransitions.
// Sets related.R.Document appropriately.
func (o *Document) AddDocumentStateTransitions(ctx context.Context, exec boil.ContextExecutor, insert bool, related ...*DocumentStateTransition) error {
	var err error
	for _, rel := range related {
		if insert {
			rel.DocumentID = o.ID
			if err = rel.Insert(ctx, exec, boil.Infer()); err != nil {
				return errors.Wrap(err, "failed to insert into foreign table")
			}
		} else {
			updateQuery := fmt.Sprintf(
				"UPDATE \"parsed_loss_runs\".\"document_state_transition\" SET %s WHERE %s",
				strmangle.SetParamNames("\"", "\"", 1, []string{"document_id"}),
				strmangle.WhereClause("\"", "\"", 2, documentStateTransitionPrimaryKeyColumns),
			)
			values := []interface{}{o.ID, rel.ID}

			if boil.IsDebug(ctx) {
				writer := boil.DebugWriterFrom(ctx)
				fmt.Fprintln(writer, updateQuery)
				fmt.Fprintln(writer, values)
			}
			if _, err = exec.ExecContext(ctx, updateQuery, values...); err != nil {
				return errors.Wrap(err, "failed to update foreign table")
			}

			rel.DocumentID = o.ID
		}
	}

	if o.R == nil {
		o.R = &documentR{
			DocumentStateTransitions: related,
		}
	} else {
		o.R.DocumentStateTransitions = append(o.R.DocumentStateTransitions, related...)
	}

	for _, rel := range related {
		if rel.R == nil {
			rel.R = &documentStateTransitionR{
				Document: o,
			}
		} else {
			rel.R.Document = o
		}
	}
	return nil
}

// AddLosses adds the given related objects to the existing relationships
// of the document, optionally inserting them as new records.
// Appends related to o.R.Losses.
// Sets related.R.Document appropriately.
func (o *Document) AddLosses(ctx context.Context, exec boil.ContextExecutor, insert bool, related ...*Loss) error {
	var err error
	for _, rel := range related {
		if insert {
			rel.DocumentID = o.ID
			if err = rel.Insert(ctx, exec, boil.Infer()); err != nil {
				return errors.Wrap(err, "failed to insert into foreign table")
			}
		} else {
			updateQuery := fmt.Sprintf(
				"UPDATE \"parsed_loss_runs\".\"loss\" SET %s WHERE %s",
				strmangle.SetParamNames("\"", "\"", 1, []string{"document_id"}),
				strmangle.WhereClause("\"", "\"", 2, lossPrimaryKeyColumns),
			)
			values := []interface{}{o.ID, rel.ID}

			if boil.IsDebug(ctx) {
				writer := boil.DebugWriterFrom(ctx)
				fmt.Fprintln(writer, updateQuery)
				fmt.Fprintln(writer, values)
			}
			if _, err = exec.ExecContext(ctx, updateQuery, values...); err != nil {
				return errors.Wrap(err, "failed to update foreign table")
			}

			rel.DocumentID = o.ID
		}
	}

	if o.R == nil {
		o.R = &documentR{
			Losses: related,
		}
	} else {
		o.R.Losses = append(o.R.Losses, related...)
	}

	for _, rel := range related {
		if rel.R == nil {
			rel.R = &lossR{
				Document: o,
			}
		} else {
			rel.R.Document = o
		}
	}
	return nil
}

// AddPolicies adds the given related objects to the existing relationships
// of the document, optionally inserting them as new records.
// Appends related to o.R.Policies.
// Sets related.R.Document appropriately.
func (o *Document) AddPolicies(ctx context.Context, exec boil.ContextExecutor, insert bool, related ...*Policy) error {
	var err error
	for _, rel := range related {
		if insert {
			rel.DocumentID = o.ID
			if err = rel.Insert(ctx, exec, boil.Infer()); err != nil {
				return errors.Wrap(err, "failed to insert into foreign table")
			}
		} else {
			updateQuery := fmt.Sprintf(
				"UPDATE \"parsed_loss_runs\".\"policy\" SET %s WHERE %s",
				strmangle.SetParamNames("\"", "\"", 1, []string{"document_id"}),
				strmangle.WhereClause("\"", "\"", 2, policyPrimaryKeyColumns),
			)
			values := []interface{}{o.ID, rel.ID}

			if boil.IsDebug(ctx) {
				writer := boil.DebugWriterFrom(ctx)
				fmt.Fprintln(writer, updateQuery)
				fmt.Fprintln(writer, values)
			}
			if _, err = exec.ExecContext(ctx, updateQuery, values...); err != nil {
				return errors.Wrap(err, "failed to update foreign table")
			}

			rel.DocumentID = o.ID
		}
	}

	if o.R == nil {
		o.R = &documentR{
			Policies: related,
		}
	} else {
		o.R.Policies = append(o.R.Policies, related...)
	}

	for _, rel := range related {
		if rel.R == nil {
			rel.R = &policyR{
				Document: o,
			}
		} else {
			rel.R.Document = o
		}
	}
	return nil
}

// Documents retrieves all the records using an executor.
func Documents(mods ...qm.QueryMod) documentQuery {
	mods = append(mods, qm.From("\"parsed_loss_runs\".\"document\""))
	q := NewQuery(mods...)
	if len(queries.GetSelect(q)) == 0 {
		queries.SetSelect(q, []string{"\"parsed_loss_runs\".\"document\".*"})
	}

	return documentQuery{q}
}

// FindDocument retrieves a single record by ID with an executor.
// If selectCols is empty Find will return all columns.
func FindDocument(ctx context.Context, exec boil.ContextExecutor, iD string, selectCols ...string) (*Document, error) {
	documentObj := &Document{}

	sel := "*"
	if len(selectCols) > 0 {
		sel = strings.Join(strmangle.IdentQuoteSlice(dialect.LQ, dialect.RQ, selectCols), ",")
	}
	query := fmt.Sprintf(
		"select %s from \"parsed_loss_runs\".\"document\" where \"id\"=$1", sel,
	)

	q := queries.Raw(query, iD)

	err := q.Bind(ctx, exec, documentObj)
	if err != nil {
		if errors.Is(err, sql.ErrNoRows) {
			return nil, sql.ErrNoRows
		}
		return nil, errors.Wrap(err, "parsed_loss_runs: unable to select from document")
	}

	if err = documentObj.doAfterSelectHooks(ctx, exec); err != nil {
		return documentObj, err
	}

	return documentObj, nil
}

// Insert a single record using an executor.
// See boil.Columns.InsertColumnSet documentation to understand column list inference for inserts.
func (o *Document) Insert(ctx context.Context, exec boil.ContextExecutor, columns boil.Columns) error {
	if o == nil {
		return errors.New("parsed_loss_runs: no document provided for insertion")
	}

	var err error
	if !boil.TimestampsAreSkipped(ctx) {
		currTime := time.Now().In(boil.GetLocation())

		if o.CreatedAt.IsZero() {
			o.CreatedAt = currTime
		}
		if queries.MustTime(o.UpdatedAt).IsZero() {
			queries.SetScanner(&o.UpdatedAt, currTime)
		}
	}

	if err := o.doBeforeInsertHooks(ctx, exec); err != nil {
		return err
	}

	nzDefaults := queries.NonZeroDefaultSet(documentColumnsWithDefault, o)

	key := makeCacheKey(columns, nzDefaults)
	documentInsertCacheMut.RLock()
	cache, cached := documentInsertCache[key]
	documentInsertCacheMut.RUnlock()

	if !cached {
		wl, returnColumns := columns.InsertColumnSet(
			documentAllColumns,
			documentColumnsWithDefault,
			documentColumnsWithoutDefault,
			nzDefaults,
		)

		cache.valueMapping, err = queries.BindMapping(documentType, documentMapping, wl)
		if err != nil {
			return err
		}
		cache.retMapping, err = queries.BindMapping(documentType, documentMapping, returnColumns)
		if err != nil {
			return err
		}
		if len(wl) != 0 {
			cache.query = fmt.Sprintf("INSERT INTO \"parsed_loss_runs\".\"document\" (\"%s\") %%sVALUES (%s)%%s", strings.Join(wl, "\",\""), strmangle.Placeholders(dialect.UseIndexPlaceholders, len(wl), 1, 1))
		} else {
			cache.query = "INSERT INTO \"parsed_loss_runs\".\"document\" %sDEFAULT VALUES%s"
		}

		var queryOutput, queryReturning string

		if len(cache.retMapping) != 0 {
			queryReturning = fmt.Sprintf(" RETURNING \"%s\"", strings.Join(returnColumns, "\",\""))
		}

		cache.query = fmt.Sprintf(cache.query, queryOutput, queryReturning)
	}

	value := reflect.Indirect(reflect.ValueOf(o))
	vals := queries.ValuesFromMapping(value, cache.valueMapping)

	if boil.IsDebug(ctx) {
		writer := boil.DebugWriterFrom(ctx)
		fmt.Fprintln(writer, cache.query)
		fmt.Fprintln(writer, vals)
	}

	if len(cache.retMapping) != 0 {
		err = exec.QueryRowContext(ctx, cache.query, vals...).Scan(queries.PtrsFromMapping(value, cache.retMapping)...)
	} else {
		_, err = exec.ExecContext(ctx, cache.query, vals...)
	}

	if err != nil {
		return errors.Wrap(err, "parsed_loss_runs: unable to insert into document")
	}

	if !cached {
		documentInsertCacheMut.Lock()
		documentInsertCache[key] = cache
		documentInsertCacheMut.Unlock()
	}

	return o.doAfterInsertHooks(ctx, exec)
}

// Update uses an executor to update the Document.
// See boil.Columns.UpdateColumnSet documentation to understand column list inference for updates.
// Update does not automatically update the record in case of default values. Use .Reload() to refresh the records.
func (o *Document) Update(ctx context.Context, exec boil.ContextExecutor, columns boil.Columns) (int64, error) {
	if !boil.TimestampsAreSkipped(ctx) {
		currTime := time.Now().In(boil.GetLocation())

		queries.SetScanner(&o.UpdatedAt, currTime)
	}

	var err error
	if err = o.doBeforeUpdateHooks(ctx, exec); err != nil {
		return 0, err
	}
	key := makeCacheKey(columns, nil)
	documentUpdateCacheMut.RLock()
	cache, cached := documentUpdateCache[key]
	documentUpdateCacheMut.RUnlock()

	if !cached {
		wl := columns.UpdateColumnSet(
			documentAllColumns,
			documentPrimaryKeyColumns,
		)

		if !columns.IsWhitelist() {
			wl = strmangle.SetComplement(wl, []string{"created_at"})
		}
		if len(wl) == 0 {
			return 0, errors.New("parsed_loss_runs: unable to update document, could not build whitelist")
		}

		cache.query = fmt.Sprintf("UPDATE \"parsed_loss_runs\".\"document\" SET %s WHERE %s",
			strmangle.SetParamNames("\"", "\"", 1, wl),
			strmangle.WhereClause("\"", "\"", len(wl)+1, documentPrimaryKeyColumns),
		)
		cache.valueMapping, err = queries.BindMapping(documentType, documentMapping, append(wl, documentPrimaryKeyColumns...))
		if err != nil {
			return 0, err
		}
	}

	values := queries.ValuesFromMapping(reflect.Indirect(reflect.ValueOf(o)), cache.valueMapping)

	if boil.IsDebug(ctx) {
		writer := boil.DebugWriterFrom(ctx)
		fmt.Fprintln(writer, cache.query)
		fmt.Fprintln(writer, values)
	}
	var result sql.Result
	result, err = exec.ExecContext(ctx, cache.query, values...)
	if err != nil {
		return 0, errors.Wrap(err, "parsed_loss_runs: unable to update document row")
	}

	rowsAff, err := result.RowsAffected()
	if err != nil {
		return 0, errors.Wrap(err, "parsed_loss_runs: failed to get rows affected by update for document")
	}

	if !cached {
		documentUpdateCacheMut.Lock()
		documentUpdateCache[key] = cache
		documentUpdateCacheMut.Unlock()
	}

	return rowsAff, o.doAfterUpdateHooks(ctx, exec)
}

// UpdateAll updates all rows with the specified column values.
func (q documentQuery) UpdateAll(ctx context.Context, exec boil.ContextExecutor, cols M) (int64, error) {
	queries.SetUpdate(q.Query, cols)

	result, err := q.Query.ExecContext(ctx, exec)
	if err != nil {
		return 0, errors.Wrap(err, "parsed_loss_runs: unable to update all for document")
	}

	rowsAff, err := result.RowsAffected()
	if err != nil {
		return 0, errors.Wrap(err, "parsed_loss_runs: unable to retrieve rows affected for document")
	}

	return rowsAff, nil
}

// UpdateAll updates all rows with the specified column values, using an executor.
func (o DocumentSlice) UpdateAll(ctx context.Context, exec boil.ContextExecutor, cols M) (int64, error) {
	ln := int64(len(o))
	if ln == 0 {
		return 0, nil
	}

	if len(cols) == 0 {
		return 0, errors.New("parsed_loss_runs: update all requires at least one column argument")
	}

	colNames := make([]string, len(cols))
	args := make([]interface{}, len(cols))

	i := 0
	for name, value := range cols {
		colNames[i] = name
		args[i] = value
		i++
	}

	// Append all of the primary key values for each column
	for _, obj := range o {
		pkeyArgs := queries.ValuesFromMapping(reflect.Indirect(reflect.ValueOf(obj)), documentPrimaryKeyMapping)
		args = append(args, pkeyArgs...)
	}

	sql := fmt.Sprintf("UPDATE \"parsed_loss_runs\".\"document\" SET %s WHERE %s",
		strmangle.SetParamNames("\"", "\"", 1, colNames),
		strmangle.WhereClauseRepeated(string(dialect.LQ), string(dialect.RQ), len(colNames)+1, documentPrimaryKeyColumns, len(o)))

	if boil.IsDebug(ctx) {
		writer := boil.DebugWriterFrom(ctx)
		fmt.Fprintln(writer, sql)
		fmt.Fprintln(writer, args...)
	}
	result, err := exec.ExecContext(ctx, sql, args...)
	if err != nil {
		return 0, errors.Wrap(err, "parsed_loss_runs: unable to update all in document slice")
	}

	rowsAff, err := result.RowsAffected()
	if err != nil {
		return 0, errors.Wrap(err, "parsed_loss_runs: unable to retrieve rows affected all in update all document")
	}
	return rowsAff, nil
}

// Upsert attempts an insert using an executor, and does an update or ignore on conflict.
// See boil.Columns documentation for how to properly use updateColumns and insertColumns.
func (o *Document) Upsert(ctx context.Context, exec boil.ContextExecutor, updateOnConflict bool, conflictColumns []string, updateColumns, insertColumns boil.Columns) error {
	if o == nil {
		return errors.New("parsed_loss_runs: no document provided for upsert")
	}
	if !boil.TimestampsAreSkipped(ctx) {
		currTime := time.Now().In(boil.GetLocation())

		if o.CreatedAt.IsZero() {
			o.CreatedAt = currTime
		}
		queries.SetScanner(&o.UpdatedAt, currTime)
	}

	if err := o.doBeforeUpsertHooks(ctx, exec); err != nil {
		return err
	}

	nzDefaults := queries.NonZeroDefaultSet(documentColumnsWithDefault, o)

	// Build cache key in-line uglily - mysql vs psql problems
	buf := strmangle.GetBuffer()
	if updateOnConflict {
		buf.WriteByte('t')
	} else {
		buf.WriteByte('f')
	}
	buf.WriteByte('.')
	for _, c := range conflictColumns {
		buf.WriteString(c)
	}
	buf.WriteByte('.')
	buf.WriteString(strconv.Itoa(updateColumns.Kind))
	for _, c := range updateColumns.Cols {
		buf.WriteString(c)
	}
	buf.WriteByte('.')
	buf.WriteString(strconv.Itoa(insertColumns.Kind))
	for _, c := range insertColumns.Cols {
		buf.WriteString(c)
	}
	buf.WriteByte('.')
	for _, c := range nzDefaults {
		buf.WriteString(c)
	}
	key := buf.String()
	strmangle.PutBuffer(buf)

	documentUpsertCacheMut.RLock()
	cache, cached := documentUpsertCache[key]
	documentUpsertCacheMut.RUnlock()

	var err error

	if !cached {
		insert, ret := insertColumns.InsertColumnSet(
			documentAllColumns,
			documentColumnsWithDefault,
			documentColumnsWithoutDefault,
			nzDefaults,
		)

		update := updateColumns.UpdateColumnSet(
			documentAllColumns,
			documentPrimaryKeyColumns,
		)

		if updateOnConflict && len(update) == 0 {
			return errors.New("parsed_loss_runs: unable to upsert document, could not build update column list")
		}

		conflict := conflictColumns
		if len(conflict) == 0 {
			conflict = make([]string, len(documentPrimaryKeyColumns))
			copy(conflict, documentPrimaryKeyColumns)
		}
		cache.query = buildUpsertQueryPostgres(dialect, "\"parsed_loss_runs\".\"document\"", updateOnConflict, ret, update, conflict, insert)

		cache.valueMapping, err = queries.BindMapping(documentType, documentMapping, insert)
		if err != nil {
			return err
		}
		if len(ret) != 0 {
			cache.retMapping, err = queries.BindMapping(documentType, documentMapping, ret)
			if err != nil {
				return err
			}
		}
	}

	value := reflect.Indirect(reflect.ValueOf(o))
	vals := queries.ValuesFromMapping(value, cache.valueMapping)
	var returns []interface{}
	if len(cache.retMapping) != 0 {
		returns = queries.PtrsFromMapping(value, cache.retMapping)
	}

	if boil.IsDebug(ctx) {
		writer := boil.DebugWriterFrom(ctx)
		fmt.Fprintln(writer, cache.query)
		fmt.Fprintln(writer, vals)
	}
	if len(cache.retMapping) != 0 {
		err = exec.QueryRowContext(ctx, cache.query, vals...).Scan(returns...)
		if errors.Is(err, sql.ErrNoRows) {
			err = nil // Postgres doesn't return anything when there's no update
		}
	} else {
		_, err = exec.ExecContext(ctx, cache.query, vals...)
	}
	if err != nil {
		return errors.Wrap(err, "parsed_loss_runs: unable to upsert document")
	}

	if !cached {
		documentUpsertCacheMut.Lock()
		documentUpsertCache[key] = cache
		documentUpsertCacheMut.Unlock()
	}

	return o.doAfterUpsertHooks(ctx, exec)
}

// Delete deletes a single Document record with an executor.
// Delete will match against the primary key column to find the record to delete.
func (o *Document) Delete(ctx context.Context, exec boil.ContextExecutor) (int64, error) {
	if o == nil {
		return 0, errors.New("parsed_loss_runs: no Document provided for delete")
	}

	if err := o.doBeforeDeleteHooks(ctx, exec); err != nil {
		return 0, err
	}

	args := queries.ValuesFromMapping(reflect.Indirect(reflect.ValueOf(o)), documentPrimaryKeyMapping)
	sql := "DELETE FROM \"parsed_loss_runs\".\"document\" WHERE \"id\"=$1"

	if boil.IsDebug(ctx) {
		writer := boil.DebugWriterFrom(ctx)
		fmt.Fprintln(writer, sql)
		fmt.Fprintln(writer, args...)
	}
	result, err := exec.ExecContext(ctx, sql, args...)
	if err != nil {
		return 0, errors.Wrap(err, "parsed_loss_runs: unable to delete from document")
	}

	rowsAff, err := result.RowsAffected()
	if err != nil {
		return 0, errors.Wrap(err, "parsed_loss_runs: failed to get rows affected by delete for document")
	}

	if err := o.doAfterDeleteHooks(ctx, exec); err != nil {
		return 0, err
	}

	return rowsAff, nil
}

// DeleteAll deletes all matching rows.
func (q documentQuery) DeleteAll(ctx context.Context, exec boil.ContextExecutor) (int64, error) {
	if q.Query == nil {
		return 0, errors.New("parsed_loss_runs: no documentQuery provided for delete all")
	}

	queries.SetDelete(q.Query)

	result, err := q.Query.ExecContext(ctx, exec)
	if err != nil {
		return 0, errors.Wrap(err, "parsed_loss_runs: unable to delete all from document")
	}

	rowsAff, err := result.RowsAffected()
	if err != nil {
		return 0, errors.Wrap(err, "parsed_loss_runs: failed to get rows affected by deleteall for document")
	}

	return rowsAff, nil
}

// DeleteAll deletes all rows in the slice, using an executor.
func (o DocumentSlice) DeleteAll(ctx context.Context, exec boil.ContextExecutor) (int64, error) {
	if len(o) == 0 {
		return 0, nil
	}

	if len(documentBeforeDeleteHooks) != 0 {
		for _, obj := range o {
			if err := obj.doBeforeDeleteHooks(ctx, exec); err != nil {
				return 0, err
			}
		}
	}

	var args []interface{}
	for _, obj := range o {
		pkeyArgs := queries.ValuesFromMapping(reflect.Indirect(reflect.ValueOf(obj)), documentPrimaryKeyMapping)
		args = append(args, pkeyArgs...)
	}

	sql := "DELETE FROM \"parsed_loss_runs\".\"document\" WHERE " +
		strmangle.WhereClauseRepeated(string(dialect.LQ), string(dialect.RQ), 1, documentPrimaryKeyColumns, len(o))

	if boil.IsDebug(ctx) {
		writer := boil.DebugWriterFrom(ctx)
		fmt.Fprintln(writer, sql)
		fmt.Fprintln(writer, args)
	}
	result, err := exec.ExecContext(ctx, sql, args...)
	if err != nil {
		return 0, errors.Wrap(err, "parsed_loss_runs: unable to delete all from document slice")
	}

	rowsAff, err := result.RowsAffected()
	if err != nil {
		return 0, errors.Wrap(err, "parsed_loss_runs: failed to get rows affected by deleteall for document")
	}

	if len(documentAfterDeleteHooks) != 0 {
		for _, obj := range o {
			if err := obj.doAfterDeleteHooks(ctx, exec); err != nil {
				return 0, err
			}
		}
	}

	return rowsAff, nil
}

// Reload refetches the object from the database
// using the primary keys with an executor.
func (o *Document) Reload(ctx context.Context, exec boil.ContextExecutor) error {
	ret, err := FindDocument(ctx, exec, o.ID)
	if err != nil {
		return err
	}

	*o = *ret
	return nil
}

// ReloadAll refetches every row with matching primary key column values
// and overwrites the original object slice with the newly updated slice.
func (o *DocumentSlice) ReloadAll(ctx context.Context, exec boil.ContextExecutor) error {
	if o == nil || len(*o) == 0 {
		return nil
	}

	slice := DocumentSlice{}
	var args []interface{}
	for _, obj := range *o {
		pkeyArgs := queries.ValuesFromMapping(reflect.Indirect(reflect.ValueOf(obj)), documentPrimaryKeyMapping)
		args = append(args, pkeyArgs...)
	}

	sql := "SELECT \"parsed_loss_runs\".\"document\".* FROM \"parsed_loss_runs\".\"document\" WHERE " +
		strmangle.WhereClauseRepeated(string(dialect.LQ), string(dialect.RQ), 1, documentPrimaryKeyColumns, len(*o))

	q := queries.Raw(sql, args...)

	err := q.Bind(ctx, exec, &slice)
	if err != nil {
		return errors.Wrap(err, "parsed_loss_runs: unable to reload all in DocumentSlice")
	}

	*o = slice

	return nil
}

// DocumentExists checks if the Document row exists.
func DocumentExists(ctx context.Context, exec boil.ContextExecutor, iD string) (bool, error) {
	var exists bool
	sql := "select exists(select 1 from \"parsed_loss_runs\".\"document\" where \"id\"=$1 limit 1)"

	if boil.IsDebug(ctx) {
		writer := boil.DebugWriterFrom(ctx)
		fmt.Fprintln(writer, sql)
		fmt.Fprintln(writer, iD)
	}
	row := exec.QueryRowContext(ctx, sql, iD)

	err := row.Scan(&exists)
	if err != nil {
		return false, errors.Wrap(err, "parsed_loss_runs: unable to check if document exists")
	}

	return exists, nil
}
