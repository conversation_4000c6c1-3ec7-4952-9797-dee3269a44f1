// Code generated by SQLBoiler (https://github.com/volatiletech/sqlboiler). DO NOT EDIT.
// This file is meant to be re-generated in place and/or deleted at any time.

package insurance_bundle

import (
	"context"
	"database/sql"
	"fmt"
	"reflect"
	"strconv"
	"strings"
	"sync"
	"time"

	"github.com/friendsofgo/errors"
	"github.com/volatiletech/null/v8"
	"github.com/volatiletech/sqlboiler/v4/boil"
	"github.com/volatiletech/sqlboiler/v4/queries"
	"github.com/volatiletech/sqlboiler/v4/queries/qm"
	"github.com/volatiletech/sqlboiler/v4/queries/qmhelper"
	"github.com/volatiletech/sqlboiler/v4/types"
	"github.com/volatiletech/strmangle"
)

// InsuranceBundleSegment is an object representing the database table.
type InsuranceBundleSegment struct {
	ID                   string     `boil:"id" json:"id" toml:"id" yaml:"id"`
	InsuranceBundleID    string     `boil:"insurance_bundle_id" json:"insurance_bundle_id" toml:"insurance_bundle_id" yaml:"insurance_bundle_id"`
	IntervalStart        time.Time  `boil:"interval_start" json:"interval_start" toml:"interval_start" yaml:"interval_start"`
	IntervalEnd          time.Time  `boil:"interval_end" json:"interval_end" toml:"interval_end" yaml:"interval_end"`
	PrimaryInsured       types.JSON `boil:"primary_insured" json:"primary_insured" toml:"primary_insured" yaml:"primary_insured"`
	CoverageCriteria     types.JSON `boil:"coverage_criteria" json:"coverage_criteria" toml:"coverage_criteria" yaml:"coverage_criteria"`
	CreatedAt            time.Time  `boil:"created_at" json:"created_at" toml:"created_at" yaml:"created_at"`
	UpdatedAt            time.Time  `boil:"updated_at" json:"updated_at" toml:"updated_at" yaml:"updated_at"`
	PrimaryInsuredID     string     `boil:"primary_insured_id" json:"primary_insured_id" toml:"primary_insured_id" yaml:"primary_insured_id"`
	NoticeOfCancellation null.JSON  `boil:"notice_of_cancellation" json:"notice_of_cancellation,omitempty" toml:"notice_of_cancellation" yaml:"notice_of_cancellation,omitempty"`

	R *insuranceBundleSegmentR `boil:"" json:"" toml:"" yaml:""`
	L insuranceBundleSegmentL  `boil:"-" json:"-" toml:"-" yaml:"-"`
}

var InsuranceBundleSegmentColumns = struct {
	ID                   string
	InsuranceBundleID    string
	IntervalStart        string
	IntervalEnd          string
	PrimaryInsured       string
	CoverageCriteria     string
	CreatedAt            string
	UpdatedAt            string
	PrimaryInsuredID     string
	NoticeOfCancellation string
}{
	ID:                   "id",
	InsuranceBundleID:    "insurance_bundle_id",
	IntervalStart:        "interval_start",
	IntervalEnd:          "interval_end",
	PrimaryInsured:       "primary_insured",
	CoverageCriteria:     "coverage_criteria",
	CreatedAt:            "created_at",
	UpdatedAt:            "updated_at",
	PrimaryInsuredID:     "primary_insured_id",
	NoticeOfCancellation: "notice_of_cancellation",
}

var InsuranceBundleSegmentTableColumns = struct {
	ID                   string
	InsuranceBundleID    string
	IntervalStart        string
	IntervalEnd          string
	PrimaryInsured       string
	CoverageCriteria     string
	CreatedAt            string
	UpdatedAt            string
	PrimaryInsuredID     string
	NoticeOfCancellation string
}{
	ID:                   "insurance_bundle_segment.id",
	InsuranceBundleID:    "insurance_bundle_segment.insurance_bundle_id",
	IntervalStart:        "insurance_bundle_segment.interval_start",
	IntervalEnd:          "insurance_bundle_segment.interval_end",
	PrimaryInsured:       "insurance_bundle_segment.primary_insured",
	CoverageCriteria:     "insurance_bundle_segment.coverage_criteria",
	CreatedAt:            "insurance_bundle_segment.created_at",
	UpdatedAt:            "insurance_bundle_segment.updated_at",
	PrimaryInsuredID:     "insurance_bundle_segment.primary_insured_id",
	NoticeOfCancellation: "insurance_bundle_segment.notice_of_cancellation",
}

// Generated where

var InsuranceBundleSegmentWhere = struct {
	ID                   whereHelperstring
	InsuranceBundleID    whereHelperstring
	IntervalStart        whereHelpertime_Time
	IntervalEnd          whereHelpertime_Time
	PrimaryInsured       whereHelpertypes_JSON
	CoverageCriteria     whereHelpertypes_JSON
	CreatedAt            whereHelpertime_Time
	UpdatedAt            whereHelpertime_Time
	PrimaryInsuredID     whereHelperstring
	NoticeOfCancellation whereHelpernull_JSON
}{
	ID:                   whereHelperstring{field: "\"insurance_bundle\".\"insurance_bundle_segment\".\"id\""},
	InsuranceBundleID:    whereHelperstring{field: "\"insurance_bundle\".\"insurance_bundle_segment\".\"insurance_bundle_id\""},
	IntervalStart:        whereHelpertime_Time{field: "\"insurance_bundle\".\"insurance_bundle_segment\".\"interval_start\""},
	IntervalEnd:          whereHelpertime_Time{field: "\"insurance_bundle\".\"insurance_bundle_segment\".\"interval_end\""},
	PrimaryInsured:       whereHelpertypes_JSON{field: "\"insurance_bundle\".\"insurance_bundle_segment\".\"primary_insured\""},
	CoverageCriteria:     whereHelpertypes_JSON{field: "\"insurance_bundle\".\"insurance_bundle_segment\".\"coverage_criteria\""},
	CreatedAt:            whereHelpertime_Time{field: "\"insurance_bundle\".\"insurance_bundle_segment\".\"created_at\""},
	UpdatedAt:            whereHelpertime_Time{field: "\"insurance_bundle\".\"insurance_bundle_segment\".\"updated_at\""},
	PrimaryInsuredID:     whereHelperstring{field: "\"insurance_bundle\".\"insurance_bundle_segment\".\"primary_insured_id\""},
	NoticeOfCancellation: whereHelpernull_JSON{field: "\"insurance_bundle\".\"insurance_bundle_segment\".\"notice_of_cancellation\""},
}

// InsuranceBundleSegmentRels is where relationship names are stored.
var InsuranceBundleSegmentRels = struct {
	InsuranceBundle string
	Policies        string
}{
	InsuranceBundle: "InsuranceBundle",
	Policies:        "Policies",
}

// insuranceBundleSegmentR is where relationships are stored.
type insuranceBundleSegmentR struct {
	InsuranceBundle *InsuranceBundle `boil:"InsuranceBundle" json:"InsuranceBundle" toml:"InsuranceBundle" yaml:"InsuranceBundle"`
	Policies        PolicySlice      `boil:"Policies" json:"Policies" toml:"Policies" yaml:"Policies"`
}

// NewStruct creates a new relationship struct
func (*insuranceBundleSegmentR) NewStruct() *insuranceBundleSegmentR {
	return &insuranceBundleSegmentR{}
}

// insuranceBundleSegmentL is where Load methods for each relationship are stored.
type insuranceBundleSegmentL struct{}

var (
	insuranceBundleSegmentAllColumns            = []string{"id", "insurance_bundle_id", "interval_start", "interval_end", "primary_insured", "coverage_criteria", "created_at", "updated_at", "primary_insured_id", "notice_of_cancellation"}
	insuranceBundleSegmentColumnsWithoutDefault = []string{"id", "insurance_bundle_id", "interval_start", "interval_end", "primary_insured", "coverage_criteria", "created_at", "updated_at", "primary_insured_id"}
	insuranceBundleSegmentColumnsWithDefault    = []string{"notice_of_cancellation"}
	insuranceBundleSegmentPrimaryKeyColumns     = []string{"id"}
	insuranceBundleSegmentGeneratedColumns      = []string{}
)

type (
	// InsuranceBundleSegmentSlice is an alias for a slice of pointers to InsuranceBundleSegment.
	// This should almost always be used instead of []InsuranceBundleSegment.
	InsuranceBundleSegmentSlice []*InsuranceBundleSegment
	// InsuranceBundleSegmentHook is the signature for custom InsuranceBundleSegment hook methods
	InsuranceBundleSegmentHook func(context.Context, boil.ContextExecutor, *InsuranceBundleSegment) error

	insuranceBundleSegmentQuery struct {
		*queries.Query
	}
)

// Cache for insert, update and upsert
var (
	insuranceBundleSegmentType                 = reflect.TypeOf(&InsuranceBundleSegment{})
	insuranceBundleSegmentMapping              = queries.MakeStructMapping(insuranceBundleSegmentType)
	insuranceBundleSegmentPrimaryKeyMapping, _ = queries.BindMapping(insuranceBundleSegmentType, insuranceBundleSegmentMapping, insuranceBundleSegmentPrimaryKeyColumns)
	insuranceBundleSegmentInsertCacheMut       sync.RWMutex
	insuranceBundleSegmentInsertCache          = make(map[string]insertCache)
	insuranceBundleSegmentUpdateCacheMut       sync.RWMutex
	insuranceBundleSegmentUpdateCache          = make(map[string]updateCache)
	insuranceBundleSegmentUpsertCacheMut       sync.RWMutex
	insuranceBundleSegmentUpsertCache          = make(map[string]insertCache)
)

var (
	// Force time package dependency for automated UpdatedAt/CreatedAt.
	_ = time.Second
	// Force qmhelper dependency for where clause generation (which doesn't
	// always happen)
	_ = qmhelper.Where
)

var insuranceBundleSegmentAfterSelectHooks []InsuranceBundleSegmentHook

var insuranceBundleSegmentBeforeInsertHooks []InsuranceBundleSegmentHook
var insuranceBundleSegmentAfterInsertHooks []InsuranceBundleSegmentHook

var insuranceBundleSegmentBeforeUpdateHooks []InsuranceBundleSegmentHook
var insuranceBundleSegmentAfterUpdateHooks []InsuranceBundleSegmentHook

var insuranceBundleSegmentBeforeDeleteHooks []InsuranceBundleSegmentHook
var insuranceBundleSegmentAfterDeleteHooks []InsuranceBundleSegmentHook

var insuranceBundleSegmentBeforeUpsertHooks []InsuranceBundleSegmentHook
var insuranceBundleSegmentAfterUpsertHooks []InsuranceBundleSegmentHook

// doAfterSelectHooks executes all "after Select" hooks.
func (o *InsuranceBundleSegment) doAfterSelectHooks(ctx context.Context, exec boil.ContextExecutor) (err error) {
	if boil.HooksAreSkipped(ctx) {
		return nil
	}

	for _, hook := range insuranceBundleSegmentAfterSelectHooks {
		if err := hook(ctx, exec, o); err != nil {
			return err
		}
	}

	return nil
}

// doBeforeInsertHooks executes all "before insert" hooks.
func (o *InsuranceBundleSegment) doBeforeInsertHooks(ctx context.Context, exec boil.ContextExecutor) (err error) {
	if boil.HooksAreSkipped(ctx) {
		return nil
	}

	for _, hook := range insuranceBundleSegmentBeforeInsertHooks {
		if err := hook(ctx, exec, o); err != nil {
			return err
		}
	}

	return nil
}

// doAfterInsertHooks executes all "after Insert" hooks.
func (o *InsuranceBundleSegment) doAfterInsertHooks(ctx context.Context, exec boil.ContextExecutor) (err error) {
	if boil.HooksAreSkipped(ctx) {
		return nil
	}

	for _, hook := range insuranceBundleSegmentAfterInsertHooks {
		if err := hook(ctx, exec, o); err != nil {
			return err
		}
	}

	return nil
}

// doBeforeUpdateHooks executes all "before Update" hooks.
func (o *InsuranceBundleSegment) doBeforeUpdateHooks(ctx context.Context, exec boil.ContextExecutor) (err error) {
	if boil.HooksAreSkipped(ctx) {
		return nil
	}

	for _, hook := range insuranceBundleSegmentBeforeUpdateHooks {
		if err := hook(ctx, exec, o); err != nil {
			return err
		}
	}

	return nil
}

// doAfterUpdateHooks executes all "after Update" hooks.
func (o *InsuranceBundleSegment) doAfterUpdateHooks(ctx context.Context, exec boil.ContextExecutor) (err error) {
	if boil.HooksAreSkipped(ctx) {
		return nil
	}

	for _, hook := range insuranceBundleSegmentAfterUpdateHooks {
		if err := hook(ctx, exec, o); err != nil {
			return err
		}
	}

	return nil
}

// doBeforeDeleteHooks executes all "before Delete" hooks.
func (o *InsuranceBundleSegment) doBeforeDeleteHooks(ctx context.Context, exec boil.ContextExecutor) (err error) {
	if boil.HooksAreSkipped(ctx) {
		return nil
	}

	for _, hook := range insuranceBundleSegmentBeforeDeleteHooks {
		if err := hook(ctx, exec, o); err != nil {
			return err
		}
	}

	return nil
}

// doAfterDeleteHooks executes all "after Delete" hooks.
func (o *InsuranceBundleSegment) doAfterDeleteHooks(ctx context.Context, exec boil.ContextExecutor) (err error) {
	if boil.HooksAreSkipped(ctx) {
		return nil
	}

	for _, hook := range insuranceBundleSegmentAfterDeleteHooks {
		if err := hook(ctx, exec, o); err != nil {
			return err
		}
	}

	return nil
}

// doBeforeUpsertHooks executes all "before Upsert" hooks.
func (o *InsuranceBundleSegment) doBeforeUpsertHooks(ctx context.Context, exec boil.ContextExecutor) (err error) {
	if boil.HooksAreSkipped(ctx) {
		return nil
	}

	for _, hook := range insuranceBundleSegmentBeforeUpsertHooks {
		if err := hook(ctx, exec, o); err != nil {
			return err
		}
	}

	return nil
}

// doAfterUpsertHooks executes all "after Upsert" hooks.
func (o *InsuranceBundleSegment) doAfterUpsertHooks(ctx context.Context, exec boil.ContextExecutor) (err error) {
	if boil.HooksAreSkipped(ctx) {
		return nil
	}

	for _, hook := range insuranceBundleSegmentAfterUpsertHooks {
		if err := hook(ctx, exec, o); err != nil {
			return err
		}
	}

	return nil
}

// AddInsuranceBundleSegmentHook registers your hook function for all future operations.
func AddInsuranceBundleSegmentHook(hookPoint boil.HookPoint, insuranceBundleSegmentHook InsuranceBundleSegmentHook) {
	switch hookPoint {
	case boil.AfterSelectHook:
		insuranceBundleSegmentAfterSelectHooks = append(insuranceBundleSegmentAfterSelectHooks, insuranceBundleSegmentHook)
	case boil.BeforeInsertHook:
		insuranceBundleSegmentBeforeInsertHooks = append(insuranceBundleSegmentBeforeInsertHooks, insuranceBundleSegmentHook)
	case boil.AfterInsertHook:
		insuranceBundleSegmentAfterInsertHooks = append(insuranceBundleSegmentAfterInsertHooks, insuranceBundleSegmentHook)
	case boil.BeforeUpdateHook:
		insuranceBundleSegmentBeforeUpdateHooks = append(insuranceBundleSegmentBeforeUpdateHooks, insuranceBundleSegmentHook)
	case boil.AfterUpdateHook:
		insuranceBundleSegmentAfterUpdateHooks = append(insuranceBundleSegmentAfterUpdateHooks, insuranceBundleSegmentHook)
	case boil.BeforeDeleteHook:
		insuranceBundleSegmentBeforeDeleteHooks = append(insuranceBundleSegmentBeforeDeleteHooks, insuranceBundleSegmentHook)
	case boil.AfterDeleteHook:
		insuranceBundleSegmentAfterDeleteHooks = append(insuranceBundleSegmentAfterDeleteHooks, insuranceBundleSegmentHook)
	case boil.BeforeUpsertHook:
		insuranceBundleSegmentBeforeUpsertHooks = append(insuranceBundleSegmentBeforeUpsertHooks, insuranceBundleSegmentHook)
	case boil.AfterUpsertHook:
		insuranceBundleSegmentAfterUpsertHooks = append(insuranceBundleSegmentAfterUpsertHooks, insuranceBundleSegmentHook)
	}
}

// One returns a single insuranceBundleSegment record from the query.
func (q insuranceBundleSegmentQuery) One(ctx context.Context, exec boil.ContextExecutor) (*InsuranceBundleSegment, error) {
	o := &InsuranceBundleSegment{}

	queries.SetLimit(q.Query, 1)

	err := q.Bind(ctx, exec, o)
	if err != nil {
		if errors.Is(err, sql.ErrNoRows) {
			return nil, sql.ErrNoRows
		}
		return nil, errors.Wrap(err, "insurance_bundle: failed to execute a one query for insurance_bundle_segment")
	}

	if err := o.doAfterSelectHooks(ctx, exec); err != nil {
		return o, err
	}

	return o, nil
}

// All returns all InsuranceBundleSegment records from the query.
func (q insuranceBundleSegmentQuery) All(ctx context.Context, exec boil.ContextExecutor) (InsuranceBundleSegmentSlice, error) {
	var o []*InsuranceBundleSegment

	err := q.Bind(ctx, exec, &o)
	if err != nil {
		return nil, errors.Wrap(err, "insurance_bundle: failed to assign all query results to InsuranceBundleSegment slice")
	}

	if len(insuranceBundleSegmentAfterSelectHooks) != 0 {
		for _, obj := range o {
			if err := obj.doAfterSelectHooks(ctx, exec); err != nil {
				return o, err
			}
		}
	}

	return o, nil
}

// Count returns the count of all InsuranceBundleSegment records in the query.
func (q insuranceBundleSegmentQuery) Count(ctx context.Context, exec boil.ContextExecutor) (int64, error) {
	var count int64

	queries.SetSelect(q.Query, nil)
	queries.SetCount(q.Query)

	err := q.Query.QueryRowContext(ctx, exec).Scan(&count)
	if err != nil {
		return 0, errors.Wrap(err, "insurance_bundle: failed to count insurance_bundle_segment rows")
	}

	return count, nil
}

// Exists checks if the row exists in the table.
func (q insuranceBundleSegmentQuery) Exists(ctx context.Context, exec boil.ContextExecutor) (bool, error) {
	var count int64

	queries.SetSelect(q.Query, nil)
	queries.SetCount(q.Query)
	queries.SetLimit(q.Query, 1)

	err := q.Query.QueryRowContext(ctx, exec).Scan(&count)
	if err != nil {
		return false, errors.Wrap(err, "insurance_bundle: failed to check if insurance_bundle_segment exists")
	}

	return count > 0, nil
}

// InsuranceBundle pointed to by the foreign key.
func (o *InsuranceBundleSegment) InsuranceBundle(mods ...qm.QueryMod) insuranceBundleQuery {
	queryMods := []qm.QueryMod{
		qm.Where("\"internal_id\" = ?", o.InsuranceBundleID),
	}

	queryMods = append(queryMods, mods...)

	return InsuranceBundles(queryMods...)
}

// Policies retrieves all the policy's Policies with an executor.
func (o *InsuranceBundleSegment) Policies(mods ...qm.QueryMod) policyQuery {
	var queryMods []qm.QueryMod
	if len(mods) != 0 {
		queryMods = append(queryMods, mods...)
	}

	queryMods = append(queryMods,
		qm.Where("\"insurance_bundle\".\"policy\".\"insurance_bundle_segment_id\"=?", o.ID),
	)

	return Policies(queryMods...)
}

// LoadInsuranceBundle allows an eager lookup of values, cached into the
// loaded structs of the objects. This is for an N-1 relationship.
func (insuranceBundleSegmentL) LoadInsuranceBundle(ctx context.Context, e boil.ContextExecutor, singular bool, maybeInsuranceBundleSegment interface{}, mods queries.Applicator) error {
	var slice []*InsuranceBundleSegment
	var object *InsuranceBundleSegment

	if singular {
		object = maybeInsuranceBundleSegment.(*InsuranceBundleSegment)
	} else {
		slice = *maybeInsuranceBundleSegment.(*[]*InsuranceBundleSegment)
	}

	args := make([]interface{}, 0, 1)
	if singular {
		if object.R == nil {
			object.R = &insuranceBundleSegmentR{}
		}
		args = append(args, object.InsuranceBundleID)

	} else {
	Outer:
		for _, obj := range slice {
			if obj.R == nil {
				obj.R = &insuranceBundleSegmentR{}
			}

			for _, a := range args {
				if a == obj.InsuranceBundleID {
					continue Outer
				}
			}

			args = append(args, obj.InsuranceBundleID)

		}
	}

	if len(args) == 0 {
		return nil
	}

	query := NewQuery(
		qm.From(`insurance_bundle.insurance_bundle`),
		qm.WhereIn(`insurance_bundle.insurance_bundle.internal_id in ?`, args...),
	)
	if mods != nil {
		mods.Apply(query)
	}

	results, err := query.QueryContext(ctx, e)
	if err != nil {
		return errors.Wrap(err, "failed to eager load InsuranceBundle")
	}

	var resultSlice []*InsuranceBundle
	if err = queries.Bind(results, &resultSlice); err != nil {
		return errors.Wrap(err, "failed to bind eager loaded slice InsuranceBundle")
	}

	if err = results.Close(); err != nil {
		return errors.Wrap(err, "failed to close results of eager load for insurance_bundle")
	}
	if err = results.Err(); err != nil {
		return errors.Wrap(err, "error occurred during iteration of eager loaded relations for insurance_bundle")
	}

	if len(insuranceBundleSegmentAfterSelectHooks) != 0 {
		for _, obj := range resultSlice {
			if err := obj.doAfterSelectHooks(ctx, e); err != nil {
				return err
			}
		}
	}

	if len(resultSlice) == 0 {
		return nil
	}

	if singular {
		foreign := resultSlice[0]
		object.R.InsuranceBundle = foreign
		if foreign.R == nil {
			foreign.R = &insuranceBundleR{}
		}
		foreign.R.InsuranceBundleSegments = append(foreign.R.InsuranceBundleSegments, object)
		return nil
	}

	for _, local := range slice {
		for _, foreign := range resultSlice {
			if local.InsuranceBundleID == foreign.InternalID {
				local.R.InsuranceBundle = foreign
				if foreign.R == nil {
					foreign.R = &insuranceBundleR{}
				}
				foreign.R.InsuranceBundleSegments = append(foreign.R.InsuranceBundleSegments, local)
				break
			}
		}
	}

	return nil
}

// LoadPolicies allows an eager lookup of values, cached into the
// loaded structs of the objects. This is for a 1-M or N-M relationship.
func (insuranceBundleSegmentL) LoadPolicies(ctx context.Context, e boil.ContextExecutor, singular bool, maybeInsuranceBundleSegment interface{}, mods queries.Applicator) error {
	var slice []*InsuranceBundleSegment
	var object *InsuranceBundleSegment

	if singular {
		object = maybeInsuranceBundleSegment.(*InsuranceBundleSegment)
	} else {
		slice = *maybeInsuranceBundleSegment.(*[]*InsuranceBundleSegment)
	}

	args := make([]interface{}, 0, 1)
	if singular {
		if object.R == nil {
			object.R = &insuranceBundleSegmentR{}
		}
		args = append(args, object.ID)
	} else {
	Outer:
		for _, obj := range slice {
			if obj.R == nil {
				obj.R = &insuranceBundleSegmentR{}
			}

			for _, a := range args {
				if a == obj.ID {
					continue Outer
				}
			}

			args = append(args, obj.ID)
		}
	}

	if len(args) == 0 {
		return nil
	}

	query := NewQuery(
		qm.From(`insurance_bundle.policy`),
		qm.WhereIn(`insurance_bundle.policy.insurance_bundle_segment_id in ?`, args...),
	)
	if mods != nil {
		mods.Apply(query)
	}

	results, err := query.QueryContext(ctx, e)
	if err != nil {
		return errors.Wrap(err, "failed to eager load policy")
	}

	var resultSlice []*Policy
	if err = queries.Bind(results, &resultSlice); err != nil {
		return errors.Wrap(err, "failed to bind eager loaded slice policy")
	}

	if err = results.Close(); err != nil {
		return errors.Wrap(err, "failed to close results in eager load on policy")
	}
	if err = results.Err(); err != nil {
		return errors.Wrap(err, "error occurred during iteration of eager loaded relations for policy")
	}

	if len(policyAfterSelectHooks) != 0 {
		for _, obj := range resultSlice {
			if err := obj.doAfterSelectHooks(ctx, e); err != nil {
				return err
			}
		}
	}
	if singular {
		object.R.Policies = resultSlice
		for _, foreign := range resultSlice {
			if foreign.R == nil {
				foreign.R = &policyR{}
			}
			foreign.R.InsuranceBundleSegment = object
		}
		return nil
	}

	for _, foreign := range resultSlice {
		for _, local := range slice {
			if local.ID == foreign.InsuranceBundleSegmentID {
				local.R.Policies = append(local.R.Policies, foreign)
				if foreign.R == nil {
					foreign.R = &policyR{}
				}
				foreign.R.InsuranceBundleSegment = local
				break
			}
		}
	}

	return nil
}

// SetInsuranceBundle of the insuranceBundleSegment to the related item.
// Sets o.R.InsuranceBundle to related.
// Adds o to related.R.InsuranceBundleSegments.
func (o *InsuranceBundleSegment) SetInsuranceBundle(ctx context.Context, exec boil.ContextExecutor, insert bool, related *InsuranceBundle) error {
	var err error
	if insert {
		if err = related.Insert(ctx, exec, boil.Infer()); err != nil {
			return errors.Wrap(err, "failed to insert into foreign table")
		}
	}

	updateQuery := fmt.Sprintf(
		"UPDATE \"insurance_bundle\".\"insurance_bundle_segment\" SET %s WHERE %s",
		strmangle.SetParamNames("\"", "\"", 1, []string{"insurance_bundle_id"}),
		strmangle.WhereClause("\"", "\"", 2, insuranceBundleSegmentPrimaryKeyColumns),
	)
	values := []interface{}{related.InternalID, o.ID}

	if boil.IsDebug(ctx) {
		writer := boil.DebugWriterFrom(ctx)
		fmt.Fprintln(writer, updateQuery)
		fmt.Fprintln(writer, values)
	}
	if _, err = exec.ExecContext(ctx, updateQuery, values...); err != nil {
		return errors.Wrap(err, "failed to update local table")
	}

	o.InsuranceBundleID = related.InternalID
	if o.R == nil {
		o.R = &insuranceBundleSegmentR{
			InsuranceBundle: related,
		}
	} else {
		o.R.InsuranceBundle = related
	}

	if related.R == nil {
		related.R = &insuranceBundleR{
			InsuranceBundleSegments: InsuranceBundleSegmentSlice{o},
		}
	} else {
		related.R.InsuranceBundleSegments = append(related.R.InsuranceBundleSegments, o)
	}

	return nil
}

// AddPolicies adds the given related objects to the existing relationships
// of the insurance_bundle_segment, optionally inserting them as new records.
// Appends related to o.R.Policies.
// Sets related.R.InsuranceBundleSegment appropriately.
func (o *InsuranceBundleSegment) AddPolicies(ctx context.Context, exec boil.ContextExecutor, insert bool, related ...*Policy) error {
	var err error
	for _, rel := range related {
		if insert {
			rel.InsuranceBundleSegmentID = o.ID
			if err = rel.Insert(ctx, exec, boil.Infer()); err != nil {
				return errors.Wrap(err, "failed to insert into foreign table")
			}
		} else {
			updateQuery := fmt.Sprintf(
				"UPDATE \"insurance_bundle\".\"policy\" SET %s WHERE %s",
				strmangle.SetParamNames("\"", "\"", 1, []string{"insurance_bundle_segment_id"}),
				strmangle.WhereClause("\"", "\"", 2, policyPrimaryKeyColumns),
			)
			values := []interface{}{o.ID, rel.ID}

			if boil.IsDebug(ctx) {
				writer := boil.DebugWriterFrom(ctx)
				fmt.Fprintln(writer, updateQuery)
				fmt.Fprintln(writer, values)
			}
			if _, err = exec.ExecContext(ctx, updateQuery, values...); err != nil {
				return errors.Wrap(err, "failed to update foreign table")
			}

			rel.InsuranceBundleSegmentID = o.ID
		}
	}

	if o.R == nil {
		o.R = &insuranceBundleSegmentR{
			Policies: related,
		}
	} else {
		o.R.Policies = append(o.R.Policies, related...)
	}

	for _, rel := range related {
		if rel.R == nil {
			rel.R = &policyR{
				InsuranceBundleSegment: o,
			}
		} else {
			rel.R.InsuranceBundleSegment = o
		}
	}
	return nil
}

// InsuranceBundleSegments retrieves all the records using an executor.
func InsuranceBundleSegments(mods ...qm.QueryMod) insuranceBundleSegmentQuery {
	mods = append(mods, qm.From("\"insurance_bundle\".\"insurance_bundle_segment\""))
	q := NewQuery(mods...)
	if len(queries.GetSelect(q)) == 0 {
		queries.SetSelect(q, []string{"\"insurance_bundle\".\"insurance_bundle_segment\".*"})
	}

	return insuranceBundleSegmentQuery{q}
}

// FindInsuranceBundleSegment retrieves a single record by ID with an executor.
// If selectCols is empty Find will return all columns.
func FindInsuranceBundleSegment(ctx context.Context, exec boil.ContextExecutor, iD string, selectCols ...string) (*InsuranceBundleSegment, error) {
	insuranceBundleSegmentObj := &InsuranceBundleSegment{}

	sel := "*"
	if len(selectCols) > 0 {
		sel = strings.Join(strmangle.IdentQuoteSlice(dialect.LQ, dialect.RQ, selectCols), ",")
	}
	query := fmt.Sprintf(
		"select %s from \"insurance_bundle\".\"insurance_bundle_segment\" where \"id\"=$1", sel,
	)

	q := queries.Raw(query, iD)

	err := q.Bind(ctx, exec, insuranceBundleSegmentObj)
	if err != nil {
		if errors.Is(err, sql.ErrNoRows) {
			return nil, sql.ErrNoRows
		}
		return nil, errors.Wrap(err, "insurance_bundle: unable to select from insurance_bundle_segment")
	}

	if err = insuranceBundleSegmentObj.doAfterSelectHooks(ctx, exec); err != nil {
		return insuranceBundleSegmentObj, err
	}

	return insuranceBundleSegmentObj, nil
}

// Insert a single record using an executor.
// See boil.Columns.InsertColumnSet documentation to understand column list inference for inserts.
func (o *InsuranceBundleSegment) Insert(ctx context.Context, exec boil.ContextExecutor, columns boil.Columns) error {
	if o == nil {
		return errors.New("insurance_bundle: no insurance_bundle_segment provided for insertion")
	}

	var err error
	if !boil.TimestampsAreSkipped(ctx) {
		currTime := time.Now().In(boil.GetLocation())

		if o.CreatedAt.IsZero() {
			o.CreatedAt = currTime
		}
		if o.UpdatedAt.IsZero() {
			o.UpdatedAt = currTime
		}
	}

	if err := o.doBeforeInsertHooks(ctx, exec); err != nil {
		return err
	}

	nzDefaults := queries.NonZeroDefaultSet(insuranceBundleSegmentColumnsWithDefault, o)

	key := makeCacheKey(columns, nzDefaults)
	insuranceBundleSegmentInsertCacheMut.RLock()
	cache, cached := insuranceBundleSegmentInsertCache[key]
	insuranceBundleSegmentInsertCacheMut.RUnlock()

	if !cached {
		wl, returnColumns := columns.InsertColumnSet(
			insuranceBundleSegmentAllColumns,
			insuranceBundleSegmentColumnsWithDefault,
			insuranceBundleSegmentColumnsWithoutDefault,
			nzDefaults,
		)

		cache.valueMapping, err = queries.BindMapping(insuranceBundleSegmentType, insuranceBundleSegmentMapping, wl)
		if err != nil {
			return err
		}
		cache.retMapping, err = queries.BindMapping(insuranceBundleSegmentType, insuranceBundleSegmentMapping, returnColumns)
		if err != nil {
			return err
		}
		if len(wl) != 0 {
			cache.query = fmt.Sprintf("INSERT INTO \"insurance_bundle\".\"insurance_bundle_segment\" (\"%s\") %%sVALUES (%s)%%s", strings.Join(wl, "\",\""), strmangle.Placeholders(dialect.UseIndexPlaceholders, len(wl), 1, 1))
		} else {
			cache.query = "INSERT INTO \"insurance_bundle\".\"insurance_bundle_segment\" %sDEFAULT VALUES%s"
		}

		var queryOutput, queryReturning string

		if len(cache.retMapping) != 0 {
			queryReturning = fmt.Sprintf(" RETURNING \"%s\"", strings.Join(returnColumns, "\",\""))
		}

		cache.query = fmt.Sprintf(cache.query, queryOutput, queryReturning)
	}

	value := reflect.Indirect(reflect.ValueOf(o))
	vals := queries.ValuesFromMapping(value, cache.valueMapping)

	if boil.IsDebug(ctx) {
		writer := boil.DebugWriterFrom(ctx)
		fmt.Fprintln(writer, cache.query)
		fmt.Fprintln(writer, vals)
	}

	if len(cache.retMapping) != 0 {
		err = exec.QueryRowContext(ctx, cache.query, vals...).Scan(queries.PtrsFromMapping(value, cache.retMapping)...)
	} else {
		_, err = exec.ExecContext(ctx, cache.query, vals...)
	}

	if err != nil {
		return errors.Wrap(err, "insurance_bundle: unable to insert into insurance_bundle_segment")
	}

	if !cached {
		insuranceBundleSegmentInsertCacheMut.Lock()
		insuranceBundleSegmentInsertCache[key] = cache
		insuranceBundleSegmentInsertCacheMut.Unlock()
	}

	return o.doAfterInsertHooks(ctx, exec)
}

// Update uses an executor to update the InsuranceBundleSegment.
// See boil.Columns.UpdateColumnSet documentation to understand column list inference for updates.
// Update does not automatically update the record in case of default values. Use .Reload() to refresh the records.
func (o *InsuranceBundleSegment) Update(ctx context.Context, exec boil.ContextExecutor, columns boil.Columns) (int64, error) {
	if !boil.TimestampsAreSkipped(ctx) {
		currTime := time.Now().In(boil.GetLocation())

		o.UpdatedAt = currTime
	}

	var err error
	if err = o.doBeforeUpdateHooks(ctx, exec); err != nil {
		return 0, err
	}
	key := makeCacheKey(columns, nil)
	insuranceBundleSegmentUpdateCacheMut.RLock()
	cache, cached := insuranceBundleSegmentUpdateCache[key]
	insuranceBundleSegmentUpdateCacheMut.RUnlock()

	if !cached {
		wl := columns.UpdateColumnSet(
			insuranceBundleSegmentAllColumns,
			insuranceBundleSegmentPrimaryKeyColumns,
		)

		if !columns.IsWhitelist() {
			wl = strmangle.SetComplement(wl, []string{"created_at"})
		}
		if len(wl) == 0 {
			return 0, errors.New("insurance_bundle: unable to update insurance_bundle_segment, could not build whitelist")
		}

		cache.query = fmt.Sprintf("UPDATE \"insurance_bundle\".\"insurance_bundle_segment\" SET %s WHERE %s",
			strmangle.SetParamNames("\"", "\"", 1, wl),
			strmangle.WhereClause("\"", "\"", len(wl)+1, insuranceBundleSegmentPrimaryKeyColumns),
		)
		cache.valueMapping, err = queries.BindMapping(insuranceBundleSegmentType, insuranceBundleSegmentMapping, append(wl, insuranceBundleSegmentPrimaryKeyColumns...))
		if err != nil {
			return 0, err
		}
	}

	values := queries.ValuesFromMapping(reflect.Indirect(reflect.ValueOf(o)), cache.valueMapping)

	if boil.IsDebug(ctx) {
		writer := boil.DebugWriterFrom(ctx)
		fmt.Fprintln(writer, cache.query)
		fmt.Fprintln(writer, values)
	}
	var result sql.Result
	result, err = exec.ExecContext(ctx, cache.query, values...)
	if err != nil {
		return 0, errors.Wrap(err, "insurance_bundle: unable to update insurance_bundle_segment row")
	}

	rowsAff, err := result.RowsAffected()
	if err != nil {
		return 0, errors.Wrap(err, "insurance_bundle: failed to get rows affected by update for insurance_bundle_segment")
	}

	if !cached {
		insuranceBundleSegmentUpdateCacheMut.Lock()
		insuranceBundleSegmentUpdateCache[key] = cache
		insuranceBundleSegmentUpdateCacheMut.Unlock()
	}

	return rowsAff, o.doAfterUpdateHooks(ctx, exec)
}

// UpdateAll updates all rows with the specified column values.
func (q insuranceBundleSegmentQuery) UpdateAll(ctx context.Context, exec boil.ContextExecutor, cols M) (int64, error) {
	queries.SetUpdate(q.Query, cols)

	result, err := q.Query.ExecContext(ctx, exec)
	if err != nil {
		return 0, errors.Wrap(err, "insurance_bundle: unable to update all for insurance_bundle_segment")
	}

	rowsAff, err := result.RowsAffected()
	if err != nil {
		return 0, errors.Wrap(err, "insurance_bundle: unable to retrieve rows affected for insurance_bundle_segment")
	}

	return rowsAff, nil
}

// UpdateAll updates all rows with the specified column values, using an executor.
func (o InsuranceBundleSegmentSlice) UpdateAll(ctx context.Context, exec boil.ContextExecutor, cols M) (int64, error) {
	ln := int64(len(o))
	if ln == 0 {
		return 0, nil
	}

	if len(cols) == 0 {
		return 0, errors.New("insurance_bundle: update all requires at least one column argument")
	}

	colNames := make([]string, len(cols))
	args := make([]interface{}, len(cols))

	i := 0
	for name, value := range cols {
		colNames[i] = name
		args[i] = value
		i++
	}

	// Append all of the primary key values for each column
	for _, obj := range o {
		pkeyArgs := queries.ValuesFromMapping(reflect.Indirect(reflect.ValueOf(obj)), insuranceBundleSegmentPrimaryKeyMapping)
		args = append(args, pkeyArgs...)
	}

	sql := fmt.Sprintf("UPDATE \"insurance_bundle\".\"insurance_bundle_segment\" SET %s WHERE %s",
		strmangle.SetParamNames("\"", "\"", 1, colNames),
		strmangle.WhereClauseRepeated(string(dialect.LQ), string(dialect.RQ), len(colNames)+1, insuranceBundleSegmentPrimaryKeyColumns, len(o)))

	if boil.IsDebug(ctx) {
		writer := boil.DebugWriterFrom(ctx)
		fmt.Fprintln(writer, sql)
		fmt.Fprintln(writer, args...)
	}
	result, err := exec.ExecContext(ctx, sql, args...)
	if err != nil {
		return 0, errors.Wrap(err, "insurance_bundle: unable to update all in insuranceBundleSegment slice")
	}

	rowsAff, err := result.RowsAffected()
	if err != nil {
		return 0, errors.Wrap(err, "insurance_bundle: unable to retrieve rows affected all in update all insuranceBundleSegment")
	}
	return rowsAff, nil
}

// Upsert attempts an insert using an executor, and does an update or ignore on conflict.
// See boil.Columns documentation for how to properly use updateColumns and insertColumns.
func (o *InsuranceBundleSegment) Upsert(ctx context.Context, exec boil.ContextExecutor, updateOnConflict bool, conflictColumns []string, updateColumns, insertColumns boil.Columns) error {
	if o == nil {
		return errors.New("insurance_bundle: no insurance_bundle_segment provided for upsert")
	}
	if !boil.TimestampsAreSkipped(ctx) {
		currTime := time.Now().In(boil.GetLocation())

		if o.CreatedAt.IsZero() {
			o.CreatedAt = currTime
		}
		o.UpdatedAt = currTime
	}

	if err := o.doBeforeUpsertHooks(ctx, exec); err != nil {
		return err
	}

	nzDefaults := queries.NonZeroDefaultSet(insuranceBundleSegmentColumnsWithDefault, o)

	// Build cache key in-line uglily - mysql vs psql problems
	buf := strmangle.GetBuffer()
	if updateOnConflict {
		buf.WriteByte('t')
	} else {
		buf.WriteByte('f')
	}
	buf.WriteByte('.')
	for _, c := range conflictColumns {
		buf.WriteString(c)
	}
	buf.WriteByte('.')
	buf.WriteString(strconv.Itoa(updateColumns.Kind))
	for _, c := range updateColumns.Cols {
		buf.WriteString(c)
	}
	buf.WriteByte('.')
	buf.WriteString(strconv.Itoa(insertColumns.Kind))
	for _, c := range insertColumns.Cols {
		buf.WriteString(c)
	}
	buf.WriteByte('.')
	for _, c := range nzDefaults {
		buf.WriteString(c)
	}
	key := buf.String()
	strmangle.PutBuffer(buf)

	insuranceBundleSegmentUpsertCacheMut.RLock()
	cache, cached := insuranceBundleSegmentUpsertCache[key]
	insuranceBundleSegmentUpsertCacheMut.RUnlock()

	var err error

	if !cached {
		insert, ret := insertColumns.InsertColumnSet(
			insuranceBundleSegmentAllColumns,
			insuranceBundleSegmentColumnsWithDefault,
			insuranceBundleSegmentColumnsWithoutDefault,
			nzDefaults,
		)

		update := updateColumns.UpdateColumnSet(
			insuranceBundleSegmentAllColumns,
			insuranceBundleSegmentPrimaryKeyColumns,
		)

		if updateOnConflict && len(update) == 0 {
			return errors.New("insurance_bundle: unable to upsert insurance_bundle_segment, could not build update column list")
		}

		conflict := conflictColumns
		if len(conflict) == 0 {
			conflict = make([]string, len(insuranceBundleSegmentPrimaryKeyColumns))
			copy(conflict, insuranceBundleSegmentPrimaryKeyColumns)
		}
		cache.query = buildUpsertQueryPostgres(dialect, "\"insurance_bundle\".\"insurance_bundle_segment\"", updateOnConflict, ret, update, conflict, insert)

		cache.valueMapping, err = queries.BindMapping(insuranceBundleSegmentType, insuranceBundleSegmentMapping, insert)
		if err != nil {
			return err
		}
		if len(ret) != 0 {
			cache.retMapping, err = queries.BindMapping(insuranceBundleSegmentType, insuranceBundleSegmentMapping, ret)
			if err != nil {
				return err
			}
		}
	}

	value := reflect.Indirect(reflect.ValueOf(o))
	vals := queries.ValuesFromMapping(value, cache.valueMapping)
	var returns []interface{}
	if len(cache.retMapping) != 0 {
		returns = queries.PtrsFromMapping(value, cache.retMapping)
	}

	if boil.IsDebug(ctx) {
		writer := boil.DebugWriterFrom(ctx)
		fmt.Fprintln(writer, cache.query)
		fmt.Fprintln(writer, vals)
	}
	if len(cache.retMapping) != 0 {
		err = exec.QueryRowContext(ctx, cache.query, vals...).Scan(returns...)
		if errors.Is(err, sql.ErrNoRows) {
			err = nil // Postgres doesn't return anything when there's no update
		}
	} else {
		_, err = exec.ExecContext(ctx, cache.query, vals...)
	}
	if err != nil {
		return errors.Wrap(err, "insurance_bundle: unable to upsert insurance_bundle_segment")
	}

	if !cached {
		insuranceBundleSegmentUpsertCacheMut.Lock()
		insuranceBundleSegmentUpsertCache[key] = cache
		insuranceBundleSegmentUpsertCacheMut.Unlock()
	}

	return o.doAfterUpsertHooks(ctx, exec)
}

// Delete deletes a single InsuranceBundleSegment record with an executor.
// Delete will match against the primary key column to find the record to delete.
func (o *InsuranceBundleSegment) Delete(ctx context.Context, exec boil.ContextExecutor) (int64, error) {
	if o == nil {
		return 0, errors.New("insurance_bundle: no InsuranceBundleSegment provided for delete")
	}

	if err := o.doBeforeDeleteHooks(ctx, exec); err != nil {
		return 0, err
	}

	args := queries.ValuesFromMapping(reflect.Indirect(reflect.ValueOf(o)), insuranceBundleSegmentPrimaryKeyMapping)
	sql := "DELETE FROM \"insurance_bundle\".\"insurance_bundle_segment\" WHERE \"id\"=$1"

	if boil.IsDebug(ctx) {
		writer := boil.DebugWriterFrom(ctx)
		fmt.Fprintln(writer, sql)
		fmt.Fprintln(writer, args...)
	}
	result, err := exec.ExecContext(ctx, sql, args...)
	if err != nil {
		return 0, errors.Wrap(err, "insurance_bundle: unable to delete from insurance_bundle_segment")
	}

	rowsAff, err := result.RowsAffected()
	if err != nil {
		return 0, errors.Wrap(err, "insurance_bundle: failed to get rows affected by delete for insurance_bundle_segment")
	}

	if err := o.doAfterDeleteHooks(ctx, exec); err != nil {
		return 0, err
	}

	return rowsAff, nil
}

// DeleteAll deletes all matching rows.
func (q insuranceBundleSegmentQuery) DeleteAll(ctx context.Context, exec boil.ContextExecutor) (int64, error) {
	if q.Query == nil {
		return 0, errors.New("insurance_bundle: no insuranceBundleSegmentQuery provided for delete all")
	}

	queries.SetDelete(q.Query)

	result, err := q.Query.ExecContext(ctx, exec)
	if err != nil {
		return 0, errors.Wrap(err, "insurance_bundle: unable to delete all from insurance_bundle_segment")
	}

	rowsAff, err := result.RowsAffected()
	if err != nil {
		return 0, errors.Wrap(err, "insurance_bundle: failed to get rows affected by deleteall for insurance_bundle_segment")
	}

	return rowsAff, nil
}

// DeleteAll deletes all rows in the slice, using an executor.
func (o InsuranceBundleSegmentSlice) DeleteAll(ctx context.Context, exec boil.ContextExecutor) (int64, error) {
	if len(o) == 0 {
		return 0, nil
	}

	if len(insuranceBundleSegmentBeforeDeleteHooks) != 0 {
		for _, obj := range o {
			if err := obj.doBeforeDeleteHooks(ctx, exec); err != nil {
				return 0, err
			}
		}
	}

	var args []interface{}
	for _, obj := range o {
		pkeyArgs := queries.ValuesFromMapping(reflect.Indirect(reflect.ValueOf(obj)), insuranceBundleSegmentPrimaryKeyMapping)
		args = append(args, pkeyArgs...)
	}

	sql := "DELETE FROM \"insurance_bundle\".\"insurance_bundle_segment\" WHERE " +
		strmangle.WhereClauseRepeated(string(dialect.LQ), string(dialect.RQ), 1, insuranceBundleSegmentPrimaryKeyColumns, len(o))

	if boil.IsDebug(ctx) {
		writer := boil.DebugWriterFrom(ctx)
		fmt.Fprintln(writer, sql)
		fmt.Fprintln(writer, args)
	}
	result, err := exec.ExecContext(ctx, sql, args...)
	if err != nil {
		return 0, errors.Wrap(err, "insurance_bundle: unable to delete all from insuranceBundleSegment slice")
	}

	rowsAff, err := result.RowsAffected()
	if err != nil {
		return 0, errors.Wrap(err, "insurance_bundle: failed to get rows affected by deleteall for insurance_bundle_segment")
	}

	if len(insuranceBundleSegmentAfterDeleteHooks) != 0 {
		for _, obj := range o {
			if err := obj.doAfterDeleteHooks(ctx, exec); err != nil {
				return 0, err
			}
		}
	}

	return rowsAff, nil
}

// Reload refetches the object from the database
// using the primary keys with an executor.
func (o *InsuranceBundleSegment) Reload(ctx context.Context, exec boil.ContextExecutor) error {
	ret, err := FindInsuranceBundleSegment(ctx, exec, o.ID)
	if err != nil {
		return err
	}

	*o = *ret
	return nil
}

// ReloadAll refetches every row with matching primary key column values
// and overwrites the original object slice with the newly updated slice.
func (o *InsuranceBundleSegmentSlice) ReloadAll(ctx context.Context, exec boil.ContextExecutor) error {
	if o == nil || len(*o) == 0 {
		return nil
	}

	slice := InsuranceBundleSegmentSlice{}
	var args []interface{}
	for _, obj := range *o {
		pkeyArgs := queries.ValuesFromMapping(reflect.Indirect(reflect.ValueOf(obj)), insuranceBundleSegmentPrimaryKeyMapping)
		args = append(args, pkeyArgs...)
	}

	sql := "SELECT \"insurance_bundle\".\"insurance_bundle_segment\".* FROM \"insurance_bundle\".\"insurance_bundle_segment\" WHERE " +
		strmangle.WhereClauseRepeated(string(dialect.LQ), string(dialect.RQ), 1, insuranceBundleSegmentPrimaryKeyColumns, len(*o))

	q := queries.Raw(sql, args...)

	err := q.Bind(ctx, exec, &slice)
	if err != nil {
		return errors.Wrap(err, "insurance_bundle: unable to reload all in InsuranceBundleSegmentSlice")
	}

	*o = slice

	return nil
}

// InsuranceBundleSegmentExists checks if the InsuranceBundleSegment row exists.
func InsuranceBundleSegmentExists(ctx context.Context, exec boil.ContextExecutor, iD string) (bool, error) {
	var exists bool
	sql := "select exists(select 1 from \"insurance_bundle\".\"insurance_bundle_segment\" where \"id\"=$1 limit 1)"

	if boil.IsDebug(ctx) {
		writer := boil.DebugWriterFrom(ctx)
		fmt.Fprintln(writer, sql)
		fmt.Fprintln(writer, iD)
	}
	row := exec.QueryRowContext(ctx, sql, iD)

	err := row.Scan(&exists)
	if err != nil {
		return false, errors.Wrap(err, "insurance_bundle: unable to check if insurance_bundle_segment exists")
	}

	return exists, nil
}
