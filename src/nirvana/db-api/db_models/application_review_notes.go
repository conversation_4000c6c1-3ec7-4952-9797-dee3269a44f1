// Code generated by SQLBoiler (https://github.com/volatiletech/sqlboiler). DO NOT EDIT.
// This file is meant to be re-generated in place and/or deleted at any time.

package db_models

import (
	"context"
	"database/sql"
	"fmt"
	"reflect"
	"strconv"
	"strings"
	"sync"
	"time"

	"github.com/friendsofgo/errors"
	"github.com/volatiletech/null/v8"
	"github.com/volatiletech/sqlboiler/v4/boil"
	"github.com/volatiletech/sqlboiler/v4/queries"
	"github.com/volatiletech/sqlboiler/v4/queries/qm"
	"github.com/volatiletech/sqlboiler/v4/queries/qmhelper"
	"github.com/volatiletech/strmangle"
)

// ApplicationReviewNote is an object representing the database table.
type ApplicationReviewNote struct {
	ID                  string    `boil:"id" json:"id" toml:"id" yaml:"id"`
	ApplicationReviewID string    `boil:"application_review_id" json:"application_review_id" toml:"application_review_id" yaml:"application_review_id"`
	GeneratedNotes      null.JSON `boil:"generated_notes" json:"generated_notes,omitempty" toml:"generated_notes" yaml:"generated_notes,omitempty"`
	Notes               null.JSON `boil:"notes" json:"notes,omitempty" toml:"notes" yaml:"notes,omitempty"`
	UpdatedAt           time.Time `boil:"updated_at" json:"updated_at" toml:"updated_at" yaml:"updated_at"`
	CreatedAt           time.Time `boil:"created_at" json:"created_at" toml:"created_at" yaml:"created_at"`

	R *applicationReviewNoteR `boil:"" json:"" toml:"" yaml:""`
	L applicationReviewNoteL  `boil:"-" json:"-" toml:"-" yaml:"-"`
}

var ApplicationReviewNoteColumns = struct {
	ID                  string
	ApplicationReviewID string
	GeneratedNotes      string
	Notes               string
	UpdatedAt           string
	CreatedAt           string
}{
	ID:                  "id",
	ApplicationReviewID: "application_review_id",
	GeneratedNotes:      "generated_notes",
	Notes:               "notes",
	UpdatedAt:           "updated_at",
	CreatedAt:           "created_at",
}

var ApplicationReviewNoteTableColumns = struct {
	ID                  string
	ApplicationReviewID string
	GeneratedNotes      string
	Notes               string
	UpdatedAt           string
	CreatedAt           string
}{
	ID:                  "application_review_notes.id",
	ApplicationReviewID: "application_review_notes.application_review_id",
	GeneratedNotes:      "application_review_notes.generated_notes",
	Notes:               "application_review_notes.notes",
	UpdatedAt:           "application_review_notes.updated_at",
	CreatedAt:           "application_review_notes.created_at",
}

// Generated where

var ApplicationReviewNoteWhere = struct {
	ID                  whereHelperstring
	ApplicationReviewID whereHelperstring
	GeneratedNotes      whereHelpernull_JSON
	Notes               whereHelpernull_JSON
	UpdatedAt           whereHelpertime_Time
	CreatedAt           whereHelpertime_Time
}{
	ID:                  whereHelperstring{field: "\"application_review_notes\".\"id\""},
	ApplicationReviewID: whereHelperstring{field: "\"application_review_notes\".\"application_review_id\""},
	GeneratedNotes:      whereHelpernull_JSON{field: "\"application_review_notes\".\"generated_notes\""},
	Notes:               whereHelpernull_JSON{field: "\"application_review_notes\".\"notes\""},
	UpdatedAt:           whereHelpertime_Time{field: "\"application_review_notes\".\"updated_at\""},
	CreatedAt:           whereHelpertime_Time{field: "\"application_review_notes\".\"created_at\""},
}

// ApplicationReviewNoteRels is where relationship names are stored.
var ApplicationReviewNoteRels = struct {
	ApplicationReview string
}{
	ApplicationReview: "ApplicationReview",
}

// applicationReviewNoteR is where relationships are stored.
type applicationReviewNoteR struct {
	ApplicationReview *ApplicationReview `boil:"ApplicationReview" json:"ApplicationReview" toml:"ApplicationReview" yaml:"ApplicationReview"`
}

// NewStruct creates a new relationship struct
func (*applicationReviewNoteR) NewStruct() *applicationReviewNoteR {
	return &applicationReviewNoteR{}
}

// applicationReviewNoteL is where Load methods for each relationship are stored.
type applicationReviewNoteL struct{}

var (
	applicationReviewNoteAllColumns            = []string{"id", "application_review_id", "generated_notes", "notes", "updated_at", "created_at"}
	applicationReviewNoteColumnsWithoutDefault = []string{"id", "application_review_id", "updated_at", "created_at"}
	applicationReviewNoteColumnsWithDefault    = []string{"generated_notes", "notes"}
	applicationReviewNotePrimaryKeyColumns     = []string{"id"}
	applicationReviewNoteGeneratedColumns      = []string{}
)

type (
	// ApplicationReviewNoteSlice is an alias for a slice of pointers to ApplicationReviewNote.
	// This should almost always be used instead of []ApplicationReviewNote.
	ApplicationReviewNoteSlice []*ApplicationReviewNote
	// ApplicationReviewNoteHook is the signature for custom ApplicationReviewNote hook methods
	ApplicationReviewNoteHook func(context.Context, boil.ContextExecutor, *ApplicationReviewNote) error

	applicationReviewNoteQuery struct {
		*queries.Query
	}
)

// Cache for insert, update and upsert
var (
	applicationReviewNoteType                 = reflect.TypeOf(&ApplicationReviewNote{})
	applicationReviewNoteMapping              = queries.MakeStructMapping(applicationReviewNoteType)
	applicationReviewNotePrimaryKeyMapping, _ = queries.BindMapping(applicationReviewNoteType, applicationReviewNoteMapping, applicationReviewNotePrimaryKeyColumns)
	applicationReviewNoteInsertCacheMut       sync.RWMutex
	applicationReviewNoteInsertCache          = make(map[string]insertCache)
	applicationReviewNoteUpdateCacheMut       sync.RWMutex
	applicationReviewNoteUpdateCache          = make(map[string]updateCache)
	applicationReviewNoteUpsertCacheMut       sync.RWMutex
	applicationReviewNoteUpsertCache          = make(map[string]insertCache)
)

var (
	// Force time package dependency for automated UpdatedAt/CreatedAt.
	_ = time.Second
	// Force qmhelper dependency for where clause generation (which doesn't
	// always happen)
	_ = qmhelper.Where
)

var applicationReviewNoteAfterSelectHooks []ApplicationReviewNoteHook

var applicationReviewNoteBeforeInsertHooks []ApplicationReviewNoteHook
var applicationReviewNoteAfterInsertHooks []ApplicationReviewNoteHook

var applicationReviewNoteBeforeUpdateHooks []ApplicationReviewNoteHook
var applicationReviewNoteAfterUpdateHooks []ApplicationReviewNoteHook

var applicationReviewNoteBeforeDeleteHooks []ApplicationReviewNoteHook
var applicationReviewNoteAfterDeleteHooks []ApplicationReviewNoteHook

var applicationReviewNoteBeforeUpsertHooks []ApplicationReviewNoteHook
var applicationReviewNoteAfterUpsertHooks []ApplicationReviewNoteHook

// doAfterSelectHooks executes all "after Select" hooks.
func (o *ApplicationReviewNote) doAfterSelectHooks(ctx context.Context, exec boil.ContextExecutor) (err error) {
	if boil.HooksAreSkipped(ctx) {
		return nil
	}

	for _, hook := range applicationReviewNoteAfterSelectHooks {
		if err := hook(ctx, exec, o); err != nil {
			return err
		}
	}

	return nil
}

// doBeforeInsertHooks executes all "before insert" hooks.
func (o *ApplicationReviewNote) doBeforeInsertHooks(ctx context.Context, exec boil.ContextExecutor) (err error) {
	if boil.HooksAreSkipped(ctx) {
		return nil
	}

	for _, hook := range applicationReviewNoteBeforeInsertHooks {
		if err := hook(ctx, exec, o); err != nil {
			return err
		}
	}

	return nil
}

// doAfterInsertHooks executes all "after Insert" hooks.
func (o *ApplicationReviewNote) doAfterInsertHooks(ctx context.Context, exec boil.ContextExecutor) (err error) {
	if boil.HooksAreSkipped(ctx) {
		return nil
	}

	for _, hook := range applicationReviewNoteAfterInsertHooks {
		if err := hook(ctx, exec, o); err != nil {
			return err
		}
	}

	return nil
}

// doBeforeUpdateHooks executes all "before Update" hooks.
func (o *ApplicationReviewNote) doBeforeUpdateHooks(ctx context.Context, exec boil.ContextExecutor) (err error) {
	if boil.HooksAreSkipped(ctx) {
		return nil
	}

	for _, hook := range applicationReviewNoteBeforeUpdateHooks {
		if err := hook(ctx, exec, o); err != nil {
			return err
		}
	}

	return nil
}

// doAfterUpdateHooks executes all "after Update" hooks.
func (o *ApplicationReviewNote) doAfterUpdateHooks(ctx context.Context, exec boil.ContextExecutor) (err error) {
	if boil.HooksAreSkipped(ctx) {
		return nil
	}

	for _, hook := range applicationReviewNoteAfterUpdateHooks {
		if err := hook(ctx, exec, o); err != nil {
			return err
		}
	}

	return nil
}

// doBeforeDeleteHooks executes all "before Delete" hooks.
func (o *ApplicationReviewNote) doBeforeDeleteHooks(ctx context.Context, exec boil.ContextExecutor) (err error) {
	if boil.HooksAreSkipped(ctx) {
		return nil
	}

	for _, hook := range applicationReviewNoteBeforeDeleteHooks {
		if err := hook(ctx, exec, o); err != nil {
			return err
		}
	}

	return nil
}

// doAfterDeleteHooks executes all "after Delete" hooks.
func (o *ApplicationReviewNote) doAfterDeleteHooks(ctx context.Context, exec boil.ContextExecutor) (err error) {
	if boil.HooksAreSkipped(ctx) {
		return nil
	}

	for _, hook := range applicationReviewNoteAfterDeleteHooks {
		if err := hook(ctx, exec, o); err != nil {
			return err
		}
	}

	return nil
}

// doBeforeUpsertHooks executes all "before Upsert" hooks.
func (o *ApplicationReviewNote) doBeforeUpsertHooks(ctx context.Context, exec boil.ContextExecutor) (err error) {
	if boil.HooksAreSkipped(ctx) {
		return nil
	}

	for _, hook := range applicationReviewNoteBeforeUpsertHooks {
		if err := hook(ctx, exec, o); err != nil {
			return err
		}
	}

	return nil
}

// doAfterUpsertHooks executes all "after Upsert" hooks.
func (o *ApplicationReviewNote) doAfterUpsertHooks(ctx context.Context, exec boil.ContextExecutor) (err error) {
	if boil.HooksAreSkipped(ctx) {
		return nil
	}

	for _, hook := range applicationReviewNoteAfterUpsertHooks {
		if err := hook(ctx, exec, o); err != nil {
			return err
		}
	}

	return nil
}

// AddApplicationReviewNoteHook registers your hook function for all future operations.
func AddApplicationReviewNoteHook(hookPoint boil.HookPoint, applicationReviewNoteHook ApplicationReviewNoteHook) {
	switch hookPoint {
	case boil.AfterSelectHook:
		applicationReviewNoteAfterSelectHooks = append(applicationReviewNoteAfterSelectHooks, applicationReviewNoteHook)
	case boil.BeforeInsertHook:
		applicationReviewNoteBeforeInsertHooks = append(applicationReviewNoteBeforeInsertHooks, applicationReviewNoteHook)
	case boil.AfterInsertHook:
		applicationReviewNoteAfterInsertHooks = append(applicationReviewNoteAfterInsertHooks, applicationReviewNoteHook)
	case boil.BeforeUpdateHook:
		applicationReviewNoteBeforeUpdateHooks = append(applicationReviewNoteBeforeUpdateHooks, applicationReviewNoteHook)
	case boil.AfterUpdateHook:
		applicationReviewNoteAfterUpdateHooks = append(applicationReviewNoteAfterUpdateHooks, applicationReviewNoteHook)
	case boil.BeforeDeleteHook:
		applicationReviewNoteBeforeDeleteHooks = append(applicationReviewNoteBeforeDeleteHooks, applicationReviewNoteHook)
	case boil.AfterDeleteHook:
		applicationReviewNoteAfterDeleteHooks = append(applicationReviewNoteAfterDeleteHooks, applicationReviewNoteHook)
	case boil.BeforeUpsertHook:
		applicationReviewNoteBeforeUpsertHooks = append(applicationReviewNoteBeforeUpsertHooks, applicationReviewNoteHook)
	case boil.AfterUpsertHook:
		applicationReviewNoteAfterUpsertHooks = append(applicationReviewNoteAfterUpsertHooks, applicationReviewNoteHook)
	}
}

// One returns a single applicationReviewNote record from the query.
func (q applicationReviewNoteQuery) One(ctx context.Context, exec boil.ContextExecutor) (*ApplicationReviewNote, error) {
	o := &ApplicationReviewNote{}

	queries.SetLimit(q.Query, 1)

	err := q.Bind(ctx, exec, o)
	if err != nil {
		if errors.Is(err, sql.ErrNoRows) {
			return nil, sql.ErrNoRows
		}
		return nil, errors.Wrap(err, "db_models: failed to execute a one query for application_review_notes")
	}

	if err := o.doAfterSelectHooks(ctx, exec); err != nil {
		return o, err
	}

	return o, nil
}

// All returns all ApplicationReviewNote records from the query.
func (q applicationReviewNoteQuery) All(ctx context.Context, exec boil.ContextExecutor) (ApplicationReviewNoteSlice, error) {
	var o []*ApplicationReviewNote

	err := q.Bind(ctx, exec, &o)
	if err != nil {
		return nil, errors.Wrap(err, "db_models: failed to assign all query results to ApplicationReviewNote slice")
	}

	if len(applicationReviewNoteAfterSelectHooks) != 0 {
		for _, obj := range o {
			if err := obj.doAfterSelectHooks(ctx, exec); err != nil {
				return o, err
			}
		}
	}

	return o, nil
}

// Count returns the count of all ApplicationReviewNote records in the query.
func (q applicationReviewNoteQuery) Count(ctx context.Context, exec boil.ContextExecutor) (int64, error) {
	var count int64

	queries.SetSelect(q.Query, nil)
	queries.SetCount(q.Query)

	err := q.Query.QueryRowContext(ctx, exec).Scan(&count)
	if err != nil {
		return 0, errors.Wrap(err, "db_models: failed to count application_review_notes rows")
	}

	return count, nil
}

// Exists checks if the row exists in the table.
func (q applicationReviewNoteQuery) Exists(ctx context.Context, exec boil.ContextExecutor) (bool, error) {
	var count int64

	queries.SetSelect(q.Query, nil)
	queries.SetCount(q.Query)
	queries.SetLimit(q.Query, 1)

	err := q.Query.QueryRowContext(ctx, exec).Scan(&count)
	if err != nil {
		return false, errors.Wrap(err, "db_models: failed to check if application_review_notes exists")
	}

	return count > 0, nil
}

// ApplicationReview pointed to by the foreign key.
func (o *ApplicationReviewNote) ApplicationReview(mods ...qm.QueryMod) applicationReviewQuery {
	queryMods := []qm.QueryMod{
		qm.Where("\"id\" = ?", o.ApplicationReviewID),
	}

	queryMods = append(queryMods, mods...)

	return ApplicationReviews(queryMods...)
}

// LoadApplicationReview allows an eager lookup of values, cached into the
// loaded structs of the objects. This is for an N-1 relationship.
func (applicationReviewNoteL) LoadApplicationReview(ctx context.Context, e boil.ContextExecutor, singular bool, maybeApplicationReviewNote interface{}, mods queries.Applicator) error {
	var slice []*ApplicationReviewNote
	var object *ApplicationReviewNote

	if singular {
		object = maybeApplicationReviewNote.(*ApplicationReviewNote)
	} else {
		slice = *maybeApplicationReviewNote.(*[]*ApplicationReviewNote)
	}

	args := make([]interface{}, 0, 1)
	if singular {
		if object.R == nil {
			object.R = &applicationReviewNoteR{}
		}
		args = append(args, object.ApplicationReviewID)

	} else {
	Outer:
		for _, obj := range slice {
			if obj.R == nil {
				obj.R = &applicationReviewNoteR{}
			}

			for _, a := range args {
				if a == obj.ApplicationReviewID {
					continue Outer
				}
			}

			args = append(args, obj.ApplicationReviewID)

		}
	}

	if len(args) == 0 {
		return nil
	}

	query := NewQuery(
		qm.From(`application_review`),
		qm.WhereIn(`application_review.id in ?`, args...),
	)
	if mods != nil {
		mods.Apply(query)
	}

	results, err := query.QueryContext(ctx, e)
	if err != nil {
		return errors.Wrap(err, "failed to eager load ApplicationReview")
	}

	var resultSlice []*ApplicationReview
	if err = queries.Bind(results, &resultSlice); err != nil {
		return errors.Wrap(err, "failed to bind eager loaded slice ApplicationReview")
	}

	if err = results.Close(); err != nil {
		return errors.Wrap(err, "failed to close results of eager load for application_review")
	}
	if err = results.Err(); err != nil {
		return errors.Wrap(err, "error occurred during iteration of eager loaded relations for application_review")
	}

	if len(applicationReviewNoteAfterSelectHooks) != 0 {
		for _, obj := range resultSlice {
			if err := obj.doAfterSelectHooks(ctx, e); err != nil {
				return err
			}
		}
	}

	if len(resultSlice) == 0 {
		return nil
	}

	if singular {
		foreign := resultSlice[0]
		object.R.ApplicationReview = foreign
		if foreign.R == nil {
			foreign.R = &applicationReviewR{}
		}
		foreign.R.ApplicationReviewNote = object
		return nil
	}

	for _, local := range slice {
		for _, foreign := range resultSlice {
			if local.ApplicationReviewID == foreign.ID {
				local.R.ApplicationReview = foreign
				if foreign.R == nil {
					foreign.R = &applicationReviewR{}
				}
				foreign.R.ApplicationReviewNote = local
				break
			}
		}
	}

	return nil
}

// SetApplicationReview of the applicationReviewNote to the related item.
// Sets o.R.ApplicationReview to related.
// Adds o to related.R.ApplicationReviewNote.
func (o *ApplicationReviewNote) SetApplicationReview(ctx context.Context, exec boil.ContextExecutor, insert bool, related *ApplicationReview) error {
	var err error
	if insert {
		if err = related.Insert(ctx, exec, boil.Infer()); err != nil {
			return errors.Wrap(err, "failed to insert into foreign table")
		}
	}

	updateQuery := fmt.Sprintf(
		"UPDATE \"application_review_notes\" SET %s WHERE %s",
		strmangle.SetParamNames("\"", "\"", 1, []string{"application_review_id"}),
		strmangle.WhereClause("\"", "\"", 2, applicationReviewNotePrimaryKeyColumns),
	)
	values := []interface{}{related.ID, o.ID}

	if boil.IsDebug(ctx) {
		writer := boil.DebugWriterFrom(ctx)
		fmt.Fprintln(writer, updateQuery)
		fmt.Fprintln(writer, values)
	}
	if _, err = exec.ExecContext(ctx, updateQuery, values...); err != nil {
		return errors.Wrap(err, "failed to update local table")
	}

	o.ApplicationReviewID = related.ID
	if o.R == nil {
		o.R = &applicationReviewNoteR{
			ApplicationReview: related,
		}
	} else {
		o.R.ApplicationReview = related
	}

	if related.R == nil {
		related.R = &applicationReviewR{
			ApplicationReviewNote: o,
		}
	} else {
		related.R.ApplicationReviewNote = o
	}

	return nil
}

// ApplicationReviewNotes retrieves all the records using an executor.
func ApplicationReviewNotes(mods ...qm.QueryMod) applicationReviewNoteQuery {
	mods = append(mods, qm.From("\"application_review_notes\""))
	q := NewQuery(mods...)
	if len(queries.GetSelect(q)) == 0 {
		queries.SetSelect(q, []string{"\"application_review_notes\".*"})
	}

	return applicationReviewNoteQuery{q}
}

// FindApplicationReviewNote retrieves a single record by ID with an executor.
// If selectCols is empty Find will return all columns.
func FindApplicationReviewNote(ctx context.Context, exec boil.ContextExecutor, iD string, selectCols ...string) (*ApplicationReviewNote, error) {
	applicationReviewNoteObj := &ApplicationReviewNote{}

	sel := "*"
	if len(selectCols) > 0 {
		sel = strings.Join(strmangle.IdentQuoteSlice(dialect.LQ, dialect.RQ, selectCols), ",")
	}
	query := fmt.Sprintf(
		"select %s from \"application_review_notes\" where \"id\"=$1", sel,
	)

	q := queries.Raw(query, iD)

	err := q.Bind(ctx, exec, applicationReviewNoteObj)
	if err != nil {
		if errors.Is(err, sql.ErrNoRows) {
			return nil, sql.ErrNoRows
		}
		return nil, errors.Wrap(err, "db_models: unable to select from application_review_notes")
	}

	if err = applicationReviewNoteObj.doAfterSelectHooks(ctx, exec); err != nil {
		return applicationReviewNoteObj, err
	}

	return applicationReviewNoteObj, nil
}

// Insert a single record using an executor.
// See boil.Columns.InsertColumnSet documentation to understand column list inference for inserts.
func (o *ApplicationReviewNote) Insert(ctx context.Context, exec boil.ContextExecutor, columns boil.Columns) error {
	if o == nil {
		return errors.New("db_models: no application_review_notes provided for insertion")
	}

	var err error
	if !boil.TimestampsAreSkipped(ctx) {
		currTime := time.Now().In(boil.GetLocation())

		if o.UpdatedAt.IsZero() {
			o.UpdatedAt = currTime
		}
		if o.CreatedAt.IsZero() {
			o.CreatedAt = currTime
		}
	}

	if err := o.doBeforeInsertHooks(ctx, exec); err != nil {
		return err
	}

	nzDefaults := queries.NonZeroDefaultSet(applicationReviewNoteColumnsWithDefault, o)

	key := makeCacheKey(columns, nzDefaults)
	applicationReviewNoteInsertCacheMut.RLock()
	cache, cached := applicationReviewNoteInsertCache[key]
	applicationReviewNoteInsertCacheMut.RUnlock()

	if !cached {
		wl, returnColumns := columns.InsertColumnSet(
			applicationReviewNoteAllColumns,
			applicationReviewNoteColumnsWithDefault,
			applicationReviewNoteColumnsWithoutDefault,
			nzDefaults,
		)

		cache.valueMapping, err = queries.BindMapping(applicationReviewNoteType, applicationReviewNoteMapping, wl)
		if err != nil {
			return err
		}
		cache.retMapping, err = queries.BindMapping(applicationReviewNoteType, applicationReviewNoteMapping, returnColumns)
		if err != nil {
			return err
		}
		if len(wl) != 0 {
			cache.query = fmt.Sprintf("INSERT INTO \"application_review_notes\" (\"%s\") %%sVALUES (%s)%%s", strings.Join(wl, "\",\""), strmangle.Placeholders(dialect.UseIndexPlaceholders, len(wl), 1, 1))
		} else {
			cache.query = "INSERT INTO \"application_review_notes\" %sDEFAULT VALUES%s"
		}

		var queryOutput, queryReturning string

		if len(cache.retMapping) != 0 {
			queryReturning = fmt.Sprintf(" RETURNING \"%s\"", strings.Join(returnColumns, "\",\""))
		}

		cache.query = fmt.Sprintf(cache.query, queryOutput, queryReturning)
	}

	value := reflect.Indirect(reflect.ValueOf(o))
	vals := queries.ValuesFromMapping(value, cache.valueMapping)

	if boil.IsDebug(ctx) {
		writer := boil.DebugWriterFrom(ctx)
		fmt.Fprintln(writer, cache.query)
		fmt.Fprintln(writer, vals)
	}

	if len(cache.retMapping) != 0 {
		err = exec.QueryRowContext(ctx, cache.query, vals...).Scan(queries.PtrsFromMapping(value, cache.retMapping)...)
	} else {
		_, err = exec.ExecContext(ctx, cache.query, vals...)
	}

	if err != nil {
		return errors.Wrap(err, "db_models: unable to insert into application_review_notes")
	}

	if !cached {
		applicationReviewNoteInsertCacheMut.Lock()
		applicationReviewNoteInsertCache[key] = cache
		applicationReviewNoteInsertCacheMut.Unlock()
	}

	return o.doAfterInsertHooks(ctx, exec)
}

// Update uses an executor to update the ApplicationReviewNote.
// See boil.Columns.UpdateColumnSet documentation to understand column list inference for updates.
// Update does not automatically update the record in case of default values. Use .Reload() to refresh the records.
func (o *ApplicationReviewNote) Update(ctx context.Context, exec boil.ContextExecutor, columns boil.Columns) (int64, error) {
	if !boil.TimestampsAreSkipped(ctx) {
		currTime := time.Now().In(boil.GetLocation())

		o.UpdatedAt = currTime
	}

	var err error
	if err = o.doBeforeUpdateHooks(ctx, exec); err != nil {
		return 0, err
	}
	key := makeCacheKey(columns, nil)
	applicationReviewNoteUpdateCacheMut.RLock()
	cache, cached := applicationReviewNoteUpdateCache[key]
	applicationReviewNoteUpdateCacheMut.RUnlock()

	if !cached {
		wl := columns.UpdateColumnSet(
			applicationReviewNoteAllColumns,
			applicationReviewNotePrimaryKeyColumns,
		)

		if !columns.IsWhitelist() {
			wl = strmangle.SetComplement(wl, []string{"created_at"})
		}
		if len(wl) == 0 {
			return 0, errors.New("db_models: unable to update application_review_notes, could not build whitelist")
		}

		cache.query = fmt.Sprintf("UPDATE \"application_review_notes\" SET %s WHERE %s",
			strmangle.SetParamNames("\"", "\"", 1, wl),
			strmangle.WhereClause("\"", "\"", len(wl)+1, applicationReviewNotePrimaryKeyColumns),
		)
		cache.valueMapping, err = queries.BindMapping(applicationReviewNoteType, applicationReviewNoteMapping, append(wl, applicationReviewNotePrimaryKeyColumns...))
		if err != nil {
			return 0, err
		}
	}

	values := queries.ValuesFromMapping(reflect.Indirect(reflect.ValueOf(o)), cache.valueMapping)

	if boil.IsDebug(ctx) {
		writer := boil.DebugWriterFrom(ctx)
		fmt.Fprintln(writer, cache.query)
		fmt.Fprintln(writer, values)
	}
	var result sql.Result
	result, err = exec.ExecContext(ctx, cache.query, values...)
	if err != nil {
		return 0, errors.Wrap(err, "db_models: unable to update application_review_notes row")
	}

	rowsAff, err := result.RowsAffected()
	if err != nil {
		return 0, errors.Wrap(err, "db_models: failed to get rows affected by update for application_review_notes")
	}

	if !cached {
		applicationReviewNoteUpdateCacheMut.Lock()
		applicationReviewNoteUpdateCache[key] = cache
		applicationReviewNoteUpdateCacheMut.Unlock()
	}

	return rowsAff, o.doAfterUpdateHooks(ctx, exec)
}

// UpdateAll updates all rows with the specified column values.
func (q applicationReviewNoteQuery) UpdateAll(ctx context.Context, exec boil.ContextExecutor, cols M) (int64, error) {
	queries.SetUpdate(q.Query, cols)

	result, err := q.Query.ExecContext(ctx, exec)
	if err != nil {
		return 0, errors.Wrap(err, "db_models: unable to update all for application_review_notes")
	}

	rowsAff, err := result.RowsAffected()
	if err != nil {
		return 0, errors.Wrap(err, "db_models: unable to retrieve rows affected for application_review_notes")
	}

	return rowsAff, nil
}

// UpdateAll updates all rows with the specified column values, using an executor.
func (o ApplicationReviewNoteSlice) UpdateAll(ctx context.Context, exec boil.ContextExecutor, cols M) (int64, error) {
	ln := int64(len(o))
	if ln == 0 {
		return 0, nil
	}

	if len(cols) == 0 {
		return 0, errors.New("db_models: update all requires at least one column argument")
	}

	colNames := make([]string, len(cols))
	args := make([]interface{}, len(cols))

	i := 0
	for name, value := range cols {
		colNames[i] = name
		args[i] = value
		i++
	}

	// Append all of the primary key values for each column
	for _, obj := range o {
		pkeyArgs := queries.ValuesFromMapping(reflect.Indirect(reflect.ValueOf(obj)), applicationReviewNotePrimaryKeyMapping)
		args = append(args, pkeyArgs...)
	}

	sql := fmt.Sprintf("UPDATE \"application_review_notes\" SET %s WHERE %s",
		strmangle.SetParamNames("\"", "\"", 1, colNames),
		strmangle.WhereClauseRepeated(string(dialect.LQ), string(dialect.RQ), len(colNames)+1, applicationReviewNotePrimaryKeyColumns, len(o)))

	if boil.IsDebug(ctx) {
		writer := boil.DebugWriterFrom(ctx)
		fmt.Fprintln(writer, sql)
		fmt.Fprintln(writer, args...)
	}
	result, err := exec.ExecContext(ctx, sql, args...)
	if err != nil {
		return 0, errors.Wrap(err, "db_models: unable to update all in applicationReviewNote slice")
	}

	rowsAff, err := result.RowsAffected()
	if err != nil {
		return 0, errors.Wrap(err, "db_models: unable to retrieve rows affected all in update all applicationReviewNote")
	}
	return rowsAff, nil
}

// Upsert attempts an insert using an executor, and does an update or ignore on conflict.
// See boil.Columns documentation for how to properly use updateColumns and insertColumns.
func (o *ApplicationReviewNote) Upsert(ctx context.Context, exec boil.ContextExecutor, updateOnConflict bool, conflictColumns []string, updateColumns, insertColumns boil.Columns) error {
	if o == nil {
		return errors.New("db_models: no application_review_notes provided for upsert")
	}
	if !boil.TimestampsAreSkipped(ctx) {
		currTime := time.Now().In(boil.GetLocation())

		o.UpdatedAt = currTime
		if o.CreatedAt.IsZero() {
			o.CreatedAt = currTime
		}
	}

	if err := o.doBeforeUpsertHooks(ctx, exec); err != nil {
		return err
	}

	nzDefaults := queries.NonZeroDefaultSet(applicationReviewNoteColumnsWithDefault, o)

	// Build cache key in-line uglily - mysql vs psql problems
	buf := strmangle.GetBuffer()
	if updateOnConflict {
		buf.WriteByte('t')
	} else {
		buf.WriteByte('f')
	}
	buf.WriteByte('.')
	for _, c := range conflictColumns {
		buf.WriteString(c)
	}
	buf.WriteByte('.')
	buf.WriteString(strconv.Itoa(updateColumns.Kind))
	for _, c := range updateColumns.Cols {
		buf.WriteString(c)
	}
	buf.WriteByte('.')
	buf.WriteString(strconv.Itoa(insertColumns.Kind))
	for _, c := range insertColumns.Cols {
		buf.WriteString(c)
	}
	buf.WriteByte('.')
	for _, c := range nzDefaults {
		buf.WriteString(c)
	}
	key := buf.String()
	strmangle.PutBuffer(buf)

	applicationReviewNoteUpsertCacheMut.RLock()
	cache, cached := applicationReviewNoteUpsertCache[key]
	applicationReviewNoteUpsertCacheMut.RUnlock()

	var err error

	if !cached {
		insert, ret := insertColumns.InsertColumnSet(
			applicationReviewNoteAllColumns,
			applicationReviewNoteColumnsWithDefault,
			applicationReviewNoteColumnsWithoutDefault,
			nzDefaults,
		)

		update := updateColumns.UpdateColumnSet(
			applicationReviewNoteAllColumns,
			applicationReviewNotePrimaryKeyColumns,
		)

		if updateOnConflict && len(update) == 0 {
			return errors.New("db_models: unable to upsert application_review_notes, could not build update column list")
		}

		conflict := conflictColumns
		if len(conflict) == 0 {
			conflict = make([]string, len(applicationReviewNotePrimaryKeyColumns))
			copy(conflict, applicationReviewNotePrimaryKeyColumns)
		}
		cache.query = buildUpsertQueryPostgres(dialect, "\"application_review_notes\"", updateOnConflict, ret, update, conflict, insert)

		cache.valueMapping, err = queries.BindMapping(applicationReviewNoteType, applicationReviewNoteMapping, insert)
		if err != nil {
			return err
		}
		if len(ret) != 0 {
			cache.retMapping, err = queries.BindMapping(applicationReviewNoteType, applicationReviewNoteMapping, ret)
			if err != nil {
				return err
			}
		}
	}

	value := reflect.Indirect(reflect.ValueOf(o))
	vals := queries.ValuesFromMapping(value, cache.valueMapping)
	var returns []interface{}
	if len(cache.retMapping) != 0 {
		returns = queries.PtrsFromMapping(value, cache.retMapping)
	}

	if boil.IsDebug(ctx) {
		writer := boil.DebugWriterFrom(ctx)
		fmt.Fprintln(writer, cache.query)
		fmt.Fprintln(writer, vals)
	}
	if len(cache.retMapping) != 0 {
		err = exec.QueryRowContext(ctx, cache.query, vals...).Scan(returns...)
		if errors.Is(err, sql.ErrNoRows) {
			err = nil // Postgres doesn't return anything when there's no update
		}
	} else {
		_, err = exec.ExecContext(ctx, cache.query, vals...)
	}
	if err != nil {
		return errors.Wrap(err, "db_models: unable to upsert application_review_notes")
	}

	if !cached {
		applicationReviewNoteUpsertCacheMut.Lock()
		applicationReviewNoteUpsertCache[key] = cache
		applicationReviewNoteUpsertCacheMut.Unlock()
	}

	return o.doAfterUpsertHooks(ctx, exec)
}

// Delete deletes a single ApplicationReviewNote record with an executor.
// Delete will match against the primary key column to find the record to delete.
func (o *ApplicationReviewNote) Delete(ctx context.Context, exec boil.ContextExecutor) (int64, error) {
	if o == nil {
		return 0, errors.New("db_models: no ApplicationReviewNote provided for delete")
	}

	if err := o.doBeforeDeleteHooks(ctx, exec); err != nil {
		return 0, err
	}

	args := queries.ValuesFromMapping(reflect.Indirect(reflect.ValueOf(o)), applicationReviewNotePrimaryKeyMapping)
	sql := "DELETE FROM \"application_review_notes\" WHERE \"id\"=$1"

	if boil.IsDebug(ctx) {
		writer := boil.DebugWriterFrom(ctx)
		fmt.Fprintln(writer, sql)
		fmt.Fprintln(writer, args...)
	}
	result, err := exec.ExecContext(ctx, sql, args...)
	if err != nil {
		return 0, errors.Wrap(err, "db_models: unable to delete from application_review_notes")
	}

	rowsAff, err := result.RowsAffected()
	if err != nil {
		return 0, errors.Wrap(err, "db_models: failed to get rows affected by delete for application_review_notes")
	}

	if err := o.doAfterDeleteHooks(ctx, exec); err != nil {
		return 0, err
	}

	return rowsAff, nil
}

// DeleteAll deletes all matching rows.
func (q applicationReviewNoteQuery) DeleteAll(ctx context.Context, exec boil.ContextExecutor) (int64, error) {
	if q.Query == nil {
		return 0, errors.New("db_models: no applicationReviewNoteQuery provided for delete all")
	}

	queries.SetDelete(q.Query)

	result, err := q.Query.ExecContext(ctx, exec)
	if err != nil {
		return 0, errors.Wrap(err, "db_models: unable to delete all from application_review_notes")
	}

	rowsAff, err := result.RowsAffected()
	if err != nil {
		return 0, errors.Wrap(err, "db_models: failed to get rows affected by deleteall for application_review_notes")
	}

	return rowsAff, nil
}

// DeleteAll deletes all rows in the slice, using an executor.
func (o ApplicationReviewNoteSlice) DeleteAll(ctx context.Context, exec boil.ContextExecutor) (int64, error) {
	if len(o) == 0 {
		return 0, nil
	}

	if len(applicationReviewNoteBeforeDeleteHooks) != 0 {
		for _, obj := range o {
			if err := obj.doBeforeDeleteHooks(ctx, exec); err != nil {
				return 0, err
			}
		}
	}

	var args []interface{}
	for _, obj := range o {
		pkeyArgs := queries.ValuesFromMapping(reflect.Indirect(reflect.ValueOf(obj)), applicationReviewNotePrimaryKeyMapping)
		args = append(args, pkeyArgs...)
	}

	sql := "DELETE FROM \"application_review_notes\" WHERE " +
		strmangle.WhereClauseRepeated(string(dialect.LQ), string(dialect.RQ), 1, applicationReviewNotePrimaryKeyColumns, len(o))

	if boil.IsDebug(ctx) {
		writer := boil.DebugWriterFrom(ctx)
		fmt.Fprintln(writer, sql)
		fmt.Fprintln(writer, args)
	}
	result, err := exec.ExecContext(ctx, sql, args...)
	if err != nil {
		return 0, errors.Wrap(err, "db_models: unable to delete all from applicationReviewNote slice")
	}

	rowsAff, err := result.RowsAffected()
	if err != nil {
		return 0, errors.Wrap(err, "db_models: failed to get rows affected by deleteall for application_review_notes")
	}

	if len(applicationReviewNoteAfterDeleteHooks) != 0 {
		for _, obj := range o {
			if err := obj.doAfterDeleteHooks(ctx, exec); err != nil {
				return 0, err
			}
		}
	}

	return rowsAff, nil
}

// Reload refetches the object from the database
// using the primary keys with an executor.
func (o *ApplicationReviewNote) Reload(ctx context.Context, exec boil.ContextExecutor) error {
	ret, err := FindApplicationReviewNote(ctx, exec, o.ID)
	if err != nil {
		return err
	}

	*o = *ret
	return nil
}

// ReloadAll refetches every row with matching primary key column values
// and overwrites the original object slice with the newly updated slice.
func (o *ApplicationReviewNoteSlice) ReloadAll(ctx context.Context, exec boil.ContextExecutor) error {
	if o == nil || len(*o) == 0 {
		return nil
	}

	slice := ApplicationReviewNoteSlice{}
	var args []interface{}
	for _, obj := range *o {
		pkeyArgs := queries.ValuesFromMapping(reflect.Indirect(reflect.ValueOf(obj)), applicationReviewNotePrimaryKeyMapping)
		args = append(args, pkeyArgs...)
	}

	sql := "SELECT \"application_review_notes\".* FROM \"application_review_notes\" WHERE " +
		strmangle.WhereClauseRepeated(string(dialect.LQ), string(dialect.RQ), 1, applicationReviewNotePrimaryKeyColumns, len(*o))

	q := queries.Raw(sql, args...)

	err := q.Bind(ctx, exec, &slice)
	if err != nil {
		return errors.Wrap(err, "db_models: unable to reload all in ApplicationReviewNoteSlice")
	}

	*o = slice

	return nil
}

// ApplicationReviewNoteExists checks if the ApplicationReviewNote row exists.
func ApplicationReviewNoteExists(ctx context.Context, exec boil.ContextExecutor, iD string) (bool, error) {
	var exists bool
	sql := "select exists(select 1 from \"application_review_notes\" where \"id\"=$1 limit 1)"

	if boil.IsDebug(ctx) {
		writer := boil.DebugWriterFrom(ctx)
		fmt.Fprintln(writer, sql)
		fmt.Fprintln(writer, iD)
	}
	row := exec.QueryRowContext(ctx, sql, iD)

	err := row.Scan(&exists)
	if err != nil {
		return false, errors.Wrap(err, "db_models: unable to check if application_review_notes exists")
	}

	return exists, nil
}
