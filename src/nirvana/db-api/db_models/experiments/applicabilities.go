// Code generated by SQLBoiler (https://github.com/volatiletech/sqlboiler). DO NOT EDIT.
// This file is meant to be re-generated in place and/or deleted at any time.

package experiments

import (
	"context"
	"database/sql"
	"fmt"
	"reflect"
	"strconv"
	"strings"
	"sync"
	"time"

	"github.com/friendsofgo/errors"
	"github.com/volatiletech/null/v8"
	"github.com/volatiletech/sqlboiler/v4/boil"
	"github.com/volatiletech/sqlboiler/v4/queries"
	"github.com/volatiletech/sqlboiler/v4/queries/qm"
	"github.com/volatiletech/sqlboiler/v4/queries/qmhelper"
	"github.com/volatiletech/strmangle"
)

// Applicability is an object representing the database table.
type Applicability struct {
	ID           string    `boil:"id" json:"id" toml:"id" yaml:"id"`
	ExperimentID string    `boil:"experiment_id" json:"experiment_id" toml:"experiment_id" yaml:"experiment_id"`
	SubjectID    string    `boil:"subject_id" json:"subject_id" toml:"subject_id" yaml:"subject_id"`
	Applied      bool      `boil:"applied" json:"applied" toml:"applied" yaml:"applied"`
	Metadata     null.JSON `boil:"metadata" json:"metadata,omitempty" toml:"metadata" yaml:"metadata,omitempty"`
	CreatedAt    time.Time `boil:"created_at" json:"created_at" toml:"created_at" yaml:"created_at"`

	R *applicabilityR `boil:"" json:"" toml:"" yaml:""`
	L applicabilityL  `boil:"-" json:"-" toml:"-" yaml:"-"`
}

var ApplicabilityColumns = struct {
	ID           string
	ExperimentID string
	SubjectID    string
	Applied      string
	Metadata     string
	CreatedAt    string
}{
	ID:           "id",
	ExperimentID: "experiment_id",
	SubjectID:    "subject_id",
	Applied:      "applied",
	Metadata:     "metadata",
	CreatedAt:    "created_at",
}

var ApplicabilityTableColumns = struct {
	ID           string
	ExperimentID string
	SubjectID    string
	Applied      string
	Metadata     string
	CreatedAt    string
}{
	ID:           "applicabilities.id",
	ExperimentID: "applicabilities.experiment_id",
	SubjectID:    "applicabilities.subject_id",
	Applied:      "applicabilities.applied",
	Metadata:     "applicabilities.metadata",
	CreatedAt:    "applicabilities.created_at",
}

// Generated where

type whereHelperstring struct{ field string }

func (w whereHelperstring) EQ(x string) qm.QueryMod  { return qmhelper.Where(w.field, qmhelper.EQ, x) }
func (w whereHelperstring) NEQ(x string) qm.QueryMod { return qmhelper.Where(w.field, qmhelper.NEQ, x) }
func (w whereHelperstring) LT(x string) qm.QueryMod  { return qmhelper.Where(w.field, qmhelper.LT, x) }
func (w whereHelperstring) LTE(x string) qm.QueryMod { return qmhelper.Where(w.field, qmhelper.LTE, x) }
func (w whereHelperstring) GT(x string) qm.QueryMod  { return qmhelper.Where(w.field, qmhelper.GT, x) }
func (w whereHelperstring) GTE(x string) qm.QueryMod { return qmhelper.Where(w.field, qmhelper.GTE, x) }
func (w whereHelperstring) IN(slice []string) qm.QueryMod {
	values := make([]interface{}, 0, len(slice))
	for _, value := range slice {
		values = append(values, value)
	}
	return qm.WhereIn(fmt.Sprintf("%s IN ?", w.field), values...)
}
func (w whereHelperstring) NIN(slice []string) qm.QueryMod {
	values := make([]interface{}, 0, len(slice))
	for _, value := range slice {
		values = append(values, value)
	}
	return qm.WhereNotIn(fmt.Sprintf("%s NOT IN ?", w.field), values...)
}

type whereHelperbool struct{ field string }

func (w whereHelperbool) EQ(x bool) qm.QueryMod  { return qmhelper.Where(w.field, qmhelper.EQ, x) }
func (w whereHelperbool) NEQ(x bool) qm.QueryMod { return qmhelper.Where(w.field, qmhelper.NEQ, x) }
func (w whereHelperbool) LT(x bool) qm.QueryMod  { return qmhelper.Where(w.field, qmhelper.LT, x) }
func (w whereHelperbool) LTE(x bool) qm.QueryMod { return qmhelper.Where(w.field, qmhelper.LTE, x) }
func (w whereHelperbool) GT(x bool) qm.QueryMod  { return qmhelper.Where(w.field, qmhelper.GT, x) }
func (w whereHelperbool) GTE(x bool) qm.QueryMod { return qmhelper.Where(w.field, qmhelper.GTE, x) }

type whereHelpernull_JSON struct{ field string }

func (w whereHelpernull_JSON) EQ(x null.JSON) qm.QueryMod {
	return qmhelper.WhereNullEQ(w.field, false, x)
}
func (w whereHelpernull_JSON) NEQ(x null.JSON) qm.QueryMod {
	return qmhelper.WhereNullEQ(w.field, true, x)
}
func (w whereHelpernull_JSON) LT(x null.JSON) qm.QueryMod {
	return qmhelper.Where(w.field, qmhelper.LT, x)
}
func (w whereHelpernull_JSON) LTE(x null.JSON) qm.QueryMod {
	return qmhelper.Where(w.field, qmhelper.LTE, x)
}
func (w whereHelpernull_JSON) GT(x null.JSON) qm.QueryMod {
	return qmhelper.Where(w.field, qmhelper.GT, x)
}
func (w whereHelpernull_JSON) GTE(x null.JSON) qm.QueryMod {
	return qmhelper.Where(w.field, qmhelper.GTE, x)
}

func (w whereHelpernull_JSON) IsNull() qm.QueryMod    { return qmhelper.WhereIsNull(w.field) }
func (w whereHelpernull_JSON) IsNotNull() qm.QueryMod { return qmhelper.WhereIsNotNull(w.field) }

type whereHelpertime_Time struct{ field string }

func (w whereHelpertime_Time) EQ(x time.Time) qm.QueryMod {
	return qmhelper.Where(w.field, qmhelper.EQ, x)
}
func (w whereHelpertime_Time) NEQ(x time.Time) qm.QueryMod {
	return qmhelper.Where(w.field, qmhelper.NEQ, x)
}
func (w whereHelpertime_Time) LT(x time.Time) qm.QueryMod {
	return qmhelper.Where(w.field, qmhelper.LT, x)
}
func (w whereHelpertime_Time) LTE(x time.Time) qm.QueryMod {
	return qmhelper.Where(w.field, qmhelper.LTE, x)
}
func (w whereHelpertime_Time) GT(x time.Time) qm.QueryMod {
	return qmhelper.Where(w.field, qmhelper.GT, x)
}
func (w whereHelpertime_Time) GTE(x time.Time) qm.QueryMod {
	return qmhelper.Where(w.field, qmhelper.GTE, x)
}

var ApplicabilityWhere = struct {
	ID           whereHelperstring
	ExperimentID whereHelperstring
	SubjectID    whereHelperstring
	Applied      whereHelperbool
	Metadata     whereHelpernull_JSON
	CreatedAt    whereHelpertime_Time
}{
	ID:           whereHelperstring{field: "\"experiments\".\"applicabilities\".\"id\""},
	ExperimentID: whereHelperstring{field: "\"experiments\".\"applicabilities\".\"experiment_id\""},
	SubjectID:    whereHelperstring{field: "\"experiments\".\"applicabilities\".\"subject_id\""},
	Applied:      whereHelperbool{field: "\"experiments\".\"applicabilities\".\"applied\""},
	Metadata:     whereHelpernull_JSON{field: "\"experiments\".\"applicabilities\".\"metadata\""},
	CreatedAt:    whereHelpertime_Time{field: "\"experiments\".\"applicabilities\".\"created_at\""},
}

// ApplicabilityRels is where relationship names are stored.
var ApplicabilityRels = struct {
	Experiment string
}{
	Experiment: "Experiment",
}

// applicabilityR is where relationships are stored.
type applicabilityR struct {
	Experiment *ExperimentDetail `boil:"Experiment" json:"Experiment" toml:"Experiment" yaml:"Experiment"`
}

// NewStruct creates a new relationship struct
func (*applicabilityR) NewStruct() *applicabilityR {
	return &applicabilityR{}
}

// applicabilityL is where Load methods for each relationship are stored.
type applicabilityL struct{}

var (
	applicabilityAllColumns            = []string{"id", "experiment_id", "subject_id", "applied", "metadata", "created_at"}
	applicabilityColumnsWithoutDefault = []string{"id", "experiment_id", "subject_id", "applied", "created_at"}
	applicabilityColumnsWithDefault    = []string{"metadata"}
	applicabilityPrimaryKeyColumns     = []string{"id"}
	applicabilityGeneratedColumns      = []string{}
)

type (
	// ApplicabilitySlice is an alias for a slice of pointers to Applicability.
	// This should almost always be used instead of []Applicability.
	ApplicabilitySlice []*Applicability
	// ApplicabilityHook is the signature for custom Applicability hook methods
	ApplicabilityHook func(context.Context, boil.ContextExecutor, *Applicability) error

	applicabilityQuery struct {
		*queries.Query
	}
)

// Cache for insert, update and upsert
var (
	applicabilityType                 = reflect.TypeOf(&Applicability{})
	applicabilityMapping              = queries.MakeStructMapping(applicabilityType)
	applicabilityPrimaryKeyMapping, _ = queries.BindMapping(applicabilityType, applicabilityMapping, applicabilityPrimaryKeyColumns)
	applicabilityInsertCacheMut       sync.RWMutex
	applicabilityInsertCache          = make(map[string]insertCache)
	applicabilityUpdateCacheMut       sync.RWMutex
	applicabilityUpdateCache          = make(map[string]updateCache)
	applicabilityUpsertCacheMut       sync.RWMutex
	applicabilityUpsertCache          = make(map[string]insertCache)
)

var (
	// Force time package dependency for automated UpdatedAt/CreatedAt.
	_ = time.Second
	// Force qmhelper dependency for where clause generation (which doesn't
	// always happen)
	_ = qmhelper.Where
)

var applicabilityAfterSelectHooks []ApplicabilityHook

var applicabilityBeforeInsertHooks []ApplicabilityHook
var applicabilityAfterInsertHooks []ApplicabilityHook

var applicabilityBeforeUpdateHooks []ApplicabilityHook
var applicabilityAfterUpdateHooks []ApplicabilityHook

var applicabilityBeforeDeleteHooks []ApplicabilityHook
var applicabilityAfterDeleteHooks []ApplicabilityHook

var applicabilityBeforeUpsertHooks []ApplicabilityHook
var applicabilityAfterUpsertHooks []ApplicabilityHook

// doAfterSelectHooks executes all "after Select" hooks.
func (o *Applicability) doAfterSelectHooks(ctx context.Context, exec boil.ContextExecutor) (err error) {
	if boil.HooksAreSkipped(ctx) {
		return nil
	}

	for _, hook := range applicabilityAfterSelectHooks {
		if err := hook(ctx, exec, o); err != nil {
			return err
		}
	}

	return nil
}

// doBeforeInsertHooks executes all "before insert" hooks.
func (o *Applicability) doBeforeInsertHooks(ctx context.Context, exec boil.ContextExecutor) (err error) {
	if boil.HooksAreSkipped(ctx) {
		return nil
	}

	for _, hook := range applicabilityBeforeInsertHooks {
		if err := hook(ctx, exec, o); err != nil {
			return err
		}
	}

	return nil
}

// doAfterInsertHooks executes all "after Insert" hooks.
func (o *Applicability) doAfterInsertHooks(ctx context.Context, exec boil.ContextExecutor) (err error) {
	if boil.HooksAreSkipped(ctx) {
		return nil
	}

	for _, hook := range applicabilityAfterInsertHooks {
		if err := hook(ctx, exec, o); err != nil {
			return err
		}
	}

	return nil
}

// doBeforeUpdateHooks executes all "before Update" hooks.
func (o *Applicability) doBeforeUpdateHooks(ctx context.Context, exec boil.ContextExecutor) (err error) {
	if boil.HooksAreSkipped(ctx) {
		return nil
	}

	for _, hook := range applicabilityBeforeUpdateHooks {
		if err := hook(ctx, exec, o); err != nil {
			return err
		}
	}

	return nil
}

// doAfterUpdateHooks executes all "after Update" hooks.
func (o *Applicability) doAfterUpdateHooks(ctx context.Context, exec boil.ContextExecutor) (err error) {
	if boil.HooksAreSkipped(ctx) {
		return nil
	}

	for _, hook := range applicabilityAfterUpdateHooks {
		if err := hook(ctx, exec, o); err != nil {
			return err
		}
	}

	return nil
}

// doBeforeDeleteHooks executes all "before Delete" hooks.
func (o *Applicability) doBeforeDeleteHooks(ctx context.Context, exec boil.ContextExecutor) (err error) {
	if boil.HooksAreSkipped(ctx) {
		return nil
	}

	for _, hook := range applicabilityBeforeDeleteHooks {
		if err := hook(ctx, exec, o); err != nil {
			return err
		}
	}

	return nil
}

// doAfterDeleteHooks executes all "after Delete" hooks.
func (o *Applicability) doAfterDeleteHooks(ctx context.Context, exec boil.ContextExecutor) (err error) {
	if boil.HooksAreSkipped(ctx) {
		return nil
	}

	for _, hook := range applicabilityAfterDeleteHooks {
		if err := hook(ctx, exec, o); err != nil {
			return err
		}
	}

	return nil
}

// doBeforeUpsertHooks executes all "before Upsert" hooks.
func (o *Applicability) doBeforeUpsertHooks(ctx context.Context, exec boil.ContextExecutor) (err error) {
	if boil.HooksAreSkipped(ctx) {
		return nil
	}

	for _, hook := range applicabilityBeforeUpsertHooks {
		if err := hook(ctx, exec, o); err != nil {
			return err
		}
	}

	return nil
}

// doAfterUpsertHooks executes all "after Upsert" hooks.
func (o *Applicability) doAfterUpsertHooks(ctx context.Context, exec boil.ContextExecutor) (err error) {
	if boil.HooksAreSkipped(ctx) {
		return nil
	}

	for _, hook := range applicabilityAfterUpsertHooks {
		if err := hook(ctx, exec, o); err != nil {
			return err
		}
	}

	return nil
}

// AddApplicabilityHook registers your hook function for all future operations.
func AddApplicabilityHook(hookPoint boil.HookPoint, applicabilityHook ApplicabilityHook) {
	switch hookPoint {
	case boil.AfterSelectHook:
		applicabilityAfterSelectHooks = append(applicabilityAfterSelectHooks, applicabilityHook)
	case boil.BeforeInsertHook:
		applicabilityBeforeInsertHooks = append(applicabilityBeforeInsertHooks, applicabilityHook)
	case boil.AfterInsertHook:
		applicabilityAfterInsertHooks = append(applicabilityAfterInsertHooks, applicabilityHook)
	case boil.BeforeUpdateHook:
		applicabilityBeforeUpdateHooks = append(applicabilityBeforeUpdateHooks, applicabilityHook)
	case boil.AfterUpdateHook:
		applicabilityAfterUpdateHooks = append(applicabilityAfterUpdateHooks, applicabilityHook)
	case boil.BeforeDeleteHook:
		applicabilityBeforeDeleteHooks = append(applicabilityBeforeDeleteHooks, applicabilityHook)
	case boil.AfterDeleteHook:
		applicabilityAfterDeleteHooks = append(applicabilityAfterDeleteHooks, applicabilityHook)
	case boil.BeforeUpsertHook:
		applicabilityBeforeUpsertHooks = append(applicabilityBeforeUpsertHooks, applicabilityHook)
	case boil.AfterUpsertHook:
		applicabilityAfterUpsertHooks = append(applicabilityAfterUpsertHooks, applicabilityHook)
	}
}

// One returns a single applicability record from the query.
func (q applicabilityQuery) One(ctx context.Context, exec boil.ContextExecutor) (*Applicability, error) {
	o := &Applicability{}

	queries.SetLimit(q.Query, 1)

	err := q.Bind(ctx, exec, o)
	if err != nil {
		if errors.Is(err, sql.ErrNoRows) {
			return nil, sql.ErrNoRows
		}
		return nil, errors.Wrap(err, "experiments: failed to execute a one query for applicabilities")
	}

	if err := o.doAfterSelectHooks(ctx, exec); err != nil {
		return o, err
	}

	return o, nil
}

// All returns all Applicability records from the query.
func (q applicabilityQuery) All(ctx context.Context, exec boil.ContextExecutor) (ApplicabilitySlice, error) {
	var o []*Applicability

	err := q.Bind(ctx, exec, &o)
	if err != nil {
		return nil, errors.Wrap(err, "experiments: failed to assign all query results to Applicability slice")
	}

	if len(applicabilityAfterSelectHooks) != 0 {
		for _, obj := range o {
			if err := obj.doAfterSelectHooks(ctx, exec); err != nil {
				return o, err
			}
		}
	}

	return o, nil
}

// Count returns the count of all Applicability records in the query.
func (q applicabilityQuery) Count(ctx context.Context, exec boil.ContextExecutor) (int64, error) {
	var count int64

	queries.SetSelect(q.Query, nil)
	queries.SetCount(q.Query)

	err := q.Query.QueryRowContext(ctx, exec).Scan(&count)
	if err != nil {
		return 0, errors.Wrap(err, "experiments: failed to count applicabilities rows")
	}

	return count, nil
}

// Exists checks if the row exists in the table.
func (q applicabilityQuery) Exists(ctx context.Context, exec boil.ContextExecutor) (bool, error) {
	var count int64

	queries.SetSelect(q.Query, nil)
	queries.SetCount(q.Query)
	queries.SetLimit(q.Query, 1)

	err := q.Query.QueryRowContext(ctx, exec).Scan(&count)
	if err != nil {
		return false, errors.Wrap(err, "experiments: failed to check if applicabilities exists")
	}

	return count > 0, nil
}

// Experiment pointed to by the foreign key.
func (o *Applicability) Experiment(mods ...qm.QueryMod) experimentDetailQuery {
	queryMods := []qm.QueryMod{
		qm.Where("\"id\" = ?", o.ExperimentID),
	}

	queryMods = append(queryMods, mods...)

	return ExperimentDetails(queryMods...)
}

// LoadExperiment allows an eager lookup of values, cached into the
// loaded structs of the objects. This is for an N-1 relationship.
func (applicabilityL) LoadExperiment(ctx context.Context, e boil.ContextExecutor, singular bool, maybeApplicability interface{}, mods queries.Applicator) error {
	var slice []*Applicability
	var object *Applicability

	if singular {
		object = maybeApplicability.(*Applicability)
	} else {
		slice = *maybeApplicability.(*[]*Applicability)
	}

	args := make([]interface{}, 0, 1)
	if singular {
		if object.R == nil {
			object.R = &applicabilityR{}
		}
		args = append(args, object.ExperimentID)

	} else {
	Outer:
		for _, obj := range slice {
			if obj.R == nil {
				obj.R = &applicabilityR{}
			}

			for _, a := range args {
				if a == obj.ExperimentID {
					continue Outer
				}
			}

			args = append(args, obj.ExperimentID)

		}
	}

	if len(args) == 0 {
		return nil
	}

	query := NewQuery(
		qm.From(`experiments.experiment_details`),
		qm.WhereIn(`experiments.experiment_details.id in ?`, args...),
	)
	if mods != nil {
		mods.Apply(query)
	}

	results, err := query.QueryContext(ctx, e)
	if err != nil {
		return errors.Wrap(err, "failed to eager load ExperimentDetail")
	}

	var resultSlice []*ExperimentDetail
	if err = queries.Bind(results, &resultSlice); err != nil {
		return errors.Wrap(err, "failed to bind eager loaded slice ExperimentDetail")
	}

	if err = results.Close(); err != nil {
		return errors.Wrap(err, "failed to close results of eager load for experiment_details")
	}
	if err = results.Err(); err != nil {
		return errors.Wrap(err, "error occurred during iteration of eager loaded relations for experiment_details")
	}

	if len(applicabilityAfterSelectHooks) != 0 {
		for _, obj := range resultSlice {
			if err := obj.doAfterSelectHooks(ctx, e); err != nil {
				return err
			}
		}
	}

	if len(resultSlice) == 0 {
		return nil
	}

	if singular {
		foreign := resultSlice[0]
		object.R.Experiment = foreign
		if foreign.R == nil {
			foreign.R = &experimentDetailR{}
		}
		foreign.R.ExperimentApplicabilities = append(foreign.R.ExperimentApplicabilities, object)
		return nil
	}

	for _, local := range slice {
		for _, foreign := range resultSlice {
			if local.ExperimentID == foreign.ID {
				local.R.Experiment = foreign
				if foreign.R == nil {
					foreign.R = &experimentDetailR{}
				}
				foreign.R.ExperimentApplicabilities = append(foreign.R.ExperimentApplicabilities, local)
				break
			}
		}
	}

	return nil
}

// SetExperiment of the applicability to the related item.
// Sets o.R.Experiment to related.
// Adds o to related.R.ExperimentApplicabilities.
func (o *Applicability) SetExperiment(ctx context.Context, exec boil.ContextExecutor, insert bool, related *ExperimentDetail) error {
	var err error
	if insert {
		if err = related.Insert(ctx, exec, boil.Infer()); err != nil {
			return errors.Wrap(err, "failed to insert into foreign table")
		}
	}

	updateQuery := fmt.Sprintf(
		"UPDATE \"experiments\".\"applicabilities\" SET %s WHERE %s",
		strmangle.SetParamNames("\"", "\"", 1, []string{"experiment_id"}),
		strmangle.WhereClause("\"", "\"", 2, applicabilityPrimaryKeyColumns),
	)
	values := []interface{}{related.ID, o.ID}

	if boil.IsDebug(ctx) {
		writer := boil.DebugWriterFrom(ctx)
		fmt.Fprintln(writer, updateQuery)
		fmt.Fprintln(writer, values)
	}
	if _, err = exec.ExecContext(ctx, updateQuery, values...); err != nil {
		return errors.Wrap(err, "failed to update local table")
	}

	o.ExperimentID = related.ID
	if o.R == nil {
		o.R = &applicabilityR{
			Experiment: related,
		}
	} else {
		o.R.Experiment = related
	}

	if related.R == nil {
		related.R = &experimentDetailR{
			ExperimentApplicabilities: ApplicabilitySlice{o},
		}
	} else {
		related.R.ExperimentApplicabilities = append(related.R.ExperimentApplicabilities, o)
	}

	return nil
}

// Applicabilities retrieves all the records using an executor.
func Applicabilities(mods ...qm.QueryMod) applicabilityQuery {
	mods = append(mods, qm.From("\"experiments\".\"applicabilities\""))
	q := NewQuery(mods...)
	if len(queries.GetSelect(q)) == 0 {
		queries.SetSelect(q, []string{"\"experiments\".\"applicabilities\".*"})
	}

	return applicabilityQuery{q}
}

// FindApplicability retrieves a single record by ID with an executor.
// If selectCols is empty Find will return all columns.
func FindApplicability(ctx context.Context, exec boil.ContextExecutor, iD string, selectCols ...string) (*Applicability, error) {
	applicabilityObj := &Applicability{}

	sel := "*"
	if len(selectCols) > 0 {
		sel = strings.Join(strmangle.IdentQuoteSlice(dialect.LQ, dialect.RQ, selectCols), ",")
	}
	query := fmt.Sprintf(
		"select %s from \"experiments\".\"applicabilities\" where \"id\"=$1", sel,
	)

	q := queries.Raw(query, iD)

	err := q.Bind(ctx, exec, applicabilityObj)
	if err != nil {
		if errors.Is(err, sql.ErrNoRows) {
			return nil, sql.ErrNoRows
		}
		return nil, errors.Wrap(err, "experiments: unable to select from applicabilities")
	}

	if err = applicabilityObj.doAfterSelectHooks(ctx, exec); err != nil {
		return applicabilityObj, err
	}

	return applicabilityObj, nil
}

// Insert a single record using an executor.
// See boil.Columns.InsertColumnSet documentation to understand column list inference for inserts.
func (o *Applicability) Insert(ctx context.Context, exec boil.ContextExecutor, columns boil.Columns) error {
	if o == nil {
		return errors.New("experiments: no applicabilities provided for insertion")
	}

	var err error
	if !boil.TimestampsAreSkipped(ctx) {
		currTime := time.Now().In(boil.GetLocation())

		if o.CreatedAt.IsZero() {
			o.CreatedAt = currTime
		}
	}

	if err := o.doBeforeInsertHooks(ctx, exec); err != nil {
		return err
	}

	nzDefaults := queries.NonZeroDefaultSet(applicabilityColumnsWithDefault, o)

	key := makeCacheKey(columns, nzDefaults)
	applicabilityInsertCacheMut.RLock()
	cache, cached := applicabilityInsertCache[key]
	applicabilityInsertCacheMut.RUnlock()

	if !cached {
		wl, returnColumns := columns.InsertColumnSet(
			applicabilityAllColumns,
			applicabilityColumnsWithDefault,
			applicabilityColumnsWithoutDefault,
			nzDefaults,
		)

		cache.valueMapping, err = queries.BindMapping(applicabilityType, applicabilityMapping, wl)
		if err != nil {
			return err
		}
		cache.retMapping, err = queries.BindMapping(applicabilityType, applicabilityMapping, returnColumns)
		if err != nil {
			return err
		}
		if len(wl) != 0 {
			cache.query = fmt.Sprintf("INSERT INTO \"experiments\".\"applicabilities\" (\"%s\") %%sVALUES (%s)%%s", strings.Join(wl, "\",\""), strmangle.Placeholders(dialect.UseIndexPlaceholders, len(wl), 1, 1))
		} else {
			cache.query = "INSERT INTO \"experiments\".\"applicabilities\" %sDEFAULT VALUES%s"
		}

		var queryOutput, queryReturning string

		if len(cache.retMapping) != 0 {
			queryReturning = fmt.Sprintf(" RETURNING \"%s\"", strings.Join(returnColumns, "\",\""))
		}

		cache.query = fmt.Sprintf(cache.query, queryOutput, queryReturning)
	}

	value := reflect.Indirect(reflect.ValueOf(o))
	vals := queries.ValuesFromMapping(value, cache.valueMapping)

	if boil.IsDebug(ctx) {
		writer := boil.DebugWriterFrom(ctx)
		fmt.Fprintln(writer, cache.query)
		fmt.Fprintln(writer, vals)
	}

	if len(cache.retMapping) != 0 {
		err = exec.QueryRowContext(ctx, cache.query, vals...).Scan(queries.PtrsFromMapping(value, cache.retMapping)...)
	} else {
		_, err = exec.ExecContext(ctx, cache.query, vals...)
	}

	if err != nil {
		return errors.Wrap(err, "experiments: unable to insert into applicabilities")
	}

	if !cached {
		applicabilityInsertCacheMut.Lock()
		applicabilityInsertCache[key] = cache
		applicabilityInsertCacheMut.Unlock()
	}

	return o.doAfterInsertHooks(ctx, exec)
}

// Update uses an executor to update the Applicability.
// See boil.Columns.UpdateColumnSet documentation to understand column list inference for updates.
// Update does not automatically update the record in case of default values. Use .Reload() to refresh the records.
func (o *Applicability) Update(ctx context.Context, exec boil.ContextExecutor, columns boil.Columns) (int64, error) {
	var err error
	if err = o.doBeforeUpdateHooks(ctx, exec); err != nil {
		return 0, err
	}
	key := makeCacheKey(columns, nil)
	applicabilityUpdateCacheMut.RLock()
	cache, cached := applicabilityUpdateCache[key]
	applicabilityUpdateCacheMut.RUnlock()

	if !cached {
		wl := columns.UpdateColumnSet(
			applicabilityAllColumns,
			applicabilityPrimaryKeyColumns,
		)

		if !columns.IsWhitelist() {
			wl = strmangle.SetComplement(wl, []string{"created_at"})
		}
		if len(wl) == 0 {
			return 0, errors.New("experiments: unable to update applicabilities, could not build whitelist")
		}

		cache.query = fmt.Sprintf("UPDATE \"experiments\".\"applicabilities\" SET %s WHERE %s",
			strmangle.SetParamNames("\"", "\"", 1, wl),
			strmangle.WhereClause("\"", "\"", len(wl)+1, applicabilityPrimaryKeyColumns),
		)
		cache.valueMapping, err = queries.BindMapping(applicabilityType, applicabilityMapping, append(wl, applicabilityPrimaryKeyColumns...))
		if err != nil {
			return 0, err
		}
	}

	values := queries.ValuesFromMapping(reflect.Indirect(reflect.ValueOf(o)), cache.valueMapping)

	if boil.IsDebug(ctx) {
		writer := boil.DebugWriterFrom(ctx)
		fmt.Fprintln(writer, cache.query)
		fmt.Fprintln(writer, values)
	}
	var result sql.Result
	result, err = exec.ExecContext(ctx, cache.query, values...)
	if err != nil {
		return 0, errors.Wrap(err, "experiments: unable to update applicabilities row")
	}

	rowsAff, err := result.RowsAffected()
	if err != nil {
		return 0, errors.Wrap(err, "experiments: failed to get rows affected by update for applicabilities")
	}

	if !cached {
		applicabilityUpdateCacheMut.Lock()
		applicabilityUpdateCache[key] = cache
		applicabilityUpdateCacheMut.Unlock()
	}

	return rowsAff, o.doAfterUpdateHooks(ctx, exec)
}

// UpdateAll updates all rows with the specified column values.
func (q applicabilityQuery) UpdateAll(ctx context.Context, exec boil.ContextExecutor, cols M) (int64, error) {
	queries.SetUpdate(q.Query, cols)

	result, err := q.Query.ExecContext(ctx, exec)
	if err != nil {
		return 0, errors.Wrap(err, "experiments: unable to update all for applicabilities")
	}

	rowsAff, err := result.RowsAffected()
	if err != nil {
		return 0, errors.Wrap(err, "experiments: unable to retrieve rows affected for applicabilities")
	}

	return rowsAff, nil
}

// UpdateAll updates all rows with the specified column values, using an executor.
func (o ApplicabilitySlice) UpdateAll(ctx context.Context, exec boil.ContextExecutor, cols M) (int64, error) {
	ln := int64(len(o))
	if ln == 0 {
		return 0, nil
	}

	if len(cols) == 0 {
		return 0, errors.New("experiments: update all requires at least one column argument")
	}

	colNames := make([]string, len(cols))
	args := make([]interface{}, len(cols))

	i := 0
	for name, value := range cols {
		colNames[i] = name
		args[i] = value
		i++
	}

	// Append all of the primary key values for each column
	for _, obj := range o {
		pkeyArgs := queries.ValuesFromMapping(reflect.Indirect(reflect.ValueOf(obj)), applicabilityPrimaryKeyMapping)
		args = append(args, pkeyArgs...)
	}

	sql := fmt.Sprintf("UPDATE \"experiments\".\"applicabilities\" SET %s WHERE %s",
		strmangle.SetParamNames("\"", "\"", 1, colNames),
		strmangle.WhereClauseRepeated(string(dialect.LQ), string(dialect.RQ), len(colNames)+1, applicabilityPrimaryKeyColumns, len(o)))

	if boil.IsDebug(ctx) {
		writer := boil.DebugWriterFrom(ctx)
		fmt.Fprintln(writer, sql)
		fmt.Fprintln(writer, args...)
	}
	result, err := exec.ExecContext(ctx, sql, args...)
	if err != nil {
		return 0, errors.Wrap(err, "experiments: unable to update all in applicability slice")
	}

	rowsAff, err := result.RowsAffected()
	if err != nil {
		return 0, errors.Wrap(err, "experiments: unable to retrieve rows affected all in update all applicability")
	}
	return rowsAff, nil
}

// Upsert attempts an insert using an executor, and does an update or ignore on conflict.
// See boil.Columns documentation for how to properly use updateColumns and insertColumns.
func (o *Applicability) Upsert(ctx context.Context, exec boil.ContextExecutor, updateOnConflict bool, conflictColumns []string, updateColumns, insertColumns boil.Columns) error {
	if o == nil {
		return errors.New("experiments: no applicabilities provided for upsert")
	}
	if !boil.TimestampsAreSkipped(ctx) {
		currTime := time.Now().In(boil.GetLocation())

		if o.CreatedAt.IsZero() {
			o.CreatedAt = currTime
		}
	}

	if err := o.doBeforeUpsertHooks(ctx, exec); err != nil {
		return err
	}

	nzDefaults := queries.NonZeroDefaultSet(applicabilityColumnsWithDefault, o)

	// Build cache key in-line uglily - mysql vs psql problems
	buf := strmangle.GetBuffer()
	if updateOnConflict {
		buf.WriteByte('t')
	} else {
		buf.WriteByte('f')
	}
	buf.WriteByte('.')
	for _, c := range conflictColumns {
		buf.WriteString(c)
	}
	buf.WriteByte('.')
	buf.WriteString(strconv.Itoa(updateColumns.Kind))
	for _, c := range updateColumns.Cols {
		buf.WriteString(c)
	}
	buf.WriteByte('.')
	buf.WriteString(strconv.Itoa(insertColumns.Kind))
	for _, c := range insertColumns.Cols {
		buf.WriteString(c)
	}
	buf.WriteByte('.')
	for _, c := range nzDefaults {
		buf.WriteString(c)
	}
	key := buf.String()
	strmangle.PutBuffer(buf)

	applicabilityUpsertCacheMut.RLock()
	cache, cached := applicabilityUpsertCache[key]
	applicabilityUpsertCacheMut.RUnlock()

	var err error

	if !cached {
		insert, ret := insertColumns.InsertColumnSet(
			applicabilityAllColumns,
			applicabilityColumnsWithDefault,
			applicabilityColumnsWithoutDefault,
			nzDefaults,
		)

		update := updateColumns.UpdateColumnSet(
			applicabilityAllColumns,
			applicabilityPrimaryKeyColumns,
		)

		if updateOnConflict && len(update) == 0 {
			return errors.New("experiments: unable to upsert applicabilities, could not build update column list")
		}

		conflict := conflictColumns
		if len(conflict) == 0 {
			conflict = make([]string, len(applicabilityPrimaryKeyColumns))
			copy(conflict, applicabilityPrimaryKeyColumns)
		}
		cache.query = buildUpsertQueryPostgres(dialect, "\"experiments\".\"applicabilities\"", updateOnConflict, ret, update, conflict, insert)

		cache.valueMapping, err = queries.BindMapping(applicabilityType, applicabilityMapping, insert)
		if err != nil {
			return err
		}
		if len(ret) != 0 {
			cache.retMapping, err = queries.BindMapping(applicabilityType, applicabilityMapping, ret)
			if err != nil {
				return err
			}
		}
	}

	value := reflect.Indirect(reflect.ValueOf(o))
	vals := queries.ValuesFromMapping(value, cache.valueMapping)
	var returns []interface{}
	if len(cache.retMapping) != 0 {
		returns = queries.PtrsFromMapping(value, cache.retMapping)
	}

	if boil.IsDebug(ctx) {
		writer := boil.DebugWriterFrom(ctx)
		fmt.Fprintln(writer, cache.query)
		fmt.Fprintln(writer, vals)
	}
	if len(cache.retMapping) != 0 {
		err = exec.QueryRowContext(ctx, cache.query, vals...).Scan(returns...)
		if errors.Is(err, sql.ErrNoRows) {
			err = nil // Postgres doesn't return anything when there's no update
		}
	} else {
		_, err = exec.ExecContext(ctx, cache.query, vals...)
	}
	if err != nil {
		return errors.Wrap(err, "experiments: unable to upsert applicabilities")
	}

	if !cached {
		applicabilityUpsertCacheMut.Lock()
		applicabilityUpsertCache[key] = cache
		applicabilityUpsertCacheMut.Unlock()
	}

	return o.doAfterUpsertHooks(ctx, exec)
}

// Delete deletes a single Applicability record with an executor.
// Delete will match against the primary key column to find the record to delete.
func (o *Applicability) Delete(ctx context.Context, exec boil.ContextExecutor) (int64, error) {
	if o == nil {
		return 0, errors.New("experiments: no Applicability provided for delete")
	}

	if err := o.doBeforeDeleteHooks(ctx, exec); err != nil {
		return 0, err
	}

	args := queries.ValuesFromMapping(reflect.Indirect(reflect.ValueOf(o)), applicabilityPrimaryKeyMapping)
	sql := "DELETE FROM \"experiments\".\"applicabilities\" WHERE \"id\"=$1"

	if boil.IsDebug(ctx) {
		writer := boil.DebugWriterFrom(ctx)
		fmt.Fprintln(writer, sql)
		fmt.Fprintln(writer, args...)
	}
	result, err := exec.ExecContext(ctx, sql, args...)
	if err != nil {
		return 0, errors.Wrap(err, "experiments: unable to delete from applicabilities")
	}

	rowsAff, err := result.RowsAffected()
	if err != nil {
		return 0, errors.Wrap(err, "experiments: failed to get rows affected by delete for applicabilities")
	}

	if err := o.doAfterDeleteHooks(ctx, exec); err != nil {
		return 0, err
	}

	return rowsAff, nil
}

// DeleteAll deletes all matching rows.
func (q applicabilityQuery) DeleteAll(ctx context.Context, exec boil.ContextExecutor) (int64, error) {
	if q.Query == nil {
		return 0, errors.New("experiments: no applicabilityQuery provided for delete all")
	}

	queries.SetDelete(q.Query)

	result, err := q.Query.ExecContext(ctx, exec)
	if err != nil {
		return 0, errors.Wrap(err, "experiments: unable to delete all from applicabilities")
	}

	rowsAff, err := result.RowsAffected()
	if err != nil {
		return 0, errors.Wrap(err, "experiments: failed to get rows affected by deleteall for applicabilities")
	}

	return rowsAff, nil
}

// DeleteAll deletes all rows in the slice, using an executor.
func (o ApplicabilitySlice) DeleteAll(ctx context.Context, exec boil.ContextExecutor) (int64, error) {
	if len(o) == 0 {
		return 0, nil
	}

	if len(applicabilityBeforeDeleteHooks) != 0 {
		for _, obj := range o {
			if err := obj.doBeforeDeleteHooks(ctx, exec); err != nil {
				return 0, err
			}
		}
	}

	var args []interface{}
	for _, obj := range o {
		pkeyArgs := queries.ValuesFromMapping(reflect.Indirect(reflect.ValueOf(obj)), applicabilityPrimaryKeyMapping)
		args = append(args, pkeyArgs...)
	}

	sql := "DELETE FROM \"experiments\".\"applicabilities\" WHERE " +
		strmangle.WhereClauseRepeated(string(dialect.LQ), string(dialect.RQ), 1, applicabilityPrimaryKeyColumns, len(o))

	if boil.IsDebug(ctx) {
		writer := boil.DebugWriterFrom(ctx)
		fmt.Fprintln(writer, sql)
		fmt.Fprintln(writer, args)
	}
	result, err := exec.ExecContext(ctx, sql, args...)
	if err != nil {
		return 0, errors.Wrap(err, "experiments: unable to delete all from applicability slice")
	}

	rowsAff, err := result.RowsAffected()
	if err != nil {
		return 0, errors.Wrap(err, "experiments: failed to get rows affected by deleteall for applicabilities")
	}

	if len(applicabilityAfterDeleteHooks) != 0 {
		for _, obj := range o {
			if err := obj.doAfterDeleteHooks(ctx, exec); err != nil {
				return 0, err
			}
		}
	}

	return rowsAff, nil
}

// Reload refetches the object from the database
// using the primary keys with an executor.
func (o *Applicability) Reload(ctx context.Context, exec boil.ContextExecutor) error {
	ret, err := FindApplicability(ctx, exec, o.ID)
	if err != nil {
		return err
	}

	*o = *ret
	return nil
}

// ReloadAll refetches every row with matching primary key column values
// and overwrites the original object slice with the newly updated slice.
func (o *ApplicabilitySlice) ReloadAll(ctx context.Context, exec boil.ContextExecutor) error {
	if o == nil || len(*o) == 0 {
		return nil
	}

	slice := ApplicabilitySlice{}
	var args []interface{}
	for _, obj := range *o {
		pkeyArgs := queries.ValuesFromMapping(reflect.Indirect(reflect.ValueOf(obj)), applicabilityPrimaryKeyMapping)
		args = append(args, pkeyArgs...)
	}

	sql := "SELECT \"experiments\".\"applicabilities\".* FROM \"experiments\".\"applicabilities\" WHERE " +
		strmangle.WhereClauseRepeated(string(dialect.LQ), string(dialect.RQ), 1, applicabilityPrimaryKeyColumns, len(*o))

	q := queries.Raw(sql, args...)

	err := q.Bind(ctx, exec, &slice)
	if err != nil {
		return errors.Wrap(err, "experiments: unable to reload all in ApplicabilitySlice")
	}

	*o = slice

	return nil
}

// ApplicabilityExists checks if the Applicability row exists.
func ApplicabilityExists(ctx context.Context, exec boil.ContextExecutor, iD string) (bool, error) {
	var exists bool
	sql := "select exists(select 1 from \"experiments\".\"applicabilities\" where \"id\"=$1 limit 1)"

	if boil.IsDebug(ctx) {
		writer := boil.DebugWriterFrom(ctx)
		fmt.Fprintln(writer, sql)
		fmt.Fprintln(writer, iD)
	}
	row := exec.QueryRowContext(ctx, sql, iD)

	err := row.Scan(&exists)
	if err != nil {
		return false, errors.Wrap(err, "experiments: unable to check if applicabilities exists")
	}

	return exists, nil
}
