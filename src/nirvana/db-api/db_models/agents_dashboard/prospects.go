// Code generated by SQLBoiler (https://github.com/volatiletech/sqlboiler). DO NOT EDIT.
// This file is meant to be re-generated in place and/or deleted at any time.

package agents_dashboard

import (
	"context"
	"database/sql"
	"fmt"
	"reflect"
	"strconv"
	"strings"
	"sync"
	"time"

	"github.com/friendsofgo/errors"
	"github.com/volatiletech/null/v8"
	"github.com/volatiletech/sqlboiler/v4/boil"
	"github.com/volatiletech/sqlboiler/v4/queries"
	"github.com/volatiletech/sqlboiler/v4/queries/qm"
	"github.com/volatiletech/sqlboiler/v4/queries/qmhelper"
	"github.com/volatiletech/strmangle"
)

// Prospect is an object representing the database table.
type Prospect struct {
	ID                     string      `boil:"id" json:"id" toml:"id" yaml:"id"`
	CompanyName            string      `boil:"company_name" json:"company_name" toml:"company_name" yaml:"company_name"`
	DotNumber              string      `boil:"dot_number" json:"dot_number" toml:"dot_number" yaml:"dot_number"`
	State                  string      `boil:"state" json:"state" toml:"state" yaml:"state"`
	PowerUnits             int         `boil:"power_units" json:"power_units" toml:"power_units" yaml:"power_units"`
	Drivers                null.Int    `boil:"drivers" json:"drivers,omitempty" toml:"drivers" yaml:"drivers,omitempty"`
	AppetiteLiteScore      string      `boil:"appetite_lite_score" json:"appetite_lite_score" toml:"appetite_lite_score" yaml:"appetite_lite_score"`
	EffectiveDate          time.Time   `boil:"effective_date" json:"effective_date" toml:"effective_date" yaml:"effective_date"`
	SaferWatchAgency       null.String `boil:"safer_watch_agency" json:"safer_watch_agency,omitempty" toml:"safer_watch_agency" yaml:"safer_watch_agency,omitempty"`
	Phone                  string      `boil:"phone" json:"phone" toml:"phone" yaml:"phone"`
	Email                  string      `boil:"email" json:"email" toml:"email" yaml:"email"`
	ActiveOrPendingInsurer null.String `boil:"active_or_pending_insurer" json:"active_or_pending_insurer,omitempty" toml:"active_or_pending_insurer" yaml:"active_or_pending_insurer,omitempty"`
	CargoIntermodal        null.Bool   `boil:"cargo_intermodal" json:"cargo_intermodal,omitempty" toml:"cargo_intermodal" yaml:"cargo_intermodal,omitempty"`
	CurrentAssignmentID    null.String `boil:"current_assignment_id" json:"current_assignment_id,omitempty" toml:"current_assignment_id" yaml:"current_assignment_id,omitempty"`
	CreatedAt              null.Time   `boil:"created_at" json:"created_at,omitempty" toml:"created_at" yaml:"created_at,omitempty"`
	UpdatedAt              null.Time   `boil:"updated_at" json:"updated_at,omitempty" toml:"updated_at" yaml:"updated_at,omitempty"`
	ArchivedAt             null.Time   `boil:"archived_at" json:"archived_at,omitempty" toml:"archived_at" yaml:"archived_at,omitempty"`

	R *prospectR `boil:"" json:"" toml:"" yaml:""`
	L prospectL  `boil:"-" json:"-" toml:"-" yaml:"-"`
}

var ProspectColumns = struct {
	ID                     string
	CompanyName            string
	DotNumber              string
	State                  string
	PowerUnits             string
	Drivers                string
	AppetiteLiteScore      string
	EffectiveDate          string
	SaferWatchAgency       string
	Phone                  string
	Email                  string
	ActiveOrPendingInsurer string
	CargoIntermodal        string
	CurrentAssignmentID    string
	CreatedAt              string
	UpdatedAt              string
	ArchivedAt             string
}{
	ID:                     "id",
	CompanyName:            "company_name",
	DotNumber:              "dot_number",
	State:                  "state",
	PowerUnits:             "power_units",
	Drivers:                "drivers",
	AppetiteLiteScore:      "appetite_lite_score",
	EffectiveDate:          "effective_date",
	SaferWatchAgency:       "safer_watch_agency",
	Phone:                  "phone",
	Email:                  "email",
	ActiveOrPendingInsurer: "active_or_pending_insurer",
	CargoIntermodal:        "cargo_intermodal",
	CurrentAssignmentID:    "current_assignment_id",
	CreatedAt:              "created_at",
	UpdatedAt:              "updated_at",
	ArchivedAt:             "archived_at",
}

var ProspectTableColumns = struct {
	ID                     string
	CompanyName            string
	DotNumber              string
	State                  string
	PowerUnits             string
	Drivers                string
	AppetiteLiteScore      string
	EffectiveDate          string
	SaferWatchAgency       string
	Phone                  string
	Email                  string
	ActiveOrPendingInsurer string
	CargoIntermodal        string
	CurrentAssignmentID    string
	CreatedAt              string
	UpdatedAt              string
	ArchivedAt             string
}{
	ID:                     "prospects.id",
	CompanyName:            "prospects.company_name",
	DotNumber:              "prospects.dot_number",
	State:                  "prospects.state",
	PowerUnits:             "prospects.power_units",
	Drivers:                "prospects.drivers",
	AppetiteLiteScore:      "prospects.appetite_lite_score",
	EffectiveDate:          "prospects.effective_date",
	SaferWatchAgency:       "prospects.safer_watch_agency",
	Phone:                  "prospects.phone",
	Email:                  "prospects.email",
	ActiveOrPendingInsurer: "prospects.active_or_pending_insurer",
	CargoIntermodal:        "prospects.cargo_intermodal",
	CurrentAssignmentID:    "prospects.current_assignment_id",
	CreatedAt:              "prospects.created_at",
	UpdatedAt:              "prospects.updated_at",
	ArchivedAt:             "prospects.archived_at",
}

// Generated where

type whereHelperint struct{ field string }

func (w whereHelperint) EQ(x int) qm.QueryMod  { return qmhelper.Where(w.field, qmhelper.EQ, x) }
func (w whereHelperint) NEQ(x int) qm.QueryMod { return qmhelper.Where(w.field, qmhelper.NEQ, x) }
func (w whereHelperint) LT(x int) qm.QueryMod  { return qmhelper.Where(w.field, qmhelper.LT, x) }
func (w whereHelperint) LTE(x int) qm.QueryMod { return qmhelper.Where(w.field, qmhelper.LTE, x) }
func (w whereHelperint) GT(x int) qm.QueryMod  { return qmhelper.Where(w.field, qmhelper.GT, x) }
func (w whereHelperint) GTE(x int) qm.QueryMod { return qmhelper.Where(w.field, qmhelper.GTE, x) }
func (w whereHelperint) IN(slice []int) qm.QueryMod {
	values := make([]interface{}, 0, len(slice))
	for _, value := range slice {
		values = append(values, value)
	}
	return qm.WhereIn(fmt.Sprintf("%s IN ?", w.field), values...)
}
func (w whereHelperint) NIN(slice []int) qm.QueryMod {
	values := make([]interface{}, 0, len(slice))
	for _, value := range slice {
		values = append(values, value)
	}
	return qm.WhereNotIn(fmt.Sprintf("%s NOT IN ?", w.field), values...)
}

type whereHelpernull_Int struct{ field string }

func (w whereHelpernull_Int) EQ(x null.Int) qm.QueryMod {
	return qmhelper.WhereNullEQ(w.field, false, x)
}
func (w whereHelpernull_Int) NEQ(x null.Int) qm.QueryMod {
	return qmhelper.WhereNullEQ(w.field, true, x)
}
func (w whereHelpernull_Int) LT(x null.Int) qm.QueryMod {
	return qmhelper.Where(w.field, qmhelper.LT, x)
}
func (w whereHelpernull_Int) LTE(x null.Int) qm.QueryMod {
	return qmhelper.Where(w.field, qmhelper.LTE, x)
}
func (w whereHelpernull_Int) GT(x null.Int) qm.QueryMod {
	return qmhelper.Where(w.field, qmhelper.GT, x)
}
func (w whereHelpernull_Int) GTE(x null.Int) qm.QueryMod {
	return qmhelper.Where(w.field, qmhelper.GTE, x)
}

func (w whereHelpernull_Int) IsNull() qm.QueryMod    { return qmhelper.WhereIsNull(w.field) }
func (w whereHelpernull_Int) IsNotNull() qm.QueryMod { return qmhelper.WhereIsNotNull(w.field) }

type whereHelpertime_Time struct{ field string }

func (w whereHelpertime_Time) EQ(x time.Time) qm.QueryMod {
	return qmhelper.Where(w.field, qmhelper.EQ, x)
}
func (w whereHelpertime_Time) NEQ(x time.Time) qm.QueryMod {
	return qmhelper.Where(w.field, qmhelper.NEQ, x)
}
func (w whereHelpertime_Time) LT(x time.Time) qm.QueryMod {
	return qmhelper.Where(w.field, qmhelper.LT, x)
}
func (w whereHelpertime_Time) LTE(x time.Time) qm.QueryMod {
	return qmhelper.Where(w.field, qmhelper.LTE, x)
}
func (w whereHelpertime_Time) GT(x time.Time) qm.QueryMod {
	return qmhelper.Where(w.field, qmhelper.GT, x)
}
func (w whereHelpertime_Time) GTE(x time.Time) qm.QueryMod {
	return qmhelper.Where(w.field, qmhelper.GTE, x)
}

type whereHelpernull_String struct{ field string }

func (w whereHelpernull_String) EQ(x null.String) qm.QueryMod {
	return qmhelper.WhereNullEQ(w.field, false, x)
}
func (w whereHelpernull_String) NEQ(x null.String) qm.QueryMod {
	return qmhelper.WhereNullEQ(w.field, true, x)
}
func (w whereHelpernull_String) LT(x null.String) qm.QueryMod {
	return qmhelper.Where(w.field, qmhelper.LT, x)
}
func (w whereHelpernull_String) LTE(x null.String) qm.QueryMod {
	return qmhelper.Where(w.field, qmhelper.LTE, x)
}
func (w whereHelpernull_String) GT(x null.String) qm.QueryMod {
	return qmhelper.Where(w.field, qmhelper.GT, x)
}
func (w whereHelpernull_String) GTE(x null.String) qm.QueryMod {
	return qmhelper.Where(w.field, qmhelper.GTE, x)
}

func (w whereHelpernull_String) IsNull() qm.QueryMod    { return qmhelper.WhereIsNull(w.field) }
func (w whereHelpernull_String) IsNotNull() qm.QueryMod { return qmhelper.WhereIsNotNull(w.field) }

type whereHelpernull_Bool struct{ field string }

func (w whereHelpernull_Bool) EQ(x null.Bool) qm.QueryMod {
	return qmhelper.WhereNullEQ(w.field, false, x)
}
func (w whereHelpernull_Bool) NEQ(x null.Bool) qm.QueryMod {
	return qmhelper.WhereNullEQ(w.field, true, x)
}
func (w whereHelpernull_Bool) LT(x null.Bool) qm.QueryMod {
	return qmhelper.Where(w.field, qmhelper.LT, x)
}
func (w whereHelpernull_Bool) LTE(x null.Bool) qm.QueryMod {
	return qmhelper.Where(w.field, qmhelper.LTE, x)
}
func (w whereHelpernull_Bool) GT(x null.Bool) qm.QueryMod {
	return qmhelper.Where(w.field, qmhelper.GT, x)
}
func (w whereHelpernull_Bool) GTE(x null.Bool) qm.QueryMod {
	return qmhelper.Where(w.field, qmhelper.GTE, x)
}

func (w whereHelpernull_Bool) IsNull() qm.QueryMod    { return qmhelper.WhereIsNull(w.field) }
func (w whereHelpernull_Bool) IsNotNull() qm.QueryMod { return qmhelper.WhereIsNotNull(w.field) }

var ProspectWhere = struct {
	ID                     whereHelperstring
	CompanyName            whereHelperstring
	DotNumber              whereHelperstring
	State                  whereHelperstring
	PowerUnits             whereHelperint
	Drivers                whereHelpernull_Int
	AppetiteLiteScore      whereHelperstring
	EffectiveDate          whereHelpertime_Time
	SaferWatchAgency       whereHelpernull_String
	Phone                  whereHelperstring
	Email                  whereHelperstring
	ActiveOrPendingInsurer whereHelpernull_String
	CargoIntermodal        whereHelpernull_Bool
	CurrentAssignmentID    whereHelpernull_String
	CreatedAt              whereHelpernull_Time
	UpdatedAt              whereHelpernull_Time
	ArchivedAt             whereHelpernull_Time
}{
	ID:                     whereHelperstring{field: "\"agents_dashboard\".\"prospects\".\"id\""},
	CompanyName:            whereHelperstring{field: "\"agents_dashboard\".\"prospects\".\"company_name\""},
	DotNumber:              whereHelperstring{field: "\"agents_dashboard\".\"prospects\".\"dot_number\""},
	State:                  whereHelperstring{field: "\"agents_dashboard\".\"prospects\".\"state\""},
	PowerUnits:             whereHelperint{field: "\"agents_dashboard\".\"prospects\".\"power_units\""},
	Drivers:                whereHelpernull_Int{field: "\"agents_dashboard\".\"prospects\".\"drivers\""},
	AppetiteLiteScore:      whereHelperstring{field: "\"agents_dashboard\".\"prospects\".\"appetite_lite_score\""},
	EffectiveDate:          whereHelpertime_Time{field: "\"agents_dashboard\".\"prospects\".\"effective_date\""},
	SaferWatchAgency:       whereHelpernull_String{field: "\"agents_dashboard\".\"prospects\".\"safer_watch_agency\""},
	Phone:                  whereHelperstring{field: "\"agents_dashboard\".\"prospects\".\"phone\""},
	Email:                  whereHelperstring{field: "\"agents_dashboard\".\"prospects\".\"email\""},
	ActiveOrPendingInsurer: whereHelpernull_String{field: "\"agents_dashboard\".\"prospects\".\"active_or_pending_insurer\""},
	CargoIntermodal:        whereHelpernull_Bool{field: "\"agents_dashboard\".\"prospects\".\"cargo_intermodal\""},
	CurrentAssignmentID:    whereHelpernull_String{field: "\"agents_dashboard\".\"prospects\".\"current_assignment_id\""},
	CreatedAt:              whereHelpernull_Time{field: "\"agents_dashboard\".\"prospects\".\"created_at\""},
	UpdatedAt:              whereHelpernull_Time{field: "\"agents_dashboard\".\"prospects\".\"updated_at\""},
	ArchivedAt:             whereHelpernull_Time{field: "\"agents_dashboard\".\"prospects\".\"archived_at\""},
}

// ProspectRels is where relationship names are stored.
var ProspectRels = struct {
	ProspectAssignments string
}{
	ProspectAssignments: "ProspectAssignments",
}

// prospectR is where relationships are stored.
type prospectR struct {
	ProspectAssignments ProspectAssignmentSlice `boil:"ProspectAssignments" json:"ProspectAssignments" toml:"ProspectAssignments" yaml:"ProspectAssignments"`
}

// NewStruct creates a new relationship struct
func (*prospectR) NewStruct() *prospectR {
	return &prospectR{}
}

// prospectL is where Load methods for each relationship are stored.
type prospectL struct{}

var (
	prospectAllColumns            = []string{"id", "company_name", "dot_number", "state", "power_units", "drivers", "appetite_lite_score", "effective_date", "safer_watch_agency", "phone", "email", "active_or_pending_insurer", "cargo_intermodal", "current_assignment_id", "created_at", "updated_at", "archived_at"}
	prospectColumnsWithoutDefault = []string{"id", "company_name", "dot_number", "state", "power_units", "appetite_lite_score", "effective_date", "phone", "email"}
	prospectColumnsWithDefault    = []string{"drivers", "safer_watch_agency", "active_or_pending_insurer", "cargo_intermodal", "current_assignment_id", "created_at", "updated_at", "archived_at"}
	prospectPrimaryKeyColumns     = []string{"id"}
	prospectGeneratedColumns      = []string{}
)

type (
	// ProspectSlice is an alias for a slice of pointers to Prospect.
	// This should almost always be used instead of []Prospect.
	ProspectSlice []*Prospect
	// ProspectHook is the signature for custom Prospect hook methods
	ProspectHook func(context.Context, boil.ContextExecutor, *Prospect) error

	prospectQuery struct {
		*queries.Query
	}
)

// Cache for insert, update and upsert
var (
	prospectType                 = reflect.TypeOf(&Prospect{})
	prospectMapping              = queries.MakeStructMapping(prospectType)
	prospectPrimaryKeyMapping, _ = queries.BindMapping(prospectType, prospectMapping, prospectPrimaryKeyColumns)
	prospectInsertCacheMut       sync.RWMutex
	prospectInsertCache          = make(map[string]insertCache)
	prospectUpdateCacheMut       sync.RWMutex
	prospectUpdateCache          = make(map[string]updateCache)
	prospectUpsertCacheMut       sync.RWMutex
	prospectUpsertCache          = make(map[string]insertCache)
)

var (
	// Force time package dependency for automated UpdatedAt/CreatedAt.
	_ = time.Second
	// Force qmhelper dependency for where clause generation (which doesn't
	// always happen)
	_ = qmhelper.Where
)

var prospectAfterSelectHooks []ProspectHook

var prospectBeforeInsertHooks []ProspectHook
var prospectAfterInsertHooks []ProspectHook

var prospectBeforeUpdateHooks []ProspectHook
var prospectAfterUpdateHooks []ProspectHook

var prospectBeforeDeleteHooks []ProspectHook
var prospectAfterDeleteHooks []ProspectHook

var prospectBeforeUpsertHooks []ProspectHook
var prospectAfterUpsertHooks []ProspectHook

// doAfterSelectHooks executes all "after Select" hooks.
func (o *Prospect) doAfterSelectHooks(ctx context.Context, exec boil.ContextExecutor) (err error) {
	if boil.HooksAreSkipped(ctx) {
		return nil
	}

	for _, hook := range prospectAfterSelectHooks {
		if err := hook(ctx, exec, o); err != nil {
			return err
		}
	}

	return nil
}

// doBeforeInsertHooks executes all "before insert" hooks.
func (o *Prospect) doBeforeInsertHooks(ctx context.Context, exec boil.ContextExecutor) (err error) {
	if boil.HooksAreSkipped(ctx) {
		return nil
	}

	for _, hook := range prospectBeforeInsertHooks {
		if err := hook(ctx, exec, o); err != nil {
			return err
		}
	}

	return nil
}

// doAfterInsertHooks executes all "after Insert" hooks.
func (o *Prospect) doAfterInsertHooks(ctx context.Context, exec boil.ContextExecutor) (err error) {
	if boil.HooksAreSkipped(ctx) {
		return nil
	}

	for _, hook := range prospectAfterInsertHooks {
		if err := hook(ctx, exec, o); err != nil {
			return err
		}
	}

	return nil
}

// doBeforeUpdateHooks executes all "before Update" hooks.
func (o *Prospect) doBeforeUpdateHooks(ctx context.Context, exec boil.ContextExecutor) (err error) {
	if boil.HooksAreSkipped(ctx) {
		return nil
	}

	for _, hook := range prospectBeforeUpdateHooks {
		if err := hook(ctx, exec, o); err != nil {
			return err
		}
	}

	return nil
}

// doAfterUpdateHooks executes all "after Update" hooks.
func (o *Prospect) doAfterUpdateHooks(ctx context.Context, exec boil.ContextExecutor) (err error) {
	if boil.HooksAreSkipped(ctx) {
		return nil
	}

	for _, hook := range prospectAfterUpdateHooks {
		if err := hook(ctx, exec, o); err != nil {
			return err
		}
	}

	return nil
}

// doBeforeDeleteHooks executes all "before Delete" hooks.
func (o *Prospect) doBeforeDeleteHooks(ctx context.Context, exec boil.ContextExecutor) (err error) {
	if boil.HooksAreSkipped(ctx) {
		return nil
	}

	for _, hook := range prospectBeforeDeleteHooks {
		if err := hook(ctx, exec, o); err != nil {
			return err
		}
	}

	return nil
}

// doAfterDeleteHooks executes all "after Delete" hooks.
func (o *Prospect) doAfterDeleteHooks(ctx context.Context, exec boil.ContextExecutor) (err error) {
	if boil.HooksAreSkipped(ctx) {
		return nil
	}

	for _, hook := range prospectAfterDeleteHooks {
		if err := hook(ctx, exec, o); err != nil {
			return err
		}
	}

	return nil
}

// doBeforeUpsertHooks executes all "before Upsert" hooks.
func (o *Prospect) doBeforeUpsertHooks(ctx context.Context, exec boil.ContextExecutor) (err error) {
	if boil.HooksAreSkipped(ctx) {
		return nil
	}

	for _, hook := range prospectBeforeUpsertHooks {
		if err := hook(ctx, exec, o); err != nil {
			return err
		}
	}

	return nil
}

// doAfterUpsertHooks executes all "after Upsert" hooks.
func (o *Prospect) doAfterUpsertHooks(ctx context.Context, exec boil.ContextExecutor) (err error) {
	if boil.HooksAreSkipped(ctx) {
		return nil
	}

	for _, hook := range prospectAfterUpsertHooks {
		if err := hook(ctx, exec, o); err != nil {
			return err
		}
	}

	return nil
}

// AddProspectHook registers your hook function for all future operations.
func AddProspectHook(hookPoint boil.HookPoint, prospectHook ProspectHook) {
	switch hookPoint {
	case boil.AfterSelectHook:
		prospectAfterSelectHooks = append(prospectAfterSelectHooks, prospectHook)
	case boil.BeforeInsertHook:
		prospectBeforeInsertHooks = append(prospectBeforeInsertHooks, prospectHook)
	case boil.AfterInsertHook:
		prospectAfterInsertHooks = append(prospectAfterInsertHooks, prospectHook)
	case boil.BeforeUpdateHook:
		prospectBeforeUpdateHooks = append(prospectBeforeUpdateHooks, prospectHook)
	case boil.AfterUpdateHook:
		prospectAfterUpdateHooks = append(prospectAfterUpdateHooks, prospectHook)
	case boil.BeforeDeleteHook:
		prospectBeforeDeleteHooks = append(prospectBeforeDeleteHooks, prospectHook)
	case boil.AfterDeleteHook:
		prospectAfterDeleteHooks = append(prospectAfterDeleteHooks, prospectHook)
	case boil.BeforeUpsertHook:
		prospectBeforeUpsertHooks = append(prospectBeforeUpsertHooks, prospectHook)
	case boil.AfterUpsertHook:
		prospectAfterUpsertHooks = append(prospectAfterUpsertHooks, prospectHook)
	}
}

// One returns a single prospect record from the query.
func (q prospectQuery) One(ctx context.Context, exec boil.ContextExecutor) (*Prospect, error) {
	o := &Prospect{}

	queries.SetLimit(q.Query, 1)

	err := q.Bind(ctx, exec, o)
	if err != nil {
		if errors.Is(err, sql.ErrNoRows) {
			return nil, sql.ErrNoRows
		}
		return nil, errors.Wrap(err, "agents_dashboard: failed to execute a one query for prospects")
	}

	if err := o.doAfterSelectHooks(ctx, exec); err != nil {
		return o, err
	}

	return o, nil
}

// All returns all Prospect records from the query.
func (q prospectQuery) All(ctx context.Context, exec boil.ContextExecutor) (ProspectSlice, error) {
	var o []*Prospect

	err := q.Bind(ctx, exec, &o)
	if err != nil {
		return nil, errors.Wrap(err, "agents_dashboard: failed to assign all query results to Prospect slice")
	}

	if len(prospectAfterSelectHooks) != 0 {
		for _, obj := range o {
			if err := obj.doAfterSelectHooks(ctx, exec); err != nil {
				return o, err
			}
		}
	}

	return o, nil
}

// Count returns the count of all Prospect records in the query.
func (q prospectQuery) Count(ctx context.Context, exec boil.ContextExecutor) (int64, error) {
	var count int64

	queries.SetSelect(q.Query, nil)
	queries.SetCount(q.Query)

	err := q.Query.QueryRowContext(ctx, exec).Scan(&count)
	if err != nil {
		return 0, errors.Wrap(err, "agents_dashboard: failed to count prospects rows")
	}

	return count, nil
}

// Exists checks if the row exists in the table.
func (q prospectQuery) Exists(ctx context.Context, exec boil.ContextExecutor) (bool, error) {
	var count int64

	queries.SetSelect(q.Query, nil)
	queries.SetCount(q.Query)
	queries.SetLimit(q.Query, 1)

	err := q.Query.QueryRowContext(ctx, exec).Scan(&count)
	if err != nil {
		return false, errors.Wrap(err, "agents_dashboard: failed to check if prospects exists")
	}

	return count > 0, nil
}

// ProspectAssignments retrieves all the prospect_assignment's ProspectAssignments with an executor.
func (o *Prospect) ProspectAssignments(mods ...qm.QueryMod) prospectAssignmentQuery {
	var queryMods []qm.QueryMod
	if len(mods) != 0 {
		queryMods = append(queryMods, mods...)
	}

	queryMods = append(queryMods,
		qm.Where("\"agents_dashboard\".\"prospect_assignments\".\"prospect_id\"=?", o.ID),
	)

	return ProspectAssignments(queryMods...)
}

// LoadProspectAssignments allows an eager lookup of values, cached into the
// loaded structs of the objects. This is for a 1-M or N-M relationship.
func (prospectL) LoadProspectAssignments(ctx context.Context, e boil.ContextExecutor, singular bool, maybeProspect interface{}, mods queries.Applicator) error {
	var slice []*Prospect
	var object *Prospect

	if singular {
		object = maybeProspect.(*Prospect)
	} else {
		slice = *maybeProspect.(*[]*Prospect)
	}

	args := make([]interface{}, 0, 1)
	if singular {
		if object.R == nil {
			object.R = &prospectR{}
		}
		args = append(args, object.ID)
	} else {
	Outer:
		for _, obj := range slice {
			if obj.R == nil {
				obj.R = &prospectR{}
			}

			for _, a := range args {
				if a == obj.ID {
					continue Outer
				}
			}

			args = append(args, obj.ID)
		}
	}

	if len(args) == 0 {
		return nil
	}

	query := NewQuery(
		qm.From(`agents_dashboard.prospect_assignments`),
		qm.WhereIn(`agents_dashboard.prospect_assignments.prospect_id in ?`, args...),
	)
	if mods != nil {
		mods.Apply(query)
	}

	results, err := query.QueryContext(ctx, e)
	if err != nil {
		return errors.Wrap(err, "failed to eager load prospect_assignments")
	}

	var resultSlice []*ProspectAssignment
	if err = queries.Bind(results, &resultSlice); err != nil {
		return errors.Wrap(err, "failed to bind eager loaded slice prospect_assignments")
	}

	if err = results.Close(); err != nil {
		return errors.Wrap(err, "failed to close results in eager load on prospect_assignments")
	}
	if err = results.Err(); err != nil {
		return errors.Wrap(err, "error occurred during iteration of eager loaded relations for prospect_assignments")
	}

	if len(prospectAssignmentAfterSelectHooks) != 0 {
		for _, obj := range resultSlice {
			if err := obj.doAfterSelectHooks(ctx, e); err != nil {
				return err
			}
		}
	}
	if singular {
		object.R.ProspectAssignments = resultSlice
		for _, foreign := range resultSlice {
			if foreign.R == nil {
				foreign.R = &prospectAssignmentR{}
			}
			foreign.R.Prospect = object
		}
		return nil
	}

	for _, foreign := range resultSlice {
		for _, local := range slice {
			if local.ID == foreign.ProspectID {
				local.R.ProspectAssignments = append(local.R.ProspectAssignments, foreign)
				if foreign.R == nil {
					foreign.R = &prospectAssignmentR{}
				}
				foreign.R.Prospect = local
				break
			}
		}
	}

	return nil
}

// AddProspectAssignments adds the given related objects to the existing relationships
// of the prospect, optionally inserting them as new records.
// Appends related to o.R.ProspectAssignments.
// Sets related.R.Prospect appropriately.
func (o *Prospect) AddProspectAssignments(ctx context.Context, exec boil.ContextExecutor, insert bool, related ...*ProspectAssignment) error {
	var err error
	for _, rel := range related {
		if insert {
			rel.ProspectID = o.ID
			if err = rel.Insert(ctx, exec, boil.Infer()); err != nil {
				return errors.Wrap(err, "failed to insert into foreign table")
			}
		} else {
			updateQuery := fmt.Sprintf(
				"UPDATE \"agents_dashboard\".\"prospect_assignments\" SET %s WHERE %s",
				strmangle.SetParamNames("\"", "\"", 1, []string{"prospect_id"}),
				strmangle.WhereClause("\"", "\"", 2, prospectAssignmentPrimaryKeyColumns),
			)
			values := []interface{}{o.ID, rel.ID}

			if boil.IsDebug(ctx) {
				writer := boil.DebugWriterFrom(ctx)
				fmt.Fprintln(writer, updateQuery)
				fmt.Fprintln(writer, values)
			}
			if _, err = exec.ExecContext(ctx, updateQuery, values...); err != nil {
				return errors.Wrap(err, "failed to update foreign table")
			}

			rel.ProspectID = o.ID
		}
	}

	if o.R == nil {
		o.R = &prospectR{
			ProspectAssignments: related,
		}
	} else {
		o.R.ProspectAssignments = append(o.R.ProspectAssignments, related...)
	}

	for _, rel := range related {
		if rel.R == nil {
			rel.R = &prospectAssignmentR{
				Prospect: o,
			}
		} else {
			rel.R.Prospect = o
		}
	}
	return nil
}

// Prospects retrieves all the records using an executor.
func Prospects(mods ...qm.QueryMod) prospectQuery {
	mods = append(mods, qm.From("\"agents_dashboard\".\"prospects\""))
	q := NewQuery(mods...)
	if len(queries.GetSelect(q)) == 0 {
		queries.SetSelect(q, []string{"\"agents_dashboard\".\"prospects\".*"})
	}

	return prospectQuery{q}
}

// FindProspect retrieves a single record by ID with an executor.
// If selectCols is empty Find will return all columns.
func FindProspect(ctx context.Context, exec boil.ContextExecutor, iD string, selectCols ...string) (*Prospect, error) {
	prospectObj := &Prospect{}

	sel := "*"
	if len(selectCols) > 0 {
		sel = strings.Join(strmangle.IdentQuoteSlice(dialect.LQ, dialect.RQ, selectCols), ",")
	}
	query := fmt.Sprintf(
		"select %s from \"agents_dashboard\".\"prospects\" where \"id\"=$1", sel,
	)

	q := queries.Raw(query, iD)

	err := q.Bind(ctx, exec, prospectObj)
	if err != nil {
		if errors.Is(err, sql.ErrNoRows) {
			return nil, sql.ErrNoRows
		}
		return nil, errors.Wrap(err, "agents_dashboard: unable to select from prospects")
	}

	if err = prospectObj.doAfterSelectHooks(ctx, exec); err != nil {
		return prospectObj, err
	}

	return prospectObj, nil
}

// Insert a single record using an executor.
// See boil.Columns.InsertColumnSet documentation to understand column list inference for inserts.
func (o *Prospect) Insert(ctx context.Context, exec boil.ContextExecutor, columns boil.Columns) error {
	if o == nil {
		return errors.New("agents_dashboard: no prospects provided for insertion")
	}

	var err error
	if !boil.TimestampsAreSkipped(ctx) {
		currTime := time.Now().In(boil.GetLocation())

		if queries.MustTime(o.CreatedAt).IsZero() {
			queries.SetScanner(&o.CreatedAt, currTime)
		}
		if queries.MustTime(o.UpdatedAt).IsZero() {
			queries.SetScanner(&o.UpdatedAt, currTime)
		}
	}

	if err := o.doBeforeInsertHooks(ctx, exec); err != nil {
		return err
	}

	nzDefaults := queries.NonZeroDefaultSet(prospectColumnsWithDefault, o)

	key := makeCacheKey(columns, nzDefaults)
	prospectInsertCacheMut.RLock()
	cache, cached := prospectInsertCache[key]
	prospectInsertCacheMut.RUnlock()

	if !cached {
		wl, returnColumns := columns.InsertColumnSet(
			prospectAllColumns,
			prospectColumnsWithDefault,
			prospectColumnsWithoutDefault,
			nzDefaults,
		)

		cache.valueMapping, err = queries.BindMapping(prospectType, prospectMapping, wl)
		if err != nil {
			return err
		}
		cache.retMapping, err = queries.BindMapping(prospectType, prospectMapping, returnColumns)
		if err != nil {
			return err
		}
		if len(wl) != 0 {
			cache.query = fmt.Sprintf("INSERT INTO \"agents_dashboard\".\"prospects\" (\"%s\") %%sVALUES (%s)%%s", strings.Join(wl, "\",\""), strmangle.Placeholders(dialect.UseIndexPlaceholders, len(wl), 1, 1))
		} else {
			cache.query = "INSERT INTO \"agents_dashboard\".\"prospects\" %sDEFAULT VALUES%s"
		}

		var queryOutput, queryReturning string

		if len(cache.retMapping) != 0 {
			queryReturning = fmt.Sprintf(" RETURNING \"%s\"", strings.Join(returnColumns, "\",\""))
		}

		cache.query = fmt.Sprintf(cache.query, queryOutput, queryReturning)
	}

	value := reflect.Indirect(reflect.ValueOf(o))
	vals := queries.ValuesFromMapping(value, cache.valueMapping)

	if boil.IsDebug(ctx) {
		writer := boil.DebugWriterFrom(ctx)
		fmt.Fprintln(writer, cache.query)
		fmt.Fprintln(writer, vals)
	}

	if len(cache.retMapping) != 0 {
		err = exec.QueryRowContext(ctx, cache.query, vals...).Scan(queries.PtrsFromMapping(value, cache.retMapping)...)
	} else {
		_, err = exec.ExecContext(ctx, cache.query, vals...)
	}

	if err != nil {
		return errors.Wrap(err, "agents_dashboard: unable to insert into prospects")
	}

	if !cached {
		prospectInsertCacheMut.Lock()
		prospectInsertCache[key] = cache
		prospectInsertCacheMut.Unlock()
	}

	return o.doAfterInsertHooks(ctx, exec)
}

// Update uses an executor to update the Prospect.
// See boil.Columns.UpdateColumnSet documentation to understand column list inference for updates.
// Update does not automatically update the record in case of default values. Use .Reload() to refresh the records.
func (o *Prospect) Update(ctx context.Context, exec boil.ContextExecutor, columns boil.Columns) (int64, error) {
	if !boil.TimestampsAreSkipped(ctx) {
		currTime := time.Now().In(boil.GetLocation())

		queries.SetScanner(&o.UpdatedAt, currTime)
	}

	var err error
	if err = o.doBeforeUpdateHooks(ctx, exec); err != nil {
		return 0, err
	}
	key := makeCacheKey(columns, nil)
	prospectUpdateCacheMut.RLock()
	cache, cached := prospectUpdateCache[key]
	prospectUpdateCacheMut.RUnlock()

	if !cached {
		wl := columns.UpdateColumnSet(
			prospectAllColumns,
			prospectPrimaryKeyColumns,
		)

		if !columns.IsWhitelist() {
			wl = strmangle.SetComplement(wl, []string{"created_at"})
		}
		if len(wl) == 0 {
			return 0, errors.New("agents_dashboard: unable to update prospects, could not build whitelist")
		}

		cache.query = fmt.Sprintf("UPDATE \"agents_dashboard\".\"prospects\" SET %s WHERE %s",
			strmangle.SetParamNames("\"", "\"", 1, wl),
			strmangle.WhereClause("\"", "\"", len(wl)+1, prospectPrimaryKeyColumns),
		)
		cache.valueMapping, err = queries.BindMapping(prospectType, prospectMapping, append(wl, prospectPrimaryKeyColumns...))
		if err != nil {
			return 0, err
		}
	}

	values := queries.ValuesFromMapping(reflect.Indirect(reflect.ValueOf(o)), cache.valueMapping)

	if boil.IsDebug(ctx) {
		writer := boil.DebugWriterFrom(ctx)
		fmt.Fprintln(writer, cache.query)
		fmt.Fprintln(writer, values)
	}
	var result sql.Result
	result, err = exec.ExecContext(ctx, cache.query, values...)
	if err != nil {
		return 0, errors.Wrap(err, "agents_dashboard: unable to update prospects row")
	}

	rowsAff, err := result.RowsAffected()
	if err != nil {
		return 0, errors.Wrap(err, "agents_dashboard: failed to get rows affected by update for prospects")
	}

	if !cached {
		prospectUpdateCacheMut.Lock()
		prospectUpdateCache[key] = cache
		prospectUpdateCacheMut.Unlock()
	}

	return rowsAff, o.doAfterUpdateHooks(ctx, exec)
}

// UpdateAll updates all rows with the specified column values.
func (q prospectQuery) UpdateAll(ctx context.Context, exec boil.ContextExecutor, cols M) (int64, error) {
	queries.SetUpdate(q.Query, cols)

	result, err := q.Query.ExecContext(ctx, exec)
	if err != nil {
		return 0, errors.Wrap(err, "agents_dashboard: unable to update all for prospects")
	}

	rowsAff, err := result.RowsAffected()
	if err != nil {
		return 0, errors.Wrap(err, "agents_dashboard: unable to retrieve rows affected for prospects")
	}

	return rowsAff, nil
}

// UpdateAll updates all rows with the specified column values, using an executor.
func (o ProspectSlice) UpdateAll(ctx context.Context, exec boil.ContextExecutor, cols M) (int64, error) {
	ln := int64(len(o))
	if ln == 0 {
		return 0, nil
	}

	if len(cols) == 0 {
		return 0, errors.New("agents_dashboard: update all requires at least one column argument")
	}

	colNames := make([]string, len(cols))
	args := make([]interface{}, len(cols))

	i := 0
	for name, value := range cols {
		colNames[i] = name
		args[i] = value
		i++
	}

	// Append all of the primary key values for each column
	for _, obj := range o {
		pkeyArgs := queries.ValuesFromMapping(reflect.Indirect(reflect.ValueOf(obj)), prospectPrimaryKeyMapping)
		args = append(args, pkeyArgs...)
	}

	sql := fmt.Sprintf("UPDATE \"agents_dashboard\".\"prospects\" SET %s WHERE %s",
		strmangle.SetParamNames("\"", "\"", 1, colNames),
		strmangle.WhereClauseRepeated(string(dialect.LQ), string(dialect.RQ), len(colNames)+1, prospectPrimaryKeyColumns, len(o)))

	if boil.IsDebug(ctx) {
		writer := boil.DebugWriterFrom(ctx)
		fmt.Fprintln(writer, sql)
		fmt.Fprintln(writer, args...)
	}
	result, err := exec.ExecContext(ctx, sql, args...)
	if err != nil {
		return 0, errors.Wrap(err, "agents_dashboard: unable to update all in prospect slice")
	}

	rowsAff, err := result.RowsAffected()
	if err != nil {
		return 0, errors.Wrap(err, "agents_dashboard: unable to retrieve rows affected all in update all prospect")
	}
	return rowsAff, nil
}

// Upsert attempts an insert using an executor, and does an update or ignore on conflict.
// See boil.Columns documentation for how to properly use updateColumns and insertColumns.
func (o *Prospect) Upsert(ctx context.Context, exec boil.ContextExecutor, updateOnConflict bool, conflictColumns []string, updateColumns, insertColumns boil.Columns) error {
	if o == nil {
		return errors.New("agents_dashboard: no prospects provided for upsert")
	}
	if !boil.TimestampsAreSkipped(ctx) {
		currTime := time.Now().In(boil.GetLocation())

		if queries.MustTime(o.CreatedAt).IsZero() {
			queries.SetScanner(&o.CreatedAt, currTime)
		}
		queries.SetScanner(&o.UpdatedAt, currTime)
	}

	if err := o.doBeforeUpsertHooks(ctx, exec); err != nil {
		return err
	}

	nzDefaults := queries.NonZeroDefaultSet(prospectColumnsWithDefault, o)

	// Build cache key in-line uglily - mysql vs psql problems
	buf := strmangle.GetBuffer()
	if updateOnConflict {
		buf.WriteByte('t')
	} else {
		buf.WriteByte('f')
	}
	buf.WriteByte('.')
	for _, c := range conflictColumns {
		buf.WriteString(c)
	}
	buf.WriteByte('.')
	buf.WriteString(strconv.Itoa(updateColumns.Kind))
	for _, c := range updateColumns.Cols {
		buf.WriteString(c)
	}
	buf.WriteByte('.')
	buf.WriteString(strconv.Itoa(insertColumns.Kind))
	for _, c := range insertColumns.Cols {
		buf.WriteString(c)
	}
	buf.WriteByte('.')
	for _, c := range nzDefaults {
		buf.WriteString(c)
	}
	key := buf.String()
	strmangle.PutBuffer(buf)

	prospectUpsertCacheMut.RLock()
	cache, cached := prospectUpsertCache[key]
	prospectUpsertCacheMut.RUnlock()

	var err error

	if !cached {
		insert, ret := insertColumns.InsertColumnSet(
			prospectAllColumns,
			prospectColumnsWithDefault,
			prospectColumnsWithoutDefault,
			nzDefaults,
		)

		update := updateColumns.UpdateColumnSet(
			prospectAllColumns,
			prospectPrimaryKeyColumns,
		)

		if updateOnConflict && len(update) == 0 {
			return errors.New("agents_dashboard: unable to upsert prospects, could not build update column list")
		}

		conflict := conflictColumns
		if len(conflict) == 0 {
			conflict = make([]string, len(prospectPrimaryKeyColumns))
			copy(conflict, prospectPrimaryKeyColumns)
		}
		cache.query = buildUpsertQueryPostgres(dialect, "\"agents_dashboard\".\"prospects\"", updateOnConflict, ret, update, conflict, insert)

		cache.valueMapping, err = queries.BindMapping(prospectType, prospectMapping, insert)
		if err != nil {
			return err
		}
		if len(ret) != 0 {
			cache.retMapping, err = queries.BindMapping(prospectType, prospectMapping, ret)
			if err != nil {
				return err
			}
		}
	}

	value := reflect.Indirect(reflect.ValueOf(o))
	vals := queries.ValuesFromMapping(value, cache.valueMapping)
	var returns []interface{}
	if len(cache.retMapping) != 0 {
		returns = queries.PtrsFromMapping(value, cache.retMapping)
	}

	if boil.IsDebug(ctx) {
		writer := boil.DebugWriterFrom(ctx)
		fmt.Fprintln(writer, cache.query)
		fmt.Fprintln(writer, vals)
	}
	if len(cache.retMapping) != 0 {
		err = exec.QueryRowContext(ctx, cache.query, vals...).Scan(returns...)
		if errors.Is(err, sql.ErrNoRows) {
			err = nil // Postgres doesn't return anything when there's no update
		}
	} else {
		_, err = exec.ExecContext(ctx, cache.query, vals...)
	}
	if err != nil {
		return errors.Wrap(err, "agents_dashboard: unable to upsert prospects")
	}

	if !cached {
		prospectUpsertCacheMut.Lock()
		prospectUpsertCache[key] = cache
		prospectUpsertCacheMut.Unlock()
	}

	return o.doAfterUpsertHooks(ctx, exec)
}

// Delete deletes a single Prospect record with an executor.
// Delete will match against the primary key column to find the record to delete.
func (o *Prospect) Delete(ctx context.Context, exec boil.ContextExecutor) (int64, error) {
	if o == nil {
		return 0, errors.New("agents_dashboard: no Prospect provided for delete")
	}

	if err := o.doBeforeDeleteHooks(ctx, exec); err != nil {
		return 0, err
	}

	args := queries.ValuesFromMapping(reflect.Indirect(reflect.ValueOf(o)), prospectPrimaryKeyMapping)
	sql := "DELETE FROM \"agents_dashboard\".\"prospects\" WHERE \"id\"=$1"

	if boil.IsDebug(ctx) {
		writer := boil.DebugWriterFrom(ctx)
		fmt.Fprintln(writer, sql)
		fmt.Fprintln(writer, args...)
	}
	result, err := exec.ExecContext(ctx, sql, args...)
	if err != nil {
		return 0, errors.Wrap(err, "agents_dashboard: unable to delete from prospects")
	}

	rowsAff, err := result.RowsAffected()
	if err != nil {
		return 0, errors.Wrap(err, "agents_dashboard: failed to get rows affected by delete for prospects")
	}

	if err := o.doAfterDeleteHooks(ctx, exec); err != nil {
		return 0, err
	}

	return rowsAff, nil
}

// DeleteAll deletes all matching rows.
func (q prospectQuery) DeleteAll(ctx context.Context, exec boil.ContextExecutor) (int64, error) {
	if q.Query == nil {
		return 0, errors.New("agents_dashboard: no prospectQuery provided for delete all")
	}

	queries.SetDelete(q.Query)

	result, err := q.Query.ExecContext(ctx, exec)
	if err != nil {
		return 0, errors.Wrap(err, "agents_dashboard: unable to delete all from prospects")
	}

	rowsAff, err := result.RowsAffected()
	if err != nil {
		return 0, errors.Wrap(err, "agents_dashboard: failed to get rows affected by deleteall for prospects")
	}

	return rowsAff, nil
}

// DeleteAll deletes all rows in the slice, using an executor.
func (o ProspectSlice) DeleteAll(ctx context.Context, exec boil.ContextExecutor) (int64, error) {
	if len(o) == 0 {
		return 0, nil
	}

	if len(prospectBeforeDeleteHooks) != 0 {
		for _, obj := range o {
			if err := obj.doBeforeDeleteHooks(ctx, exec); err != nil {
				return 0, err
			}
		}
	}

	var args []interface{}
	for _, obj := range o {
		pkeyArgs := queries.ValuesFromMapping(reflect.Indirect(reflect.ValueOf(obj)), prospectPrimaryKeyMapping)
		args = append(args, pkeyArgs...)
	}

	sql := "DELETE FROM \"agents_dashboard\".\"prospects\" WHERE " +
		strmangle.WhereClauseRepeated(string(dialect.LQ), string(dialect.RQ), 1, prospectPrimaryKeyColumns, len(o))

	if boil.IsDebug(ctx) {
		writer := boil.DebugWriterFrom(ctx)
		fmt.Fprintln(writer, sql)
		fmt.Fprintln(writer, args)
	}
	result, err := exec.ExecContext(ctx, sql, args...)
	if err != nil {
		return 0, errors.Wrap(err, "agents_dashboard: unable to delete all from prospect slice")
	}

	rowsAff, err := result.RowsAffected()
	if err != nil {
		return 0, errors.Wrap(err, "agents_dashboard: failed to get rows affected by deleteall for prospects")
	}

	if len(prospectAfterDeleteHooks) != 0 {
		for _, obj := range o {
			if err := obj.doAfterDeleteHooks(ctx, exec); err != nil {
				return 0, err
			}
		}
	}

	return rowsAff, nil
}

// Reload refetches the object from the database
// using the primary keys with an executor.
func (o *Prospect) Reload(ctx context.Context, exec boil.ContextExecutor) error {
	ret, err := FindProspect(ctx, exec, o.ID)
	if err != nil {
		return err
	}

	*o = *ret
	return nil
}

// ReloadAll refetches every row with matching primary key column values
// and overwrites the original object slice with the newly updated slice.
func (o *ProspectSlice) ReloadAll(ctx context.Context, exec boil.ContextExecutor) error {
	if o == nil || len(*o) == 0 {
		return nil
	}

	slice := ProspectSlice{}
	var args []interface{}
	for _, obj := range *o {
		pkeyArgs := queries.ValuesFromMapping(reflect.Indirect(reflect.ValueOf(obj)), prospectPrimaryKeyMapping)
		args = append(args, pkeyArgs...)
	}

	sql := "SELECT \"agents_dashboard\".\"prospects\".* FROM \"agents_dashboard\".\"prospects\" WHERE " +
		strmangle.WhereClauseRepeated(string(dialect.LQ), string(dialect.RQ), 1, prospectPrimaryKeyColumns, len(*o))

	q := queries.Raw(sql, args...)

	err := q.Bind(ctx, exec, &slice)
	if err != nil {
		return errors.Wrap(err, "agents_dashboard: unable to reload all in ProspectSlice")
	}

	*o = slice

	return nil
}

// ProspectExists checks if the Prospect row exists.
func ProspectExists(ctx context.Context, exec boil.ContextExecutor, iD string) (bool, error) {
	var exists bool
	sql := "select exists(select 1 from \"agents_dashboard\".\"prospects\" where \"id\"=$1 limit 1)"

	if boil.IsDebug(ctx) {
		writer := boil.DebugWriterFrom(ctx)
		fmt.Fprintln(writer, sql)
		fmt.Fprintln(writer, iD)
	}
	row := exec.QueryRowContext(ctx, sql, iD)

	err := row.Scan(&exists)
	if err != nil {
		return false, errors.Wrap(err, "agents_dashboard: unable to check if prospects exists")
	}

	return exists, nil
}
