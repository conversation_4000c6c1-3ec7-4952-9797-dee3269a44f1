// Code generated by SQLBoiler (https://github.com/volatiletech/sqlboiler). DO NOT EDIT.
// This file is meant to be re-generated in place and/or deleted at any time.

package parsed_loss_runs

import (
	"context"
	"database/sql"
	"fmt"
	"reflect"
	"strconv"
	"strings"
	"sync"
	"time"

	"github.com/friendsofgo/errors"
	"github.com/volatiletech/null/v8"
	"github.com/volatiletech/sqlboiler/v4/boil"
	"github.com/volatiletech/sqlboiler/v4/queries"
	"github.com/volatiletech/sqlboiler/v4/queries/qm"
	"github.com/volatiletech/sqlboiler/v4/queries/qmhelper"
	"github.com/volatiletech/strmangle"
)

// DocumentStateTransition is an object representing the database table.
type DocumentStateTransition struct {
	ID             string      `boil:"id" json:"id" toml:"id" yaml:"id"`
	DocumentID     string      `boil:"document_id" json:"document_id" toml:"document_id" yaml:"document_id"`
	FromStatus     string      `boil:"from_status" json:"from_status" toml:"from_status" yaml:"from_status"`
	ToStatus       string      `boil:"to_status" json:"to_status" toml:"to_status" yaml:"to_status"`
	TransitionTime time.Time   `boil:"transition_time" json:"transition_time" toml:"transition_time" yaml:"transition_time"`
	TransitionedBy string      `boil:"transitioned_by" json:"transitioned_by" toml:"transitioned_by" yaml:"transitioned_by"`
	RequestID      null.String `boil:"request_id" json:"request_id,omitempty" toml:"request_id" yaml:"request_id,omitempty"`

	R *documentStateTransitionR `boil:"" json:"" toml:"" yaml:""`
	L documentStateTransitionL  `boil:"-" json:"-" toml:"-" yaml:"-"`
}

var DocumentStateTransitionColumns = struct {
	ID             string
	DocumentID     string
	FromStatus     string
	ToStatus       string
	TransitionTime string
	TransitionedBy string
	RequestID      string
}{
	ID:             "id",
	DocumentID:     "document_id",
	FromStatus:     "from_status",
	ToStatus:       "to_status",
	TransitionTime: "transition_time",
	TransitionedBy: "transitioned_by",
	RequestID:      "request_id",
}

var DocumentStateTransitionTableColumns = struct {
	ID             string
	DocumentID     string
	FromStatus     string
	ToStatus       string
	TransitionTime string
	TransitionedBy string
	RequestID      string
}{
	ID:             "document_state_transition.id",
	DocumentID:     "document_state_transition.document_id",
	FromStatus:     "document_state_transition.from_status",
	ToStatus:       "document_state_transition.to_status",
	TransitionTime: "document_state_transition.transition_time",
	TransitionedBy: "document_state_transition.transitioned_by",
	RequestID:      "document_state_transition.request_id",
}

// Generated where

var DocumentStateTransitionWhere = struct {
	ID             whereHelperstring
	DocumentID     whereHelperstring
	FromStatus     whereHelperstring
	ToStatus       whereHelperstring
	TransitionTime whereHelpertime_Time
	TransitionedBy whereHelperstring
	RequestID      whereHelpernull_String
}{
	ID:             whereHelperstring{field: "\"parsed_loss_runs\".\"document_state_transition\".\"id\""},
	DocumentID:     whereHelperstring{field: "\"parsed_loss_runs\".\"document_state_transition\".\"document_id\""},
	FromStatus:     whereHelperstring{field: "\"parsed_loss_runs\".\"document_state_transition\".\"from_status\""},
	ToStatus:       whereHelperstring{field: "\"parsed_loss_runs\".\"document_state_transition\".\"to_status\""},
	TransitionTime: whereHelpertime_Time{field: "\"parsed_loss_runs\".\"document_state_transition\".\"transition_time\""},
	TransitionedBy: whereHelperstring{field: "\"parsed_loss_runs\".\"document_state_transition\".\"transitioned_by\""},
	RequestID:      whereHelpernull_String{field: "\"parsed_loss_runs\".\"document_state_transition\".\"request_id\""},
}

// DocumentStateTransitionRels is where relationship names are stored.
var DocumentStateTransitionRels = struct {
	Document string
}{
	Document: "Document",
}

// documentStateTransitionR is where relationships are stored.
type documentStateTransitionR struct {
	Document *Document `boil:"Document" json:"Document" toml:"Document" yaml:"Document"`
}

// NewStruct creates a new relationship struct
func (*documentStateTransitionR) NewStruct() *documentStateTransitionR {
	return &documentStateTransitionR{}
}

// documentStateTransitionL is where Load methods for each relationship are stored.
type documentStateTransitionL struct{}

var (
	documentStateTransitionAllColumns            = []string{"id", "document_id", "from_status", "to_status", "transition_time", "transitioned_by", "request_id"}
	documentStateTransitionColumnsWithoutDefault = []string{"id", "document_id", "from_status", "to_status", "transition_time", "transitioned_by"}
	documentStateTransitionColumnsWithDefault    = []string{"request_id"}
	documentStateTransitionPrimaryKeyColumns     = []string{"id"}
	documentStateTransitionGeneratedColumns      = []string{}
)

type (
	// DocumentStateTransitionSlice is an alias for a slice of pointers to DocumentStateTransition.
	// This should almost always be used instead of []DocumentStateTransition.
	DocumentStateTransitionSlice []*DocumentStateTransition
	// DocumentStateTransitionHook is the signature for custom DocumentStateTransition hook methods
	DocumentStateTransitionHook func(context.Context, boil.ContextExecutor, *DocumentStateTransition) error

	documentStateTransitionQuery struct {
		*queries.Query
	}
)

// Cache for insert, update and upsert
var (
	documentStateTransitionType                 = reflect.TypeOf(&DocumentStateTransition{})
	documentStateTransitionMapping              = queries.MakeStructMapping(documentStateTransitionType)
	documentStateTransitionPrimaryKeyMapping, _ = queries.BindMapping(documentStateTransitionType, documentStateTransitionMapping, documentStateTransitionPrimaryKeyColumns)
	documentStateTransitionInsertCacheMut       sync.RWMutex
	documentStateTransitionInsertCache          = make(map[string]insertCache)
	documentStateTransitionUpdateCacheMut       sync.RWMutex
	documentStateTransitionUpdateCache          = make(map[string]updateCache)
	documentStateTransitionUpsertCacheMut       sync.RWMutex
	documentStateTransitionUpsertCache          = make(map[string]insertCache)
)

var (
	// Force time package dependency for automated UpdatedAt/CreatedAt.
	_ = time.Second
	// Force qmhelper dependency for where clause generation (which doesn't
	// always happen)
	_ = qmhelper.Where
)

var documentStateTransitionAfterSelectHooks []DocumentStateTransitionHook

var documentStateTransitionBeforeInsertHooks []DocumentStateTransitionHook
var documentStateTransitionAfterInsertHooks []DocumentStateTransitionHook

var documentStateTransitionBeforeUpdateHooks []DocumentStateTransitionHook
var documentStateTransitionAfterUpdateHooks []DocumentStateTransitionHook

var documentStateTransitionBeforeDeleteHooks []DocumentStateTransitionHook
var documentStateTransitionAfterDeleteHooks []DocumentStateTransitionHook

var documentStateTransitionBeforeUpsertHooks []DocumentStateTransitionHook
var documentStateTransitionAfterUpsertHooks []DocumentStateTransitionHook

// doAfterSelectHooks executes all "after Select" hooks.
func (o *DocumentStateTransition) doAfterSelectHooks(ctx context.Context, exec boil.ContextExecutor) (err error) {
	if boil.HooksAreSkipped(ctx) {
		return nil
	}

	for _, hook := range documentStateTransitionAfterSelectHooks {
		if err := hook(ctx, exec, o); err != nil {
			return err
		}
	}

	return nil
}

// doBeforeInsertHooks executes all "before insert" hooks.
func (o *DocumentStateTransition) doBeforeInsertHooks(ctx context.Context, exec boil.ContextExecutor) (err error) {
	if boil.HooksAreSkipped(ctx) {
		return nil
	}

	for _, hook := range documentStateTransitionBeforeInsertHooks {
		if err := hook(ctx, exec, o); err != nil {
			return err
		}
	}

	return nil
}

// doAfterInsertHooks executes all "after Insert" hooks.
func (o *DocumentStateTransition) doAfterInsertHooks(ctx context.Context, exec boil.ContextExecutor) (err error) {
	if boil.HooksAreSkipped(ctx) {
		return nil
	}

	for _, hook := range documentStateTransitionAfterInsertHooks {
		if err := hook(ctx, exec, o); err != nil {
			return err
		}
	}

	return nil
}

// doBeforeUpdateHooks executes all "before Update" hooks.
func (o *DocumentStateTransition) doBeforeUpdateHooks(ctx context.Context, exec boil.ContextExecutor) (err error) {
	if boil.HooksAreSkipped(ctx) {
		return nil
	}

	for _, hook := range documentStateTransitionBeforeUpdateHooks {
		if err := hook(ctx, exec, o); err != nil {
			return err
		}
	}

	return nil
}

// doAfterUpdateHooks executes all "after Update" hooks.
func (o *DocumentStateTransition) doAfterUpdateHooks(ctx context.Context, exec boil.ContextExecutor) (err error) {
	if boil.HooksAreSkipped(ctx) {
		return nil
	}

	for _, hook := range documentStateTransitionAfterUpdateHooks {
		if err := hook(ctx, exec, o); err != nil {
			return err
		}
	}

	return nil
}

// doBeforeDeleteHooks executes all "before Delete" hooks.
func (o *DocumentStateTransition) doBeforeDeleteHooks(ctx context.Context, exec boil.ContextExecutor) (err error) {
	if boil.HooksAreSkipped(ctx) {
		return nil
	}

	for _, hook := range documentStateTransitionBeforeDeleteHooks {
		if err := hook(ctx, exec, o); err != nil {
			return err
		}
	}

	return nil
}

// doAfterDeleteHooks executes all "after Delete" hooks.
func (o *DocumentStateTransition) doAfterDeleteHooks(ctx context.Context, exec boil.ContextExecutor) (err error) {
	if boil.HooksAreSkipped(ctx) {
		return nil
	}

	for _, hook := range documentStateTransitionAfterDeleteHooks {
		if err := hook(ctx, exec, o); err != nil {
			return err
		}
	}

	return nil
}

// doBeforeUpsertHooks executes all "before Upsert" hooks.
func (o *DocumentStateTransition) doBeforeUpsertHooks(ctx context.Context, exec boil.ContextExecutor) (err error) {
	if boil.HooksAreSkipped(ctx) {
		return nil
	}

	for _, hook := range documentStateTransitionBeforeUpsertHooks {
		if err := hook(ctx, exec, o); err != nil {
			return err
		}
	}

	return nil
}

// doAfterUpsertHooks executes all "after Upsert" hooks.
func (o *DocumentStateTransition) doAfterUpsertHooks(ctx context.Context, exec boil.ContextExecutor) (err error) {
	if boil.HooksAreSkipped(ctx) {
		return nil
	}

	for _, hook := range documentStateTransitionAfterUpsertHooks {
		if err := hook(ctx, exec, o); err != nil {
			return err
		}
	}

	return nil
}

// AddDocumentStateTransitionHook registers your hook function for all future operations.
func AddDocumentStateTransitionHook(hookPoint boil.HookPoint, documentStateTransitionHook DocumentStateTransitionHook) {
	switch hookPoint {
	case boil.AfterSelectHook:
		documentStateTransitionAfterSelectHooks = append(documentStateTransitionAfterSelectHooks, documentStateTransitionHook)
	case boil.BeforeInsertHook:
		documentStateTransitionBeforeInsertHooks = append(documentStateTransitionBeforeInsertHooks, documentStateTransitionHook)
	case boil.AfterInsertHook:
		documentStateTransitionAfterInsertHooks = append(documentStateTransitionAfterInsertHooks, documentStateTransitionHook)
	case boil.BeforeUpdateHook:
		documentStateTransitionBeforeUpdateHooks = append(documentStateTransitionBeforeUpdateHooks, documentStateTransitionHook)
	case boil.AfterUpdateHook:
		documentStateTransitionAfterUpdateHooks = append(documentStateTransitionAfterUpdateHooks, documentStateTransitionHook)
	case boil.BeforeDeleteHook:
		documentStateTransitionBeforeDeleteHooks = append(documentStateTransitionBeforeDeleteHooks, documentStateTransitionHook)
	case boil.AfterDeleteHook:
		documentStateTransitionAfterDeleteHooks = append(documentStateTransitionAfterDeleteHooks, documentStateTransitionHook)
	case boil.BeforeUpsertHook:
		documentStateTransitionBeforeUpsertHooks = append(documentStateTransitionBeforeUpsertHooks, documentStateTransitionHook)
	case boil.AfterUpsertHook:
		documentStateTransitionAfterUpsertHooks = append(documentStateTransitionAfterUpsertHooks, documentStateTransitionHook)
	}
}

// One returns a single documentStateTransition record from the query.
func (q documentStateTransitionQuery) One(ctx context.Context, exec boil.ContextExecutor) (*DocumentStateTransition, error) {
	o := &DocumentStateTransition{}

	queries.SetLimit(q.Query, 1)

	err := q.Bind(ctx, exec, o)
	if err != nil {
		if errors.Is(err, sql.ErrNoRows) {
			return nil, sql.ErrNoRows
		}
		return nil, errors.Wrap(err, "parsed_loss_runs: failed to execute a one query for document_state_transition")
	}

	if err := o.doAfterSelectHooks(ctx, exec); err != nil {
		return o, err
	}

	return o, nil
}

// All returns all DocumentStateTransition records from the query.
func (q documentStateTransitionQuery) All(ctx context.Context, exec boil.ContextExecutor) (DocumentStateTransitionSlice, error) {
	var o []*DocumentStateTransition

	err := q.Bind(ctx, exec, &o)
	if err != nil {
		return nil, errors.Wrap(err, "parsed_loss_runs: failed to assign all query results to DocumentStateTransition slice")
	}

	if len(documentStateTransitionAfterSelectHooks) != 0 {
		for _, obj := range o {
			if err := obj.doAfterSelectHooks(ctx, exec); err != nil {
				return o, err
			}
		}
	}

	return o, nil
}

// Count returns the count of all DocumentStateTransition records in the query.
func (q documentStateTransitionQuery) Count(ctx context.Context, exec boil.ContextExecutor) (int64, error) {
	var count int64

	queries.SetSelect(q.Query, nil)
	queries.SetCount(q.Query)

	err := q.Query.QueryRowContext(ctx, exec).Scan(&count)
	if err != nil {
		return 0, errors.Wrap(err, "parsed_loss_runs: failed to count document_state_transition rows")
	}

	return count, nil
}

// Exists checks if the row exists in the table.
func (q documentStateTransitionQuery) Exists(ctx context.Context, exec boil.ContextExecutor) (bool, error) {
	var count int64

	queries.SetSelect(q.Query, nil)
	queries.SetCount(q.Query)
	queries.SetLimit(q.Query, 1)

	err := q.Query.QueryRowContext(ctx, exec).Scan(&count)
	if err != nil {
		return false, errors.Wrap(err, "parsed_loss_runs: failed to check if document_state_transition exists")
	}

	return count > 0, nil
}

// Document pointed to by the foreign key.
func (o *DocumentStateTransition) Document(mods ...qm.QueryMod) documentQuery {
	queryMods := []qm.QueryMod{
		qm.Where("\"id\" = ?", o.DocumentID),
	}

	queryMods = append(queryMods, mods...)

	return Documents(queryMods...)
}

// LoadDocument allows an eager lookup of values, cached into the
// loaded structs of the objects. This is for an N-1 relationship.
func (documentStateTransitionL) LoadDocument(ctx context.Context, e boil.ContextExecutor, singular bool, maybeDocumentStateTransition interface{}, mods queries.Applicator) error {
	var slice []*DocumentStateTransition
	var object *DocumentStateTransition

	if singular {
		object = maybeDocumentStateTransition.(*DocumentStateTransition)
	} else {
		slice = *maybeDocumentStateTransition.(*[]*DocumentStateTransition)
	}

	args := make([]interface{}, 0, 1)
	if singular {
		if object.R == nil {
			object.R = &documentStateTransitionR{}
		}
		args = append(args, object.DocumentID)

	} else {
	Outer:
		for _, obj := range slice {
			if obj.R == nil {
				obj.R = &documentStateTransitionR{}
			}

			for _, a := range args {
				if a == obj.DocumentID {
					continue Outer
				}
			}

			args = append(args, obj.DocumentID)

		}
	}

	if len(args) == 0 {
		return nil
	}

	query := NewQuery(
		qm.From(`parsed_loss_runs.document`),
		qm.WhereIn(`parsed_loss_runs.document.id in ?`, args...),
	)
	if mods != nil {
		mods.Apply(query)
	}

	results, err := query.QueryContext(ctx, e)
	if err != nil {
		return errors.Wrap(err, "failed to eager load Document")
	}

	var resultSlice []*Document
	if err = queries.Bind(results, &resultSlice); err != nil {
		return errors.Wrap(err, "failed to bind eager loaded slice Document")
	}

	if err = results.Close(); err != nil {
		return errors.Wrap(err, "failed to close results of eager load for document")
	}
	if err = results.Err(); err != nil {
		return errors.Wrap(err, "error occurred during iteration of eager loaded relations for document")
	}

	if len(documentStateTransitionAfterSelectHooks) != 0 {
		for _, obj := range resultSlice {
			if err := obj.doAfterSelectHooks(ctx, e); err != nil {
				return err
			}
		}
	}

	if len(resultSlice) == 0 {
		return nil
	}

	if singular {
		foreign := resultSlice[0]
		object.R.Document = foreign
		if foreign.R == nil {
			foreign.R = &documentR{}
		}
		foreign.R.DocumentStateTransitions = append(foreign.R.DocumentStateTransitions, object)
		return nil
	}

	for _, local := range slice {
		for _, foreign := range resultSlice {
			if local.DocumentID == foreign.ID {
				local.R.Document = foreign
				if foreign.R == nil {
					foreign.R = &documentR{}
				}
				foreign.R.DocumentStateTransitions = append(foreign.R.DocumentStateTransitions, local)
				break
			}
		}
	}

	return nil
}

// SetDocument of the documentStateTransition to the related item.
// Sets o.R.Document to related.
// Adds o to related.R.DocumentStateTransitions.
func (o *DocumentStateTransition) SetDocument(ctx context.Context, exec boil.ContextExecutor, insert bool, related *Document) error {
	var err error
	if insert {
		if err = related.Insert(ctx, exec, boil.Infer()); err != nil {
			return errors.Wrap(err, "failed to insert into foreign table")
		}
	}

	updateQuery := fmt.Sprintf(
		"UPDATE \"parsed_loss_runs\".\"document_state_transition\" SET %s WHERE %s",
		strmangle.SetParamNames("\"", "\"", 1, []string{"document_id"}),
		strmangle.WhereClause("\"", "\"", 2, documentStateTransitionPrimaryKeyColumns),
	)
	values := []interface{}{related.ID, o.ID}

	if boil.IsDebug(ctx) {
		writer := boil.DebugWriterFrom(ctx)
		fmt.Fprintln(writer, updateQuery)
		fmt.Fprintln(writer, values)
	}
	if _, err = exec.ExecContext(ctx, updateQuery, values...); err != nil {
		return errors.Wrap(err, "failed to update local table")
	}

	o.DocumentID = related.ID
	if o.R == nil {
		o.R = &documentStateTransitionR{
			Document: related,
		}
	} else {
		o.R.Document = related
	}

	if related.R == nil {
		related.R = &documentR{
			DocumentStateTransitions: DocumentStateTransitionSlice{o},
		}
	} else {
		related.R.DocumentStateTransitions = append(related.R.DocumentStateTransitions, o)
	}

	return nil
}

// DocumentStateTransitions retrieves all the records using an executor.
func DocumentStateTransitions(mods ...qm.QueryMod) documentStateTransitionQuery {
	mods = append(mods, qm.From("\"parsed_loss_runs\".\"document_state_transition\""))
	q := NewQuery(mods...)
	if len(queries.GetSelect(q)) == 0 {
		queries.SetSelect(q, []string{"\"parsed_loss_runs\".\"document_state_transition\".*"})
	}

	return documentStateTransitionQuery{q}
}

// FindDocumentStateTransition retrieves a single record by ID with an executor.
// If selectCols is empty Find will return all columns.
func FindDocumentStateTransition(ctx context.Context, exec boil.ContextExecutor, iD string, selectCols ...string) (*DocumentStateTransition, error) {
	documentStateTransitionObj := &DocumentStateTransition{}

	sel := "*"
	if len(selectCols) > 0 {
		sel = strings.Join(strmangle.IdentQuoteSlice(dialect.LQ, dialect.RQ, selectCols), ",")
	}
	query := fmt.Sprintf(
		"select %s from \"parsed_loss_runs\".\"document_state_transition\" where \"id\"=$1", sel,
	)

	q := queries.Raw(query, iD)

	err := q.Bind(ctx, exec, documentStateTransitionObj)
	if err != nil {
		if errors.Is(err, sql.ErrNoRows) {
			return nil, sql.ErrNoRows
		}
		return nil, errors.Wrap(err, "parsed_loss_runs: unable to select from document_state_transition")
	}

	if err = documentStateTransitionObj.doAfterSelectHooks(ctx, exec); err != nil {
		return documentStateTransitionObj, err
	}

	return documentStateTransitionObj, nil
}

// Insert a single record using an executor.
// See boil.Columns.InsertColumnSet documentation to understand column list inference for inserts.
func (o *DocumentStateTransition) Insert(ctx context.Context, exec boil.ContextExecutor, columns boil.Columns) error {
	if o == nil {
		return errors.New("parsed_loss_runs: no document_state_transition provided for insertion")
	}

	var err error

	if err := o.doBeforeInsertHooks(ctx, exec); err != nil {
		return err
	}

	nzDefaults := queries.NonZeroDefaultSet(documentStateTransitionColumnsWithDefault, o)

	key := makeCacheKey(columns, nzDefaults)
	documentStateTransitionInsertCacheMut.RLock()
	cache, cached := documentStateTransitionInsertCache[key]
	documentStateTransitionInsertCacheMut.RUnlock()

	if !cached {
		wl, returnColumns := columns.InsertColumnSet(
			documentStateTransitionAllColumns,
			documentStateTransitionColumnsWithDefault,
			documentStateTransitionColumnsWithoutDefault,
			nzDefaults,
		)

		cache.valueMapping, err = queries.BindMapping(documentStateTransitionType, documentStateTransitionMapping, wl)
		if err != nil {
			return err
		}
		cache.retMapping, err = queries.BindMapping(documentStateTransitionType, documentStateTransitionMapping, returnColumns)
		if err != nil {
			return err
		}
		if len(wl) != 0 {
			cache.query = fmt.Sprintf("INSERT INTO \"parsed_loss_runs\".\"document_state_transition\" (\"%s\") %%sVALUES (%s)%%s", strings.Join(wl, "\",\""), strmangle.Placeholders(dialect.UseIndexPlaceholders, len(wl), 1, 1))
		} else {
			cache.query = "INSERT INTO \"parsed_loss_runs\".\"document_state_transition\" %sDEFAULT VALUES%s"
		}

		var queryOutput, queryReturning string

		if len(cache.retMapping) != 0 {
			queryReturning = fmt.Sprintf(" RETURNING \"%s\"", strings.Join(returnColumns, "\",\""))
		}

		cache.query = fmt.Sprintf(cache.query, queryOutput, queryReturning)
	}

	value := reflect.Indirect(reflect.ValueOf(o))
	vals := queries.ValuesFromMapping(value, cache.valueMapping)

	if boil.IsDebug(ctx) {
		writer := boil.DebugWriterFrom(ctx)
		fmt.Fprintln(writer, cache.query)
		fmt.Fprintln(writer, vals)
	}

	if len(cache.retMapping) != 0 {
		err = exec.QueryRowContext(ctx, cache.query, vals...).Scan(queries.PtrsFromMapping(value, cache.retMapping)...)
	} else {
		_, err = exec.ExecContext(ctx, cache.query, vals...)
	}

	if err != nil {
		return errors.Wrap(err, "parsed_loss_runs: unable to insert into document_state_transition")
	}

	if !cached {
		documentStateTransitionInsertCacheMut.Lock()
		documentStateTransitionInsertCache[key] = cache
		documentStateTransitionInsertCacheMut.Unlock()
	}

	return o.doAfterInsertHooks(ctx, exec)
}

// Update uses an executor to update the DocumentStateTransition.
// See boil.Columns.UpdateColumnSet documentation to understand column list inference for updates.
// Update does not automatically update the record in case of default values. Use .Reload() to refresh the records.
func (o *DocumentStateTransition) Update(ctx context.Context, exec boil.ContextExecutor, columns boil.Columns) (int64, error) {
	var err error
	if err = o.doBeforeUpdateHooks(ctx, exec); err != nil {
		return 0, err
	}
	key := makeCacheKey(columns, nil)
	documentStateTransitionUpdateCacheMut.RLock()
	cache, cached := documentStateTransitionUpdateCache[key]
	documentStateTransitionUpdateCacheMut.RUnlock()

	if !cached {
		wl := columns.UpdateColumnSet(
			documentStateTransitionAllColumns,
			documentStateTransitionPrimaryKeyColumns,
		)

		if !columns.IsWhitelist() {
			wl = strmangle.SetComplement(wl, []string{"created_at"})
		}
		if len(wl) == 0 {
			return 0, errors.New("parsed_loss_runs: unable to update document_state_transition, could not build whitelist")
		}

		cache.query = fmt.Sprintf("UPDATE \"parsed_loss_runs\".\"document_state_transition\" SET %s WHERE %s",
			strmangle.SetParamNames("\"", "\"", 1, wl),
			strmangle.WhereClause("\"", "\"", len(wl)+1, documentStateTransitionPrimaryKeyColumns),
		)
		cache.valueMapping, err = queries.BindMapping(documentStateTransitionType, documentStateTransitionMapping, append(wl, documentStateTransitionPrimaryKeyColumns...))
		if err != nil {
			return 0, err
		}
	}

	values := queries.ValuesFromMapping(reflect.Indirect(reflect.ValueOf(o)), cache.valueMapping)

	if boil.IsDebug(ctx) {
		writer := boil.DebugWriterFrom(ctx)
		fmt.Fprintln(writer, cache.query)
		fmt.Fprintln(writer, values)
	}
	var result sql.Result
	result, err = exec.ExecContext(ctx, cache.query, values...)
	if err != nil {
		return 0, errors.Wrap(err, "parsed_loss_runs: unable to update document_state_transition row")
	}

	rowsAff, err := result.RowsAffected()
	if err != nil {
		return 0, errors.Wrap(err, "parsed_loss_runs: failed to get rows affected by update for document_state_transition")
	}

	if !cached {
		documentStateTransitionUpdateCacheMut.Lock()
		documentStateTransitionUpdateCache[key] = cache
		documentStateTransitionUpdateCacheMut.Unlock()
	}

	return rowsAff, o.doAfterUpdateHooks(ctx, exec)
}

// UpdateAll updates all rows with the specified column values.
func (q documentStateTransitionQuery) UpdateAll(ctx context.Context, exec boil.ContextExecutor, cols M) (int64, error) {
	queries.SetUpdate(q.Query, cols)

	result, err := q.Query.ExecContext(ctx, exec)
	if err != nil {
		return 0, errors.Wrap(err, "parsed_loss_runs: unable to update all for document_state_transition")
	}

	rowsAff, err := result.RowsAffected()
	if err != nil {
		return 0, errors.Wrap(err, "parsed_loss_runs: unable to retrieve rows affected for document_state_transition")
	}

	return rowsAff, nil
}

// UpdateAll updates all rows with the specified column values, using an executor.
func (o DocumentStateTransitionSlice) UpdateAll(ctx context.Context, exec boil.ContextExecutor, cols M) (int64, error) {
	ln := int64(len(o))
	if ln == 0 {
		return 0, nil
	}

	if len(cols) == 0 {
		return 0, errors.New("parsed_loss_runs: update all requires at least one column argument")
	}

	colNames := make([]string, len(cols))
	args := make([]interface{}, len(cols))

	i := 0
	for name, value := range cols {
		colNames[i] = name
		args[i] = value
		i++
	}

	// Append all of the primary key values for each column
	for _, obj := range o {
		pkeyArgs := queries.ValuesFromMapping(reflect.Indirect(reflect.ValueOf(obj)), documentStateTransitionPrimaryKeyMapping)
		args = append(args, pkeyArgs...)
	}

	sql := fmt.Sprintf("UPDATE \"parsed_loss_runs\".\"document_state_transition\" SET %s WHERE %s",
		strmangle.SetParamNames("\"", "\"", 1, colNames),
		strmangle.WhereClauseRepeated(string(dialect.LQ), string(dialect.RQ), len(colNames)+1, documentStateTransitionPrimaryKeyColumns, len(o)))

	if boil.IsDebug(ctx) {
		writer := boil.DebugWriterFrom(ctx)
		fmt.Fprintln(writer, sql)
		fmt.Fprintln(writer, args...)
	}
	result, err := exec.ExecContext(ctx, sql, args...)
	if err != nil {
		return 0, errors.Wrap(err, "parsed_loss_runs: unable to update all in documentStateTransition slice")
	}

	rowsAff, err := result.RowsAffected()
	if err != nil {
		return 0, errors.Wrap(err, "parsed_loss_runs: unable to retrieve rows affected all in update all documentStateTransition")
	}
	return rowsAff, nil
}

// Upsert attempts an insert using an executor, and does an update or ignore on conflict.
// See boil.Columns documentation for how to properly use updateColumns and insertColumns.
func (o *DocumentStateTransition) Upsert(ctx context.Context, exec boil.ContextExecutor, updateOnConflict bool, conflictColumns []string, updateColumns, insertColumns boil.Columns) error {
	if o == nil {
		return errors.New("parsed_loss_runs: no document_state_transition provided for upsert")
	}

	if err := o.doBeforeUpsertHooks(ctx, exec); err != nil {
		return err
	}

	nzDefaults := queries.NonZeroDefaultSet(documentStateTransitionColumnsWithDefault, o)

	// Build cache key in-line uglily - mysql vs psql problems
	buf := strmangle.GetBuffer()
	if updateOnConflict {
		buf.WriteByte('t')
	} else {
		buf.WriteByte('f')
	}
	buf.WriteByte('.')
	for _, c := range conflictColumns {
		buf.WriteString(c)
	}
	buf.WriteByte('.')
	buf.WriteString(strconv.Itoa(updateColumns.Kind))
	for _, c := range updateColumns.Cols {
		buf.WriteString(c)
	}
	buf.WriteByte('.')
	buf.WriteString(strconv.Itoa(insertColumns.Kind))
	for _, c := range insertColumns.Cols {
		buf.WriteString(c)
	}
	buf.WriteByte('.')
	for _, c := range nzDefaults {
		buf.WriteString(c)
	}
	key := buf.String()
	strmangle.PutBuffer(buf)

	documentStateTransitionUpsertCacheMut.RLock()
	cache, cached := documentStateTransitionUpsertCache[key]
	documentStateTransitionUpsertCacheMut.RUnlock()

	var err error

	if !cached {
		insert, ret := insertColumns.InsertColumnSet(
			documentStateTransitionAllColumns,
			documentStateTransitionColumnsWithDefault,
			documentStateTransitionColumnsWithoutDefault,
			nzDefaults,
		)

		update := updateColumns.UpdateColumnSet(
			documentStateTransitionAllColumns,
			documentStateTransitionPrimaryKeyColumns,
		)

		if updateOnConflict && len(update) == 0 {
			return errors.New("parsed_loss_runs: unable to upsert document_state_transition, could not build update column list")
		}

		conflict := conflictColumns
		if len(conflict) == 0 {
			conflict = make([]string, len(documentStateTransitionPrimaryKeyColumns))
			copy(conflict, documentStateTransitionPrimaryKeyColumns)
		}
		cache.query = buildUpsertQueryPostgres(dialect, "\"parsed_loss_runs\".\"document_state_transition\"", updateOnConflict, ret, update, conflict, insert)

		cache.valueMapping, err = queries.BindMapping(documentStateTransitionType, documentStateTransitionMapping, insert)
		if err != nil {
			return err
		}
		if len(ret) != 0 {
			cache.retMapping, err = queries.BindMapping(documentStateTransitionType, documentStateTransitionMapping, ret)
			if err != nil {
				return err
			}
		}
	}

	value := reflect.Indirect(reflect.ValueOf(o))
	vals := queries.ValuesFromMapping(value, cache.valueMapping)
	var returns []interface{}
	if len(cache.retMapping) != 0 {
		returns = queries.PtrsFromMapping(value, cache.retMapping)
	}

	if boil.IsDebug(ctx) {
		writer := boil.DebugWriterFrom(ctx)
		fmt.Fprintln(writer, cache.query)
		fmt.Fprintln(writer, vals)
	}
	if len(cache.retMapping) != 0 {
		err = exec.QueryRowContext(ctx, cache.query, vals...).Scan(returns...)
		if errors.Is(err, sql.ErrNoRows) {
			err = nil // Postgres doesn't return anything when there's no update
		}
	} else {
		_, err = exec.ExecContext(ctx, cache.query, vals...)
	}
	if err != nil {
		return errors.Wrap(err, "parsed_loss_runs: unable to upsert document_state_transition")
	}

	if !cached {
		documentStateTransitionUpsertCacheMut.Lock()
		documentStateTransitionUpsertCache[key] = cache
		documentStateTransitionUpsertCacheMut.Unlock()
	}

	return o.doAfterUpsertHooks(ctx, exec)
}

// Delete deletes a single DocumentStateTransition record with an executor.
// Delete will match against the primary key column to find the record to delete.
func (o *DocumentStateTransition) Delete(ctx context.Context, exec boil.ContextExecutor) (int64, error) {
	if o == nil {
		return 0, errors.New("parsed_loss_runs: no DocumentStateTransition provided for delete")
	}

	if err := o.doBeforeDeleteHooks(ctx, exec); err != nil {
		return 0, err
	}

	args := queries.ValuesFromMapping(reflect.Indirect(reflect.ValueOf(o)), documentStateTransitionPrimaryKeyMapping)
	sql := "DELETE FROM \"parsed_loss_runs\".\"document_state_transition\" WHERE \"id\"=$1"

	if boil.IsDebug(ctx) {
		writer := boil.DebugWriterFrom(ctx)
		fmt.Fprintln(writer, sql)
		fmt.Fprintln(writer, args...)
	}
	result, err := exec.ExecContext(ctx, sql, args...)
	if err != nil {
		return 0, errors.Wrap(err, "parsed_loss_runs: unable to delete from document_state_transition")
	}

	rowsAff, err := result.RowsAffected()
	if err != nil {
		return 0, errors.Wrap(err, "parsed_loss_runs: failed to get rows affected by delete for document_state_transition")
	}

	if err := o.doAfterDeleteHooks(ctx, exec); err != nil {
		return 0, err
	}

	return rowsAff, nil
}

// DeleteAll deletes all matching rows.
func (q documentStateTransitionQuery) DeleteAll(ctx context.Context, exec boil.ContextExecutor) (int64, error) {
	if q.Query == nil {
		return 0, errors.New("parsed_loss_runs: no documentStateTransitionQuery provided for delete all")
	}

	queries.SetDelete(q.Query)

	result, err := q.Query.ExecContext(ctx, exec)
	if err != nil {
		return 0, errors.Wrap(err, "parsed_loss_runs: unable to delete all from document_state_transition")
	}

	rowsAff, err := result.RowsAffected()
	if err != nil {
		return 0, errors.Wrap(err, "parsed_loss_runs: failed to get rows affected by deleteall for document_state_transition")
	}

	return rowsAff, nil
}

// DeleteAll deletes all rows in the slice, using an executor.
func (o DocumentStateTransitionSlice) DeleteAll(ctx context.Context, exec boil.ContextExecutor) (int64, error) {
	if len(o) == 0 {
		return 0, nil
	}

	if len(documentStateTransitionBeforeDeleteHooks) != 0 {
		for _, obj := range o {
			if err := obj.doBeforeDeleteHooks(ctx, exec); err != nil {
				return 0, err
			}
		}
	}

	var args []interface{}
	for _, obj := range o {
		pkeyArgs := queries.ValuesFromMapping(reflect.Indirect(reflect.ValueOf(obj)), documentStateTransitionPrimaryKeyMapping)
		args = append(args, pkeyArgs...)
	}

	sql := "DELETE FROM \"parsed_loss_runs\".\"document_state_transition\" WHERE " +
		strmangle.WhereClauseRepeated(string(dialect.LQ), string(dialect.RQ), 1, documentStateTransitionPrimaryKeyColumns, len(o))

	if boil.IsDebug(ctx) {
		writer := boil.DebugWriterFrom(ctx)
		fmt.Fprintln(writer, sql)
		fmt.Fprintln(writer, args)
	}
	result, err := exec.ExecContext(ctx, sql, args...)
	if err != nil {
		return 0, errors.Wrap(err, "parsed_loss_runs: unable to delete all from documentStateTransition slice")
	}

	rowsAff, err := result.RowsAffected()
	if err != nil {
		return 0, errors.Wrap(err, "parsed_loss_runs: failed to get rows affected by deleteall for document_state_transition")
	}

	if len(documentStateTransitionAfterDeleteHooks) != 0 {
		for _, obj := range o {
			if err := obj.doAfterDeleteHooks(ctx, exec); err != nil {
				return 0, err
			}
		}
	}

	return rowsAff, nil
}

// Reload refetches the object from the database
// using the primary keys with an executor.
func (o *DocumentStateTransition) Reload(ctx context.Context, exec boil.ContextExecutor) error {
	ret, err := FindDocumentStateTransition(ctx, exec, o.ID)
	if err != nil {
		return err
	}

	*o = *ret
	return nil
}

// ReloadAll refetches every row with matching primary key column values
// and overwrites the original object slice with the newly updated slice.
func (o *DocumentStateTransitionSlice) ReloadAll(ctx context.Context, exec boil.ContextExecutor) error {
	if o == nil || len(*o) == 0 {
		return nil
	}

	slice := DocumentStateTransitionSlice{}
	var args []interface{}
	for _, obj := range *o {
		pkeyArgs := queries.ValuesFromMapping(reflect.Indirect(reflect.ValueOf(obj)), documentStateTransitionPrimaryKeyMapping)
		args = append(args, pkeyArgs...)
	}

	sql := "SELECT \"parsed_loss_runs\".\"document_state_transition\".* FROM \"parsed_loss_runs\".\"document_state_transition\" WHERE " +
		strmangle.WhereClauseRepeated(string(dialect.LQ), string(dialect.RQ), 1, documentStateTransitionPrimaryKeyColumns, len(*o))

	q := queries.Raw(sql, args...)

	err := q.Bind(ctx, exec, &slice)
	if err != nil {
		return errors.Wrap(err, "parsed_loss_runs: unable to reload all in DocumentStateTransitionSlice")
	}

	*o = slice

	return nil
}

// DocumentStateTransitionExists checks if the DocumentStateTransition row exists.
func DocumentStateTransitionExists(ctx context.Context, exec boil.ContextExecutor, iD string) (bool, error) {
	var exists bool
	sql := "select exists(select 1 from \"parsed_loss_runs\".\"document_state_transition\" where \"id\"=$1 limit 1)"

	if boil.IsDebug(ctx) {
		writer := boil.DebugWriterFrom(ctx)
		fmt.Fprintln(writer, sql)
		fmt.Fprintln(writer, iD)
	}
	row := exec.QueryRowContext(ctx, sql, iD)

	err := row.Scan(&exists)
	if err != nil {
		return false, errors.Wrap(err, "parsed_loss_runs: unable to check if document_state_transition exists")
	}

	return exists, nil
}
