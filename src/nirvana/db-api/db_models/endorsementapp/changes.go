// Code generated by SQLBoiler (https://github.com/volatiletech/sqlboiler). DO NOT EDIT.
// This file is meant to be re-generated in place and/or deleted at any time.

package endorsementapp

import (
	"context"
	"database/sql"
	"fmt"
	"reflect"
	"strconv"
	"strings"
	"sync"
	"time"

	"github.com/friendsofgo/errors"
	"github.com/volatiletech/null/v8"
	"github.com/volatiletech/sqlboiler/v4/boil"
	"github.com/volatiletech/sqlboiler/v4/queries"
	"github.com/volatiletech/sqlboiler/v4/queries/qm"
	"github.com/volatiletech/sqlboiler/v4/queries/qmhelper"
	"github.com/volatiletech/sqlboiler/v4/types"
	"github.com/volatiletech/strmangle"
)

// Change is an object representing the database table.
type Change struct {
	ID                    string            `boil:"id" json:"id" toml:"id" yaml:"id"`
	RequestID             string            `boil:"request_id" json:"request_id" toml:"request_id" yaml:"request_id"`
	ChangeType            string            `boil:"change_type" json:"change_type" toml:"change_type" yaml:"change_type"`
	ChangeData            null.JSON         `boil:"change_data" json:"change_data,omitempty" toml:"change_data" yaml:"change_data,omitempty"`
	AffectedPolicyNumbers types.StringArray `boil:"affected_policy_numbers" json:"affected_policy_numbers,omitempty" toml:"affected_policy_numbers" yaml:"affected_policy_numbers,omitempty"`
	IsActive              bool              `boil:"is_active" json:"is_active" toml:"is_active" yaml:"is_active"`
	EffectiveFrom         time.Time         `boil:"effective_from" json:"effective_from" toml:"effective_from" yaml:"effective_from"`
	ExpiresAt             time.Time         `boil:"expires_at" json:"expires_at" toml:"expires_at" yaml:"expires_at"`
	CreatedAt             time.Time         `boil:"created_at" json:"created_at" toml:"created_at" yaml:"created_at"`
	Description           null.String       `boil:"description" json:"description,omitempty" toml:"description" yaml:"description,omitempty"`

	R *changeR `boil:"" json:"" toml:"" yaml:""`
	L changeL  `boil:"-" json:"-" toml:"-" yaml:"-"`
}

var ChangeColumns = struct {
	ID                    string
	RequestID             string
	ChangeType            string
	ChangeData            string
	AffectedPolicyNumbers string
	IsActive              string
	EffectiveFrom         string
	ExpiresAt             string
	CreatedAt             string
	Description           string
}{
	ID:                    "id",
	RequestID:             "request_id",
	ChangeType:            "change_type",
	ChangeData:            "change_data",
	AffectedPolicyNumbers: "affected_policy_numbers",
	IsActive:              "is_active",
	EffectiveFrom:         "effective_from",
	ExpiresAt:             "expires_at",
	CreatedAt:             "created_at",
	Description:           "description",
}

var ChangeTableColumns = struct {
	ID                    string
	RequestID             string
	ChangeType            string
	ChangeData            string
	AffectedPolicyNumbers string
	IsActive              string
	EffectiveFrom         string
	ExpiresAt             string
	CreatedAt             string
	Description           string
}{
	ID:                    "changes.id",
	RequestID:             "changes.request_id",
	ChangeType:            "changes.change_type",
	ChangeData:            "changes.change_data",
	AffectedPolicyNumbers: "changes.affected_policy_numbers",
	IsActive:              "changes.is_active",
	EffectiveFrom:         "changes.effective_from",
	ExpiresAt:             "changes.expires_at",
	CreatedAt:             "changes.created_at",
	Description:           "changes.description",
}

// Generated where

type whereHelperstring struct{ field string }

func (w whereHelperstring) EQ(x string) qm.QueryMod  { return qmhelper.Where(w.field, qmhelper.EQ, x) }
func (w whereHelperstring) NEQ(x string) qm.QueryMod { return qmhelper.Where(w.field, qmhelper.NEQ, x) }
func (w whereHelperstring) LT(x string) qm.QueryMod  { return qmhelper.Where(w.field, qmhelper.LT, x) }
func (w whereHelperstring) LTE(x string) qm.QueryMod { return qmhelper.Where(w.field, qmhelper.LTE, x) }
func (w whereHelperstring) GT(x string) qm.QueryMod  { return qmhelper.Where(w.field, qmhelper.GT, x) }
func (w whereHelperstring) GTE(x string) qm.QueryMod { return qmhelper.Where(w.field, qmhelper.GTE, x) }
func (w whereHelperstring) IN(slice []string) qm.QueryMod {
	values := make([]interface{}, 0, len(slice))
	for _, value := range slice {
		values = append(values, value)
	}
	return qm.WhereIn(fmt.Sprintf("%s IN ?", w.field), values...)
}
func (w whereHelperstring) NIN(slice []string) qm.QueryMod {
	values := make([]interface{}, 0, len(slice))
	for _, value := range slice {
		values = append(values, value)
	}
	return qm.WhereNotIn(fmt.Sprintf("%s NOT IN ?", w.field), values...)
}

type whereHelpernull_JSON struct{ field string }

func (w whereHelpernull_JSON) EQ(x null.JSON) qm.QueryMod {
	return qmhelper.WhereNullEQ(w.field, false, x)
}
func (w whereHelpernull_JSON) NEQ(x null.JSON) qm.QueryMod {
	return qmhelper.WhereNullEQ(w.field, true, x)
}
func (w whereHelpernull_JSON) LT(x null.JSON) qm.QueryMod {
	return qmhelper.Where(w.field, qmhelper.LT, x)
}
func (w whereHelpernull_JSON) LTE(x null.JSON) qm.QueryMod {
	return qmhelper.Where(w.field, qmhelper.LTE, x)
}
func (w whereHelpernull_JSON) GT(x null.JSON) qm.QueryMod {
	return qmhelper.Where(w.field, qmhelper.GT, x)
}
func (w whereHelpernull_JSON) GTE(x null.JSON) qm.QueryMod {
	return qmhelper.Where(w.field, qmhelper.GTE, x)
}

func (w whereHelpernull_JSON) IsNull() qm.QueryMod    { return qmhelper.WhereIsNull(w.field) }
func (w whereHelpernull_JSON) IsNotNull() qm.QueryMod { return qmhelper.WhereIsNotNull(w.field) }

type whereHelpertypes_StringArray struct{ field string }

func (w whereHelpertypes_StringArray) EQ(x types.StringArray) qm.QueryMod {
	return qmhelper.WhereNullEQ(w.field, false, x)
}
func (w whereHelpertypes_StringArray) NEQ(x types.StringArray) qm.QueryMod {
	return qmhelper.WhereNullEQ(w.field, true, x)
}
func (w whereHelpertypes_StringArray) LT(x types.StringArray) qm.QueryMod {
	return qmhelper.Where(w.field, qmhelper.LT, x)
}
func (w whereHelpertypes_StringArray) LTE(x types.StringArray) qm.QueryMod {
	return qmhelper.Where(w.field, qmhelper.LTE, x)
}
func (w whereHelpertypes_StringArray) GT(x types.StringArray) qm.QueryMod {
	return qmhelper.Where(w.field, qmhelper.GT, x)
}
func (w whereHelpertypes_StringArray) GTE(x types.StringArray) qm.QueryMod {
	return qmhelper.Where(w.field, qmhelper.GTE, x)
}

func (w whereHelpertypes_StringArray) IsNull() qm.QueryMod { return qmhelper.WhereIsNull(w.field) }
func (w whereHelpertypes_StringArray) IsNotNull() qm.QueryMod {
	return qmhelper.WhereIsNotNull(w.field)
}

type whereHelperbool struct{ field string }

func (w whereHelperbool) EQ(x bool) qm.QueryMod  { return qmhelper.Where(w.field, qmhelper.EQ, x) }
func (w whereHelperbool) NEQ(x bool) qm.QueryMod { return qmhelper.Where(w.field, qmhelper.NEQ, x) }
func (w whereHelperbool) LT(x bool) qm.QueryMod  { return qmhelper.Where(w.field, qmhelper.LT, x) }
func (w whereHelperbool) LTE(x bool) qm.QueryMod { return qmhelper.Where(w.field, qmhelper.LTE, x) }
func (w whereHelperbool) GT(x bool) qm.QueryMod  { return qmhelper.Where(w.field, qmhelper.GT, x) }
func (w whereHelperbool) GTE(x bool) qm.QueryMod { return qmhelper.Where(w.field, qmhelper.GTE, x) }

type whereHelpertime_Time struct{ field string }

func (w whereHelpertime_Time) EQ(x time.Time) qm.QueryMod {
	return qmhelper.Where(w.field, qmhelper.EQ, x)
}
func (w whereHelpertime_Time) NEQ(x time.Time) qm.QueryMod {
	return qmhelper.Where(w.field, qmhelper.NEQ, x)
}
func (w whereHelpertime_Time) LT(x time.Time) qm.QueryMod {
	return qmhelper.Where(w.field, qmhelper.LT, x)
}
func (w whereHelpertime_Time) LTE(x time.Time) qm.QueryMod {
	return qmhelper.Where(w.field, qmhelper.LTE, x)
}
func (w whereHelpertime_Time) GT(x time.Time) qm.QueryMod {
	return qmhelper.Where(w.field, qmhelper.GT, x)
}
func (w whereHelpertime_Time) GTE(x time.Time) qm.QueryMod {
	return qmhelper.Where(w.field, qmhelper.GTE, x)
}

type whereHelpernull_String struct{ field string }

func (w whereHelpernull_String) EQ(x null.String) qm.QueryMod {
	return qmhelper.WhereNullEQ(w.field, false, x)
}
func (w whereHelpernull_String) NEQ(x null.String) qm.QueryMod {
	return qmhelper.WhereNullEQ(w.field, true, x)
}
func (w whereHelpernull_String) LT(x null.String) qm.QueryMod {
	return qmhelper.Where(w.field, qmhelper.LT, x)
}
func (w whereHelpernull_String) LTE(x null.String) qm.QueryMod {
	return qmhelper.Where(w.field, qmhelper.LTE, x)
}
func (w whereHelpernull_String) GT(x null.String) qm.QueryMod {
	return qmhelper.Where(w.field, qmhelper.GT, x)
}
func (w whereHelpernull_String) GTE(x null.String) qm.QueryMod {
	return qmhelper.Where(w.field, qmhelper.GTE, x)
}

func (w whereHelpernull_String) IsNull() qm.QueryMod    { return qmhelper.WhereIsNull(w.field) }
func (w whereHelpernull_String) IsNotNull() qm.QueryMod { return qmhelper.WhereIsNotNull(w.field) }

var ChangeWhere = struct {
	ID                    whereHelperstring
	RequestID             whereHelperstring
	ChangeType            whereHelperstring
	ChangeData            whereHelpernull_JSON
	AffectedPolicyNumbers whereHelpertypes_StringArray
	IsActive              whereHelperbool
	EffectiveFrom         whereHelpertime_Time
	ExpiresAt             whereHelpertime_Time
	CreatedAt             whereHelpertime_Time
	Description           whereHelpernull_String
}{
	ID:                    whereHelperstring{field: "\"endorsements\".\"changes\".\"id\""},
	RequestID:             whereHelperstring{field: "\"endorsements\".\"changes\".\"request_id\""},
	ChangeType:            whereHelperstring{field: "\"endorsements\".\"changes\".\"change_type\""},
	ChangeData:            whereHelpernull_JSON{field: "\"endorsements\".\"changes\".\"change_data\""},
	AffectedPolicyNumbers: whereHelpertypes_StringArray{field: "\"endorsements\".\"changes\".\"affected_policy_numbers\""},
	IsActive:              whereHelperbool{field: "\"endorsements\".\"changes\".\"is_active\""},
	EffectiveFrom:         whereHelpertime_Time{field: "\"endorsements\".\"changes\".\"effective_from\""},
	ExpiresAt:             whereHelpertime_Time{field: "\"endorsements\".\"changes\".\"expires_at\""},
	CreatedAt:             whereHelpertime_Time{field: "\"endorsements\".\"changes\".\"created_at\""},
	Description:           whereHelpernull_String{field: "\"endorsements\".\"changes\".\"description\""},
}

// ChangeRels is where relationship names are stored.
var ChangeRels = struct {
	Request                       string
	OriginalChangeReviewOverrides string
}{
	Request:                       "Request",
	OriginalChangeReviewOverrides: "OriginalChangeReviewOverrides",
}

// changeR is where relationships are stored.
type changeR struct {
	Request                       *Request            `boil:"Request" json:"Request" toml:"Request" yaml:"Request"`
	OriginalChangeReviewOverrides ReviewOverrideSlice `boil:"OriginalChangeReviewOverrides" json:"OriginalChangeReviewOverrides" toml:"OriginalChangeReviewOverrides" yaml:"OriginalChangeReviewOverrides"`
}

// NewStruct creates a new relationship struct
func (*changeR) NewStruct() *changeR {
	return &changeR{}
}

// changeL is where Load methods for each relationship are stored.
type changeL struct{}

var (
	changeAllColumns            = []string{"id", "request_id", "change_type", "change_data", "affected_policy_numbers", "is_active", "effective_from", "expires_at", "created_at", "description"}
	changeColumnsWithoutDefault = []string{"id", "request_id", "change_type", "is_active", "effective_from", "expires_at"}
	changeColumnsWithDefault    = []string{"change_data", "affected_policy_numbers", "created_at", "description"}
	changePrimaryKeyColumns     = []string{"id"}
	changeGeneratedColumns      = []string{}
)

type (
	// ChangeSlice is an alias for a slice of pointers to Change.
	// This should almost always be used instead of []Change.
	ChangeSlice []*Change
	// ChangeHook is the signature for custom Change hook methods
	ChangeHook func(context.Context, boil.ContextExecutor, *Change) error

	changeQuery struct {
		*queries.Query
	}
)

// Cache for insert, update and upsert
var (
	changeType                 = reflect.TypeOf(&Change{})
	changeMapping              = queries.MakeStructMapping(changeType)
	changePrimaryKeyMapping, _ = queries.BindMapping(changeType, changeMapping, changePrimaryKeyColumns)
	changeInsertCacheMut       sync.RWMutex
	changeInsertCache          = make(map[string]insertCache)
	changeUpdateCacheMut       sync.RWMutex
	changeUpdateCache          = make(map[string]updateCache)
	changeUpsertCacheMut       sync.RWMutex
	changeUpsertCache          = make(map[string]insertCache)
)

var (
	// Force time package dependency for automated UpdatedAt/CreatedAt.
	_ = time.Second
	// Force qmhelper dependency for where clause generation (which doesn't
	// always happen)
	_ = qmhelper.Where
)

var changeAfterSelectHooks []ChangeHook

var changeBeforeInsertHooks []ChangeHook
var changeAfterInsertHooks []ChangeHook

var changeBeforeUpdateHooks []ChangeHook
var changeAfterUpdateHooks []ChangeHook

var changeBeforeDeleteHooks []ChangeHook
var changeAfterDeleteHooks []ChangeHook

var changeBeforeUpsertHooks []ChangeHook
var changeAfterUpsertHooks []ChangeHook

// doAfterSelectHooks executes all "after Select" hooks.
func (o *Change) doAfterSelectHooks(ctx context.Context, exec boil.ContextExecutor) (err error) {
	if boil.HooksAreSkipped(ctx) {
		return nil
	}

	for _, hook := range changeAfterSelectHooks {
		if err := hook(ctx, exec, o); err != nil {
			return err
		}
	}

	return nil
}

// doBeforeInsertHooks executes all "before insert" hooks.
func (o *Change) doBeforeInsertHooks(ctx context.Context, exec boil.ContextExecutor) (err error) {
	if boil.HooksAreSkipped(ctx) {
		return nil
	}

	for _, hook := range changeBeforeInsertHooks {
		if err := hook(ctx, exec, o); err != nil {
			return err
		}
	}

	return nil
}

// doAfterInsertHooks executes all "after Insert" hooks.
func (o *Change) doAfterInsertHooks(ctx context.Context, exec boil.ContextExecutor) (err error) {
	if boil.HooksAreSkipped(ctx) {
		return nil
	}

	for _, hook := range changeAfterInsertHooks {
		if err := hook(ctx, exec, o); err != nil {
			return err
		}
	}

	return nil
}

// doBeforeUpdateHooks executes all "before Update" hooks.
func (o *Change) doBeforeUpdateHooks(ctx context.Context, exec boil.ContextExecutor) (err error) {
	if boil.HooksAreSkipped(ctx) {
		return nil
	}

	for _, hook := range changeBeforeUpdateHooks {
		if err := hook(ctx, exec, o); err != nil {
			return err
		}
	}

	return nil
}

// doAfterUpdateHooks executes all "after Update" hooks.
func (o *Change) doAfterUpdateHooks(ctx context.Context, exec boil.ContextExecutor) (err error) {
	if boil.HooksAreSkipped(ctx) {
		return nil
	}

	for _, hook := range changeAfterUpdateHooks {
		if err := hook(ctx, exec, o); err != nil {
			return err
		}
	}

	return nil
}

// doBeforeDeleteHooks executes all "before Delete" hooks.
func (o *Change) doBeforeDeleteHooks(ctx context.Context, exec boil.ContextExecutor) (err error) {
	if boil.HooksAreSkipped(ctx) {
		return nil
	}

	for _, hook := range changeBeforeDeleteHooks {
		if err := hook(ctx, exec, o); err != nil {
			return err
		}
	}

	return nil
}

// doAfterDeleteHooks executes all "after Delete" hooks.
func (o *Change) doAfterDeleteHooks(ctx context.Context, exec boil.ContextExecutor) (err error) {
	if boil.HooksAreSkipped(ctx) {
		return nil
	}

	for _, hook := range changeAfterDeleteHooks {
		if err := hook(ctx, exec, o); err != nil {
			return err
		}
	}

	return nil
}

// doBeforeUpsertHooks executes all "before Upsert" hooks.
func (o *Change) doBeforeUpsertHooks(ctx context.Context, exec boil.ContextExecutor) (err error) {
	if boil.HooksAreSkipped(ctx) {
		return nil
	}

	for _, hook := range changeBeforeUpsertHooks {
		if err := hook(ctx, exec, o); err != nil {
			return err
		}
	}

	return nil
}

// doAfterUpsertHooks executes all "after Upsert" hooks.
func (o *Change) doAfterUpsertHooks(ctx context.Context, exec boil.ContextExecutor) (err error) {
	if boil.HooksAreSkipped(ctx) {
		return nil
	}

	for _, hook := range changeAfterUpsertHooks {
		if err := hook(ctx, exec, o); err != nil {
			return err
		}
	}

	return nil
}

// AddChangeHook registers your hook function for all future operations.
func AddChangeHook(hookPoint boil.HookPoint, changeHook ChangeHook) {
	switch hookPoint {
	case boil.AfterSelectHook:
		changeAfterSelectHooks = append(changeAfterSelectHooks, changeHook)
	case boil.BeforeInsertHook:
		changeBeforeInsertHooks = append(changeBeforeInsertHooks, changeHook)
	case boil.AfterInsertHook:
		changeAfterInsertHooks = append(changeAfterInsertHooks, changeHook)
	case boil.BeforeUpdateHook:
		changeBeforeUpdateHooks = append(changeBeforeUpdateHooks, changeHook)
	case boil.AfterUpdateHook:
		changeAfterUpdateHooks = append(changeAfterUpdateHooks, changeHook)
	case boil.BeforeDeleteHook:
		changeBeforeDeleteHooks = append(changeBeforeDeleteHooks, changeHook)
	case boil.AfterDeleteHook:
		changeAfterDeleteHooks = append(changeAfterDeleteHooks, changeHook)
	case boil.BeforeUpsertHook:
		changeBeforeUpsertHooks = append(changeBeforeUpsertHooks, changeHook)
	case boil.AfterUpsertHook:
		changeAfterUpsertHooks = append(changeAfterUpsertHooks, changeHook)
	}
}

// One returns a single change record from the query.
func (q changeQuery) One(ctx context.Context, exec boil.ContextExecutor) (*Change, error) {
	o := &Change{}

	queries.SetLimit(q.Query, 1)

	err := q.Bind(ctx, exec, o)
	if err != nil {
		if errors.Is(err, sql.ErrNoRows) {
			return nil, sql.ErrNoRows
		}
		return nil, errors.Wrap(err, "endorsementapp: failed to execute a one query for changes")
	}

	if err := o.doAfterSelectHooks(ctx, exec); err != nil {
		return o, err
	}

	return o, nil
}

// All returns all Change records from the query.
func (q changeQuery) All(ctx context.Context, exec boil.ContextExecutor) (ChangeSlice, error) {
	var o []*Change

	err := q.Bind(ctx, exec, &o)
	if err != nil {
		return nil, errors.Wrap(err, "endorsementapp: failed to assign all query results to Change slice")
	}

	if len(changeAfterSelectHooks) != 0 {
		for _, obj := range o {
			if err := obj.doAfterSelectHooks(ctx, exec); err != nil {
				return o, err
			}
		}
	}

	return o, nil
}

// Count returns the count of all Change records in the query.
func (q changeQuery) Count(ctx context.Context, exec boil.ContextExecutor) (int64, error) {
	var count int64

	queries.SetSelect(q.Query, nil)
	queries.SetCount(q.Query)

	err := q.Query.QueryRowContext(ctx, exec).Scan(&count)
	if err != nil {
		return 0, errors.Wrap(err, "endorsementapp: failed to count changes rows")
	}

	return count, nil
}

// Exists checks if the row exists in the table.
func (q changeQuery) Exists(ctx context.Context, exec boil.ContextExecutor) (bool, error) {
	var count int64

	queries.SetSelect(q.Query, nil)
	queries.SetCount(q.Query)
	queries.SetLimit(q.Query, 1)

	err := q.Query.QueryRowContext(ctx, exec).Scan(&count)
	if err != nil {
		return false, errors.Wrap(err, "endorsementapp: failed to check if changes exists")
	}

	return count > 0, nil
}

// Request pointed to by the foreign key.
func (o *Change) Request(mods ...qm.QueryMod) requestQuery {
	queryMods := []qm.QueryMod{
		qm.Where("\"id\" = ?", o.RequestID),
	}

	queryMods = append(queryMods, mods...)

	return Requests(queryMods...)
}

// OriginalChangeReviewOverrides retrieves all the review_override's ReviewOverrides with an executor via original_change_id column.
func (o *Change) OriginalChangeReviewOverrides(mods ...qm.QueryMod) reviewOverrideQuery {
	var queryMods []qm.QueryMod
	if len(mods) != 0 {
		queryMods = append(queryMods, mods...)
	}

	queryMods = append(queryMods,
		qm.Where("\"endorsements\".\"review_override\".\"original_change_id\"=?", o.ID),
	)

	return ReviewOverrides(queryMods...)
}

// LoadRequest allows an eager lookup of values, cached into the
// loaded structs of the objects. This is for an N-1 relationship.
func (changeL) LoadRequest(ctx context.Context, e boil.ContextExecutor, singular bool, maybeChange interface{}, mods queries.Applicator) error {
	var slice []*Change
	var object *Change

	if singular {
		object = maybeChange.(*Change)
	} else {
		slice = *maybeChange.(*[]*Change)
	}

	args := make([]interface{}, 0, 1)
	if singular {
		if object.R == nil {
			object.R = &changeR{}
		}
		args = append(args, object.RequestID)

	} else {
	Outer:
		for _, obj := range slice {
			if obj.R == nil {
				obj.R = &changeR{}
			}

			for _, a := range args {
				if a == obj.RequestID {
					continue Outer
				}
			}

			args = append(args, obj.RequestID)

		}
	}

	if len(args) == 0 {
		return nil
	}

	query := NewQuery(
		qm.From(`endorsements.request`),
		qm.WhereIn(`endorsements.request.id in ?`, args...),
	)
	if mods != nil {
		mods.Apply(query)
	}

	results, err := query.QueryContext(ctx, e)
	if err != nil {
		return errors.Wrap(err, "failed to eager load Request")
	}

	var resultSlice []*Request
	if err = queries.Bind(results, &resultSlice); err != nil {
		return errors.Wrap(err, "failed to bind eager loaded slice Request")
	}

	if err = results.Close(); err != nil {
		return errors.Wrap(err, "failed to close results of eager load for request")
	}
	if err = results.Err(); err != nil {
		return errors.Wrap(err, "error occurred during iteration of eager loaded relations for request")
	}

	if len(changeAfterSelectHooks) != 0 {
		for _, obj := range resultSlice {
			if err := obj.doAfterSelectHooks(ctx, e); err != nil {
				return err
			}
		}
	}

	if len(resultSlice) == 0 {
		return nil
	}

	if singular {
		foreign := resultSlice[0]
		object.R.Request = foreign
		if foreign.R == nil {
			foreign.R = &requestR{}
		}
		foreign.R.Changes = append(foreign.R.Changes, object)
		return nil
	}

	for _, local := range slice {
		for _, foreign := range resultSlice {
			if local.RequestID == foreign.ID {
				local.R.Request = foreign
				if foreign.R == nil {
					foreign.R = &requestR{}
				}
				foreign.R.Changes = append(foreign.R.Changes, local)
				break
			}
		}
	}

	return nil
}

// LoadOriginalChangeReviewOverrides allows an eager lookup of values, cached into the
// loaded structs of the objects. This is for a 1-M or N-M relationship.
func (changeL) LoadOriginalChangeReviewOverrides(ctx context.Context, e boil.ContextExecutor, singular bool, maybeChange interface{}, mods queries.Applicator) error {
	var slice []*Change
	var object *Change

	if singular {
		object = maybeChange.(*Change)
	} else {
		slice = *maybeChange.(*[]*Change)
	}

	args := make([]interface{}, 0, 1)
	if singular {
		if object.R == nil {
			object.R = &changeR{}
		}
		args = append(args, object.ID)
	} else {
	Outer:
		for _, obj := range slice {
			if obj.R == nil {
				obj.R = &changeR{}
			}

			for _, a := range args {
				if queries.Equal(a, obj.ID) {
					continue Outer
				}
			}

			args = append(args, obj.ID)
		}
	}

	if len(args) == 0 {
		return nil
	}

	query := NewQuery(
		qm.From(`endorsements.review_override`),
		qm.WhereIn(`endorsements.review_override.original_change_id in ?`, args...),
	)
	if mods != nil {
		mods.Apply(query)
	}

	results, err := query.QueryContext(ctx, e)
	if err != nil {
		return errors.Wrap(err, "failed to eager load review_override")
	}

	var resultSlice []*ReviewOverride
	if err = queries.Bind(results, &resultSlice); err != nil {
		return errors.Wrap(err, "failed to bind eager loaded slice review_override")
	}

	if err = results.Close(); err != nil {
		return errors.Wrap(err, "failed to close results in eager load on review_override")
	}
	if err = results.Err(); err != nil {
		return errors.Wrap(err, "error occurred during iteration of eager loaded relations for review_override")
	}

	if len(reviewOverrideAfterSelectHooks) != 0 {
		for _, obj := range resultSlice {
			if err := obj.doAfterSelectHooks(ctx, e); err != nil {
				return err
			}
		}
	}
	if singular {
		object.R.OriginalChangeReviewOverrides = resultSlice
		for _, foreign := range resultSlice {
			if foreign.R == nil {
				foreign.R = &reviewOverrideR{}
			}
			foreign.R.OriginalChange = object
		}
		return nil
	}

	for _, foreign := range resultSlice {
		for _, local := range slice {
			if queries.Equal(local.ID, foreign.OriginalChangeID) {
				local.R.OriginalChangeReviewOverrides = append(local.R.OriginalChangeReviewOverrides, foreign)
				if foreign.R == nil {
					foreign.R = &reviewOverrideR{}
				}
				foreign.R.OriginalChange = local
				break
			}
		}
	}

	return nil
}

// SetRequest of the change to the related item.
// Sets o.R.Request to related.
// Adds o to related.R.Changes.
func (o *Change) SetRequest(ctx context.Context, exec boil.ContextExecutor, insert bool, related *Request) error {
	var err error
	if insert {
		if err = related.Insert(ctx, exec, boil.Infer()); err != nil {
			return errors.Wrap(err, "failed to insert into foreign table")
		}
	}

	updateQuery := fmt.Sprintf(
		"UPDATE \"endorsements\".\"changes\" SET %s WHERE %s",
		strmangle.SetParamNames("\"", "\"", 1, []string{"request_id"}),
		strmangle.WhereClause("\"", "\"", 2, changePrimaryKeyColumns),
	)
	values := []interface{}{related.ID, o.ID}

	if boil.IsDebug(ctx) {
		writer := boil.DebugWriterFrom(ctx)
		fmt.Fprintln(writer, updateQuery)
		fmt.Fprintln(writer, values)
	}
	if _, err = exec.ExecContext(ctx, updateQuery, values...); err != nil {
		return errors.Wrap(err, "failed to update local table")
	}

	o.RequestID = related.ID
	if o.R == nil {
		o.R = &changeR{
			Request: related,
		}
	} else {
		o.R.Request = related
	}

	if related.R == nil {
		related.R = &requestR{
			Changes: ChangeSlice{o},
		}
	} else {
		related.R.Changes = append(related.R.Changes, o)
	}

	return nil
}

// AddOriginalChangeReviewOverrides adds the given related objects to the existing relationships
// of the change, optionally inserting them as new records.
// Appends related to o.R.OriginalChangeReviewOverrides.
// Sets related.R.OriginalChange appropriately.
func (o *Change) AddOriginalChangeReviewOverrides(ctx context.Context, exec boil.ContextExecutor, insert bool, related ...*ReviewOverride) error {
	var err error
	for _, rel := range related {
		if insert {
			queries.Assign(&rel.OriginalChangeID, o.ID)
			if err = rel.Insert(ctx, exec, boil.Infer()); err != nil {
				return errors.Wrap(err, "failed to insert into foreign table")
			}
		} else {
			updateQuery := fmt.Sprintf(
				"UPDATE \"endorsements\".\"review_override\" SET %s WHERE %s",
				strmangle.SetParamNames("\"", "\"", 1, []string{"original_change_id"}),
				strmangle.WhereClause("\"", "\"", 2, reviewOverridePrimaryKeyColumns),
			)
			values := []interface{}{o.ID, rel.ID}

			if boil.IsDebug(ctx) {
				writer := boil.DebugWriterFrom(ctx)
				fmt.Fprintln(writer, updateQuery)
				fmt.Fprintln(writer, values)
			}
			if _, err = exec.ExecContext(ctx, updateQuery, values...); err != nil {
				return errors.Wrap(err, "failed to update foreign table")
			}

			queries.Assign(&rel.OriginalChangeID, o.ID)
		}
	}

	if o.R == nil {
		o.R = &changeR{
			OriginalChangeReviewOverrides: related,
		}
	} else {
		o.R.OriginalChangeReviewOverrides = append(o.R.OriginalChangeReviewOverrides, related...)
	}

	for _, rel := range related {
		if rel.R == nil {
			rel.R = &reviewOverrideR{
				OriginalChange: o,
			}
		} else {
			rel.R.OriginalChange = o
		}
	}
	return nil
}

// SetOriginalChangeReviewOverrides removes all previously related items of the
// change replacing them completely with the passed
// in related items, optionally inserting them as new records.
// Sets o.R.OriginalChange's OriginalChangeReviewOverrides accordingly.
// Replaces o.R.OriginalChangeReviewOverrides with related.
// Sets related.R.OriginalChange's OriginalChangeReviewOverrides accordingly.
func (o *Change) SetOriginalChangeReviewOverrides(ctx context.Context, exec boil.ContextExecutor, insert bool, related ...*ReviewOverride) error {
	query := "update \"endorsements\".\"review_override\" set \"original_change_id\" = null where \"original_change_id\" = $1"
	values := []interface{}{o.ID}
	if boil.IsDebug(ctx) {
		writer := boil.DebugWriterFrom(ctx)
		fmt.Fprintln(writer, query)
		fmt.Fprintln(writer, values)
	}
	_, err := exec.ExecContext(ctx, query, values...)
	if err != nil {
		return errors.Wrap(err, "failed to remove relationships before set")
	}

	if o.R != nil {
		for _, rel := range o.R.OriginalChangeReviewOverrides {
			queries.SetScanner(&rel.OriginalChangeID, nil)
			if rel.R == nil {
				continue
			}

			rel.R.OriginalChange = nil
		}
		o.R.OriginalChangeReviewOverrides = nil
	}

	return o.AddOriginalChangeReviewOverrides(ctx, exec, insert, related...)
}

// RemoveOriginalChangeReviewOverrides relationships from objects passed in.
// Removes related items from R.OriginalChangeReviewOverrides (uses pointer comparison, removal does not keep order)
// Sets related.R.OriginalChange.
func (o *Change) RemoveOriginalChangeReviewOverrides(ctx context.Context, exec boil.ContextExecutor, related ...*ReviewOverride) error {
	if len(related) == 0 {
		return nil
	}

	var err error
	for _, rel := range related {
		queries.SetScanner(&rel.OriginalChangeID, nil)
		if rel.R != nil {
			rel.R.OriginalChange = nil
		}
		if _, err = rel.Update(ctx, exec, boil.Whitelist("original_change_id")); err != nil {
			return err
		}
	}
	if o.R == nil {
		return nil
	}

	for _, rel := range related {
		for i, ri := range o.R.OriginalChangeReviewOverrides {
			if rel != ri {
				continue
			}

			ln := len(o.R.OriginalChangeReviewOverrides)
			if ln > 1 && i < ln-1 {
				o.R.OriginalChangeReviewOverrides[i] = o.R.OriginalChangeReviewOverrides[ln-1]
			}
			o.R.OriginalChangeReviewOverrides = o.R.OriginalChangeReviewOverrides[:ln-1]
			break
		}
	}

	return nil
}

// Changes retrieves all the records using an executor.
func Changes(mods ...qm.QueryMod) changeQuery {
	mods = append(mods, qm.From("\"endorsements\".\"changes\""))
	q := NewQuery(mods...)
	if len(queries.GetSelect(q)) == 0 {
		queries.SetSelect(q, []string{"\"endorsements\".\"changes\".*"})
	}

	return changeQuery{q}
}

// FindChange retrieves a single record by ID with an executor.
// If selectCols is empty Find will return all columns.
func FindChange(ctx context.Context, exec boil.ContextExecutor, iD string, selectCols ...string) (*Change, error) {
	changeObj := &Change{}

	sel := "*"
	if len(selectCols) > 0 {
		sel = strings.Join(strmangle.IdentQuoteSlice(dialect.LQ, dialect.RQ, selectCols), ",")
	}
	query := fmt.Sprintf(
		"select %s from \"endorsements\".\"changes\" where \"id\"=$1", sel,
	)

	q := queries.Raw(query, iD)

	err := q.Bind(ctx, exec, changeObj)
	if err != nil {
		if errors.Is(err, sql.ErrNoRows) {
			return nil, sql.ErrNoRows
		}
		return nil, errors.Wrap(err, "endorsementapp: unable to select from changes")
	}

	if err = changeObj.doAfterSelectHooks(ctx, exec); err != nil {
		return changeObj, err
	}

	return changeObj, nil
}

// Insert a single record using an executor.
// See boil.Columns.InsertColumnSet documentation to understand column list inference for inserts.
func (o *Change) Insert(ctx context.Context, exec boil.ContextExecutor, columns boil.Columns) error {
	if o == nil {
		return errors.New("endorsementapp: no changes provided for insertion")
	}

	var err error
	if !boil.TimestampsAreSkipped(ctx) {
		currTime := time.Now().In(boil.GetLocation())

		if o.CreatedAt.IsZero() {
			o.CreatedAt = currTime
		}
	}

	if err := o.doBeforeInsertHooks(ctx, exec); err != nil {
		return err
	}

	nzDefaults := queries.NonZeroDefaultSet(changeColumnsWithDefault, o)

	key := makeCacheKey(columns, nzDefaults)
	changeInsertCacheMut.RLock()
	cache, cached := changeInsertCache[key]
	changeInsertCacheMut.RUnlock()

	if !cached {
		wl, returnColumns := columns.InsertColumnSet(
			changeAllColumns,
			changeColumnsWithDefault,
			changeColumnsWithoutDefault,
			nzDefaults,
		)

		cache.valueMapping, err = queries.BindMapping(changeType, changeMapping, wl)
		if err != nil {
			return err
		}
		cache.retMapping, err = queries.BindMapping(changeType, changeMapping, returnColumns)
		if err != nil {
			return err
		}
		if len(wl) != 0 {
			cache.query = fmt.Sprintf("INSERT INTO \"endorsements\".\"changes\" (\"%s\") %%sVALUES (%s)%%s", strings.Join(wl, "\",\""), strmangle.Placeholders(dialect.UseIndexPlaceholders, len(wl), 1, 1))
		} else {
			cache.query = "INSERT INTO \"endorsements\".\"changes\" %sDEFAULT VALUES%s"
		}

		var queryOutput, queryReturning string

		if len(cache.retMapping) != 0 {
			queryReturning = fmt.Sprintf(" RETURNING \"%s\"", strings.Join(returnColumns, "\",\""))
		}

		cache.query = fmt.Sprintf(cache.query, queryOutput, queryReturning)
	}

	value := reflect.Indirect(reflect.ValueOf(o))
	vals := queries.ValuesFromMapping(value, cache.valueMapping)

	if boil.IsDebug(ctx) {
		writer := boil.DebugWriterFrom(ctx)
		fmt.Fprintln(writer, cache.query)
		fmt.Fprintln(writer, vals)
	}

	if len(cache.retMapping) != 0 {
		err = exec.QueryRowContext(ctx, cache.query, vals...).Scan(queries.PtrsFromMapping(value, cache.retMapping)...)
	} else {
		_, err = exec.ExecContext(ctx, cache.query, vals...)
	}

	if err != nil {
		return errors.Wrap(err, "endorsementapp: unable to insert into changes")
	}

	if !cached {
		changeInsertCacheMut.Lock()
		changeInsertCache[key] = cache
		changeInsertCacheMut.Unlock()
	}

	return o.doAfterInsertHooks(ctx, exec)
}

// Update uses an executor to update the Change.
// See boil.Columns.UpdateColumnSet documentation to understand column list inference for updates.
// Update does not automatically update the record in case of default values. Use .Reload() to refresh the records.
func (o *Change) Update(ctx context.Context, exec boil.ContextExecutor, columns boil.Columns) (int64, error) {
	var err error
	if err = o.doBeforeUpdateHooks(ctx, exec); err != nil {
		return 0, err
	}
	key := makeCacheKey(columns, nil)
	changeUpdateCacheMut.RLock()
	cache, cached := changeUpdateCache[key]
	changeUpdateCacheMut.RUnlock()

	if !cached {
		wl := columns.UpdateColumnSet(
			changeAllColumns,
			changePrimaryKeyColumns,
		)

		if !columns.IsWhitelist() {
			wl = strmangle.SetComplement(wl, []string{"created_at"})
		}
		if len(wl) == 0 {
			return 0, errors.New("endorsementapp: unable to update changes, could not build whitelist")
		}

		cache.query = fmt.Sprintf("UPDATE \"endorsements\".\"changes\" SET %s WHERE %s",
			strmangle.SetParamNames("\"", "\"", 1, wl),
			strmangle.WhereClause("\"", "\"", len(wl)+1, changePrimaryKeyColumns),
		)
		cache.valueMapping, err = queries.BindMapping(changeType, changeMapping, append(wl, changePrimaryKeyColumns...))
		if err != nil {
			return 0, err
		}
	}

	values := queries.ValuesFromMapping(reflect.Indirect(reflect.ValueOf(o)), cache.valueMapping)

	if boil.IsDebug(ctx) {
		writer := boil.DebugWriterFrom(ctx)
		fmt.Fprintln(writer, cache.query)
		fmt.Fprintln(writer, values)
	}
	var result sql.Result
	result, err = exec.ExecContext(ctx, cache.query, values...)
	if err != nil {
		return 0, errors.Wrap(err, "endorsementapp: unable to update changes row")
	}

	rowsAff, err := result.RowsAffected()
	if err != nil {
		return 0, errors.Wrap(err, "endorsementapp: failed to get rows affected by update for changes")
	}

	if !cached {
		changeUpdateCacheMut.Lock()
		changeUpdateCache[key] = cache
		changeUpdateCacheMut.Unlock()
	}

	return rowsAff, o.doAfterUpdateHooks(ctx, exec)
}

// UpdateAll updates all rows with the specified column values.
func (q changeQuery) UpdateAll(ctx context.Context, exec boil.ContextExecutor, cols M) (int64, error) {
	queries.SetUpdate(q.Query, cols)

	result, err := q.Query.ExecContext(ctx, exec)
	if err != nil {
		return 0, errors.Wrap(err, "endorsementapp: unable to update all for changes")
	}

	rowsAff, err := result.RowsAffected()
	if err != nil {
		return 0, errors.Wrap(err, "endorsementapp: unable to retrieve rows affected for changes")
	}

	return rowsAff, nil
}

// UpdateAll updates all rows with the specified column values, using an executor.
func (o ChangeSlice) UpdateAll(ctx context.Context, exec boil.ContextExecutor, cols M) (int64, error) {
	ln := int64(len(o))
	if ln == 0 {
		return 0, nil
	}

	if len(cols) == 0 {
		return 0, errors.New("endorsementapp: update all requires at least one column argument")
	}

	colNames := make([]string, len(cols))
	args := make([]interface{}, len(cols))

	i := 0
	for name, value := range cols {
		colNames[i] = name
		args[i] = value
		i++
	}

	// Append all of the primary key values for each column
	for _, obj := range o {
		pkeyArgs := queries.ValuesFromMapping(reflect.Indirect(reflect.ValueOf(obj)), changePrimaryKeyMapping)
		args = append(args, pkeyArgs...)
	}

	sql := fmt.Sprintf("UPDATE \"endorsements\".\"changes\" SET %s WHERE %s",
		strmangle.SetParamNames("\"", "\"", 1, colNames),
		strmangle.WhereClauseRepeated(string(dialect.LQ), string(dialect.RQ), len(colNames)+1, changePrimaryKeyColumns, len(o)))

	if boil.IsDebug(ctx) {
		writer := boil.DebugWriterFrom(ctx)
		fmt.Fprintln(writer, sql)
		fmt.Fprintln(writer, args...)
	}
	result, err := exec.ExecContext(ctx, sql, args...)
	if err != nil {
		return 0, errors.Wrap(err, "endorsementapp: unable to update all in change slice")
	}

	rowsAff, err := result.RowsAffected()
	if err != nil {
		return 0, errors.Wrap(err, "endorsementapp: unable to retrieve rows affected all in update all change")
	}
	return rowsAff, nil
}

// Upsert attempts an insert using an executor, and does an update or ignore on conflict.
// See boil.Columns documentation for how to properly use updateColumns and insertColumns.
func (o *Change) Upsert(ctx context.Context, exec boil.ContextExecutor, updateOnConflict bool, conflictColumns []string, updateColumns, insertColumns boil.Columns) error {
	if o == nil {
		return errors.New("endorsementapp: no changes provided for upsert")
	}
	if !boil.TimestampsAreSkipped(ctx) {
		currTime := time.Now().In(boil.GetLocation())

		if o.CreatedAt.IsZero() {
			o.CreatedAt = currTime
		}
	}

	if err := o.doBeforeUpsertHooks(ctx, exec); err != nil {
		return err
	}

	nzDefaults := queries.NonZeroDefaultSet(changeColumnsWithDefault, o)

	// Build cache key in-line uglily - mysql vs psql problems
	buf := strmangle.GetBuffer()
	if updateOnConflict {
		buf.WriteByte('t')
	} else {
		buf.WriteByte('f')
	}
	buf.WriteByte('.')
	for _, c := range conflictColumns {
		buf.WriteString(c)
	}
	buf.WriteByte('.')
	buf.WriteString(strconv.Itoa(updateColumns.Kind))
	for _, c := range updateColumns.Cols {
		buf.WriteString(c)
	}
	buf.WriteByte('.')
	buf.WriteString(strconv.Itoa(insertColumns.Kind))
	for _, c := range insertColumns.Cols {
		buf.WriteString(c)
	}
	buf.WriteByte('.')
	for _, c := range nzDefaults {
		buf.WriteString(c)
	}
	key := buf.String()
	strmangle.PutBuffer(buf)

	changeUpsertCacheMut.RLock()
	cache, cached := changeUpsertCache[key]
	changeUpsertCacheMut.RUnlock()

	var err error

	if !cached {
		insert, ret := insertColumns.InsertColumnSet(
			changeAllColumns,
			changeColumnsWithDefault,
			changeColumnsWithoutDefault,
			nzDefaults,
		)

		update := updateColumns.UpdateColumnSet(
			changeAllColumns,
			changePrimaryKeyColumns,
		)

		if updateOnConflict && len(update) == 0 {
			return errors.New("endorsementapp: unable to upsert changes, could not build update column list")
		}

		conflict := conflictColumns
		if len(conflict) == 0 {
			conflict = make([]string, len(changePrimaryKeyColumns))
			copy(conflict, changePrimaryKeyColumns)
		}
		cache.query = buildUpsertQueryPostgres(dialect, "\"endorsements\".\"changes\"", updateOnConflict, ret, update, conflict, insert)

		cache.valueMapping, err = queries.BindMapping(changeType, changeMapping, insert)
		if err != nil {
			return err
		}
		if len(ret) != 0 {
			cache.retMapping, err = queries.BindMapping(changeType, changeMapping, ret)
			if err != nil {
				return err
			}
		}
	}

	value := reflect.Indirect(reflect.ValueOf(o))
	vals := queries.ValuesFromMapping(value, cache.valueMapping)
	var returns []interface{}
	if len(cache.retMapping) != 0 {
		returns = queries.PtrsFromMapping(value, cache.retMapping)
	}

	if boil.IsDebug(ctx) {
		writer := boil.DebugWriterFrom(ctx)
		fmt.Fprintln(writer, cache.query)
		fmt.Fprintln(writer, vals)
	}
	if len(cache.retMapping) != 0 {
		err = exec.QueryRowContext(ctx, cache.query, vals...).Scan(returns...)
		if errors.Is(err, sql.ErrNoRows) {
			err = nil // Postgres doesn't return anything when there's no update
		}
	} else {
		_, err = exec.ExecContext(ctx, cache.query, vals...)
	}
	if err != nil {
		return errors.Wrap(err, "endorsementapp: unable to upsert changes")
	}

	if !cached {
		changeUpsertCacheMut.Lock()
		changeUpsertCache[key] = cache
		changeUpsertCacheMut.Unlock()
	}

	return o.doAfterUpsertHooks(ctx, exec)
}

// Delete deletes a single Change record with an executor.
// Delete will match against the primary key column to find the record to delete.
func (o *Change) Delete(ctx context.Context, exec boil.ContextExecutor) (int64, error) {
	if o == nil {
		return 0, errors.New("endorsementapp: no Change provided for delete")
	}

	if err := o.doBeforeDeleteHooks(ctx, exec); err != nil {
		return 0, err
	}

	args := queries.ValuesFromMapping(reflect.Indirect(reflect.ValueOf(o)), changePrimaryKeyMapping)
	sql := "DELETE FROM \"endorsements\".\"changes\" WHERE \"id\"=$1"

	if boil.IsDebug(ctx) {
		writer := boil.DebugWriterFrom(ctx)
		fmt.Fprintln(writer, sql)
		fmt.Fprintln(writer, args...)
	}
	result, err := exec.ExecContext(ctx, sql, args...)
	if err != nil {
		return 0, errors.Wrap(err, "endorsementapp: unable to delete from changes")
	}

	rowsAff, err := result.RowsAffected()
	if err != nil {
		return 0, errors.Wrap(err, "endorsementapp: failed to get rows affected by delete for changes")
	}

	if err := o.doAfterDeleteHooks(ctx, exec); err != nil {
		return 0, err
	}

	return rowsAff, nil
}

// DeleteAll deletes all matching rows.
func (q changeQuery) DeleteAll(ctx context.Context, exec boil.ContextExecutor) (int64, error) {
	if q.Query == nil {
		return 0, errors.New("endorsementapp: no changeQuery provided for delete all")
	}

	queries.SetDelete(q.Query)

	result, err := q.Query.ExecContext(ctx, exec)
	if err != nil {
		return 0, errors.Wrap(err, "endorsementapp: unable to delete all from changes")
	}

	rowsAff, err := result.RowsAffected()
	if err != nil {
		return 0, errors.Wrap(err, "endorsementapp: failed to get rows affected by deleteall for changes")
	}

	return rowsAff, nil
}

// DeleteAll deletes all rows in the slice, using an executor.
func (o ChangeSlice) DeleteAll(ctx context.Context, exec boil.ContextExecutor) (int64, error) {
	if len(o) == 0 {
		return 0, nil
	}

	if len(changeBeforeDeleteHooks) != 0 {
		for _, obj := range o {
			if err := obj.doBeforeDeleteHooks(ctx, exec); err != nil {
				return 0, err
			}
		}
	}

	var args []interface{}
	for _, obj := range o {
		pkeyArgs := queries.ValuesFromMapping(reflect.Indirect(reflect.ValueOf(obj)), changePrimaryKeyMapping)
		args = append(args, pkeyArgs...)
	}

	sql := "DELETE FROM \"endorsements\".\"changes\" WHERE " +
		strmangle.WhereClauseRepeated(string(dialect.LQ), string(dialect.RQ), 1, changePrimaryKeyColumns, len(o))

	if boil.IsDebug(ctx) {
		writer := boil.DebugWriterFrom(ctx)
		fmt.Fprintln(writer, sql)
		fmt.Fprintln(writer, args)
	}
	result, err := exec.ExecContext(ctx, sql, args...)
	if err != nil {
		return 0, errors.Wrap(err, "endorsementapp: unable to delete all from change slice")
	}

	rowsAff, err := result.RowsAffected()
	if err != nil {
		return 0, errors.Wrap(err, "endorsementapp: failed to get rows affected by deleteall for changes")
	}

	if len(changeAfterDeleteHooks) != 0 {
		for _, obj := range o {
			if err := obj.doAfterDeleteHooks(ctx, exec); err != nil {
				return 0, err
			}
		}
	}

	return rowsAff, nil
}

// Reload refetches the object from the database
// using the primary keys with an executor.
func (o *Change) Reload(ctx context.Context, exec boil.ContextExecutor) error {
	ret, err := FindChange(ctx, exec, o.ID)
	if err != nil {
		return err
	}

	*o = *ret
	return nil
}

// ReloadAll refetches every row with matching primary key column values
// and overwrites the original object slice with the newly updated slice.
func (o *ChangeSlice) ReloadAll(ctx context.Context, exec boil.ContextExecutor) error {
	if o == nil || len(*o) == 0 {
		return nil
	}

	slice := ChangeSlice{}
	var args []interface{}
	for _, obj := range *o {
		pkeyArgs := queries.ValuesFromMapping(reflect.Indirect(reflect.ValueOf(obj)), changePrimaryKeyMapping)
		args = append(args, pkeyArgs...)
	}

	sql := "SELECT \"endorsements\".\"changes\".* FROM \"endorsements\".\"changes\" WHERE " +
		strmangle.WhereClauseRepeated(string(dialect.LQ), string(dialect.RQ), 1, changePrimaryKeyColumns, len(*o))

	q := queries.Raw(sql, args...)

	err := q.Bind(ctx, exec, &slice)
	if err != nil {
		return errors.Wrap(err, "endorsementapp: unable to reload all in ChangeSlice")
	}

	*o = slice

	return nil
}

// ChangeExists checks if the Change row exists.
func ChangeExists(ctx context.Context, exec boil.ContextExecutor, iD string) (bool, error) {
	var exists bool
	sql := "select exists(select 1 from \"endorsements\".\"changes\" where \"id\"=$1 limit 1)"

	if boil.IsDebug(ctx) {
		writer := boil.DebugWriterFrom(ctx)
		fmt.Fprintln(writer, sql)
		fmt.Fprintln(writer, iD)
	}
	row := exec.QueryRowContext(ctx, sql, iD)

	err := row.Scan(&exists)
	if err != nil {
		return false, errors.Wrap(err, "endorsementapp: unable to check if changes exists")
	}

	return exists, nil
}
