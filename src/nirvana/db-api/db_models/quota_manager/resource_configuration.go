// Code generated by SQLBoiler (https://github.com/volatiletech/sqlboiler). DO NOT EDIT.
// This file is meant to be re-generated in place and/or deleted at any time.

package quota_manager

import (
	"context"
	"database/sql"
	"fmt"
	"reflect"
	"strconv"
	"strings"
	"sync"
	"time"

	"github.com/friendsofgo/errors"
	"github.com/volatiletech/sqlboiler/v4/boil"
	"github.com/volatiletech/sqlboiler/v4/queries"
	"github.com/volatiletech/sqlboiler/v4/queries/qm"
	"github.com/volatiletech/sqlboiler/v4/queries/qmhelper"
	"github.com/volatiletech/sqlboiler/v4/types"
	"github.com/volatiletech/strmangle"
)

// ResourceConfiguration is an object representing the database table.
type ResourceConfiguration struct {
	ID                  string     `boil:"id" json:"id" toml:"id" yaml:"id"`
	ResourceKey         string     `boil:"resource_key" json:"resource_key" toml:"resource_key" yaml:"resource_key"`
	RefillStrategy      string     `boil:"refill_strategy" json:"refill_strategy" toml:"refill_strategy" yaml:"refill_strategy"`
	RefillConfiguration types.JSON `boil:"refill_configuration" json:"refill_configuration" toml:"refill_configuration" yaml:"refill_configuration"`

	R *resourceConfigurationR `boil:"" json:"" toml:"" yaml:""`
	L resourceConfigurationL  `boil:"-" json:"-" toml:"-" yaml:"-"`
}

var ResourceConfigurationColumns = struct {
	ID                  string
	ResourceKey         string
	RefillStrategy      string
	RefillConfiguration string
}{
	ID:                  "id",
	ResourceKey:         "resource_key",
	RefillStrategy:      "refill_strategy",
	RefillConfiguration: "refill_configuration",
}

var ResourceConfigurationTableColumns = struct {
	ID                  string
	ResourceKey         string
	RefillStrategy      string
	RefillConfiguration string
}{
	ID:                  "resource_configuration.id",
	ResourceKey:         "resource_configuration.resource_key",
	RefillStrategy:      "resource_configuration.refill_strategy",
	RefillConfiguration: "resource_configuration.refill_configuration",
}

// Generated where

type whereHelperstring struct{ field string }

func (w whereHelperstring) EQ(x string) qm.QueryMod  { return qmhelper.Where(w.field, qmhelper.EQ, x) }
func (w whereHelperstring) NEQ(x string) qm.QueryMod { return qmhelper.Where(w.field, qmhelper.NEQ, x) }
func (w whereHelperstring) LT(x string) qm.QueryMod  { return qmhelper.Where(w.field, qmhelper.LT, x) }
func (w whereHelperstring) LTE(x string) qm.QueryMod { return qmhelper.Where(w.field, qmhelper.LTE, x) }
func (w whereHelperstring) GT(x string) qm.QueryMod  { return qmhelper.Where(w.field, qmhelper.GT, x) }
func (w whereHelperstring) GTE(x string) qm.QueryMod { return qmhelper.Where(w.field, qmhelper.GTE, x) }
func (w whereHelperstring) IN(slice []string) qm.QueryMod {
	values := make([]interface{}, 0, len(slice))
	for _, value := range slice {
		values = append(values, value)
	}
	return qm.WhereIn(fmt.Sprintf("%s IN ?", w.field), values...)
}
func (w whereHelperstring) NIN(slice []string) qm.QueryMod {
	values := make([]interface{}, 0, len(slice))
	for _, value := range slice {
		values = append(values, value)
	}
	return qm.WhereNotIn(fmt.Sprintf("%s NOT IN ?", w.field), values...)
}

type whereHelpertypes_JSON struct{ field string }

func (w whereHelpertypes_JSON) EQ(x types.JSON) qm.QueryMod {
	return qmhelper.Where(w.field, qmhelper.EQ, x)
}
func (w whereHelpertypes_JSON) NEQ(x types.JSON) qm.QueryMod {
	return qmhelper.Where(w.field, qmhelper.NEQ, x)
}
func (w whereHelpertypes_JSON) LT(x types.JSON) qm.QueryMod {
	return qmhelper.Where(w.field, qmhelper.LT, x)
}
func (w whereHelpertypes_JSON) LTE(x types.JSON) qm.QueryMod {
	return qmhelper.Where(w.field, qmhelper.LTE, x)
}
func (w whereHelpertypes_JSON) GT(x types.JSON) qm.QueryMod {
	return qmhelper.Where(w.field, qmhelper.GT, x)
}
func (w whereHelpertypes_JSON) GTE(x types.JSON) qm.QueryMod {
	return qmhelper.Where(w.field, qmhelper.GTE, x)
}

var ResourceConfigurationWhere = struct {
	ID                  whereHelperstring
	ResourceKey         whereHelperstring
	RefillStrategy      whereHelperstring
	RefillConfiguration whereHelpertypes_JSON
}{
	ID:                  whereHelperstring{field: "\"quota_manager\".\"resource_configuration\".\"id\""},
	ResourceKey:         whereHelperstring{field: "\"quota_manager\".\"resource_configuration\".\"resource_key\""},
	RefillStrategy:      whereHelperstring{field: "\"quota_manager\".\"resource_configuration\".\"refill_strategy\""},
	RefillConfiguration: whereHelpertypes_JSON{field: "\"quota_manager\".\"resource_configuration\".\"refill_configuration\""},
}

// ResourceConfigurationRels is where relationship names are stored.
var ResourceConfigurationRels = struct {
}{}

// resourceConfigurationR is where relationships are stored.
type resourceConfigurationR struct {
}

// NewStruct creates a new relationship struct
func (*resourceConfigurationR) NewStruct() *resourceConfigurationR {
	return &resourceConfigurationR{}
}

// resourceConfigurationL is where Load methods for each relationship are stored.
type resourceConfigurationL struct{}

var (
	resourceConfigurationAllColumns            = []string{"id", "resource_key", "refill_strategy", "refill_configuration"}
	resourceConfigurationColumnsWithoutDefault = []string{"id", "resource_key", "refill_strategy", "refill_configuration"}
	resourceConfigurationColumnsWithDefault    = []string{}
	resourceConfigurationPrimaryKeyColumns     = []string{"id"}
	resourceConfigurationGeneratedColumns      = []string{}
)

type (
	// ResourceConfigurationSlice is an alias for a slice of pointers to ResourceConfiguration.
	// This should almost always be used instead of []ResourceConfiguration.
	ResourceConfigurationSlice []*ResourceConfiguration
	// ResourceConfigurationHook is the signature for custom ResourceConfiguration hook methods
	ResourceConfigurationHook func(context.Context, boil.ContextExecutor, *ResourceConfiguration) error

	resourceConfigurationQuery struct {
		*queries.Query
	}
)

// Cache for insert, update and upsert
var (
	resourceConfigurationType                 = reflect.TypeOf(&ResourceConfiguration{})
	resourceConfigurationMapping              = queries.MakeStructMapping(resourceConfigurationType)
	resourceConfigurationPrimaryKeyMapping, _ = queries.BindMapping(resourceConfigurationType, resourceConfigurationMapping, resourceConfigurationPrimaryKeyColumns)
	resourceConfigurationInsertCacheMut       sync.RWMutex
	resourceConfigurationInsertCache          = make(map[string]insertCache)
	resourceConfigurationUpdateCacheMut       sync.RWMutex
	resourceConfigurationUpdateCache          = make(map[string]updateCache)
	resourceConfigurationUpsertCacheMut       sync.RWMutex
	resourceConfigurationUpsertCache          = make(map[string]insertCache)
)

var (
	// Force time package dependency for automated UpdatedAt/CreatedAt.
	_ = time.Second
	// Force qmhelper dependency for where clause generation (which doesn't
	// always happen)
	_ = qmhelper.Where
)

var resourceConfigurationAfterSelectHooks []ResourceConfigurationHook

var resourceConfigurationBeforeInsertHooks []ResourceConfigurationHook
var resourceConfigurationAfterInsertHooks []ResourceConfigurationHook

var resourceConfigurationBeforeUpdateHooks []ResourceConfigurationHook
var resourceConfigurationAfterUpdateHooks []ResourceConfigurationHook

var resourceConfigurationBeforeDeleteHooks []ResourceConfigurationHook
var resourceConfigurationAfterDeleteHooks []ResourceConfigurationHook

var resourceConfigurationBeforeUpsertHooks []ResourceConfigurationHook
var resourceConfigurationAfterUpsertHooks []ResourceConfigurationHook

// doAfterSelectHooks executes all "after Select" hooks.
func (o *ResourceConfiguration) doAfterSelectHooks(ctx context.Context, exec boil.ContextExecutor) (err error) {
	if boil.HooksAreSkipped(ctx) {
		return nil
	}

	for _, hook := range resourceConfigurationAfterSelectHooks {
		if err := hook(ctx, exec, o); err != nil {
			return err
		}
	}

	return nil
}

// doBeforeInsertHooks executes all "before insert" hooks.
func (o *ResourceConfiguration) doBeforeInsertHooks(ctx context.Context, exec boil.ContextExecutor) (err error) {
	if boil.HooksAreSkipped(ctx) {
		return nil
	}

	for _, hook := range resourceConfigurationBeforeInsertHooks {
		if err := hook(ctx, exec, o); err != nil {
			return err
		}
	}

	return nil
}

// doAfterInsertHooks executes all "after Insert" hooks.
func (o *ResourceConfiguration) doAfterInsertHooks(ctx context.Context, exec boil.ContextExecutor) (err error) {
	if boil.HooksAreSkipped(ctx) {
		return nil
	}

	for _, hook := range resourceConfigurationAfterInsertHooks {
		if err := hook(ctx, exec, o); err != nil {
			return err
		}
	}

	return nil
}

// doBeforeUpdateHooks executes all "before Update" hooks.
func (o *ResourceConfiguration) doBeforeUpdateHooks(ctx context.Context, exec boil.ContextExecutor) (err error) {
	if boil.HooksAreSkipped(ctx) {
		return nil
	}

	for _, hook := range resourceConfigurationBeforeUpdateHooks {
		if err := hook(ctx, exec, o); err != nil {
			return err
		}
	}

	return nil
}

// doAfterUpdateHooks executes all "after Update" hooks.
func (o *ResourceConfiguration) doAfterUpdateHooks(ctx context.Context, exec boil.ContextExecutor) (err error) {
	if boil.HooksAreSkipped(ctx) {
		return nil
	}

	for _, hook := range resourceConfigurationAfterUpdateHooks {
		if err := hook(ctx, exec, o); err != nil {
			return err
		}
	}

	return nil
}

// doBeforeDeleteHooks executes all "before Delete" hooks.
func (o *ResourceConfiguration) doBeforeDeleteHooks(ctx context.Context, exec boil.ContextExecutor) (err error) {
	if boil.HooksAreSkipped(ctx) {
		return nil
	}

	for _, hook := range resourceConfigurationBeforeDeleteHooks {
		if err := hook(ctx, exec, o); err != nil {
			return err
		}
	}

	return nil
}

// doAfterDeleteHooks executes all "after Delete" hooks.
func (o *ResourceConfiguration) doAfterDeleteHooks(ctx context.Context, exec boil.ContextExecutor) (err error) {
	if boil.HooksAreSkipped(ctx) {
		return nil
	}

	for _, hook := range resourceConfigurationAfterDeleteHooks {
		if err := hook(ctx, exec, o); err != nil {
			return err
		}
	}

	return nil
}

// doBeforeUpsertHooks executes all "before Upsert" hooks.
func (o *ResourceConfiguration) doBeforeUpsertHooks(ctx context.Context, exec boil.ContextExecutor) (err error) {
	if boil.HooksAreSkipped(ctx) {
		return nil
	}

	for _, hook := range resourceConfigurationBeforeUpsertHooks {
		if err := hook(ctx, exec, o); err != nil {
			return err
		}
	}

	return nil
}

// doAfterUpsertHooks executes all "after Upsert" hooks.
func (o *ResourceConfiguration) doAfterUpsertHooks(ctx context.Context, exec boil.ContextExecutor) (err error) {
	if boil.HooksAreSkipped(ctx) {
		return nil
	}

	for _, hook := range resourceConfigurationAfterUpsertHooks {
		if err := hook(ctx, exec, o); err != nil {
			return err
		}
	}

	return nil
}

// AddResourceConfigurationHook registers your hook function for all future operations.
func AddResourceConfigurationHook(hookPoint boil.HookPoint, resourceConfigurationHook ResourceConfigurationHook) {
	switch hookPoint {
	case boil.AfterSelectHook:
		resourceConfigurationAfterSelectHooks = append(resourceConfigurationAfterSelectHooks, resourceConfigurationHook)
	case boil.BeforeInsertHook:
		resourceConfigurationBeforeInsertHooks = append(resourceConfigurationBeforeInsertHooks, resourceConfigurationHook)
	case boil.AfterInsertHook:
		resourceConfigurationAfterInsertHooks = append(resourceConfigurationAfterInsertHooks, resourceConfigurationHook)
	case boil.BeforeUpdateHook:
		resourceConfigurationBeforeUpdateHooks = append(resourceConfigurationBeforeUpdateHooks, resourceConfigurationHook)
	case boil.AfterUpdateHook:
		resourceConfigurationAfterUpdateHooks = append(resourceConfigurationAfterUpdateHooks, resourceConfigurationHook)
	case boil.BeforeDeleteHook:
		resourceConfigurationBeforeDeleteHooks = append(resourceConfigurationBeforeDeleteHooks, resourceConfigurationHook)
	case boil.AfterDeleteHook:
		resourceConfigurationAfterDeleteHooks = append(resourceConfigurationAfterDeleteHooks, resourceConfigurationHook)
	case boil.BeforeUpsertHook:
		resourceConfigurationBeforeUpsertHooks = append(resourceConfigurationBeforeUpsertHooks, resourceConfigurationHook)
	case boil.AfterUpsertHook:
		resourceConfigurationAfterUpsertHooks = append(resourceConfigurationAfterUpsertHooks, resourceConfigurationHook)
	}
}

// One returns a single resourceConfiguration record from the query.
func (q resourceConfigurationQuery) One(ctx context.Context, exec boil.ContextExecutor) (*ResourceConfiguration, error) {
	o := &ResourceConfiguration{}

	queries.SetLimit(q.Query, 1)

	err := q.Bind(ctx, exec, o)
	if err != nil {
		if errors.Is(err, sql.ErrNoRows) {
			return nil, sql.ErrNoRows
		}
		return nil, errors.Wrap(err, "quota_manager: failed to execute a one query for resource_configuration")
	}

	if err := o.doAfterSelectHooks(ctx, exec); err != nil {
		return o, err
	}

	return o, nil
}

// All returns all ResourceConfiguration records from the query.
func (q resourceConfigurationQuery) All(ctx context.Context, exec boil.ContextExecutor) (ResourceConfigurationSlice, error) {
	var o []*ResourceConfiguration

	err := q.Bind(ctx, exec, &o)
	if err != nil {
		return nil, errors.Wrap(err, "quota_manager: failed to assign all query results to ResourceConfiguration slice")
	}

	if len(resourceConfigurationAfterSelectHooks) != 0 {
		for _, obj := range o {
			if err := obj.doAfterSelectHooks(ctx, exec); err != nil {
				return o, err
			}
		}
	}

	return o, nil
}

// Count returns the count of all ResourceConfiguration records in the query.
func (q resourceConfigurationQuery) Count(ctx context.Context, exec boil.ContextExecutor) (int64, error) {
	var count int64

	queries.SetSelect(q.Query, nil)
	queries.SetCount(q.Query)

	err := q.Query.QueryRowContext(ctx, exec).Scan(&count)
	if err != nil {
		return 0, errors.Wrap(err, "quota_manager: failed to count resource_configuration rows")
	}

	return count, nil
}

// Exists checks if the row exists in the table.
func (q resourceConfigurationQuery) Exists(ctx context.Context, exec boil.ContextExecutor) (bool, error) {
	var count int64

	queries.SetSelect(q.Query, nil)
	queries.SetCount(q.Query)
	queries.SetLimit(q.Query, 1)

	err := q.Query.QueryRowContext(ctx, exec).Scan(&count)
	if err != nil {
		return false, errors.Wrap(err, "quota_manager: failed to check if resource_configuration exists")
	}

	return count > 0, nil
}

// ResourceConfigurations retrieves all the records using an executor.
func ResourceConfigurations(mods ...qm.QueryMod) resourceConfigurationQuery {
	mods = append(mods, qm.From("\"quota_manager\".\"resource_configuration\""))
	q := NewQuery(mods...)
	if len(queries.GetSelect(q)) == 0 {
		queries.SetSelect(q, []string{"\"quota_manager\".\"resource_configuration\".*"})
	}

	return resourceConfigurationQuery{q}
}

// FindResourceConfiguration retrieves a single record by ID with an executor.
// If selectCols is empty Find will return all columns.
func FindResourceConfiguration(ctx context.Context, exec boil.ContextExecutor, iD string, selectCols ...string) (*ResourceConfiguration, error) {
	resourceConfigurationObj := &ResourceConfiguration{}

	sel := "*"
	if len(selectCols) > 0 {
		sel = strings.Join(strmangle.IdentQuoteSlice(dialect.LQ, dialect.RQ, selectCols), ",")
	}
	query := fmt.Sprintf(
		"select %s from \"quota_manager\".\"resource_configuration\" where \"id\"=$1", sel,
	)

	q := queries.Raw(query, iD)

	err := q.Bind(ctx, exec, resourceConfigurationObj)
	if err != nil {
		if errors.Is(err, sql.ErrNoRows) {
			return nil, sql.ErrNoRows
		}
		return nil, errors.Wrap(err, "quota_manager: unable to select from resource_configuration")
	}

	if err = resourceConfigurationObj.doAfterSelectHooks(ctx, exec); err != nil {
		return resourceConfigurationObj, err
	}

	return resourceConfigurationObj, nil
}

// Insert a single record using an executor.
// See boil.Columns.InsertColumnSet documentation to understand column list inference for inserts.
func (o *ResourceConfiguration) Insert(ctx context.Context, exec boil.ContextExecutor, columns boil.Columns) error {
	if o == nil {
		return errors.New("quota_manager: no resource_configuration provided for insertion")
	}

	var err error

	if err := o.doBeforeInsertHooks(ctx, exec); err != nil {
		return err
	}

	nzDefaults := queries.NonZeroDefaultSet(resourceConfigurationColumnsWithDefault, o)

	key := makeCacheKey(columns, nzDefaults)
	resourceConfigurationInsertCacheMut.RLock()
	cache, cached := resourceConfigurationInsertCache[key]
	resourceConfigurationInsertCacheMut.RUnlock()

	if !cached {
		wl, returnColumns := columns.InsertColumnSet(
			resourceConfigurationAllColumns,
			resourceConfigurationColumnsWithDefault,
			resourceConfigurationColumnsWithoutDefault,
			nzDefaults,
		)

		cache.valueMapping, err = queries.BindMapping(resourceConfigurationType, resourceConfigurationMapping, wl)
		if err != nil {
			return err
		}
		cache.retMapping, err = queries.BindMapping(resourceConfigurationType, resourceConfigurationMapping, returnColumns)
		if err != nil {
			return err
		}
		if len(wl) != 0 {
			cache.query = fmt.Sprintf("INSERT INTO \"quota_manager\".\"resource_configuration\" (\"%s\") %%sVALUES (%s)%%s", strings.Join(wl, "\",\""), strmangle.Placeholders(dialect.UseIndexPlaceholders, len(wl), 1, 1))
		} else {
			cache.query = "INSERT INTO \"quota_manager\".\"resource_configuration\" %sDEFAULT VALUES%s"
		}

		var queryOutput, queryReturning string

		if len(cache.retMapping) != 0 {
			queryReturning = fmt.Sprintf(" RETURNING \"%s\"", strings.Join(returnColumns, "\",\""))
		}

		cache.query = fmt.Sprintf(cache.query, queryOutput, queryReturning)
	}

	value := reflect.Indirect(reflect.ValueOf(o))
	vals := queries.ValuesFromMapping(value, cache.valueMapping)

	if boil.IsDebug(ctx) {
		writer := boil.DebugWriterFrom(ctx)
		fmt.Fprintln(writer, cache.query)
		fmt.Fprintln(writer, vals)
	}

	if len(cache.retMapping) != 0 {
		err = exec.QueryRowContext(ctx, cache.query, vals...).Scan(queries.PtrsFromMapping(value, cache.retMapping)...)
	} else {
		_, err = exec.ExecContext(ctx, cache.query, vals...)
	}

	if err != nil {
		return errors.Wrap(err, "quota_manager: unable to insert into resource_configuration")
	}

	if !cached {
		resourceConfigurationInsertCacheMut.Lock()
		resourceConfigurationInsertCache[key] = cache
		resourceConfigurationInsertCacheMut.Unlock()
	}

	return o.doAfterInsertHooks(ctx, exec)
}

// Update uses an executor to update the ResourceConfiguration.
// See boil.Columns.UpdateColumnSet documentation to understand column list inference for updates.
// Update does not automatically update the record in case of default values. Use .Reload() to refresh the records.
func (o *ResourceConfiguration) Update(ctx context.Context, exec boil.ContextExecutor, columns boil.Columns) (int64, error) {
	var err error
	if err = o.doBeforeUpdateHooks(ctx, exec); err != nil {
		return 0, err
	}
	key := makeCacheKey(columns, nil)
	resourceConfigurationUpdateCacheMut.RLock()
	cache, cached := resourceConfigurationUpdateCache[key]
	resourceConfigurationUpdateCacheMut.RUnlock()

	if !cached {
		wl := columns.UpdateColumnSet(
			resourceConfigurationAllColumns,
			resourceConfigurationPrimaryKeyColumns,
		)

		if !columns.IsWhitelist() {
			wl = strmangle.SetComplement(wl, []string{"created_at"})
		}
		if len(wl) == 0 {
			return 0, errors.New("quota_manager: unable to update resource_configuration, could not build whitelist")
		}

		cache.query = fmt.Sprintf("UPDATE \"quota_manager\".\"resource_configuration\" SET %s WHERE %s",
			strmangle.SetParamNames("\"", "\"", 1, wl),
			strmangle.WhereClause("\"", "\"", len(wl)+1, resourceConfigurationPrimaryKeyColumns),
		)
		cache.valueMapping, err = queries.BindMapping(resourceConfigurationType, resourceConfigurationMapping, append(wl, resourceConfigurationPrimaryKeyColumns...))
		if err != nil {
			return 0, err
		}
	}

	values := queries.ValuesFromMapping(reflect.Indirect(reflect.ValueOf(o)), cache.valueMapping)

	if boil.IsDebug(ctx) {
		writer := boil.DebugWriterFrom(ctx)
		fmt.Fprintln(writer, cache.query)
		fmt.Fprintln(writer, values)
	}
	var result sql.Result
	result, err = exec.ExecContext(ctx, cache.query, values...)
	if err != nil {
		return 0, errors.Wrap(err, "quota_manager: unable to update resource_configuration row")
	}

	rowsAff, err := result.RowsAffected()
	if err != nil {
		return 0, errors.Wrap(err, "quota_manager: failed to get rows affected by update for resource_configuration")
	}

	if !cached {
		resourceConfigurationUpdateCacheMut.Lock()
		resourceConfigurationUpdateCache[key] = cache
		resourceConfigurationUpdateCacheMut.Unlock()
	}

	return rowsAff, o.doAfterUpdateHooks(ctx, exec)
}

// UpdateAll updates all rows with the specified column values.
func (q resourceConfigurationQuery) UpdateAll(ctx context.Context, exec boil.ContextExecutor, cols M) (int64, error) {
	queries.SetUpdate(q.Query, cols)

	result, err := q.Query.ExecContext(ctx, exec)
	if err != nil {
		return 0, errors.Wrap(err, "quota_manager: unable to update all for resource_configuration")
	}

	rowsAff, err := result.RowsAffected()
	if err != nil {
		return 0, errors.Wrap(err, "quota_manager: unable to retrieve rows affected for resource_configuration")
	}

	return rowsAff, nil
}

// UpdateAll updates all rows with the specified column values, using an executor.
func (o ResourceConfigurationSlice) UpdateAll(ctx context.Context, exec boil.ContextExecutor, cols M) (int64, error) {
	ln := int64(len(o))
	if ln == 0 {
		return 0, nil
	}

	if len(cols) == 0 {
		return 0, errors.New("quota_manager: update all requires at least one column argument")
	}

	colNames := make([]string, len(cols))
	args := make([]interface{}, len(cols))

	i := 0
	for name, value := range cols {
		colNames[i] = name
		args[i] = value
		i++
	}

	// Append all of the primary key values for each column
	for _, obj := range o {
		pkeyArgs := queries.ValuesFromMapping(reflect.Indirect(reflect.ValueOf(obj)), resourceConfigurationPrimaryKeyMapping)
		args = append(args, pkeyArgs...)
	}

	sql := fmt.Sprintf("UPDATE \"quota_manager\".\"resource_configuration\" SET %s WHERE %s",
		strmangle.SetParamNames("\"", "\"", 1, colNames),
		strmangle.WhereClauseRepeated(string(dialect.LQ), string(dialect.RQ), len(colNames)+1, resourceConfigurationPrimaryKeyColumns, len(o)))

	if boil.IsDebug(ctx) {
		writer := boil.DebugWriterFrom(ctx)
		fmt.Fprintln(writer, sql)
		fmt.Fprintln(writer, args...)
	}
	result, err := exec.ExecContext(ctx, sql, args...)
	if err != nil {
		return 0, errors.Wrap(err, "quota_manager: unable to update all in resourceConfiguration slice")
	}

	rowsAff, err := result.RowsAffected()
	if err != nil {
		return 0, errors.Wrap(err, "quota_manager: unable to retrieve rows affected all in update all resourceConfiguration")
	}
	return rowsAff, nil
}

// Upsert attempts an insert using an executor, and does an update or ignore on conflict.
// See boil.Columns documentation for how to properly use updateColumns and insertColumns.
func (o *ResourceConfiguration) Upsert(ctx context.Context, exec boil.ContextExecutor, updateOnConflict bool, conflictColumns []string, updateColumns, insertColumns boil.Columns) error {
	if o == nil {
		return errors.New("quota_manager: no resource_configuration provided for upsert")
	}

	if err := o.doBeforeUpsertHooks(ctx, exec); err != nil {
		return err
	}

	nzDefaults := queries.NonZeroDefaultSet(resourceConfigurationColumnsWithDefault, o)

	// Build cache key in-line uglily - mysql vs psql problems
	buf := strmangle.GetBuffer()
	if updateOnConflict {
		buf.WriteByte('t')
	} else {
		buf.WriteByte('f')
	}
	buf.WriteByte('.')
	for _, c := range conflictColumns {
		buf.WriteString(c)
	}
	buf.WriteByte('.')
	buf.WriteString(strconv.Itoa(updateColumns.Kind))
	for _, c := range updateColumns.Cols {
		buf.WriteString(c)
	}
	buf.WriteByte('.')
	buf.WriteString(strconv.Itoa(insertColumns.Kind))
	for _, c := range insertColumns.Cols {
		buf.WriteString(c)
	}
	buf.WriteByte('.')
	for _, c := range nzDefaults {
		buf.WriteString(c)
	}
	key := buf.String()
	strmangle.PutBuffer(buf)

	resourceConfigurationUpsertCacheMut.RLock()
	cache, cached := resourceConfigurationUpsertCache[key]
	resourceConfigurationUpsertCacheMut.RUnlock()

	var err error

	if !cached {
		insert, ret := insertColumns.InsertColumnSet(
			resourceConfigurationAllColumns,
			resourceConfigurationColumnsWithDefault,
			resourceConfigurationColumnsWithoutDefault,
			nzDefaults,
		)

		update := updateColumns.UpdateColumnSet(
			resourceConfigurationAllColumns,
			resourceConfigurationPrimaryKeyColumns,
		)

		if updateOnConflict && len(update) == 0 {
			return errors.New("quota_manager: unable to upsert resource_configuration, could not build update column list")
		}

		conflict := conflictColumns
		if len(conflict) == 0 {
			conflict = make([]string, len(resourceConfigurationPrimaryKeyColumns))
			copy(conflict, resourceConfigurationPrimaryKeyColumns)
		}
		cache.query = buildUpsertQueryPostgres(dialect, "\"quota_manager\".\"resource_configuration\"", updateOnConflict, ret, update, conflict, insert)

		cache.valueMapping, err = queries.BindMapping(resourceConfigurationType, resourceConfigurationMapping, insert)
		if err != nil {
			return err
		}
		if len(ret) != 0 {
			cache.retMapping, err = queries.BindMapping(resourceConfigurationType, resourceConfigurationMapping, ret)
			if err != nil {
				return err
			}
		}
	}

	value := reflect.Indirect(reflect.ValueOf(o))
	vals := queries.ValuesFromMapping(value, cache.valueMapping)
	var returns []interface{}
	if len(cache.retMapping) != 0 {
		returns = queries.PtrsFromMapping(value, cache.retMapping)
	}

	if boil.IsDebug(ctx) {
		writer := boil.DebugWriterFrom(ctx)
		fmt.Fprintln(writer, cache.query)
		fmt.Fprintln(writer, vals)
	}
	if len(cache.retMapping) != 0 {
		err = exec.QueryRowContext(ctx, cache.query, vals...).Scan(returns...)
		if errors.Is(err, sql.ErrNoRows) {
			err = nil // Postgres doesn't return anything when there's no update
		}
	} else {
		_, err = exec.ExecContext(ctx, cache.query, vals...)
	}
	if err != nil {
		return errors.Wrap(err, "quota_manager: unable to upsert resource_configuration")
	}

	if !cached {
		resourceConfigurationUpsertCacheMut.Lock()
		resourceConfigurationUpsertCache[key] = cache
		resourceConfigurationUpsertCacheMut.Unlock()
	}

	return o.doAfterUpsertHooks(ctx, exec)
}

// Delete deletes a single ResourceConfiguration record with an executor.
// Delete will match against the primary key column to find the record to delete.
func (o *ResourceConfiguration) Delete(ctx context.Context, exec boil.ContextExecutor) (int64, error) {
	if o == nil {
		return 0, errors.New("quota_manager: no ResourceConfiguration provided for delete")
	}

	if err := o.doBeforeDeleteHooks(ctx, exec); err != nil {
		return 0, err
	}

	args := queries.ValuesFromMapping(reflect.Indirect(reflect.ValueOf(o)), resourceConfigurationPrimaryKeyMapping)
	sql := "DELETE FROM \"quota_manager\".\"resource_configuration\" WHERE \"id\"=$1"

	if boil.IsDebug(ctx) {
		writer := boil.DebugWriterFrom(ctx)
		fmt.Fprintln(writer, sql)
		fmt.Fprintln(writer, args...)
	}
	result, err := exec.ExecContext(ctx, sql, args...)
	if err != nil {
		return 0, errors.Wrap(err, "quota_manager: unable to delete from resource_configuration")
	}

	rowsAff, err := result.RowsAffected()
	if err != nil {
		return 0, errors.Wrap(err, "quota_manager: failed to get rows affected by delete for resource_configuration")
	}

	if err := o.doAfterDeleteHooks(ctx, exec); err != nil {
		return 0, err
	}

	return rowsAff, nil
}

// DeleteAll deletes all matching rows.
func (q resourceConfigurationQuery) DeleteAll(ctx context.Context, exec boil.ContextExecutor) (int64, error) {
	if q.Query == nil {
		return 0, errors.New("quota_manager: no resourceConfigurationQuery provided for delete all")
	}

	queries.SetDelete(q.Query)

	result, err := q.Query.ExecContext(ctx, exec)
	if err != nil {
		return 0, errors.Wrap(err, "quota_manager: unable to delete all from resource_configuration")
	}

	rowsAff, err := result.RowsAffected()
	if err != nil {
		return 0, errors.Wrap(err, "quota_manager: failed to get rows affected by deleteall for resource_configuration")
	}

	return rowsAff, nil
}

// DeleteAll deletes all rows in the slice, using an executor.
func (o ResourceConfigurationSlice) DeleteAll(ctx context.Context, exec boil.ContextExecutor) (int64, error) {
	if len(o) == 0 {
		return 0, nil
	}

	if len(resourceConfigurationBeforeDeleteHooks) != 0 {
		for _, obj := range o {
			if err := obj.doBeforeDeleteHooks(ctx, exec); err != nil {
				return 0, err
			}
		}
	}

	var args []interface{}
	for _, obj := range o {
		pkeyArgs := queries.ValuesFromMapping(reflect.Indirect(reflect.ValueOf(obj)), resourceConfigurationPrimaryKeyMapping)
		args = append(args, pkeyArgs...)
	}

	sql := "DELETE FROM \"quota_manager\".\"resource_configuration\" WHERE " +
		strmangle.WhereClauseRepeated(string(dialect.LQ), string(dialect.RQ), 1, resourceConfigurationPrimaryKeyColumns, len(o))

	if boil.IsDebug(ctx) {
		writer := boil.DebugWriterFrom(ctx)
		fmt.Fprintln(writer, sql)
		fmt.Fprintln(writer, args)
	}
	result, err := exec.ExecContext(ctx, sql, args...)
	if err != nil {
		return 0, errors.Wrap(err, "quota_manager: unable to delete all from resourceConfiguration slice")
	}

	rowsAff, err := result.RowsAffected()
	if err != nil {
		return 0, errors.Wrap(err, "quota_manager: failed to get rows affected by deleteall for resource_configuration")
	}

	if len(resourceConfigurationAfterDeleteHooks) != 0 {
		for _, obj := range o {
			if err := obj.doAfterDeleteHooks(ctx, exec); err != nil {
				return 0, err
			}
		}
	}

	return rowsAff, nil
}

// Reload refetches the object from the database
// using the primary keys with an executor.
func (o *ResourceConfiguration) Reload(ctx context.Context, exec boil.ContextExecutor) error {
	ret, err := FindResourceConfiguration(ctx, exec, o.ID)
	if err != nil {
		return err
	}

	*o = *ret
	return nil
}

// ReloadAll refetches every row with matching primary key column values
// and overwrites the original object slice with the newly updated slice.
func (o *ResourceConfigurationSlice) ReloadAll(ctx context.Context, exec boil.ContextExecutor) error {
	if o == nil || len(*o) == 0 {
		return nil
	}

	slice := ResourceConfigurationSlice{}
	var args []interface{}
	for _, obj := range *o {
		pkeyArgs := queries.ValuesFromMapping(reflect.Indirect(reflect.ValueOf(obj)), resourceConfigurationPrimaryKeyMapping)
		args = append(args, pkeyArgs...)
	}

	sql := "SELECT \"quota_manager\".\"resource_configuration\".* FROM \"quota_manager\".\"resource_configuration\" WHERE " +
		strmangle.WhereClauseRepeated(string(dialect.LQ), string(dialect.RQ), 1, resourceConfigurationPrimaryKeyColumns, len(*o))

	q := queries.Raw(sql, args...)

	err := q.Bind(ctx, exec, &slice)
	if err != nil {
		return errors.Wrap(err, "quota_manager: unable to reload all in ResourceConfigurationSlice")
	}

	*o = slice

	return nil
}

// ResourceConfigurationExists checks if the ResourceConfiguration row exists.
func ResourceConfigurationExists(ctx context.Context, exec boil.ContextExecutor, iD string) (bool, error) {
	var exists bool
	sql := "select exists(select 1 from \"quota_manager\".\"resource_configuration\" where \"id\"=$1 limit 1)"

	if boil.IsDebug(ctx) {
		writer := boil.DebugWriterFrom(ctx)
		fmt.Fprintln(writer, sql)
		fmt.Fprintln(writer, iD)
	}
	row := exec.QueryRowContext(ctx, sql, iD)

	err := row.Scan(&exists)
	if err != nil {
		return false, errors.Wrap(err, "quota_manager: unable to check if resource_configuration exists")
	}

	return exists, nil
}
