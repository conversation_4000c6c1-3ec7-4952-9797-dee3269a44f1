// Code generated by SQLBoiler (https://github.com/volatiletech/sqlboiler). DO NOT EDIT.
// This file is meant to be re-generated in place and/or deleted at any time.

package truckercloud

import (
	"context"
	"database/sql"
	"fmt"
	"reflect"
	"strconv"
	"strings"
	"sync"
	"time"

	"github.com/friendsofgo/errors"
	"github.com/volatiletech/null/v8"
	"github.com/volatiletech/sqlboiler/v4/boil"
	"github.com/volatiletech/sqlboiler/v4/queries"
	"github.com/volatiletech/sqlboiler/v4/queries/qm"
	"github.com/volatiletech/sqlboiler/v4/queries/qmhelper"
	"github.com/volatiletech/sqlboiler/v4/types"
	"github.com/volatiletech/strmangle"
)

// Vehicle is an object representing the database table.
type Vehicle struct {
	HandleID       string      `boil:"handle_id" json:"handle_id" toml:"handle_id" yaml:"handle_id"`
	AssetEldID     string      `boil:"asset_eld_id" json:"asset_eld_id" toml:"asset_eld_id" yaml:"asset_eld_id"`
	AssetID        null.String `boil:"asset_id" json:"asset_id,omitempty" toml:"asset_id" yaml:"asset_id,omitempty"`
	VehicleID      null.String `boil:"vehicle_id" json:"vehicle_id,omitempty" toml:"vehicle_id" yaml:"vehicle_id,omitempty"`
	DeviceID       null.String `boil:"device_id" json:"device_id,omitempty" toml:"device_id" yaml:"device_id,omitempty"`
	Vin            null.String `boil:"vin" json:"vin,omitempty" toml:"vin" yaml:"vin,omitempty"`
	LicensePlate   null.String `boil:"license_plate" json:"license_plate,omitempty" toml:"license_plate" yaml:"license_plate,omitempty"`
	SubscriptionID null.String `boil:"subscription_id" json:"subscription_id,omitempty" toml:"subscription_id" yaml:"subscription_id,omitempty"`
	CreatedAt      time.Time   `boil:"created_at" json:"created_at" toml:"created_at" yaml:"created_at"`
	UpdatedAt      time.Time   `boil:"updated_at" json:"updated_at" toml:"updated_at" yaml:"updated_at"`
	TSPCreatedAt   time.Time   `boil:"tsp_created_at" json:"tsp_created_at" toml:"tsp_created_at" yaml:"tsp_created_at"`
	TSPUpdatedAt   time.Time   `boil:"tsp_updated_at" json:"tsp_updated_at" toml:"tsp_updated_at" yaml:"tsp_updated_at"`
	Raw            types.JSON  `boil:"raw" json:"raw" toml:"raw" yaml:"raw"`
	Disabled       bool        `boil:"disabled" json:"disabled" toml:"disabled" yaml:"disabled"`
	DeviceType     null.String `boil:"device_type" json:"device_type,omitempty" toml:"device_type" yaml:"device_type,omitempty"`
	PlanType       null.String `boil:"plan_type" json:"plan_type,omitempty" toml:"plan_type" yaml:"plan_type,omitempty"`
	AssetType      null.String `boil:"asset_type" json:"asset_type,omitempty" toml:"asset_type" yaml:"asset_type,omitempty"`

	R *vehicleR `boil:"" json:"" toml:"" yaml:""`
	L vehicleL  `boil:"-" json:"-" toml:"-" yaml:"-"`
}

var VehicleColumns = struct {
	HandleID       string
	AssetEldID     string
	AssetID        string
	VehicleID      string
	DeviceID       string
	Vin            string
	LicensePlate   string
	SubscriptionID string
	CreatedAt      string
	UpdatedAt      string
	TSPCreatedAt   string
	TSPUpdatedAt   string
	Raw            string
	Disabled       string
	DeviceType     string
	PlanType       string
	AssetType      string
}{
	HandleID:       "handle_id",
	AssetEldID:     "asset_eld_id",
	AssetID:        "asset_id",
	VehicleID:      "vehicle_id",
	DeviceID:       "device_id",
	Vin:            "vin",
	LicensePlate:   "license_plate",
	SubscriptionID: "subscription_id",
	CreatedAt:      "created_at",
	UpdatedAt:      "updated_at",
	TSPCreatedAt:   "tsp_created_at",
	TSPUpdatedAt:   "tsp_updated_at",
	Raw:            "raw",
	Disabled:       "disabled",
	DeviceType:     "device_type",
	PlanType:       "plan_type",
	AssetType:      "asset_type",
}

var VehicleTableColumns = struct {
	HandleID       string
	AssetEldID     string
	AssetID        string
	VehicleID      string
	DeviceID       string
	Vin            string
	LicensePlate   string
	SubscriptionID string
	CreatedAt      string
	UpdatedAt      string
	TSPCreatedAt   string
	TSPUpdatedAt   string
	Raw            string
	Disabled       string
	DeviceType     string
	PlanType       string
	AssetType      string
}{
	HandleID:       "vehicles.handle_id",
	AssetEldID:     "vehicles.asset_eld_id",
	AssetID:        "vehicles.asset_id",
	VehicleID:      "vehicles.vehicle_id",
	DeviceID:       "vehicles.device_id",
	Vin:            "vehicles.vin",
	LicensePlate:   "vehicles.license_plate",
	SubscriptionID: "vehicles.subscription_id",
	CreatedAt:      "vehicles.created_at",
	UpdatedAt:      "vehicles.updated_at",
	TSPCreatedAt:   "vehicles.tsp_created_at",
	TSPUpdatedAt:   "vehicles.tsp_updated_at",
	Raw:            "vehicles.raw",
	Disabled:       "vehicles.disabled",
	DeviceType:     "vehicles.device_type",
	PlanType:       "vehicles.plan_type",
	AssetType:      "vehicles.asset_type",
}

// Generated where

type whereHelperstring struct{ field string }

func (w whereHelperstring) EQ(x string) qm.QueryMod  { return qmhelper.Where(w.field, qmhelper.EQ, x) }
func (w whereHelperstring) NEQ(x string) qm.QueryMod { return qmhelper.Where(w.field, qmhelper.NEQ, x) }
func (w whereHelperstring) LT(x string) qm.QueryMod  { return qmhelper.Where(w.field, qmhelper.LT, x) }
func (w whereHelperstring) LTE(x string) qm.QueryMod { return qmhelper.Where(w.field, qmhelper.LTE, x) }
func (w whereHelperstring) GT(x string) qm.QueryMod  { return qmhelper.Where(w.field, qmhelper.GT, x) }
func (w whereHelperstring) GTE(x string) qm.QueryMod { return qmhelper.Where(w.field, qmhelper.GTE, x) }
func (w whereHelperstring) IN(slice []string) qm.QueryMod {
	values := make([]interface{}, 0, len(slice))
	for _, value := range slice {
		values = append(values, value)
	}
	return qm.WhereIn(fmt.Sprintf("%s IN ?", w.field), values...)
}
func (w whereHelperstring) NIN(slice []string) qm.QueryMod {
	values := make([]interface{}, 0, len(slice))
	for _, value := range slice {
		values = append(values, value)
	}
	return qm.WhereNotIn(fmt.Sprintf("%s NOT IN ?", w.field), values...)
}

type whereHelpernull_String struct{ field string }

func (w whereHelpernull_String) EQ(x null.String) qm.QueryMod {
	return qmhelper.WhereNullEQ(w.field, false, x)
}
func (w whereHelpernull_String) NEQ(x null.String) qm.QueryMod {
	return qmhelper.WhereNullEQ(w.field, true, x)
}
func (w whereHelpernull_String) LT(x null.String) qm.QueryMod {
	return qmhelper.Where(w.field, qmhelper.LT, x)
}
func (w whereHelpernull_String) LTE(x null.String) qm.QueryMod {
	return qmhelper.Where(w.field, qmhelper.LTE, x)
}
func (w whereHelpernull_String) GT(x null.String) qm.QueryMod {
	return qmhelper.Where(w.field, qmhelper.GT, x)
}
func (w whereHelpernull_String) GTE(x null.String) qm.QueryMod {
	return qmhelper.Where(w.field, qmhelper.GTE, x)
}

func (w whereHelpernull_String) IsNull() qm.QueryMod    { return qmhelper.WhereIsNull(w.field) }
func (w whereHelpernull_String) IsNotNull() qm.QueryMod { return qmhelper.WhereIsNotNull(w.field) }

type whereHelpertime_Time struct{ field string }

func (w whereHelpertime_Time) EQ(x time.Time) qm.QueryMod {
	return qmhelper.Where(w.field, qmhelper.EQ, x)
}
func (w whereHelpertime_Time) NEQ(x time.Time) qm.QueryMod {
	return qmhelper.Where(w.field, qmhelper.NEQ, x)
}
func (w whereHelpertime_Time) LT(x time.Time) qm.QueryMod {
	return qmhelper.Where(w.field, qmhelper.LT, x)
}
func (w whereHelpertime_Time) LTE(x time.Time) qm.QueryMod {
	return qmhelper.Where(w.field, qmhelper.LTE, x)
}
func (w whereHelpertime_Time) GT(x time.Time) qm.QueryMod {
	return qmhelper.Where(w.field, qmhelper.GT, x)
}
func (w whereHelpertime_Time) GTE(x time.Time) qm.QueryMod {
	return qmhelper.Where(w.field, qmhelper.GTE, x)
}

type whereHelpertypes_JSON struct{ field string }

func (w whereHelpertypes_JSON) EQ(x types.JSON) qm.QueryMod {
	return qmhelper.Where(w.field, qmhelper.EQ, x)
}
func (w whereHelpertypes_JSON) NEQ(x types.JSON) qm.QueryMod {
	return qmhelper.Where(w.field, qmhelper.NEQ, x)
}
func (w whereHelpertypes_JSON) LT(x types.JSON) qm.QueryMod {
	return qmhelper.Where(w.field, qmhelper.LT, x)
}
func (w whereHelpertypes_JSON) LTE(x types.JSON) qm.QueryMod {
	return qmhelper.Where(w.field, qmhelper.LTE, x)
}
func (w whereHelpertypes_JSON) GT(x types.JSON) qm.QueryMod {
	return qmhelper.Where(w.field, qmhelper.GT, x)
}
func (w whereHelpertypes_JSON) GTE(x types.JSON) qm.QueryMod {
	return qmhelper.Where(w.field, qmhelper.GTE, x)
}

type whereHelperbool struct{ field string }

func (w whereHelperbool) EQ(x bool) qm.QueryMod  { return qmhelper.Where(w.field, qmhelper.EQ, x) }
func (w whereHelperbool) NEQ(x bool) qm.QueryMod { return qmhelper.Where(w.field, qmhelper.NEQ, x) }
func (w whereHelperbool) LT(x bool) qm.QueryMod  { return qmhelper.Where(w.field, qmhelper.LT, x) }
func (w whereHelperbool) LTE(x bool) qm.QueryMod { return qmhelper.Where(w.field, qmhelper.LTE, x) }
func (w whereHelperbool) GT(x bool) qm.QueryMod  { return qmhelper.Where(w.field, qmhelper.GT, x) }
func (w whereHelperbool) GTE(x bool) qm.QueryMod { return qmhelper.Where(w.field, qmhelper.GTE, x) }

var VehicleWhere = struct {
	HandleID       whereHelperstring
	AssetEldID     whereHelperstring
	AssetID        whereHelpernull_String
	VehicleID      whereHelpernull_String
	DeviceID       whereHelpernull_String
	Vin            whereHelpernull_String
	LicensePlate   whereHelpernull_String
	SubscriptionID whereHelpernull_String
	CreatedAt      whereHelpertime_Time
	UpdatedAt      whereHelpertime_Time
	TSPCreatedAt   whereHelpertime_Time
	TSPUpdatedAt   whereHelpertime_Time
	Raw            whereHelpertypes_JSON
	Disabled       whereHelperbool
	DeviceType     whereHelpernull_String
	PlanType       whereHelpernull_String
	AssetType      whereHelpernull_String
}{
	HandleID:       whereHelperstring{field: "\"truckercloud\".\"vehicles\".\"handle_id\""},
	AssetEldID:     whereHelperstring{field: "\"truckercloud\".\"vehicles\".\"asset_eld_id\""},
	AssetID:        whereHelpernull_String{field: "\"truckercloud\".\"vehicles\".\"asset_id\""},
	VehicleID:      whereHelpernull_String{field: "\"truckercloud\".\"vehicles\".\"vehicle_id\""},
	DeviceID:       whereHelpernull_String{field: "\"truckercloud\".\"vehicles\".\"device_id\""},
	Vin:            whereHelpernull_String{field: "\"truckercloud\".\"vehicles\".\"vin\""},
	LicensePlate:   whereHelpernull_String{field: "\"truckercloud\".\"vehicles\".\"license_plate\""},
	SubscriptionID: whereHelpernull_String{field: "\"truckercloud\".\"vehicles\".\"subscription_id\""},
	CreatedAt:      whereHelpertime_Time{field: "\"truckercloud\".\"vehicles\".\"created_at\""},
	UpdatedAt:      whereHelpertime_Time{field: "\"truckercloud\".\"vehicles\".\"updated_at\""},
	TSPCreatedAt:   whereHelpertime_Time{field: "\"truckercloud\".\"vehicles\".\"tsp_created_at\""},
	TSPUpdatedAt:   whereHelpertime_Time{field: "\"truckercloud\".\"vehicles\".\"tsp_updated_at\""},
	Raw:            whereHelpertypes_JSON{field: "\"truckercloud\".\"vehicles\".\"raw\""},
	Disabled:       whereHelperbool{field: "\"truckercloud\".\"vehicles\".\"disabled\""},
	DeviceType:     whereHelpernull_String{field: "\"truckercloud\".\"vehicles\".\"device_type\""},
	PlanType:       whereHelpernull_String{field: "\"truckercloud\".\"vehicles\".\"plan_type\""},
	AssetType:      whereHelpernull_String{field: "\"truckercloud\".\"vehicles\".\"asset_type\""},
}

// VehicleRels is where relationship names are stored.
var VehicleRels = struct {
}{}

// vehicleR is where relationships are stored.
type vehicleR struct {
}

// NewStruct creates a new relationship struct
func (*vehicleR) NewStruct() *vehicleR {
	return &vehicleR{}
}

// vehicleL is where Load methods for each relationship are stored.
type vehicleL struct{}

var (
	vehicleAllColumns            = []string{"handle_id", "asset_eld_id", "asset_id", "vehicle_id", "device_id", "vin", "license_plate", "subscription_id", "created_at", "updated_at", "tsp_created_at", "tsp_updated_at", "raw", "disabled", "device_type", "plan_type", "asset_type"}
	vehicleColumnsWithoutDefault = []string{"handle_id", "asset_eld_id", "created_at", "updated_at", "tsp_created_at", "tsp_updated_at", "raw"}
	vehicleColumnsWithDefault    = []string{"asset_id", "vehicle_id", "device_id", "vin", "license_plate", "subscription_id", "disabled", "device_type", "plan_type", "asset_type"}
	vehiclePrimaryKeyColumns     = []string{"handle_id", "asset_eld_id"}
	vehicleGeneratedColumns      = []string{}
)

type (
	// VehicleSlice is an alias for a slice of pointers to Vehicle.
	// This should almost always be used instead of []Vehicle.
	VehicleSlice []*Vehicle
	// VehicleHook is the signature for custom Vehicle hook methods
	VehicleHook func(context.Context, boil.ContextExecutor, *Vehicle) error

	vehicleQuery struct {
		*queries.Query
	}
)

// Cache for insert, update and upsert
var (
	vehicleType                 = reflect.TypeOf(&Vehicle{})
	vehicleMapping              = queries.MakeStructMapping(vehicleType)
	vehiclePrimaryKeyMapping, _ = queries.BindMapping(vehicleType, vehicleMapping, vehiclePrimaryKeyColumns)
	vehicleInsertCacheMut       sync.RWMutex
	vehicleInsertCache          = make(map[string]insertCache)
	vehicleUpdateCacheMut       sync.RWMutex
	vehicleUpdateCache          = make(map[string]updateCache)
	vehicleUpsertCacheMut       sync.RWMutex
	vehicleUpsertCache          = make(map[string]insertCache)
)

var (
	// Force time package dependency for automated UpdatedAt/CreatedAt.
	_ = time.Second
	// Force qmhelper dependency for where clause generation (which doesn't
	// always happen)
	_ = qmhelper.Where
)

var vehicleAfterSelectHooks []VehicleHook

var vehicleBeforeInsertHooks []VehicleHook
var vehicleAfterInsertHooks []VehicleHook

var vehicleBeforeUpdateHooks []VehicleHook
var vehicleAfterUpdateHooks []VehicleHook

var vehicleBeforeDeleteHooks []VehicleHook
var vehicleAfterDeleteHooks []VehicleHook

var vehicleBeforeUpsertHooks []VehicleHook
var vehicleAfterUpsertHooks []VehicleHook

// doAfterSelectHooks executes all "after Select" hooks.
func (o *Vehicle) doAfterSelectHooks(ctx context.Context, exec boil.ContextExecutor) (err error) {
	if boil.HooksAreSkipped(ctx) {
		return nil
	}

	for _, hook := range vehicleAfterSelectHooks {
		if err := hook(ctx, exec, o); err != nil {
			return err
		}
	}

	return nil
}

// doBeforeInsertHooks executes all "before insert" hooks.
func (o *Vehicle) doBeforeInsertHooks(ctx context.Context, exec boil.ContextExecutor) (err error) {
	if boil.HooksAreSkipped(ctx) {
		return nil
	}

	for _, hook := range vehicleBeforeInsertHooks {
		if err := hook(ctx, exec, o); err != nil {
			return err
		}
	}

	return nil
}

// doAfterInsertHooks executes all "after Insert" hooks.
func (o *Vehicle) doAfterInsertHooks(ctx context.Context, exec boil.ContextExecutor) (err error) {
	if boil.HooksAreSkipped(ctx) {
		return nil
	}

	for _, hook := range vehicleAfterInsertHooks {
		if err := hook(ctx, exec, o); err != nil {
			return err
		}
	}

	return nil
}

// doBeforeUpdateHooks executes all "before Update" hooks.
func (o *Vehicle) doBeforeUpdateHooks(ctx context.Context, exec boil.ContextExecutor) (err error) {
	if boil.HooksAreSkipped(ctx) {
		return nil
	}

	for _, hook := range vehicleBeforeUpdateHooks {
		if err := hook(ctx, exec, o); err != nil {
			return err
		}
	}

	return nil
}

// doAfterUpdateHooks executes all "after Update" hooks.
func (o *Vehicle) doAfterUpdateHooks(ctx context.Context, exec boil.ContextExecutor) (err error) {
	if boil.HooksAreSkipped(ctx) {
		return nil
	}

	for _, hook := range vehicleAfterUpdateHooks {
		if err := hook(ctx, exec, o); err != nil {
			return err
		}
	}

	return nil
}

// doBeforeDeleteHooks executes all "before Delete" hooks.
func (o *Vehicle) doBeforeDeleteHooks(ctx context.Context, exec boil.ContextExecutor) (err error) {
	if boil.HooksAreSkipped(ctx) {
		return nil
	}

	for _, hook := range vehicleBeforeDeleteHooks {
		if err := hook(ctx, exec, o); err != nil {
			return err
		}
	}

	return nil
}

// doAfterDeleteHooks executes all "after Delete" hooks.
func (o *Vehicle) doAfterDeleteHooks(ctx context.Context, exec boil.ContextExecutor) (err error) {
	if boil.HooksAreSkipped(ctx) {
		return nil
	}

	for _, hook := range vehicleAfterDeleteHooks {
		if err := hook(ctx, exec, o); err != nil {
			return err
		}
	}

	return nil
}

// doBeforeUpsertHooks executes all "before Upsert" hooks.
func (o *Vehicle) doBeforeUpsertHooks(ctx context.Context, exec boil.ContextExecutor) (err error) {
	if boil.HooksAreSkipped(ctx) {
		return nil
	}

	for _, hook := range vehicleBeforeUpsertHooks {
		if err := hook(ctx, exec, o); err != nil {
			return err
		}
	}

	return nil
}

// doAfterUpsertHooks executes all "after Upsert" hooks.
func (o *Vehicle) doAfterUpsertHooks(ctx context.Context, exec boil.ContextExecutor) (err error) {
	if boil.HooksAreSkipped(ctx) {
		return nil
	}

	for _, hook := range vehicleAfterUpsertHooks {
		if err := hook(ctx, exec, o); err != nil {
			return err
		}
	}

	return nil
}

// AddVehicleHook registers your hook function for all future operations.
func AddVehicleHook(hookPoint boil.HookPoint, vehicleHook VehicleHook) {
	switch hookPoint {
	case boil.AfterSelectHook:
		vehicleAfterSelectHooks = append(vehicleAfterSelectHooks, vehicleHook)
	case boil.BeforeInsertHook:
		vehicleBeforeInsertHooks = append(vehicleBeforeInsertHooks, vehicleHook)
	case boil.AfterInsertHook:
		vehicleAfterInsertHooks = append(vehicleAfterInsertHooks, vehicleHook)
	case boil.BeforeUpdateHook:
		vehicleBeforeUpdateHooks = append(vehicleBeforeUpdateHooks, vehicleHook)
	case boil.AfterUpdateHook:
		vehicleAfterUpdateHooks = append(vehicleAfterUpdateHooks, vehicleHook)
	case boil.BeforeDeleteHook:
		vehicleBeforeDeleteHooks = append(vehicleBeforeDeleteHooks, vehicleHook)
	case boil.AfterDeleteHook:
		vehicleAfterDeleteHooks = append(vehicleAfterDeleteHooks, vehicleHook)
	case boil.BeforeUpsertHook:
		vehicleBeforeUpsertHooks = append(vehicleBeforeUpsertHooks, vehicleHook)
	case boil.AfterUpsertHook:
		vehicleAfterUpsertHooks = append(vehicleAfterUpsertHooks, vehicleHook)
	}
}

// One returns a single vehicle record from the query.
func (q vehicleQuery) One(ctx context.Context, exec boil.ContextExecutor) (*Vehicle, error) {
	o := &Vehicle{}

	queries.SetLimit(q.Query, 1)

	err := q.Bind(ctx, exec, o)
	if err != nil {
		if errors.Is(err, sql.ErrNoRows) {
			return nil, sql.ErrNoRows
		}
		return nil, errors.Wrap(err, "truckercloud: failed to execute a one query for vehicles")
	}

	if err := o.doAfterSelectHooks(ctx, exec); err != nil {
		return o, err
	}

	return o, nil
}

// All returns all Vehicle records from the query.
func (q vehicleQuery) All(ctx context.Context, exec boil.ContextExecutor) (VehicleSlice, error) {
	var o []*Vehicle

	err := q.Bind(ctx, exec, &o)
	if err != nil {
		return nil, errors.Wrap(err, "truckercloud: failed to assign all query results to Vehicle slice")
	}

	if len(vehicleAfterSelectHooks) != 0 {
		for _, obj := range o {
			if err := obj.doAfterSelectHooks(ctx, exec); err != nil {
				return o, err
			}
		}
	}

	return o, nil
}

// Count returns the count of all Vehicle records in the query.
func (q vehicleQuery) Count(ctx context.Context, exec boil.ContextExecutor) (int64, error) {
	var count int64

	queries.SetSelect(q.Query, nil)
	queries.SetCount(q.Query)

	err := q.Query.QueryRowContext(ctx, exec).Scan(&count)
	if err != nil {
		return 0, errors.Wrap(err, "truckercloud: failed to count vehicles rows")
	}

	return count, nil
}

// Exists checks if the row exists in the table.
func (q vehicleQuery) Exists(ctx context.Context, exec boil.ContextExecutor) (bool, error) {
	var count int64

	queries.SetSelect(q.Query, nil)
	queries.SetCount(q.Query)
	queries.SetLimit(q.Query, 1)

	err := q.Query.QueryRowContext(ctx, exec).Scan(&count)
	if err != nil {
		return false, errors.Wrap(err, "truckercloud: failed to check if vehicles exists")
	}

	return count > 0, nil
}

// Vehicles retrieves all the records using an executor.
func Vehicles(mods ...qm.QueryMod) vehicleQuery {
	mods = append(mods, qm.From("\"truckercloud\".\"vehicles\""))
	q := NewQuery(mods...)
	if len(queries.GetSelect(q)) == 0 {
		queries.SetSelect(q, []string{"\"truckercloud\".\"vehicles\".*"})
	}

	return vehicleQuery{q}
}

// FindVehicle retrieves a single record by ID with an executor.
// If selectCols is empty Find will return all columns.
func FindVehicle(ctx context.Context, exec boil.ContextExecutor, handleID string, assetEldID string, selectCols ...string) (*Vehicle, error) {
	vehicleObj := &Vehicle{}

	sel := "*"
	if len(selectCols) > 0 {
		sel = strings.Join(strmangle.IdentQuoteSlice(dialect.LQ, dialect.RQ, selectCols), ",")
	}
	query := fmt.Sprintf(
		"select %s from \"truckercloud\".\"vehicles\" where \"handle_id\"=$1 AND \"asset_eld_id\"=$2", sel,
	)

	q := queries.Raw(query, handleID, assetEldID)

	err := q.Bind(ctx, exec, vehicleObj)
	if err != nil {
		if errors.Is(err, sql.ErrNoRows) {
			return nil, sql.ErrNoRows
		}
		return nil, errors.Wrap(err, "truckercloud: unable to select from vehicles")
	}

	if err = vehicleObj.doAfterSelectHooks(ctx, exec); err != nil {
		return vehicleObj, err
	}

	return vehicleObj, nil
}

// Insert a single record using an executor.
// See boil.Columns.InsertColumnSet documentation to understand column list inference for inserts.
func (o *Vehicle) Insert(ctx context.Context, exec boil.ContextExecutor, columns boil.Columns) error {
	if o == nil {
		return errors.New("truckercloud: no vehicles provided for insertion")
	}

	var err error
	if !boil.TimestampsAreSkipped(ctx) {
		currTime := time.Now().In(boil.GetLocation())

		if o.CreatedAt.IsZero() {
			o.CreatedAt = currTime
		}
		if o.UpdatedAt.IsZero() {
			o.UpdatedAt = currTime
		}
	}

	if err := o.doBeforeInsertHooks(ctx, exec); err != nil {
		return err
	}

	nzDefaults := queries.NonZeroDefaultSet(vehicleColumnsWithDefault, o)

	key := makeCacheKey(columns, nzDefaults)
	vehicleInsertCacheMut.RLock()
	cache, cached := vehicleInsertCache[key]
	vehicleInsertCacheMut.RUnlock()

	if !cached {
		wl, returnColumns := columns.InsertColumnSet(
			vehicleAllColumns,
			vehicleColumnsWithDefault,
			vehicleColumnsWithoutDefault,
			nzDefaults,
		)

		cache.valueMapping, err = queries.BindMapping(vehicleType, vehicleMapping, wl)
		if err != nil {
			return err
		}
		cache.retMapping, err = queries.BindMapping(vehicleType, vehicleMapping, returnColumns)
		if err != nil {
			return err
		}
		if len(wl) != 0 {
			cache.query = fmt.Sprintf("INSERT INTO \"truckercloud\".\"vehicles\" (\"%s\") %%sVALUES (%s)%%s", strings.Join(wl, "\",\""), strmangle.Placeholders(dialect.UseIndexPlaceholders, len(wl), 1, 1))
		} else {
			cache.query = "INSERT INTO \"truckercloud\".\"vehicles\" %sDEFAULT VALUES%s"
		}

		var queryOutput, queryReturning string

		if len(cache.retMapping) != 0 {
			queryReturning = fmt.Sprintf(" RETURNING \"%s\"", strings.Join(returnColumns, "\",\""))
		}

		cache.query = fmt.Sprintf(cache.query, queryOutput, queryReturning)
	}

	value := reflect.Indirect(reflect.ValueOf(o))
	vals := queries.ValuesFromMapping(value, cache.valueMapping)

	if boil.IsDebug(ctx) {
		writer := boil.DebugWriterFrom(ctx)
		fmt.Fprintln(writer, cache.query)
		fmt.Fprintln(writer, vals)
	}

	if len(cache.retMapping) != 0 {
		err = exec.QueryRowContext(ctx, cache.query, vals...).Scan(queries.PtrsFromMapping(value, cache.retMapping)...)
	} else {
		_, err = exec.ExecContext(ctx, cache.query, vals...)
	}

	if err != nil {
		return errors.Wrap(err, "truckercloud: unable to insert into vehicles")
	}

	if !cached {
		vehicleInsertCacheMut.Lock()
		vehicleInsertCache[key] = cache
		vehicleInsertCacheMut.Unlock()
	}

	return o.doAfterInsertHooks(ctx, exec)
}

// Update uses an executor to update the Vehicle.
// See boil.Columns.UpdateColumnSet documentation to understand column list inference for updates.
// Update does not automatically update the record in case of default values. Use .Reload() to refresh the records.
func (o *Vehicle) Update(ctx context.Context, exec boil.ContextExecutor, columns boil.Columns) (int64, error) {
	if !boil.TimestampsAreSkipped(ctx) {
		currTime := time.Now().In(boil.GetLocation())

		o.UpdatedAt = currTime
	}

	var err error
	if err = o.doBeforeUpdateHooks(ctx, exec); err != nil {
		return 0, err
	}
	key := makeCacheKey(columns, nil)
	vehicleUpdateCacheMut.RLock()
	cache, cached := vehicleUpdateCache[key]
	vehicleUpdateCacheMut.RUnlock()

	if !cached {
		wl := columns.UpdateColumnSet(
			vehicleAllColumns,
			vehiclePrimaryKeyColumns,
		)

		if !columns.IsWhitelist() {
			wl = strmangle.SetComplement(wl, []string{"created_at"})
		}
		if len(wl) == 0 {
			return 0, errors.New("truckercloud: unable to update vehicles, could not build whitelist")
		}

		cache.query = fmt.Sprintf("UPDATE \"truckercloud\".\"vehicles\" SET %s WHERE %s",
			strmangle.SetParamNames("\"", "\"", 1, wl),
			strmangle.WhereClause("\"", "\"", len(wl)+1, vehiclePrimaryKeyColumns),
		)
		cache.valueMapping, err = queries.BindMapping(vehicleType, vehicleMapping, append(wl, vehiclePrimaryKeyColumns...))
		if err != nil {
			return 0, err
		}
	}

	values := queries.ValuesFromMapping(reflect.Indirect(reflect.ValueOf(o)), cache.valueMapping)

	if boil.IsDebug(ctx) {
		writer := boil.DebugWriterFrom(ctx)
		fmt.Fprintln(writer, cache.query)
		fmt.Fprintln(writer, values)
	}
	var result sql.Result
	result, err = exec.ExecContext(ctx, cache.query, values...)
	if err != nil {
		return 0, errors.Wrap(err, "truckercloud: unable to update vehicles row")
	}

	rowsAff, err := result.RowsAffected()
	if err != nil {
		return 0, errors.Wrap(err, "truckercloud: failed to get rows affected by update for vehicles")
	}

	if !cached {
		vehicleUpdateCacheMut.Lock()
		vehicleUpdateCache[key] = cache
		vehicleUpdateCacheMut.Unlock()
	}

	return rowsAff, o.doAfterUpdateHooks(ctx, exec)
}

// UpdateAll updates all rows with the specified column values.
func (q vehicleQuery) UpdateAll(ctx context.Context, exec boil.ContextExecutor, cols M) (int64, error) {
	queries.SetUpdate(q.Query, cols)

	result, err := q.Query.ExecContext(ctx, exec)
	if err != nil {
		return 0, errors.Wrap(err, "truckercloud: unable to update all for vehicles")
	}

	rowsAff, err := result.RowsAffected()
	if err != nil {
		return 0, errors.Wrap(err, "truckercloud: unable to retrieve rows affected for vehicles")
	}

	return rowsAff, nil
}

// UpdateAll updates all rows with the specified column values, using an executor.
func (o VehicleSlice) UpdateAll(ctx context.Context, exec boil.ContextExecutor, cols M) (int64, error) {
	ln := int64(len(o))
	if ln == 0 {
		return 0, nil
	}

	if len(cols) == 0 {
		return 0, errors.New("truckercloud: update all requires at least one column argument")
	}

	colNames := make([]string, len(cols))
	args := make([]interface{}, len(cols))

	i := 0
	for name, value := range cols {
		colNames[i] = name
		args[i] = value
		i++
	}

	// Append all of the primary key values for each column
	for _, obj := range o {
		pkeyArgs := queries.ValuesFromMapping(reflect.Indirect(reflect.ValueOf(obj)), vehiclePrimaryKeyMapping)
		args = append(args, pkeyArgs...)
	}

	sql := fmt.Sprintf("UPDATE \"truckercloud\".\"vehicles\" SET %s WHERE %s",
		strmangle.SetParamNames("\"", "\"", 1, colNames),
		strmangle.WhereClauseRepeated(string(dialect.LQ), string(dialect.RQ), len(colNames)+1, vehiclePrimaryKeyColumns, len(o)))

	if boil.IsDebug(ctx) {
		writer := boil.DebugWriterFrom(ctx)
		fmt.Fprintln(writer, sql)
		fmt.Fprintln(writer, args...)
	}
	result, err := exec.ExecContext(ctx, sql, args...)
	if err != nil {
		return 0, errors.Wrap(err, "truckercloud: unable to update all in vehicle slice")
	}

	rowsAff, err := result.RowsAffected()
	if err != nil {
		return 0, errors.Wrap(err, "truckercloud: unable to retrieve rows affected all in update all vehicle")
	}
	return rowsAff, nil
}

// Upsert attempts an insert using an executor, and does an update or ignore on conflict.
// See boil.Columns documentation for how to properly use updateColumns and insertColumns.
func (o *Vehicle) Upsert(ctx context.Context, exec boil.ContextExecutor, updateOnConflict bool, conflictColumns []string, updateColumns, insertColumns boil.Columns) error {
	if o == nil {
		return errors.New("truckercloud: no vehicles provided for upsert")
	}
	if !boil.TimestampsAreSkipped(ctx) {
		currTime := time.Now().In(boil.GetLocation())

		if o.CreatedAt.IsZero() {
			o.CreatedAt = currTime
		}
		o.UpdatedAt = currTime
	}

	if err := o.doBeforeUpsertHooks(ctx, exec); err != nil {
		return err
	}

	nzDefaults := queries.NonZeroDefaultSet(vehicleColumnsWithDefault, o)

	// Build cache key in-line uglily - mysql vs psql problems
	buf := strmangle.GetBuffer()
	if updateOnConflict {
		buf.WriteByte('t')
	} else {
		buf.WriteByte('f')
	}
	buf.WriteByte('.')
	for _, c := range conflictColumns {
		buf.WriteString(c)
	}
	buf.WriteByte('.')
	buf.WriteString(strconv.Itoa(updateColumns.Kind))
	for _, c := range updateColumns.Cols {
		buf.WriteString(c)
	}
	buf.WriteByte('.')
	buf.WriteString(strconv.Itoa(insertColumns.Kind))
	for _, c := range insertColumns.Cols {
		buf.WriteString(c)
	}
	buf.WriteByte('.')
	for _, c := range nzDefaults {
		buf.WriteString(c)
	}
	key := buf.String()
	strmangle.PutBuffer(buf)

	vehicleUpsertCacheMut.RLock()
	cache, cached := vehicleUpsertCache[key]
	vehicleUpsertCacheMut.RUnlock()

	var err error

	if !cached {
		insert, ret := insertColumns.InsertColumnSet(
			vehicleAllColumns,
			vehicleColumnsWithDefault,
			vehicleColumnsWithoutDefault,
			nzDefaults,
		)

		update := updateColumns.UpdateColumnSet(
			vehicleAllColumns,
			vehiclePrimaryKeyColumns,
		)

		if updateOnConflict && len(update) == 0 {
			return errors.New("truckercloud: unable to upsert vehicles, could not build update column list")
		}

		conflict := conflictColumns
		if len(conflict) == 0 {
			conflict = make([]string, len(vehiclePrimaryKeyColumns))
			copy(conflict, vehiclePrimaryKeyColumns)
		}
		cache.query = buildUpsertQueryPostgres(dialect, "\"truckercloud\".\"vehicles\"", updateOnConflict, ret, update, conflict, insert)

		cache.valueMapping, err = queries.BindMapping(vehicleType, vehicleMapping, insert)
		if err != nil {
			return err
		}
		if len(ret) != 0 {
			cache.retMapping, err = queries.BindMapping(vehicleType, vehicleMapping, ret)
			if err != nil {
				return err
			}
		}
	}

	value := reflect.Indirect(reflect.ValueOf(o))
	vals := queries.ValuesFromMapping(value, cache.valueMapping)
	var returns []interface{}
	if len(cache.retMapping) != 0 {
		returns = queries.PtrsFromMapping(value, cache.retMapping)
	}

	if boil.IsDebug(ctx) {
		writer := boil.DebugWriterFrom(ctx)
		fmt.Fprintln(writer, cache.query)
		fmt.Fprintln(writer, vals)
	}
	if len(cache.retMapping) != 0 {
		err = exec.QueryRowContext(ctx, cache.query, vals...).Scan(returns...)
		if errors.Is(err, sql.ErrNoRows) {
			err = nil // Postgres doesn't return anything when there's no update
		}
	} else {
		_, err = exec.ExecContext(ctx, cache.query, vals...)
	}
	if err != nil {
		return errors.Wrap(err, "truckercloud: unable to upsert vehicles")
	}

	if !cached {
		vehicleUpsertCacheMut.Lock()
		vehicleUpsertCache[key] = cache
		vehicleUpsertCacheMut.Unlock()
	}

	return o.doAfterUpsertHooks(ctx, exec)
}

// Delete deletes a single Vehicle record with an executor.
// Delete will match against the primary key column to find the record to delete.
func (o *Vehicle) Delete(ctx context.Context, exec boil.ContextExecutor) (int64, error) {
	if o == nil {
		return 0, errors.New("truckercloud: no Vehicle provided for delete")
	}

	if err := o.doBeforeDeleteHooks(ctx, exec); err != nil {
		return 0, err
	}

	args := queries.ValuesFromMapping(reflect.Indirect(reflect.ValueOf(o)), vehiclePrimaryKeyMapping)
	sql := "DELETE FROM \"truckercloud\".\"vehicles\" WHERE \"handle_id\"=$1 AND \"asset_eld_id\"=$2"

	if boil.IsDebug(ctx) {
		writer := boil.DebugWriterFrom(ctx)
		fmt.Fprintln(writer, sql)
		fmt.Fprintln(writer, args...)
	}
	result, err := exec.ExecContext(ctx, sql, args...)
	if err != nil {
		return 0, errors.Wrap(err, "truckercloud: unable to delete from vehicles")
	}

	rowsAff, err := result.RowsAffected()
	if err != nil {
		return 0, errors.Wrap(err, "truckercloud: failed to get rows affected by delete for vehicles")
	}

	if err := o.doAfterDeleteHooks(ctx, exec); err != nil {
		return 0, err
	}

	return rowsAff, nil
}

// DeleteAll deletes all matching rows.
func (q vehicleQuery) DeleteAll(ctx context.Context, exec boil.ContextExecutor) (int64, error) {
	if q.Query == nil {
		return 0, errors.New("truckercloud: no vehicleQuery provided for delete all")
	}

	queries.SetDelete(q.Query)

	result, err := q.Query.ExecContext(ctx, exec)
	if err != nil {
		return 0, errors.Wrap(err, "truckercloud: unable to delete all from vehicles")
	}

	rowsAff, err := result.RowsAffected()
	if err != nil {
		return 0, errors.Wrap(err, "truckercloud: failed to get rows affected by deleteall for vehicles")
	}

	return rowsAff, nil
}

// DeleteAll deletes all rows in the slice, using an executor.
func (o VehicleSlice) DeleteAll(ctx context.Context, exec boil.ContextExecutor) (int64, error) {
	if len(o) == 0 {
		return 0, nil
	}

	if len(vehicleBeforeDeleteHooks) != 0 {
		for _, obj := range o {
			if err := obj.doBeforeDeleteHooks(ctx, exec); err != nil {
				return 0, err
			}
		}
	}

	var args []interface{}
	for _, obj := range o {
		pkeyArgs := queries.ValuesFromMapping(reflect.Indirect(reflect.ValueOf(obj)), vehiclePrimaryKeyMapping)
		args = append(args, pkeyArgs...)
	}

	sql := "DELETE FROM \"truckercloud\".\"vehicles\" WHERE " +
		strmangle.WhereClauseRepeated(string(dialect.LQ), string(dialect.RQ), 1, vehiclePrimaryKeyColumns, len(o))

	if boil.IsDebug(ctx) {
		writer := boil.DebugWriterFrom(ctx)
		fmt.Fprintln(writer, sql)
		fmt.Fprintln(writer, args)
	}
	result, err := exec.ExecContext(ctx, sql, args...)
	if err != nil {
		return 0, errors.Wrap(err, "truckercloud: unable to delete all from vehicle slice")
	}

	rowsAff, err := result.RowsAffected()
	if err != nil {
		return 0, errors.Wrap(err, "truckercloud: failed to get rows affected by deleteall for vehicles")
	}

	if len(vehicleAfterDeleteHooks) != 0 {
		for _, obj := range o {
			if err := obj.doAfterDeleteHooks(ctx, exec); err != nil {
				return 0, err
			}
		}
	}

	return rowsAff, nil
}

// Reload refetches the object from the database
// using the primary keys with an executor.
func (o *Vehicle) Reload(ctx context.Context, exec boil.ContextExecutor) error {
	ret, err := FindVehicle(ctx, exec, o.HandleID, o.AssetEldID)
	if err != nil {
		return err
	}

	*o = *ret
	return nil
}

// ReloadAll refetches every row with matching primary key column values
// and overwrites the original object slice with the newly updated slice.
func (o *VehicleSlice) ReloadAll(ctx context.Context, exec boil.ContextExecutor) error {
	if o == nil || len(*o) == 0 {
		return nil
	}

	slice := VehicleSlice{}
	var args []interface{}
	for _, obj := range *o {
		pkeyArgs := queries.ValuesFromMapping(reflect.Indirect(reflect.ValueOf(obj)), vehiclePrimaryKeyMapping)
		args = append(args, pkeyArgs...)
	}

	sql := "SELECT \"truckercloud\".\"vehicles\".* FROM \"truckercloud\".\"vehicles\" WHERE " +
		strmangle.WhereClauseRepeated(string(dialect.LQ), string(dialect.RQ), 1, vehiclePrimaryKeyColumns, len(*o))

	q := queries.Raw(sql, args...)

	err := q.Bind(ctx, exec, &slice)
	if err != nil {
		return errors.Wrap(err, "truckercloud: unable to reload all in VehicleSlice")
	}

	*o = slice

	return nil
}

// VehicleExists checks if the Vehicle row exists.
func VehicleExists(ctx context.Context, exec boil.ContextExecutor, handleID string, assetEldID string) (bool, error) {
	var exists bool
	sql := "select exists(select 1 from \"truckercloud\".\"vehicles\" where \"handle_id\"=$1 AND \"asset_eld_id\"=$2 limit 1)"

	if boil.IsDebug(ctx) {
		writer := boil.DebugWriterFrom(ctx)
		fmt.Fprintln(writer, sql)
		fmt.Fprintln(writer, handleID, assetEldID)
	}
	row := exec.QueryRowContext(ctx, sql, handleID, assetEldID)

	err := row.Scan(&exists)
	if err != nil {
		return false, errors.Wrap(err, "truckercloud: unable to check if vehicles exists")
	}

	return exists, nil
}
