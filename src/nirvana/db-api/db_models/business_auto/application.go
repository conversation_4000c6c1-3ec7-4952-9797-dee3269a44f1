// Code generated by SQLBoiler (https://github.com/volatiletech/sqlboiler). DO NOT EDIT.
// This file is meant to be re-generated in place and/or deleted at any time.

package business_auto

import (
	"context"
	"database/sql"
	"fmt"
	"reflect"
	"strconv"
	"strings"
	"sync"
	"time"

	"github.com/friendsofgo/errors"
	"github.com/volatiletech/null/v8"
	"github.com/volatiletech/sqlboiler/v4/boil"
	"github.com/volatiletech/sqlboiler/v4/queries"
	"github.com/volatiletech/sqlboiler/v4/queries/qm"
	"github.com/volatiletech/sqlboiler/v4/queries/qmhelper"
	"github.com/volatiletech/sqlboiler/v4/types"
	"github.com/volatiletech/strmangle"
)

// Application is an object representing the database table.
type Application struct {
	ID                              string      `boil:"id" json:"id" toml:"id" yaml:"id"`
	EffectiveDurationStart          time.Time   `boil:"effective_duration_start" json:"effective_duration_start" toml:"effective_duration_start" yaml:"effective_duration_start"`
	EffectiveDurationEnd            time.Time   `boil:"effective_duration_end" json:"effective_duration_end" toml:"effective_duration_end" yaml:"effective_duration_end"`
	CompanyInfo                     types.JSON  `boil:"company_info" json:"company_info" toml:"company_info" yaml:"company_info"`
	VehiclesInfo                    null.JSON   `boil:"vehicles_info" json:"vehicles_info,omitempty" toml:"vehicles_info" yaml:"vehicles_info,omitempty"`
	CoveragesInfo                   null.JSON   `boil:"coverages_info" json:"coverages_info,omitempty" toml:"coverages_info" yaml:"coverages_info,omitempty"`
	FilingsInfo                     null.JSON   `boil:"filings_info" json:"filings_info,omitempty" toml:"filings_info" yaml:"filings_info,omitempty"`
	ProducerID                      string      `boil:"producer_id" json:"producer_id" toml:"producer_id" yaml:"producer_id"`
	CreatedBy                       string      `boil:"created_by" json:"created_by" toml:"created_by" yaml:"created_by"`
	CreatedAt                       time.Time   `boil:"created_at" json:"created_at" toml:"created_at" yaml:"created_at"`
	UpdatedAt                       time.Time   `boil:"updated_at" json:"updated_at" toml:"updated_at" yaml:"updated_at"`
	UwState                         string      `boil:"uw_state" json:"uw_state" toml:"uw_state" yaml:"uw_state"`
	Documents                       null.JSON   `boil:"documents" json:"documents,omitempty" toml:"documents" yaml:"documents,omitempty"`
	UnderwritingOverrides           null.JSON   `boil:"underwriting_overrides" json:"underwriting_overrides,omitempty" toml:"underwriting_overrides" yaml:"underwriting_overrides,omitempty"`
	AgencyID                        string      `boil:"agency_id" json:"agency_id" toml:"agency_id" yaml:"agency_id"`
	ShortID                         string      `boil:"short_id" json:"short_id" toml:"short_id" yaml:"short_id"`
	SignaturePacketID               null.String `boil:"signature_packet_id" json:"signature_packet_id,omitempty" toml:"signature_packet_id" yaml:"signature_packet_id,omitempty"`
	TelematicsInfo                  null.JSON   `boil:"telematics_info" json:"telematics_info,omitempty" toml:"telematics_info" yaml:"telematics_info,omitempty"`
	TSPConnHandleID                 null.String `boil:"tsp_conn_handle_id" json:"tsp_conn_handle_id,omitempty" toml:"tsp_conn_handle_id" yaml:"tsp_conn_handle_id,omitempty"`
	DataContextID                   string      `boil:"data_context_id" json:"data_context_id" toml:"data_context_id" yaml:"data_context_id"`
	PricingJobInfo                  null.JSON   `boil:"pricing_job_info" json:"pricing_job_info,omitempty" toml:"pricing_job_info" yaml:"pricing_job_info,omitempty"`
	SelectedQuotingPricingContextID null.String `boil:"selected_quoting_pricing_context_id" json:"selected_quoting_pricing_context_id,omitempty" toml:"selected_quoting_pricing_context_id" yaml:"selected_quoting_pricing_context_id,omitempty"`
	ModelPinConfig                  null.JSON   `boil:"model_pin_config" json:"model_pin_config,omitempty" toml:"model_pin_config" yaml:"model_pin_config,omitempty"`
	AccountID                       null.String `boil:"account_id" json:"account_id,omitempty" toml:"account_id" yaml:"account_id,omitempty"`

	R *applicationR `boil:"" json:"" toml:"" yaml:""`
	L applicationL  `boil:"-" json:"-" toml:"-" yaml:"-"`
}

var ApplicationColumns = struct {
	ID                              string
	EffectiveDurationStart          string
	EffectiveDurationEnd            string
	CompanyInfo                     string
	VehiclesInfo                    string
	CoveragesInfo                   string
	FilingsInfo                     string
	ProducerID                      string
	CreatedBy                       string
	CreatedAt                       string
	UpdatedAt                       string
	UwState                         string
	Documents                       string
	UnderwritingOverrides           string
	AgencyID                        string
	ShortID                         string
	SignaturePacketID               string
	TelematicsInfo                  string
	TSPConnHandleID                 string
	DataContextID                   string
	PricingJobInfo                  string
	SelectedQuotingPricingContextID string
	ModelPinConfig                  string
	AccountID                       string
}{
	ID:                              "id",
	EffectiveDurationStart:          "effective_duration_start",
	EffectiveDurationEnd:            "effective_duration_end",
	CompanyInfo:                     "company_info",
	VehiclesInfo:                    "vehicles_info",
	CoveragesInfo:                   "coverages_info",
	FilingsInfo:                     "filings_info",
	ProducerID:                      "producer_id",
	CreatedBy:                       "created_by",
	CreatedAt:                       "created_at",
	UpdatedAt:                       "updated_at",
	UwState:                         "uw_state",
	Documents:                       "documents",
	UnderwritingOverrides:           "underwriting_overrides",
	AgencyID:                        "agency_id",
	ShortID:                         "short_id",
	SignaturePacketID:               "signature_packet_id",
	TelematicsInfo:                  "telematics_info",
	TSPConnHandleID:                 "tsp_conn_handle_id",
	DataContextID:                   "data_context_id",
	PricingJobInfo:                  "pricing_job_info",
	SelectedQuotingPricingContextID: "selected_quoting_pricing_context_id",
	ModelPinConfig:                  "model_pin_config",
	AccountID:                       "account_id",
}

var ApplicationTableColumns = struct {
	ID                              string
	EffectiveDurationStart          string
	EffectiveDurationEnd            string
	CompanyInfo                     string
	VehiclesInfo                    string
	CoveragesInfo                   string
	FilingsInfo                     string
	ProducerID                      string
	CreatedBy                       string
	CreatedAt                       string
	UpdatedAt                       string
	UwState                         string
	Documents                       string
	UnderwritingOverrides           string
	AgencyID                        string
	ShortID                         string
	SignaturePacketID               string
	TelematicsInfo                  string
	TSPConnHandleID                 string
	DataContextID                   string
	PricingJobInfo                  string
	SelectedQuotingPricingContextID string
	ModelPinConfig                  string
	AccountID                       string
}{
	ID:                              "application.id",
	EffectiveDurationStart:          "application.effective_duration_start",
	EffectiveDurationEnd:            "application.effective_duration_end",
	CompanyInfo:                     "application.company_info",
	VehiclesInfo:                    "application.vehicles_info",
	CoveragesInfo:                   "application.coverages_info",
	FilingsInfo:                     "application.filings_info",
	ProducerID:                      "application.producer_id",
	CreatedBy:                       "application.created_by",
	CreatedAt:                       "application.created_at",
	UpdatedAt:                       "application.updated_at",
	UwState:                         "application.uw_state",
	Documents:                       "application.documents",
	UnderwritingOverrides:           "application.underwriting_overrides",
	AgencyID:                        "application.agency_id",
	ShortID:                         "application.short_id",
	SignaturePacketID:               "application.signature_packet_id",
	TelematicsInfo:                  "application.telematics_info",
	TSPConnHandleID:                 "application.tsp_conn_handle_id",
	DataContextID:                   "application.data_context_id",
	PricingJobInfo:                  "application.pricing_job_info",
	SelectedQuotingPricingContextID: "application.selected_quoting_pricing_context_id",
	ModelPinConfig:                  "application.model_pin_config",
	AccountID:                       "application.account_id",
}

// Generated where

type whereHelperstring struct{ field string }

func (w whereHelperstring) EQ(x string) qm.QueryMod  { return qmhelper.Where(w.field, qmhelper.EQ, x) }
func (w whereHelperstring) NEQ(x string) qm.QueryMod { return qmhelper.Where(w.field, qmhelper.NEQ, x) }
func (w whereHelperstring) LT(x string) qm.QueryMod  { return qmhelper.Where(w.field, qmhelper.LT, x) }
func (w whereHelperstring) LTE(x string) qm.QueryMod { return qmhelper.Where(w.field, qmhelper.LTE, x) }
func (w whereHelperstring) GT(x string) qm.QueryMod  { return qmhelper.Where(w.field, qmhelper.GT, x) }
func (w whereHelperstring) GTE(x string) qm.QueryMod { return qmhelper.Where(w.field, qmhelper.GTE, x) }
func (w whereHelperstring) IN(slice []string) qm.QueryMod {
	values := make([]interface{}, 0, len(slice))
	for _, value := range slice {
		values = append(values, value)
	}
	return qm.WhereIn(fmt.Sprintf("%s IN ?", w.field), values...)
}
func (w whereHelperstring) NIN(slice []string) qm.QueryMod {
	values := make([]interface{}, 0, len(slice))
	for _, value := range slice {
		values = append(values, value)
	}
	return qm.WhereNotIn(fmt.Sprintf("%s NOT IN ?", w.field), values...)
}

type whereHelpertime_Time struct{ field string }

func (w whereHelpertime_Time) EQ(x time.Time) qm.QueryMod {
	return qmhelper.Where(w.field, qmhelper.EQ, x)
}
func (w whereHelpertime_Time) NEQ(x time.Time) qm.QueryMod {
	return qmhelper.Where(w.field, qmhelper.NEQ, x)
}
func (w whereHelpertime_Time) LT(x time.Time) qm.QueryMod {
	return qmhelper.Where(w.field, qmhelper.LT, x)
}
func (w whereHelpertime_Time) LTE(x time.Time) qm.QueryMod {
	return qmhelper.Where(w.field, qmhelper.LTE, x)
}
func (w whereHelpertime_Time) GT(x time.Time) qm.QueryMod {
	return qmhelper.Where(w.field, qmhelper.GT, x)
}
func (w whereHelpertime_Time) GTE(x time.Time) qm.QueryMod {
	return qmhelper.Where(w.field, qmhelper.GTE, x)
}

type whereHelpertypes_JSON struct{ field string }

func (w whereHelpertypes_JSON) EQ(x types.JSON) qm.QueryMod {
	return qmhelper.Where(w.field, qmhelper.EQ, x)
}
func (w whereHelpertypes_JSON) NEQ(x types.JSON) qm.QueryMod {
	return qmhelper.Where(w.field, qmhelper.NEQ, x)
}
func (w whereHelpertypes_JSON) LT(x types.JSON) qm.QueryMod {
	return qmhelper.Where(w.field, qmhelper.LT, x)
}
func (w whereHelpertypes_JSON) LTE(x types.JSON) qm.QueryMod {
	return qmhelper.Where(w.field, qmhelper.LTE, x)
}
func (w whereHelpertypes_JSON) GT(x types.JSON) qm.QueryMod {
	return qmhelper.Where(w.field, qmhelper.GT, x)
}
func (w whereHelpertypes_JSON) GTE(x types.JSON) qm.QueryMod {
	return qmhelper.Where(w.field, qmhelper.GTE, x)
}

type whereHelpernull_JSON struct{ field string }

func (w whereHelpernull_JSON) EQ(x null.JSON) qm.QueryMod {
	return qmhelper.WhereNullEQ(w.field, false, x)
}
func (w whereHelpernull_JSON) NEQ(x null.JSON) qm.QueryMod {
	return qmhelper.WhereNullEQ(w.field, true, x)
}
func (w whereHelpernull_JSON) LT(x null.JSON) qm.QueryMod {
	return qmhelper.Where(w.field, qmhelper.LT, x)
}
func (w whereHelpernull_JSON) LTE(x null.JSON) qm.QueryMod {
	return qmhelper.Where(w.field, qmhelper.LTE, x)
}
func (w whereHelpernull_JSON) GT(x null.JSON) qm.QueryMod {
	return qmhelper.Where(w.field, qmhelper.GT, x)
}
func (w whereHelpernull_JSON) GTE(x null.JSON) qm.QueryMod {
	return qmhelper.Where(w.field, qmhelper.GTE, x)
}

func (w whereHelpernull_JSON) IsNull() qm.QueryMod    { return qmhelper.WhereIsNull(w.field) }
func (w whereHelpernull_JSON) IsNotNull() qm.QueryMod { return qmhelper.WhereIsNotNull(w.field) }

type whereHelpernull_String struct{ field string }

func (w whereHelpernull_String) EQ(x null.String) qm.QueryMod {
	return qmhelper.WhereNullEQ(w.field, false, x)
}
func (w whereHelpernull_String) NEQ(x null.String) qm.QueryMod {
	return qmhelper.WhereNullEQ(w.field, true, x)
}
func (w whereHelpernull_String) LT(x null.String) qm.QueryMod {
	return qmhelper.Where(w.field, qmhelper.LT, x)
}
func (w whereHelpernull_String) LTE(x null.String) qm.QueryMod {
	return qmhelper.Where(w.field, qmhelper.LTE, x)
}
func (w whereHelpernull_String) GT(x null.String) qm.QueryMod {
	return qmhelper.Where(w.field, qmhelper.GT, x)
}
func (w whereHelpernull_String) GTE(x null.String) qm.QueryMod {
	return qmhelper.Where(w.field, qmhelper.GTE, x)
}

func (w whereHelpernull_String) IsNull() qm.QueryMod    { return qmhelper.WhereIsNull(w.field) }
func (w whereHelpernull_String) IsNotNull() qm.QueryMod { return qmhelper.WhereIsNotNull(w.field) }

var ApplicationWhere = struct {
	ID                              whereHelperstring
	EffectiveDurationStart          whereHelpertime_Time
	EffectiveDurationEnd            whereHelpertime_Time
	CompanyInfo                     whereHelpertypes_JSON
	VehiclesInfo                    whereHelpernull_JSON
	CoveragesInfo                   whereHelpernull_JSON
	FilingsInfo                     whereHelpernull_JSON
	ProducerID                      whereHelperstring
	CreatedBy                       whereHelperstring
	CreatedAt                       whereHelpertime_Time
	UpdatedAt                       whereHelpertime_Time
	UwState                         whereHelperstring
	Documents                       whereHelpernull_JSON
	UnderwritingOverrides           whereHelpernull_JSON
	AgencyID                        whereHelperstring
	ShortID                         whereHelperstring
	SignaturePacketID               whereHelpernull_String
	TelematicsInfo                  whereHelpernull_JSON
	TSPConnHandleID                 whereHelpernull_String
	DataContextID                   whereHelperstring
	PricingJobInfo                  whereHelpernull_JSON
	SelectedQuotingPricingContextID whereHelpernull_String
	ModelPinConfig                  whereHelpernull_JSON
	AccountID                       whereHelpernull_String
}{
	ID:                              whereHelperstring{field: "\"business_auto\".\"application\".\"id\""},
	EffectiveDurationStart:          whereHelpertime_Time{field: "\"business_auto\".\"application\".\"effective_duration_start\""},
	EffectiveDurationEnd:            whereHelpertime_Time{field: "\"business_auto\".\"application\".\"effective_duration_end\""},
	CompanyInfo:                     whereHelpertypes_JSON{field: "\"business_auto\".\"application\".\"company_info\""},
	VehiclesInfo:                    whereHelpernull_JSON{field: "\"business_auto\".\"application\".\"vehicles_info\""},
	CoveragesInfo:                   whereHelpernull_JSON{field: "\"business_auto\".\"application\".\"coverages_info\""},
	FilingsInfo:                     whereHelpernull_JSON{field: "\"business_auto\".\"application\".\"filings_info\""},
	ProducerID:                      whereHelperstring{field: "\"business_auto\".\"application\".\"producer_id\""},
	CreatedBy:                       whereHelperstring{field: "\"business_auto\".\"application\".\"created_by\""},
	CreatedAt:                       whereHelpertime_Time{field: "\"business_auto\".\"application\".\"created_at\""},
	UpdatedAt:                       whereHelpertime_Time{field: "\"business_auto\".\"application\".\"updated_at\""},
	UwState:                         whereHelperstring{field: "\"business_auto\".\"application\".\"uw_state\""},
	Documents:                       whereHelpernull_JSON{field: "\"business_auto\".\"application\".\"documents\""},
	UnderwritingOverrides:           whereHelpernull_JSON{field: "\"business_auto\".\"application\".\"underwriting_overrides\""},
	AgencyID:                        whereHelperstring{field: "\"business_auto\".\"application\".\"agency_id\""},
	ShortID:                         whereHelperstring{field: "\"business_auto\".\"application\".\"short_id\""},
	SignaturePacketID:               whereHelpernull_String{field: "\"business_auto\".\"application\".\"signature_packet_id\""},
	TelematicsInfo:                  whereHelpernull_JSON{field: "\"business_auto\".\"application\".\"telematics_info\""},
	TSPConnHandleID:                 whereHelpernull_String{field: "\"business_auto\".\"application\".\"tsp_conn_handle_id\""},
	DataContextID:                   whereHelperstring{field: "\"business_auto\".\"application\".\"data_context_id\""},
	PricingJobInfo:                  whereHelpernull_JSON{field: "\"business_auto\".\"application\".\"pricing_job_info\""},
	SelectedQuotingPricingContextID: whereHelpernull_String{field: "\"business_auto\".\"application\".\"selected_quoting_pricing_context_id\""},
	ModelPinConfig:                  whereHelpernull_JSON{field: "\"business_auto\".\"application\".\"model_pin_config\""},
	AccountID:                       whereHelpernull_String{field: "\"business_auto\".\"application\".\"account_id\""},
}

// ApplicationRels is where relationship names are stored.
var ApplicationRels = struct {
}{}

// applicationR is where relationships are stored.
type applicationR struct {
}

// NewStruct creates a new relationship struct
func (*applicationR) NewStruct() *applicationR {
	return &applicationR{}
}

// applicationL is where Load methods for each relationship are stored.
type applicationL struct{}

var (
	applicationAllColumns            = []string{"id", "effective_duration_start", "effective_duration_end", "company_info", "vehicles_info", "coverages_info", "filings_info", "producer_id", "created_by", "created_at", "updated_at", "uw_state", "documents", "underwriting_overrides", "agency_id", "short_id", "signature_packet_id", "telematics_info", "tsp_conn_handle_id", "data_context_id", "pricing_job_info", "selected_quoting_pricing_context_id", "model_pin_config", "account_id"}
	applicationColumnsWithoutDefault = []string{"id", "effective_duration_start", "effective_duration_end", "company_info", "producer_id", "created_by", "uw_state", "agency_id", "short_id"}
	applicationColumnsWithDefault    = []string{"vehicles_info", "coverages_info", "filings_info", "created_at", "updated_at", "documents", "underwriting_overrides", "signature_packet_id", "telematics_info", "tsp_conn_handle_id", "data_context_id", "pricing_job_info", "selected_quoting_pricing_context_id", "model_pin_config", "account_id"}
	applicationPrimaryKeyColumns     = []string{"id"}
	applicationGeneratedColumns      = []string{}
)

type (
	// ApplicationSlice is an alias for a slice of pointers to Application.
	// This should almost always be used instead of []Application.
	ApplicationSlice []*Application
	// ApplicationHook is the signature for custom Application hook methods
	ApplicationHook func(context.Context, boil.ContextExecutor, *Application) error

	applicationQuery struct {
		*queries.Query
	}
)

// Cache for insert, update and upsert
var (
	applicationType                 = reflect.TypeOf(&Application{})
	applicationMapping              = queries.MakeStructMapping(applicationType)
	applicationPrimaryKeyMapping, _ = queries.BindMapping(applicationType, applicationMapping, applicationPrimaryKeyColumns)
	applicationInsertCacheMut       sync.RWMutex
	applicationInsertCache          = make(map[string]insertCache)
	applicationUpdateCacheMut       sync.RWMutex
	applicationUpdateCache          = make(map[string]updateCache)
	applicationUpsertCacheMut       sync.RWMutex
	applicationUpsertCache          = make(map[string]insertCache)
)

var (
	// Force time package dependency for automated UpdatedAt/CreatedAt.
	_ = time.Second
	// Force qmhelper dependency for where clause generation (which doesn't
	// always happen)
	_ = qmhelper.Where
)

var applicationAfterSelectHooks []ApplicationHook

var applicationBeforeInsertHooks []ApplicationHook
var applicationAfterInsertHooks []ApplicationHook

var applicationBeforeUpdateHooks []ApplicationHook
var applicationAfterUpdateHooks []ApplicationHook

var applicationBeforeDeleteHooks []ApplicationHook
var applicationAfterDeleteHooks []ApplicationHook

var applicationBeforeUpsertHooks []ApplicationHook
var applicationAfterUpsertHooks []ApplicationHook

// doAfterSelectHooks executes all "after Select" hooks.
func (o *Application) doAfterSelectHooks(ctx context.Context, exec boil.ContextExecutor) (err error) {
	if boil.HooksAreSkipped(ctx) {
		return nil
	}

	for _, hook := range applicationAfterSelectHooks {
		if err := hook(ctx, exec, o); err != nil {
			return err
		}
	}

	return nil
}

// doBeforeInsertHooks executes all "before insert" hooks.
func (o *Application) doBeforeInsertHooks(ctx context.Context, exec boil.ContextExecutor) (err error) {
	if boil.HooksAreSkipped(ctx) {
		return nil
	}

	for _, hook := range applicationBeforeInsertHooks {
		if err := hook(ctx, exec, o); err != nil {
			return err
		}
	}

	return nil
}

// doAfterInsertHooks executes all "after Insert" hooks.
func (o *Application) doAfterInsertHooks(ctx context.Context, exec boil.ContextExecutor) (err error) {
	if boil.HooksAreSkipped(ctx) {
		return nil
	}

	for _, hook := range applicationAfterInsertHooks {
		if err := hook(ctx, exec, o); err != nil {
			return err
		}
	}

	return nil
}

// doBeforeUpdateHooks executes all "before Update" hooks.
func (o *Application) doBeforeUpdateHooks(ctx context.Context, exec boil.ContextExecutor) (err error) {
	if boil.HooksAreSkipped(ctx) {
		return nil
	}

	for _, hook := range applicationBeforeUpdateHooks {
		if err := hook(ctx, exec, o); err != nil {
			return err
		}
	}

	return nil
}

// doAfterUpdateHooks executes all "after Update" hooks.
func (o *Application) doAfterUpdateHooks(ctx context.Context, exec boil.ContextExecutor) (err error) {
	if boil.HooksAreSkipped(ctx) {
		return nil
	}

	for _, hook := range applicationAfterUpdateHooks {
		if err := hook(ctx, exec, o); err != nil {
			return err
		}
	}

	return nil
}

// doBeforeDeleteHooks executes all "before Delete" hooks.
func (o *Application) doBeforeDeleteHooks(ctx context.Context, exec boil.ContextExecutor) (err error) {
	if boil.HooksAreSkipped(ctx) {
		return nil
	}

	for _, hook := range applicationBeforeDeleteHooks {
		if err := hook(ctx, exec, o); err != nil {
			return err
		}
	}

	return nil
}

// doAfterDeleteHooks executes all "after Delete" hooks.
func (o *Application) doAfterDeleteHooks(ctx context.Context, exec boil.ContextExecutor) (err error) {
	if boil.HooksAreSkipped(ctx) {
		return nil
	}

	for _, hook := range applicationAfterDeleteHooks {
		if err := hook(ctx, exec, o); err != nil {
			return err
		}
	}

	return nil
}

// doBeforeUpsertHooks executes all "before Upsert" hooks.
func (o *Application) doBeforeUpsertHooks(ctx context.Context, exec boil.ContextExecutor) (err error) {
	if boil.HooksAreSkipped(ctx) {
		return nil
	}

	for _, hook := range applicationBeforeUpsertHooks {
		if err := hook(ctx, exec, o); err != nil {
			return err
		}
	}

	return nil
}

// doAfterUpsertHooks executes all "after Upsert" hooks.
func (o *Application) doAfterUpsertHooks(ctx context.Context, exec boil.ContextExecutor) (err error) {
	if boil.HooksAreSkipped(ctx) {
		return nil
	}

	for _, hook := range applicationAfterUpsertHooks {
		if err := hook(ctx, exec, o); err != nil {
			return err
		}
	}

	return nil
}

// AddApplicationHook registers your hook function for all future operations.
func AddApplicationHook(hookPoint boil.HookPoint, applicationHook ApplicationHook) {
	switch hookPoint {
	case boil.AfterSelectHook:
		applicationAfterSelectHooks = append(applicationAfterSelectHooks, applicationHook)
	case boil.BeforeInsertHook:
		applicationBeforeInsertHooks = append(applicationBeforeInsertHooks, applicationHook)
	case boil.AfterInsertHook:
		applicationAfterInsertHooks = append(applicationAfterInsertHooks, applicationHook)
	case boil.BeforeUpdateHook:
		applicationBeforeUpdateHooks = append(applicationBeforeUpdateHooks, applicationHook)
	case boil.AfterUpdateHook:
		applicationAfterUpdateHooks = append(applicationAfterUpdateHooks, applicationHook)
	case boil.BeforeDeleteHook:
		applicationBeforeDeleteHooks = append(applicationBeforeDeleteHooks, applicationHook)
	case boil.AfterDeleteHook:
		applicationAfterDeleteHooks = append(applicationAfterDeleteHooks, applicationHook)
	case boil.BeforeUpsertHook:
		applicationBeforeUpsertHooks = append(applicationBeforeUpsertHooks, applicationHook)
	case boil.AfterUpsertHook:
		applicationAfterUpsertHooks = append(applicationAfterUpsertHooks, applicationHook)
	}
}

// One returns a single application record from the query.
func (q applicationQuery) One(ctx context.Context, exec boil.ContextExecutor) (*Application, error) {
	o := &Application{}

	queries.SetLimit(q.Query, 1)

	err := q.Bind(ctx, exec, o)
	if err != nil {
		if errors.Is(err, sql.ErrNoRows) {
			return nil, sql.ErrNoRows
		}
		return nil, errors.Wrap(err, "business_auto: failed to execute a one query for application")
	}

	if err := o.doAfterSelectHooks(ctx, exec); err != nil {
		return o, err
	}

	return o, nil
}

// All returns all Application records from the query.
func (q applicationQuery) All(ctx context.Context, exec boil.ContextExecutor) (ApplicationSlice, error) {
	var o []*Application

	err := q.Bind(ctx, exec, &o)
	if err != nil {
		return nil, errors.Wrap(err, "business_auto: failed to assign all query results to Application slice")
	}

	if len(applicationAfterSelectHooks) != 0 {
		for _, obj := range o {
			if err := obj.doAfterSelectHooks(ctx, exec); err != nil {
				return o, err
			}
		}
	}

	return o, nil
}

// Count returns the count of all Application records in the query.
func (q applicationQuery) Count(ctx context.Context, exec boil.ContextExecutor) (int64, error) {
	var count int64

	queries.SetSelect(q.Query, nil)
	queries.SetCount(q.Query)

	err := q.Query.QueryRowContext(ctx, exec).Scan(&count)
	if err != nil {
		return 0, errors.Wrap(err, "business_auto: failed to count application rows")
	}

	return count, nil
}

// Exists checks if the row exists in the table.
func (q applicationQuery) Exists(ctx context.Context, exec boil.ContextExecutor) (bool, error) {
	var count int64

	queries.SetSelect(q.Query, nil)
	queries.SetCount(q.Query)
	queries.SetLimit(q.Query, 1)

	err := q.Query.QueryRowContext(ctx, exec).Scan(&count)
	if err != nil {
		return false, errors.Wrap(err, "business_auto: failed to check if application exists")
	}

	return count > 0, nil
}

// Applications retrieves all the records using an executor.
func Applications(mods ...qm.QueryMod) applicationQuery {
	mods = append(mods, qm.From("\"business_auto\".\"application\""))
	q := NewQuery(mods...)
	if len(queries.GetSelect(q)) == 0 {
		queries.SetSelect(q, []string{"\"business_auto\".\"application\".*"})
	}

	return applicationQuery{q}
}

// FindApplication retrieves a single record by ID with an executor.
// If selectCols is empty Find will return all columns.
func FindApplication(ctx context.Context, exec boil.ContextExecutor, iD string, selectCols ...string) (*Application, error) {
	applicationObj := &Application{}

	sel := "*"
	if len(selectCols) > 0 {
		sel = strings.Join(strmangle.IdentQuoteSlice(dialect.LQ, dialect.RQ, selectCols), ",")
	}
	query := fmt.Sprintf(
		"select %s from \"business_auto\".\"application\" where \"id\"=$1", sel,
	)

	q := queries.Raw(query, iD)

	err := q.Bind(ctx, exec, applicationObj)
	if err != nil {
		if errors.Is(err, sql.ErrNoRows) {
			return nil, sql.ErrNoRows
		}
		return nil, errors.Wrap(err, "business_auto: unable to select from application")
	}

	if err = applicationObj.doAfterSelectHooks(ctx, exec); err != nil {
		return applicationObj, err
	}

	return applicationObj, nil
}

// Insert a single record using an executor.
// See boil.Columns.InsertColumnSet documentation to understand column list inference for inserts.
func (o *Application) Insert(ctx context.Context, exec boil.ContextExecutor, columns boil.Columns) error {
	if o == nil {
		return errors.New("business_auto: no application provided for insertion")
	}

	var err error
	if !boil.TimestampsAreSkipped(ctx) {
		currTime := time.Now().In(boil.GetLocation())

		if o.CreatedAt.IsZero() {
			o.CreatedAt = currTime
		}
		if o.UpdatedAt.IsZero() {
			o.UpdatedAt = currTime
		}
	}

	if err := o.doBeforeInsertHooks(ctx, exec); err != nil {
		return err
	}

	nzDefaults := queries.NonZeroDefaultSet(applicationColumnsWithDefault, o)

	key := makeCacheKey(columns, nzDefaults)
	applicationInsertCacheMut.RLock()
	cache, cached := applicationInsertCache[key]
	applicationInsertCacheMut.RUnlock()

	if !cached {
		wl, returnColumns := columns.InsertColumnSet(
			applicationAllColumns,
			applicationColumnsWithDefault,
			applicationColumnsWithoutDefault,
			nzDefaults,
		)

		cache.valueMapping, err = queries.BindMapping(applicationType, applicationMapping, wl)
		if err != nil {
			return err
		}
		cache.retMapping, err = queries.BindMapping(applicationType, applicationMapping, returnColumns)
		if err != nil {
			return err
		}
		if len(wl) != 0 {
			cache.query = fmt.Sprintf("INSERT INTO \"business_auto\".\"application\" (\"%s\") %%sVALUES (%s)%%s", strings.Join(wl, "\",\""), strmangle.Placeholders(dialect.UseIndexPlaceholders, len(wl), 1, 1))
		} else {
			cache.query = "INSERT INTO \"business_auto\".\"application\" %sDEFAULT VALUES%s"
		}

		var queryOutput, queryReturning string

		if len(cache.retMapping) != 0 {
			queryReturning = fmt.Sprintf(" RETURNING \"%s\"", strings.Join(returnColumns, "\",\""))
		}

		cache.query = fmt.Sprintf(cache.query, queryOutput, queryReturning)
	}

	value := reflect.Indirect(reflect.ValueOf(o))
	vals := queries.ValuesFromMapping(value, cache.valueMapping)

	if boil.IsDebug(ctx) {
		writer := boil.DebugWriterFrom(ctx)
		fmt.Fprintln(writer, cache.query)
		fmt.Fprintln(writer, vals)
	}

	if len(cache.retMapping) != 0 {
		err = exec.QueryRowContext(ctx, cache.query, vals...).Scan(queries.PtrsFromMapping(value, cache.retMapping)...)
	} else {
		_, err = exec.ExecContext(ctx, cache.query, vals...)
	}

	if err != nil {
		return errors.Wrap(err, "business_auto: unable to insert into application")
	}

	if !cached {
		applicationInsertCacheMut.Lock()
		applicationInsertCache[key] = cache
		applicationInsertCacheMut.Unlock()
	}

	return o.doAfterInsertHooks(ctx, exec)
}

// Update uses an executor to update the Application.
// See boil.Columns.UpdateColumnSet documentation to understand column list inference for updates.
// Update does not automatically update the record in case of default values. Use .Reload() to refresh the records.
func (o *Application) Update(ctx context.Context, exec boil.ContextExecutor, columns boil.Columns) (int64, error) {
	if !boil.TimestampsAreSkipped(ctx) {
		currTime := time.Now().In(boil.GetLocation())

		o.UpdatedAt = currTime
	}

	var err error
	if err = o.doBeforeUpdateHooks(ctx, exec); err != nil {
		return 0, err
	}
	key := makeCacheKey(columns, nil)
	applicationUpdateCacheMut.RLock()
	cache, cached := applicationUpdateCache[key]
	applicationUpdateCacheMut.RUnlock()

	if !cached {
		wl := columns.UpdateColumnSet(
			applicationAllColumns,
			applicationPrimaryKeyColumns,
		)

		if !columns.IsWhitelist() {
			wl = strmangle.SetComplement(wl, []string{"created_at"})
		}
		if len(wl) == 0 {
			return 0, errors.New("business_auto: unable to update application, could not build whitelist")
		}

		cache.query = fmt.Sprintf("UPDATE \"business_auto\".\"application\" SET %s WHERE %s",
			strmangle.SetParamNames("\"", "\"", 1, wl),
			strmangle.WhereClause("\"", "\"", len(wl)+1, applicationPrimaryKeyColumns),
		)
		cache.valueMapping, err = queries.BindMapping(applicationType, applicationMapping, append(wl, applicationPrimaryKeyColumns...))
		if err != nil {
			return 0, err
		}
	}

	values := queries.ValuesFromMapping(reflect.Indirect(reflect.ValueOf(o)), cache.valueMapping)

	if boil.IsDebug(ctx) {
		writer := boil.DebugWriterFrom(ctx)
		fmt.Fprintln(writer, cache.query)
		fmt.Fprintln(writer, values)
	}
	var result sql.Result
	result, err = exec.ExecContext(ctx, cache.query, values...)
	if err != nil {
		return 0, errors.Wrap(err, "business_auto: unable to update application row")
	}

	rowsAff, err := result.RowsAffected()
	if err != nil {
		return 0, errors.Wrap(err, "business_auto: failed to get rows affected by update for application")
	}

	if !cached {
		applicationUpdateCacheMut.Lock()
		applicationUpdateCache[key] = cache
		applicationUpdateCacheMut.Unlock()
	}

	return rowsAff, o.doAfterUpdateHooks(ctx, exec)
}

// UpdateAll updates all rows with the specified column values.
func (q applicationQuery) UpdateAll(ctx context.Context, exec boil.ContextExecutor, cols M) (int64, error) {
	queries.SetUpdate(q.Query, cols)

	result, err := q.Query.ExecContext(ctx, exec)
	if err != nil {
		return 0, errors.Wrap(err, "business_auto: unable to update all for application")
	}

	rowsAff, err := result.RowsAffected()
	if err != nil {
		return 0, errors.Wrap(err, "business_auto: unable to retrieve rows affected for application")
	}

	return rowsAff, nil
}

// UpdateAll updates all rows with the specified column values, using an executor.
func (o ApplicationSlice) UpdateAll(ctx context.Context, exec boil.ContextExecutor, cols M) (int64, error) {
	ln := int64(len(o))
	if ln == 0 {
		return 0, nil
	}

	if len(cols) == 0 {
		return 0, errors.New("business_auto: update all requires at least one column argument")
	}

	colNames := make([]string, len(cols))
	args := make([]interface{}, len(cols))

	i := 0
	for name, value := range cols {
		colNames[i] = name
		args[i] = value
		i++
	}

	// Append all of the primary key values for each column
	for _, obj := range o {
		pkeyArgs := queries.ValuesFromMapping(reflect.Indirect(reflect.ValueOf(obj)), applicationPrimaryKeyMapping)
		args = append(args, pkeyArgs...)
	}

	sql := fmt.Sprintf("UPDATE \"business_auto\".\"application\" SET %s WHERE %s",
		strmangle.SetParamNames("\"", "\"", 1, colNames),
		strmangle.WhereClauseRepeated(string(dialect.LQ), string(dialect.RQ), len(colNames)+1, applicationPrimaryKeyColumns, len(o)))

	if boil.IsDebug(ctx) {
		writer := boil.DebugWriterFrom(ctx)
		fmt.Fprintln(writer, sql)
		fmt.Fprintln(writer, args...)
	}
	result, err := exec.ExecContext(ctx, sql, args...)
	if err != nil {
		return 0, errors.Wrap(err, "business_auto: unable to update all in application slice")
	}

	rowsAff, err := result.RowsAffected()
	if err != nil {
		return 0, errors.Wrap(err, "business_auto: unable to retrieve rows affected all in update all application")
	}
	return rowsAff, nil
}

// Upsert attempts an insert using an executor, and does an update or ignore on conflict.
// See boil.Columns documentation for how to properly use updateColumns and insertColumns.
func (o *Application) Upsert(ctx context.Context, exec boil.ContextExecutor, updateOnConflict bool, conflictColumns []string, updateColumns, insertColumns boil.Columns) error {
	if o == nil {
		return errors.New("business_auto: no application provided for upsert")
	}
	if !boil.TimestampsAreSkipped(ctx) {
		currTime := time.Now().In(boil.GetLocation())

		if o.CreatedAt.IsZero() {
			o.CreatedAt = currTime
		}
		o.UpdatedAt = currTime
	}

	if err := o.doBeforeUpsertHooks(ctx, exec); err != nil {
		return err
	}

	nzDefaults := queries.NonZeroDefaultSet(applicationColumnsWithDefault, o)

	// Build cache key in-line uglily - mysql vs psql problems
	buf := strmangle.GetBuffer()
	if updateOnConflict {
		buf.WriteByte('t')
	} else {
		buf.WriteByte('f')
	}
	buf.WriteByte('.')
	for _, c := range conflictColumns {
		buf.WriteString(c)
	}
	buf.WriteByte('.')
	buf.WriteString(strconv.Itoa(updateColumns.Kind))
	for _, c := range updateColumns.Cols {
		buf.WriteString(c)
	}
	buf.WriteByte('.')
	buf.WriteString(strconv.Itoa(insertColumns.Kind))
	for _, c := range insertColumns.Cols {
		buf.WriteString(c)
	}
	buf.WriteByte('.')
	for _, c := range nzDefaults {
		buf.WriteString(c)
	}
	key := buf.String()
	strmangle.PutBuffer(buf)

	applicationUpsertCacheMut.RLock()
	cache, cached := applicationUpsertCache[key]
	applicationUpsertCacheMut.RUnlock()

	var err error

	if !cached {
		insert, ret := insertColumns.InsertColumnSet(
			applicationAllColumns,
			applicationColumnsWithDefault,
			applicationColumnsWithoutDefault,
			nzDefaults,
		)

		update := updateColumns.UpdateColumnSet(
			applicationAllColumns,
			applicationPrimaryKeyColumns,
		)

		if updateOnConflict && len(update) == 0 {
			return errors.New("business_auto: unable to upsert application, could not build update column list")
		}

		conflict := conflictColumns
		if len(conflict) == 0 {
			conflict = make([]string, len(applicationPrimaryKeyColumns))
			copy(conflict, applicationPrimaryKeyColumns)
		}
		cache.query = buildUpsertQueryPostgres(dialect, "\"business_auto\".\"application\"", updateOnConflict, ret, update, conflict, insert)

		cache.valueMapping, err = queries.BindMapping(applicationType, applicationMapping, insert)
		if err != nil {
			return err
		}
		if len(ret) != 0 {
			cache.retMapping, err = queries.BindMapping(applicationType, applicationMapping, ret)
			if err != nil {
				return err
			}
		}
	}

	value := reflect.Indirect(reflect.ValueOf(o))
	vals := queries.ValuesFromMapping(value, cache.valueMapping)
	var returns []interface{}
	if len(cache.retMapping) != 0 {
		returns = queries.PtrsFromMapping(value, cache.retMapping)
	}

	if boil.IsDebug(ctx) {
		writer := boil.DebugWriterFrom(ctx)
		fmt.Fprintln(writer, cache.query)
		fmt.Fprintln(writer, vals)
	}
	if len(cache.retMapping) != 0 {
		err = exec.QueryRowContext(ctx, cache.query, vals...).Scan(returns...)
		if errors.Is(err, sql.ErrNoRows) {
			err = nil // Postgres doesn't return anything when there's no update
		}
	} else {
		_, err = exec.ExecContext(ctx, cache.query, vals...)
	}
	if err != nil {
		return errors.Wrap(err, "business_auto: unable to upsert application")
	}

	if !cached {
		applicationUpsertCacheMut.Lock()
		applicationUpsertCache[key] = cache
		applicationUpsertCacheMut.Unlock()
	}

	return o.doAfterUpsertHooks(ctx, exec)
}

// Delete deletes a single Application record with an executor.
// Delete will match against the primary key column to find the record to delete.
func (o *Application) Delete(ctx context.Context, exec boil.ContextExecutor) (int64, error) {
	if o == nil {
		return 0, errors.New("business_auto: no Application provided for delete")
	}

	if err := o.doBeforeDeleteHooks(ctx, exec); err != nil {
		return 0, err
	}

	args := queries.ValuesFromMapping(reflect.Indirect(reflect.ValueOf(o)), applicationPrimaryKeyMapping)
	sql := "DELETE FROM \"business_auto\".\"application\" WHERE \"id\"=$1"

	if boil.IsDebug(ctx) {
		writer := boil.DebugWriterFrom(ctx)
		fmt.Fprintln(writer, sql)
		fmt.Fprintln(writer, args...)
	}
	result, err := exec.ExecContext(ctx, sql, args...)
	if err != nil {
		return 0, errors.Wrap(err, "business_auto: unable to delete from application")
	}

	rowsAff, err := result.RowsAffected()
	if err != nil {
		return 0, errors.Wrap(err, "business_auto: failed to get rows affected by delete for application")
	}

	if err := o.doAfterDeleteHooks(ctx, exec); err != nil {
		return 0, err
	}

	return rowsAff, nil
}

// DeleteAll deletes all matching rows.
func (q applicationQuery) DeleteAll(ctx context.Context, exec boil.ContextExecutor) (int64, error) {
	if q.Query == nil {
		return 0, errors.New("business_auto: no applicationQuery provided for delete all")
	}

	queries.SetDelete(q.Query)

	result, err := q.Query.ExecContext(ctx, exec)
	if err != nil {
		return 0, errors.Wrap(err, "business_auto: unable to delete all from application")
	}

	rowsAff, err := result.RowsAffected()
	if err != nil {
		return 0, errors.Wrap(err, "business_auto: failed to get rows affected by deleteall for application")
	}

	return rowsAff, nil
}

// DeleteAll deletes all rows in the slice, using an executor.
func (o ApplicationSlice) DeleteAll(ctx context.Context, exec boil.ContextExecutor) (int64, error) {
	if len(o) == 0 {
		return 0, nil
	}

	if len(applicationBeforeDeleteHooks) != 0 {
		for _, obj := range o {
			if err := obj.doBeforeDeleteHooks(ctx, exec); err != nil {
				return 0, err
			}
		}
	}

	var args []interface{}
	for _, obj := range o {
		pkeyArgs := queries.ValuesFromMapping(reflect.Indirect(reflect.ValueOf(obj)), applicationPrimaryKeyMapping)
		args = append(args, pkeyArgs...)
	}

	sql := "DELETE FROM \"business_auto\".\"application\" WHERE " +
		strmangle.WhereClauseRepeated(string(dialect.LQ), string(dialect.RQ), 1, applicationPrimaryKeyColumns, len(o))

	if boil.IsDebug(ctx) {
		writer := boil.DebugWriterFrom(ctx)
		fmt.Fprintln(writer, sql)
		fmt.Fprintln(writer, args)
	}
	result, err := exec.ExecContext(ctx, sql, args...)
	if err != nil {
		return 0, errors.Wrap(err, "business_auto: unable to delete all from application slice")
	}

	rowsAff, err := result.RowsAffected()
	if err != nil {
		return 0, errors.Wrap(err, "business_auto: failed to get rows affected by deleteall for application")
	}

	if len(applicationAfterDeleteHooks) != 0 {
		for _, obj := range o {
			if err := obj.doAfterDeleteHooks(ctx, exec); err != nil {
				return 0, err
			}
		}
	}

	return rowsAff, nil
}

// Reload refetches the object from the database
// using the primary keys with an executor.
func (o *Application) Reload(ctx context.Context, exec boil.ContextExecutor) error {
	ret, err := FindApplication(ctx, exec, o.ID)
	if err != nil {
		return err
	}

	*o = *ret
	return nil
}

// ReloadAll refetches every row with matching primary key column values
// and overwrites the original object slice with the newly updated slice.
func (o *ApplicationSlice) ReloadAll(ctx context.Context, exec boil.ContextExecutor) error {
	if o == nil || len(*o) == 0 {
		return nil
	}

	slice := ApplicationSlice{}
	var args []interface{}
	for _, obj := range *o {
		pkeyArgs := queries.ValuesFromMapping(reflect.Indirect(reflect.ValueOf(obj)), applicationPrimaryKeyMapping)
		args = append(args, pkeyArgs...)
	}

	sql := "SELECT \"business_auto\".\"application\".* FROM \"business_auto\".\"application\" WHERE " +
		strmangle.WhereClauseRepeated(string(dialect.LQ), string(dialect.RQ), 1, applicationPrimaryKeyColumns, len(*o))

	q := queries.Raw(sql, args...)

	err := q.Bind(ctx, exec, &slice)
	if err != nil {
		return errors.Wrap(err, "business_auto: unable to reload all in ApplicationSlice")
	}

	*o = slice

	return nil
}

// ApplicationExists checks if the Application row exists.
func ApplicationExists(ctx context.Context, exec boil.ContextExecutor, iD string) (bool, error) {
	var exists bool
	sql := "select exists(select 1 from \"business_auto\".\"application\" where \"id\"=$1 limit 1)"

	if boil.IsDebug(ctx) {
		writer := boil.DebugWriterFrom(ctx)
		fmt.Fprintln(writer, sql)
		fmt.Fprintln(writer, iD)
	}
	row := exec.QueryRowContext(ctx, sql, iD)

	err := row.Scan(&exists)
	if err != nil {
		return false, errors.Wrap(err, "business_auto: unable to check if application exists")
	}

	return exists, nil
}
