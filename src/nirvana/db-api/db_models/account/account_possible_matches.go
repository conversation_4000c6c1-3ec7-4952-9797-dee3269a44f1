// Code generated by SQLBoiler (https://github.com/volatiletech/sqlboiler). DO NOT EDIT.
// This file is meant to be re-generated in place and/or deleted at any time.

package account

import (
	"context"
	"database/sql"
	"fmt"
	"reflect"
	"strconv"
	"strings"
	"sync"
	"time"

	"github.com/friendsofgo/errors"
	"github.com/volatiletech/null/v8"
	"github.com/volatiletech/sqlboiler/v4/boil"
	"github.com/volatiletech/sqlboiler/v4/queries"
	"github.com/volatiletech/sqlboiler/v4/queries/qm"
	"github.com/volatiletech/sqlboiler/v4/queries/qmhelper"
	"github.com/volatiletech/strmangle"
)

// AccountPossibleMatch is an object representing the database table.
type AccountPossibleMatch struct {
	MatchID            string      `boil:"match_id" json:"match_id" toml:"match_id" yaml:"match_id"`
	CandidateAccountID string      `boil:"candidate_account_id" json:"candidate_account_id" toml:"candidate_account_id" yaml:"candidate_account_id"`
	MatchedAccountID   string      `boil:"matched_account_id" json:"matched_account_id" toml:"matched_account_id" yaml:"matched_account_id"`
	MatchConfidence    float64     `boil:"match_confidence" json:"match_confidence" toml:"match_confidence" yaml:"match_confidence"`
	MatchMethod        string      `boil:"match_method" json:"match_method" toml:"match_method" yaml:"match_method"`
	IdentifiersMatched null.JSON   `boil:"identifiers_matched" json:"identifiers_matched,omitempty" toml:"identifiers_matched" yaml:"identifiers_matched,omitempty"`
	Status             string      `boil:"status" json:"status" toml:"status" yaml:"status"`
	FlaggedBy          null.String `boil:"flagged_by" json:"flagged_by,omitempty" toml:"flagged_by" yaml:"flagged_by,omitempty"`
	FlaggedAt          time.Time   `boil:"flagged_at" json:"flagged_at" toml:"flagged_at" yaml:"flagged_at"`
	ReviewedBy         null.String `boil:"reviewed_by" json:"reviewed_by,omitempty" toml:"reviewed_by" yaml:"reviewed_by,omitempty"`
	ReviewedAt         null.Time   `boil:"reviewed_at" json:"reviewed_at,omitempty" toml:"reviewed_at" yaml:"reviewed_at,omitempty"`
	ReviewNotes        null.String `boil:"review_notes" json:"review_notes,omitempty" toml:"review_notes" yaml:"review_notes,omitempty"`
	CreatedAt          time.Time   `boil:"created_at" json:"created_at" toml:"created_at" yaml:"created_at"`
	UpdatedAt          time.Time   `boil:"updated_at" json:"updated_at" toml:"updated_at" yaml:"updated_at"`

	R *accountPossibleMatchR `boil:"" json:"" toml:"" yaml:""`
	L accountPossibleMatchL  `boil:"-" json:"-" toml:"-" yaml:"-"`
}

var AccountPossibleMatchColumns = struct {
	MatchID            string
	CandidateAccountID string
	MatchedAccountID   string
	MatchConfidence    string
	MatchMethod        string
	IdentifiersMatched string
	Status             string
	FlaggedBy          string
	FlaggedAt          string
	ReviewedBy         string
	ReviewedAt         string
	ReviewNotes        string
	CreatedAt          string
	UpdatedAt          string
}{
	MatchID:            "match_id",
	CandidateAccountID: "candidate_account_id",
	MatchedAccountID:   "matched_account_id",
	MatchConfidence:    "match_confidence",
	MatchMethod:        "match_method",
	IdentifiersMatched: "identifiers_matched",
	Status:             "status",
	FlaggedBy:          "flagged_by",
	FlaggedAt:          "flagged_at",
	ReviewedBy:         "reviewed_by",
	ReviewedAt:         "reviewed_at",
	ReviewNotes:        "review_notes",
	CreatedAt:          "created_at",
	UpdatedAt:          "updated_at",
}

var AccountPossibleMatchTableColumns = struct {
	MatchID            string
	CandidateAccountID string
	MatchedAccountID   string
	MatchConfidence    string
	MatchMethod        string
	IdentifiersMatched string
	Status             string
	FlaggedBy          string
	FlaggedAt          string
	ReviewedBy         string
	ReviewedAt         string
	ReviewNotes        string
	CreatedAt          string
	UpdatedAt          string
}{
	MatchID:            "account_possible_matches.match_id",
	CandidateAccountID: "account_possible_matches.candidate_account_id",
	MatchedAccountID:   "account_possible_matches.matched_account_id",
	MatchConfidence:    "account_possible_matches.match_confidence",
	MatchMethod:        "account_possible_matches.match_method",
	IdentifiersMatched: "account_possible_matches.identifiers_matched",
	Status:             "account_possible_matches.status",
	FlaggedBy:          "account_possible_matches.flagged_by",
	FlaggedAt:          "account_possible_matches.flagged_at",
	ReviewedBy:         "account_possible_matches.reviewed_by",
	ReviewedAt:         "account_possible_matches.reviewed_at",
	ReviewNotes:        "account_possible_matches.review_notes",
	CreatedAt:          "account_possible_matches.created_at",
	UpdatedAt:          "account_possible_matches.updated_at",
}

// Generated where

type whereHelperfloat64 struct{ field string }

func (w whereHelperfloat64) EQ(x float64) qm.QueryMod { return qmhelper.Where(w.field, qmhelper.EQ, x) }
func (w whereHelperfloat64) NEQ(x float64) qm.QueryMod {
	return qmhelper.Where(w.field, qmhelper.NEQ, x)
}
func (w whereHelperfloat64) LT(x float64) qm.QueryMod { return qmhelper.Where(w.field, qmhelper.LT, x) }
func (w whereHelperfloat64) LTE(x float64) qm.QueryMod {
	return qmhelper.Where(w.field, qmhelper.LTE, x)
}
func (w whereHelperfloat64) GT(x float64) qm.QueryMod { return qmhelper.Where(w.field, qmhelper.GT, x) }
func (w whereHelperfloat64) GTE(x float64) qm.QueryMod {
	return qmhelper.Where(w.field, qmhelper.GTE, x)
}
func (w whereHelperfloat64) IN(slice []float64) qm.QueryMod {
	values := make([]interface{}, 0, len(slice))
	for _, value := range slice {
		values = append(values, value)
	}
	return qm.WhereIn(fmt.Sprintf("%s IN ?", w.field), values...)
}
func (w whereHelperfloat64) NIN(slice []float64) qm.QueryMod {
	values := make([]interface{}, 0, len(slice))
	for _, value := range slice {
		values = append(values, value)
	}
	return qm.WhereNotIn(fmt.Sprintf("%s NOT IN ?", w.field), values...)
}

type whereHelpernull_JSON struct{ field string }

func (w whereHelpernull_JSON) EQ(x null.JSON) qm.QueryMod {
	return qmhelper.WhereNullEQ(w.field, false, x)
}
func (w whereHelpernull_JSON) NEQ(x null.JSON) qm.QueryMod {
	return qmhelper.WhereNullEQ(w.field, true, x)
}
func (w whereHelpernull_JSON) LT(x null.JSON) qm.QueryMod {
	return qmhelper.Where(w.field, qmhelper.LT, x)
}
func (w whereHelpernull_JSON) LTE(x null.JSON) qm.QueryMod {
	return qmhelper.Where(w.field, qmhelper.LTE, x)
}
func (w whereHelpernull_JSON) GT(x null.JSON) qm.QueryMod {
	return qmhelper.Where(w.field, qmhelper.GT, x)
}
func (w whereHelpernull_JSON) GTE(x null.JSON) qm.QueryMod {
	return qmhelper.Where(w.field, qmhelper.GTE, x)
}

func (w whereHelpernull_JSON) IsNull() qm.QueryMod    { return qmhelper.WhereIsNull(w.field) }
func (w whereHelpernull_JSON) IsNotNull() qm.QueryMod { return qmhelper.WhereIsNotNull(w.field) }

type whereHelpernull_Time struct{ field string }

func (w whereHelpernull_Time) EQ(x null.Time) qm.QueryMod {
	return qmhelper.WhereNullEQ(w.field, false, x)
}
func (w whereHelpernull_Time) NEQ(x null.Time) qm.QueryMod {
	return qmhelper.WhereNullEQ(w.field, true, x)
}
func (w whereHelpernull_Time) LT(x null.Time) qm.QueryMod {
	return qmhelper.Where(w.field, qmhelper.LT, x)
}
func (w whereHelpernull_Time) LTE(x null.Time) qm.QueryMod {
	return qmhelper.Where(w.field, qmhelper.LTE, x)
}
func (w whereHelpernull_Time) GT(x null.Time) qm.QueryMod {
	return qmhelper.Where(w.field, qmhelper.GT, x)
}
func (w whereHelpernull_Time) GTE(x null.Time) qm.QueryMod {
	return qmhelper.Where(w.field, qmhelper.GTE, x)
}

func (w whereHelpernull_Time) IsNull() qm.QueryMod    { return qmhelper.WhereIsNull(w.field) }
func (w whereHelpernull_Time) IsNotNull() qm.QueryMod { return qmhelper.WhereIsNotNull(w.field) }

var AccountPossibleMatchWhere = struct {
	MatchID            whereHelperstring
	CandidateAccountID whereHelperstring
	MatchedAccountID   whereHelperstring
	MatchConfidence    whereHelperfloat64
	MatchMethod        whereHelperstring
	IdentifiersMatched whereHelpernull_JSON
	Status             whereHelperstring
	FlaggedBy          whereHelpernull_String
	FlaggedAt          whereHelpertime_Time
	ReviewedBy         whereHelpernull_String
	ReviewedAt         whereHelpernull_Time
	ReviewNotes        whereHelpernull_String
	CreatedAt          whereHelpertime_Time
	UpdatedAt          whereHelpertime_Time
}{
	MatchID:            whereHelperstring{field: "\"account\".\"account_possible_matches\".\"match_id\""},
	CandidateAccountID: whereHelperstring{field: "\"account\".\"account_possible_matches\".\"candidate_account_id\""},
	MatchedAccountID:   whereHelperstring{field: "\"account\".\"account_possible_matches\".\"matched_account_id\""},
	MatchConfidence:    whereHelperfloat64{field: "\"account\".\"account_possible_matches\".\"match_confidence\""},
	MatchMethod:        whereHelperstring{field: "\"account\".\"account_possible_matches\".\"match_method\""},
	IdentifiersMatched: whereHelpernull_JSON{field: "\"account\".\"account_possible_matches\".\"identifiers_matched\""},
	Status:             whereHelperstring{field: "\"account\".\"account_possible_matches\".\"status\""},
	FlaggedBy:          whereHelpernull_String{field: "\"account\".\"account_possible_matches\".\"flagged_by\""},
	FlaggedAt:          whereHelpertime_Time{field: "\"account\".\"account_possible_matches\".\"flagged_at\""},
	ReviewedBy:         whereHelpernull_String{field: "\"account\".\"account_possible_matches\".\"reviewed_by\""},
	ReviewedAt:         whereHelpernull_Time{field: "\"account\".\"account_possible_matches\".\"reviewed_at\""},
	ReviewNotes:        whereHelpernull_String{field: "\"account\".\"account_possible_matches\".\"review_notes\""},
	CreatedAt:          whereHelpertime_Time{field: "\"account\".\"account_possible_matches\".\"created_at\""},
	UpdatedAt:          whereHelpertime_Time{field: "\"account\".\"account_possible_matches\".\"updated_at\""},
}

// AccountPossibleMatchRels is where relationship names are stored.
var AccountPossibleMatchRels = struct {
	CandidateAccount string
	MatchedAccount   string
}{
	CandidateAccount: "CandidateAccount",
	MatchedAccount:   "MatchedAccount",
}

// accountPossibleMatchR is where relationships are stored.
type accountPossibleMatchR struct {
	CandidateAccount *Account `boil:"CandidateAccount" json:"CandidateAccount" toml:"CandidateAccount" yaml:"CandidateAccount"`
	MatchedAccount   *Account `boil:"MatchedAccount" json:"MatchedAccount" toml:"MatchedAccount" yaml:"MatchedAccount"`
}

// NewStruct creates a new relationship struct
func (*accountPossibleMatchR) NewStruct() *accountPossibleMatchR {
	return &accountPossibleMatchR{}
}

// accountPossibleMatchL is where Load methods for each relationship are stored.
type accountPossibleMatchL struct{}

var (
	accountPossibleMatchAllColumns            = []string{"match_id", "candidate_account_id", "matched_account_id", "match_confidence", "match_method", "identifiers_matched", "status", "flagged_by", "flagged_at", "reviewed_by", "reviewed_at", "review_notes", "created_at", "updated_at"}
	accountPossibleMatchColumnsWithoutDefault = []string{"match_id", "candidate_account_id", "matched_account_id", "match_confidence", "match_method"}
	accountPossibleMatchColumnsWithDefault    = []string{"identifiers_matched", "status", "flagged_by", "flagged_at", "reviewed_by", "reviewed_at", "review_notes", "created_at", "updated_at"}
	accountPossibleMatchPrimaryKeyColumns     = []string{"match_id"}
	accountPossibleMatchGeneratedColumns      = []string{}
)

type (
	// AccountPossibleMatchSlice is an alias for a slice of pointers to AccountPossibleMatch.
	// This should almost always be used instead of []AccountPossibleMatch.
	AccountPossibleMatchSlice []*AccountPossibleMatch
	// AccountPossibleMatchHook is the signature for custom AccountPossibleMatch hook methods
	AccountPossibleMatchHook func(context.Context, boil.ContextExecutor, *AccountPossibleMatch) error

	accountPossibleMatchQuery struct {
		*queries.Query
	}
)

// Cache for insert, update and upsert
var (
	accountPossibleMatchType                 = reflect.TypeOf(&AccountPossibleMatch{})
	accountPossibleMatchMapping              = queries.MakeStructMapping(accountPossibleMatchType)
	accountPossibleMatchPrimaryKeyMapping, _ = queries.BindMapping(accountPossibleMatchType, accountPossibleMatchMapping, accountPossibleMatchPrimaryKeyColumns)
	accountPossibleMatchInsertCacheMut       sync.RWMutex
	accountPossibleMatchInsertCache          = make(map[string]insertCache)
	accountPossibleMatchUpdateCacheMut       sync.RWMutex
	accountPossibleMatchUpdateCache          = make(map[string]updateCache)
	accountPossibleMatchUpsertCacheMut       sync.RWMutex
	accountPossibleMatchUpsertCache          = make(map[string]insertCache)
)

var (
	// Force time package dependency for automated UpdatedAt/CreatedAt.
	_ = time.Second
	// Force qmhelper dependency for where clause generation (which doesn't
	// always happen)
	_ = qmhelper.Where
)

var accountPossibleMatchAfterSelectHooks []AccountPossibleMatchHook

var accountPossibleMatchBeforeInsertHooks []AccountPossibleMatchHook
var accountPossibleMatchAfterInsertHooks []AccountPossibleMatchHook

var accountPossibleMatchBeforeUpdateHooks []AccountPossibleMatchHook
var accountPossibleMatchAfterUpdateHooks []AccountPossibleMatchHook

var accountPossibleMatchBeforeDeleteHooks []AccountPossibleMatchHook
var accountPossibleMatchAfterDeleteHooks []AccountPossibleMatchHook

var accountPossibleMatchBeforeUpsertHooks []AccountPossibleMatchHook
var accountPossibleMatchAfterUpsertHooks []AccountPossibleMatchHook

// doAfterSelectHooks executes all "after Select" hooks.
func (o *AccountPossibleMatch) doAfterSelectHooks(ctx context.Context, exec boil.ContextExecutor) (err error) {
	if boil.HooksAreSkipped(ctx) {
		return nil
	}

	for _, hook := range accountPossibleMatchAfterSelectHooks {
		if err := hook(ctx, exec, o); err != nil {
			return err
		}
	}

	return nil
}

// doBeforeInsertHooks executes all "before insert" hooks.
func (o *AccountPossibleMatch) doBeforeInsertHooks(ctx context.Context, exec boil.ContextExecutor) (err error) {
	if boil.HooksAreSkipped(ctx) {
		return nil
	}

	for _, hook := range accountPossibleMatchBeforeInsertHooks {
		if err := hook(ctx, exec, o); err != nil {
			return err
		}
	}

	return nil
}

// doAfterInsertHooks executes all "after Insert" hooks.
func (o *AccountPossibleMatch) doAfterInsertHooks(ctx context.Context, exec boil.ContextExecutor) (err error) {
	if boil.HooksAreSkipped(ctx) {
		return nil
	}

	for _, hook := range accountPossibleMatchAfterInsertHooks {
		if err := hook(ctx, exec, o); err != nil {
			return err
		}
	}

	return nil
}

// doBeforeUpdateHooks executes all "before Update" hooks.
func (o *AccountPossibleMatch) doBeforeUpdateHooks(ctx context.Context, exec boil.ContextExecutor) (err error) {
	if boil.HooksAreSkipped(ctx) {
		return nil
	}

	for _, hook := range accountPossibleMatchBeforeUpdateHooks {
		if err := hook(ctx, exec, o); err != nil {
			return err
		}
	}

	return nil
}

// doAfterUpdateHooks executes all "after Update" hooks.
func (o *AccountPossibleMatch) doAfterUpdateHooks(ctx context.Context, exec boil.ContextExecutor) (err error) {
	if boil.HooksAreSkipped(ctx) {
		return nil
	}

	for _, hook := range accountPossibleMatchAfterUpdateHooks {
		if err := hook(ctx, exec, o); err != nil {
			return err
		}
	}

	return nil
}

// doBeforeDeleteHooks executes all "before Delete" hooks.
func (o *AccountPossibleMatch) doBeforeDeleteHooks(ctx context.Context, exec boil.ContextExecutor) (err error) {
	if boil.HooksAreSkipped(ctx) {
		return nil
	}

	for _, hook := range accountPossibleMatchBeforeDeleteHooks {
		if err := hook(ctx, exec, o); err != nil {
			return err
		}
	}

	return nil
}

// doAfterDeleteHooks executes all "after Delete" hooks.
func (o *AccountPossibleMatch) doAfterDeleteHooks(ctx context.Context, exec boil.ContextExecutor) (err error) {
	if boil.HooksAreSkipped(ctx) {
		return nil
	}

	for _, hook := range accountPossibleMatchAfterDeleteHooks {
		if err := hook(ctx, exec, o); err != nil {
			return err
		}
	}

	return nil
}

// doBeforeUpsertHooks executes all "before Upsert" hooks.
func (o *AccountPossibleMatch) doBeforeUpsertHooks(ctx context.Context, exec boil.ContextExecutor) (err error) {
	if boil.HooksAreSkipped(ctx) {
		return nil
	}

	for _, hook := range accountPossibleMatchBeforeUpsertHooks {
		if err := hook(ctx, exec, o); err != nil {
			return err
		}
	}

	return nil
}

// doAfterUpsertHooks executes all "after Upsert" hooks.
func (o *AccountPossibleMatch) doAfterUpsertHooks(ctx context.Context, exec boil.ContextExecutor) (err error) {
	if boil.HooksAreSkipped(ctx) {
		return nil
	}

	for _, hook := range accountPossibleMatchAfterUpsertHooks {
		if err := hook(ctx, exec, o); err != nil {
			return err
		}
	}

	return nil
}

// AddAccountPossibleMatchHook registers your hook function for all future operations.
func AddAccountPossibleMatchHook(hookPoint boil.HookPoint, accountPossibleMatchHook AccountPossibleMatchHook) {
	switch hookPoint {
	case boil.AfterSelectHook:
		accountPossibleMatchAfterSelectHooks = append(accountPossibleMatchAfterSelectHooks, accountPossibleMatchHook)
	case boil.BeforeInsertHook:
		accountPossibleMatchBeforeInsertHooks = append(accountPossibleMatchBeforeInsertHooks, accountPossibleMatchHook)
	case boil.AfterInsertHook:
		accountPossibleMatchAfterInsertHooks = append(accountPossibleMatchAfterInsertHooks, accountPossibleMatchHook)
	case boil.BeforeUpdateHook:
		accountPossibleMatchBeforeUpdateHooks = append(accountPossibleMatchBeforeUpdateHooks, accountPossibleMatchHook)
	case boil.AfterUpdateHook:
		accountPossibleMatchAfterUpdateHooks = append(accountPossibleMatchAfterUpdateHooks, accountPossibleMatchHook)
	case boil.BeforeDeleteHook:
		accountPossibleMatchBeforeDeleteHooks = append(accountPossibleMatchBeforeDeleteHooks, accountPossibleMatchHook)
	case boil.AfterDeleteHook:
		accountPossibleMatchAfterDeleteHooks = append(accountPossibleMatchAfterDeleteHooks, accountPossibleMatchHook)
	case boil.BeforeUpsertHook:
		accountPossibleMatchBeforeUpsertHooks = append(accountPossibleMatchBeforeUpsertHooks, accountPossibleMatchHook)
	case boil.AfterUpsertHook:
		accountPossibleMatchAfterUpsertHooks = append(accountPossibleMatchAfterUpsertHooks, accountPossibleMatchHook)
	}
}

// One returns a single accountPossibleMatch record from the query.
func (q accountPossibleMatchQuery) One(ctx context.Context, exec boil.ContextExecutor) (*AccountPossibleMatch, error) {
	o := &AccountPossibleMatch{}

	queries.SetLimit(q.Query, 1)

	err := q.Bind(ctx, exec, o)
	if err != nil {
		if errors.Is(err, sql.ErrNoRows) {
			return nil, sql.ErrNoRows
		}
		return nil, errors.Wrap(err, "account: failed to execute a one query for account_possible_matches")
	}

	if err := o.doAfterSelectHooks(ctx, exec); err != nil {
		return o, err
	}

	return o, nil
}

// All returns all AccountPossibleMatch records from the query.
func (q accountPossibleMatchQuery) All(ctx context.Context, exec boil.ContextExecutor) (AccountPossibleMatchSlice, error) {
	var o []*AccountPossibleMatch

	err := q.Bind(ctx, exec, &o)
	if err != nil {
		return nil, errors.Wrap(err, "account: failed to assign all query results to AccountPossibleMatch slice")
	}

	if len(accountPossibleMatchAfterSelectHooks) != 0 {
		for _, obj := range o {
			if err := obj.doAfterSelectHooks(ctx, exec); err != nil {
				return o, err
			}
		}
	}

	return o, nil
}

// Count returns the count of all AccountPossibleMatch records in the query.
func (q accountPossibleMatchQuery) Count(ctx context.Context, exec boil.ContextExecutor) (int64, error) {
	var count int64

	queries.SetSelect(q.Query, nil)
	queries.SetCount(q.Query)

	err := q.Query.QueryRowContext(ctx, exec).Scan(&count)
	if err != nil {
		return 0, errors.Wrap(err, "account: failed to count account_possible_matches rows")
	}

	return count, nil
}

// Exists checks if the row exists in the table.
func (q accountPossibleMatchQuery) Exists(ctx context.Context, exec boil.ContextExecutor) (bool, error) {
	var count int64

	queries.SetSelect(q.Query, nil)
	queries.SetCount(q.Query)
	queries.SetLimit(q.Query, 1)

	err := q.Query.QueryRowContext(ctx, exec).Scan(&count)
	if err != nil {
		return false, errors.Wrap(err, "account: failed to check if account_possible_matches exists")
	}

	return count > 0, nil
}

// CandidateAccount pointed to by the foreign key.
func (o *AccountPossibleMatch) CandidateAccount(mods ...qm.QueryMod) accountQuery {
	queryMods := []qm.QueryMod{
		qm.Where("\"account_id\" = ?", o.CandidateAccountID),
	}

	queryMods = append(queryMods, mods...)

	return Accounts(queryMods...)
}

// MatchedAccount pointed to by the foreign key.
func (o *AccountPossibleMatch) MatchedAccount(mods ...qm.QueryMod) accountQuery {
	queryMods := []qm.QueryMod{
		qm.Where("\"account_id\" = ?", o.MatchedAccountID),
	}

	queryMods = append(queryMods, mods...)

	return Accounts(queryMods...)
}

// LoadCandidateAccount allows an eager lookup of values, cached into the
// loaded structs of the objects. This is for an N-1 relationship.
func (accountPossibleMatchL) LoadCandidateAccount(ctx context.Context, e boil.ContextExecutor, singular bool, maybeAccountPossibleMatch interface{}, mods queries.Applicator) error {
	var slice []*AccountPossibleMatch
	var object *AccountPossibleMatch

	if singular {
		object = maybeAccountPossibleMatch.(*AccountPossibleMatch)
	} else {
		slice = *maybeAccountPossibleMatch.(*[]*AccountPossibleMatch)
	}

	args := make([]interface{}, 0, 1)
	if singular {
		if object.R == nil {
			object.R = &accountPossibleMatchR{}
		}
		args = append(args, object.CandidateAccountID)

	} else {
	Outer:
		for _, obj := range slice {
			if obj.R == nil {
				obj.R = &accountPossibleMatchR{}
			}

			for _, a := range args {
				if a == obj.CandidateAccountID {
					continue Outer
				}
			}

			args = append(args, obj.CandidateAccountID)

		}
	}

	if len(args) == 0 {
		return nil
	}

	query := NewQuery(
		qm.From(`account.accounts`),
		qm.WhereIn(`account.accounts.account_id in ?`, args...),
	)
	if mods != nil {
		mods.Apply(query)
	}

	results, err := query.QueryContext(ctx, e)
	if err != nil {
		return errors.Wrap(err, "failed to eager load Account")
	}

	var resultSlice []*Account
	if err = queries.Bind(results, &resultSlice); err != nil {
		return errors.Wrap(err, "failed to bind eager loaded slice Account")
	}

	if err = results.Close(); err != nil {
		return errors.Wrap(err, "failed to close results of eager load for accounts")
	}
	if err = results.Err(); err != nil {
		return errors.Wrap(err, "error occurred during iteration of eager loaded relations for accounts")
	}

	if len(accountPossibleMatchAfterSelectHooks) != 0 {
		for _, obj := range resultSlice {
			if err := obj.doAfterSelectHooks(ctx, e); err != nil {
				return err
			}
		}
	}

	if len(resultSlice) == 0 {
		return nil
	}

	if singular {
		foreign := resultSlice[0]
		object.R.CandidateAccount = foreign
		if foreign.R == nil {
			foreign.R = &accountR{}
		}
		foreign.R.CandidateAccountAccountPossibleMatches = append(foreign.R.CandidateAccountAccountPossibleMatches, object)
		return nil
	}

	for _, local := range slice {
		for _, foreign := range resultSlice {
			if local.CandidateAccountID == foreign.AccountID {
				local.R.CandidateAccount = foreign
				if foreign.R == nil {
					foreign.R = &accountR{}
				}
				foreign.R.CandidateAccountAccountPossibleMatches = append(foreign.R.CandidateAccountAccountPossibleMatches, local)
				break
			}
		}
	}

	return nil
}

// LoadMatchedAccount allows an eager lookup of values, cached into the
// loaded structs of the objects. This is for an N-1 relationship.
func (accountPossibleMatchL) LoadMatchedAccount(ctx context.Context, e boil.ContextExecutor, singular bool, maybeAccountPossibleMatch interface{}, mods queries.Applicator) error {
	var slice []*AccountPossibleMatch
	var object *AccountPossibleMatch

	if singular {
		object = maybeAccountPossibleMatch.(*AccountPossibleMatch)
	} else {
		slice = *maybeAccountPossibleMatch.(*[]*AccountPossibleMatch)
	}

	args := make([]interface{}, 0, 1)
	if singular {
		if object.R == nil {
			object.R = &accountPossibleMatchR{}
		}
		args = append(args, object.MatchedAccountID)

	} else {
	Outer:
		for _, obj := range slice {
			if obj.R == nil {
				obj.R = &accountPossibleMatchR{}
			}

			for _, a := range args {
				if a == obj.MatchedAccountID {
					continue Outer
				}
			}

			args = append(args, obj.MatchedAccountID)

		}
	}

	if len(args) == 0 {
		return nil
	}

	query := NewQuery(
		qm.From(`account.accounts`),
		qm.WhereIn(`account.accounts.account_id in ?`, args...),
	)
	if mods != nil {
		mods.Apply(query)
	}

	results, err := query.QueryContext(ctx, e)
	if err != nil {
		return errors.Wrap(err, "failed to eager load Account")
	}

	var resultSlice []*Account
	if err = queries.Bind(results, &resultSlice); err != nil {
		return errors.Wrap(err, "failed to bind eager loaded slice Account")
	}

	if err = results.Close(); err != nil {
		return errors.Wrap(err, "failed to close results of eager load for accounts")
	}
	if err = results.Err(); err != nil {
		return errors.Wrap(err, "error occurred during iteration of eager loaded relations for accounts")
	}

	if len(accountPossibleMatchAfterSelectHooks) != 0 {
		for _, obj := range resultSlice {
			if err := obj.doAfterSelectHooks(ctx, e); err != nil {
				return err
			}
		}
	}

	if len(resultSlice) == 0 {
		return nil
	}

	if singular {
		foreign := resultSlice[0]
		object.R.MatchedAccount = foreign
		if foreign.R == nil {
			foreign.R = &accountR{}
		}
		foreign.R.MatchedAccountAccountPossibleMatches = append(foreign.R.MatchedAccountAccountPossibleMatches, object)
		return nil
	}

	for _, local := range slice {
		for _, foreign := range resultSlice {
			if local.MatchedAccountID == foreign.AccountID {
				local.R.MatchedAccount = foreign
				if foreign.R == nil {
					foreign.R = &accountR{}
				}
				foreign.R.MatchedAccountAccountPossibleMatches = append(foreign.R.MatchedAccountAccountPossibleMatches, local)
				break
			}
		}
	}

	return nil
}

// SetCandidateAccount of the accountPossibleMatch to the related item.
// Sets o.R.CandidateAccount to related.
// Adds o to related.R.CandidateAccountAccountPossibleMatches.
func (o *AccountPossibleMatch) SetCandidateAccount(ctx context.Context, exec boil.ContextExecutor, insert bool, related *Account) error {
	var err error
	if insert {
		if err = related.Insert(ctx, exec, boil.Infer()); err != nil {
			return errors.Wrap(err, "failed to insert into foreign table")
		}
	}

	updateQuery := fmt.Sprintf(
		"UPDATE \"account\".\"account_possible_matches\" SET %s WHERE %s",
		strmangle.SetParamNames("\"", "\"", 1, []string{"candidate_account_id"}),
		strmangle.WhereClause("\"", "\"", 2, accountPossibleMatchPrimaryKeyColumns),
	)
	values := []interface{}{related.AccountID, o.MatchID}

	if boil.IsDebug(ctx) {
		writer := boil.DebugWriterFrom(ctx)
		fmt.Fprintln(writer, updateQuery)
		fmt.Fprintln(writer, values)
	}
	if _, err = exec.ExecContext(ctx, updateQuery, values...); err != nil {
		return errors.Wrap(err, "failed to update local table")
	}

	o.CandidateAccountID = related.AccountID
	if o.R == nil {
		o.R = &accountPossibleMatchR{
			CandidateAccount: related,
		}
	} else {
		o.R.CandidateAccount = related
	}

	if related.R == nil {
		related.R = &accountR{
			CandidateAccountAccountPossibleMatches: AccountPossibleMatchSlice{o},
		}
	} else {
		related.R.CandidateAccountAccountPossibleMatches = append(related.R.CandidateAccountAccountPossibleMatches, o)
	}

	return nil
}

// SetMatchedAccount of the accountPossibleMatch to the related item.
// Sets o.R.MatchedAccount to related.
// Adds o to related.R.MatchedAccountAccountPossibleMatches.
func (o *AccountPossibleMatch) SetMatchedAccount(ctx context.Context, exec boil.ContextExecutor, insert bool, related *Account) error {
	var err error
	if insert {
		if err = related.Insert(ctx, exec, boil.Infer()); err != nil {
			return errors.Wrap(err, "failed to insert into foreign table")
		}
	}

	updateQuery := fmt.Sprintf(
		"UPDATE \"account\".\"account_possible_matches\" SET %s WHERE %s",
		strmangle.SetParamNames("\"", "\"", 1, []string{"matched_account_id"}),
		strmangle.WhereClause("\"", "\"", 2, accountPossibleMatchPrimaryKeyColumns),
	)
	values := []interface{}{related.AccountID, o.MatchID}

	if boil.IsDebug(ctx) {
		writer := boil.DebugWriterFrom(ctx)
		fmt.Fprintln(writer, updateQuery)
		fmt.Fprintln(writer, values)
	}
	if _, err = exec.ExecContext(ctx, updateQuery, values...); err != nil {
		return errors.Wrap(err, "failed to update local table")
	}

	o.MatchedAccountID = related.AccountID
	if o.R == nil {
		o.R = &accountPossibleMatchR{
			MatchedAccount: related,
		}
	} else {
		o.R.MatchedAccount = related
	}

	if related.R == nil {
		related.R = &accountR{
			MatchedAccountAccountPossibleMatches: AccountPossibleMatchSlice{o},
		}
	} else {
		related.R.MatchedAccountAccountPossibleMatches = append(related.R.MatchedAccountAccountPossibleMatches, o)
	}

	return nil
}

// AccountPossibleMatches retrieves all the records using an executor.
func AccountPossibleMatches(mods ...qm.QueryMod) accountPossibleMatchQuery {
	mods = append(mods, qm.From("\"account\".\"account_possible_matches\""))
	q := NewQuery(mods...)
	if len(queries.GetSelect(q)) == 0 {
		queries.SetSelect(q, []string{"\"account\".\"account_possible_matches\".*"})
	}

	return accountPossibleMatchQuery{q}
}

// FindAccountPossibleMatch retrieves a single record by ID with an executor.
// If selectCols is empty Find will return all columns.
func FindAccountPossibleMatch(ctx context.Context, exec boil.ContextExecutor, matchID string, selectCols ...string) (*AccountPossibleMatch, error) {
	accountPossibleMatchObj := &AccountPossibleMatch{}

	sel := "*"
	if len(selectCols) > 0 {
		sel = strings.Join(strmangle.IdentQuoteSlice(dialect.LQ, dialect.RQ, selectCols), ",")
	}
	query := fmt.Sprintf(
		"select %s from \"account\".\"account_possible_matches\" where \"match_id\"=$1", sel,
	)

	q := queries.Raw(query, matchID)

	err := q.Bind(ctx, exec, accountPossibleMatchObj)
	if err != nil {
		if errors.Is(err, sql.ErrNoRows) {
			return nil, sql.ErrNoRows
		}
		return nil, errors.Wrap(err, "account: unable to select from account_possible_matches")
	}

	if err = accountPossibleMatchObj.doAfterSelectHooks(ctx, exec); err != nil {
		return accountPossibleMatchObj, err
	}

	return accountPossibleMatchObj, nil
}

// Insert a single record using an executor.
// See boil.Columns.InsertColumnSet documentation to understand column list inference for inserts.
func (o *AccountPossibleMatch) Insert(ctx context.Context, exec boil.ContextExecutor, columns boil.Columns) error {
	if o == nil {
		return errors.New("account: no account_possible_matches provided for insertion")
	}

	var err error
	if !boil.TimestampsAreSkipped(ctx) {
		currTime := time.Now().In(boil.GetLocation())

		if o.CreatedAt.IsZero() {
			o.CreatedAt = currTime
		}
		if o.UpdatedAt.IsZero() {
			o.UpdatedAt = currTime
		}
	}

	if err := o.doBeforeInsertHooks(ctx, exec); err != nil {
		return err
	}

	nzDefaults := queries.NonZeroDefaultSet(accountPossibleMatchColumnsWithDefault, o)

	key := makeCacheKey(columns, nzDefaults)
	accountPossibleMatchInsertCacheMut.RLock()
	cache, cached := accountPossibleMatchInsertCache[key]
	accountPossibleMatchInsertCacheMut.RUnlock()

	if !cached {
		wl, returnColumns := columns.InsertColumnSet(
			accountPossibleMatchAllColumns,
			accountPossibleMatchColumnsWithDefault,
			accountPossibleMatchColumnsWithoutDefault,
			nzDefaults,
		)

		cache.valueMapping, err = queries.BindMapping(accountPossibleMatchType, accountPossibleMatchMapping, wl)
		if err != nil {
			return err
		}
		cache.retMapping, err = queries.BindMapping(accountPossibleMatchType, accountPossibleMatchMapping, returnColumns)
		if err != nil {
			return err
		}
		if len(wl) != 0 {
			cache.query = fmt.Sprintf("INSERT INTO \"account\".\"account_possible_matches\" (\"%s\") %%sVALUES (%s)%%s", strings.Join(wl, "\",\""), strmangle.Placeholders(dialect.UseIndexPlaceholders, len(wl), 1, 1))
		} else {
			cache.query = "INSERT INTO \"account\".\"account_possible_matches\" %sDEFAULT VALUES%s"
		}

		var queryOutput, queryReturning string

		if len(cache.retMapping) != 0 {
			queryReturning = fmt.Sprintf(" RETURNING \"%s\"", strings.Join(returnColumns, "\",\""))
		}

		cache.query = fmt.Sprintf(cache.query, queryOutput, queryReturning)
	}

	value := reflect.Indirect(reflect.ValueOf(o))
	vals := queries.ValuesFromMapping(value, cache.valueMapping)

	if boil.IsDebug(ctx) {
		writer := boil.DebugWriterFrom(ctx)
		fmt.Fprintln(writer, cache.query)
		fmt.Fprintln(writer, vals)
	}

	if len(cache.retMapping) != 0 {
		err = exec.QueryRowContext(ctx, cache.query, vals...).Scan(queries.PtrsFromMapping(value, cache.retMapping)...)
	} else {
		_, err = exec.ExecContext(ctx, cache.query, vals...)
	}

	if err != nil {
		return errors.Wrap(err, "account: unable to insert into account_possible_matches")
	}

	if !cached {
		accountPossibleMatchInsertCacheMut.Lock()
		accountPossibleMatchInsertCache[key] = cache
		accountPossibleMatchInsertCacheMut.Unlock()
	}

	return o.doAfterInsertHooks(ctx, exec)
}

// Update uses an executor to update the AccountPossibleMatch.
// See boil.Columns.UpdateColumnSet documentation to understand column list inference for updates.
// Update does not automatically update the record in case of default values. Use .Reload() to refresh the records.
func (o *AccountPossibleMatch) Update(ctx context.Context, exec boil.ContextExecutor, columns boil.Columns) (int64, error) {
	if !boil.TimestampsAreSkipped(ctx) {
		currTime := time.Now().In(boil.GetLocation())

		o.UpdatedAt = currTime
	}

	var err error
	if err = o.doBeforeUpdateHooks(ctx, exec); err != nil {
		return 0, err
	}
	key := makeCacheKey(columns, nil)
	accountPossibleMatchUpdateCacheMut.RLock()
	cache, cached := accountPossibleMatchUpdateCache[key]
	accountPossibleMatchUpdateCacheMut.RUnlock()

	if !cached {
		wl := columns.UpdateColumnSet(
			accountPossibleMatchAllColumns,
			accountPossibleMatchPrimaryKeyColumns,
		)

		if !columns.IsWhitelist() {
			wl = strmangle.SetComplement(wl, []string{"created_at"})
		}
		if len(wl) == 0 {
			return 0, errors.New("account: unable to update account_possible_matches, could not build whitelist")
		}

		cache.query = fmt.Sprintf("UPDATE \"account\".\"account_possible_matches\" SET %s WHERE %s",
			strmangle.SetParamNames("\"", "\"", 1, wl),
			strmangle.WhereClause("\"", "\"", len(wl)+1, accountPossibleMatchPrimaryKeyColumns),
		)
		cache.valueMapping, err = queries.BindMapping(accountPossibleMatchType, accountPossibleMatchMapping, append(wl, accountPossibleMatchPrimaryKeyColumns...))
		if err != nil {
			return 0, err
		}
	}

	values := queries.ValuesFromMapping(reflect.Indirect(reflect.ValueOf(o)), cache.valueMapping)

	if boil.IsDebug(ctx) {
		writer := boil.DebugWriterFrom(ctx)
		fmt.Fprintln(writer, cache.query)
		fmt.Fprintln(writer, values)
	}
	var result sql.Result
	result, err = exec.ExecContext(ctx, cache.query, values...)
	if err != nil {
		return 0, errors.Wrap(err, "account: unable to update account_possible_matches row")
	}

	rowsAff, err := result.RowsAffected()
	if err != nil {
		return 0, errors.Wrap(err, "account: failed to get rows affected by update for account_possible_matches")
	}

	if !cached {
		accountPossibleMatchUpdateCacheMut.Lock()
		accountPossibleMatchUpdateCache[key] = cache
		accountPossibleMatchUpdateCacheMut.Unlock()
	}

	return rowsAff, o.doAfterUpdateHooks(ctx, exec)
}

// UpdateAll updates all rows with the specified column values.
func (q accountPossibleMatchQuery) UpdateAll(ctx context.Context, exec boil.ContextExecutor, cols M) (int64, error) {
	queries.SetUpdate(q.Query, cols)

	result, err := q.Query.ExecContext(ctx, exec)
	if err != nil {
		return 0, errors.Wrap(err, "account: unable to update all for account_possible_matches")
	}

	rowsAff, err := result.RowsAffected()
	if err != nil {
		return 0, errors.Wrap(err, "account: unable to retrieve rows affected for account_possible_matches")
	}

	return rowsAff, nil
}

// UpdateAll updates all rows with the specified column values, using an executor.
func (o AccountPossibleMatchSlice) UpdateAll(ctx context.Context, exec boil.ContextExecutor, cols M) (int64, error) {
	ln := int64(len(o))
	if ln == 0 {
		return 0, nil
	}

	if len(cols) == 0 {
		return 0, errors.New("account: update all requires at least one column argument")
	}

	colNames := make([]string, len(cols))
	args := make([]interface{}, len(cols))

	i := 0
	for name, value := range cols {
		colNames[i] = name
		args[i] = value
		i++
	}

	// Append all of the primary key values for each column
	for _, obj := range o {
		pkeyArgs := queries.ValuesFromMapping(reflect.Indirect(reflect.ValueOf(obj)), accountPossibleMatchPrimaryKeyMapping)
		args = append(args, pkeyArgs...)
	}

	sql := fmt.Sprintf("UPDATE \"account\".\"account_possible_matches\" SET %s WHERE %s",
		strmangle.SetParamNames("\"", "\"", 1, colNames),
		strmangle.WhereClauseRepeated(string(dialect.LQ), string(dialect.RQ), len(colNames)+1, accountPossibleMatchPrimaryKeyColumns, len(o)))

	if boil.IsDebug(ctx) {
		writer := boil.DebugWriterFrom(ctx)
		fmt.Fprintln(writer, sql)
		fmt.Fprintln(writer, args...)
	}
	result, err := exec.ExecContext(ctx, sql, args...)
	if err != nil {
		return 0, errors.Wrap(err, "account: unable to update all in accountPossibleMatch slice")
	}

	rowsAff, err := result.RowsAffected()
	if err != nil {
		return 0, errors.Wrap(err, "account: unable to retrieve rows affected all in update all accountPossibleMatch")
	}
	return rowsAff, nil
}

// Upsert attempts an insert using an executor, and does an update or ignore on conflict.
// See boil.Columns documentation for how to properly use updateColumns and insertColumns.
func (o *AccountPossibleMatch) Upsert(ctx context.Context, exec boil.ContextExecutor, updateOnConflict bool, conflictColumns []string, updateColumns, insertColumns boil.Columns) error {
	if o == nil {
		return errors.New("account: no account_possible_matches provided for upsert")
	}
	if !boil.TimestampsAreSkipped(ctx) {
		currTime := time.Now().In(boil.GetLocation())

		if o.CreatedAt.IsZero() {
			o.CreatedAt = currTime
		}
		o.UpdatedAt = currTime
	}

	if err := o.doBeforeUpsertHooks(ctx, exec); err != nil {
		return err
	}

	nzDefaults := queries.NonZeroDefaultSet(accountPossibleMatchColumnsWithDefault, o)

	// Build cache key in-line uglily - mysql vs psql problems
	buf := strmangle.GetBuffer()
	if updateOnConflict {
		buf.WriteByte('t')
	} else {
		buf.WriteByte('f')
	}
	buf.WriteByte('.')
	for _, c := range conflictColumns {
		buf.WriteString(c)
	}
	buf.WriteByte('.')
	buf.WriteString(strconv.Itoa(updateColumns.Kind))
	for _, c := range updateColumns.Cols {
		buf.WriteString(c)
	}
	buf.WriteByte('.')
	buf.WriteString(strconv.Itoa(insertColumns.Kind))
	for _, c := range insertColumns.Cols {
		buf.WriteString(c)
	}
	buf.WriteByte('.')
	for _, c := range nzDefaults {
		buf.WriteString(c)
	}
	key := buf.String()
	strmangle.PutBuffer(buf)

	accountPossibleMatchUpsertCacheMut.RLock()
	cache, cached := accountPossibleMatchUpsertCache[key]
	accountPossibleMatchUpsertCacheMut.RUnlock()

	var err error

	if !cached {
		insert, ret := insertColumns.InsertColumnSet(
			accountPossibleMatchAllColumns,
			accountPossibleMatchColumnsWithDefault,
			accountPossibleMatchColumnsWithoutDefault,
			nzDefaults,
		)

		update := updateColumns.UpdateColumnSet(
			accountPossibleMatchAllColumns,
			accountPossibleMatchPrimaryKeyColumns,
		)

		if updateOnConflict && len(update) == 0 {
			return errors.New("account: unable to upsert account_possible_matches, could not build update column list")
		}

		conflict := conflictColumns
		if len(conflict) == 0 {
			conflict = make([]string, len(accountPossibleMatchPrimaryKeyColumns))
			copy(conflict, accountPossibleMatchPrimaryKeyColumns)
		}
		cache.query = buildUpsertQueryPostgres(dialect, "\"account\".\"account_possible_matches\"", updateOnConflict, ret, update, conflict, insert)

		cache.valueMapping, err = queries.BindMapping(accountPossibleMatchType, accountPossibleMatchMapping, insert)
		if err != nil {
			return err
		}
		if len(ret) != 0 {
			cache.retMapping, err = queries.BindMapping(accountPossibleMatchType, accountPossibleMatchMapping, ret)
			if err != nil {
				return err
			}
		}
	}

	value := reflect.Indirect(reflect.ValueOf(o))
	vals := queries.ValuesFromMapping(value, cache.valueMapping)
	var returns []interface{}
	if len(cache.retMapping) != 0 {
		returns = queries.PtrsFromMapping(value, cache.retMapping)
	}

	if boil.IsDebug(ctx) {
		writer := boil.DebugWriterFrom(ctx)
		fmt.Fprintln(writer, cache.query)
		fmt.Fprintln(writer, vals)
	}
	if len(cache.retMapping) != 0 {
		err = exec.QueryRowContext(ctx, cache.query, vals...).Scan(returns...)
		if errors.Is(err, sql.ErrNoRows) {
			err = nil // Postgres doesn't return anything when there's no update
		}
	} else {
		_, err = exec.ExecContext(ctx, cache.query, vals...)
	}
	if err != nil {
		return errors.Wrap(err, "account: unable to upsert account_possible_matches")
	}

	if !cached {
		accountPossibleMatchUpsertCacheMut.Lock()
		accountPossibleMatchUpsertCache[key] = cache
		accountPossibleMatchUpsertCacheMut.Unlock()
	}

	return o.doAfterUpsertHooks(ctx, exec)
}

// Delete deletes a single AccountPossibleMatch record with an executor.
// Delete will match against the primary key column to find the record to delete.
func (o *AccountPossibleMatch) Delete(ctx context.Context, exec boil.ContextExecutor) (int64, error) {
	if o == nil {
		return 0, errors.New("account: no AccountPossibleMatch provided for delete")
	}

	if err := o.doBeforeDeleteHooks(ctx, exec); err != nil {
		return 0, err
	}

	args := queries.ValuesFromMapping(reflect.Indirect(reflect.ValueOf(o)), accountPossibleMatchPrimaryKeyMapping)
	sql := "DELETE FROM \"account\".\"account_possible_matches\" WHERE \"match_id\"=$1"

	if boil.IsDebug(ctx) {
		writer := boil.DebugWriterFrom(ctx)
		fmt.Fprintln(writer, sql)
		fmt.Fprintln(writer, args...)
	}
	result, err := exec.ExecContext(ctx, sql, args...)
	if err != nil {
		return 0, errors.Wrap(err, "account: unable to delete from account_possible_matches")
	}

	rowsAff, err := result.RowsAffected()
	if err != nil {
		return 0, errors.Wrap(err, "account: failed to get rows affected by delete for account_possible_matches")
	}

	if err := o.doAfterDeleteHooks(ctx, exec); err != nil {
		return 0, err
	}

	return rowsAff, nil
}

// DeleteAll deletes all matching rows.
func (q accountPossibleMatchQuery) DeleteAll(ctx context.Context, exec boil.ContextExecutor) (int64, error) {
	if q.Query == nil {
		return 0, errors.New("account: no accountPossibleMatchQuery provided for delete all")
	}

	queries.SetDelete(q.Query)

	result, err := q.Query.ExecContext(ctx, exec)
	if err != nil {
		return 0, errors.Wrap(err, "account: unable to delete all from account_possible_matches")
	}

	rowsAff, err := result.RowsAffected()
	if err != nil {
		return 0, errors.Wrap(err, "account: failed to get rows affected by deleteall for account_possible_matches")
	}

	return rowsAff, nil
}

// DeleteAll deletes all rows in the slice, using an executor.
func (o AccountPossibleMatchSlice) DeleteAll(ctx context.Context, exec boil.ContextExecutor) (int64, error) {
	if len(o) == 0 {
		return 0, nil
	}

	if len(accountPossibleMatchBeforeDeleteHooks) != 0 {
		for _, obj := range o {
			if err := obj.doBeforeDeleteHooks(ctx, exec); err != nil {
				return 0, err
			}
		}
	}

	var args []interface{}
	for _, obj := range o {
		pkeyArgs := queries.ValuesFromMapping(reflect.Indirect(reflect.ValueOf(obj)), accountPossibleMatchPrimaryKeyMapping)
		args = append(args, pkeyArgs...)
	}

	sql := "DELETE FROM \"account\".\"account_possible_matches\" WHERE " +
		strmangle.WhereClauseRepeated(string(dialect.LQ), string(dialect.RQ), 1, accountPossibleMatchPrimaryKeyColumns, len(o))

	if boil.IsDebug(ctx) {
		writer := boil.DebugWriterFrom(ctx)
		fmt.Fprintln(writer, sql)
		fmt.Fprintln(writer, args)
	}
	result, err := exec.ExecContext(ctx, sql, args...)
	if err != nil {
		return 0, errors.Wrap(err, "account: unable to delete all from accountPossibleMatch slice")
	}

	rowsAff, err := result.RowsAffected()
	if err != nil {
		return 0, errors.Wrap(err, "account: failed to get rows affected by deleteall for account_possible_matches")
	}

	if len(accountPossibleMatchAfterDeleteHooks) != 0 {
		for _, obj := range o {
			if err := obj.doAfterDeleteHooks(ctx, exec); err != nil {
				return 0, err
			}
		}
	}

	return rowsAff, nil
}

// Reload refetches the object from the database
// using the primary keys with an executor.
func (o *AccountPossibleMatch) Reload(ctx context.Context, exec boil.ContextExecutor) error {
	ret, err := FindAccountPossibleMatch(ctx, exec, o.MatchID)
	if err != nil {
		return err
	}

	*o = *ret
	return nil
}

// ReloadAll refetches every row with matching primary key column values
// and overwrites the original object slice with the newly updated slice.
func (o *AccountPossibleMatchSlice) ReloadAll(ctx context.Context, exec boil.ContextExecutor) error {
	if o == nil || len(*o) == 0 {
		return nil
	}

	slice := AccountPossibleMatchSlice{}
	var args []interface{}
	for _, obj := range *o {
		pkeyArgs := queries.ValuesFromMapping(reflect.Indirect(reflect.ValueOf(obj)), accountPossibleMatchPrimaryKeyMapping)
		args = append(args, pkeyArgs...)
	}

	sql := "SELECT \"account\".\"account_possible_matches\".* FROM \"account\".\"account_possible_matches\" WHERE " +
		strmangle.WhereClauseRepeated(string(dialect.LQ), string(dialect.RQ), 1, accountPossibleMatchPrimaryKeyColumns, len(*o))

	q := queries.Raw(sql, args...)

	err := q.Bind(ctx, exec, &slice)
	if err != nil {
		return errors.Wrap(err, "account: unable to reload all in AccountPossibleMatchSlice")
	}

	*o = slice

	return nil
}

// AccountPossibleMatchExists checks if the AccountPossibleMatch row exists.
func AccountPossibleMatchExists(ctx context.Context, exec boil.ContextExecutor, matchID string) (bool, error) {
	var exists bool
	sql := "select exists(select 1 from \"account\".\"account_possible_matches\" where \"match_id\"=$1 limit 1)"

	if boil.IsDebug(ctx) {
		writer := boil.DebugWriterFrom(ctx)
		fmt.Fprintln(writer, sql)
		fmt.Fprintln(writer, matchID)
	}
	row := exec.QueryRowContext(ctx, sql, matchID)

	err := row.Scan(&exists)
	if err != nil {
		return false, errors.Wrap(err, "account: unable to check if account_possible_matches exists")
	}

	return exists, nil
}
