// Code generated by SQLBoiler (https://github.com/volatiletech/sqlboiler). DO NOT EDIT.
// This file is meant to be re-generated in place and/or deleted at any time.

package snapsheet

import (
	"context"
	"database/sql"
	"fmt"
	"reflect"
	"strconv"
	"strings"
	"sync"
	"time"

	"github.com/friendsofgo/errors"
	"github.com/volatiletech/null/v8"
	"github.com/volatiletech/sqlboiler/v4/boil"
	"github.com/volatiletech/sqlboiler/v4/queries"
	"github.com/volatiletech/sqlboiler/v4/queries/qm"
	"github.com/volatiletech/sqlboiler/v4/queries/qmhelper"
	"github.com/volatiletech/strmangle"
)

// Note is an object representing the database table.
type Note struct {
	PolicyNumber     null.String `boil:"policy_number" json:"policy_number,omitempty" toml:"policy_number" yaml:"policy_number,omitempty"`
	CreatedBy        string      `boil:"created_by" json:"created_by" toml:"created_by" yaml:"created_by"`
	Type             null.String `boil:"type" json:"type,omitempty" toml:"type" yaml:"type,omitempty"`
	ContactType      null.String `boil:"contact_type" json:"contact_type,omitempty" toml:"contact_type" yaml:"contact_type,omitempty"`
	Category         null.String `boil:"category" json:"category,omitempty" toml:"category" yaml:"category,omitempty"`
	CreatedAt        time.Time   `boil:"created_at" json:"created_at" toml:"created_at" yaml:"created_at"`
	UpdatedAt        time.Time   `boil:"updated_at" json:"updated_at" toml:"updated_at" yaml:"updated_at"`
	ID               string      `boil:"id" json:"id" toml:"id" yaml:"id"`
	SnapsheetClaimID string      `boil:"snapsheet_claim_id" json:"snapsheet_claim_id" toml:"snapsheet_claim_id" yaml:"snapsheet_claim_id"`
	SnapsheetID      string      `boil:"snapsheet_id" json:"snapsheet_id" toml:"snapsheet_id" yaml:"snapsheet_id"`
	Value            string      `boil:"value" json:"value" toml:"value" yaml:"value"`

	R *noteR `boil:"" json:"" toml:"" yaml:""`
	L noteL  `boil:"-" json:"-" toml:"-" yaml:"-"`
}

var NoteColumns = struct {
	PolicyNumber     string
	CreatedBy        string
	Type             string
	ContactType      string
	Category         string
	CreatedAt        string
	UpdatedAt        string
	ID               string
	SnapsheetClaimID string
	SnapsheetID      string
	Value            string
}{
	PolicyNumber:     "policy_number",
	CreatedBy:        "created_by",
	Type:             "type",
	ContactType:      "contact_type",
	Category:         "category",
	CreatedAt:        "created_at",
	UpdatedAt:        "updated_at",
	ID:               "id",
	SnapsheetClaimID: "snapsheet_claim_id",
	SnapsheetID:      "snapsheet_id",
	Value:            "value",
}

var NoteTableColumns = struct {
	PolicyNumber     string
	CreatedBy        string
	Type             string
	ContactType      string
	Category         string
	CreatedAt        string
	UpdatedAt        string
	ID               string
	SnapsheetClaimID string
	SnapsheetID      string
	Value            string
}{
	PolicyNumber:     "notes.policy_number",
	CreatedBy:        "notes.created_by",
	Type:             "notes.type",
	ContactType:      "notes.contact_type",
	Category:         "notes.category",
	CreatedAt:        "notes.created_at",
	UpdatedAt:        "notes.updated_at",
	ID:               "notes.id",
	SnapsheetClaimID: "notes.snapsheet_claim_id",
	SnapsheetID:      "notes.snapsheet_id",
	Value:            "notes.value",
}

// Generated where

var NoteWhere = struct {
	PolicyNumber     whereHelpernull_String
	CreatedBy        whereHelperstring
	Type             whereHelpernull_String
	ContactType      whereHelpernull_String
	Category         whereHelpernull_String
	CreatedAt        whereHelpertime_Time
	UpdatedAt        whereHelpertime_Time
	ID               whereHelperstring
	SnapsheetClaimID whereHelperstring
	SnapsheetID      whereHelperstring
	Value            whereHelperstring
}{
	PolicyNumber:     whereHelpernull_String{field: "\"snapsheet\".\"notes\".\"policy_number\""},
	CreatedBy:        whereHelperstring{field: "\"snapsheet\".\"notes\".\"created_by\""},
	Type:             whereHelpernull_String{field: "\"snapsheet\".\"notes\".\"type\""},
	ContactType:      whereHelpernull_String{field: "\"snapsheet\".\"notes\".\"contact_type\""},
	Category:         whereHelpernull_String{field: "\"snapsheet\".\"notes\".\"category\""},
	CreatedAt:        whereHelpertime_Time{field: "\"snapsheet\".\"notes\".\"created_at\""},
	UpdatedAt:        whereHelpertime_Time{field: "\"snapsheet\".\"notes\".\"updated_at\""},
	ID:               whereHelperstring{field: "\"snapsheet\".\"notes\".\"id\""},
	SnapsheetClaimID: whereHelperstring{field: "\"snapsheet\".\"notes\".\"snapsheet_claim_id\""},
	SnapsheetID:      whereHelperstring{field: "\"snapsheet\".\"notes\".\"snapsheet_id\""},
	Value:            whereHelperstring{field: "\"snapsheet\".\"notes\".\"value\""},
}

// NoteRels is where relationship names are stored.
var NoteRels = struct {
}{}

// noteR is where relationships are stored.
type noteR struct {
}

// NewStruct creates a new relationship struct
func (*noteR) NewStruct() *noteR {
	return &noteR{}
}

// noteL is where Load methods for each relationship are stored.
type noteL struct{}

var (
	noteAllColumns            = []string{"policy_number", "created_by", "type", "contact_type", "category", "created_at", "updated_at", "id", "snapsheet_claim_id", "snapsheet_id", "value"}
	noteColumnsWithoutDefault = []string{"created_by", "created_at", "updated_at", "id", "snapsheet_claim_id", "snapsheet_id", "value"}
	noteColumnsWithDefault    = []string{"policy_number", "type", "contact_type", "category"}
	notePrimaryKeyColumns     = []string{"id"}
	noteGeneratedColumns      = []string{}
)

type (
	// NoteSlice is an alias for a slice of pointers to Note.
	// This should almost always be used instead of []Note.
	NoteSlice []*Note
	// NoteHook is the signature for custom Note hook methods
	NoteHook func(context.Context, boil.ContextExecutor, *Note) error

	noteQuery struct {
		*queries.Query
	}
)

// Cache for insert, update and upsert
var (
	noteType                 = reflect.TypeOf(&Note{})
	noteMapping              = queries.MakeStructMapping(noteType)
	notePrimaryKeyMapping, _ = queries.BindMapping(noteType, noteMapping, notePrimaryKeyColumns)
	noteInsertCacheMut       sync.RWMutex
	noteInsertCache          = make(map[string]insertCache)
	noteUpdateCacheMut       sync.RWMutex
	noteUpdateCache          = make(map[string]updateCache)
	noteUpsertCacheMut       sync.RWMutex
	noteUpsertCache          = make(map[string]insertCache)
)

var (
	// Force time package dependency for automated UpdatedAt/CreatedAt.
	_ = time.Second
	// Force qmhelper dependency for where clause generation (which doesn't
	// always happen)
	_ = qmhelper.Where
)

var noteAfterSelectHooks []NoteHook

var noteBeforeInsertHooks []NoteHook
var noteAfterInsertHooks []NoteHook

var noteBeforeUpdateHooks []NoteHook
var noteAfterUpdateHooks []NoteHook

var noteBeforeDeleteHooks []NoteHook
var noteAfterDeleteHooks []NoteHook

var noteBeforeUpsertHooks []NoteHook
var noteAfterUpsertHooks []NoteHook

// doAfterSelectHooks executes all "after Select" hooks.
func (o *Note) doAfterSelectHooks(ctx context.Context, exec boil.ContextExecutor) (err error) {
	if boil.HooksAreSkipped(ctx) {
		return nil
	}

	for _, hook := range noteAfterSelectHooks {
		if err := hook(ctx, exec, o); err != nil {
			return err
		}
	}

	return nil
}

// doBeforeInsertHooks executes all "before insert" hooks.
func (o *Note) doBeforeInsertHooks(ctx context.Context, exec boil.ContextExecutor) (err error) {
	if boil.HooksAreSkipped(ctx) {
		return nil
	}

	for _, hook := range noteBeforeInsertHooks {
		if err := hook(ctx, exec, o); err != nil {
			return err
		}
	}

	return nil
}

// doAfterInsertHooks executes all "after Insert" hooks.
func (o *Note) doAfterInsertHooks(ctx context.Context, exec boil.ContextExecutor) (err error) {
	if boil.HooksAreSkipped(ctx) {
		return nil
	}

	for _, hook := range noteAfterInsertHooks {
		if err := hook(ctx, exec, o); err != nil {
			return err
		}
	}

	return nil
}

// doBeforeUpdateHooks executes all "before Update" hooks.
func (o *Note) doBeforeUpdateHooks(ctx context.Context, exec boil.ContextExecutor) (err error) {
	if boil.HooksAreSkipped(ctx) {
		return nil
	}

	for _, hook := range noteBeforeUpdateHooks {
		if err := hook(ctx, exec, o); err != nil {
			return err
		}
	}

	return nil
}

// doAfterUpdateHooks executes all "after Update" hooks.
func (o *Note) doAfterUpdateHooks(ctx context.Context, exec boil.ContextExecutor) (err error) {
	if boil.HooksAreSkipped(ctx) {
		return nil
	}

	for _, hook := range noteAfterUpdateHooks {
		if err := hook(ctx, exec, o); err != nil {
			return err
		}
	}

	return nil
}

// doBeforeDeleteHooks executes all "before Delete" hooks.
func (o *Note) doBeforeDeleteHooks(ctx context.Context, exec boil.ContextExecutor) (err error) {
	if boil.HooksAreSkipped(ctx) {
		return nil
	}

	for _, hook := range noteBeforeDeleteHooks {
		if err := hook(ctx, exec, o); err != nil {
			return err
		}
	}

	return nil
}

// doAfterDeleteHooks executes all "after Delete" hooks.
func (o *Note) doAfterDeleteHooks(ctx context.Context, exec boil.ContextExecutor) (err error) {
	if boil.HooksAreSkipped(ctx) {
		return nil
	}

	for _, hook := range noteAfterDeleteHooks {
		if err := hook(ctx, exec, o); err != nil {
			return err
		}
	}

	return nil
}

// doBeforeUpsertHooks executes all "before Upsert" hooks.
func (o *Note) doBeforeUpsertHooks(ctx context.Context, exec boil.ContextExecutor) (err error) {
	if boil.HooksAreSkipped(ctx) {
		return nil
	}

	for _, hook := range noteBeforeUpsertHooks {
		if err := hook(ctx, exec, o); err != nil {
			return err
		}
	}

	return nil
}

// doAfterUpsertHooks executes all "after Upsert" hooks.
func (o *Note) doAfterUpsertHooks(ctx context.Context, exec boil.ContextExecutor) (err error) {
	if boil.HooksAreSkipped(ctx) {
		return nil
	}

	for _, hook := range noteAfterUpsertHooks {
		if err := hook(ctx, exec, o); err != nil {
			return err
		}
	}

	return nil
}

// AddNoteHook registers your hook function for all future operations.
func AddNoteHook(hookPoint boil.HookPoint, noteHook NoteHook) {
	switch hookPoint {
	case boil.AfterSelectHook:
		noteAfterSelectHooks = append(noteAfterSelectHooks, noteHook)
	case boil.BeforeInsertHook:
		noteBeforeInsertHooks = append(noteBeforeInsertHooks, noteHook)
	case boil.AfterInsertHook:
		noteAfterInsertHooks = append(noteAfterInsertHooks, noteHook)
	case boil.BeforeUpdateHook:
		noteBeforeUpdateHooks = append(noteBeforeUpdateHooks, noteHook)
	case boil.AfterUpdateHook:
		noteAfterUpdateHooks = append(noteAfterUpdateHooks, noteHook)
	case boil.BeforeDeleteHook:
		noteBeforeDeleteHooks = append(noteBeforeDeleteHooks, noteHook)
	case boil.AfterDeleteHook:
		noteAfterDeleteHooks = append(noteAfterDeleteHooks, noteHook)
	case boil.BeforeUpsertHook:
		noteBeforeUpsertHooks = append(noteBeforeUpsertHooks, noteHook)
	case boil.AfterUpsertHook:
		noteAfterUpsertHooks = append(noteAfterUpsertHooks, noteHook)
	}
}

// One returns a single note record from the query.
func (q noteQuery) One(ctx context.Context, exec boil.ContextExecutor) (*Note, error) {
	o := &Note{}

	queries.SetLimit(q.Query, 1)

	err := q.Bind(ctx, exec, o)
	if err != nil {
		if errors.Is(err, sql.ErrNoRows) {
			return nil, sql.ErrNoRows
		}
		return nil, errors.Wrap(err, "snapsheet: failed to execute a one query for notes")
	}

	if err := o.doAfterSelectHooks(ctx, exec); err != nil {
		return o, err
	}

	return o, nil
}

// All returns all Note records from the query.
func (q noteQuery) All(ctx context.Context, exec boil.ContextExecutor) (NoteSlice, error) {
	var o []*Note

	err := q.Bind(ctx, exec, &o)
	if err != nil {
		return nil, errors.Wrap(err, "snapsheet: failed to assign all query results to Note slice")
	}

	if len(noteAfterSelectHooks) != 0 {
		for _, obj := range o {
			if err := obj.doAfterSelectHooks(ctx, exec); err != nil {
				return o, err
			}
		}
	}

	return o, nil
}

// Count returns the count of all Note records in the query.
func (q noteQuery) Count(ctx context.Context, exec boil.ContextExecutor) (int64, error) {
	var count int64

	queries.SetSelect(q.Query, nil)
	queries.SetCount(q.Query)

	err := q.Query.QueryRowContext(ctx, exec).Scan(&count)
	if err != nil {
		return 0, errors.Wrap(err, "snapsheet: failed to count notes rows")
	}

	return count, nil
}

// Exists checks if the row exists in the table.
func (q noteQuery) Exists(ctx context.Context, exec boil.ContextExecutor) (bool, error) {
	var count int64

	queries.SetSelect(q.Query, nil)
	queries.SetCount(q.Query)
	queries.SetLimit(q.Query, 1)

	err := q.Query.QueryRowContext(ctx, exec).Scan(&count)
	if err != nil {
		return false, errors.Wrap(err, "snapsheet: failed to check if notes exists")
	}

	return count > 0, nil
}

// Notes retrieves all the records using an executor.
func Notes(mods ...qm.QueryMod) noteQuery {
	mods = append(mods, qm.From("\"snapsheet\".\"notes\""))
	q := NewQuery(mods...)
	if len(queries.GetSelect(q)) == 0 {
		queries.SetSelect(q, []string{"\"snapsheet\".\"notes\".*"})
	}

	return noteQuery{q}
}

// FindNote retrieves a single record by ID with an executor.
// If selectCols is empty Find will return all columns.
func FindNote(ctx context.Context, exec boil.ContextExecutor, iD string, selectCols ...string) (*Note, error) {
	noteObj := &Note{}

	sel := "*"
	if len(selectCols) > 0 {
		sel = strings.Join(strmangle.IdentQuoteSlice(dialect.LQ, dialect.RQ, selectCols), ",")
	}
	query := fmt.Sprintf(
		"select %s from \"snapsheet\".\"notes\" where \"id\"=$1", sel,
	)

	q := queries.Raw(query, iD)

	err := q.Bind(ctx, exec, noteObj)
	if err != nil {
		if errors.Is(err, sql.ErrNoRows) {
			return nil, sql.ErrNoRows
		}
		return nil, errors.Wrap(err, "snapsheet: unable to select from notes")
	}

	if err = noteObj.doAfterSelectHooks(ctx, exec); err != nil {
		return noteObj, err
	}

	return noteObj, nil
}

// Insert a single record using an executor.
// See boil.Columns.InsertColumnSet documentation to understand column list inference for inserts.
func (o *Note) Insert(ctx context.Context, exec boil.ContextExecutor, columns boil.Columns) error {
	if o == nil {
		return errors.New("snapsheet: no notes provided for insertion")
	}

	var err error
	if !boil.TimestampsAreSkipped(ctx) {
		currTime := time.Now().In(boil.GetLocation())

		if o.CreatedAt.IsZero() {
			o.CreatedAt = currTime
		}
		if o.UpdatedAt.IsZero() {
			o.UpdatedAt = currTime
		}
	}

	if err := o.doBeforeInsertHooks(ctx, exec); err != nil {
		return err
	}

	nzDefaults := queries.NonZeroDefaultSet(noteColumnsWithDefault, o)

	key := makeCacheKey(columns, nzDefaults)
	noteInsertCacheMut.RLock()
	cache, cached := noteInsertCache[key]
	noteInsertCacheMut.RUnlock()

	if !cached {
		wl, returnColumns := columns.InsertColumnSet(
			noteAllColumns,
			noteColumnsWithDefault,
			noteColumnsWithoutDefault,
			nzDefaults,
		)

		cache.valueMapping, err = queries.BindMapping(noteType, noteMapping, wl)
		if err != nil {
			return err
		}
		cache.retMapping, err = queries.BindMapping(noteType, noteMapping, returnColumns)
		if err != nil {
			return err
		}
		if len(wl) != 0 {
			cache.query = fmt.Sprintf("INSERT INTO \"snapsheet\".\"notes\" (\"%s\") %%sVALUES (%s)%%s", strings.Join(wl, "\",\""), strmangle.Placeholders(dialect.UseIndexPlaceholders, len(wl), 1, 1))
		} else {
			cache.query = "INSERT INTO \"snapsheet\".\"notes\" %sDEFAULT VALUES%s"
		}

		var queryOutput, queryReturning string

		if len(cache.retMapping) != 0 {
			queryReturning = fmt.Sprintf(" RETURNING \"%s\"", strings.Join(returnColumns, "\",\""))
		}

		cache.query = fmt.Sprintf(cache.query, queryOutput, queryReturning)
	}

	value := reflect.Indirect(reflect.ValueOf(o))
	vals := queries.ValuesFromMapping(value, cache.valueMapping)

	if boil.IsDebug(ctx) {
		writer := boil.DebugWriterFrom(ctx)
		fmt.Fprintln(writer, cache.query)
		fmt.Fprintln(writer, vals)
	}

	if len(cache.retMapping) != 0 {
		err = exec.QueryRowContext(ctx, cache.query, vals...).Scan(queries.PtrsFromMapping(value, cache.retMapping)...)
	} else {
		_, err = exec.ExecContext(ctx, cache.query, vals...)
	}

	if err != nil {
		return errors.Wrap(err, "snapsheet: unable to insert into notes")
	}

	if !cached {
		noteInsertCacheMut.Lock()
		noteInsertCache[key] = cache
		noteInsertCacheMut.Unlock()
	}

	return o.doAfterInsertHooks(ctx, exec)
}

// Update uses an executor to update the Note.
// See boil.Columns.UpdateColumnSet documentation to understand column list inference for updates.
// Update does not automatically update the record in case of default values. Use .Reload() to refresh the records.
func (o *Note) Update(ctx context.Context, exec boil.ContextExecutor, columns boil.Columns) (int64, error) {
	if !boil.TimestampsAreSkipped(ctx) {
		currTime := time.Now().In(boil.GetLocation())

		o.UpdatedAt = currTime
	}

	var err error
	if err = o.doBeforeUpdateHooks(ctx, exec); err != nil {
		return 0, err
	}
	key := makeCacheKey(columns, nil)
	noteUpdateCacheMut.RLock()
	cache, cached := noteUpdateCache[key]
	noteUpdateCacheMut.RUnlock()

	if !cached {
		wl := columns.UpdateColumnSet(
			noteAllColumns,
			notePrimaryKeyColumns,
		)

		if !columns.IsWhitelist() {
			wl = strmangle.SetComplement(wl, []string{"created_at"})
		}
		if len(wl) == 0 {
			return 0, errors.New("snapsheet: unable to update notes, could not build whitelist")
		}

		cache.query = fmt.Sprintf("UPDATE \"snapsheet\".\"notes\" SET %s WHERE %s",
			strmangle.SetParamNames("\"", "\"", 1, wl),
			strmangle.WhereClause("\"", "\"", len(wl)+1, notePrimaryKeyColumns),
		)
		cache.valueMapping, err = queries.BindMapping(noteType, noteMapping, append(wl, notePrimaryKeyColumns...))
		if err != nil {
			return 0, err
		}
	}

	values := queries.ValuesFromMapping(reflect.Indirect(reflect.ValueOf(o)), cache.valueMapping)

	if boil.IsDebug(ctx) {
		writer := boil.DebugWriterFrom(ctx)
		fmt.Fprintln(writer, cache.query)
		fmt.Fprintln(writer, values)
	}
	var result sql.Result
	result, err = exec.ExecContext(ctx, cache.query, values...)
	if err != nil {
		return 0, errors.Wrap(err, "snapsheet: unable to update notes row")
	}

	rowsAff, err := result.RowsAffected()
	if err != nil {
		return 0, errors.Wrap(err, "snapsheet: failed to get rows affected by update for notes")
	}

	if !cached {
		noteUpdateCacheMut.Lock()
		noteUpdateCache[key] = cache
		noteUpdateCacheMut.Unlock()
	}

	return rowsAff, o.doAfterUpdateHooks(ctx, exec)
}

// UpdateAll updates all rows with the specified column values.
func (q noteQuery) UpdateAll(ctx context.Context, exec boil.ContextExecutor, cols M) (int64, error) {
	queries.SetUpdate(q.Query, cols)

	result, err := q.Query.ExecContext(ctx, exec)
	if err != nil {
		return 0, errors.Wrap(err, "snapsheet: unable to update all for notes")
	}

	rowsAff, err := result.RowsAffected()
	if err != nil {
		return 0, errors.Wrap(err, "snapsheet: unable to retrieve rows affected for notes")
	}

	return rowsAff, nil
}

// UpdateAll updates all rows with the specified column values, using an executor.
func (o NoteSlice) UpdateAll(ctx context.Context, exec boil.ContextExecutor, cols M) (int64, error) {
	ln := int64(len(o))
	if ln == 0 {
		return 0, nil
	}

	if len(cols) == 0 {
		return 0, errors.New("snapsheet: update all requires at least one column argument")
	}

	colNames := make([]string, len(cols))
	args := make([]interface{}, len(cols))

	i := 0
	for name, value := range cols {
		colNames[i] = name
		args[i] = value
		i++
	}

	// Append all of the primary key values for each column
	for _, obj := range o {
		pkeyArgs := queries.ValuesFromMapping(reflect.Indirect(reflect.ValueOf(obj)), notePrimaryKeyMapping)
		args = append(args, pkeyArgs...)
	}

	sql := fmt.Sprintf("UPDATE \"snapsheet\".\"notes\" SET %s WHERE %s",
		strmangle.SetParamNames("\"", "\"", 1, colNames),
		strmangle.WhereClauseRepeated(string(dialect.LQ), string(dialect.RQ), len(colNames)+1, notePrimaryKeyColumns, len(o)))

	if boil.IsDebug(ctx) {
		writer := boil.DebugWriterFrom(ctx)
		fmt.Fprintln(writer, sql)
		fmt.Fprintln(writer, args...)
	}
	result, err := exec.ExecContext(ctx, sql, args...)
	if err != nil {
		return 0, errors.Wrap(err, "snapsheet: unable to update all in note slice")
	}

	rowsAff, err := result.RowsAffected()
	if err != nil {
		return 0, errors.Wrap(err, "snapsheet: unable to retrieve rows affected all in update all note")
	}
	return rowsAff, nil
}

// Upsert attempts an insert using an executor, and does an update or ignore on conflict.
// See boil.Columns documentation for how to properly use updateColumns and insertColumns.
func (o *Note) Upsert(ctx context.Context, exec boil.ContextExecutor, updateOnConflict bool, conflictColumns []string, updateColumns, insertColumns boil.Columns) error {
	if o == nil {
		return errors.New("snapsheet: no notes provided for upsert")
	}
	if !boil.TimestampsAreSkipped(ctx) {
		currTime := time.Now().In(boil.GetLocation())

		if o.CreatedAt.IsZero() {
			o.CreatedAt = currTime
		}
		o.UpdatedAt = currTime
	}

	if err := o.doBeforeUpsertHooks(ctx, exec); err != nil {
		return err
	}

	nzDefaults := queries.NonZeroDefaultSet(noteColumnsWithDefault, o)

	// Build cache key in-line uglily - mysql vs psql problems
	buf := strmangle.GetBuffer()
	if updateOnConflict {
		buf.WriteByte('t')
	} else {
		buf.WriteByte('f')
	}
	buf.WriteByte('.')
	for _, c := range conflictColumns {
		buf.WriteString(c)
	}
	buf.WriteByte('.')
	buf.WriteString(strconv.Itoa(updateColumns.Kind))
	for _, c := range updateColumns.Cols {
		buf.WriteString(c)
	}
	buf.WriteByte('.')
	buf.WriteString(strconv.Itoa(insertColumns.Kind))
	for _, c := range insertColumns.Cols {
		buf.WriteString(c)
	}
	buf.WriteByte('.')
	for _, c := range nzDefaults {
		buf.WriteString(c)
	}
	key := buf.String()
	strmangle.PutBuffer(buf)

	noteUpsertCacheMut.RLock()
	cache, cached := noteUpsertCache[key]
	noteUpsertCacheMut.RUnlock()

	var err error

	if !cached {
		insert, ret := insertColumns.InsertColumnSet(
			noteAllColumns,
			noteColumnsWithDefault,
			noteColumnsWithoutDefault,
			nzDefaults,
		)

		update := updateColumns.UpdateColumnSet(
			noteAllColumns,
			notePrimaryKeyColumns,
		)

		if updateOnConflict && len(update) == 0 {
			return errors.New("snapsheet: unable to upsert notes, could not build update column list")
		}

		conflict := conflictColumns
		if len(conflict) == 0 {
			conflict = make([]string, len(notePrimaryKeyColumns))
			copy(conflict, notePrimaryKeyColumns)
		}
		cache.query = buildUpsertQueryPostgres(dialect, "\"snapsheet\".\"notes\"", updateOnConflict, ret, update, conflict, insert)

		cache.valueMapping, err = queries.BindMapping(noteType, noteMapping, insert)
		if err != nil {
			return err
		}
		if len(ret) != 0 {
			cache.retMapping, err = queries.BindMapping(noteType, noteMapping, ret)
			if err != nil {
				return err
			}
		}
	}

	value := reflect.Indirect(reflect.ValueOf(o))
	vals := queries.ValuesFromMapping(value, cache.valueMapping)
	var returns []interface{}
	if len(cache.retMapping) != 0 {
		returns = queries.PtrsFromMapping(value, cache.retMapping)
	}

	if boil.IsDebug(ctx) {
		writer := boil.DebugWriterFrom(ctx)
		fmt.Fprintln(writer, cache.query)
		fmt.Fprintln(writer, vals)
	}
	if len(cache.retMapping) != 0 {
		err = exec.QueryRowContext(ctx, cache.query, vals...).Scan(returns...)
		if errors.Is(err, sql.ErrNoRows) {
			err = nil // Postgres doesn't return anything when there's no update
		}
	} else {
		_, err = exec.ExecContext(ctx, cache.query, vals...)
	}
	if err != nil {
		return errors.Wrap(err, "snapsheet: unable to upsert notes")
	}

	if !cached {
		noteUpsertCacheMut.Lock()
		noteUpsertCache[key] = cache
		noteUpsertCacheMut.Unlock()
	}

	return o.doAfterUpsertHooks(ctx, exec)
}

// Delete deletes a single Note record with an executor.
// Delete will match against the primary key column to find the record to delete.
func (o *Note) Delete(ctx context.Context, exec boil.ContextExecutor) (int64, error) {
	if o == nil {
		return 0, errors.New("snapsheet: no Note provided for delete")
	}

	if err := o.doBeforeDeleteHooks(ctx, exec); err != nil {
		return 0, err
	}

	args := queries.ValuesFromMapping(reflect.Indirect(reflect.ValueOf(o)), notePrimaryKeyMapping)
	sql := "DELETE FROM \"snapsheet\".\"notes\" WHERE \"id\"=$1"

	if boil.IsDebug(ctx) {
		writer := boil.DebugWriterFrom(ctx)
		fmt.Fprintln(writer, sql)
		fmt.Fprintln(writer, args...)
	}
	result, err := exec.ExecContext(ctx, sql, args...)
	if err != nil {
		return 0, errors.Wrap(err, "snapsheet: unable to delete from notes")
	}

	rowsAff, err := result.RowsAffected()
	if err != nil {
		return 0, errors.Wrap(err, "snapsheet: failed to get rows affected by delete for notes")
	}

	if err := o.doAfterDeleteHooks(ctx, exec); err != nil {
		return 0, err
	}

	return rowsAff, nil
}

// DeleteAll deletes all matching rows.
func (q noteQuery) DeleteAll(ctx context.Context, exec boil.ContextExecutor) (int64, error) {
	if q.Query == nil {
		return 0, errors.New("snapsheet: no noteQuery provided for delete all")
	}

	queries.SetDelete(q.Query)

	result, err := q.Query.ExecContext(ctx, exec)
	if err != nil {
		return 0, errors.Wrap(err, "snapsheet: unable to delete all from notes")
	}

	rowsAff, err := result.RowsAffected()
	if err != nil {
		return 0, errors.Wrap(err, "snapsheet: failed to get rows affected by deleteall for notes")
	}

	return rowsAff, nil
}

// DeleteAll deletes all rows in the slice, using an executor.
func (o NoteSlice) DeleteAll(ctx context.Context, exec boil.ContextExecutor) (int64, error) {
	if len(o) == 0 {
		return 0, nil
	}

	if len(noteBeforeDeleteHooks) != 0 {
		for _, obj := range o {
			if err := obj.doBeforeDeleteHooks(ctx, exec); err != nil {
				return 0, err
			}
		}
	}

	var args []interface{}
	for _, obj := range o {
		pkeyArgs := queries.ValuesFromMapping(reflect.Indirect(reflect.ValueOf(obj)), notePrimaryKeyMapping)
		args = append(args, pkeyArgs...)
	}

	sql := "DELETE FROM \"snapsheet\".\"notes\" WHERE " +
		strmangle.WhereClauseRepeated(string(dialect.LQ), string(dialect.RQ), 1, notePrimaryKeyColumns, len(o))

	if boil.IsDebug(ctx) {
		writer := boil.DebugWriterFrom(ctx)
		fmt.Fprintln(writer, sql)
		fmt.Fprintln(writer, args)
	}
	result, err := exec.ExecContext(ctx, sql, args...)
	if err != nil {
		return 0, errors.Wrap(err, "snapsheet: unable to delete all from note slice")
	}

	rowsAff, err := result.RowsAffected()
	if err != nil {
		return 0, errors.Wrap(err, "snapsheet: failed to get rows affected by deleteall for notes")
	}

	if len(noteAfterDeleteHooks) != 0 {
		for _, obj := range o {
			if err := obj.doAfterDeleteHooks(ctx, exec); err != nil {
				return 0, err
			}
		}
	}

	return rowsAff, nil
}

// Reload refetches the object from the database
// using the primary keys with an executor.
func (o *Note) Reload(ctx context.Context, exec boil.ContextExecutor) error {
	ret, err := FindNote(ctx, exec, o.ID)
	if err != nil {
		return err
	}

	*o = *ret
	return nil
}

// ReloadAll refetches every row with matching primary key column values
// and overwrites the original object slice with the newly updated slice.
func (o *NoteSlice) ReloadAll(ctx context.Context, exec boil.ContextExecutor) error {
	if o == nil || len(*o) == 0 {
		return nil
	}

	slice := NoteSlice{}
	var args []interface{}
	for _, obj := range *o {
		pkeyArgs := queries.ValuesFromMapping(reflect.Indirect(reflect.ValueOf(obj)), notePrimaryKeyMapping)
		args = append(args, pkeyArgs...)
	}

	sql := "SELECT \"snapsheet\".\"notes\".* FROM \"snapsheet\".\"notes\" WHERE " +
		strmangle.WhereClauseRepeated(string(dialect.LQ), string(dialect.RQ), 1, notePrimaryKeyColumns, len(*o))

	q := queries.Raw(sql, args...)

	err := q.Bind(ctx, exec, &slice)
	if err != nil {
		return errors.Wrap(err, "snapsheet: unable to reload all in NoteSlice")
	}

	*o = slice

	return nil
}

// NoteExists checks if the Note row exists.
func NoteExists(ctx context.Context, exec boil.ContextExecutor, iD string) (bool, error) {
	var exists bool
	sql := "select exists(select 1 from \"snapsheet\".\"notes\" where \"id\"=$1 limit 1)"

	if boil.IsDebug(ctx) {
		writer := boil.DebugWriterFrom(ctx)
		fmt.Fprintln(writer, sql)
		fmt.Fprintln(writer, iD)
	}
	row := exec.QueryRowContext(ctx, sql, iD)

	err := row.Scan(&exists)
	if err != nil {
		return false, errors.Wrap(err, "snapsheet: unable to check if notes exists")
	}

	return exists, nil
}
