// Code generated by SQLBoiler (https://github.com/volatiletech/sqlboiler). DO NOT EDIT.
// This file is meant to be re-generated in place and/or deleted at any time.

package insurance_bundle

import (
	"context"
	"database/sql"
	"fmt"
	"reflect"
	"strconv"
	"strings"
	"sync"
	"time"

	"github.com/friendsofgo/errors"
	"github.com/volatiletech/null/v8"
	"github.com/volatiletech/sqlboiler/v4/boil"
	"github.com/volatiletech/sqlboiler/v4/queries"
	"github.com/volatiletech/sqlboiler/v4/queries/qm"
	"github.com/volatiletech/sqlboiler/v4/queries/qmhelper"
	"github.com/volatiletech/sqlboiler/v4/types"
	"github.com/volatiletech/strmangle"
)

// Change is an object representing the database table.
type Change struct {
	ID                string     `boil:"id" json:"id" toml:"id" yaml:"id"`
	ChangeContainerID string     `boil:"change_container_id" json:"change_container_id" toml:"change_container_id" yaml:"change_container_id"`
	SequenceNumber    int        `boil:"sequence_number" json:"sequence_number" toml:"sequence_number" yaml:"sequence_number"`
	PolicyNumbers     null.JSON  `boil:"policy_numbers" json:"policy_numbers,omitempty" toml:"policy_numbers" yaml:"policy_numbers,omitempty"`
	Type              string     `boil:"type" json:"type" toml:"type" yaml:"type"`
	Data              types.JSON `boil:"data" json:"data" toml:"data" yaml:"data"`
	CreatedAt         null.Time  `boil:"created_at" json:"created_at,omitempty" toml:"created_at" yaml:"created_at,omitempty"`
	UpdatedAt         null.Time  `boil:"updated_at" json:"updated_at,omitempty" toml:"updated_at" yaml:"updated_at,omitempty"`

	R *changeR `boil:"" json:"" toml:"" yaml:""`
	L changeL  `boil:"-" json:"-" toml:"-" yaml:"-"`
}

var ChangeColumns = struct {
	ID                string
	ChangeContainerID string
	SequenceNumber    string
	PolicyNumbers     string
	Type              string
	Data              string
	CreatedAt         string
	UpdatedAt         string
}{
	ID:                "id",
	ChangeContainerID: "change_container_id",
	SequenceNumber:    "sequence_number",
	PolicyNumbers:     "policy_numbers",
	Type:              "type",
	Data:              "data",
	CreatedAt:         "created_at",
	UpdatedAt:         "updated_at",
}

var ChangeTableColumns = struct {
	ID                string
	ChangeContainerID string
	SequenceNumber    string
	PolicyNumbers     string
	Type              string
	Data              string
	CreatedAt         string
	UpdatedAt         string
}{
	ID:                "change.id",
	ChangeContainerID: "change.change_container_id",
	SequenceNumber:    "change.sequence_number",
	PolicyNumbers:     "change.policy_numbers",
	Type:              "change.type",
	Data:              "change.data",
	CreatedAt:         "change.created_at",
	UpdatedAt:         "change.updated_at",
}

// Generated where

type whereHelperstring struct{ field string }

func (w whereHelperstring) EQ(x string) qm.QueryMod  { return qmhelper.Where(w.field, qmhelper.EQ, x) }
func (w whereHelperstring) NEQ(x string) qm.QueryMod { return qmhelper.Where(w.field, qmhelper.NEQ, x) }
func (w whereHelperstring) LT(x string) qm.QueryMod  { return qmhelper.Where(w.field, qmhelper.LT, x) }
func (w whereHelperstring) LTE(x string) qm.QueryMod { return qmhelper.Where(w.field, qmhelper.LTE, x) }
func (w whereHelperstring) GT(x string) qm.QueryMod  { return qmhelper.Where(w.field, qmhelper.GT, x) }
func (w whereHelperstring) GTE(x string) qm.QueryMod { return qmhelper.Where(w.field, qmhelper.GTE, x) }
func (w whereHelperstring) IN(slice []string) qm.QueryMod {
	values := make([]interface{}, 0, len(slice))
	for _, value := range slice {
		values = append(values, value)
	}
	return qm.WhereIn(fmt.Sprintf("%s IN ?", w.field), values...)
}
func (w whereHelperstring) NIN(slice []string) qm.QueryMod {
	values := make([]interface{}, 0, len(slice))
	for _, value := range slice {
		values = append(values, value)
	}
	return qm.WhereNotIn(fmt.Sprintf("%s NOT IN ?", w.field), values...)
}

type whereHelperint struct{ field string }

func (w whereHelperint) EQ(x int) qm.QueryMod  { return qmhelper.Where(w.field, qmhelper.EQ, x) }
func (w whereHelperint) NEQ(x int) qm.QueryMod { return qmhelper.Where(w.field, qmhelper.NEQ, x) }
func (w whereHelperint) LT(x int) qm.QueryMod  { return qmhelper.Where(w.field, qmhelper.LT, x) }
func (w whereHelperint) LTE(x int) qm.QueryMod { return qmhelper.Where(w.field, qmhelper.LTE, x) }
func (w whereHelperint) GT(x int) qm.QueryMod  { return qmhelper.Where(w.field, qmhelper.GT, x) }
func (w whereHelperint) GTE(x int) qm.QueryMod { return qmhelper.Where(w.field, qmhelper.GTE, x) }
func (w whereHelperint) IN(slice []int) qm.QueryMod {
	values := make([]interface{}, 0, len(slice))
	for _, value := range slice {
		values = append(values, value)
	}
	return qm.WhereIn(fmt.Sprintf("%s IN ?", w.field), values...)
}
func (w whereHelperint) NIN(slice []int) qm.QueryMod {
	values := make([]interface{}, 0, len(slice))
	for _, value := range slice {
		values = append(values, value)
	}
	return qm.WhereNotIn(fmt.Sprintf("%s NOT IN ?", w.field), values...)
}

type whereHelpernull_JSON struct{ field string }

func (w whereHelpernull_JSON) EQ(x null.JSON) qm.QueryMod {
	return qmhelper.WhereNullEQ(w.field, false, x)
}
func (w whereHelpernull_JSON) NEQ(x null.JSON) qm.QueryMod {
	return qmhelper.WhereNullEQ(w.field, true, x)
}
func (w whereHelpernull_JSON) LT(x null.JSON) qm.QueryMod {
	return qmhelper.Where(w.field, qmhelper.LT, x)
}
func (w whereHelpernull_JSON) LTE(x null.JSON) qm.QueryMod {
	return qmhelper.Where(w.field, qmhelper.LTE, x)
}
func (w whereHelpernull_JSON) GT(x null.JSON) qm.QueryMod {
	return qmhelper.Where(w.field, qmhelper.GT, x)
}
func (w whereHelpernull_JSON) GTE(x null.JSON) qm.QueryMod {
	return qmhelper.Where(w.field, qmhelper.GTE, x)
}

func (w whereHelpernull_JSON) IsNull() qm.QueryMod    { return qmhelper.WhereIsNull(w.field) }
func (w whereHelpernull_JSON) IsNotNull() qm.QueryMod { return qmhelper.WhereIsNotNull(w.field) }

type whereHelpertypes_JSON struct{ field string }

func (w whereHelpertypes_JSON) EQ(x types.JSON) qm.QueryMod {
	return qmhelper.Where(w.field, qmhelper.EQ, x)
}
func (w whereHelpertypes_JSON) NEQ(x types.JSON) qm.QueryMod {
	return qmhelper.Where(w.field, qmhelper.NEQ, x)
}
func (w whereHelpertypes_JSON) LT(x types.JSON) qm.QueryMod {
	return qmhelper.Where(w.field, qmhelper.LT, x)
}
func (w whereHelpertypes_JSON) LTE(x types.JSON) qm.QueryMod {
	return qmhelper.Where(w.field, qmhelper.LTE, x)
}
func (w whereHelpertypes_JSON) GT(x types.JSON) qm.QueryMod {
	return qmhelper.Where(w.field, qmhelper.GT, x)
}
func (w whereHelpertypes_JSON) GTE(x types.JSON) qm.QueryMod {
	return qmhelper.Where(w.field, qmhelper.GTE, x)
}

type whereHelpernull_Time struct{ field string }

func (w whereHelpernull_Time) EQ(x null.Time) qm.QueryMod {
	return qmhelper.WhereNullEQ(w.field, false, x)
}
func (w whereHelpernull_Time) NEQ(x null.Time) qm.QueryMod {
	return qmhelper.WhereNullEQ(w.field, true, x)
}
func (w whereHelpernull_Time) LT(x null.Time) qm.QueryMod {
	return qmhelper.Where(w.field, qmhelper.LT, x)
}
func (w whereHelpernull_Time) LTE(x null.Time) qm.QueryMod {
	return qmhelper.Where(w.field, qmhelper.LTE, x)
}
func (w whereHelpernull_Time) GT(x null.Time) qm.QueryMod {
	return qmhelper.Where(w.field, qmhelper.GT, x)
}
func (w whereHelpernull_Time) GTE(x null.Time) qm.QueryMod {
	return qmhelper.Where(w.field, qmhelper.GTE, x)
}

func (w whereHelpernull_Time) IsNull() qm.QueryMod    { return qmhelper.WhereIsNull(w.field) }
func (w whereHelpernull_Time) IsNotNull() qm.QueryMod { return qmhelper.WhereIsNotNull(w.field) }

var ChangeWhere = struct {
	ID                whereHelperstring
	ChangeContainerID whereHelperstring
	SequenceNumber    whereHelperint
	PolicyNumbers     whereHelpernull_JSON
	Type              whereHelperstring
	Data              whereHelpertypes_JSON
	CreatedAt         whereHelpernull_Time
	UpdatedAt         whereHelpernull_Time
}{
	ID:                whereHelperstring{field: "\"insurance_bundle\".\"change\".\"id\""},
	ChangeContainerID: whereHelperstring{field: "\"insurance_bundle\".\"change\".\"change_container_id\""},
	SequenceNumber:    whereHelperint{field: "\"insurance_bundle\".\"change\".\"sequence_number\""},
	PolicyNumbers:     whereHelpernull_JSON{field: "\"insurance_bundle\".\"change\".\"policy_numbers\""},
	Type:              whereHelperstring{field: "\"insurance_bundle\".\"change\".\"type\""},
	Data:              whereHelpertypes_JSON{field: "\"insurance_bundle\".\"change\".\"data\""},
	CreatedAt:         whereHelpernull_Time{field: "\"insurance_bundle\".\"change\".\"created_at\""},
	UpdatedAt:         whereHelpernull_Time{field: "\"insurance_bundle\".\"change\".\"updated_at\""},
}

// ChangeRels is where relationship names are stored.
var ChangeRels = struct {
	ChangeContainer string
}{
	ChangeContainer: "ChangeContainer",
}

// changeR is where relationships are stored.
type changeR struct {
	ChangeContainer *ChangeContainer `boil:"ChangeContainer" json:"ChangeContainer" toml:"ChangeContainer" yaml:"ChangeContainer"`
}

// NewStruct creates a new relationship struct
func (*changeR) NewStruct() *changeR {
	return &changeR{}
}

// changeL is where Load methods for each relationship are stored.
type changeL struct{}

var (
	changeAllColumns            = []string{"id", "change_container_id", "sequence_number", "policy_numbers", "type", "data", "created_at", "updated_at"}
	changeColumnsWithoutDefault = []string{"id", "change_container_id", "sequence_number", "type", "data"}
	changeColumnsWithDefault    = []string{"policy_numbers", "created_at", "updated_at"}
	changePrimaryKeyColumns     = []string{"id"}
	changeGeneratedColumns      = []string{}
)

type (
	// ChangeSlice is an alias for a slice of pointers to Change.
	// This should almost always be used instead of []Change.
	ChangeSlice []*Change
	// ChangeHook is the signature for custom Change hook methods
	ChangeHook func(context.Context, boil.ContextExecutor, *Change) error

	changeQuery struct {
		*queries.Query
	}
)

// Cache for insert, update and upsert
var (
	changeType                 = reflect.TypeOf(&Change{})
	changeMapping              = queries.MakeStructMapping(changeType)
	changePrimaryKeyMapping, _ = queries.BindMapping(changeType, changeMapping, changePrimaryKeyColumns)
	changeInsertCacheMut       sync.RWMutex
	changeInsertCache          = make(map[string]insertCache)
	changeUpdateCacheMut       sync.RWMutex
	changeUpdateCache          = make(map[string]updateCache)
	changeUpsertCacheMut       sync.RWMutex
	changeUpsertCache          = make(map[string]insertCache)
)

var (
	// Force time package dependency for automated UpdatedAt/CreatedAt.
	_ = time.Second
	// Force qmhelper dependency for where clause generation (which doesn't
	// always happen)
	_ = qmhelper.Where
)

var changeAfterSelectHooks []ChangeHook

var changeBeforeInsertHooks []ChangeHook
var changeAfterInsertHooks []ChangeHook

var changeBeforeUpdateHooks []ChangeHook
var changeAfterUpdateHooks []ChangeHook

var changeBeforeDeleteHooks []ChangeHook
var changeAfterDeleteHooks []ChangeHook

var changeBeforeUpsertHooks []ChangeHook
var changeAfterUpsertHooks []ChangeHook

// doAfterSelectHooks executes all "after Select" hooks.
func (o *Change) doAfterSelectHooks(ctx context.Context, exec boil.ContextExecutor) (err error) {
	if boil.HooksAreSkipped(ctx) {
		return nil
	}

	for _, hook := range changeAfterSelectHooks {
		if err := hook(ctx, exec, o); err != nil {
			return err
		}
	}

	return nil
}

// doBeforeInsertHooks executes all "before insert" hooks.
func (o *Change) doBeforeInsertHooks(ctx context.Context, exec boil.ContextExecutor) (err error) {
	if boil.HooksAreSkipped(ctx) {
		return nil
	}

	for _, hook := range changeBeforeInsertHooks {
		if err := hook(ctx, exec, o); err != nil {
			return err
		}
	}

	return nil
}

// doAfterInsertHooks executes all "after Insert" hooks.
func (o *Change) doAfterInsertHooks(ctx context.Context, exec boil.ContextExecutor) (err error) {
	if boil.HooksAreSkipped(ctx) {
		return nil
	}

	for _, hook := range changeAfterInsertHooks {
		if err := hook(ctx, exec, o); err != nil {
			return err
		}
	}

	return nil
}

// doBeforeUpdateHooks executes all "before Update" hooks.
func (o *Change) doBeforeUpdateHooks(ctx context.Context, exec boil.ContextExecutor) (err error) {
	if boil.HooksAreSkipped(ctx) {
		return nil
	}

	for _, hook := range changeBeforeUpdateHooks {
		if err := hook(ctx, exec, o); err != nil {
			return err
		}
	}

	return nil
}

// doAfterUpdateHooks executes all "after Update" hooks.
func (o *Change) doAfterUpdateHooks(ctx context.Context, exec boil.ContextExecutor) (err error) {
	if boil.HooksAreSkipped(ctx) {
		return nil
	}

	for _, hook := range changeAfterUpdateHooks {
		if err := hook(ctx, exec, o); err != nil {
			return err
		}
	}

	return nil
}

// doBeforeDeleteHooks executes all "before Delete" hooks.
func (o *Change) doBeforeDeleteHooks(ctx context.Context, exec boil.ContextExecutor) (err error) {
	if boil.HooksAreSkipped(ctx) {
		return nil
	}

	for _, hook := range changeBeforeDeleteHooks {
		if err := hook(ctx, exec, o); err != nil {
			return err
		}
	}

	return nil
}

// doAfterDeleteHooks executes all "after Delete" hooks.
func (o *Change) doAfterDeleteHooks(ctx context.Context, exec boil.ContextExecutor) (err error) {
	if boil.HooksAreSkipped(ctx) {
		return nil
	}

	for _, hook := range changeAfterDeleteHooks {
		if err := hook(ctx, exec, o); err != nil {
			return err
		}
	}

	return nil
}

// doBeforeUpsertHooks executes all "before Upsert" hooks.
func (o *Change) doBeforeUpsertHooks(ctx context.Context, exec boil.ContextExecutor) (err error) {
	if boil.HooksAreSkipped(ctx) {
		return nil
	}

	for _, hook := range changeBeforeUpsertHooks {
		if err := hook(ctx, exec, o); err != nil {
			return err
		}
	}

	return nil
}

// doAfterUpsertHooks executes all "after Upsert" hooks.
func (o *Change) doAfterUpsertHooks(ctx context.Context, exec boil.ContextExecutor) (err error) {
	if boil.HooksAreSkipped(ctx) {
		return nil
	}

	for _, hook := range changeAfterUpsertHooks {
		if err := hook(ctx, exec, o); err != nil {
			return err
		}
	}

	return nil
}

// AddChangeHook registers your hook function for all future operations.
func AddChangeHook(hookPoint boil.HookPoint, changeHook ChangeHook) {
	switch hookPoint {
	case boil.AfterSelectHook:
		changeAfterSelectHooks = append(changeAfterSelectHooks, changeHook)
	case boil.BeforeInsertHook:
		changeBeforeInsertHooks = append(changeBeforeInsertHooks, changeHook)
	case boil.AfterInsertHook:
		changeAfterInsertHooks = append(changeAfterInsertHooks, changeHook)
	case boil.BeforeUpdateHook:
		changeBeforeUpdateHooks = append(changeBeforeUpdateHooks, changeHook)
	case boil.AfterUpdateHook:
		changeAfterUpdateHooks = append(changeAfterUpdateHooks, changeHook)
	case boil.BeforeDeleteHook:
		changeBeforeDeleteHooks = append(changeBeforeDeleteHooks, changeHook)
	case boil.AfterDeleteHook:
		changeAfterDeleteHooks = append(changeAfterDeleteHooks, changeHook)
	case boil.BeforeUpsertHook:
		changeBeforeUpsertHooks = append(changeBeforeUpsertHooks, changeHook)
	case boil.AfterUpsertHook:
		changeAfterUpsertHooks = append(changeAfterUpsertHooks, changeHook)
	}
}

// One returns a single change record from the query.
func (q changeQuery) One(ctx context.Context, exec boil.ContextExecutor) (*Change, error) {
	o := &Change{}

	queries.SetLimit(q.Query, 1)

	err := q.Bind(ctx, exec, o)
	if err != nil {
		if errors.Is(err, sql.ErrNoRows) {
			return nil, sql.ErrNoRows
		}
		return nil, errors.Wrap(err, "insurance_bundle: failed to execute a one query for change")
	}

	if err := o.doAfterSelectHooks(ctx, exec); err != nil {
		return o, err
	}

	return o, nil
}

// All returns all Change records from the query.
func (q changeQuery) All(ctx context.Context, exec boil.ContextExecutor) (ChangeSlice, error) {
	var o []*Change

	err := q.Bind(ctx, exec, &o)
	if err != nil {
		return nil, errors.Wrap(err, "insurance_bundle: failed to assign all query results to Change slice")
	}

	if len(changeAfterSelectHooks) != 0 {
		for _, obj := range o {
			if err := obj.doAfterSelectHooks(ctx, exec); err != nil {
				return o, err
			}
		}
	}

	return o, nil
}

// Count returns the count of all Change records in the query.
func (q changeQuery) Count(ctx context.Context, exec boil.ContextExecutor) (int64, error) {
	var count int64

	queries.SetSelect(q.Query, nil)
	queries.SetCount(q.Query)

	err := q.Query.QueryRowContext(ctx, exec).Scan(&count)
	if err != nil {
		return 0, errors.Wrap(err, "insurance_bundle: failed to count change rows")
	}

	return count, nil
}

// Exists checks if the row exists in the table.
func (q changeQuery) Exists(ctx context.Context, exec boil.ContextExecutor) (bool, error) {
	var count int64

	queries.SetSelect(q.Query, nil)
	queries.SetCount(q.Query)
	queries.SetLimit(q.Query, 1)

	err := q.Query.QueryRowContext(ctx, exec).Scan(&count)
	if err != nil {
		return false, errors.Wrap(err, "insurance_bundle: failed to check if change exists")
	}

	return count > 0, nil
}

// ChangeContainer pointed to by the foreign key.
func (o *Change) ChangeContainer(mods ...qm.QueryMod) changeContainerQuery {
	queryMods := []qm.QueryMod{
		qm.Where("\"id\" = ?", o.ChangeContainerID),
	}

	queryMods = append(queryMods, mods...)

	return ChangeContainers(queryMods...)
}

// LoadChangeContainer allows an eager lookup of values, cached into the
// loaded structs of the objects. This is for an N-1 relationship.
func (changeL) LoadChangeContainer(ctx context.Context, e boil.ContextExecutor, singular bool, maybeChange interface{}, mods queries.Applicator) error {
	var slice []*Change
	var object *Change

	if singular {
		object = maybeChange.(*Change)
	} else {
		slice = *maybeChange.(*[]*Change)
	}

	args := make([]interface{}, 0, 1)
	if singular {
		if object.R == nil {
			object.R = &changeR{}
		}
		args = append(args, object.ChangeContainerID)

	} else {
	Outer:
		for _, obj := range slice {
			if obj.R == nil {
				obj.R = &changeR{}
			}

			for _, a := range args {
				if a == obj.ChangeContainerID {
					continue Outer
				}
			}

			args = append(args, obj.ChangeContainerID)

		}
	}

	if len(args) == 0 {
		return nil
	}

	query := NewQuery(
		qm.From(`insurance_bundle.change_container`),
		qm.WhereIn(`insurance_bundle.change_container.id in ?`, args...),
	)
	if mods != nil {
		mods.Apply(query)
	}

	results, err := query.QueryContext(ctx, e)
	if err != nil {
		return errors.Wrap(err, "failed to eager load ChangeContainer")
	}

	var resultSlice []*ChangeContainer
	if err = queries.Bind(results, &resultSlice); err != nil {
		return errors.Wrap(err, "failed to bind eager loaded slice ChangeContainer")
	}

	if err = results.Close(); err != nil {
		return errors.Wrap(err, "failed to close results of eager load for change_container")
	}
	if err = results.Err(); err != nil {
		return errors.Wrap(err, "error occurred during iteration of eager loaded relations for change_container")
	}

	if len(changeAfterSelectHooks) != 0 {
		for _, obj := range resultSlice {
			if err := obj.doAfterSelectHooks(ctx, e); err != nil {
				return err
			}
		}
	}

	if len(resultSlice) == 0 {
		return nil
	}

	if singular {
		foreign := resultSlice[0]
		object.R.ChangeContainer = foreign
		if foreign.R == nil {
			foreign.R = &changeContainerR{}
		}
		foreign.R.Changes = append(foreign.R.Changes, object)
		return nil
	}

	for _, local := range slice {
		for _, foreign := range resultSlice {
			if local.ChangeContainerID == foreign.ID {
				local.R.ChangeContainer = foreign
				if foreign.R == nil {
					foreign.R = &changeContainerR{}
				}
				foreign.R.Changes = append(foreign.R.Changes, local)
				break
			}
		}
	}

	return nil
}

// SetChangeContainer of the change to the related item.
// Sets o.R.ChangeContainer to related.
// Adds o to related.R.Changes.
func (o *Change) SetChangeContainer(ctx context.Context, exec boil.ContextExecutor, insert bool, related *ChangeContainer) error {
	var err error
	if insert {
		if err = related.Insert(ctx, exec, boil.Infer()); err != nil {
			return errors.Wrap(err, "failed to insert into foreign table")
		}
	}

	updateQuery := fmt.Sprintf(
		"UPDATE \"insurance_bundle\".\"change\" SET %s WHERE %s",
		strmangle.SetParamNames("\"", "\"", 1, []string{"change_container_id"}),
		strmangle.WhereClause("\"", "\"", 2, changePrimaryKeyColumns),
	)
	values := []interface{}{related.ID, o.ID}

	if boil.IsDebug(ctx) {
		writer := boil.DebugWriterFrom(ctx)
		fmt.Fprintln(writer, updateQuery)
		fmt.Fprintln(writer, values)
	}
	if _, err = exec.ExecContext(ctx, updateQuery, values...); err != nil {
		return errors.Wrap(err, "failed to update local table")
	}

	o.ChangeContainerID = related.ID
	if o.R == nil {
		o.R = &changeR{
			ChangeContainer: related,
		}
	} else {
		o.R.ChangeContainer = related
	}

	if related.R == nil {
		related.R = &changeContainerR{
			Changes: ChangeSlice{o},
		}
	} else {
		related.R.Changes = append(related.R.Changes, o)
	}

	return nil
}

// Changes retrieves all the records using an executor.
func Changes(mods ...qm.QueryMod) changeQuery {
	mods = append(mods, qm.From("\"insurance_bundle\".\"change\""))
	q := NewQuery(mods...)
	if len(queries.GetSelect(q)) == 0 {
		queries.SetSelect(q, []string{"\"insurance_bundle\".\"change\".*"})
	}

	return changeQuery{q}
}

// FindChange retrieves a single record by ID with an executor.
// If selectCols is empty Find will return all columns.
func FindChange(ctx context.Context, exec boil.ContextExecutor, iD string, selectCols ...string) (*Change, error) {
	changeObj := &Change{}

	sel := "*"
	if len(selectCols) > 0 {
		sel = strings.Join(strmangle.IdentQuoteSlice(dialect.LQ, dialect.RQ, selectCols), ",")
	}
	query := fmt.Sprintf(
		"select %s from \"insurance_bundle\".\"change\" where \"id\"=$1", sel,
	)

	q := queries.Raw(query, iD)

	err := q.Bind(ctx, exec, changeObj)
	if err != nil {
		if errors.Is(err, sql.ErrNoRows) {
			return nil, sql.ErrNoRows
		}
		return nil, errors.Wrap(err, "insurance_bundle: unable to select from change")
	}

	if err = changeObj.doAfterSelectHooks(ctx, exec); err != nil {
		return changeObj, err
	}

	return changeObj, nil
}

// Insert a single record using an executor.
// See boil.Columns.InsertColumnSet documentation to understand column list inference for inserts.
func (o *Change) Insert(ctx context.Context, exec boil.ContextExecutor, columns boil.Columns) error {
	if o == nil {
		return errors.New("insurance_bundle: no change provided for insertion")
	}

	var err error
	if !boil.TimestampsAreSkipped(ctx) {
		currTime := time.Now().In(boil.GetLocation())

		if queries.MustTime(o.CreatedAt).IsZero() {
			queries.SetScanner(&o.CreatedAt, currTime)
		}
		if queries.MustTime(o.UpdatedAt).IsZero() {
			queries.SetScanner(&o.UpdatedAt, currTime)
		}
	}

	if err := o.doBeforeInsertHooks(ctx, exec); err != nil {
		return err
	}

	nzDefaults := queries.NonZeroDefaultSet(changeColumnsWithDefault, o)

	key := makeCacheKey(columns, nzDefaults)
	changeInsertCacheMut.RLock()
	cache, cached := changeInsertCache[key]
	changeInsertCacheMut.RUnlock()

	if !cached {
		wl, returnColumns := columns.InsertColumnSet(
			changeAllColumns,
			changeColumnsWithDefault,
			changeColumnsWithoutDefault,
			nzDefaults,
		)

		cache.valueMapping, err = queries.BindMapping(changeType, changeMapping, wl)
		if err != nil {
			return err
		}
		cache.retMapping, err = queries.BindMapping(changeType, changeMapping, returnColumns)
		if err != nil {
			return err
		}
		if len(wl) != 0 {
			cache.query = fmt.Sprintf("INSERT INTO \"insurance_bundle\".\"change\" (\"%s\") %%sVALUES (%s)%%s", strings.Join(wl, "\",\""), strmangle.Placeholders(dialect.UseIndexPlaceholders, len(wl), 1, 1))
		} else {
			cache.query = "INSERT INTO \"insurance_bundle\".\"change\" %sDEFAULT VALUES%s"
		}

		var queryOutput, queryReturning string

		if len(cache.retMapping) != 0 {
			queryReturning = fmt.Sprintf(" RETURNING \"%s\"", strings.Join(returnColumns, "\",\""))
		}

		cache.query = fmt.Sprintf(cache.query, queryOutput, queryReturning)
	}

	value := reflect.Indirect(reflect.ValueOf(o))
	vals := queries.ValuesFromMapping(value, cache.valueMapping)

	if boil.IsDebug(ctx) {
		writer := boil.DebugWriterFrom(ctx)
		fmt.Fprintln(writer, cache.query)
		fmt.Fprintln(writer, vals)
	}

	if len(cache.retMapping) != 0 {
		err = exec.QueryRowContext(ctx, cache.query, vals...).Scan(queries.PtrsFromMapping(value, cache.retMapping)...)
	} else {
		_, err = exec.ExecContext(ctx, cache.query, vals...)
	}

	if err != nil {
		return errors.Wrap(err, "insurance_bundle: unable to insert into change")
	}

	if !cached {
		changeInsertCacheMut.Lock()
		changeInsertCache[key] = cache
		changeInsertCacheMut.Unlock()
	}

	return o.doAfterInsertHooks(ctx, exec)
}

// Update uses an executor to update the Change.
// See boil.Columns.UpdateColumnSet documentation to understand column list inference for updates.
// Update does not automatically update the record in case of default values. Use .Reload() to refresh the records.
func (o *Change) Update(ctx context.Context, exec boil.ContextExecutor, columns boil.Columns) (int64, error) {
	if !boil.TimestampsAreSkipped(ctx) {
		currTime := time.Now().In(boil.GetLocation())

		queries.SetScanner(&o.UpdatedAt, currTime)
	}

	var err error
	if err = o.doBeforeUpdateHooks(ctx, exec); err != nil {
		return 0, err
	}
	key := makeCacheKey(columns, nil)
	changeUpdateCacheMut.RLock()
	cache, cached := changeUpdateCache[key]
	changeUpdateCacheMut.RUnlock()

	if !cached {
		wl := columns.UpdateColumnSet(
			changeAllColumns,
			changePrimaryKeyColumns,
		)

		if !columns.IsWhitelist() {
			wl = strmangle.SetComplement(wl, []string{"created_at"})
		}
		if len(wl) == 0 {
			return 0, errors.New("insurance_bundle: unable to update change, could not build whitelist")
		}

		cache.query = fmt.Sprintf("UPDATE \"insurance_bundle\".\"change\" SET %s WHERE %s",
			strmangle.SetParamNames("\"", "\"", 1, wl),
			strmangle.WhereClause("\"", "\"", len(wl)+1, changePrimaryKeyColumns),
		)
		cache.valueMapping, err = queries.BindMapping(changeType, changeMapping, append(wl, changePrimaryKeyColumns...))
		if err != nil {
			return 0, err
		}
	}

	values := queries.ValuesFromMapping(reflect.Indirect(reflect.ValueOf(o)), cache.valueMapping)

	if boil.IsDebug(ctx) {
		writer := boil.DebugWriterFrom(ctx)
		fmt.Fprintln(writer, cache.query)
		fmt.Fprintln(writer, values)
	}
	var result sql.Result
	result, err = exec.ExecContext(ctx, cache.query, values...)
	if err != nil {
		return 0, errors.Wrap(err, "insurance_bundle: unable to update change row")
	}

	rowsAff, err := result.RowsAffected()
	if err != nil {
		return 0, errors.Wrap(err, "insurance_bundle: failed to get rows affected by update for change")
	}

	if !cached {
		changeUpdateCacheMut.Lock()
		changeUpdateCache[key] = cache
		changeUpdateCacheMut.Unlock()
	}

	return rowsAff, o.doAfterUpdateHooks(ctx, exec)
}

// UpdateAll updates all rows with the specified column values.
func (q changeQuery) UpdateAll(ctx context.Context, exec boil.ContextExecutor, cols M) (int64, error) {
	queries.SetUpdate(q.Query, cols)

	result, err := q.Query.ExecContext(ctx, exec)
	if err != nil {
		return 0, errors.Wrap(err, "insurance_bundle: unable to update all for change")
	}

	rowsAff, err := result.RowsAffected()
	if err != nil {
		return 0, errors.Wrap(err, "insurance_bundle: unable to retrieve rows affected for change")
	}

	return rowsAff, nil
}

// UpdateAll updates all rows with the specified column values, using an executor.
func (o ChangeSlice) UpdateAll(ctx context.Context, exec boil.ContextExecutor, cols M) (int64, error) {
	ln := int64(len(o))
	if ln == 0 {
		return 0, nil
	}

	if len(cols) == 0 {
		return 0, errors.New("insurance_bundle: update all requires at least one column argument")
	}

	colNames := make([]string, len(cols))
	args := make([]interface{}, len(cols))

	i := 0
	for name, value := range cols {
		colNames[i] = name
		args[i] = value
		i++
	}

	// Append all of the primary key values for each column
	for _, obj := range o {
		pkeyArgs := queries.ValuesFromMapping(reflect.Indirect(reflect.ValueOf(obj)), changePrimaryKeyMapping)
		args = append(args, pkeyArgs...)
	}

	sql := fmt.Sprintf("UPDATE \"insurance_bundle\".\"change\" SET %s WHERE %s",
		strmangle.SetParamNames("\"", "\"", 1, colNames),
		strmangle.WhereClauseRepeated(string(dialect.LQ), string(dialect.RQ), len(colNames)+1, changePrimaryKeyColumns, len(o)))

	if boil.IsDebug(ctx) {
		writer := boil.DebugWriterFrom(ctx)
		fmt.Fprintln(writer, sql)
		fmt.Fprintln(writer, args...)
	}
	result, err := exec.ExecContext(ctx, sql, args...)
	if err != nil {
		return 0, errors.Wrap(err, "insurance_bundle: unable to update all in change slice")
	}

	rowsAff, err := result.RowsAffected()
	if err != nil {
		return 0, errors.Wrap(err, "insurance_bundle: unable to retrieve rows affected all in update all change")
	}
	return rowsAff, nil
}

// Upsert attempts an insert using an executor, and does an update or ignore on conflict.
// See boil.Columns documentation for how to properly use updateColumns and insertColumns.
func (o *Change) Upsert(ctx context.Context, exec boil.ContextExecutor, updateOnConflict bool, conflictColumns []string, updateColumns, insertColumns boil.Columns) error {
	if o == nil {
		return errors.New("insurance_bundle: no change provided for upsert")
	}
	if !boil.TimestampsAreSkipped(ctx) {
		currTime := time.Now().In(boil.GetLocation())

		if queries.MustTime(o.CreatedAt).IsZero() {
			queries.SetScanner(&o.CreatedAt, currTime)
		}
		queries.SetScanner(&o.UpdatedAt, currTime)
	}

	if err := o.doBeforeUpsertHooks(ctx, exec); err != nil {
		return err
	}

	nzDefaults := queries.NonZeroDefaultSet(changeColumnsWithDefault, o)

	// Build cache key in-line uglily - mysql vs psql problems
	buf := strmangle.GetBuffer()
	if updateOnConflict {
		buf.WriteByte('t')
	} else {
		buf.WriteByte('f')
	}
	buf.WriteByte('.')
	for _, c := range conflictColumns {
		buf.WriteString(c)
	}
	buf.WriteByte('.')
	buf.WriteString(strconv.Itoa(updateColumns.Kind))
	for _, c := range updateColumns.Cols {
		buf.WriteString(c)
	}
	buf.WriteByte('.')
	buf.WriteString(strconv.Itoa(insertColumns.Kind))
	for _, c := range insertColumns.Cols {
		buf.WriteString(c)
	}
	buf.WriteByte('.')
	for _, c := range nzDefaults {
		buf.WriteString(c)
	}
	key := buf.String()
	strmangle.PutBuffer(buf)

	changeUpsertCacheMut.RLock()
	cache, cached := changeUpsertCache[key]
	changeUpsertCacheMut.RUnlock()

	var err error

	if !cached {
		insert, ret := insertColumns.InsertColumnSet(
			changeAllColumns,
			changeColumnsWithDefault,
			changeColumnsWithoutDefault,
			nzDefaults,
		)

		update := updateColumns.UpdateColumnSet(
			changeAllColumns,
			changePrimaryKeyColumns,
		)

		if updateOnConflict && len(update) == 0 {
			return errors.New("insurance_bundle: unable to upsert change, could not build update column list")
		}

		conflict := conflictColumns
		if len(conflict) == 0 {
			conflict = make([]string, len(changePrimaryKeyColumns))
			copy(conflict, changePrimaryKeyColumns)
		}
		cache.query = buildUpsertQueryPostgres(dialect, "\"insurance_bundle\".\"change\"", updateOnConflict, ret, update, conflict, insert)

		cache.valueMapping, err = queries.BindMapping(changeType, changeMapping, insert)
		if err != nil {
			return err
		}
		if len(ret) != 0 {
			cache.retMapping, err = queries.BindMapping(changeType, changeMapping, ret)
			if err != nil {
				return err
			}
		}
	}

	value := reflect.Indirect(reflect.ValueOf(o))
	vals := queries.ValuesFromMapping(value, cache.valueMapping)
	var returns []interface{}
	if len(cache.retMapping) != 0 {
		returns = queries.PtrsFromMapping(value, cache.retMapping)
	}

	if boil.IsDebug(ctx) {
		writer := boil.DebugWriterFrom(ctx)
		fmt.Fprintln(writer, cache.query)
		fmt.Fprintln(writer, vals)
	}
	if len(cache.retMapping) != 0 {
		err = exec.QueryRowContext(ctx, cache.query, vals...).Scan(returns...)
		if errors.Is(err, sql.ErrNoRows) {
			err = nil // Postgres doesn't return anything when there's no update
		}
	} else {
		_, err = exec.ExecContext(ctx, cache.query, vals...)
	}
	if err != nil {
		return errors.Wrap(err, "insurance_bundle: unable to upsert change")
	}

	if !cached {
		changeUpsertCacheMut.Lock()
		changeUpsertCache[key] = cache
		changeUpsertCacheMut.Unlock()
	}

	return o.doAfterUpsertHooks(ctx, exec)
}

// Delete deletes a single Change record with an executor.
// Delete will match against the primary key column to find the record to delete.
func (o *Change) Delete(ctx context.Context, exec boil.ContextExecutor) (int64, error) {
	if o == nil {
		return 0, errors.New("insurance_bundle: no Change provided for delete")
	}

	if err := o.doBeforeDeleteHooks(ctx, exec); err != nil {
		return 0, err
	}

	args := queries.ValuesFromMapping(reflect.Indirect(reflect.ValueOf(o)), changePrimaryKeyMapping)
	sql := "DELETE FROM \"insurance_bundle\".\"change\" WHERE \"id\"=$1"

	if boil.IsDebug(ctx) {
		writer := boil.DebugWriterFrom(ctx)
		fmt.Fprintln(writer, sql)
		fmt.Fprintln(writer, args...)
	}
	result, err := exec.ExecContext(ctx, sql, args...)
	if err != nil {
		return 0, errors.Wrap(err, "insurance_bundle: unable to delete from change")
	}

	rowsAff, err := result.RowsAffected()
	if err != nil {
		return 0, errors.Wrap(err, "insurance_bundle: failed to get rows affected by delete for change")
	}

	if err := o.doAfterDeleteHooks(ctx, exec); err != nil {
		return 0, err
	}

	return rowsAff, nil
}

// DeleteAll deletes all matching rows.
func (q changeQuery) DeleteAll(ctx context.Context, exec boil.ContextExecutor) (int64, error) {
	if q.Query == nil {
		return 0, errors.New("insurance_bundle: no changeQuery provided for delete all")
	}

	queries.SetDelete(q.Query)

	result, err := q.Query.ExecContext(ctx, exec)
	if err != nil {
		return 0, errors.Wrap(err, "insurance_bundle: unable to delete all from change")
	}

	rowsAff, err := result.RowsAffected()
	if err != nil {
		return 0, errors.Wrap(err, "insurance_bundle: failed to get rows affected by deleteall for change")
	}

	return rowsAff, nil
}

// DeleteAll deletes all rows in the slice, using an executor.
func (o ChangeSlice) DeleteAll(ctx context.Context, exec boil.ContextExecutor) (int64, error) {
	if len(o) == 0 {
		return 0, nil
	}

	if len(changeBeforeDeleteHooks) != 0 {
		for _, obj := range o {
			if err := obj.doBeforeDeleteHooks(ctx, exec); err != nil {
				return 0, err
			}
		}
	}

	var args []interface{}
	for _, obj := range o {
		pkeyArgs := queries.ValuesFromMapping(reflect.Indirect(reflect.ValueOf(obj)), changePrimaryKeyMapping)
		args = append(args, pkeyArgs...)
	}

	sql := "DELETE FROM \"insurance_bundle\".\"change\" WHERE " +
		strmangle.WhereClauseRepeated(string(dialect.LQ), string(dialect.RQ), 1, changePrimaryKeyColumns, len(o))

	if boil.IsDebug(ctx) {
		writer := boil.DebugWriterFrom(ctx)
		fmt.Fprintln(writer, sql)
		fmt.Fprintln(writer, args)
	}
	result, err := exec.ExecContext(ctx, sql, args...)
	if err != nil {
		return 0, errors.Wrap(err, "insurance_bundle: unable to delete all from change slice")
	}

	rowsAff, err := result.RowsAffected()
	if err != nil {
		return 0, errors.Wrap(err, "insurance_bundle: failed to get rows affected by deleteall for change")
	}

	if len(changeAfterDeleteHooks) != 0 {
		for _, obj := range o {
			if err := obj.doAfterDeleteHooks(ctx, exec); err != nil {
				return 0, err
			}
		}
	}

	return rowsAff, nil
}

// Reload refetches the object from the database
// using the primary keys with an executor.
func (o *Change) Reload(ctx context.Context, exec boil.ContextExecutor) error {
	ret, err := FindChange(ctx, exec, o.ID)
	if err != nil {
		return err
	}

	*o = *ret
	return nil
}

// ReloadAll refetches every row with matching primary key column values
// and overwrites the original object slice with the newly updated slice.
func (o *ChangeSlice) ReloadAll(ctx context.Context, exec boil.ContextExecutor) error {
	if o == nil || len(*o) == 0 {
		return nil
	}

	slice := ChangeSlice{}
	var args []interface{}
	for _, obj := range *o {
		pkeyArgs := queries.ValuesFromMapping(reflect.Indirect(reflect.ValueOf(obj)), changePrimaryKeyMapping)
		args = append(args, pkeyArgs...)
	}

	sql := "SELECT \"insurance_bundle\".\"change\".* FROM \"insurance_bundle\".\"change\" WHERE " +
		strmangle.WhereClauseRepeated(string(dialect.LQ), string(dialect.RQ), 1, changePrimaryKeyColumns, len(*o))

	q := queries.Raw(sql, args...)

	err := q.Bind(ctx, exec, &slice)
	if err != nil {
		return errors.Wrap(err, "insurance_bundle: unable to reload all in ChangeSlice")
	}

	*o = slice

	return nil
}

// ChangeExists checks if the Change row exists.
func ChangeExists(ctx context.Context, exec boil.ContextExecutor, iD string) (bool, error) {
	var exists bool
	sql := "select exists(select 1 from \"insurance_bundle\".\"change\" where \"id\"=$1 limit 1)"

	if boil.IsDebug(ctx) {
		writer := boil.DebugWriterFrom(ctx)
		fmt.Fprintln(writer, sql)
		fmt.Fprintln(writer, iD)
	}
	row := exec.QueryRowContext(ctx, sql, iD)

	err := row.Scan(&exists)
	if err != nil {
		return false, errors.Wrap(err, "insurance_bundle: unable to check if change exists")
	}

	return exists, nil
}
