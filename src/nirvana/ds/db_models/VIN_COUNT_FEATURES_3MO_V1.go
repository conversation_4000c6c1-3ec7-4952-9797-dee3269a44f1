// Code generated by SQLBoiler (https://github.com/volatiletech/sqlboiler). DO NOT EDIT.
// This file is meant to be re-generated in place and/or deleted at any time.

package ds

import (
	"context"
	"database/sql"
	"fmt"
	"reflect"
	"strconv"
	"strings"
	"sync"
	"time"

	"github.com/friendsofgo/errors"
	"github.com/volatiletech/null/v8"
	"github.com/volatiletech/sqlboiler/v4/boil"
	"github.com/volatiletech/sqlboiler/v4/queries"
	"github.com/volatiletech/sqlboiler/v4/queries/qm"
	"github.com/volatiletech/sqlboiler/v4/queries/qmhelper"
	"github.com/volatiletech/sqlboiler/v4/types"
	"github.com/volatiletech/strmangle"
)

// VIN_COUNT_FEATURES_3MO_V1 is an object representing the database table.
type VIN_COUNT_FEATURES_3MO_V1 struct {
	END_DATE                           time.Time         `boil:"END_DATE" json:"END_DATE" toml:"END_DATE" yaml:"END_DATE"`
	START_DATE                         time.Time         `boil:"START_DATE" json:"START_DATE" toml:"START_DATE" yaml:"START_DATE"`
	UNIQUE_KEY                         null.String       `boil:"UNIQUE_KEY" json:"UNIQUE_KEY,omitempty" toml:"UNIQUE_KEY" yaml:"UNIQUE_KEY,omitempty"`
	UPDATED_AT                         null.Time         `boil:"UPDATED_AT" json:"UPDATED_AT,omitempty" toml:"UPDATED_AT" yaml:"UPDATED_AT,omitempty"`
	CONNECTION_ID                      string            `boil:"CONNECTION_ID" json:"CONNECTION_ID" toml:"CONNECTION_ID" yaml:"CONNECTION_ID"`
	AVG_VIN_COUNT_TRAIN                types.NullDecimal `boil:"AVG_VIN_COUNT_TRAIN" json:"AVG_VIN_COUNT_TRAIN,omitempty" toml:"AVG_VIN_COUNT_TRAIN" yaml:"AVG_VIN_COUNT_TRAIN,omitempty"`
	MAX_VIN_COUNT_TRAIN                null.Int64        `boil:"MAX_VIN_COUNT_TRAIN" json:"MAX_VIN_COUNT_TRAIN,omitempty" toml:"MAX_VIN_COUNT_TRAIN" yaml:"MAX_VIN_COUNT_TRAIN,omitempty"`
	MIN_VIN_COUNT_TRAIN                null.Int64        `boil:"MIN_VIN_COUNT_TRAIN" json:"MIN_VIN_COUNT_TRAIN,omitempty" toml:"MIN_VIN_COUNT_TRAIN" yaml:"MIN_VIN_COUNT_TRAIN,omitempty"`
	FLEET_SIZE_VARIABILITY             types.NullDecimal `boil:"FLEET_SIZE_VARIABILITY" json:"FLEET_SIZE_VARIABILITY,omitempty" toml:"FLEET_SIZE_VARIABILITY" yaml:"FLEET_SIZE_VARIABILITY,omitempty"`
	CARRIER_SIZE_TRAIN_LARGE           null.Int64        `boil:"CARRIER_SIZE_TRAIN_LARGE" json:"CARRIER_SIZE_TRAIN_LARGE,omitempty" toml:"CARRIER_SIZE_TRAIN_LARGE" yaml:"CARRIER_SIZE_TRAIN_LARGE,omitempty"`
	CARRIER_SIZE_TRAIN_MEDIUM          null.Int64        `boil:"CARRIER_SIZE_TRAIN_MEDIUM" json:"CARRIER_SIZE_TRAIN_MEDIUM,omitempty" toml:"CARRIER_SIZE_TRAIN_MEDIUM" yaml:"CARRIER_SIZE_TRAIN_MEDIUM,omitempty"`
	VEHICLE_UTILIZATION_RATIO          types.NullDecimal `boil:"VEHICLE_UTILIZATION_RATIO" json:"VEHICLE_UTILIZATION_RATIO,omitempty" toml:"VEHICLE_UTILIZATION_RATIO" yaml:"VEHICLE_UTILIZATION_RATIO,omitempty"`
	CARRIER_SIZE_TRAIN_VERY_LARGE      null.Int64        `boil:"CARRIER_SIZE_TRAIN_VERY_LARGE" json:"CARRIER_SIZE_TRAIN_VERY_LARGE,omitempty" toml:"CARRIER_SIZE_TRAIN_VERY_LARGE" yaml:"CARRIER_SIZE_TRAIN_VERY_LARGE,omitempty"`
	MAX_VIN_COUNT_TRAIN_GROWTH_6TO3MO  types.NullDecimal `boil:"MAX_VIN_COUNT_TRAIN_GROWTH_6TO3MO" json:"MAX_VIN_COUNT_TRAIN_GROWTH_6TO3MO,omitempty" toml:"MAX_VIN_COUNT_TRAIN_GROWTH_6TO3MO" yaml:"MAX_VIN_COUNT_TRAIN_GROWTH_6TO3MO,omitempty"`
	MAX_VIN_COUNT_TRAIN_GROWTH_9TO3MO  types.NullDecimal `boil:"MAX_VIN_COUNT_TRAIN_GROWTH_9TO3MO" json:"MAX_VIN_COUNT_TRAIN_GROWTH_9TO3MO,omitempty" toml:"MAX_VIN_COUNT_TRAIN_GROWTH_9TO3MO" yaml:"MAX_VIN_COUNT_TRAIN_GROWTH_9TO3MO,omitempty"`
	MAX_VIN_COUNT_TRAIN_GROWTH_12TO3MO types.NullDecimal `boil:"MAX_VIN_COUNT_TRAIN_GROWTH_12TO3MO" json:"MAX_VIN_COUNT_TRAIN_GROWTH_12TO3MO,omitempty" toml:"MAX_VIN_COUNT_TRAIN_GROWTH_12TO3MO" yaml:"MAX_VIN_COUNT_TRAIN_GROWTH_12TO3MO,omitempty"`

	R *vINCOUNT_FEATURES_3MO_V1R `boil:"" json:"" toml:"" yaml:""`
	L vINCOUNT_FEATURES_3MO_V1L  `boil:"-" json:"-" toml:"-" yaml:"-"`
}

var VIN_COUNT_FEATURES_3MO_V1Columns = struct {
	END_DATE                           string
	START_DATE                         string
	UNIQUE_KEY                         string
	UPDATED_AT                         string
	CONNECTION_ID                      string
	AVG_VIN_COUNT_TRAIN                string
	MAX_VIN_COUNT_TRAIN                string
	MIN_VIN_COUNT_TRAIN                string
	FLEET_SIZE_VARIABILITY             string
	CARRIER_SIZE_TRAIN_LARGE           string
	CARRIER_SIZE_TRAIN_MEDIUM          string
	VEHICLE_UTILIZATION_RATIO          string
	CARRIER_SIZE_TRAIN_VERY_LARGE      string
	MAX_VIN_COUNT_TRAIN_GROWTH_6TO3MO  string
	MAX_VIN_COUNT_TRAIN_GROWTH_9TO3MO  string
	MAX_VIN_COUNT_TRAIN_GROWTH_12TO3MO string
}{
	END_DATE:                           "END_DATE",
	START_DATE:                         "START_DATE",
	UNIQUE_KEY:                         "UNIQUE_KEY",
	UPDATED_AT:                         "UPDATED_AT",
	CONNECTION_ID:                      "CONNECTION_ID",
	AVG_VIN_COUNT_TRAIN:                "AVG_VIN_COUNT_TRAIN",
	MAX_VIN_COUNT_TRAIN:                "MAX_VIN_COUNT_TRAIN",
	MIN_VIN_COUNT_TRAIN:                "MIN_VIN_COUNT_TRAIN",
	FLEET_SIZE_VARIABILITY:             "FLEET_SIZE_VARIABILITY",
	CARRIER_SIZE_TRAIN_LARGE:           "CARRIER_SIZE_TRAIN_LARGE",
	CARRIER_SIZE_TRAIN_MEDIUM:          "CARRIER_SIZE_TRAIN_MEDIUM",
	VEHICLE_UTILIZATION_RATIO:          "VEHICLE_UTILIZATION_RATIO",
	CARRIER_SIZE_TRAIN_VERY_LARGE:      "CARRIER_SIZE_TRAIN_VERY_LARGE",
	MAX_VIN_COUNT_TRAIN_GROWTH_6TO3MO:  "MAX_VIN_COUNT_TRAIN_GROWTH_6TO3MO",
	MAX_VIN_COUNT_TRAIN_GROWTH_9TO3MO:  "MAX_VIN_COUNT_TRAIN_GROWTH_9TO3MO",
	MAX_VIN_COUNT_TRAIN_GROWTH_12TO3MO: "MAX_VIN_COUNT_TRAIN_GROWTH_12TO3MO",
}

var VIN_COUNT_FEATURES_3MO_V1TableColumns = struct {
	END_DATE                           string
	START_DATE                         string
	UNIQUE_KEY                         string
	UPDATED_AT                         string
	CONNECTION_ID                      string
	AVG_VIN_COUNT_TRAIN                string
	MAX_VIN_COUNT_TRAIN                string
	MIN_VIN_COUNT_TRAIN                string
	FLEET_SIZE_VARIABILITY             string
	CARRIER_SIZE_TRAIN_LARGE           string
	CARRIER_SIZE_TRAIN_MEDIUM          string
	VEHICLE_UTILIZATION_RATIO          string
	CARRIER_SIZE_TRAIN_VERY_LARGE      string
	MAX_VIN_COUNT_TRAIN_GROWTH_6TO3MO  string
	MAX_VIN_COUNT_TRAIN_GROWTH_9TO3MO  string
	MAX_VIN_COUNT_TRAIN_GROWTH_12TO3MO string
}{
	END_DATE:                           "VIN_COUNT_FEATURES_3MO_V1.END_DATE",
	START_DATE:                         "VIN_COUNT_FEATURES_3MO_V1.START_DATE",
	UNIQUE_KEY:                         "VIN_COUNT_FEATURES_3MO_V1.UNIQUE_KEY",
	UPDATED_AT:                         "VIN_COUNT_FEATURES_3MO_V1.UPDATED_AT",
	CONNECTION_ID:                      "VIN_COUNT_FEATURES_3MO_V1.CONNECTION_ID",
	AVG_VIN_COUNT_TRAIN:                "VIN_COUNT_FEATURES_3MO_V1.AVG_VIN_COUNT_TRAIN",
	MAX_VIN_COUNT_TRAIN:                "VIN_COUNT_FEATURES_3MO_V1.MAX_VIN_COUNT_TRAIN",
	MIN_VIN_COUNT_TRAIN:                "VIN_COUNT_FEATURES_3MO_V1.MIN_VIN_COUNT_TRAIN",
	FLEET_SIZE_VARIABILITY:             "VIN_COUNT_FEATURES_3MO_V1.FLEET_SIZE_VARIABILITY",
	CARRIER_SIZE_TRAIN_LARGE:           "VIN_COUNT_FEATURES_3MO_V1.CARRIER_SIZE_TRAIN_LARGE",
	CARRIER_SIZE_TRAIN_MEDIUM:          "VIN_COUNT_FEATURES_3MO_V1.CARRIER_SIZE_TRAIN_MEDIUM",
	VEHICLE_UTILIZATION_RATIO:          "VIN_COUNT_FEATURES_3MO_V1.VEHICLE_UTILIZATION_RATIO",
	CARRIER_SIZE_TRAIN_VERY_LARGE:      "VIN_COUNT_FEATURES_3MO_V1.CARRIER_SIZE_TRAIN_VERY_LARGE",
	MAX_VIN_COUNT_TRAIN_GROWTH_6TO3MO:  "VIN_COUNT_FEATURES_3MO_V1.MAX_VIN_COUNT_TRAIN_GROWTH_6TO3MO",
	MAX_VIN_COUNT_TRAIN_GROWTH_9TO3MO:  "VIN_COUNT_FEATURES_3MO_V1.MAX_VIN_COUNT_TRAIN_GROWTH_9TO3MO",
	MAX_VIN_COUNT_TRAIN_GROWTH_12TO3MO: "VIN_COUNT_FEATURES_3MO_V1.MAX_VIN_COUNT_TRAIN_GROWTH_12TO3MO",
}

// Generated where

var VIN_COUNT_FEATURES_3MO_V1Where = struct {
	END_DATE                           whereHelpertime_Time
	START_DATE                         whereHelpertime_Time
	UNIQUE_KEY                         whereHelpernull_String
	UPDATED_AT                         whereHelpernull_Time
	CONNECTION_ID                      whereHelperstring
	AVG_VIN_COUNT_TRAIN                whereHelpertypes_NullDecimal
	MAX_VIN_COUNT_TRAIN                whereHelpernull_Int64
	MIN_VIN_COUNT_TRAIN                whereHelpernull_Int64
	FLEET_SIZE_VARIABILITY             whereHelpertypes_NullDecimal
	CARRIER_SIZE_TRAIN_LARGE           whereHelpernull_Int64
	CARRIER_SIZE_TRAIN_MEDIUM          whereHelpernull_Int64
	VEHICLE_UTILIZATION_RATIO          whereHelpertypes_NullDecimal
	CARRIER_SIZE_TRAIN_VERY_LARGE      whereHelpernull_Int64
	MAX_VIN_COUNT_TRAIN_GROWTH_6TO3MO  whereHelpertypes_NullDecimal
	MAX_VIN_COUNT_TRAIN_GROWTH_9TO3MO  whereHelpertypes_NullDecimal
	MAX_VIN_COUNT_TRAIN_GROWTH_12TO3MO whereHelpertypes_NullDecimal
}{
	END_DATE:                           whereHelpertime_Time{field: "\"VIN_COUNT_FEATURES_3MO_V1\".\"END_DATE\""},
	START_DATE:                         whereHelpertime_Time{field: "\"VIN_COUNT_FEATURES_3MO_V1\".\"START_DATE\""},
	UNIQUE_KEY:                         whereHelpernull_String{field: "\"VIN_COUNT_FEATURES_3MO_V1\".\"UNIQUE_KEY\""},
	UPDATED_AT:                         whereHelpernull_Time{field: "\"VIN_COUNT_FEATURES_3MO_V1\".\"UPDATED_AT\""},
	CONNECTION_ID:                      whereHelperstring{field: "\"VIN_COUNT_FEATURES_3MO_V1\".\"CONNECTION_ID\""},
	AVG_VIN_COUNT_TRAIN:                whereHelpertypes_NullDecimal{field: "\"VIN_COUNT_FEATURES_3MO_V1\".\"AVG_VIN_COUNT_TRAIN\""},
	MAX_VIN_COUNT_TRAIN:                whereHelpernull_Int64{field: "\"VIN_COUNT_FEATURES_3MO_V1\".\"MAX_VIN_COUNT_TRAIN\""},
	MIN_VIN_COUNT_TRAIN:                whereHelpernull_Int64{field: "\"VIN_COUNT_FEATURES_3MO_V1\".\"MIN_VIN_COUNT_TRAIN\""},
	FLEET_SIZE_VARIABILITY:             whereHelpertypes_NullDecimal{field: "\"VIN_COUNT_FEATURES_3MO_V1\".\"FLEET_SIZE_VARIABILITY\""},
	CARRIER_SIZE_TRAIN_LARGE:           whereHelpernull_Int64{field: "\"VIN_COUNT_FEATURES_3MO_V1\".\"CARRIER_SIZE_TRAIN_LARGE\""},
	CARRIER_SIZE_TRAIN_MEDIUM:          whereHelpernull_Int64{field: "\"VIN_COUNT_FEATURES_3MO_V1\".\"CARRIER_SIZE_TRAIN_MEDIUM\""},
	VEHICLE_UTILIZATION_RATIO:          whereHelpertypes_NullDecimal{field: "\"VIN_COUNT_FEATURES_3MO_V1\".\"VEHICLE_UTILIZATION_RATIO\""},
	CARRIER_SIZE_TRAIN_VERY_LARGE:      whereHelpernull_Int64{field: "\"VIN_COUNT_FEATURES_3MO_V1\".\"CARRIER_SIZE_TRAIN_VERY_LARGE\""},
	MAX_VIN_COUNT_TRAIN_GROWTH_6TO3MO:  whereHelpertypes_NullDecimal{field: "\"VIN_COUNT_FEATURES_3MO_V1\".\"MAX_VIN_COUNT_TRAIN_GROWTH_6TO3MO\""},
	MAX_VIN_COUNT_TRAIN_GROWTH_9TO3MO:  whereHelpertypes_NullDecimal{field: "\"VIN_COUNT_FEATURES_3MO_V1\".\"MAX_VIN_COUNT_TRAIN_GROWTH_9TO3MO\""},
	MAX_VIN_COUNT_TRAIN_GROWTH_12TO3MO: whereHelpertypes_NullDecimal{field: "\"VIN_COUNT_FEATURES_3MO_V1\".\"MAX_VIN_COUNT_TRAIN_GROWTH_12TO3MO\""},
}

// VIN_COUNT_FEATURES_3MO_V1Rels is where relationship names are stored.
var VIN_COUNT_FEATURES_3MO_V1Rels = struct {
}{}

// vINCOUNT_FEATURES_3MO_V1R is where relationships are stored.
type vINCOUNT_FEATURES_3MO_V1R struct {
}

// NewStruct creates a new relationship struct
func (*vINCOUNT_FEATURES_3MO_V1R) NewStruct() *vINCOUNT_FEATURES_3MO_V1R {
	return &vINCOUNT_FEATURES_3MO_V1R{}
}

// vINCOUNT_FEATURES_3MO_V1L is where Load methods for each relationship are stored.
type vINCOUNT_FEATURES_3MO_V1L struct{}

var (
	vINCOUNT_FEATURES_3MO_V1AllColumns            = []string{"END_DATE", "START_DATE", "UNIQUE_KEY", "UPDATED_AT", "CONNECTION_ID", "AVG_VIN_COUNT_TRAIN", "MAX_VIN_COUNT_TRAIN", "MIN_VIN_COUNT_TRAIN", "FLEET_SIZE_VARIABILITY", "CARRIER_SIZE_TRAIN_LARGE", "CARRIER_SIZE_TRAIN_MEDIUM", "VEHICLE_UTILIZATION_RATIO", "CARRIER_SIZE_TRAIN_VERY_LARGE", "MAX_VIN_COUNT_TRAIN_GROWTH_6TO3MO", "MAX_VIN_COUNT_TRAIN_GROWTH_9TO3MO", "MAX_VIN_COUNT_TRAIN_GROWTH_12TO3MO"}
	vINCOUNT_FEATURES_3MO_V1ColumnsWithoutDefault = []string{"END_DATE", "START_DATE", "CONNECTION_ID"}
	vINCOUNT_FEATURES_3MO_V1ColumnsWithDefault    = []string{"UNIQUE_KEY", "UPDATED_AT", "AVG_VIN_COUNT_TRAIN", "MAX_VIN_COUNT_TRAIN", "MIN_VIN_COUNT_TRAIN", "FLEET_SIZE_VARIABILITY", "CARRIER_SIZE_TRAIN_LARGE", "CARRIER_SIZE_TRAIN_MEDIUM", "VEHICLE_UTILIZATION_RATIO", "CARRIER_SIZE_TRAIN_VERY_LARGE", "MAX_VIN_COUNT_TRAIN_GROWTH_6TO3MO", "MAX_VIN_COUNT_TRAIN_GROWTH_9TO3MO", "MAX_VIN_COUNT_TRAIN_GROWTH_12TO3MO"}
	vINCOUNT_FEATURES_3MO_V1PrimaryKeyColumns     = []string{"CONNECTION_ID", "START_DATE", "END_DATE"}
	vINCOUNT_FEATURES_3MO_V1GeneratedColumns      = []string{}
)

type (
	// VIN_COUNT_FEATURES_3MO_V1Slice is an alias for a slice of pointers to VIN_COUNT_FEATURES_3MO_V1.
	// This should almost always be used instead of []VIN_COUNT_FEATURES_3MO_V1.
	VIN_COUNT_FEATURES_3MO_V1Slice []*VIN_COUNT_FEATURES_3MO_V1
	// VIN_COUNT_FEATURES_3MO_V1Hook is the signature for custom VIN_COUNT_FEATURES_3MO_V1 hook methods
	VIN_COUNT_FEATURES_3MO_V1Hook func(context.Context, boil.ContextExecutor, *VIN_COUNT_FEATURES_3MO_V1) error

	vINCOUNT_FEATURES_3MO_V1Query struct {
		*queries.Query
	}
)

// Cache for insert, update and upsert
var (
	vINCOUNT_FEATURES_3MO_V1Type                 = reflect.TypeOf(&VIN_COUNT_FEATURES_3MO_V1{})
	vINCOUNT_FEATURES_3MO_V1Mapping              = queries.MakeStructMapping(vINCOUNT_FEATURES_3MO_V1Type)
	vINCOUNT_FEATURES_3MO_V1PrimaryKeyMapping, _ = queries.BindMapping(vINCOUNT_FEATURES_3MO_V1Type, vINCOUNT_FEATURES_3MO_V1Mapping, vINCOUNT_FEATURES_3MO_V1PrimaryKeyColumns)
	vINCOUNT_FEATURES_3MO_V1InsertCacheMut       sync.RWMutex
	vINCOUNT_FEATURES_3MO_V1InsertCache          = make(map[string]insertCache)
	vINCOUNT_FEATURES_3MO_V1UpdateCacheMut       sync.RWMutex
	vINCOUNT_FEATURES_3MO_V1UpdateCache          = make(map[string]updateCache)
	vINCOUNT_FEATURES_3MO_V1UpsertCacheMut       sync.RWMutex
	vINCOUNT_FEATURES_3MO_V1UpsertCache          = make(map[string]insertCache)
)

var (
	// Force time package dependency for automated UpdatedAt/CreatedAt.
	_ = time.Second
	// Force qmhelper dependency for where clause generation (which doesn't
	// always happen)
	_ = qmhelper.Where
)

var vINCOUNT_FEATURES_3MO_V1AfterSelectHooks []VIN_COUNT_FEATURES_3MO_V1Hook

var vINCOUNT_FEATURES_3MO_V1BeforeInsertHooks []VIN_COUNT_FEATURES_3MO_V1Hook
var vINCOUNT_FEATURES_3MO_V1AfterInsertHooks []VIN_COUNT_FEATURES_3MO_V1Hook

var vINCOUNT_FEATURES_3MO_V1BeforeUpdateHooks []VIN_COUNT_FEATURES_3MO_V1Hook
var vINCOUNT_FEATURES_3MO_V1AfterUpdateHooks []VIN_COUNT_FEATURES_3MO_V1Hook

var vINCOUNT_FEATURES_3MO_V1BeforeDeleteHooks []VIN_COUNT_FEATURES_3MO_V1Hook
var vINCOUNT_FEATURES_3MO_V1AfterDeleteHooks []VIN_COUNT_FEATURES_3MO_V1Hook

var vINCOUNT_FEATURES_3MO_V1BeforeUpsertHooks []VIN_COUNT_FEATURES_3MO_V1Hook
var vINCOUNT_FEATURES_3MO_V1AfterUpsertHooks []VIN_COUNT_FEATURES_3MO_V1Hook

// doAfterSelectHooks executes all "after Select" hooks.
func (o *VIN_COUNT_FEATURES_3MO_V1) doAfterSelectHooks(ctx context.Context, exec boil.ContextExecutor) (err error) {
	if boil.HooksAreSkipped(ctx) {
		return nil
	}

	for _, hook := range vINCOUNT_FEATURES_3MO_V1AfterSelectHooks {
		if err := hook(ctx, exec, o); err != nil {
			return err
		}
	}

	return nil
}

// doBeforeInsertHooks executes all "before insert" hooks.
func (o *VIN_COUNT_FEATURES_3MO_V1) doBeforeInsertHooks(ctx context.Context, exec boil.ContextExecutor) (err error) {
	if boil.HooksAreSkipped(ctx) {
		return nil
	}

	for _, hook := range vINCOUNT_FEATURES_3MO_V1BeforeInsertHooks {
		if err := hook(ctx, exec, o); err != nil {
			return err
		}
	}

	return nil
}

// doAfterInsertHooks executes all "after Insert" hooks.
func (o *VIN_COUNT_FEATURES_3MO_V1) doAfterInsertHooks(ctx context.Context, exec boil.ContextExecutor) (err error) {
	if boil.HooksAreSkipped(ctx) {
		return nil
	}

	for _, hook := range vINCOUNT_FEATURES_3MO_V1AfterInsertHooks {
		if err := hook(ctx, exec, o); err != nil {
			return err
		}
	}

	return nil
}

// doBeforeUpdateHooks executes all "before Update" hooks.
func (o *VIN_COUNT_FEATURES_3MO_V1) doBeforeUpdateHooks(ctx context.Context, exec boil.ContextExecutor) (err error) {
	if boil.HooksAreSkipped(ctx) {
		return nil
	}

	for _, hook := range vINCOUNT_FEATURES_3MO_V1BeforeUpdateHooks {
		if err := hook(ctx, exec, o); err != nil {
			return err
		}
	}

	return nil
}

// doAfterUpdateHooks executes all "after Update" hooks.
func (o *VIN_COUNT_FEATURES_3MO_V1) doAfterUpdateHooks(ctx context.Context, exec boil.ContextExecutor) (err error) {
	if boil.HooksAreSkipped(ctx) {
		return nil
	}

	for _, hook := range vINCOUNT_FEATURES_3MO_V1AfterUpdateHooks {
		if err := hook(ctx, exec, o); err != nil {
			return err
		}
	}

	return nil
}

// doBeforeDeleteHooks executes all "before Delete" hooks.
func (o *VIN_COUNT_FEATURES_3MO_V1) doBeforeDeleteHooks(ctx context.Context, exec boil.ContextExecutor) (err error) {
	if boil.HooksAreSkipped(ctx) {
		return nil
	}

	for _, hook := range vINCOUNT_FEATURES_3MO_V1BeforeDeleteHooks {
		if err := hook(ctx, exec, o); err != nil {
			return err
		}
	}

	return nil
}

// doAfterDeleteHooks executes all "after Delete" hooks.
func (o *VIN_COUNT_FEATURES_3MO_V1) doAfterDeleteHooks(ctx context.Context, exec boil.ContextExecutor) (err error) {
	if boil.HooksAreSkipped(ctx) {
		return nil
	}

	for _, hook := range vINCOUNT_FEATURES_3MO_V1AfterDeleteHooks {
		if err := hook(ctx, exec, o); err != nil {
			return err
		}
	}

	return nil
}

// doBeforeUpsertHooks executes all "before Upsert" hooks.
func (o *VIN_COUNT_FEATURES_3MO_V1) doBeforeUpsertHooks(ctx context.Context, exec boil.ContextExecutor) (err error) {
	if boil.HooksAreSkipped(ctx) {
		return nil
	}

	for _, hook := range vINCOUNT_FEATURES_3MO_V1BeforeUpsertHooks {
		if err := hook(ctx, exec, o); err != nil {
			return err
		}
	}

	return nil
}

// doAfterUpsertHooks executes all "after Upsert" hooks.
func (o *VIN_COUNT_FEATURES_3MO_V1) doAfterUpsertHooks(ctx context.Context, exec boil.ContextExecutor) (err error) {
	if boil.HooksAreSkipped(ctx) {
		return nil
	}

	for _, hook := range vINCOUNT_FEATURES_3MO_V1AfterUpsertHooks {
		if err := hook(ctx, exec, o); err != nil {
			return err
		}
	}

	return nil
}

// AddVIN_COUNT_FEATURES_3MO_V1Hook registers your hook function for all future operations.
func AddVIN_COUNT_FEATURES_3MO_V1Hook(hookPoint boil.HookPoint, vINCOUNT_FEATURES_3MO_V1Hook VIN_COUNT_FEATURES_3MO_V1Hook) {
	switch hookPoint {
	case boil.AfterSelectHook:
		vINCOUNT_FEATURES_3MO_V1AfterSelectHooks = append(vINCOUNT_FEATURES_3MO_V1AfterSelectHooks, vINCOUNT_FEATURES_3MO_V1Hook)
	case boil.BeforeInsertHook:
		vINCOUNT_FEATURES_3MO_V1BeforeInsertHooks = append(vINCOUNT_FEATURES_3MO_V1BeforeInsertHooks, vINCOUNT_FEATURES_3MO_V1Hook)
	case boil.AfterInsertHook:
		vINCOUNT_FEATURES_3MO_V1AfterInsertHooks = append(vINCOUNT_FEATURES_3MO_V1AfterInsertHooks, vINCOUNT_FEATURES_3MO_V1Hook)
	case boil.BeforeUpdateHook:
		vINCOUNT_FEATURES_3MO_V1BeforeUpdateHooks = append(vINCOUNT_FEATURES_3MO_V1BeforeUpdateHooks, vINCOUNT_FEATURES_3MO_V1Hook)
	case boil.AfterUpdateHook:
		vINCOUNT_FEATURES_3MO_V1AfterUpdateHooks = append(vINCOUNT_FEATURES_3MO_V1AfterUpdateHooks, vINCOUNT_FEATURES_3MO_V1Hook)
	case boil.BeforeDeleteHook:
		vINCOUNT_FEATURES_3MO_V1BeforeDeleteHooks = append(vINCOUNT_FEATURES_3MO_V1BeforeDeleteHooks, vINCOUNT_FEATURES_3MO_V1Hook)
	case boil.AfterDeleteHook:
		vINCOUNT_FEATURES_3MO_V1AfterDeleteHooks = append(vINCOUNT_FEATURES_3MO_V1AfterDeleteHooks, vINCOUNT_FEATURES_3MO_V1Hook)
	case boil.BeforeUpsertHook:
		vINCOUNT_FEATURES_3MO_V1BeforeUpsertHooks = append(vINCOUNT_FEATURES_3MO_V1BeforeUpsertHooks, vINCOUNT_FEATURES_3MO_V1Hook)
	case boil.AfterUpsertHook:
		vINCOUNT_FEATURES_3MO_V1AfterUpsertHooks = append(vINCOUNT_FEATURES_3MO_V1AfterUpsertHooks, vINCOUNT_FEATURES_3MO_V1Hook)
	}
}

// One returns a single vINCOUNT_FEATURES_3MO_V1 record from the query.
func (q vINCOUNT_FEATURES_3MO_V1Query) One(ctx context.Context, exec boil.ContextExecutor) (*VIN_COUNT_FEATURES_3MO_V1, error) {
	o := &VIN_COUNT_FEATURES_3MO_V1{}

	queries.SetLimit(q.Query, 1)

	err := q.Bind(ctx, exec, o)
	if err != nil {
		if errors.Is(err, sql.ErrNoRows) {
			return nil, sql.ErrNoRows
		}
		return nil, errors.Wrap(err, "ds: failed to execute a one query for VIN_COUNT_FEATURES_3MO_V1")
	}

	if err := o.doAfterSelectHooks(ctx, exec); err != nil {
		return o, err
	}

	return o, nil
}

// All returns all VIN_COUNT_FEATURES_3MO_V1 records from the query.
func (q vINCOUNT_FEATURES_3MO_V1Query) All(ctx context.Context, exec boil.ContextExecutor) (VIN_COUNT_FEATURES_3MO_V1Slice, error) {
	var o []*VIN_COUNT_FEATURES_3MO_V1

	err := q.Bind(ctx, exec, &o)
	if err != nil {
		return nil, errors.Wrap(err, "ds: failed to assign all query results to VIN_COUNT_FEATURES_3MO_V1 slice")
	}

	if len(vINCOUNT_FEATURES_3MO_V1AfterSelectHooks) != 0 {
		for _, obj := range o {
			if err := obj.doAfterSelectHooks(ctx, exec); err != nil {
				return o, err
			}
		}
	}

	return o, nil
}

// Count returns the count of all VIN_COUNT_FEATURES_3MO_V1 records in the query.
func (q vINCOUNT_FEATURES_3MO_V1Query) Count(ctx context.Context, exec boil.ContextExecutor) (int64, error) {
	var count int64

	queries.SetSelect(q.Query, nil)
	queries.SetCount(q.Query)

	err := q.Query.QueryRowContext(ctx, exec).Scan(&count)
	if err != nil {
		return 0, errors.Wrap(err, "ds: failed to count VIN_COUNT_FEATURES_3MO_V1 rows")
	}

	return count, nil
}

// Exists checks if the row exists in the table.
func (q vINCOUNT_FEATURES_3MO_V1Query) Exists(ctx context.Context, exec boil.ContextExecutor) (bool, error) {
	var count int64

	queries.SetSelect(q.Query, nil)
	queries.SetCount(q.Query)
	queries.SetLimit(q.Query, 1)

	err := q.Query.QueryRowContext(ctx, exec).Scan(&count)
	if err != nil {
		return false, errors.Wrap(err, "ds: failed to check if VIN_COUNT_FEATURES_3MO_V1 exists")
	}

	return count > 0, nil
}

// VINCOUNTFEATURES3MOV1S retrieves all the records using an executor.
func VINCOUNTFEATURES3MOV1S(mods ...qm.QueryMod) vINCOUNT_FEATURES_3MO_V1Query {
	mods = append(mods, qm.From("\"VIN_COUNT_FEATURES_3MO_V1\""))
	q := NewQuery(mods...)
	if len(queries.GetSelect(q)) == 0 {
		queries.SetSelect(q, []string{"\"VIN_COUNT_FEATURES_3MO_V1\".*"})
	}

	return vINCOUNT_FEATURES_3MO_V1Query{q}
}

// FindVIN_COUNT_FEATURES_3MO_V1 retrieves a single record by ID with an executor.
// If selectCols is empty Find will return all columns.
func FindVIN_COUNT_FEATURES_3MO_V1(ctx context.Context, exec boil.ContextExecutor, cONNECTIONID string, sTARTDATE time.Time, eNDDATE time.Time, selectCols ...string) (*VIN_COUNT_FEATURES_3MO_V1, error) {
	vINCOUNT_FEATURES_3MO_V1Obj := &VIN_COUNT_FEATURES_3MO_V1{}

	sel := "*"
	if len(selectCols) > 0 {
		sel = strings.Join(strmangle.IdentQuoteSlice(dialect.LQ, dialect.RQ, selectCols), ",")
	}
	query := fmt.Sprintf(
		"select %s from \"VIN_COUNT_FEATURES_3MO_V1\" where \"CONNECTION_ID\"=$1 AND \"START_DATE\"=$2 AND \"END_DATE\"=$3", sel,
	)

	q := queries.Raw(query, cONNECTIONID, sTARTDATE, eNDDATE)

	err := q.Bind(ctx, exec, vINCOUNT_FEATURES_3MO_V1Obj)
	if err != nil {
		if errors.Is(err, sql.ErrNoRows) {
			return nil, sql.ErrNoRows
		}
		return nil, errors.Wrap(err, "ds: unable to select from VIN_COUNT_FEATURES_3MO_V1")
	}

	if err = vINCOUNT_FEATURES_3MO_V1Obj.doAfterSelectHooks(ctx, exec); err != nil {
		return vINCOUNT_FEATURES_3MO_V1Obj, err
	}

	return vINCOUNT_FEATURES_3MO_V1Obj, nil
}

// Insert a single record using an executor.
// See boil.Columns.InsertColumnSet documentation to understand column list inference for inserts.
func (o *VIN_COUNT_FEATURES_3MO_V1) Insert(ctx context.Context, exec boil.ContextExecutor, columns boil.Columns) error {
	if o == nil {
		return errors.New("ds: no VIN_COUNT_FEATURES_3MO_V1 provided for insertion")
	}

	var err error

	if err := o.doBeforeInsertHooks(ctx, exec); err != nil {
		return err
	}

	nzDefaults := queries.NonZeroDefaultSet(vINCOUNT_FEATURES_3MO_V1ColumnsWithDefault, o)

	key := makeCacheKey(columns, nzDefaults)
	vINCOUNT_FEATURES_3MO_V1InsertCacheMut.RLock()
	cache, cached := vINCOUNT_FEATURES_3MO_V1InsertCache[key]
	vINCOUNT_FEATURES_3MO_V1InsertCacheMut.RUnlock()

	if !cached {
		wl, returnColumns := columns.InsertColumnSet(
			vINCOUNT_FEATURES_3MO_V1AllColumns,
			vINCOUNT_FEATURES_3MO_V1ColumnsWithDefault,
			vINCOUNT_FEATURES_3MO_V1ColumnsWithoutDefault,
			nzDefaults,
		)

		cache.valueMapping, err = queries.BindMapping(vINCOUNT_FEATURES_3MO_V1Type, vINCOUNT_FEATURES_3MO_V1Mapping, wl)
		if err != nil {
			return err
		}
		cache.retMapping, err = queries.BindMapping(vINCOUNT_FEATURES_3MO_V1Type, vINCOUNT_FEATURES_3MO_V1Mapping, returnColumns)
		if err != nil {
			return err
		}
		if len(wl) != 0 {
			cache.query = fmt.Sprintf("INSERT INTO \"VIN_COUNT_FEATURES_3MO_V1\" (\"%s\") %%sVALUES (%s)%%s", strings.Join(wl, "\",\""), strmangle.Placeholders(dialect.UseIndexPlaceholders, len(wl), 1, 1))
		} else {
			cache.query = "INSERT INTO \"VIN_COUNT_FEATURES_3MO_V1\" %sDEFAULT VALUES%s"
		}

		var queryOutput, queryReturning string

		if len(cache.retMapping) != 0 {
			queryReturning = fmt.Sprintf(" RETURNING \"%s\"", strings.Join(returnColumns, "\",\""))
		}

		cache.query = fmt.Sprintf(cache.query, queryOutput, queryReturning)
	}

	value := reflect.Indirect(reflect.ValueOf(o))
	vals := queries.ValuesFromMapping(value, cache.valueMapping)

	if boil.IsDebug(ctx) {
		writer := boil.DebugWriterFrom(ctx)
		fmt.Fprintln(writer, cache.query)
		fmt.Fprintln(writer, vals)
	}

	if len(cache.retMapping) != 0 {
		err = exec.QueryRowContext(ctx, cache.query, vals...).Scan(queries.PtrsFromMapping(value, cache.retMapping)...)
	} else {
		_, err = exec.ExecContext(ctx, cache.query, vals...)
	}

	if err != nil {
		return errors.Wrap(err, "ds: unable to insert into VIN_COUNT_FEATURES_3MO_V1")
	}

	if !cached {
		vINCOUNT_FEATURES_3MO_V1InsertCacheMut.Lock()
		vINCOUNT_FEATURES_3MO_V1InsertCache[key] = cache
		vINCOUNT_FEATURES_3MO_V1InsertCacheMut.Unlock()
	}

	return o.doAfterInsertHooks(ctx, exec)
}

// Update uses an executor to update the VIN_COUNT_FEATURES_3MO_V1.
// See boil.Columns.UpdateColumnSet documentation to understand column list inference for updates.
// Update does not automatically update the record in case of default values. Use .Reload() to refresh the records.
func (o *VIN_COUNT_FEATURES_3MO_V1) Update(ctx context.Context, exec boil.ContextExecutor, columns boil.Columns) (int64, error) {
	var err error
	if err = o.doBeforeUpdateHooks(ctx, exec); err != nil {
		return 0, err
	}
	key := makeCacheKey(columns, nil)
	vINCOUNT_FEATURES_3MO_V1UpdateCacheMut.RLock()
	cache, cached := vINCOUNT_FEATURES_3MO_V1UpdateCache[key]
	vINCOUNT_FEATURES_3MO_V1UpdateCacheMut.RUnlock()

	if !cached {
		wl := columns.UpdateColumnSet(
			vINCOUNT_FEATURES_3MO_V1AllColumns,
			vINCOUNT_FEATURES_3MO_V1PrimaryKeyColumns,
		)

		if !columns.IsWhitelist() {
			wl = strmangle.SetComplement(wl, []string{"created_at"})
		}
		if len(wl) == 0 {
			return 0, errors.New("ds: unable to update VIN_COUNT_FEATURES_3MO_V1, could not build whitelist")
		}

		cache.query = fmt.Sprintf("UPDATE \"VIN_COUNT_FEATURES_3MO_V1\" SET %s WHERE %s",
			strmangle.SetParamNames("\"", "\"", 1, wl),
			strmangle.WhereClause("\"", "\"", len(wl)+1, vINCOUNT_FEATURES_3MO_V1PrimaryKeyColumns),
		)
		cache.valueMapping, err = queries.BindMapping(vINCOUNT_FEATURES_3MO_V1Type, vINCOUNT_FEATURES_3MO_V1Mapping, append(wl, vINCOUNT_FEATURES_3MO_V1PrimaryKeyColumns...))
		if err != nil {
			return 0, err
		}
	}

	values := queries.ValuesFromMapping(reflect.Indirect(reflect.ValueOf(o)), cache.valueMapping)

	if boil.IsDebug(ctx) {
		writer := boil.DebugWriterFrom(ctx)
		fmt.Fprintln(writer, cache.query)
		fmt.Fprintln(writer, values)
	}
	var result sql.Result
	result, err = exec.ExecContext(ctx, cache.query, values...)
	if err != nil {
		return 0, errors.Wrap(err, "ds: unable to update VIN_COUNT_FEATURES_3MO_V1 row")
	}

	rowsAff, err := result.RowsAffected()
	if err != nil {
		return 0, errors.Wrap(err, "ds: failed to get rows affected by update for VIN_COUNT_FEATURES_3MO_V1")
	}

	if !cached {
		vINCOUNT_FEATURES_3MO_V1UpdateCacheMut.Lock()
		vINCOUNT_FEATURES_3MO_V1UpdateCache[key] = cache
		vINCOUNT_FEATURES_3MO_V1UpdateCacheMut.Unlock()
	}

	return rowsAff, o.doAfterUpdateHooks(ctx, exec)
}

// UpdateAll updates all rows with the specified column values.
func (q vINCOUNT_FEATURES_3MO_V1Query) UpdateAll(ctx context.Context, exec boil.ContextExecutor, cols M) (int64, error) {
	queries.SetUpdate(q.Query, cols)

	result, err := q.Query.ExecContext(ctx, exec)
	if err != nil {
		return 0, errors.Wrap(err, "ds: unable to update all for VIN_COUNT_FEATURES_3MO_V1")
	}

	rowsAff, err := result.RowsAffected()
	if err != nil {
		return 0, errors.Wrap(err, "ds: unable to retrieve rows affected for VIN_COUNT_FEATURES_3MO_V1")
	}

	return rowsAff, nil
}

// UpdateAll updates all rows with the specified column values, using an executor.
func (o VIN_COUNT_FEATURES_3MO_V1Slice) UpdateAll(ctx context.Context, exec boil.ContextExecutor, cols M) (int64, error) {
	ln := int64(len(o))
	if ln == 0 {
		return 0, nil
	}

	if len(cols) == 0 {
		return 0, errors.New("ds: update all requires at least one column argument")
	}

	colNames := make([]string, len(cols))
	args := make([]interface{}, len(cols))

	i := 0
	for name, value := range cols {
		colNames[i] = name
		args[i] = value
		i++
	}

	// Append all of the primary key values for each column
	for _, obj := range o {
		pkeyArgs := queries.ValuesFromMapping(reflect.Indirect(reflect.ValueOf(obj)), vINCOUNT_FEATURES_3MO_V1PrimaryKeyMapping)
		args = append(args, pkeyArgs...)
	}

	sql := fmt.Sprintf("UPDATE \"VIN_COUNT_FEATURES_3MO_V1\" SET %s WHERE %s",
		strmangle.SetParamNames("\"", "\"", 1, colNames),
		strmangle.WhereClauseRepeated(string(dialect.LQ), string(dialect.RQ), len(colNames)+1, vINCOUNT_FEATURES_3MO_V1PrimaryKeyColumns, len(o)))

	if boil.IsDebug(ctx) {
		writer := boil.DebugWriterFrom(ctx)
		fmt.Fprintln(writer, sql)
		fmt.Fprintln(writer, args...)
	}
	result, err := exec.ExecContext(ctx, sql, args...)
	if err != nil {
		return 0, errors.Wrap(err, "ds: unable to update all in vINCOUNT_FEATURES_3MO_V1 slice")
	}

	rowsAff, err := result.RowsAffected()
	if err != nil {
		return 0, errors.Wrap(err, "ds: unable to retrieve rows affected all in update all vINCOUNT_FEATURES_3MO_V1")
	}
	return rowsAff, nil
}

// Upsert attempts an insert using an executor, and does an update or ignore on conflict.
// See boil.Columns documentation for how to properly use updateColumns and insertColumns.
func (o *VIN_COUNT_FEATURES_3MO_V1) Upsert(ctx context.Context, exec boil.ContextExecutor, updateOnConflict bool, conflictColumns []string, updateColumns, insertColumns boil.Columns) error {
	if o == nil {
		return errors.New("ds: no VIN_COUNT_FEATURES_3MO_V1 provided for upsert")
	}

	if err := o.doBeforeUpsertHooks(ctx, exec); err != nil {
		return err
	}

	nzDefaults := queries.NonZeroDefaultSet(vINCOUNT_FEATURES_3MO_V1ColumnsWithDefault, o)

	// Build cache key in-line uglily - mysql vs psql problems
	buf := strmangle.GetBuffer()
	if updateOnConflict {
		buf.WriteByte('t')
	} else {
		buf.WriteByte('f')
	}
	buf.WriteByte('.')
	for _, c := range conflictColumns {
		buf.WriteString(c)
	}
	buf.WriteByte('.')
	buf.WriteString(strconv.Itoa(updateColumns.Kind))
	for _, c := range updateColumns.Cols {
		buf.WriteString(c)
	}
	buf.WriteByte('.')
	buf.WriteString(strconv.Itoa(insertColumns.Kind))
	for _, c := range insertColumns.Cols {
		buf.WriteString(c)
	}
	buf.WriteByte('.')
	for _, c := range nzDefaults {
		buf.WriteString(c)
	}
	key := buf.String()
	strmangle.PutBuffer(buf)

	vINCOUNT_FEATURES_3MO_V1UpsertCacheMut.RLock()
	cache, cached := vINCOUNT_FEATURES_3MO_V1UpsertCache[key]
	vINCOUNT_FEATURES_3MO_V1UpsertCacheMut.RUnlock()

	var err error

	if !cached {
		insert, ret := insertColumns.InsertColumnSet(
			vINCOUNT_FEATURES_3MO_V1AllColumns,
			vINCOUNT_FEATURES_3MO_V1ColumnsWithDefault,
			vINCOUNT_FEATURES_3MO_V1ColumnsWithoutDefault,
			nzDefaults,
		)

		update := updateColumns.UpdateColumnSet(
			vINCOUNT_FEATURES_3MO_V1AllColumns,
			vINCOUNT_FEATURES_3MO_V1PrimaryKeyColumns,
		)

		if updateOnConflict && len(update) == 0 {
			return errors.New("ds: unable to upsert VIN_COUNT_FEATURES_3MO_V1, could not build update column list")
		}

		conflict := conflictColumns
		if len(conflict) == 0 {
			conflict = make([]string, len(vINCOUNT_FEATURES_3MO_V1PrimaryKeyColumns))
			copy(conflict, vINCOUNT_FEATURES_3MO_V1PrimaryKeyColumns)
		}
		cache.query = buildUpsertQueryPostgres(dialect, "\"VIN_COUNT_FEATURES_3MO_V1\"", updateOnConflict, ret, update, conflict, insert)

		cache.valueMapping, err = queries.BindMapping(vINCOUNT_FEATURES_3MO_V1Type, vINCOUNT_FEATURES_3MO_V1Mapping, insert)
		if err != nil {
			return err
		}
		if len(ret) != 0 {
			cache.retMapping, err = queries.BindMapping(vINCOUNT_FEATURES_3MO_V1Type, vINCOUNT_FEATURES_3MO_V1Mapping, ret)
			if err != nil {
				return err
			}
		}
	}

	value := reflect.Indirect(reflect.ValueOf(o))
	vals := queries.ValuesFromMapping(value, cache.valueMapping)
	var returns []interface{}
	if len(cache.retMapping) != 0 {
		returns = queries.PtrsFromMapping(value, cache.retMapping)
	}

	if boil.IsDebug(ctx) {
		writer := boil.DebugWriterFrom(ctx)
		fmt.Fprintln(writer, cache.query)
		fmt.Fprintln(writer, vals)
	}
	if len(cache.retMapping) != 0 {
		err = exec.QueryRowContext(ctx, cache.query, vals...).Scan(returns...)
		if errors.Is(err, sql.ErrNoRows) {
			err = nil // Postgres doesn't return anything when there's no update
		}
	} else {
		_, err = exec.ExecContext(ctx, cache.query, vals...)
	}
	if err != nil {
		return errors.Wrap(err, "ds: unable to upsert VIN_COUNT_FEATURES_3MO_V1")
	}

	if !cached {
		vINCOUNT_FEATURES_3MO_V1UpsertCacheMut.Lock()
		vINCOUNT_FEATURES_3MO_V1UpsertCache[key] = cache
		vINCOUNT_FEATURES_3MO_V1UpsertCacheMut.Unlock()
	}

	return o.doAfterUpsertHooks(ctx, exec)
}

// Delete deletes a single VIN_COUNT_FEATURES_3MO_V1 record with an executor.
// Delete will match against the primary key column to find the record to delete.
func (o *VIN_COUNT_FEATURES_3MO_V1) Delete(ctx context.Context, exec boil.ContextExecutor) (int64, error) {
	if o == nil {
		return 0, errors.New("ds: no VIN_COUNT_FEATURES_3MO_V1 provided for delete")
	}

	if err := o.doBeforeDeleteHooks(ctx, exec); err != nil {
		return 0, err
	}

	args := queries.ValuesFromMapping(reflect.Indirect(reflect.ValueOf(o)), vINCOUNT_FEATURES_3MO_V1PrimaryKeyMapping)
	sql := "DELETE FROM \"VIN_COUNT_FEATURES_3MO_V1\" WHERE \"CONNECTION_ID\"=$1 AND \"START_DATE\"=$2 AND \"END_DATE\"=$3"

	if boil.IsDebug(ctx) {
		writer := boil.DebugWriterFrom(ctx)
		fmt.Fprintln(writer, sql)
		fmt.Fprintln(writer, args...)
	}
	result, err := exec.ExecContext(ctx, sql, args...)
	if err != nil {
		return 0, errors.Wrap(err, "ds: unable to delete from VIN_COUNT_FEATURES_3MO_V1")
	}

	rowsAff, err := result.RowsAffected()
	if err != nil {
		return 0, errors.Wrap(err, "ds: failed to get rows affected by delete for VIN_COUNT_FEATURES_3MO_V1")
	}

	if err := o.doAfterDeleteHooks(ctx, exec); err != nil {
		return 0, err
	}

	return rowsAff, nil
}

// DeleteAll deletes all matching rows.
func (q vINCOUNT_FEATURES_3MO_V1Query) DeleteAll(ctx context.Context, exec boil.ContextExecutor) (int64, error) {
	if q.Query == nil {
		return 0, errors.New("ds: no vINCOUNT_FEATURES_3MO_V1Query provided for delete all")
	}

	queries.SetDelete(q.Query)

	result, err := q.Query.ExecContext(ctx, exec)
	if err != nil {
		return 0, errors.Wrap(err, "ds: unable to delete all from VIN_COUNT_FEATURES_3MO_V1")
	}

	rowsAff, err := result.RowsAffected()
	if err != nil {
		return 0, errors.Wrap(err, "ds: failed to get rows affected by deleteall for VIN_COUNT_FEATURES_3MO_V1")
	}

	return rowsAff, nil
}

// DeleteAll deletes all rows in the slice, using an executor.
func (o VIN_COUNT_FEATURES_3MO_V1Slice) DeleteAll(ctx context.Context, exec boil.ContextExecutor) (int64, error) {
	if len(o) == 0 {
		return 0, nil
	}

	if len(vINCOUNT_FEATURES_3MO_V1BeforeDeleteHooks) != 0 {
		for _, obj := range o {
			if err := obj.doBeforeDeleteHooks(ctx, exec); err != nil {
				return 0, err
			}
		}
	}

	var args []interface{}
	for _, obj := range o {
		pkeyArgs := queries.ValuesFromMapping(reflect.Indirect(reflect.ValueOf(obj)), vINCOUNT_FEATURES_3MO_V1PrimaryKeyMapping)
		args = append(args, pkeyArgs...)
	}

	sql := "DELETE FROM \"VIN_COUNT_FEATURES_3MO_V1\" WHERE " +
		strmangle.WhereClauseRepeated(string(dialect.LQ), string(dialect.RQ), 1, vINCOUNT_FEATURES_3MO_V1PrimaryKeyColumns, len(o))

	if boil.IsDebug(ctx) {
		writer := boil.DebugWriterFrom(ctx)
		fmt.Fprintln(writer, sql)
		fmt.Fprintln(writer, args)
	}
	result, err := exec.ExecContext(ctx, sql, args...)
	if err != nil {
		return 0, errors.Wrap(err, "ds: unable to delete all from vINCOUNT_FEATURES_3MO_V1 slice")
	}

	rowsAff, err := result.RowsAffected()
	if err != nil {
		return 0, errors.Wrap(err, "ds: failed to get rows affected by deleteall for VIN_COUNT_FEATURES_3MO_V1")
	}

	if len(vINCOUNT_FEATURES_3MO_V1AfterDeleteHooks) != 0 {
		for _, obj := range o {
			if err := obj.doAfterDeleteHooks(ctx, exec); err != nil {
				return 0, err
			}
		}
	}

	return rowsAff, nil
}

// Reload refetches the object from the database
// using the primary keys with an executor.
func (o *VIN_COUNT_FEATURES_3MO_V1) Reload(ctx context.Context, exec boil.ContextExecutor) error {
	ret, err := FindVIN_COUNT_FEATURES_3MO_V1(ctx, exec, o.CONNECTION_ID, o.START_DATE, o.END_DATE)
	if err != nil {
		return err
	}

	*o = *ret
	return nil
}

// ReloadAll refetches every row with matching primary key column values
// and overwrites the original object slice with the newly updated slice.
func (o *VIN_COUNT_FEATURES_3MO_V1Slice) ReloadAll(ctx context.Context, exec boil.ContextExecutor) error {
	if o == nil || len(*o) == 0 {
		return nil
	}

	slice := VIN_COUNT_FEATURES_3MO_V1Slice{}
	var args []interface{}
	for _, obj := range *o {
		pkeyArgs := queries.ValuesFromMapping(reflect.Indirect(reflect.ValueOf(obj)), vINCOUNT_FEATURES_3MO_V1PrimaryKeyMapping)
		args = append(args, pkeyArgs...)
	}

	sql := "SELECT \"VIN_COUNT_FEATURES_3MO_V1\".* FROM \"VIN_COUNT_FEATURES_3MO_V1\" WHERE " +
		strmangle.WhereClauseRepeated(string(dialect.LQ), string(dialect.RQ), 1, vINCOUNT_FEATURES_3MO_V1PrimaryKeyColumns, len(*o))

	q := queries.Raw(sql, args...)

	err := q.Bind(ctx, exec, &slice)
	if err != nil {
		return errors.Wrap(err, "ds: unable to reload all in VIN_COUNT_FEATURES_3MO_V1Slice")
	}

	*o = slice

	return nil
}

// VIN_COUNT_FEATURES_3MO_V1Exists checks if the VIN_COUNT_FEATURES_3MO_V1 row exists.
func VIN_COUNT_FEATURES_3MO_V1Exists(ctx context.Context, exec boil.ContextExecutor, cONNECTIONID string, sTARTDATE time.Time, eNDDATE time.Time) (bool, error) {
	var exists bool
	sql := "select exists(select 1 from \"VIN_COUNT_FEATURES_3MO_V1\" where \"CONNECTION_ID\"=$1 AND \"START_DATE\"=$2 AND \"END_DATE\"=$3 limit 1)"

	if boil.IsDebug(ctx) {
		writer := boil.DebugWriterFrom(ctx)
		fmt.Fprintln(writer, sql)
		fmt.Fprintln(writer, cONNECTIONID, sTARTDATE, eNDDATE)
	}
	row := exec.QueryRowContext(ctx, sql, cONNECTIONID, sTARTDATE, eNDDATE)

	err := row.Scan(&exists)
	if err != nil {
		return false, errors.Wrap(err, "ds: unable to check if VIN_COUNT_FEATURES_3MO_V1 exists")
	}

	return exists, nil
}
