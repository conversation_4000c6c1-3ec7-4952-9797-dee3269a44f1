// Code generated by SQLBoiler (https://github.com/volatiletech/sqlboiler). DO NOT EDIT.
// This file is meant to be re-generated in place and/or deleted at any time.

package jobbertables

import (
	"context"
	"database/sql"
	"fmt"
	"reflect"
	"strconv"
	"strings"
	"sync"
	"time"

	"github.com/friendsofgo/errors"
	"github.com/volatiletech/sqlboiler/v4/boil"
	"github.com/volatiletech/sqlboiler/v4/queries"
	"github.com/volatiletech/sqlboiler/v4/queries/qm"
	"github.com/volatiletech/sqlboiler/v4/queries/qmhelper"
	"github.com/volatiletech/sqlboiler/v4/types"
	"github.com/volatiletech/strmangle"
)

// Processor is an object representing the database table.
type Processor struct {
	ClusterID       string     `boil:"cluster_id" json:"cluster_id" toml:"cluster_id" yaml:"cluster_id"`
	ProcessorID     string     `boil:"processor_id" json:"processor_id" toml:"processor_id" yaml:"processor_id"`
	ProcessorInfo   types.JSON `boil:"processor_info" json:"processor_info" toml:"processor_info" yaml:"processor_info"`
	LastHeartbeat   types.JSON `boil:"last_heartbeat" json:"last_heartbeat" toml:"last_heartbeat" yaml:"last_heartbeat"`
	ExpiryTimestamp time.Time  `boil:"expiry_timestamp" json:"expiry_timestamp" toml:"expiry_timestamp" yaml:"expiry_timestamp"`
	Status          string     `boil:"status" json:"status" toml:"status" yaml:"status"`

	R *processorR `boil:"" json:"" toml:"" yaml:""`
	L processorL  `boil:"-" json:"-" toml:"-" yaml:"-"`
}

var ProcessorColumns = struct {
	ClusterID       string
	ProcessorID     string
	ProcessorInfo   string
	LastHeartbeat   string
	ExpiryTimestamp string
	Status          string
}{
	ClusterID:       "cluster_id",
	ProcessorID:     "processor_id",
	ProcessorInfo:   "processor_info",
	LastHeartbeat:   "last_heartbeat",
	ExpiryTimestamp: "expiry_timestamp",
	Status:          "status",
}

var ProcessorTableColumns = struct {
	ClusterID       string
	ProcessorID     string
	ProcessorInfo   string
	LastHeartbeat   string
	ExpiryTimestamp string
	Status          string
}{
	ClusterID:       "processors.cluster_id",
	ProcessorID:     "processors.processor_id",
	ProcessorInfo:   "processors.processor_info",
	LastHeartbeat:   "processors.last_heartbeat",
	ExpiryTimestamp: "processors.expiry_timestamp",
	Status:          "processors.status",
}

// Generated where

var ProcessorWhere = struct {
	ClusterID       whereHelperstring
	ProcessorID     whereHelperstring
	ProcessorInfo   whereHelpertypes_JSON
	LastHeartbeat   whereHelpertypes_JSON
	ExpiryTimestamp whereHelpertime_Time
	Status          whereHelperstring
}{
	ClusterID:       whereHelperstring{field: "\"jobber\".\"processors\".\"cluster_id\""},
	ProcessorID:     whereHelperstring{field: "\"jobber\".\"processors\".\"processor_id\""},
	ProcessorInfo:   whereHelpertypes_JSON{field: "\"jobber\".\"processors\".\"processor_info\""},
	LastHeartbeat:   whereHelpertypes_JSON{field: "\"jobber\".\"processors\".\"last_heartbeat\""},
	ExpiryTimestamp: whereHelpertime_Time{field: "\"jobber\".\"processors\".\"expiry_timestamp\""},
	Status:          whereHelperstring{field: "\"jobber\".\"processors\".\"status\""},
}

// ProcessorRels is where relationship names are stored.
var ProcessorRels = struct {
}{}

// processorR is where relationships are stored.
type processorR struct {
}

// NewStruct creates a new relationship struct
func (*processorR) NewStruct() *processorR {
	return &processorR{}
}

// processorL is where Load methods for each relationship are stored.
type processorL struct{}

var (
	processorAllColumns            = []string{"cluster_id", "processor_id", "processor_info", "last_heartbeat", "expiry_timestamp", "status"}
	processorColumnsWithoutDefault = []string{"cluster_id", "processor_id", "processor_info", "last_heartbeat", "expiry_timestamp", "status"}
	processorColumnsWithDefault    = []string{}
	processorPrimaryKeyColumns     = []string{"cluster_id", "processor_id"}
	processorGeneratedColumns      = []string{}
)

type (
	// ProcessorSlice is an alias for a slice of pointers to Processor.
	// This should almost always be used instead of []Processor.
	ProcessorSlice []*Processor
	// ProcessorHook is the signature for custom Processor hook methods
	ProcessorHook func(context.Context, boil.ContextExecutor, *Processor) error

	processorQuery struct {
		*queries.Query
	}
)

// Cache for insert, update and upsert
var (
	processorType                 = reflect.TypeOf(&Processor{})
	processorMapping              = queries.MakeStructMapping(processorType)
	processorPrimaryKeyMapping, _ = queries.BindMapping(processorType, processorMapping, processorPrimaryKeyColumns)
	processorInsertCacheMut       sync.RWMutex
	processorInsertCache          = make(map[string]insertCache)
	processorUpdateCacheMut       sync.RWMutex
	processorUpdateCache          = make(map[string]updateCache)
	processorUpsertCacheMut       sync.RWMutex
	processorUpsertCache          = make(map[string]insertCache)
)

var (
	// Force time package dependency for automated UpdatedAt/CreatedAt.
	_ = time.Second
	// Force qmhelper dependency for where clause generation (which doesn't
	// always happen)
	_ = qmhelper.Where
)

var processorAfterSelectHooks []ProcessorHook

var processorBeforeInsertHooks []ProcessorHook
var processorAfterInsertHooks []ProcessorHook

var processorBeforeUpdateHooks []ProcessorHook
var processorAfterUpdateHooks []ProcessorHook

var processorBeforeDeleteHooks []ProcessorHook
var processorAfterDeleteHooks []ProcessorHook

var processorBeforeUpsertHooks []ProcessorHook
var processorAfterUpsertHooks []ProcessorHook

// doAfterSelectHooks executes all "after Select" hooks.
func (o *Processor) doAfterSelectHooks(ctx context.Context, exec boil.ContextExecutor) (err error) {
	if boil.HooksAreSkipped(ctx) {
		return nil
	}

	for _, hook := range processorAfterSelectHooks {
		if err := hook(ctx, exec, o); err != nil {
			return err
		}
	}

	return nil
}

// doBeforeInsertHooks executes all "before insert" hooks.
func (o *Processor) doBeforeInsertHooks(ctx context.Context, exec boil.ContextExecutor) (err error) {
	if boil.HooksAreSkipped(ctx) {
		return nil
	}

	for _, hook := range processorBeforeInsertHooks {
		if err := hook(ctx, exec, o); err != nil {
			return err
		}
	}

	return nil
}

// doAfterInsertHooks executes all "after Insert" hooks.
func (o *Processor) doAfterInsertHooks(ctx context.Context, exec boil.ContextExecutor) (err error) {
	if boil.HooksAreSkipped(ctx) {
		return nil
	}

	for _, hook := range processorAfterInsertHooks {
		if err := hook(ctx, exec, o); err != nil {
			return err
		}
	}

	return nil
}

// doBeforeUpdateHooks executes all "before Update" hooks.
func (o *Processor) doBeforeUpdateHooks(ctx context.Context, exec boil.ContextExecutor) (err error) {
	if boil.HooksAreSkipped(ctx) {
		return nil
	}

	for _, hook := range processorBeforeUpdateHooks {
		if err := hook(ctx, exec, o); err != nil {
			return err
		}
	}

	return nil
}

// doAfterUpdateHooks executes all "after Update" hooks.
func (o *Processor) doAfterUpdateHooks(ctx context.Context, exec boil.ContextExecutor) (err error) {
	if boil.HooksAreSkipped(ctx) {
		return nil
	}

	for _, hook := range processorAfterUpdateHooks {
		if err := hook(ctx, exec, o); err != nil {
			return err
		}
	}

	return nil
}

// doBeforeDeleteHooks executes all "before Delete" hooks.
func (o *Processor) doBeforeDeleteHooks(ctx context.Context, exec boil.ContextExecutor) (err error) {
	if boil.HooksAreSkipped(ctx) {
		return nil
	}

	for _, hook := range processorBeforeDeleteHooks {
		if err := hook(ctx, exec, o); err != nil {
			return err
		}
	}

	return nil
}

// doAfterDeleteHooks executes all "after Delete" hooks.
func (o *Processor) doAfterDeleteHooks(ctx context.Context, exec boil.ContextExecutor) (err error) {
	if boil.HooksAreSkipped(ctx) {
		return nil
	}

	for _, hook := range processorAfterDeleteHooks {
		if err := hook(ctx, exec, o); err != nil {
			return err
		}
	}

	return nil
}

// doBeforeUpsertHooks executes all "before Upsert" hooks.
func (o *Processor) doBeforeUpsertHooks(ctx context.Context, exec boil.ContextExecutor) (err error) {
	if boil.HooksAreSkipped(ctx) {
		return nil
	}

	for _, hook := range processorBeforeUpsertHooks {
		if err := hook(ctx, exec, o); err != nil {
			return err
		}
	}

	return nil
}

// doAfterUpsertHooks executes all "after Upsert" hooks.
func (o *Processor) doAfterUpsertHooks(ctx context.Context, exec boil.ContextExecutor) (err error) {
	if boil.HooksAreSkipped(ctx) {
		return nil
	}

	for _, hook := range processorAfterUpsertHooks {
		if err := hook(ctx, exec, o); err != nil {
			return err
		}
	}

	return nil
}

// AddProcessorHook registers your hook function for all future operations.
func AddProcessorHook(hookPoint boil.HookPoint, processorHook ProcessorHook) {
	switch hookPoint {
	case boil.AfterSelectHook:
		processorAfterSelectHooks = append(processorAfterSelectHooks, processorHook)
	case boil.BeforeInsertHook:
		processorBeforeInsertHooks = append(processorBeforeInsertHooks, processorHook)
	case boil.AfterInsertHook:
		processorAfterInsertHooks = append(processorAfterInsertHooks, processorHook)
	case boil.BeforeUpdateHook:
		processorBeforeUpdateHooks = append(processorBeforeUpdateHooks, processorHook)
	case boil.AfterUpdateHook:
		processorAfterUpdateHooks = append(processorAfterUpdateHooks, processorHook)
	case boil.BeforeDeleteHook:
		processorBeforeDeleteHooks = append(processorBeforeDeleteHooks, processorHook)
	case boil.AfterDeleteHook:
		processorAfterDeleteHooks = append(processorAfterDeleteHooks, processorHook)
	case boil.BeforeUpsertHook:
		processorBeforeUpsertHooks = append(processorBeforeUpsertHooks, processorHook)
	case boil.AfterUpsertHook:
		processorAfterUpsertHooks = append(processorAfterUpsertHooks, processorHook)
	}
}

// One returns a single processor record from the query.
func (q processorQuery) One(ctx context.Context, exec boil.ContextExecutor) (*Processor, error) {
	o := &Processor{}

	queries.SetLimit(q.Query, 1)

	err := q.Bind(ctx, exec, o)
	if err != nil {
		if errors.Is(err, sql.ErrNoRows) {
			return nil, sql.ErrNoRows
		}
		return nil, errors.Wrap(err, "jobbertables: failed to execute a one query for processors")
	}

	if err := o.doAfterSelectHooks(ctx, exec); err != nil {
		return o, err
	}

	return o, nil
}

// All returns all Processor records from the query.
func (q processorQuery) All(ctx context.Context, exec boil.ContextExecutor) (ProcessorSlice, error) {
	var o []*Processor

	err := q.Bind(ctx, exec, &o)
	if err != nil {
		return nil, errors.Wrap(err, "jobbertables: failed to assign all query results to Processor slice")
	}

	if len(processorAfterSelectHooks) != 0 {
		for _, obj := range o {
			if err := obj.doAfterSelectHooks(ctx, exec); err != nil {
				return o, err
			}
		}
	}

	return o, nil
}

// Count returns the count of all Processor records in the query.
func (q processorQuery) Count(ctx context.Context, exec boil.ContextExecutor) (int64, error) {
	var count int64

	queries.SetSelect(q.Query, nil)
	queries.SetCount(q.Query)

	err := q.Query.QueryRowContext(ctx, exec).Scan(&count)
	if err != nil {
		return 0, errors.Wrap(err, "jobbertables: failed to count processors rows")
	}

	return count, nil
}

// Exists checks if the row exists in the table.
func (q processorQuery) Exists(ctx context.Context, exec boil.ContextExecutor) (bool, error) {
	var count int64

	queries.SetSelect(q.Query, nil)
	queries.SetCount(q.Query)
	queries.SetLimit(q.Query, 1)

	err := q.Query.QueryRowContext(ctx, exec).Scan(&count)
	if err != nil {
		return false, errors.Wrap(err, "jobbertables: failed to check if processors exists")
	}

	return count > 0, nil
}

// Processors retrieves all the records using an executor.
func Processors(mods ...qm.QueryMod) processorQuery {
	mods = append(mods, qm.From("\"jobber\".\"processors\""))
	q := NewQuery(mods...)
	if len(queries.GetSelect(q)) == 0 {
		queries.SetSelect(q, []string{"\"jobber\".\"processors\".*"})
	}

	return processorQuery{q}
}

// FindProcessor retrieves a single record by ID with an executor.
// If selectCols is empty Find will return all columns.
func FindProcessor(ctx context.Context, exec boil.ContextExecutor, clusterID string, processorID string, selectCols ...string) (*Processor, error) {
	processorObj := &Processor{}

	sel := "*"
	if len(selectCols) > 0 {
		sel = strings.Join(strmangle.IdentQuoteSlice(dialect.LQ, dialect.RQ, selectCols), ",")
	}
	query := fmt.Sprintf(
		"select %s from \"jobber\".\"processors\" where \"cluster_id\"=$1 AND \"processor_id\"=$2", sel,
	)

	q := queries.Raw(query, clusterID, processorID)

	err := q.Bind(ctx, exec, processorObj)
	if err != nil {
		if errors.Is(err, sql.ErrNoRows) {
			return nil, sql.ErrNoRows
		}
		return nil, errors.Wrap(err, "jobbertables: unable to select from processors")
	}

	if err = processorObj.doAfterSelectHooks(ctx, exec); err != nil {
		return processorObj, err
	}

	return processorObj, nil
}

// Insert a single record using an executor.
// See boil.Columns.InsertColumnSet documentation to understand column list inference for inserts.
func (o *Processor) Insert(ctx context.Context, exec boil.ContextExecutor, columns boil.Columns) error {
	if o == nil {
		return errors.New("jobbertables: no processors provided for insertion")
	}

	var err error

	if err := o.doBeforeInsertHooks(ctx, exec); err != nil {
		return err
	}

	nzDefaults := queries.NonZeroDefaultSet(processorColumnsWithDefault, o)

	key := makeCacheKey(columns, nzDefaults)
	processorInsertCacheMut.RLock()
	cache, cached := processorInsertCache[key]
	processorInsertCacheMut.RUnlock()

	if !cached {
		wl, returnColumns := columns.InsertColumnSet(
			processorAllColumns,
			processorColumnsWithDefault,
			processorColumnsWithoutDefault,
			nzDefaults,
		)

		cache.valueMapping, err = queries.BindMapping(processorType, processorMapping, wl)
		if err != nil {
			return err
		}
		cache.retMapping, err = queries.BindMapping(processorType, processorMapping, returnColumns)
		if err != nil {
			return err
		}
		if len(wl) != 0 {
			cache.query = fmt.Sprintf("INSERT INTO \"jobber\".\"processors\" (\"%s\") %%sVALUES (%s)%%s", strings.Join(wl, "\",\""), strmangle.Placeholders(dialect.UseIndexPlaceholders, len(wl), 1, 1))
		} else {
			cache.query = "INSERT INTO \"jobber\".\"processors\" %sDEFAULT VALUES%s"
		}

		var queryOutput, queryReturning string

		if len(cache.retMapping) != 0 {
			queryReturning = fmt.Sprintf(" RETURNING \"%s\"", strings.Join(returnColumns, "\",\""))
		}

		cache.query = fmt.Sprintf(cache.query, queryOutput, queryReturning)
	}

	value := reflect.Indirect(reflect.ValueOf(o))
	vals := queries.ValuesFromMapping(value, cache.valueMapping)

	if boil.IsDebug(ctx) {
		writer := boil.DebugWriterFrom(ctx)
		fmt.Fprintln(writer, cache.query)
		fmt.Fprintln(writer, vals)
	}

	if len(cache.retMapping) != 0 {
		err = exec.QueryRowContext(ctx, cache.query, vals...).Scan(queries.PtrsFromMapping(value, cache.retMapping)...)
	} else {
		_, err = exec.ExecContext(ctx, cache.query, vals...)
	}

	if err != nil {
		return errors.Wrap(err, "jobbertables: unable to insert into processors")
	}

	if !cached {
		processorInsertCacheMut.Lock()
		processorInsertCache[key] = cache
		processorInsertCacheMut.Unlock()
	}

	return o.doAfterInsertHooks(ctx, exec)
}

// Update uses an executor to update the Processor.
// See boil.Columns.UpdateColumnSet documentation to understand column list inference for updates.
// Update does not automatically update the record in case of default values. Use .Reload() to refresh the records.
func (o *Processor) Update(ctx context.Context, exec boil.ContextExecutor, columns boil.Columns) (int64, error) {
	var err error
	if err = o.doBeforeUpdateHooks(ctx, exec); err != nil {
		return 0, err
	}
	key := makeCacheKey(columns, nil)
	processorUpdateCacheMut.RLock()
	cache, cached := processorUpdateCache[key]
	processorUpdateCacheMut.RUnlock()

	if !cached {
		wl := columns.UpdateColumnSet(
			processorAllColumns,
			processorPrimaryKeyColumns,
		)

		if !columns.IsWhitelist() {
			wl = strmangle.SetComplement(wl, []string{"created_at"})
		}
		if len(wl) == 0 {
			return 0, errors.New("jobbertables: unable to update processors, could not build whitelist")
		}

		cache.query = fmt.Sprintf("UPDATE \"jobber\".\"processors\" SET %s WHERE %s",
			strmangle.SetParamNames("\"", "\"", 1, wl),
			strmangle.WhereClause("\"", "\"", len(wl)+1, processorPrimaryKeyColumns),
		)
		cache.valueMapping, err = queries.BindMapping(processorType, processorMapping, append(wl, processorPrimaryKeyColumns...))
		if err != nil {
			return 0, err
		}
	}

	values := queries.ValuesFromMapping(reflect.Indirect(reflect.ValueOf(o)), cache.valueMapping)

	if boil.IsDebug(ctx) {
		writer := boil.DebugWriterFrom(ctx)
		fmt.Fprintln(writer, cache.query)
		fmt.Fprintln(writer, values)
	}
	var result sql.Result
	result, err = exec.ExecContext(ctx, cache.query, values...)
	if err != nil {
		return 0, errors.Wrap(err, "jobbertables: unable to update processors row")
	}

	rowsAff, err := result.RowsAffected()
	if err != nil {
		return 0, errors.Wrap(err, "jobbertables: failed to get rows affected by update for processors")
	}

	if !cached {
		processorUpdateCacheMut.Lock()
		processorUpdateCache[key] = cache
		processorUpdateCacheMut.Unlock()
	}

	return rowsAff, o.doAfterUpdateHooks(ctx, exec)
}

// UpdateAll updates all rows with the specified column values.
func (q processorQuery) UpdateAll(ctx context.Context, exec boil.ContextExecutor, cols M) (int64, error) {
	queries.SetUpdate(q.Query, cols)

	result, err := q.Query.ExecContext(ctx, exec)
	if err != nil {
		return 0, errors.Wrap(err, "jobbertables: unable to update all for processors")
	}

	rowsAff, err := result.RowsAffected()
	if err != nil {
		return 0, errors.Wrap(err, "jobbertables: unable to retrieve rows affected for processors")
	}

	return rowsAff, nil
}

// UpdateAll updates all rows with the specified column values, using an executor.
func (o ProcessorSlice) UpdateAll(ctx context.Context, exec boil.ContextExecutor, cols M) (int64, error) {
	ln := int64(len(o))
	if ln == 0 {
		return 0, nil
	}

	if len(cols) == 0 {
		return 0, errors.New("jobbertables: update all requires at least one column argument")
	}

	colNames := make([]string, len(cols))
	args := make([]interface{}, len(cols))

	i := 0
	for name, value := range cols {
		colNames[i] = name
		args[i] = value
		i++
	}

	// Append all of the primary key values for each column
	for _, obj := range o {
		pkeyArgs := queries.ValuesFromMapping(reflect.Indirect(reflect.ValueOf(obj)), processorPrimaryKeyMapping)
		args = append(args, pkeyArgs...)
	}

	sql := fmt.Sprintf("UPDATE \"jobber\".\"processors\" SET %s WHERE %s",
		strmangle.SetParamNames("\"", "\"", 1, colNames),
		strmangle.WhereClauseRepeated(string(dialect.LQ), string(dialect.RQ), len(colNames)+1, processorPrimaryKeyColumns, len(o)))

	if boil.IsDebug(ctx) {
		writer := boil.DebugWriterFrom(ctx)
		fmt.Fprintln(writer, sql)
		fmt.Fprintln(writer, args...)
	}
	result, err := exec.ExecContext(ctx, sql, args...)
	if err != nil {
		return 0, errors.Wrap(err, "jobbertables: unable to update all in processor slice")
	}

	rowsAff, err := result.RowsAffected()
	if err != nil {
		return 0, errors.Wrap(err, "jobbertables: unable to retrieve rows affected all in update all processor")
	}
	return rowsAff, nil
}

// Upsert attempts an insert using an executor, and does an update or ignore on conflict.
// See boil.Columns documentation for how to properly use updateColumns and insertColumns.
func (o *Processor) Upsert(ctx context.Context, exec boil.ContextExecutor, updateOnConflict bool, conflictColumns []string, updateColumns, insertColumns boil.Columns) error {
	if o == nil {
		return errors.New("jobbertables: no processors provided for upsert")
	}

	if err := o.doBeforeUpsertHooks(ctx, exec); err != nil {
		return err
	}

	nzDefaults := queries.NonZeroDefaultSet(processorColumnsWithDefault, o)

	// Build cache key in-line uglily - mysql vs psql problems
	buf := strmangle.GetBuffer()
	if updateOnConflict {
		buf.WriteByte('t')
	} else {
		buf.WriteByte('f')
	}
	buf.WriteByte('.')
	for _, c := range conflictColumns {
		buf.WriteString(c)
	}
	buf.WriteByte('.')
	buf.WriteString(strconv.Itoa(updateColumns.Kind))
	for _, c := range updateColumns.Cols {
		buf.WriteString(c)
	}
	buf.WriteByte('.')
	buf.WriteString(strconv.Itoa(insertColumns.Kind))
	for _, c := range insertColumns.Cols {
		buf.WriteString(c)
	}
	buf.WriteByte('.')
	for _, c := range nzDefaults {
		buf.WriteString(c)
	}
	key := buf.String()
	strmangle.PutBuffer(buf)

	processorUpsertCacheMut.RLock()
	cache, cached := processorUpsertCache[key]
	processorUpsertCacheMut.RUnlock()

	var err error

	if !cached {
		insert, ret := insertColumns.InsertColumnSet(
			processorAllColumns,
			processorColumnsWithDefault,
			processorColumnsWithoutDefault,
			nzDefaults,
		)

		update := updateColumns.UpdateColumnSet(
			processorAllColumns,
			processorPrimaryKeyColumns,
		)

		if updateOnConflict && len(update) == 0 {
			return errors.New("jobbertables: unable to upsert processors, could not build update column list")
		}

		conflict := conflictColumns
		if len(conflict) == 0 {
			conflict = make([]string, len(processorPrimaryKeyColumns))
			copy(conflict, processorPrimaryKeyColumns)
		}
		cache.query = buildUpsertQueryPostgres(dialect, "\"jobber\".\"processors\"", updateOnConflict, ret, update, conflict, insert)

		cache.valueMapping, err = queries.BindMapping(processorType, processorMapping, insert)
		if err != nil {
			return err
		}
		if len(ret) != 0 {
			cache.retMapping, err = queries.BindMapping(processorType, processorMapping, ret)
			if err != nil {
				return err
			}
		}
	}

	value := reflect.Indirect(reflect.ValueOf(o))
	vals := queries.ValuesFromMapping(value, cache.valueMapping)
	var returns []interface{}
	if len(cache.retMapping) != 0 {
		returns = queries.PtrsFromMapping(value, cache.retMapping)
	}

	if boil.IsDebug(ctx) {
		writer := boil.DebugWriterFrom(ctx)
		fmt.Fprintln(writer, cache.query)
		fmt.Fprintln(writer, vals)
	}
	if len(cache.retMapping) != 0 {
		err = exec.QueryRowContext(ctx, cache.query, vals...).Scan(returns...)
		if errors.Is(err, sql.ErrNoRows) {
			err = nil // Postgres doesn't return anything when there's no update
		}
	} else {
		_, err = exec.ExecContext(ctx, cache.query, vals...)
	}
	if err != nil {
		return errors.Wrap(err, "jobbertables: unable to upsert processors")
	}

	if !cached {
		processorUpsertCacheMut.Lock()
		processorUpsertCache[key] = cache
		processorUpsertCacheMut.Unlock()
	}

	return o.doAfterUpsertHooks(ctx, exec)
}

// Delete deletes a single Processor record with an executor.
// Delete will match against the primary key column to find the record to delete.
func (o *Processor) Delete(ctx context.Context, exec boil.ContextExecutor) (int64, error) {
	if o == nil {
		return 0, errors.New("jobbertables: no Processor provided for delete")
	}

	if err := o.doBeforeDeleteHooks(ctx, exec); err != nil {
		return 0, err
	}

	args := queries.ValuesFromMapping(reflect.Indirect(reflect.ValueOf(o)), processorPrimaryKeyMapping)
	sql := "DELETE FROM \"jobber\".\"processors\" WHERE \"cluster_id\"=$1 AND \"processor_id\"=$2"

	if boil.IsDebug(ctx) {
		writer := boil.DebugWriterFrom(ctx)
		fmt.Fprintln(writer, sql)
		fmt.Fprintln(writer, args...)
	}
	result, err := exec.ExecContext(ctx, sql, args...)
	if err != nil {
		return 0, errors.Wrap(err, "jobbertables: unable to delete from processors")
	}

	rowsAff, err := result.RowsAffected()
	if err != nil {
		return 0, errors.Wrap(err, "jobbertables: failed to get rows affected by delete for processors")
	}

	if err := o.doAfterDeleteHooks(ctx, exec); err != nil {
		return 0, err
	}

	return rowsAff, nil
}

// DeleteAll deletes all matching rows.
func (q processorQuery) DeleteAll(ctx context.Context, exec boil.ContextExecutor) (int64, error) {
	if q.Query == nil {
		return 0, errors.New("jobbertables: no processorQuery provided for delete all")
	}

	queries.SetDelete(q.Query)

	result, err := q.Query.ExecContext(ctx, exec)
	if err != nil {
		return 0, errors.Wrap(err, "jobbertables: unable to delete all from processors")
	}

	rowsAff, err := result.RowsAffected()
	if err != nil {
		return 0, errors.Wrap(err, "jobbertables: failed to get rows affected by deleteall for processors")
	}

	return rowsAff, nil
}

// DeleteAll deletes all rows in the slice, using an executor.
func (o ProcessorSlice) DeleteAll(ctx context.Context, exec boil.ContextExecutor) (int64, error) {
	if len(o) == 0 {
		return 0, nil
	}

	if len(processorBeforeDeleteHooks) != 0 {
		for _, obj := range o {
			if err := obj.doBeforeDeleteHooks(ctx, exec); err != nil {
				return 0, err
			}
		}
	}

	var args []interface{}
	for _, obj := range o {
		pkeyArgs := queries.ValuesFromMapping(reflect.Indirect(reflect.ValueOf(obj)), processorPrimaryKeyMapping)
		args = append(args, pkeyArgs...)
	}

	sql := "DELETE FROM \"jobber\".\"processors\" WHERE " +
		strmangle.WhereClauseRepeated(string(dialect.LQ), string(dialect.RQ), 1, processorPrimaryKeyColumns, len(o))

	if boil.IsDebug(ctx) {
		writer := boil.DebugWriterFrom(ctx)
		fmt.Fprintln(writer, sql)
		fmt.Fprintln(writer, args)
	}
	result, err := exec.ExecContext(ctx, sql, args...)
	if err != nil {
		return 0, errors.Wrap(err, "jobbertables: unable to delete all from processor slice")
	}

	rowsAff, err := result.RowsAffected()
	if err != nil {
		return 0, errors.Wrap(err, "jobbertables: failed to get rows affected by deleteall for processors")
	}

	if len(processorAfterDeleteHooks) != 0 {
		for _, obj := range o {
			if err := obj.doAfterDeleteHooks(ctx, exec); err != nil {
				return 0, err
			}
		}
	}

	return rowsAff, nil
}

// Reload refetches the object from the database
// using the primary keys with an executor.
func (o *Processor) Reload(ctx context.Context, exec boil.ContextExecutor) error {
	ret, err := FindProcessor(ctx, exec, o.ClusterID, o.ProcessorID)
	if err != nil {
		return err
	}

	*o = *ret
	return nil
}

// ReloadAll refetches every row with matching primary key column values
// and overwrites the original object slice with the newly updated slice.
func (o *ProcessorSlice) ReloadAll(ctx context.Context, exec boil.ContextExecutor) error {
	if o == nil || len(*o) == 0 {
		return nil
	}

	slice := ProcessorSlice{}
	var args []interface{}
	for _, obj := range *o {
		pkeyArgs := queries.ValuesFromMapping(reflect.Indirect(reflect.ValueOf(obj)), processorPrimaryKeyMapping)
		args = append(args, pkeyArgs...)
	}

	sql := "SELECT \"jobber\".\"processors\".* FROM \"jobber\".\"processors\" WHERE " +
		strmangle.WhereClauseRepeated(string(dialect.LQ), string(dialect.RQ), 1, processorPrimaryKeyColumns, len(*o))

	q := queries.Raw(sql, args...)

	err := q.Bind(ctx, exec, &slice)
	if err != nil {
		return errors.Wrap(err, "jobbertables: unable to reload all in ProcessorSlice")
	}

	*o = slice

	return nil
}

// ProcessorExists checks if the Processor row exists.
func ProcessorExists(ctx context.Context, exec boil.ContextExecutor, clusterID string, processorID string) (bool, error) {
	var exists bool
	sql := "select exists(select 1 from \"jobber\".\"processors\" where \"cluster_id\"=$1 AND \"processor_id\"=$2 limit 1)"

	if boil.IsDebug(ctx) {
		writer := boil.DebugWriterFrom(ctx)
		fmt.Fprintln(writer, sql)
		fmt.Fprintln(writer, clusterID, processorID)
	}
	row := exec.QueryRowContext(ctx, sql, clusterID, processorID)

	err := row.Scan(&exists)
	if err != nil {
		return false, errors.Wrap(err, "jobbertables: unable to check if processors exists")
	}

	return exists, nil
}
