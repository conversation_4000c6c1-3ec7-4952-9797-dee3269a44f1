// Code generated by SQLBoiler (https://github.com/volatiletech/sqlboiler). DO NOT EDIT.
// This file is meant to be re-generated in place and/or deleted at any time.

package account

import (
	"context"
	"database/sql"
	"fmt"
	"reflect"
	"strconv"
	"strings"
	"sync"
	"time"

	"github.com/friendsofgo/errors"
	"github.com/volatiletech/null/v8"
	"github.com/volatiletech/sqlboiler/v4/boil"
	"github.com/volatiletech/sqlboiler/v4/queries"
	"github.com/volatiletech/sqlboiler/v4/queries/qm"
	"github.com/volatiletech/sqlboiler/v4/queries/qmhelper"
	"github.com/volatiletech/sqlboiler/v4/types"
	"github.com/volatiletech/strmangle"
)

// AccountMergeHistory is an object representing the database table.
type AccountMergeHistory struct {
	MergeID         string      `boil:"merge_id" json:"merge_id" toml:"merge_id" yaml:"merge_id"`
	SourceAccountID string      `boil:"source_account_id" json:"source_account_id" toml:"source_account_id" yaml:"source_account_id"`
	TargetAccountID string      `boil:"target_account_id" json:"target_account_id" toml:"target_account_id" yaml:"target_account_id"`
	MergedBy        null.String `boil:"merged_by" json:"merged_by,omitempty" toml:"merged_by" yaml:"merged_by,omitempty"`
	MergedAt        time.Time   `boil:"merged_at" json:"merged_at" toml:"merged_at" yaml:"merged_at"`
	Metadata        types.JSON  `boil:"metadata" json:"metadata" toml:"metadata" yaml:"metadata"`

	R *accountMergeHistoryR `boil:"" json:"" toml:"" yaml:""`
	L accountMergeHistoryL  `boil:"-" json:"-" toml:"-" yaml:"-"`
}

var AccountMergeHistoryColumns = struct {
	MergeID         string
	SourceAccountID string
	TargetAccountID string
	MergedBy        string
	MergedAt        string
	Metadata        string
}{
	MergeID:         "merge_id",
	SourceAccountID: "source_account_id",
	TargetAccountID: "target_account_id",
	MergedBy:        "merged_by",
	MergedAt:        "merged_at",
	Metadata:        "metadata",
}

var AccountMergeHistoryTableColumns = struct {
	MergeID         string
	SourceAccountID string
	TargetAccountID string
	MergedBy        string
	MergedAt        string
	Metadata        string
}{
	MergeID:         "account_merge_history.merge_id",
	SourceAccountID: "account_merge_history.source_account_id",
	TargetAccountID: "account_merge_history.target_account_id",
	MergedBy:        "account_merge_history.merged_by",
	MergedAt:        "account_merge_history.merged_at",
	Metadata:        "account_merge_history.metadata",
}

// Generated where

type whereHelpernull_String struct{ field string }

func (w whereHelpernull_String) EQ(x null.String) qm.QueryMod {
	return qmhelper.WhereNullEQ(w.field, false, x)
}
func (w whereHelpernull_String) NEQ(x null.String) qm.QueryMod {
	return qmhelper.WhereNullEQ(w.field, true, x)
}
func (w whereHelpernull_String) LT(x null.String) qm.QueryMod {
	return qmhelper.Where(w.field, qmhelper.LT, x)
}
func (w whereHelpernull_String) LTE(x null.String) qm.QueryMod {
	return qmhelper.Where(w.field, qmhelper.LTE, x)
}
func (w whereHelpernull_String) GT(x null.String) qm.QueryMod {
	return qmhelper.Where(w.field, qmhelper.GT, x)
}
func (w whereHelpernull_String) GTE(x null.String) qm.QueryMod {
	return qmhelper.Where(w.field, qmhelper.GTE, x)
}

func (w whereHelpernull_String) IsNull() qm.QueryMod    { return qmhelper.WhereIsNull(w.field) }
func (w whereHelpernull_String) IsNotNull() qm.QueryMod { return qmhelper.WhereIsNotNull(w.field) }

type whereHelpertypes_JSON struct{ field string }

func (w whereHelpertypes_JSON) EQ(x types.JSON) qm.QueryMod {
	return qmhelper.Where(w.field, qmhelper.EQ, x)
}
func (w whereHelpertypes_JSON) NEQ(x types.JSON) qm.QueryMod {
	return qmhelper.Where(w.field, qmhelper.NEQ, x)
}
func (w whereHelpertypes_JSON) LT(x types.JSON) qm.QueryMod {
	return qmhelper.Where(w.field, qmhelper.LT, x)
}
func (w whereHelpertypes_JSON) LTE(x types.JSON) qm.QueryMod {
	return qmhelper.Where(w.field, qmhelper.LTE, x)
}
func (w whereHelpertypes_JSON) GT(x types.JSON) qm.QueryMod {
	return qmhelper.Where(w.field, qmhelper.GT, x)
}
func (w whereHelpertypes_JSON) GTE(x types.JSON) qm.QueryMod {
	return qmhelper.Where(w.field, qmhelper.GTE, x)
}

var AccountMergeHistoryWhere = struct {
	MergeID         whereHelperstring
	SourceAccountID whereHelperstring
	TargetAccountID whereHelperstring
	MergedBy        whereHelpernull_String
	MergedAt        whereHelpertime_Time
	Metadata        whereHelpertypes_JSON
}{
	MergeID:         whereHelperstring{field: "\"account\".\"account_merge_history\".\"merge_id\""},
	SourceAccountID: whereHelperstring{field: "\"account\".\"account_merge_history\".\"source_account_id\""},
	TargetAccountID: whereHelperstring{field: "\"account\".\"account_merge_history\".\"target_account_id\""},
	MergedBy:        whereHelpernull_String{field: "\"account\".\"account_merge_history\".\"merged_by\""},
	MergedAt:        whereHelpertime_Time{field: "\"account\".\"account_merge_history\".\"merged_at\""},
	Metadata:        whereHelpertypes_JSON{field: "\"account\".\"account_merge_history\".\"metadata\""},
}

// AccountMergeHistoryRels is where relationship names are stored.
var AccountMergeHistoryRels = struct {
	SourceAccount string
	TargetAccount string
}{
	SourceAccount: "SourceAccount",
	TargetAccount: "TargetAccount",
}

// accountMergeHistoryR is where relationships are stored.
type accountMergeHistoryR struct {
	SourceAccount *Account `boil:"SourceAccount" json:"SourceAccount" toml:"SourceAccount" yaml:"SourceAccount"`
	TargetAccount *Account `boil:"TargetAccount" json:"TargetAccount" toml:"TargetAccount" yaml:"TargetAccount"`
}

// NewStruct creates a new relationship struct
func (*accountMergeHistoryR) NewStruct() *accountMergeHistoryR {
	return &accountMergeHistoryR{}
}

// accountMergeHistoryL is where Load methods for each relationship are stored.
type accountMergeHistoryL struct{}

var (
	accountMergeHistoryAllColumns            = []string{"merge_id", "source_account_id", "target_account_id", "merged_by", "merged_at", "metadata"}
	accountMergeHistoryColumnsWithoutDefault = []string{"merge_id", "source_account_id", "target_account_id"}
	accountMergeHistoryColumnsWithDefault    = []string{"merged_by", "merged_at", "metadata"}
	accountMergeHistoryPrimaryKeyColumns     = []string{"merge_id"}
	accountMergeHistoryGeneratedColumns      = []string{}
)

type (
	// AccountMergeHistorySlice is an alias for a slice of pointers to AccountMergeHistory.
	// This should almost always be used instead of []AccountMergeHistory.
	AccountMergeHistorySlice []*AccountMergeHistory
	// AccountMergeHistoryHook is the signature for custom AccountMergeHistory hook methods
	AccountMergeHistoryHook func(context.Context, boil.ContextExecutor, *AccountMergeHistory) error

	accountMergeHistoryQuery struct {
		*queries.Query
	}
)

// Cache for insert, update and upsert
var (
	accountMergeHistoryType                 = reflect.TypeOf(&AccountMergeHistory{})
	accountMergeHistoryMapping              = queries.MakeStructMapping(accountMergeHistoryType)
	accountMergeHistoryPrimaryKeyMapping, _ = queries.BindMapping(accountMergeHistoryType, accountMergeHistoryMapping, accountMergeHistoryPrimaryKeyColumns)
	accountMergeHistoryInsertCacheMut       sync.RWMutex
	accountMergeHistoryInsertCache          = make(map[string]insertCache)
	accountMergeHistoryUpdateCacheMut       sync.RWMutex
	accountMergeHistoryUpdateCache          = make(map[string]updateCache)
	accountMergeHistoryUpsertCacheMut       sync.RWMutex
	accountMergeHistoryUpsertCache          = make(map[string]insertCache)
)

var (
	// Force time package dependency for automated UpdatedAt/CreatedAt.
	_ = time.Second
	// Force qmhelper dependency for where clause generation (which doesn't
	// always happen)
	_ = qmhelper.Where
)

var accountMergeHistoryAfterSelectHooks []AccountMergeHistoryHook

var accountMergeHistoryBeforeInsertHooks []AccountMergeHistoryHook
var accountMergeHistoryAfterInsertHooks []AccountMergeHistoryHook

var accountMergeHistoryBeforeUpdateHooks []AccountMergeHistoryHook
var accountMergeHistoryAfterUpdateHooks []AccountMergeHistoryHook

var accountMergeHistoryBeforeDeleteHooks []AccountMergeHistoryHook
var accountMergeHistoryAfterDeleteHooks []AccountMergeHistoryHook

var accountMergeHistoryBeforeUpsertHooks []AccountMergeHistoryHook
var accountMergeHistoryAfterUpsertHooks []AccountMergeHistoryHook

// doAfterSelectHooks executes all "after Select" hooks.
func (o *AccountMergeHistory) doAfterSelectHooks(ctx context.Context, exec boil.ContextExecutor) (err error) {
	if boil.HooksAreSkipped(ctx) {
		return nil
	}

	for _, hook := range accountMergeHistoryAfterSelectHooks {
		if err := hook(ctx, exec, o); err != nil {
			return err
		}
	}

	return nil
}

// doBeforeInsertHooks executes all "before insert" hooks.
func (o *AccountMergeHistory) doBeforeInsertHooks(ctx context.Context, exec boil.ContextExecutor) (err error) {
	if boil.HooksAreSkipped(ctx) {
		return nil
	}

	for _, hook := range accountMergeHistoryBeforeInsertHooks {
		if err := hook(ctx, exec, o); err != nil {
			return err
		}
	}

	return nil
}

// doAfterInsertHooks executes all "after Insert" hooks.
func (o *AccountMergeHistory) doAfterInsertHooks(ctx context.Context, exec boil.ContextExecutor) (err error) {
	if boil.HooksAreSkipped(ctx) {
		return nil
	}

	for _, hook := range accountMergeHistoryAfterInsertHooks {
		if err := hook(ctx, exec, o); err != nil {
			return err
		}
	}

	return nil
}

// doBeforeUpdateHooks executes all "before Update" hooks.
func (o *AccountMergeHistory) doBeforeUpdateHooks(ctx context.Context, exec boil.ContextExecutor) (err error) {
	if boil.HooksAreSkipped(ctx) {
		return nil
	}

	for _, hook := range accountMergeHistoryBeforeUpdateHooks {
		if err := hook(ctx, exec, o); err != nil {
			return err
		}
	}

	return nil
}

// doAfterUpdateHooks executes all "after Update" hooks.
func (o *AccountMergeHistory) doAfterUpdateHooks(ctx context.Context, exec boil.ContextExecutor) (err error) {
	if boil.HooksAreSkipped(ctx) {
		return nil
	}

	for _, hook := range accountMergeHistoryAfterUpdateHooks {
		if err := hook(ctx, exec, o); err != nil {
			return err
		}
	}

	return nil
}

// doBeforeDeleteHooks executes all "before Delete" hooks.
func (o *AccountMergeHistory) doBeforeDeleteHooks(ctx context.Context, exec boil.ContextExecutor) (err error) {
	if boil.HooksAreSkipped(ctx) {
		return nil
	}

	for _, hook := range accountMergeHistoryBeforeDeleteHooks {
		if err := hook(ctx, exec, o); err != nil {
			return err
		}
	}

	return nil
}

// doAfterDeleteHooks executes all "after Delete" hooks.
func (o *AccountMergeHistory) doAfterDeleteHooks(ctx context.Context, exec boil.ContextExecutor) (err error) {
	if boil.HooksAreSkipped(ctx) {
		return nil
	}

	for _, hook := range accountMergeHistoryAfterDeleteHooks {
		if err := hook(ctx, exec, o); err != nil {
			return err
		}
	}

	return nil
}

// doBeforeUpsertHooks executes all "before Upsert" hooks.
func (o *AccountMergeHistory) doBeforeUpsertHooks(ctx context.Context, exec boil.ContextExecutor) (err error) {
	if boil.HooksAreSkipped(ctx) {
		return nil
	}

	for _, hook := range accountMergeHistoryBeforeUpsertHooks {
		if err := hook(ctx, exec, o); err != nil {
			return err
		}
	}

	return nil
}

// doAfterUpsertHooks executes all "after Upsert" hooks.
func (o *AccountMergeHistory) doAfterUpsertHooks(ctx context.Context, exec boil.ContextExecutor) (err error) {
	if boil.HooksAreSkipped(ctx) {
		return nil
	}

	for _, hook := range accountMergeHistoryAfterUpsertHooks {
		if err := hook(ctx, exec, o); err != nil {
			return err
		}
	}

	return nil
}

// AddAccountMergeHistoryHook registers your hook function for all future operations.
func AddAccountMergeHistoryHook(hookPoint boil.HookPoint, accountMergeHistoryHook AccountMergeHistoryHook) {
	switch hookPoint {
	case boil.AfterSelectHook:
		accountMergeHistoryAfterSelectHooks = append(accountMergeHistoryAfterSelectHooks, accountMergeHistoryHook)
	case boil.BeforeInsertHook:
		accountMergeHistoryBeforeInsertHooks = append(accountMergeHistoryBeforeInsertHooks, accountMergeHistoryHook)
	case boil.AfterInsertHook:
		accountMergeHistoryAfterInsertHooks = append(accountMergeHistoryAfterInsertHooks, accountMergeHistoryHook)
	case boil.BeforeUpdateHook:
		accountMergeHistoryBeforeUpdateHooks = append(accountMergeHistoryBeforeUpdateHooks, accountMergeHistoryHook)
	case boil.AfterUpdateHook:
		accountMergeHistoryAfterUpdateHooks = append(accountMergeHistoryAfterUpdateHooks, accountMergeHistoryHook)
	case boil.BeforeDeleteHook:
		accountMergeHistoryBeforeDeleteHooks = append(accountMergeHistoryBeforeDeleteHooks, accountMergeHistoryHook)
	case boil.AfterDeleteHook:
		accountMergeHistoryAfterDeleteHooks = append(accountMergeHistoryAfterDeleteHooks, accountMergeHistoryHook)
	case boil.BeforeUpsertHook:
		accountMergeHistoryBeforeUpsertHooks = append(accountMergeHistoryBeforeUpsertHooks, accountMergeHistoryHook)
	case boil.AfterUpsertHook:
		accountMergeHistoryAfterUpsertHooks = append(accountMergeHistoryAfterUpsertHooks, accountMergeHistoryHook)
	}
}

// One returns a single accountMergeHistory record from the query.
func (q accountMergeHistoryQuery) One(ctx context.Context, exec boil.ContextExecutor) (*AccountMergeHistory, error) {
	o := &AccountMergeHistory{}

	queries.SetLimit(q.Query, 1)

	err := q.Bind(ctx, exec, o)
	if err != nil {
		if errors.Is(err, sql.ErrNoRows) {
			return nil, sql.ErrNoRows
		}
		return nil, errors.Wrap(err, "account: failed to execute a one query for account_merge_history")
	}

	if err := o.doAfterSelectHooks(ctx, exec); err != nil {
		return o, err
	}

	return o, nil
}

// All returns all AccountMergeHistory records from the query.
func (q accountMergeHistoryQuery) All(ctx context.Context, exec boil.ContextExecutor) (AccountMergeHistorySlice, error) {
	var o []*AccountMergeHistory

	err := q.Bind(ctx, exec, &o)
	if err != nil {
		return nil, errors.Wrap(err, "account: failed to assign all query results to AccountMergeHistory slice")
	}

	if len(accountMergeHistoryAfterSelectHooks) != 0 {
		for _, obj := range o {
			if err := obj.doAfterSelectHooks(ctx, exec); err != nil {
				return o, err
			}
		}
	}

	return o, nil
}

// Count returns the count of all AccountMergeHistory records in the query.
func (q accountMergeHistoryQuery) Count(ctx context.Context, exec boil.ContextExecutor) (int64, error) {
	var count int64

	queries.SetSelect(q.Query, nil)
	queries.SetCount(q.Query)

	err := q.Query.QueryRowContext(ctx, exec).Scan(&count)
	if err != nil {
		return 0, errors.Wrap(err, "account: failed to count account_merge_history rows")
	}

	return count, nil
}

// Exists checks if the row exists in the table.
func (q accountMergeHistoryQuery) Exists(ctx context.Context, exec boil.ContextExecutor) (bool, error) {
	var count int64

	queries.SetSelect(q.Query, nil)
	queries.SetCount(q.Query)
	queries.SetLimit(q.Query, 1)

	err := q.Query.QueryRowContext(ctx, exec).Scan(&count)
	if err != nil {
		return false, errors.Wrap(err, "account: failed to check if account_merge_history exists")
	}

	return count > 0, nil
}

// SourceAccount pointed to by the foreign key.
func (o *AccountMergeHistory) SourceAccount(mods ...qm.QueryMod) accountQuery {
	queryMods := []qm.QueryMod{
		qm.Where("\"account_id\" = ?", o.SourceAccountID),
	}

	queryMods = append(queryMods, mods...)

	return Accounts(queryMods...)
}

// TargetAccount pointed to by the foreign key.
func (o *AccountMergeHistory) TargetAccount(mods ...qm.QueryMod) accountQuery {
	queryMods := []qm.QueryMod{
		qm.Where("\"account_id\" = ?", o.TargetAccountID),
	}

	queryMods = append(queryMods, mods...)

	return Accounts(queryMods...)
}

// LoadSourceAccount allows an eager lookup of values, cached into the
// loaded structs of the objects. This is for an N-1 relationship.
func (accountMergeHistoryL) LoadSourceAccount(ctx context.Context, e boil.ContextExecutor, singular bool, maybeAccountMergeHistory interface{}, mods queries.Applicator) error {
	var slice []*AccountMergeHistory
	var object *AccountMergeHistory

	if singular {
		object = maybeAccountMergeHistory.(*AccountMergeHistory)
	} else {
		slice = *maybeAccountMergeHistory.(*[]*AccountMergeHistory)
	}

	args := make([]interface{}, 0, 1)
	if singular {
		if object.R == nil {
			object.R = &accountMergeHistoryR{}
		}
		args = append(args, object.SourceAccountID)

	} else {
	Outer:
		for _, obj := range slice {
			if obj.R == nil {
				obj.R = &accountMergeHistoryR{}
			}

			for _, a := range args {
				if a == obj.SourceAccountID {
					continue Outer
				}
			}

			args = append(args, obj.SourceAccountID)

		}
	}

	if len(args) == 0 {
		return nil
	}

	query := NewQuery(
		qm.From(`account.accounts`),
		qm.WhereIn(`account.accounts.account_id in ?`, args...),
	)
	if mods != nil {
		mods.Apply(query)
	}

	results, err := query.QueryContext(ctx, e)
	if err != nil {
		return errors.Wrap(err, "failed to eager load Account")
	}

	var resultSlice []*Account
	if err = queries.Bind(results, &resultSlice); err != nil {
		return errors.Wrap(err, "failed to bind eager loaded slice Account")
	}

	if err = results.Close(); err != nil {
		return errors.Wrap(err, "failed to close results of eager load for accounts")
	}
	if err = results.Err(); err != nil {
		return errors.Wrap(err, "error occurred during iteration of eager loaded relations for accounts")
	}

	if len(accountMergeHistoryAfterSelectHooks) != 0 {
		for _, obj := range resultSlice {
			if err := obj.doAfterSelectHooks(ctx, e); err != nil {
				return err
			}
		}
	}

	if len(resultSlice) == 0 {
		return nil
	}

	if singular {
		foreign := resultSlice[0]
		object.R.SourceAccount = foreign
		if foreign.R == nil {
			foreign.R = &accountR{}
		}
		foreign.R.SourceAccountAccountMergeHistories = append(foreign.R.SourceAccountAccountMergeHistories, object)
		return nil
	}

	for _, local := range slice {
		for _, foreign := range resultSlice {
			if local.SourceAccountID == foreign.AccountID {
				local.R.SourceAccount = foreign
				if foreign.R == nil {
					foreign.R = &accountR{}
				}
				foreign.R.SourceAccountAccountMergeHistories = append(foreign.R.SourceAccountAccountMergeHistories, local)
				break
			}
		}
	}

	return nil
}

// LoadTargetAccount allows an eager lookup of values, cached into the
// loaded structs of the objects. This is for an N-1 relationship.
func (accountMergeHistoryL) LoadTargetAccount(ctx context.Context, e boil.ContextExecutor, singular bool, maybeAccountMergeHistory interface{}, mods queries.Applicator) error {
	var slice []*AccountMergeHistory
	var object *AccountMergeHistory

	if singular {
		object = maybeAccountMergeHistory.(*AccountMergeHistory)
	} else {
		slice = *maybeAccountMergeHistory.(*[]*AccountMergeHistory)
	}

	args := make([]interface{}, 0, 1)
	if singular {
		if object.R == nil {
			object.R = &accountMergeHistoryR{}
		}
		args = append(args, object.TargetAccountID)

	} else {
	Outer:
		for _, obj := range slice {
			if obj.R == nil {
				obj.R = &accountMergeHistoryR{}
			}

			for _, a := range args {
				if a == obj.TargetAccountID {
					continue Outer
				}
			}

			args = append(args, obj.TargetAccountID)

		}
	}

	if len(args) == 0 {
		return nil
	}

	query := NewQuery(
		qm.From(`account.accounts`),
		qm.WhereIn(`account.accounts.account_id in ?`, args...),
	)
	if mods != nil {
		mods.Apply(query)
	}

	results, err := query.QueryContext(ctx, e)
	if err != nil {
		return errors.Wrap(err, "failed to eager load Account")
	}

	var resultSlice []*Account
	if err = queries.Bind(results, &resultSlice); err != nil {
		return errors.Wrap(err, "failed to bind eager loaded slice Account")
	}

	if err = results.Close(); err != nil {
		return errors.Wrap(err, "failed to close results of eager load for accounts")
	}
	if err = results.Err(); err != nil {
		return errors.Wrap(err, "error occurred during iteration of eager loaded relations for accounts")
	}

	if len(accountMergeHistoryAfterSelectHooks) != 0 {
		for _, obj := range resultSlice {
			if err := obj.doAfterSelectHooks(ctx, e); err != nil {
				return err
			}
		}
	}

	if len(resultSlice) == 0 {
		return nil
	}

	if singular {
		foreign := resultSlice[0]
		object.R.TargetAccount = foreign
		if foreign.R == nil {
			foreign.R = &accountR{}
		}
		foreign.R.TargetAccountAccountMergeHistories = append(foreign.R.TargetAccountAccountMergeHistories, object)
		return nil
	}

	for _, local := range slice {
		for _, foreign := range resultSlice {
			if local.TargetAccountID == foreign.AccountID {
				local.R.TargetAccount = foreign
				if foreign.R == nil {
					foreign.R = &accountR{}
				}
				foreign.R.TargetAccountAccountMergeHistories = append(foreign.R.TargetAccountAccountMergeHistories, local)
				break
			}
		}
	}

	return nil
}

// SetSourceAccount of the accountMergeHistory to the related item.
// Sets o.R.SourceAccount to related.
// Adds o to related.R.SourceAccountAccountMergeHistories.
func (o *AccountMergeHistory) SetSourceAccount(ctx context.Context, exec boil.ContextExecutor, insert bool, related *Account) error {
	var err error
	if insert {
		if err = related.Insert(ctx, exec, boil.Infer()); err != nil {
			return errors.Wrap(err, "failed to insert into foreign table")
		}
	}

	updateQuery := fmt.Sprintf(
		"UPDATE \"account\".\"account_merge_history\" SET %s WHERE %s",
		strmangle.SetParamNames("\"", "\"", 1, []string{"source_account_id"}),
		strmangle.WhereClause("\"", "\"", 2, accountMergeHistoryPrimaryKeyColumns),
	)
	values := []interface{}{related.AccountID, o.MergeID}

	if boil.IsDebug(ctx) {
		writer := boil.DebugWriterFrom(ctx)
		fmt.Fprintln(writer, updateQuery)
		fmt.Fprintln(writer, values)
	}
	if _, err = exec.ExecContext(ctx, updateQuery, values...); err != nil {
		return errors.Wrap(err, "failed to update local table")
	}

	o.SourceAccountID = related.AccountID
	if o.R == nil {
		o.R = &accountMergeHistoryR{
			SourceAccount: related,
		}
	} else {
		o.R.SourceAccount = related
	}

	if related.R == nil {
		related.R = &accountR{
			SourceAccountAccountMergeHistories: AccountMergeHistorySlice{o},
		}
	} else {
		related.R.SourceAccountAccountMergeHistories = append(related.R.SourceAccountAccountMergeHistories, o)
	}

	return nil
}

// SetTargetAccount of the accountMergeHistory to the related item.
// Sets o.R.TargetAccount to related.
// Adds o to related.R.TargetAccountAccountMergeHistories.
func (o *AccountMergeHistory) SetTargetAccount(ctx context.Context, exec boil.ContextExecutor, insert bool, related *Account) error {
	var err error
	if insert {
		if err = related.Insert(ctx, exec, boil.Infer()); err != nil {
			return errors.Wrap(err, "failed to insert into foreign table")
		}
	}

	updateQuery := fmt.Sprintf(
		"UPDATE \"account\".\"account_merge_history\" SET %s WHERE %s",
		strmangle.SetParamNames("\"", "\"", 1, []string{"target_account_id"}),
		strmangle.WhereClause("\"", "\"", 2, accountMergeHistoryPrimaryKeyColumns),
	)
	values := []interface{}{related.AccountID, o.MergeID}

	if boil.IsDebug(ctx) {
		writer := boil.DebugWriterFrom(ctx)
		fmt.Fprintln(writer, updateQuery)
		fmt.Fprintln(writer, values)
	}
	if _, err = exec.ExecContext(ctx, updateQuery, values...); err != nil {
		return errors.Wrap(err, "failed to update local table")
	}

	o.TargetAccountID = related.AccountID
	if o.R == nil {
		o.R = &accountMergeHistoryR{
			TargetAccount: related,
		}
	} else {
		o.R.TargetAccount = related
	}

	if related.R == nil {
		related.R = &accountR{
			TargetAccountAccountMergeHistories: AccountMergeHistorySlice{o},
		}
	} else {
		related.R.TargetAccountAccountMergeHistories = append(related.R.TargetAccountAccountMergeHistories, o)
	}

	return nil
}

// AccountMergeHistories retrieves all the records using an executor.
func AccountMergeHistories(mods ...qm.QueryMod) accountMergeHistoryQuery {
	mods = append(mods, qm.From("\"account\".\"account_merge_history\""))
	q := NewQuery(mods...)
	if len(queries.GetSelect(q)) == 0 {
		queries.SetSelect(q, []string{"\"account\".\"account_merge_history\".*"})
	}

	return accountMergeHistoryQuery{q}
}

// FindAccountMergeHistory retrieves a single record by ID with an executor.
// If selectCols is empty Find will return all columns.
func FindAccountMergeHistory(ctx context.Context, exec boil.ContextExecutor, mergeID string, selectCols ...string) (*AccountMergeHistory, error) {
	accountMergeHistoryObj := &AccountMergeHistory{}

	sel := "*"
	if len(selectCols) > 0 {
		sel = strings.Join(strmangle.IdentQuoteSlice(dialect.LQ, dialect.RQ, selectCols), ",")
	}
	query := fmt.Sprintf(
		"select %s from \"account\".\"account_merge_history\" where \"merge_id\"=$1", sel,
	)

	q := queries.Raw(query, mergeID)

	err := q.Bind(ctx, exec, accountMergeHistoryObj)
	if err != nil {
		if errors.Is(err, sql.ErrNoRows) {
			return nil, sql.ErrNoRows
		}
		return nil, errors.Wrap(err, "account: unable to select from account_merge_history")
	}

	if err = accountMergeHistoryObj.doAfterSelectHooks(ctx, exec); err != nil {
		return accountMergeHistoryObj, err
	}

	return accountMergeHistoryObj, nil
}

// Insert a single record using an executor.
// See boil.Columns.InsertColumnSet documentation to understand column list inference for inserts.
func (o *AccountMergeHistory) Insert(ctx context.Context, exec boil.ContextExecutor, columns boil.Columns) error {
	if o == nil {
		return errors.New("account: no account_merge_history provided for insertion")
	}

	var err error

	if err := o.doBeforeInsertHooks(ctx, exec); err != nil {
		return err
	}

	nzDefaults := queries.NonZeroDefaultSet(accountMergeHistoryColumnsWithDefault, o)

	key := makeCacheKey(columns, nzDefaults)
	accountMergeHistoryInsertCacheMut.RLock()
	cache, cached := accountMergeHistoryInsertCache[key]
	accountMergeHistoryInsertCacheMut.RUnlock()

	if !cached {
		wl, returnColumns := columns.InsertColumnSet(
			accountMergeHistoryAllColumns,
			accountMergeHistoryColumnsWithDefault,
			accountMergeHistoryColumnsWithoutDefault,
			nzDefaults,
		)

		cache.valueMapping, err = queries.BindMapping(accountMergeHistoryType, accountMergeHistoryMapping, wl)
		if err != nil {
			return err
		}
		cache.retMapping, err = queries.BindMapping(accountMergeHistoryType, accountMergeHistoryMapping, returnColumns)
		if err != nil {
			return err
		}
		if len(wl) != 0 {
			cache.query = fmt.Sprintf("INSERT INTO \"account\".\"account_merge_history\" (\"%s\") %%sVALUES (%s)%%s", strings.Join(wl, "\",\""), strmangle.Placeholders(dialect.UseIndexPlaceholders, len(wl), 1, 1))
		} else {
			cache.query = "INSERT INTO \"account\".\"account_merge_history\" %sDEFAULT VALUES%s"
		}

		var queryOutput, queryReturning string

		if len(cache.retMapping) != 0 {
			queryReturning = fmt.Sprintf(" RETURNING \"%s\"", strings.Join(returnColumns, "\",\""))
		}

		cache.query = fmt.Sprintf(cache.query, queryOutput, queryReturning)
	}

	value := reflect.Indirect(reflect.ValueOf(o))
	vals := queries.ValuesFromMapping(value, cache.valueMapping)

	if boil.IsDebug(ctx) {
		writer := boil.DebugWriterFrom(ctx)
		fmt.Fprintln(writer, cache.query)
		fmt.Fprintln(writer, vals)
	}

	if len(cache.retMapping) != 0 {
		err = exec.QueryRowContext(ctx, cache.query, vals...).Scan(queries.PtrsFromMapping(value, cache.retMapping)...)
	} else {
		_, err = exec.ExecContext(ctx, cache.query, vals...)
	}

	if err != nil {
		return errors.Wrap(err, "account: unable to insert into account_merge_history")
	}

	if !cached {
		accountMergeHistoryInsertCacheMut.Lock()
		accountMergeHistoryInsertCache[key] = cache
		accountMergeHistoryInsertCacheMut.Unlock()
	}

	return o.doAfterInsertHooks(ctx, exec)
}

// Update uses an executor to update the AccountMergeHistory.
// See boil.Columns.UpdateColumnSet documentation to understand column list inference for updates.
// Update does not automatically update the record in case of default values. Use .Reload() to refresh the records.
func (o *AccountMergeHistory) Update(ctx context.Context, exec boil.ContextExecutor, columns boil.Columns) (int64, error) {
	var err error
	if err = o.doBeforeUpdateHooks(ctx, exec); err != nil {
		return 0, err
	}
	key := makeCacheKey(columns, nil)
	accountMergeHistoryUpdateCacheMut.RLock()
	cache, cached := accountMergeHistoryUpdateCache[key]
	accountMergeHistoryUpdateCacheMut.RUnlock()

	if !cached {
		wl := columns.UpdateColumnSet(
			accountMergeHistoryAllColumns,
			accountMergeHistoryPrimaryKeyColumns,
		)

		if !columns.IsWhitelist() {
			wl = strmangle.SetComplement(wl, []string{"created_at"})
		}
		if len(wl) == 0 {
			return 0, errors.New("account: unable to update account_merge_history, could not build whitelist")
		}

		cache.query = fmt.Sprintf("UPDATE \"account\".\"account_merge_history\" SET %s WHERE %s",
			strmangle.SetParamNames("\"", "\"", 1, wl),
			strmangle.WhereClause("\"", "\"", len(wl)+1, accountMergeHistoryPrimaryKeyColumns),
		)
		cache.valueMapping, err = queries.BindMapping(accountMergeHistoryType, accountMergeHistoryMapping, append(wl, accountMergeHistoryPrimaryKeyColumns...))
		if err != nil {
			return 0, err
		}
	}

	values := queries.ValuesFromMapping(reflect.Indirect(reflect.ValueOf(o)), cache.valueMapping)

	if boil.IsDebug(ctx) {
		writer := boil.DebugWriterFrom(ctx)
		fmt.Fprintln(writer, cache.query)
		fmt.Fprintln(writer, values)
	}
	var result sql.Result
	result, err = exec.ExecContext(ctx, cache.query, values...)
	if err != nil {
		return 0, errors.Wrap(err, "account: unable to update account_merge_history row")
	}

	rowsAff, err := result.RowsAffected()
	if err != nil {
		return 0, errors.Wrap(err, "account: failed to get rows affected by update for account_merge_history")
	}

	if !cached {
		accountMergeHistoryUpdateCacheMut.Lock()
		accountMergeHistoryUpdateCache[key] = cache
		accountMergeHistoryUpdateCacheMut.Unlock()
	}

	return rowsAff, o.doAfterUpdateHooks(ctx, exec)
}

// UpdateAll updates all rows with the specified column values.
func (q accountMergeHistoryQuery) UpdateAll(ctx context.Context, exec boil.ContextExecutor, cols M) (int64, error) {
	queries.SetUpdate(q.Query, cols)

	result, err := q.Query.ExecContext(ctx, exec)
	if err != nil {
		return 0, errors.Wrap(err, "account: unable to update all for account_merge_history")
	}

	rowsAff, err := result.RowsAffected()
	if err != nil {
		return 0, errors.Wrap(err, "account: unable to retrieve rows affected for account_merge_history")
	}

	return rowsAff, nil
}

// UpdateAll updates all rows with the specified column values, using an executor.
func (o AccountMergeHistorySlice) UpdateAll(ctx context.Context, exec boil.ContextExecutor, cols M) (int64, error) {
	ln := int64(len(o))
	if ln == 0 {
		return 0, nil
	}

	if len(cols) == 0 {
		return 0, errors.New("account: update all requires at least one column argument")
	}

	colNames := make([]string, len(cols))
	args := make([]interface{}, len(cols))

	i := 0
	for name, value := range cols {
		colNames[i] = name
		args[i] = value
		i++
	}

	// Append all of the primary key values for each column
	for _, obj := range o {
		pkeyArgs := queries.ValuesFromMapping(reflect.Indirect(reflect.ValueOf(obj)), accountMergeHistoryPrimaryKeyMapping)
		args = append(args, pkeyArgs...)
	}

	sql := fmt.Sprintf("UPDATE \"account\".\"account_merge_history\" SET %s WHERE %s",
		strmangle.SetParamNames("\"", "\"", 1, colNames),
		strmangle.WhereClauseRepeated(string(dialect.LQ), string(dialect.RQ), len(colNames)+1, accountMergeHistoryPrimaryKeyColumns, len(o)))

	if boil.IsDebug(ctx) {
		writer := boil.DebugWriterFrom(ctx)
		fmt.Fprintln(writer, sql)
		fmt.Fprintln(writer, args...)
	}
	result, err := exec.ExecContext(ctx, sql, args...)
	if err != nil {
		return 0, errors.Wrap(err, "account: unable to update all in accountMergeHistory slice")
	}

	rowsAff, err := result.RowsAffected()
	if err != nil {
		return 0, errors.Wrap(err, "account: unable to retrieve rows affected all in update all accountMergeHistory")
	}
	return rowsAff, nil
}

// Upsert attempts an insert using an executor, and does an update or ignore on conflict.
// See boil.Columns documentation for how to properly use updateColumns and insertColumns.
func (o *AccountMergeHistory) Upsert(ctx context.Context, exec boil.ContextExecutor, updateOnConflict bool, conflictColumns []string, updateColumns, insertColumns boil.Columns) error {
	if o == nil {
		return errors.New("account: no account_merge_history provided for upsert")
	}

	if err := o.doBeforeUpsertHooks(ctx, exec); err != nil {
		return err
	}

	nzDefaults := queries.NonZeroDefaultSet(accountMergeHistoryColumnsWithDefault, o)

	// Build cache key in-line uglily - mysql vs psql problems
	buf := strmangle.GetBuffer()
	if updateOnConflict {
		buf.WriteByte('t')
	} else {
		buf.WriteByte('f')
	}
	buf.WriteByte('.')
	for _, c := range conflictColumns {
		buf.WriteString(c)
	}
	buf.WriteByte('.')
	buf.WriteString(strconv.Itoa(updateColumns.Kind))
	for _, c := range updateColumns.Cols {
		buf.WriteString(c)
	}
	buf.WriteByte('.')
	buf.WriteString(strconv.Itoa(insertColumns.Kind))
	for _, c := range insertColumns.Cols {
		buf.WriteString(c)
	}
	buf.WriteByte('.')
	for _, c := range nzDefaults {
		buf.WriteString(c)
	}
	key := buf.String()
	strmangle.PutBuffer(buf)

	accountMergeHistoryUpsertCacheMut.RLock()
	cache, cached := accountMergeHistoryUpsertCache[key]
	accountMergeHistoryUpsertCacheMut.RUnlock()

	var err error

	if !cached {
		insert, ret := insertColumns.InsertColumnSet(
			accountMergeHistoryAllColumns,
			accountMergeHistoryColumnsWithDefault,
			accountMergeHistoryColumnsWithoutDefault,
			nzDefaults,
		)

		update := updateColumns.UpdateColumnSet(
			accountMergeHistoryAllColumns,
			accountMergeHistoryPrimaryKeyColumns,
		)

		if updateOnConflict && len(update) == 0 {
			return errors.New("account: unable to upsert account_merge_history, could not build update column list")
		}

		conflict := conflictColumns
		if len(conflict) == 0 {
			conflict = make([]string, len(accountMergeHistoryPrimaryKeyColumns))
			copy(conflict, accountMergeHistoryPrimaryKeyColumns)
		}
		cache.query = buildUpsertQueryPostgres(dialect, "\"account\".\"account_merge_history\"", updateOnConflict, ret, update, conflict, insert)

		cache.valueMapping, err = queries.BindMapping(accountMergeHistoryType, accountMergeHistoryMapping, insert)
		if err != nil {
			return err
		}
		if len(ret) != 0 {
			cache.retMapping, err = queries.BindMapping(accountMergeHistoryType, accountMergeHistoryMapping, ret)
			if err != nil {
				return err
			}
		}
	}

	value := reflect.Indirect(reflect.ValueOf(o))
	vals := queries.ValuesFromMapping(value, cache.valueMapping)
	var returns []interface{}
	if len(cache.retMapping) != 0 {
		returns = queries.PtrsFromMapping(value, cache.retMapping)
	}

	if boil.IsDebug(ctx) {
		writer := boil.DebugWriterFrom(ctx)
		fmt.Fprintln(writer, cache.query)
		fmt.Fprintln(writer, vals)
	}
	if len(cache.retMapping) != 0 {
		err = exec.QueryRowContext(ctx, cache.query, vals...).Scan(returns...)
		if errors.Is(err, sql.ErrNoRows) {
			err = nil // Postgres doesn't return anything when there's no update
		}
	} else {
		_, err = exec.ExecContext(ctx, cache.query, vals...)
	}
	if err != nil {
		return errors.Wrap(err, "account: unable to upsert account_merge_history")
	}

	if !cached {
		accountMergeHistoryUpsertCacheMut.Lock()
		accountMergeHistoryUpsertCache[key] = cache
		accountMergeHistoryUpsertCacheMut.Unlock()
	}

	return o.doAfterUpsertHooks(ctx, exec)
}

// Delete deletes a single AccountMergeHistory record with an executor.
// Delete will match against the primary key column to find the record to delete.
func (o *AccountMergeHistory) Delete(ctx context.Context, exec boil.ContextExecutor) (int64, error) {
	if o == nil {
		return 0, errors.New("account: no AccountMergeHistory provided for delete")
	}

	if err := o.doBeforeDeleteHooks(ctx, exec); err != nil {
		return 0, err
	}

	args := queries.ValuesFromMapping(reflect.Indirect(reflect.ValueOf(o)), accountMergeHistoryPrimaryKeyMapping)
	sql := "DELETE FROM \"account\".\"account_merge_history\" WHERE \"merge_id\"=$1"

	if boil.IsDebug(ctx) {
		writer := boil.DebugWriterFrom(ctx)
		fmt.Fprintln(writer, sql)
		fmt.Fprintln(writer, args...)
	}
	result, err := exec.ExecContext(ctx, sql, args...)
	if err != nil {
		return 0, errors.Wrap(err, "account: unable to delete from account_merge_history")
	}

	rowsAff, err := result.RowsAffected()
	if err != nil {
		return 0, errors.Wrap(err, "account: failed to get rows affected by delete for account_merge_history")
	}

	if err := o.doAfterDeleteHooks(ctx, exec); err != nil {
		return 0, err
	}

	return rowsAff, nil
}

// DeleteAll deletes all matching rows.
func (q accountMergeHistoryQuery) DeleteAll(ctx context.Context, exec boil.ContextExecutor) (int64, error) {
	if q.Query == nil {
		return 0, errors.New("account: no accountMergeHistoryQuery provided for delete all")
	}

	queries.SetDelete(q.Query)

	result, err := q.Query.ExecContext(ctx, exec)
	if err != nil {
		return 0, errors.Wrap(err, "account: unable to delete all from account_merge_history")
	}

	rowsAff, err := result.RowsAffected()
	if err != nil {
		return 0, errors.Wrap(err, "account: failed to get rows affected by deleteall for account_merge_history")
	}

	return rowsAff, nil
}

// DeleteAll deletes all rows in the slice, using an executor.
func (o AccountMergeHistorySlice) DeleteAll(ctx context.Context, exec boil.ContextExecutor) (int64, error) {
	if len(o) == 0 {
		return 0, nil
	}

	if len(accountMergeHistoryBeforeDeleteHooks) != 0 {
		for _, obj := range o {
			if err := obj.doBeforeDeleteHooks(ctx, exec); err != nil {
				return 0, err
			}
		}
	}

	var args []interface{}
	for _, obj := range o {
		pkeyArgs := queries.ValuesFromMapping(reflect.Indirect(reflect.ValueOf(obj)), accountMergeHistoryPrimaryKeyMapping)
		args = append(args, pkeyArgs...)
	}

	sql := "DELETE FROM \"account\".\"account_merge_history\" WHERE " +
		strmangle.WhereClauseRepeated(string(dialect.LQ), string(dialect.RQ), 1, accountMergeHistoryPrimaryKeyColumns, len(o))

	if boil.IsDebug(ctx) {
		writer := boil.DebugWriterFrom(ctx)
		fmt.Fprintln(writer, sql)
		fmt.Fprintln(writer, args)
	}
	result, err := exec.ExecContext(ctx, sql, args...)
	if err != nil {
		return 0, errors.Wrap(err, "account: unable to delete all from accountMergeHistory slice")
	}

	rowsAff, err := result.RowsAffected()
	if err != nil {
		return 0, errors.Wrap(err, "account: failed to get rows affected by deleteall for account_merge_history")
	}

	if len(accountMergeHistoryAfterDeleteHooks) != 0 {
		for _, obj := range o {
			if err := obj.doAfterDeleteHooks(ctx, exec); err != nil {
				return 0, err
			}
		}
	}

	return rowsAff, nil
}

// Reload refetches the object from the database
// using the primary keys with an executor.
func (o *AccountMergeHistory) Reload(ctx context.Context, exec boil.ContextExecutor) error {
	ret, err := FindAccountMergeHistory(ctx, exec, o.MergeID)
	if err != nil {
		return err
	}

	*o = *ret
	return nil
}

// ReloadAll refetches every row with matching primary key column values
// and overwrites the original object slice with the newly updated slice.
func (o *AccountMergeHistorySlice) ReloadAll(ctx context.Context, exec boil.ContextExecutor) error {
	if o == nil || len(*o) == 0 {
		return nil
	}

	slice := AccountMergeHistorySlice{}
	var args []interface{}
	for _, obj := range *o {
		pkeyArgs := queries.ValuesFromMapping(reflect.Indirect(reflect.ValueOf(obj)), accountMergeHistoryPrimaryKeyMapping)
		args = append(args, pkeyArgs...)
	}

	sql := "SELECT \"account\".\"account_merge_history\".* FROM \"account\".\"account_merge_history\" WHERE " +
		strmangle.WhereClauseRepeated(string(dialect.LQ), string(dialect.RQ), 1, accountMergeHistoryPrimaryKeyColumns, len(*o))

	q := queries.Raw(sql, args...)

	err := q.Bind(ctx, exec, &slice)
	if err != nil {
		return errors.Wrap(err, "account: unable to reload all in AccountMergeHistorySlice")
	}

	*o = slice

	return nil
}

// AccountMergeHistoryExists checks if the AccountMergeHistory row exists.
func AccountMergeHistoryExists(ctx context.Context, exec boil.ContextExecutor, mergeID string) (bool, error) {
	var exists bool
	sql := "select exists(select 1 from \"account\".\"account_merge_history\" where \"merge_id\"=$1 limit 1)"

	if boil.IsDebug(ctx) {
		writer := boil.DebugWriterFrom(ctx)
		fmt.Fprintln(writer, sql)
		fmt.Fprintln(writer, mergeID)
	}
	row := exec.QueryRowContext(ctx, sql, mergeID)

	err := row.Scan(&exists)
	if err != nil {
		return false, errors.Wrap(err, "account: unable to check if account_merge_history exists")
	}

	return exists, nil
}
