// Code generated by SQLBoiler (https://github.com/volatiletech/sqlboiler). DO NOT EDIT.
// This file is meant to be re-generated in place and/or deleted at any time.

package telematics

import (
	"context"
	"database/sql"
	"fmt"
	"reflect"
	"strconv"
	"strings"
	"sync"
	"time"

	"github.com/friendsofgo/errors"
	"github.com/volatiletech/sqlboiler/v4/boil"
	"github.com/volatiletech/sqlboiler/v4/queries"
	"github.com/volatiletech/sqlboiler/v4/queries/qm"
	"github.com/volatiletech/sqlboiler/v4/queries/qmhelper"
	"github.com/volatiletech/strmangle"
)

// CMTFleetScore is an object representing the database table.
type CMTFleetScore struct {
	HandleID        string    `boil:"handle_id" json:"handle_id" toml:"handle_id" yaml:"handle_id"`
	ScoreDate       time.Time `boil:"score_date" json:"score_date" toml:"score_date" yaml:"score_date"`
	RollingWindow   string    `boil:"rolling_window" json:"rolling_window" toml:"rolling_window" yaml:"rolling_window"`
	TripDistanceKM  float64   `boil:"trip_distance_km" json:"trip_distance_km" toml:"trip_distance_km" yaml:"trip_distance_km"`
	TripDurationSec float64   `boil:"trip_duration_sec" json:"trip_duration_sec" toml:"trip_duration_sec" yaml:"trip_duration_sec"`
	TripCount       int       `boil:"trip_count" json:"trip_count" toml:"trip_count" yaml:"trip_count"`
	FleetScore      int       `boil:"fleet_score" json:"fleet_score" toml:"fleet_score" yaml:"fleet_score"`
	CMTJobID        string    `boil:"cmt_job_id" json:"cmt_job_id" toml:"cmt_job_id" yaml:"cmt_job_id"`
	JobRunID        string    `boil:"job_run_id" json:"job_run_id" toml:"job_run_id" yaml:"job_run_id"`
	LastUpdatedAt   time.Time `boil:"last_updated_at" json:"last_updated_at" toml:"last_updated_at" yaml:"last_updated_at"`

	R *cmtFleetScoreR `boil:"" json:"" toml:"" yaml:""`
	L cmtFleetScoreL  `boil:"-" json:"-" toml:"-" yaml:"-"`
}

var CMTFleetScoreColumns = struct {
	HandleID        string
	ScoreDate       string
	RollingWindow   string
	TripDistanceKM  string
	TripDurationSec string
	TripCount       string
	FleetScore      string
	CMTJobID        string
	JobRunID        string
	LastUpdatedAt   string
}{
	HandleID:        "handle_id",
	ScoreDate:       "score_date",
	RollingWindow:   "rolling_window",
	TripDistanceKM:  "trip_distance_km",
	TripDurationSec: "trip_duration_sec",
	TripCount:       "trip_count",
	FleetScore:      "fleet_score",
	CMTJobID:        "cmt_job_id",
	JobRunID:        "job_run_id",
	LastUpdatedAt:   "last_updated_at",
}

var CMTFleetScoreTableColumns = struct {
	HandleID        string
	ScoreDate       string
	RollingWindow   string
	TripDistanceKM  string
	TripDurationSec string
	TripCount       string
	FleetScore      string
	CMTJobID        string
	JobRunID        string
	LastUpdatedAt   string
}{
	HandleID:        "cmt_fleet_scores.handle_id",
	ScoreDate:       "cmt_fleet_scores.score_date",
	RollingWindow:   "cmt_fleet_scores.rolling_window",
	TripDistanceKM:  "cmt_fleet_scores.trip_distance_km",
	TripDurationSec: "cmt_fleet_scores.trip_duration_sec",
	TripCount:       "cmt_fleet_scores.trip_count",
	FleetScore:      "cmt_fleet_scores.fleet_score",
	CMTJobID:        "cmt_fleet_scores.cmt_job_id",
	JobRunID:        "cmt_fleet_scores.job_run_id",
	LastUpdatedAt:   "cmt_fleet_scores.last_updated_at",
}

// Generated where

type whereHelperfloat64 struct{ field string }

func (w whereHelperfloat64) EQ(x float64) qm.QueryMod { return qmhelper.Where(w.field, qmhelper.EQ, x) }
func (w whereHelperfloat64) NEQ(x float64) qm.QueryMod {
	return qmhelper.Where(w.field, qmhelper.NEQ, x)
}
func (w whereHelperfloat64) LT(x float64) qm.QueryMod { return qmhelper.Where(w.field, qmhelper.LT, x) }
func (w whereHelperfloat64) LTE(x float64) qm.QueryMod {
	return qmhelper.Where(w.field, qmhelper.LTE, x)
}
func (w whereHelperfloat64) GT(x float64) qm.QueryMod { return qmhelper.Where(w.field, qmhelper.GT, x) }
func (w whereHelperfloat64) GTE(x float64) qm.QueryMod {
	return qmhelper.Where(w.field, qmhelper.GTE, x)
}
func (w whereHelperfloat64) IN(slice []float64) qm.QueryMod {
	values := make([]interface{}, 0, len(slice))
	for _, value := range slice {
		values = append(values, value)
	}
	return qm.WhereIn(fmt.Sprintf("%s IN ?", w.field), values...)
}
func (w whereHelperfloat64) NIN(slice []float64) qm.QueryMod {
	values := make([]interface{}, 0, len(slice))
	for _, value := range slice {
		values = append(values, value)
	}
	return qm.WhereNotIn(fmt.Sprintf("%s NOT IN ?", w.field), values...)
}

var CMTFleetScoreWhere = struct {
	HandleID        whereHelperstring
	ScoreDate       whereHelpertime_Time
	RollingWindow   whereHelperstring
	TripDistanceKM  whereHelperfloat64
	TripDurationSec whereHelperfloat64
	TripCount       whereHelperint
	FleetScore      whereHelperint
	CMTJobID        whereHelperstring
	JobRunID        whereHelperstring
	LastUpdatedAt   whereHelpertime_Time
}{
	HandleID:        whereHelperstring{field: "\"telematics\".\"cmt_fleet_scores\".\"handle_id\""},
	ScoreDate:       whereHelpertime_Time{field: "\"telematics\".\"cmt_fleet_scores\".\"score_date\""},
	RollingWindow:   whereHelperstring{field: "\"telematics\".\"cmt_fleet_scores\".\"rolling_window\""},
	TripDistanceKM:  whereHelperfloat64{field: "\"telematics\".\"cmt_fleet_scores\".\"trip_distance_km\""},
	TripDurationSec: whereHelperfloat64{field: "\"telematics\".\"cmt_fleet_scores\".\"trip_duration_sec\""},
	TripCount:       whereHelperint{field: "\"telematics\".\"cmt_fleet_scores\".\"trip_count\""},
	FleetScore:      whereHelperint{field: "\"telematics\".\"cmt_fleet_scores\".\"fleet_score\""},
	CMTJobID:        whereHelperstring{field: "\"telematics\".\"cmt_fleet_scores\".\"cmt_job_id\""},
	JobRunID:        whereHelperstring{field: "\"telematics\".\"cmt_fleet_scores\".\"job_run_id\""},
	LastUpdatedAt:   whereHelpertime_Time{field: "\"telematics\".\"cmt_fleet_scores\".\"last_updated_at\""},
}

// CMTFleetScoreRels is where relationship names are stored.
var CMTFleetScoreRels = struct {
}{}

// cmtFleetScoreR is where relationships are stored.
type cmtFleetScoreR struct {
}

// NewStruct creates a new relationship struct
func (*cmtFleetScoreR) NewStruct() *cmtFleetScoreR {
	return &cmtFleetScoreR{}
}

// cmtFleetScoreL is where Load methods for each relationship are stored.
type cmtFleetScoreL struct{}

var (
	cmtFleetScoreAllColumns            = []string{"handle_id", "score_date", "rolling_window", "trip_distance_km", "trip_duration_sec", "trip_count", "fleet_score", "cmt_job_id", "job_run_id", "last_updated_at"}
	cmtFleetScoreColumnsWithoutDefault = []string{"handle_id", "score_date", "rolling_window", "trip_distance_km", "trip_duration_sec", "trip_count", "fleet_score", "cmt_job_id", "job_run_id", "last_updated_at"}
	cmtFleetScoreColumnsWithDefault    = []string{}
	cmtFleetScorePrimaryKeyColumns     = []string{"handle_id", "score_date"}
	cmtFleetScoreGeneratedColumns      = []string{}
)

type (
	// CMTFleetScoreSlice is an alias for a slice of pointers to CMTFleetScore.
	// This should almost always be used instead of []CMTFleetScore.
	CMTFleetScoreSlice []*CMTFleetScore
	// CMTFleetScoreHook is the signature for custom CMTFleetScore hook methods
	CMTFleetScoreHook func(context.Context, boil.ContextExecutor, *CMTFleetScore) error

	cmtFleetScoreQuery struct {
		*queries.Query
	}
)

// Cache for insert, update and upsert
var (
	cmtFleetScoreType                 = reflect.TypeOf(&CMTFleetScore{})
	cmtFleetScoreMapping              = queries.MakeStructMapping(cmtFleetScoreType)
	cmtFleetScorePrimaryKeyMapping, _ = queries.BindMapping(cmtFleetScoreType, cmtFleetScoreMapping, cmtFleetScorePrimaryKeyColumns)
	cmtFleetScoreInsertCacheMut       sync.RWMutex
	cmtFleetScoreInsertCache          = make(map[string]insertCache)
	cmtFleetScoreUpdateCacheMut       sync.RWMutex
	cmtFleetScoreUpdateCache          = make(map[string]updateCache)
	cmtFleetScoreUpsertCacheMut       sync.RWMutex
	cmtFleetScoreUpsertCache          = make(map[string]insertCache)
)

var (
	// Force time package dependency for automated UpdatedAt/CreatedAt.
	_ = time.Second
	// Force qmhelper dependency for where clause generation (which doesn't
	// always happen)
	_ = qmhelper.Where
)

var cmtFleetScoreAfterSelectHooks []CMTFleetScoreHook

var cmtFleetScoreBeforeInsertHooks []CMTFleetScoreHook
var cmtFleetScoreAfterInsertHooks []CMTFleetScoreHook

var cmtFleetScoreBeforeUpdateHooks []CMTFleetScoreHook
var cmtFleetScoreAfterUpdateHooks []CMTFleetScoreHook

var cmtFleetScoreBeforeDeleteHooks []CMTFleetScoreHook
var cmtFleetScoreAfterDeleteHooks []CMTFleetScoreHook

var cmtFleetScoreBeforeUpsertHooks []CMTFleetScoreHook
var cmtFleetScoreAfterUpsertHooks []CMTFleetScoreHook

// doAfterSelectHooks executes all "after Select" hooks.
func (o *CMTFleetScore) doAfterSelectHooks(ctx context.Context, exec boil.ContextExecutor) (err error) {
	if boil.HooksAreSkipped(ctx) {
		return nil
	}

	for _, hook := range cmtFleetScoreAfterSelectHooks {
		if err := hook(ctx, exec, o); err != nil {
			return err
		}
	}

	return nil
}

// doBeforeInsertHooks executes all "before insert" hooks.
func (o *CMTFleetScore) doBeforeInsertHooks(ctx context.Context, exec boil.ContextExecutor) (err error) {
	if boil.HooksAreSkipped(ctx) {
		return nil
	}

	for _, hook := range cmtFleetScoreBeforeInsertHooks {
		if err := hook(ctx, exec, o); err != nil {
			return err
		}
	}

	return nil
}

// doAfterInsertHooks executes all "after Insert" hooks.
func (o *CMTFleetScore) doAfterInsertHooks(ctx context.Context, exec boil.ContextExecutor) (err error) {
	if boil.HooksAreSkipped(ctx) {
		return nil
	}

	for _, hook := range cmtFleetScoreAfterInsertHooks {
		if err := hook(ctx, exec, o); err != nil {
			return err
		}
	}

	return nil
}

// doBeforeUpdateHooks executes all "before Update" hooks.
func (o *CMTFleetScore) doBeforeUpdateHooks(ctx context.Context, exec boil.ContextExecutor) (err error) {
	if boil.HooksAreSkipped(ctx) {
		return nil
	}

	for _, hook := range cmtFleetScoreBeforeUpdateHooks {
		if err := hook(ctx, exec, o); err != nil {
			return err
		}
	}

	return nil
}

// doAfterUpdateHooks executes all "after Update" hooks.
func (o *CMTFleetScore) doAfterUpdateHooks(ctx context.Context, exec boil.ContextExecutor) (err error) {
	if boil.HooksAreSkipped(ctx) {
		return nil
	}

	for _, hook := range cmtFleetScoreAfterUpdateHooks {
		if err := hook(ctx, exec, o); err != nil {
			return err
		}
	}

	return nil
}

// doBeforeDeleteHooks executes all "before Delete" hooks.
func (o *CMTFleetScore) doBeforeDeleteHooks(ctx context.Context, exec boil.ContextExecutor) (err error) {
	if boil.HooksAreSkipped(ctx) {
		return nil
	}

	for _, hook := range cmtFleetScoreBeforeDeleteHooks {
		if err := hook(ctx, exec, o); err != nil {
			return err
		}
	}

	return nil
}

// doAfterDeleteHooks executes all "after Delete" hooks.
func (o *CMTFleetScore) doAfterDeleteHooks(ctx context.Context, exec boil.ContextExecutor) (err error) {
	if boil.HooksAreSkipped(ctx) {
		return nil
	}

	for _, hook := range cmtFleetScoreAfterDeleteHooks {
		if err := hook(ctx, exec, o); err != nil {
			return err
		}
	}

	return nil
}

// doBeforeUpsertHooks executes all "before Upsert" hooks.
func (o *CMTFleetScore) doBeforeUpsertHooks(ctx context.Context, exec boil.ContextExecutor) (err error) {
	if boil.HooksAreSkipped(ctx) {
		return nil
	}

	for _, hook := range cmtFleetScoreBeforeUpsertHooks {
		if err := hook(ctx, exec, o); err != nil {
			return err
		}
	}

	return nil
}

// doAfterUpsertHooks executes all "after Upsert" hooks.
func (o *CMTFleetScore) doAfterUpsertHooks(ctx context.Context, exec boil.ContextExecutor) (err error) {
	if boil.HooksAreSkipped(ctx) {
		return nil
	}

	for _, hook := range cmtFleetScoreAfterUpsertHooks {
		if err := hook(ctx, exec, o); err != nil {
			return err
		}
	}

	return nil
}

// AddCMTFleetScoreHook registers your hook function for all future operations.
func AddCMTFleetScoreHook(hookPoint boil.HookPoint, cmtFleetScoreHook CMTFleetScoreHook) {
	switch hookPoint {
	case boil.AfterSelectHook:
		cmtFleetScoreAfterSelectHooks = append(cmtFleetScoreAfterSelectHooks, cmtFleetScoreHook)
	case boil.BeforeInsertHook:
		cmtFleetScoreBeforeInsertHooks = append(cmtFleetScoreBeforeInsertHooks, cmtFleetScoreHook)
	case boil.AfterInsertHook:
		cmtFleetScoreAfterInsertHooks = append(cmtFleetScoreAfterInsertHooks, cmtFleetScoreHook)
	case boil.BeforeUpdateHook:
		cmtFleetScoreBeforeUpdateHooks = append(cmtFleetScoreBeforeUpdateHooks, cmtFleetScoreHook)
	case boil.AfterUpdateHook:
		cmtFleetScoreAfterUpdateHooks = append(cmtFleetScoreAfterUpdateHooks, cmtFleetScoreHook)
	case boil.BeforeDeleteHook:
		cmtFleetScoreBeforeDeleteHooks = append(cmtFleetScoreBeforeDeleteHooks, cmtFleetScoreHook)
	case boil.AfterDeleteHook:
		cmtFleetScoreAfterDeleteHooks = append(cmtFleetScoreAfterDeleteHooks, cmtFleetScoreHook)
	case boil.BeforeUpsertHook:
		cmtFleetScoreBeforeUpsertHooks = append(cmtFleetScoreBeforeUpsertHooks, cmtFleetScoreHook)
	case boil.AfterUpsertHook:
		cmtFleetScoreAfterUpsertHooks = append(cmtFleetScoreAfterUpsertHooks, cmtFleetScoreHook)
	}
}

// One returns a single cmtFleetScore record from the query.
func (q cmtFleetScoreQuery) One(ctx context.Context, exec boil.ContextExecutor) (*CMTFleetScore, error) {
	o := &CMTFleetScore{}

	queries.SetLimit(q.Query, 1)

	err := q.Bind(ctx, exec, o)
	if err != nil {
		if errors.Is(err, sql.ErrNoRows) {
			return nil, sql.ErrNoRows
		}
		return nil, errors.Wrap(err, "telematics: failed to execute a one query for cmt_fleet_scores")
	}

	if err := o.doAfterSelectHooks(ctx, exec); err != nil {
		return o, err
	}

	return o, nil
}

// All returns all CMTFleetScore records from the query.
func (q cmtFleetScoreQuery) All(ctx context.Context, exec boil.ContextExecutor) (CMTFleetScoreSlice, error) {
	var o []*CMTFleetScore

	err := q.Bind(ctx, exec, &o)
	if err != nil {
		return nil, errors.Wrap(err, "telematics: failed to assign all query results to CMTFleetScore slice")
	}

	if len(cmtFleetScoreAfterSelectHooks) != 0 {
		for _, obj := range o {
			if err := obj.doAfterSelectHooks(ctx, exec); err != nil {
				return o, err
			}
		}
	}

	return o, nil
}

// Count returns the count of all CMTFleetScore records in the query.
func (q cmtFleetScoreQuery) Count(ctx context.Context, exec boil.ContextExecutor) (int64, error) {
	var count int64

	queries.SetSelect(q.Query, nil)
	queries.SetCount(q.Query)

	err := q.Query.QueryRowContext(ctx, exec).Scan(&count)
	if err != nil {
		return 0, errors.Wrap(err, "telematics: failed to count cmt_fleet_scores rows")
	}

	return count, nil
}

// Exists checks if the row exists in the table.
func (q cmtFleetScoreQuery) Exists(ctx context.Context, exec boil.ContextExecutor) (bool, error) {
	var count int64

	queries.SetSelect(q.Query, nil)
	queries.SetCount(q.Query)
	queries.SetLimit(q.Query, 1)

	err := q.Query.QueryRowContext(ctx, exec).Scan(&count)
	if err != nil {
		return false, errors.Wrap(err, "telematics: failed to check if cmt_fleet_scores exists")
	}

	return count > 0, nil
}

// CMTFleetScores retrieves all the records using an executor.
func CMTFleetScores(mods ...qm.QueryMod) cmtFleetScoreQuery {
	mods = append(mods, qm.From("\"telematics\".\"cmt_fleet_scores\""))
	q := NewQuery(mods...)
	if len(queries.GetSelect(q)) == 0 {
		queries.SetSelect(q, []string{"\"telematics\".\"cmt_fleet_scores\".*"})
	}

	return cmtFleetScoreQuery{q}
}

// FindCMTFleetScore retrieves a single record by ID with an executor.
// If selectCols is empty Find will return all columns.
func FindCMTFleetScore(ctx context.Context, exec boil.ContextExecutor, handleID string, scoreDate time.Time, selectCols ...string) (*CMTFleetScore, error) {
	cmtFleetScoreObj := &CMTFleetScore{}

	sel := "*"
	if len(selectCols) > 0 {
		sel = strings.Join(strmangle.IdentQuoteSlice(dialect.LQ, dialect.RQ, selectCols), ",")
	}
	query := fmt.Sprintf(
		"select %s from \"telematics\".\"cmt_fleet_scores\" where \"handle_id\"=$1 AND \"score_date\"=$2", sel,
	)

	q := queries.Raw(query, handleID, scoreDate)

	err := q.Bind(ctx, exec, cmtFleetScoreObj)
	if err != nil {
		if errors.Is(err, sql.ErrNoRows) {
			return nil, sql.ErrNoRows
		}
		return nil, errors.Wrap(err, "telematics: unable to select from cmt_fleet_scores")
	}

	if err = cmtFleetScoreObj.doAfterSelectHooks(ctx, exec); err != nil {
		return cmtFleetScoreObj, err
	}

	return cmtFleetScoreObj, nil
}

// Insert a single record using an executor.
// See boil.Columns.InsertColumnSet documentation to understand column list inference for inserts.
func (o *CMTFleetScore) Insert(ctx context.Context, exec boil.ContextExecutor, columns boil.Columns) error {
	if o == nil {
		return errors.New("telematics: no cmt_fleet_scores provided for insertion")
	}

	var err error

	if err := o.doBeforeInsertHooks(ctx, exec); err != nil {
		return err
	}

	nzDefaults := queries.NonZeroDefaultSet(cmtFleetScoreColumnsWithDefault, o)

	key := makeCacheKey(columns, nzDefaults)
	cmtFleetScoreInsertCacheMut.RLock()
	cache, cached := cmtFleetScoreInsertCache[key]
	cmtFleetScoreInsertCacheMut.RUnlock()

	if !cached {
		wl, returnColumns := columns.InsertColumnSet(
			cmtFleetScoreAllColumns,
			cmtFleetScoreColumnsWithDefault,
			cmtFleetScoreColumnsWithoutDefault,
			nzDefaults,
		)

		cache.valueMapping, err = queries.BindMapping(cmtFleetScoreType, cmtFleetScoreMapping, wl)
		if err != nil {
			return err
		}
		cache.retMapping, err = queries.BindMapping(cmtFleetScoreType, cmtFleetScoreMapping, returnColumns)
		if err != nil {
			return err
		}
		if len(wl) != 0 {
			cache.query = fmt.Sprintf("INSERT INTO \"telematics\".\"cmt_fleet_scores\" (\"%s\") %%sVALUES (%s)%%s", strings.Join(wl, "\",\""), strmangle.Placeholders(dialect.UseIndexPlaceholders, len(wl), 1, 1))
		} else {
			cache.query = "INSERT INTO \"telematics\".\"cmt_fleet_scores\" %sDEFAULT VALUES%s"
		}

		var queryOutput, queryReturning string

		if len(cache.retMapping) != 0 {
			queryReturning = fmt.Sprintf(" RETURNING \"%s\"", strings.Join(returnColumns, "\",\""))
		}

		cache.query = fmt.Sprintf(cache.query, queryOutput, queryReturning)
	}

	value := reflect.Indirect(reflect.ValueOf(o))
	vals := queries.ValuesFromMapping(value, cache.valueMapping)

	if boil.IsDebug(ctx) {
		writer := boil.DebugWriterFrom(ctx)
		fmt.Fprintln(writer, cache.query)
		fmt.Fprintln(writer, vals)
	}

	if len(cache.retMapping) != 0 {
		err = exec.QueryRowContext(ctx, cache.query, vals...).Scan(queries.PtrsFromMapping(value, cache.retMapping)...)
	} else {
		_, err = exec.ExecContext(ctx, cache.query, vals...)
	}

	if err != nil {
		return errors.Wrap(err, "telematics: unable to insert into cmt_fleet_scores")
	}

	if !cached {
		cmtFleetScoreInsertCacheMut.Lock()
		cmtFleetScoreInsertCache[key] = cache
		cmtFleetScoreInsertCacheMut.Unlock()
	}

	return o.doAfterInsertHooks(ctx, exec)
}

// Update uses an executor to update the CMTFleetScore.
// See boil.Columns.UpdateColumnSet documentation to understand column list inference for updates.
// Update does not automatically update the record in case of default values. Use .Reload() to refresh the records.
func (o *CMTFleetScore) Update(ctx context.Context, exec boil.ContextExecutor, columns boil.Columns) (int64, error) {
	var err error
	if err = o.doBeforeUpdateHooks(ctx, exec); err != nil {
		return 0, err
	}
	key := makeCacheKey(columns, nil)
	cmtFleetScoreUpdateCacheMut.RLock()
	cache, cached := cmtFleetScoreUpdateCache[key]
	cmtFleetScoreUpdateCacheMut.RUnlock()

	if !cached {
		wl := columns.UpdateColumnSet(
			cmtFleetScoreAllColumns,
			cmtFleetScorePrimaryKeyColumns,
		)

		if !columns.IsWhitelist() {
			wl = strmangle.SetComplement(wl, []string{"created_at"})
		}
		if len(wl) == 0 {
			return 0, errors.New("telematics: unable to update cmt_fleet_scores, could not build whitelist")
		}

		cache.query = fmt.Sprintf("UPDATE \"telematics\".\"cmt_fleet_scores\" SET %s WHERE %s",
			strmangle.SetParamNames("\"", "\"", 1, wl),
			strmangle.WhereClause("\"", "\"", len(wl)+1, cmtFleetScorePrimaryKeyColumns),
		)
		cache.valueMapping, err = queries.BindMapping(cmtFleetScoreType, cmtFleetScoreMapping, append(wl, cmtFleetScorePrimaryKeyColumns...))
		if err != nil {
			return 0, err
		}
	}

	values := queries.ValuesFromMapping(reflect.Indirect(reflect.ValueOf(o)), cache.valueMapping)

	if boil.IsDebug(ctx) {
		writer := boil.DebugWriterFrom(ctx)
		fmt.Fprintln(writer, cache.query)
		fmt.Fprintln(writer, values)
	}
	var result sql.Result
	result, err = exec.ExecContext(ctx, cache.query, values...)
	if err != nil {
		return 0, errors.Wrap(err, "telematics: unable to update cmt_fleet_scores row")
	}

	rowsAff, err := result.RowsAffected()
	if err != nil {
		return 0, errors.Wrap(err, "telematics: failed to get rows affected by update for cmt_fleet_scores")
	}

	if !cached {
		cmtFleetScoreUpdateCacheMut.Lock()
		cmtFleetScoreUpdateCache[key] = cache
		cmtFleetScoreUpdateCacheMut.Unlock()
	}

	return rowsAff, o.doAfterUpdateHooks(ctx, exec)
}

// UpdateAll updates all rows with the specified column values.
func (q cmtFleetScoreQuery) UpdateAll(ctx context.Context, exec boil.ContextExecutor, cols M) (int64, error) {
	queries.SetUpdate(q.Query, cols)

	result, err := q.Query.ExecContext(ctx, exec)
	if err != nil {
		return 0, errors.Wrap(err, "telematics: unable to update all for cmt_fleet_scores")
	}

	rowsAff, err := result.RowsAffected()
	if err != nil {
		return 0, errors.Wrap(err, "telematics: unable to retrieve rows affected for cmt_fleet_scores")
	}

	return rowsAff, nil
}

// UpdateAll updates all rows with the specified column values, using an executor.
func (o CMTFleetScoreSlice) UpdateAll(ctx context.Context, exec boil.ContextExecutor, cols M) (int64, error) {
	ln := int64(len(o))
	if ln == 0 {
		return 0, nil
	}

	if len(cols) == 0 {
		return 0, errors.New("telematics: update all requires at least one column argument")
	}

	colNames := make([]string, len(cols))
	args := make([]interface{}, len(cols))

	i := 0
	for name, value := range cols {
		colNames[i] = name
		args[i] = value
		i++
	}

	// Append all of the primary key values for each column
	for _, obj := range o {
		pkeyArgs := queries.ValuesFromMapping(reflect.Indirect(reflect.ValueOf(obj)), cmtFleetScorePrimaryKeyMapping)
		args = append(args, pkeyArgs...)
	}

	sql := fmt.Sprintf("UPDATE \"telematics\".\"cmt_fleet_scores\" SET %s WHERE %s",
		strmangle.SetParamNames("\"", "\"", 1, colNames),
		strmangle.WhereClauseRepeated(string(dialect.LQ), string(dialect.RQ), len(colNames)+1, cmtFleetScorePrimaryKeyColumns, len(o)))

	if boil.IsDebug(ctx) {
		writer := boil.DebugWriterFrom(ctx)
		fmt.Fprintln(writer, sql)
		fmt.Fprintln(writer, args...)
	}
	result, err := exec.ExecContext(ctx, sql, args...)
	if err != nil {
		return 0, errors.Wrap(err, "telematics: unable to update all in cmtFleetScore slice")
	}

	rowsAff, err := result.RowsAffected()
	if err != nil {
		return 0, errors.Wrap(err, "telematics: unable to retrieve rows affected all in update all cmtFleetScore")
	}
	return rowsAff, nil
}

// Upsert attempts an insert using an executor, and does an update or ignore on conflict.
// See boil.Columns documentation for how to properly use updateColumns and insertColumns.
func (o *CMTFleetScore) Upsert(ctx context.Context, exec boil.ContextExecutor, updateOnConflict bool, conflictColumns []string, updateColumns, insertColumns boil.Columns) error {
	if o == nil {
		return errors.New("telematics: no cmt_fleet_scores provided for upsert")
	}

	if err := o.doBeforeUpsertHooks(ctx, exec); err != nil {
		return err
	}

	nzDefaults := queries.NonZeroDefaultSet(cmtFleetScoreColumnsWithDefault, o)

	// Build cache key in-line uglily - mysql vs psql problems
	buf := strmangle.GetBuffer()
	if updateOnConflict {
		buf.WriteByte('t')
	} else {
		buf.WriteByte('f')
	}
	buf.WriteByte('.')
	for _, c := range conflictColumns {
		buf.WriteString(c)
	}
	buf.WriteByte('.')
	buf.WriteString(strconv.Itoa(updateColumns.Kind))
	for _, c := range updateColumns.Cols {
		buf.WriteString(c)
	}
	buf.WriteByte('.')
	buf.WriteString(strconv.Itoa(insertColumns.Kind))
	for _, c := range insertColumns.Cols {
		buf.WriteString(c)
	}
	buf.WriteByte('.')
	for _, c := range nzDefaults {
		buf.WriteString(c)
	}
	key := buf.String()
	strmangle.PutBuffer(buf)

	cmtFleetScoreUpsertCacheMut.RLock()
	cache, cached := cmtFleetScoreUpsertCache[key]
	cmtFleetScoreUpsertCacheMut.RUnlock()

	var err error

	if !cached {
		insert, ret := insertColumns.InsertColumnSet(
			cmtFleetScoreAllColumns,
			cmtFleetScoreColumnsWithDefault,
			cmtFleetScoreColumnsWithoutDefault,
			nzDefaults,
		)

		update := updateColumns.UpdateColumnSet(
			cmtFleetScoreAllColumns,
			cmtFleetScorePrimaryKeyColumns,
		)

		if updateOnConflict && len(update) == 0 {
			return errors.New("telematics: unable to upsert cmt_fleet_scores, could not build update column list")
		}

		conflict := conflictColumns
		if len(conflict) == 0 {
			conflict = make([]string, len(cmtFleetScorePrimaryKeyColumns))
			copy(conflict, cmtFleetScorePrimaryKeyColumns)
		}
		cache.query = buildUpsertQueryPostgres(dialect, "\"telematics\".\"cmt_fleet_scores\"", updateOnConflict, ret, update, conflict, insert)

		cache.valueMapping, err = queries.BindMapping(cmtFleetScoreType, cmtFleetScoreMapping, insert)
		if err != nil {
			return err
		}
		if len(ret) != 0 {
			cache.retMapping, err = queries.BindMapping(cmtFleetScoreType, cmtFleetScoreMapping, ret)
			if err != nil {
				return err
			}
		}
	}

	value := reflect.Indirect(reflect.ValueOf(o))
	vals := queries.ValuesFromMapping(value, cache.valueMapping)
	var returns []interface{}
	if len(cache.retMapping) != 0 {
		returns = queries.PtrsFromMapping(value, cache.retMapping)
	}

	if boil.IsDebug(ctx) {
		writer := boil.DebugWriterFrom(ctx)
		fmt.Fprintln(writer, cache.query)
		fmt.Fprintln(writer, vals)
	}
	if len(cache.retMapping) != 0 {
		err = exec.QueryRowContext(ctx, cache.query, vals...).Scan(returns...)
		if errors.Is(err, sql.ErrNoRows) {
			err = nil // Postgres doesn't return anything when there's no update
		}
	} else {
		_, err = exec.ExecContext(ctx, cache.query, vals...)
	}
	if err != nil {
		return errors.Wrap(err, "telematics: unable to upsert cmt_fleet_scores")
	}

	if !cached {
		cmtFleetScoreUpsertCacheMut.Lock()
		cmtFleetScoreUpsertCache[key] = cache
		cmtFleetScoreUpsertCacheMut.Unlock()
	}

	return o.doAfterUpsertHooks(ctx, exec)
}

// Delete deletes a single CMTFleetScore record with an executor.
// Delete will match against the primary key column to find the record to delete.
func (o *CMTFleetScore) Delete(ctx context.Context, exec boil.ContextExecutor) (int64, error) {
	if o == nil {
		return 0, errors.New("telematics: no CMTFleetScore provided for delete")
	}

	if err := o.doBeforeDeleteHooks(ctx, exec); err != nil {
		return 0, err
	}

	args := queries.ValuesFromMapping(reflect.Indirect(reflect.ValueOf(o)), cmtFleetScorePrimaryKeyMapping)
	sql := "DELETE FROM \"telematics\".\"cmt_fleet_scores\" WHERE \"handle_id\"=$1 AND \"score_date\"=$2"

	if boil.IsDebug(ctx) {
		writer := boil.DebugWriterFrom(ctx)
		fmt.Fprintln(writer, sql)
		fmt.Fprintln(writer, args...)
	}
	result, err := exec.ExecContext(ctx, sql, args...)
	if err != nil {
		return 0, errors.Wrap(err, "telematics: unable to delete from cmt_fleet_scores")
	}

	rowsAff, err := result.RowsAffected()
	if err != nil {
		return 0, errors.Wrap(err, "telematics: failed to get rows affected by delete for cmt_fleet_scores")
	}

	if err := o.doAfterDeleteHooks(ctx, exec); err != nil {
		return 0, err
	}

	return rowsAff, nil
}

// DeleteAll deletes all matching rows.
func (q cmtFleetScoreQuery) DeleteAll(ctx context.Context, exec boil.ContextExecutor) (int64, error) {
	if q.Query == nil {
		return 0, errors.New("telematics: no cmtFleetScoreQuery provided for delete all")
	}

	queries.SetDelete(q.Query)

	result, err := q.Query.ExecContext(ctx, exec)
	if err != nil {
		return 0, errors.Wrap(err, "telematics: unable to delete all from cmt_fleet_scores")
	}

	rowsAff, err := result.RowsAffected()
	if err != nil {
		return 0, errors.Wrap(err, "telematics: failed to get rows affected by deleteall for cmt_fleet_scores")
	}

	return rowsAff, nil
}

// DeleteAll deletes all rows in the slice, using an executor.
func (o CMTFleetScoreSlice) DeleteAll(ctx context.Context, exec boil.ContextExecutor) (int64, error) {
	if len(o) == 0 {
		return 0, nil
	}

	if len(cmtFleetScoreBeforeDeleteHooks) != 0 {
		for _, obj := range o {
			if err := obj.doBeforeDeleteHooks(ctx, exec); err != nil {
				return 0, err
			}
		}
	}

	var args []interface{}
	for _, obj := range o {
		pkeyArgs := queries.ValuesFromMapping(reflect.Indirect(reflect.ValueOf(obj)), cmtFleetScorePrimaryKeyMapping)
		args = append(args, pkeyArgs...)
	}

	sql := "DELETE FROM \"telematics\".\"cmt_fleet_scores\" WHERE " +
		strmangle.WhereClauseRepeated(string(dialect.LQ), string(dialect.RQ), 1, cmtFleetScorePrimaryKeyColumns, len(o))

	if boil.IsDebug(ctx) {
		writer := boil.DebugWriterFrom(ctx)
		fmt.Fprintln(writer, sql)
		fmt.Fprintln(writer, args)
	}
	result, err := exec.ExecContext(ctx, sql, args...)
	if err != nil {
		return 0, errors.Wrap(err, "telematics: unable to delete all from cmtFleetScore slice")
	}

	rowsAff, err := result.RowsAffected()
	if err != nil {
		return 0, errors.Wrap(err, "telematics: failed to get rows affected by deleteall for cmt_fleet_scores")
	}

	if len(cmtFleetScoreAfterDeleteHooks) != 0 {
		for _, obj := range o {
			if err := obj.doAfterDeleteHooks(ctx, exec); err != nil {
				return 0, err
			}
		}
	}

	return rowsAff, nil
}

// Reload refetches the object from the database
// using the primary keys with an executor.
func (o *CMTFleetScore) Reload(ctx context.Context, exec boil.ContextExecutor) error {
	ret, err := FindCMTFleetScore(ctx, exec, o.HandleID, o.ScoreDate)
	if err != nil {
		return err
	}

	*o = *ret
	return nil
}

// ReloadAll refetches every row with matching primary key column values
// and overwrites the original object slice with the newly updated slice.
func (o *CMTFleetScoreSlice) ReloadAll(ctx context.Context, exec boil.ContextExecutor) error {
	if o == nil || len(*o) == 0 {
		return nil
	}

	slice := CMTFleetScoreSlice{}
	var args []interface{}
	for _, obj := range *o {
		pkeyArgs := queries.ValuesFromMapping(reflect.Indirect(reflect.ValueOf(obj)), cmtFleetScorePrimaryKeyMapping)
		args = append(args, pkeyArgs...)
	}

	sql := "SELECT \"telematics\".\"cmt_fleet_scores\".* FROM \"telematics\".\"cmt_fleet_scores\" WHERE " +
		strmangle.WhereClauseRepeated(string(dialect.LQ), string(dialect.RQ), 1, cmtFleetScorePrimaryKeyColumns, len(*o))

	q := queries.Raw(sql, args...)

	err := q.Bind(ctx, exec, &slice)
	if err != nil {
		return errors.Wrap(err, "telematics: unable to reload all in CMTFleetScoreSlice")
	}

	*o = slice

	return nil
}

// CMTFleetScoreExists checks if the CMTFleetScore row exists.
func CMTFleetScoreExists(ctx context.Context, exec boil.ContextExecutor, handleID string, scoreDate time.Time) (bool, error) {
	var exists bool
	sql := "select exists(select 1 from \"telematics\".\"cmt_fleet_scores\" where \"handle_id\"=$1 AND \"score_date\"=$2 limit 1)"

	if boil.IsDebug(ctx) {
		writer := boil.DebugWriterFrom(ctx)
		fmt.Fprintln(writer, sql)
		fmt.Fprintln(writer, handleID, scoreDate)
	}
	row := exec.QueryRowContext(ctx, sql, handleID, scoreDate)

	err := row.Scan(&exists)
	if err != nil {
		return false, errors.Wrap(err, "telematics: unable to check if cmt_fleet_scores exists")
	}

	return exists, nil
}
