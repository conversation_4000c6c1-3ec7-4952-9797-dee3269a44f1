// Code generated by SQLBoiler (https://github.com/volatiletech/sqlboiler). DO NOT EDIT.
// This file is meant to be re-generated in place and/or deleted at any time.

package endorsementapp

import (
	"context"
	"database/sql"
	"fmt"
	"reflect"
	"strconv"
	"strings"
	"sync"
	"time"

	"github.com/friendsofgo/errors"
	"github.com/volatiletech/null/v8"
	"github.com/volatiletech/sqlboiler/v4/boil"
	"github.com/volatiletech/sqlboiler/v4/queries"
	"github.com/volatiletech/sqlboiler/v4/queries/qm"
	"github.com/volatiletech/sqlboiler/v4/queries/qmhelper"
	"github.com/volatiletech/sqlboiler/v4/types"
	"github.com/volatiletech/strmangle"
)

// Review is an object representing the database table.
type Review struct {
	ID                         string            `boil:"id" json:"id" toml:"id" yaml:"id"`
	RequestID                  string            `boil:"request_id" json:"request_id" toml:"request_id" yaml:"request_id"`
	State                      string            `boil:"state" json:"state" toml:"state" yaml:"state"`
	PricingContextIds          types.StringArray `boil:"pricing_context_ids" json:"pricing_context_ids,omitempty" toml:"pricing_context_ids" yaml:"pricing_context_ids,omitempty"`
	UnderwritingAssistantID    string            `boil:"underwriting_assistant_id" json:"underwriting_assistant_id" toml:"underwriting_assistant_id" yaml:"underwriting_assistant_id"`
	WrittenPremium             null.JSON         `boil:"written_premium" json:"written_premium,omitempty" toml:"written_premium" yaml:"written_premium,omitempty"`
	ApprovedAt                 null.Time         `boil:"approved_at" json:"approved_at,omitempty" toml:"approved_at" yaml:"approved_at,omitempty"`
	CreatedAt                  time.Time         `boil:"created_at" json:"created_at" toml:"created_at" yaml:"created_at"`
	UpdatedAt                  time.Time         `boil:"updated_at" json:"updated_at" toml:"updated_at" yaml:"updated_at"`
	PrimaryInsuredName         string            `boil:"primary_insured_name" json:"primary_insured_name" toml:"primary_insured_name" yaml:"primary_insured_name"`
	DefaultEffectiveDate       time.Time         `boil:"default_effective_date" json:"default_effective_date" toml:"default_effective_date" yaml:"default_effective_date"`
	Actions                    null.JSON         `boil:"actions" json:"actions,omitempty" toml:"actions" yaml:"actions,omitempty"`
	PolicyChangeFormsHandleIds null.JSON         `boil:"policy_change_forms_handle_ids" json:"policy_change_forms_handle_ids,omitempty" toml:"policy_change_forms_handle_ids" yaml:"policy_change_forms_handle_ids,omitempty"`
	SupportingDocsHandleIds    types.StringArray `boil:"supporting_docs_handle_ids" json:"supporting_docs_handle_ids,omitempty" toml:"supporting_docs_handle_ids" yaml:"supporting_docs_handle_ids,omitempty"`
	Notes                      null.String       `boil:"notes" json:"notes,omitempty" toml:"notes" yaml:"notes,omitempty"`

	R *reviewR `boil:"" json:"" toml:"" yaml:""`
	L reviewL  `boil:"-" json:"-" toml:"-" yaml:"-"`
}

var ReviewColumns = struct {
	ID                         string
	RequestID                  string
	State                      string
	PricingContextIds          string
	UnderwritingAssistantID    string
	WrittenPremium             string
	ApprovedAt                 string
	CreatedAt                  string
	UpdatedAt                  string
	PrimaryInsuredName         string
	DefaultEffectiveDate       string
	Actions                    string
	PolicyChangeFormsHandleIds string
	SupportingDocsHandleIds    string
	Notes                      string
}{
	ID:                         "id",
	RequestID:                  "request_id",
	State:                      "state",
	PricingContextIds:          "pricing_context_ids",
	UnderwritingAssistantID:    "underwriting_assistant_id",
	WrittenPremium:             "written_premium",
	ApprovedAt:                 "approved_at",
	CreatedAt:                  "created_at",
	UpdatedAt:                  "updated_at",
	PrimaryInsuredName:         "primary_insured_name",
	DefaultEffectiveDate:       "default_effective_date",
	Actions:                    "actions",
	PolicyChangeFormsHandleIds: "policy_change_forms_handle_ids",
	SupportingDocsHandleIds:    "supporting_docs_handle_ids",
	Notes:                      "notes",
}

var ReviewTableColumns = struct {
	ID                         string
	RequestID                  string
	State                      string
	PricingContextIds          string
	UnderwritingAssistantID    string
	WrittenPremium             string
	ApprovedAt                 string
	CreatedAt                  string
	UpdatedAt                  string
	PrimaryInsuredName         string
	DefaultEffectiveDate       string
	Actions                    string
	PolicyChangeFormsHandleIds string
	SupportingDocsHandleIds    string
	Notes                      string
}{
	ID:                         "review.id",
	RequestID:                  "review.request_id",
	State:                      "review.state",
	PricingContextIds:          "review.pricing_context_ids",
	UnderwritingAssistantID:    "review.underwriting_assistant_id",
	WrittenPremium:             "review.written_premium",
	ApprovedAt:                 "review.approved_at",
	CreatedAt:                  "review.created_at",
	UpdatedAt:                  "review.updated_at",
	PrimaryInsuredName:         "review.primary_insured_name",
	DefaultEffectiveDate:       "review.default_effective_date",
	Actions:                    "review.actions",
	PolicyChangeFormsHandleIds: "review.policy_change_forms_handle_ids",
	SupportingDocsHandleIds:    "review.supporting_docs_handle_ids",
	Notes:                      "review.notes",
}

// Generated where

var ReviewWhere = struct {
	ID                         whereHelperstring
	RequestID                  whereHelperstring
	State                      whereHelperstring
	PricingContextIds          whereHelpertypes_StringArray
	UnderwritingAssistantID    whereHelperstring
	WrittenPremium             whereHelpernull_JSON
	ApprovedAt                 whereHelpernull_Time
	CreatedAt                  whereHelpertime_Time
	UpdatedAt                  whereHelpertime_Time
	PrimaryInsuredName         whereHelperstring
	DefaultEffectiveDate       whereHelpertime_Time
	Actions                    whereHelpernull_JSON
	PolicyChangeFormsHandleIds whereHelpernull_JSON
	SupportingDocsHandleIds    whereHelpertypes_StringArray
	Notes                      whereHelpernull_String
}{
	ID:                         whereHelperstring{field: "\"endorsements\".\"review\".\"id\""},
	RequestID:                  whereHelperstring{field: "\"endorsements\".\"review\".\"request_id\""},
	State:                      whereHelperstring{field: "\"endorsements\".\"review\".\"state\""},
	PricingContextIds:          whereHelpertypes_StringArray{field: "\"endorsements\".\"review\".\"pricing_context_ids\""},
	UnderwritingAssistantID:    whereHelperstring{field: "\"endorsements\".\"review\".\"underwriting_assistant_id\""},
	WrittenPremium:             whereHelpernull_JSON{field: "\"endorsements\".\"review\".\"written_premium\""},
	ApprovedAt:                 whereHelpernull_Time{field: "\"endorsements\".\"review\".\"approved_at\""},
	CreatedAt:                  whereHelpertime_Time{field: "\"endorsements\".\"review\".\"created_at\""},
	UpdatedAt:                  whereHelpertime_Time{field: "\"endorsements\".\"review\".\"updated_at\""},
	PrimaryInsuredName:         whereHelperstring{field: "\"endorsements\".\"review\".\"primary_insured_name\""},
	DefaultEffectiveDate:       whereHelpertime_Time{field: "\"endorsements\".\"review\".\"default_effective_date\""},
	Actions:                    whereHelpernull_JSON{field: "\"endorsements\".\"review\".\"actions\""},
	PolicyChangeFormsHandleIds: whereHelpernull_JSON{field: "\"endorsements\".\"review\".\"policy_change_forms_handle_ids\""},
	SupportingDocsHandleIds:    whereHelpertypes_StringArray{field: "\"endorsements\".\"review\".\"supporting_docs_handle_ids\""},
	Notes:                      whereHelpernull_String{field: "\"endorsements\".\"review\".\"notes\""},
}

// ReviewRels is where relationship names are stored.
var ReviewRels = struct {
	Request         string
	ReviewOverrides string
}{
	Request:         "Request",
	ReviewOverrides: "ReviewOverrides",
}

// reviewR is where relationships are stored.
type reviewR struct {
	Request         *Request            `boil:"Request" json:"Request" toml:"Request" yaml:"Request"`
	ReviewOverrides ReviewOverrideSlice `boil:"ReviewOverrides" json:"ReviewOverrides" toml:"ReviewOverrides" yaml:"ReviewOverrides"`
}

// NewStruct creates a new relationship struct
func (*reviewR) NewStruct() *reviewR {
	return &reviewR{}
}

// reviewL is where Load methods for each relationship are stored.
type reviewL struct{}

var (
	reviewAllColumns            = []string{"id", "request_id", "state", "pricing_context_ids", "underwriting_assistant_id", "written_premium", "approved_at", "created_at", "updated_at", "primary_insured_name", "default_effective_date", "actions", "policy_change_forms_handle_ids", "supporting_docs_handle_ids", "notes"}
	reviewColumnsWithoutDefault = []string{"id", "request_id", "state", "underwriting_assistant_id", "primary_insured_name", "default_effective_date"}
	reviewColumnsWithDefault    = []string{"pricing_context_ids", "written_premium", "approved_at", "created_at", "updated_at", "actions", "policy_change_forms_handle_ids", "supporting_docs_handle_ids", "notes"}
	reviewPrimaryKeyColumns     = []string{"id"}
	reviewGeneratedColumns      = []string{}
)

type (
	// ReviewSlice is an alias for a slice of pointers to Review.
	// This should almost always be used instead of []Review.
	ReviewSlice []*Review
	// ReviewHook is the signature for custom Review hook methods
	ReviewHook func(context.Context, boil.ContextExecutor, *Review) error

	reviewQuery struct {
		*queries.Query
	}
)

// Cache for insert, update and upsert
var (
	reviewType                 = reflect.TypeOf(&Review{})
	reviewMapping              = queries.MakeStructMapping(reviewType)
	reviewPrimaryKeyMapping, _ = queries.BindMapping(reviewType, reviewMapping, reviewPrimaryKeyColumns)
	reviewInsertCacheMut       sync.RWMutex
	reviewInsertCache          = make(map[string]insertCache)
	reviewUpdateCacheMut       sync.RWMutex
	reviewUpdateCache          = make(map[string]updateCache)
	reviewUpsertCacheMut       sync.RWMutex
	reviewUpsertCache          = make(map[string]insertCache)
)

var (
	// Force time package dependency for automated UpdatedAt/CreatedAt.
	_ = time.Second
	// Force qmhelper dependency for where clause generation (which doesn't
	// always happen)
	_ = qmhelper.Where
)

var reviewAfterSelectHooks []ReviewHook

var reviewBeforeInsertHooks []ReviewHook
var reviewAfterInsertHooks []ReviewHook

var reviewBeforeUpdateHooks []ReviewHook
var reviewAfterUpdateHooks []ReviewHook

var reviewBeforeDeleteHooks []ReviewHook
var reviewAfterDeleteHooks []ReviewHook

var reviewBeforeUpsertHooks []ReviewHook
var reviewAfterUpsertHooks []ReviewHook

// doAfterSelectHooks executes all "after Select" hooks.
func (o *Review) doAfterSelectHooks(ctx context.Context, exec boil.ContextExecutor) (err error) {
	if boil.HooksAreSkipped(ctx) {
		return nil
	}

	for _, hook := range reviewAfterSelectHooks {
		if err := hook(ctx, exec, o); err != nil {
			return err
		}
	}

	return nil
}

// doBeforeInsertHooks executes all "before insert" hooks.
func (o *Review) doBeforeInsertHooks(ctx context.Context, exec boil.ContextExecutor) (err error) {
	if boil.HooksAreSkipped(ctx) {
		return nil
	}

	for _, hook := range reviewBeforeInsertHooks {
		if err := hook(ctx, exec, o); err != nil {
			return err
		}
	}

	return nil
}

// doAfterInsertHooks executes all "after Insert" hooks.
func (o *Review) doAfterInsertHooks(ctx context.Context, exec boil.ContextExecutor) (err error) {
	if boil.HooksAreSkipped(ctx) {
		return nil
	}

	for _, hook := range reviewAfterInsertHooks {
		if err := hook(ctx, exec, o); err != nil {
			return err
		}
	}

	return nil
}

// doBeforeUpdateHooks executes all "before Update" hooks.
func (o *Review) doBeforeUpdateHooks(ctx context.Context, exec boil.ContextExecutor) (err error) {
	if boil.HooksAreSkipped(ctx) {
		return nil
	}

	for _, hook := range reviewBeforeUpdateHooks {
		if err := hook(ctx, exec, o); err != nil {
			return err
		}
	}

	return nil
}

// doAfterUpdateHooks executes all "after Update" hooks.
func (o *Review) doAfterUpdateHooks(ctx context.Context, exec boil.ContextExecutor) (err error) {
	if boil.HooksAreSkipped(ctx) {
		return nil
	}

	for _, hook := range reviewAfterUpdateHooks {
		if err := hook(ctx, exec, o); err != nil {
			return err
		}
	}

	return nil
}

// doBeforeDeleteHooks executes all "before Delete" hooks.
func (o *Review) doBeforeDeleteHooks(ctx context.Context, exec boil.ContextExecutor) (err error) {
	if boil.HooksAreSkipped(ctx) {
		return nil
	}

	for _, hook := range reviewBeforeDeleteHooks {
		if err := hook(ctx, exec, o); err != nil {
			return err
		}
	}

	return nil
}

// doAfterDeleteHooks executes all "after Delete" hooks.
func (o *Review) doAfterDeleteHooks(ctx context.Context, exec boil.ContextExecutor) (err error) {
	if boil.HooksAreSkipped(ctx) {
		return nil
	}

	for _, hook := range reviewAfterDeleteHooks {
		if err := hook(ctx, exec, o); err != nil {
			return err
		}
	}

	return nil
}

// doBeforeUpsertHooks executes all "before Upsert" hooks.
func (o *Review) doBeforeUpsertHooks(ctx context.Context, exec boil.ContextExecutor) (err error) {
	if boil.HooksAreSkipped(ctx) {
		return nil
	}

	for _, hook := range reviewBeforeUpsertHooks {
		if err := hook(ctx, exec, o); err != nil {
			return err
		}
	}

	return nil
}

// doAfterUpsertHooks executes all "after Upsert" hooks.
func (o *Review) doAfterUpsertHooks(ctx context.Context, exec boil.ContextExecutor) (err error) {
	if boil.HooksAreSkipped(ctx) {
		return nil
	}

	for _, hook := range reviewAfterUpsertHooks {
		if err := hook(ctx, exec, o); err != nil {
			return err
		}
	}

	return nil
}

// AddReviewHook registers your hook function for all future operations.
func AddReviewHook(hookPoint boil.HookPoint, reviewHook ReviewHook) {
	switch hookPoint {
	case boil.AfterSelectHook:
		reviewAfterSelectHooks = append(reviewAfterSelectHooks, reviewHook)
	case boil.BeforeInsertHook:
		reviewBeforeInsertHooks = append(reviewBeforeInsertHooks, reviewHook)
	case boil.AfterInsertHook:
		reviewAfterInsertHooks = append(reviewAfterInsertHooks, reviewHook)
	case boil.BeforeUpdateHook:
		reviewBeforeUpdateHooks = append(reviewBeforeUpdateHooks, reviewHook)
	case boil.AfterUpdateHook:
		reviewAfterUpdateHooks = append(reviewAfterUpdateHooks, reviewHook)
	case boil.BeforeDeleteHook:
		reviewBeforeDeleteHooks = append(reviewBeforeDeleteHooks, reviewHook)
	case boil.AfterDeleteHook:
		reviewAfterDeleteHooks = append(reviewAfterDeleteHooks, reviewHook)
	case boil.BeforeUpsertHook:
		reviewBeforeUpsertHooks = append(reviewBeforeUpsertHooks, reviewHook)
	case boil.AfterUpsertHook:
		reviewAfterUpsertHooks = append(reviewAfterUpsertHooks, reviewHook)
	}
}

// One returns a single review record from the query.
func (q reviewQuery) One(ctx context.Context, exec boil.ContextExecutor) (*Review, error) {
	o := &Review{}

	queries.SetLimit(q.Query, 1)

	err := q.Bind(ctx, exec, o)
	if err != nil {
		if errors.Is(err, sql.ErrNoRows) {
			return nil, sql.ErrNoRows
		}
		return nil, errors.Wrap(err, "endorsementapp: failed to execute a one query for review")
	}

	if err := o.doAfterSelectHooks(ctx, exec); err != nil {
		return o, err
	}

	return o, nil
}

// All returns all Review records from the query.
func (q reviewQuery) All(ctx context.Context, exec boil.ContextExecutor) (ReviewSlice, error) {
	var o []*Review

	err := q.Bind(ctx, exec, &o)
	if err != nil {
		return nil, errors.Wrap(err, "endorsementapp: failed to assign all query results to Review slice")
	}

	if len(reviewAfterSelectHooks) != 0 {
		for _, obj := range o {
			if err := obj.doAfterSelectHooks(ctx, exec); err != nil {
				return o, err
			}
		}
	}

	return o, nil
}

// Count returns the count of all Review records in the query.
func (q reviewQuery) Count(ctx context.Context, exec boil.ContextExecutor) (int64, error) {
	var count int64

	queries.SetSelect(q.Query, nil)
	queries.SetCount(q.Query)

	err := q.Query.QueryRowContext(ctx, exec).Scan(&count)
	if err != nil {
		return 0, errors.Wrap(err, "endorsementapp: failed to count review rows")
	}

	return count, nil
}

// Exists checks if the row exists in the table.
func (q reviewQuery) Exists(ctx context.Context, exec boil.ContextExecutor) (bool, error) {
	var count int64

	queries.SetSelect(q.Query, nil)
	queries.SetCount(q.Query)
	queries.SetLimit(q.Query, 1)

	err := q.Query.QueryRowContext(ctx, exec).Scan(&count)
	if err != nil {
		return false, errors.Wrap(err, "endorsementapp: failed to check if review exists")
	}

	return count > 0, nil
}

// Request pointed to by the foreign key.
func (o *Review) Request(mods ...qm.QueryMod) requestQuery {
	queryMods := []qm.QueryMod{
		qm.Where("\"id\" = ?", o.RequestID),
	}

	queryMods = append(queryMods, mods...)

	return Requests(queryMods...)
}

// ReviewOverrides retrieves all the review_override's ReviewOverrides with an executor.
func (o *Review) ReviewOverrides(mods ...qm.QueryMod) reviewOverrideQuery {
	var queryMods []qm.QueryMod
	if len(mods) != 0 {
		queryMods = append(queryMods, mods...)
	}

	queryMods = append(queryMods,
		qm.Where("\"endorsements\".\"review_override\".\"review_id\"=?", o.ID),
	)

	return ReviewOverrides(queryMods...)
}

// LoadRequest allows an eager lookup of values, cached into the
// loaded structs of the objects. This is for an N-1 relationship.
func (reviewL) LoadRequest(ctx context.Context, e boil.ContextExecutor, singular bool, maybeReview interface{}, mods queries.Applicator) error {
	var slice []*Review
	var object *Review

	if singular {
		object = maybeReview.(*Review)
	} else {
		slice = *maybeReview.(*[]*Review)
	}

	args := make([]interface{}, 0, 1)
	if singular {
		if object.R == nil {
			object.R = &reviewR{}
		}
		args = append(args, object.RequestID)

	} else {
	Outer:
		for _, obj := range slice {
			if obj.R == nil {
				obj.R = &reviewR{}
			}

			for _, a := range args {
				if a == obj.RequestID {
					continue Outer
				}
			}

			args = append(args, obj.RequestID)

		}
	}

	if len(args) == 0 {
		return nil
	}

	query := NewQuery(
		qm.From(`endorsements.request`),
		qm.WhereIn(`endorsements.request.id in ?`, args...),
	)
	if mods != nil {
		mods.Apply(query)
	}

	results, err := query.QueryContext(ctx, e)
	if err != nil {
		return errors.Wrap(err, "failed to eager load Request")
	}

	var resultSlice []*Request
	if err = queries.Bind(results, &resultSlice); err != nil {
		return errors.Wrap(err, "failed to bind eager loaded slice Request")
	}

	if err = results.Close(); err != nil {
		return errors.Wrap(err, "failed to close results of eager load for request")
	}
	if err = results.Err(); err != nil {
		return errors.Wrap(err, "error occurred during iteration of eager loaded relations for request")
	}

	if len(reviewAfterSelectHooks) != 0 {
		for _, obj := range resultSlice {
			if err := obj.doAfterSelectHooks(ctx, e); err != nil {
				return err
			}
		}
	}

	if len(resultSlice) == 0 {
		return nil
	}

	if singular {
		foreign := resultSlice[0]
		object.R.Request = foreign
		if foreign.R == nil {
			foreign.R = &requestR{}
		}
		foreign.R.Reviews = append(foreign.R.Reviews, object)
		return nil
	}

	for _, local := range slice {
		for _, foreign := range resultSlice {
			if local.RequestID == foreign.ID {
				local.R.Request = foreign
				if foreign.R == nil {
					foreign.R = &requestR{}
				}
				foreign.R.Reviews = append(foreign.R.Reviews, local)
				break
			}
		}
	}

	return nil
}

// LoadReviewOverrides allows an eager lookup of values, cached into the
// loaded structs of the objects. This is for a 1-M or N-M relationship.
func (reviewL) LoadReviewOverrides(ctx context.Context, e boil.ContextExecutor, singular bool, maybeReview interface{}, mods queries.Applicator) error {
	var slice []*Review
	var object *Review

	if singular {
		object = maybeReview.(*Review)
	} else {
		slice = *maybeReview.(*[]*Review)
	}

	args := make([]interface{}, 0, 1)
	if singular {
		if object.R == nil {
			object.R = &reviewR{}
		}
		args = append(args, object.ID)
	} else {
	Outer:
		for _, obj := range slice {
			if obj.R == nil {
				obj.R = &reviewR{}
			}

			for _, a := range args {
				if a == obj.ID {
					continue Outer
				}
			}

			args = append(args, obj.ID)
		}
	}

	if len(args) == 0 {
		return nil
	}

	query := NewQuery(
		qm.From(`endorsements.review_override`),
		qm.WhereIn(`endorsements.review_override.review_id in ?`, args...),
	)
	if mods != nil {
		mods.Apply(query)
	}

	results, err := query.QueryContext(ctx, e)
	if err != nil {
		return errors.Wrap(err, "failed to eager load review_override")
	}

	var resultSlice []*ReviewOverride
	if err = queries.Bind(results, &resultSlice); err != nil {
		return errors.Wrap(err, "failed to bind eager loaded slice review_override")
	}

	if err = results.Close(); err != nil {
		return errors.Wrap(err, "failed to close results in eager load on review_override")
	}
	if err = results.Err(); err != nil {
		return errors.Wrap(err, "error occurred during iteration of eager loaded relations for review_override")
	}

	if len(reviewOverrideAfterSelectHooks) != 0 {
		for _, obj := range resultSlice {
			if err := obj.doAfterSelectHooks(ctx, e); err != nil {
				return err
			}
		}
	}
	if singular {
		object.R.ReviewOverrides = resultSlice
		for _, foreign := range resultSlice {
			if foreign.R == nil {
				foreign.R = &reviewOverrideR{}
			}
			foreign.R.Review = object
		}
		return nil
	}

	for _, foreign := range resultSlice {
		for _, local := range slice {
			if local.ID == foreign.ReviewID {
				local.R.ReviewOverrides = append(local.R.ReviewOverrides, foreign)
				if foreign.R == nil {
					foreign.R = &reviewOverrideR{}
				}
				foreign.R.Review = local
				break
			}
		}
	}

	return nil
}

// SetRequest of the review to the related item.
// Sets o.R.Request to related.
// Adds o to related.R.Reviews.
func (o *Review) SetRequest(ctx context.Context, exec boil.ContextExecutor, insert bool, related *Request) error {
	var err error
	if insert {
		if err = related.Insert(ctx, exec, boil.Infer()); err != nil {
			return errors.Wrap(err, "failed to insert into foreign table")
		}
	}

	updateQuery := fmt.Sprintf(
		"UPDATE \"endorsements\".\"review\" SET %s WHERE %s",
		strmangle.SetParamNames("\"", "\"", 1, []string{"request_id"}),
		strmangle.WhereClause("\"", "\"", 2, reviewPrimaryKeyColumns),
	)
	values := []interface{}{related.ID, o.ID}

	if boil.IsDebug(ctx) {
		writer := boil.DebugWriterFrom(ctx)
		fmt.Fprintln(writer, updateQuery)
		fmt.Fprintln(writer, values)
	}
	if _, err = exec.ExecContext(ctx, updateQuery, values...); err != nil {
		return errors.Wrap(err, "failed to update local table")
	}

	o.RequestID = related.ID
	if o.R == nil {
		o.R = &reviewR{
			Request: related,
		}
	} else {
		o.R.Request = related
	}

	if related.R == nil {
		related.R = &requestR{
			Reviews: ReviewSlice{o},
		}
	} else {
		related.R.Reviews = append(related.R.Reviews, o)
	}

	return nil
}

// AddReviewOverrides adds the given related objects to the existing relationships
// of the review, optionally inserting them as new records.
// Appends related to o.R.ReviewOverrides.
// Sets related.R.Review appropriately.
func (o *Review) AddReviewOverrides(ctx context.Context, exec boil.ContextExecutor, insert bool, related ...*ReviewOverride) error {
	var err error
	for _, rel := range related {
		if insert {
			rel.ReviewID = o.ID
			if err = rel.Insert(ctx, exec, boil.Infer()); err != nil {
				return errors.Wrap(err, "failed to insert into foreign table")
			}
		} else {
			updateQuery := fmt.Sprintf(
				"UPDATE \"endorsements\".\"review_override\" SET %s WHERE %s",
				strmangle.SetParamNames("\"", "\"", 1, []string{"review_id"}),
				strmangle.WhereClause("\"", "\"", 2, reviewOverridePrimaryKeyColumns),
			)
			values := []interface{}{o.ID, rel.ID}

			if boil.IsDebug(ctx) {
				writer := boil.DebugWriterFrom(ctx)
				fmt.Fprintln(writer, updateQuery)
				fmt.Fprintln(writer, values)
			}
			if _, err = exec.ExecContext(ctx, updateQuery, values...); err != nil {
				return errors.Wrap(err, "failed to update foreign table")
			}

			rel.ReviewID = o.ID
		}
	}

	if o.R == nil {
		o.R = &reviewR{
			ReviewOverrides: related,
		}
	} else {
		o.R.ReviewOverrides = append(o.R.ReviewOverrides, related...)
	}

	for _, rel := range related {
		if rel.R == nil {
			rel.R = &reviewOverrideR{
				Review: o,
			}
		} else {
			rel.R.Review = o
		}
	}
	return nil
}

// Reviews retrieves all the records using an executor.
func Reviews(mods ...qm.QueryMod) reviewQuery {
	mods = append(mods, qm.From("\"endorsements\".\"review\""))
	q := NewQuery(mods...)
	if len(queries.GetSelect(q)) == 0 {
		queries.SetSelect(q, []string{"\"endorsements\".\"review\".*"})
	}

	return reviewQuery{q}
}

// FindReview retrieves a single record by ID with an executor.
// If selectCols is empty Find will return all columns.
func FindReview(ctx context.Context, exec boil.ContextExecutor, iD string, selectCols ...string) (*Review, error) {
	reviewObj := &Review{}

	sel := "*"
	if len(selectCols) > 0 {
		sel = strings.Join(strmangle.IdentQuoteSlice(dialect.LQ, dialect.RQ, selectCols), ",")
	}
	query := fmt.Sprintf(
		"select %s from \"endorsements\".\"review\" where \"id\"=$1", sel,
	)

	q := queries.Raw(query, iD)

	err := q.Bind(ctx, exec, reviewObj)
	if err != nil {
		if errors.Is(err, sql.ErrNoRows) {
			return nil, sql.ErrNoRows
		}
		return nil, errors.Wrap(err, "endorsementapp: unable to select from review")
	}

	if err = reviewObj.doAfterSelectHooks(ctx, exec); err != nil {
		return reviewObj, err
	}

	return reviewObj, nil
}

// Insert a single record using an executor.
// See boil.Columns.InsertColumnSet documentation to understand column list inference for inserts.
func (o *Review) Insert(ctx context.Context, exec boil.ContextExecutor, columns boil.Columns) error {
	if o == nil {
		return errors.New("endorsementapp: no review provided for insertion")
	}

	var err error
	if !boil.TimestampsAreSkipped(ctx) {
		currTime := time.Now().In(boil.GetLocation())

		if o.CreatedAt.IsZero() {
			o.CreatedAt = currTime
		}
		if o.UpdatedAt.IsZero() {
			o.UpdatedAt = currTime
		}
	}

	if err := o.doBeforeInsertHooks(ctx, exec); err != nil {
		return err
	}

	nzDefaults := queries.NonZeroDefaultSet(reviewColumnsWithDefault, o)

	key := makeCacheKey(columns, nzDefaults)
	reviewInsertCacheMut.RLock()
	cache, cached := reviewInsertCache[key]
	reviewInsertCacheMut.RUnlock()

	if !cached {
		wl, returnColumns := columns.InsertColumnSet(
			reviewAllColumns,
			reviewColumnsWithDefault,
			reviewColumnsWithoutDefault,
			nzDefaults,
		)

		cache.valueMapping, err = queries.BindMapping(reviewType, reviewMapping, wl)
		if err != nil {
			return err
		}
		cache.retMapping, err = queries.BindMapping(reviewType, reviewMapping, returnColumns)
		if err != nil {
			return err
		}
		if len(wl) != 0 {
			cache.query = fmt.Sprintf("INSERT INTO \"endorsements\".\"review\" (\"%s\") %%sVALUES (%s)%%s", strings.Join(wl, "\",\""), strmangle.Placeholders(dialect.UseIndexPlaceholders, len(wl), 1, 1))
		} else {
			cache.query = "INSERT INTO \"endorsements\".\"review\" %sDEFAULT VALUES%s"
		}

		var queryOutput, queryReturning string

		if len(cache.retMapping) != 0 {
			queryReturning = fmt.Sprintf(" RETURNING \"%s\"", strings.Join(returnColumns, "\",\""))
		}

		cache.query = fmt.Sprintf(cache.query, queryOutput, queryReturning)
	}

	value := reflect.Indirect(reflect.ValueOf(o))
	vals := queries.ValuesFromMapping(value, cache.valueMapping)

	if boil.IsDebug(ctx) {
		writer := boil.DebugWriterFrom(ctx)
		fmt.Fprintln(writer, cache.query)
		fmt.Fprintln(writer, vals)
	}

	if len(cache.retMapping) != 0 {
		err = exec.QueryRowContext(ctx, cache.query, vals...).Scan(queries.PtrsFromMapping(value, cache.retMapping)...)
	} else {
		_, err = exec.ExecContext(ctx, cache.query, vals...)
	}

	if err != nil {
		return errors.Wrap(err, "endorsementapp: unable to insert into review")
	}

	if !cached {
		reviewInsertCacheMut.Lock()
		reviewInsertCache[key] = cache
		reviewInsertCacheMut.Unlock()
	}

	return o.doAfterInsertHooks(ctx, exec)
}

// Update uses an executor to update the Review.
// See boil.Columns.UpdateColumnSet documentation to understand column list inference for updates.
// Update does not automatically update the record in case of default values. Use .Reload() to refresh the records.
func (o *Review) Update(ctx context.Context, exec boil.ContextExecutor, columns boil.Columns) (int64, error) {
	if !boil.TimestampsAreSkipped(ctx) {
		currTime := time.Now().In(boil.GetLocation())

		o.UpdatedAt = currTime
	}

	var err error
	if err = o.doBeforeUpdateHooks(ctx, exec); err != nil {
		return 0, err
	}
	key := makeCacheKey(columns, nil)
	reviewUpdateCacheMut.RLock()
	cache, cached := reviewUpdateCache[key]
	reviewUpdateCacheMut.RUnlock()

	if !cached {
		wl := columns.UpdateColumnSet(
			reviewAllColumns,
			reviewPrimaryKeyColumns,
		)

		if !columns.IsWhitelist() {
			wl = strmangle.SetComplement(wl, []string{"created_at"})
		}
		if len(wl) == 0 {
			return 0, errors.New("endorsementapp: unable to update review, could not build whitelist")
		}

		cache.query = fmt.Sprintf("UPDATE \"endorsements\".\"review\" SET %s WHERE %s",
			strmangle.SetParamNames("\"", "\"", 1, wl),
			strmangle.WhereClause("\"", "\"", len(wl)+1, reviewPrimaryKeyColumns),
		)
		cache.valueMapping, err = queries.BindMapping(reviewType, reviewMapping, append(wl, reviewPrimaryKeyColumns...))
		if err != nil {
			return 0, err
		}
	}

	values := queries.ValuesFromMapping(reflect.Indirect(reflect.ValueOf(o)), cache.valueMapping)

	if boil.IsDebug(ctx) {
		writer := boil.DebugWriterFrom(ctx)
		fmt.Fprintln(writer, cache.query)
		fmt.Fprintln(writer, values)
	}
	var result sql.Result
	result, err = exec.ExecContext(ctx, cache.query, values...)
	if err != nil {
		return 0, errors.Wrap(err, "endorsementapp: unable to update review row")
	}

	rowsAff, err := result.RowsAffected()
	if err != nil {
		return 0, errors.Wrap(err, "endorsementapp: failed to get rows affected by update for review")
	}

	if !cached {
		reviewUpdateCacheMut.Lock()
		reviewUpdateCache[key] = cache
		reviewUpdateCacheMut.Unlock()
	}

	return rowsAff, o.doAfterUpdateHooks(ctx, exec)
}

// UpdateAll updates all rows with the specified column values.
func (q reviewQuery) UpdateAll(ctx context.Context, exec boil.ContextExecutor, cols M) (int64, error) {
	queries.SetUpdate(q.Query, cols)

	result, err := q.Query.ExecContext(ctx, exec)
	if err != nil {
		return 0, errors.Wrap(err, "endorsementapp: unable to update all for review")
	}

	rowsAff, err := result.RowsAffected()
	if err != nil {
		return 0, errors.Wrap(err, "endorsementapp: unable to retrieve rows affected for review")
	}

	return rowsAff, nil
}

// UpdateAll updates all rows with the specified column values, using an executor.
func (o ReviewSlice) UpdateAll(ctx context.Context, exec boil.ContextExecutor, cols M) (int64, error) {
	ln := int64(len(o))
	if ln == 0 {
		return 0, nil
	}

	if len(cols) == 0 {
		return 0, errors.New("endorsementapp: update all requires at least one column argument")
	}

	colNames := make([]string, len(cols))
	args := make([]interface{}, len(cols))

	i := 0
	for name, value := range cols {
		colNames[i] = name
		args[i] = value
		i++
	}

	// Append all of the primary key values for each column
	for _, obj := range o {
		pkeyArgs := queries.ValuesFromMapping(reflect.Indirect(reflect.ValueOf(obj)), reviewPrimaryKeyMapping)
		args = append(args, pkeyArgs...)
	}

	sql := fmt.Sprintf("UPDATE \"endorsements\".\"review\" SET %s WHERE %s",
		strmangle.SetParamNames("\"", "\"", 1, colNames),
		strmangle.WhereClauseRepeated(string(dialect.LQ), string(dialect.RQ), len(colNames)+1, reviewPrimaryKeyColumns, len(o)))

	if boil.IsDebug(ctx) {
		writer := boil.DebugWriterFrom(ctx)
		fmt.Fprintln(writer, sql)
		fmt.Fprintln(writer, args...)
	}
	result, err := exec.ExecContext(ctx, sql, args...)
	if err != nil {
		return 0, errors.Wrap(err, "endorsementapp: unable to update all in review slice")
	}

	rowsAff, err := result.RowsAffected()
	if err != nil {
		return 0, errors.Wrap(err, "endorsementapp: unable to retrieve rows affected all in update all review")
	}
	return rowsAff, nil
}

// Upsert attempts an insert using an executor, and does an update or ignore on conflict.
// See boil.Columns documentation for how to properly use updateColumns and insertColumns.
func (o *Review) Upsert(ctx context.Context, exec boil.ContextExecutor, updateOnConflict bool, conflictColumns []string, updateColumns, insertColumns boil.Columns) error {
	if o == nil {
		return errors.New("endorsementapp: no review provided for upsert")
	}
	if !boil.TimestampsAreSkipped(ctx) {
		currTime := time.Now().In(boil.GetLocation())

		if o.CreatedAt.IsZero() {
			o.CreatedAt = currTime
		}
		o.UpdatedAt = currTime
	}

	if err := o.doBeforeUpsertHooks(ctx, exec); err != nil {
		return err
	}

	nzDefaults := queries.NonZeroDefaultSet(reviewColumnsWithDefault, o)

	// Build cache key in-line uglily - mysql vs psql problems
	buf := strmangle.GetBuffer()
	if updateOnConflict {
		buf.WriteByte('t')
	} else {
		buf.WriteByte('f')
	}
	buf.WriteByte('.')
	for _, c := range conflictColumns {
		buf.WriteString(c)
	}
	buf.WriteByte('.')
	buf.WriteString(strconv.Itoa(updateColumns.Kind))
	for _, c := range updateColumns.Cols {
		buf.WriteString(c)
	}
	buf.WriteByte('.')
	buf.WriteString(strconv.Itoa(insertColumns.Kind))
	for _, c := range insertColumns.Cols {
		buf.WriteString(c)
	}
	buf.WriteByte('.')
	for _, c := range nzDefaults {
		buf.WriteString(c)
	}
	key := buf.String()
	strmangle.PutBuffer(buf)

	reviewUpsertCacheMut.RLock()
	cache, cached := reviewUpsertCache[key]
	reviewUpsertCacheMut.RUnlock()

	var err error

	if !cached {
		insert, ret := insertColumns.InsertColumnSet(
			reviewAllColumns,
			reviewColumnsWithDefault,
			reviewColumnsWithoutDefault,
			nzDefaults,
		)

		update := updateColumns.UpdateColumnSet(
			reviewAllColumns,
			reviewPrimaryKeyColumns,
		)

		if updateOnConflict && len(update) == 0 {
			return errors.New("endorsementapp: unable to upsert review, could not build update column list")
		}

		conflict := conflictColumns
		if len(conflict) == 0 {
			conflict = make([]string, len(reviewPrimaryKeyColumns))
			copy(conflict, reviewPrimaryKeyColumns)
		}
		cache.query = buildUpsertQueryPostgres(dialect, "\"endorsements\".\"review\"", updateOnConflict, ret, update, conflict, insert)

		cache.valueMapping, err = queries.BindMapping(reviewType, reviewMapping, insert)
		if err != nil {
			return err
		}
		if len(ret) != 0 {
			cache.retMapping, err = queries.BindMapping(reviewType, reviewMapping, ret)
			if err != nil {
				return err
			}
		}
	}

	value := reflect.Indirect(reflect.ValueOf(o))
	vals := queries.ValuesFromMapping(value, cache.valueMapping)
	var returns []interface{}
	if len(cache.retMapping) != 0 {
		returns = queries.PtrsFromMapping(value, cache.retMapping)
	}

	if boil.IsDebug(ctx) {
		writer := boil.DebugWriterFrom(ctx)
		fmt.Fprintln(writer, cache.query)
		fmt.Fprintln(writer, vals)
	}
	if len(cache.retMapping) != 0 {
		err = exec.QueryRowContext(ctx, cache.query, vals...).Scan(returns...)
		if errors.Is(err, sql.ErrNoRows) {
			err = nil // Postgres doesn't return anything when there's no update
		}
	} else {
		_, err = exec.ExecContext(ctx, cache.query, vals...)
	}
	if err != nil {
		return errors.Wrap(err, "endorsementapp: unable to upsert review")
	}

	if !cached {
		reviewUpsertCacheMut.Lock()
		reviewUpsertCache[key] = cache
		reviewUpsertCacheMut.Unlock()
	}

	return o.doAfterUpsertHooks(ctx, exec)
}

// Delete deletes a single Review record with an executor.
// Delete will match against the primary key column to find the record to delete.
func (o *Review) Delete(ctx context.Context, exec boil.ContextExecutor) (int64, error) {
	if o == nil {
		return 0, errors.New("endorsementapp: no Review provided for delete")
	}

	if err := o.doBeforeDeleteHooks(ctx, exec); err != nil {
		return 0, err
	}

	args := queries.ValuesFromMapping(reflect.Indirect(reflect.ValueOf(o)), reviewPrimaryKeyMapping)
	sql := "DELETE FROM \"endorsements\".\"review\" WHERE \"id\"=$1"

	if boil.IsDebug(ctx) {
		writer := boil.DebugWriterFrom(ctx)
		fmt.Fprintln(writer, sql)
		fmt.Fprintln(writer, args...)
	}
	result, err := exec.ExecContext(ctx, sql, args...)
	if err != nil {
		return 0, errors.Wrap(err, "endorsementapp: unable to delete from review")
	}

	rowsAff, err := result.RowsAffected()
	if err != nil {
		return 0, errors.Wrap(err, "endorsementapp: failed to get rows affected by delete for review")
	}

	if err := o.doAfterDeleteHooks(ctx, exec); err != nil {
		return 0, err
	}

	return rowsAff, nil
}

// DeleteAll deletes all matching rows.
func (q reviewQuery) DeleteAll(ctx context.Context, exec boil.ContextExecutor) (int64, error) {
	if q.Query == nil {
		return 0, errors.New("endorsementapp: no reviewQuery provided for delete all")
	}

	queries.SetDelete(q.Query)

	result, err := q.Query.ExecContext(ctx, exec)
	if err != nil {
		return 0, errors.Wrap(err, "endorsementapp: unable to delete all from review")
	}

	rowsAff, err := result.RowsAffected()
	if err != nil {
		return 0, errors.Wrap(err, "endorsementapp: failed to get rows affected by deleteall for review")
	}

	return rowsAff, nil
}

// DeleteAll deletes all rows in the slice, using an executor.
func (o ReviewSlice) DeleteAll(ctx context.Context, exec boil.ContextExecutor) (int64, error) {
	if len(o) == 0 {
		return 0, nil
	}

	if len(reviewBeforeDeleteHooks) != 0 {
		for _, obj := range o {
			if err := obj.doBeforeDeleteHooks(ctx, exec); err != nil {
				return 0, err
			}
		}
	}

	var args []interface{}
	for _, obj := range o {
		pkeyArgs := queries.ValuesFromMapping(reflect.Indirect(reflect.ValueOf(obj)), reviewPrimaryKeyMapping)
		args = append(args, pkeyArgs...)
	}

	sql := "DELETE FROM \"endorsements\".\"review\" WHERE " +
		strmangle.WhereClauseRepeated(string(dialect.LQ), string(dialect.RQ), 1, reviewPrimaryKeyColumns, len(o))

	if boil.IsDebug(ctx) {
		writer := boil.DebugWriterFrom(ctx)
		fmt.Fprintln(writer, sql)
		fmt.Fprintln(writer, args)
	}
	result, err := exec.ExecContext(ctx, sql, args...)
	if err != nil {
		return 0, errors.Wrap(err, "endorsementapp: unable to delete all from review slice")
	}

	rowsAff, err := result.RowsAffected()
	if err != nil {
		return 0, errors.Wrap(err, "endorsementapp: failed to get rows affected by deleteall for review")
	}

	if len(reviewAfterDeleteHooks) != 0 {
		for _, obj := range o {
			if err := obj.doAfterDeleteHooks(ctx, exec); err != nil {
				return 0, err
			}
		}
	}

	return rowsAff, nil
}

// Reload refetches the object from the database
// using the primary keys with an executor.
func (o *Review) Reload(ctx context.Context, exec boil.ContextExecutor) error {
	ret, err := FindReview(ctx, exec, o.ID)
	if err != nil {
		return err
	}

	*o = *ret
	return nil
}

// ReloadAll refetches every row with matching primary key column values
// and overwrites the original object slice with the newly updated slice.
func (o *ReviewSlice) ReloadAll(ctx context.Context, exec boil.ContextExecutor) error {
	if o == nil || len(*o) == 0 {
		return nil
	}

	slice := ReviewSlice{}
	var args []interface{}
	for _, obj := range *o {
		pkeyArgs := queries.ValuesFromMapping(reflect.Indirect(reflect.ValueOf(obj)), reviewPrimaryKeyMapping)
		args = append(args, pkeyArgs...)
	}

	sql := "SELECT \"endorsements\".\"review\".* FROM \"endorsements\".\"review\" WHERE " +
		strmangle.WhereClauseRepeated(string(dialect.LQ), string(dialect.RQ), 1, reviewPrimaryKeyColumns, len(*o))

	q := queries.Raw(sql, args...)

	err := q.Bind(ctx, exec, &slice)
	if err != nil {
		return errors.Wrap(err, "endorsementapp: unable to reload all in ReviewSlice")
	}

	*o = slice

	return nil
}

// ReviewExists checks if the Review row exists.
func ReviewExists(ctx context.Context, exec boil.ContextExecutor, iD string) (bool, error) {
	var exists bool
	sql := "select exists(select 1 from \"endorsements\".\"review\" where \"id\"=$1 limit 1)"

	if boil.IsDebug(ctx) {
		writer := boil.DebugWriterFrom(ctx)
		fmt.Fprintln(writer, sql)
		fmt.Fprintln(writer, iD)
	}
	row := exec.QueryRowContext(ctx, sql, iD)

	err := row.Scan(&exists)
	if err != nil {
		return false, errors.Wrap(err, "endorsementapp: unable to check if review exists")
	}

	return exists, nil
}
