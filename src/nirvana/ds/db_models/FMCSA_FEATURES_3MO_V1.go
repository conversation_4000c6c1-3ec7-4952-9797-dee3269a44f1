// Code generated by SQLBoiler (https://github.com/volatiletech/sqlboiler). DO NOT EDIT.
// This file is meant to be re-generated in place and/or deleted at any time.

package ds

import (
	"context"
	"database/sql"
	"fmt"
	"reflect"
	"strconv"
	"strings"
	"sync"
	"time"

	"github.com/friendsofgo/errors"
	"github.com/volatiletech/null/v8"
	"github.com/volatiletech/sqlboiler/v4/boil"
	"github.com/volatiletech/sqlboiler/v4/queries"
	"github.com/volatiletech/sqlboiler/v4/queries/qm"
	"github.com/volatiletech/sqlboiler/v4/queries/qmhelper"
	"github.com/volatiletech/sqlboiler/v4/types"
	"github.com/volatiletech/strmangle"
)

// FMCSA_FEATURES_3MO_V1 is an object representing the database table.
type FMCSA_FEATURES_3MO_V1 struct {
	END_DATE                        time.Time         `boil:"END_DATE" json:"END_DATE" toml:"END_DATE" yaml:"END_DATE"`
	HAZMAT_AC                       null.Int64        `boil:"HAZMAT_AC" json:"HAZMAT_AC,omitempty" toml:"HAZMAT_AC" yaml:"HAZMAT_AC,omitempty"`
	DOT_NUMBER                      int64             `boil:"DOT_NUMBER" json:"DOT_NUMBER" toml:"DOT_NUMBER" yaml:"DOT_NUMBER"`
	START_DATE                      time.Time         `boil:"START_DATE" json:"START_DATE" toml:"START_DATE" yaml:"START_DATE"`
	UNIQUE_KEY                      null.String       `boil:"UNIQUE_KEY" json:"UNIQUE_KEY,omitempty" toml:"UNIQUE_KEY" yaml:"UNIQUE_KEY,omitempty"`
	UPDATED_AT                      null.Time         `boil:"UPDATED_AT" json:"UPDATED_AT,omitempty" toml:"UPDATED_AT" yaml:"UPDATED_AT,omitempty"`
	PHY_COUNTRY                     null.String       `boil:"PHY_COUNTRY" json:"PHY_COUNTRY,omitempty" toml:"PHY_COUNTRY" yaml:"PHY_COUNTRY,omitempty"`
	CARRIER_TYPE                    null.String       `boil:"CARRIER_TYPE" json:"CARRIER_TYPE,omitempty" toml:"CARRIER_TYPE" yaml:"CARRIER_TYPE,omitempty"`
	HAZMAT_GROUP                    null.String       `boil:"HAZMAT_GROUP" json:"HAZMAT_GROUP,omitempty" toml:"HAZMAT_GROUP" yaml:"HAZMAT_GROUP,omitempty"`
	HAZMAT_MEASURE                  types.NullDecimal `boil:"HAZMAT_MEASURE" json:"HAZMAT_MEASURE,omitempty" toml:"HAZMAT_MEASURE" yaml:"HAZMAT_MEASURE,omitempty"`
	DRIVER_OOS_RATE                 types.NullDecimal `boil:"DRIVER_OOS_RATE" json:"DRIVER_OOS_RATE,omitempty" toml:"DRIVER_OOS_RATE" yaml:"DRIVER_OOS_RATE,omitempty"`
	PHY_COUNTRY_ENC                 null.Int64        `boil:"PHY_COUNTRY_ENC" json:"PHY_COUNTRY_ENC,omitempty" toml:"PHY_COUNTRY_ENC" yaml:"PHY_COUNTRY_ENC,omitempty"`
	CARRIER_TYPE_ENC                null.Int64        `boil:"CARRIER_TYPE_ENC" json:"CARRIER_TYPE_ENC,omitempty" toml:"CARRIER_TYPE_ENC" yaml:"CARRIER_TYPE_ENC,omitempty"`
	COMPLIANCE_SCORE                types.NullDecimal `boil:"COMPLIANCE_SCORE" json:"COMPLIANCE_SCORE,omitempty" toml:"COMPLIANCE_SCORE" yaml:"COMPLIANCE_SCORE,omitempty"`
	DRIV_FIT_MEASURE                types.NullDecimal `boil:"DRIV_FIT_MEASURE" json:"DRIV_FIT_MEASURE,omitempty" toml:"DRIV_FIT_MEASURE" yaml:"DRIV_FIT_MEASURE,omitempty"`
	HOS_DRIV_MEASURE                types.NullDecimal `boil:"HOS_DRIV_MEASURE" json:"HOS_DRIV_MEASURE,omitempty" toml:"HOS_DRIV_MEASURE" yaml:"HOS_DRIV_MEASURE,omitempty"`
	VEHICLE_OOS_RATE                types.NullDecimal `boil:"VEHICLE_OOS_RATE" json:"VEHICLE_OOS_RATE,omitempty" toml:"VEHICLE_OOS_RATE" yaml:"VEHICLE_OOS_RATE,omitempty"`
	CRASH_IND_MEASURE               types.NullDecimal `boil:"CRASH_IND_MEASURE" json:"CRASH_IND_MEASURE,omitempty" toml:"CRASH_IND_MEASURE" yaml:"CRASH_IND_MEASURE,omitempty"`
	HAZMAT_CONCLUSIVE               null.Int64        `boil:"HAZMAT_CONCLUSIVE" json:"HAZMAT_CONCLUSIVE,omitempty" toml:"HAZMAT_CONCLUSIVE" yaml:"HAZMAT_CONCLUSIVE,omitempty"`
	IS_HAZMAT_CARRIER               null.Int64        `boil:"IS_HAZMAT_CARRIER" json:"IS_HAZMAT_CARRIER,omitempty" toml:"IS_HAZMAT_CARRIER" yaml:"IS_HAZMAT_CARRIER,omitempty"`
	VEH_MAINT_MEASURE               types.NullDecimal `boil:"VEH_MAINT_MEASURE" json:"VEH_MAINT_MEASURE,omitempty" toml:"VEH_MAINT_MEASURE" yaml:"VEH_MAINT_MEASURE,omitempty"`
	VEHICLE_RISK_SCORE              types.NullDecimal `boil:"VEHICLE_RISK_SCORE" json:"VEHICLE_RISK_SCORE,omitempty" toml:"VEHICLE_RISK_SCORE" yaml:"VEHICLE_RISK_SCORE,omitempty"`
	CONTR_SUBST_MEASURE             types.NullDecimal `boil:"CONTR_SUBST_MEASURE" json:"CONTR_SUBST_MEASURE,omitempty" toml:"CONTR_SUBST_MEASURE" yaml:"CONTR_SUBST_MEASURE,omitempty"`
	DRIV_FIT_CONCLUSIVE             null.Int64        `boil:"DRIV_FIT_CONCLUSIVE" json:"DRIV_FIT_CONCLUSIVE,omitempty" toml:"DRIV_FIT_CONCLUSIVE" yaml:"DRIV_FIT_CONCLUSIVE,omitempty"`
	HAZMAT_CARRIER_FLAG             null.Int64        `boil:"HAZMAT_CARRIER_FLAG" json:"HAZMAT_CARRIER_FLAG,omitempty" toml:"HAZMAT_CARRIER_FLAG" yaml:"HAZMAT_CARRIER_FLAG,omitempty"`
	HOS_DRIV_CONCLUSIVE             null.Int64        `boil:"HOS_DRIV_CONCLUSIVE" json:"HOS_DRIV_CONCLUSIVE,omitempty" toml:"HOS_DRIV_CONCLUSIVE" yaml:"HOS_DRIV_CONCLUSIVE,omitempty"`
	CRASH_IND_CONCLUSIVE            null.Int64        `boil:"CRASH_IND_CONCLUSIVE" json:"CRASH_IND_CONCLUSIVE,omitempty" toml:"CRASH_IND_CONCLUSIVE" yaml:"CRASH_IND_CONCLUSIVE,omitempty"`
	INSP_VIOLATION_RATIO            types.NullDecimal `boil:"INSP_VIOLATION_RATIO" json:"INSP_VIOLATION_RATIO,omitempty" toml:"INSP_VIOLATION_RATIO" yaml:"INSP_VIOLATION_RATIO,omitempty"`
	SUBSTANCE_RISK_SCORE            types.NullDecimal `boil:"SUBSTANCE_RISK_SCORE" json:"SUBSTANCE_RISK_SCORE,omitempty" toml:"SUBSTANCE_RISK_SCORE" yaml:"SUBSTANCE_RISK_SCORE,omitempty"`
	VEH_MAINT_CONCLUSIVE            null.Int64        `boil:"VEH_MAINT_CONCLUSIVE" json:"VEH_MAINT_CONCLUSIVE,omitempty" toml:"VEH_MAINT_CONCLUSIVE" yaml:"VEH_MAINT_CONCLUSIVE,omitempty"`
	BEHAVIORAL_RISK_SCORE           types.NullDecimal `boil:"BEHAVIORAL_RISK_SCORE" json:"BEHAVIORAL_RISK_SCORE,omitempty" toml:"BEHAVIORAL_RISK_SCORE" yaml:"BEHAVIORAL_RISK_SCORE,omitempty"`
	CONTR_SUBST_CONCLUSIVE          null.Int64        `boil:"CONTR_SUBST_CONCLUSIVE" json:"CONTR_SUBST_CONCLUSIVE,omitempty" toml:"CONTR_SUBST_CONCLUSIVE" yaml:"CONTR_SUBST_CONCLUSIVE,omitempty"`
	HAZMAT_VIOLATION_RATIO          types.NullDecimal `boil:"HAZMAT_VIOLATION_RATIO" json:"HAZMAT_VIOLATION_RATIO,omitempty" toml:"HAZMAT_VIOLATION_RATIO" yaml:"HAZMAT_VIOLATION_RATIO,omitempty"`
	UNSAFE_DRIV_CONCLUSIVE          null.Int64        `boil:"UNSAFE_DRIV_CONCLUSIVE" json:"UNSAFE_DRIV_CONCLUSIVE,omitempty" toml:"UNSAFE_DRIV_CONCLUSIVE" yaml:"UNSAFE_DRIV_CONCLUSIVE,omitempty"`
	COMPLIANCE_SCORE_3MO_AGO        types.NullDecimal `boil:"COMPLIANCE_SCORE_3MO_AGO" json:"COMPLIANCE_SCORE_3MO_AGO,omitempty" toml:"COMPLIANCE_SCORE_3MO_AGO" yaml:"COMPLIANCE_SCORE_3MO_AGO,omitempty"`
	COMPLIANCE_SCORE_6MO_AGO        types.NullDecimal `boil:"COMPLIANCE_SCORE_6MO_AGO" json:"COMPLIANCE_SCORE_6MO_AGO,omitempty" toml:"COMPLIANCE_SCORE_6MO_AGO" yaml:"COMPLIANCE_SCORE_6MO_AGO,omitempty"`
	COMPLIANCE_SCORE_9MO_AGO        types.NullDecimal `boil:"COMPLIANCE_SCORE_9MO_AGO" json:"COMPLIANCE_SCORE_9MO_AGO,omitempty" toml:"COMPLIANCE_SCORE_9MO_AGO" yaml:"COMPLIANCE_SCORE_9MO_AGO,omitempty"`
	DRIV_FIT_VIOLATION_RATIO        types.NullDecimal `boil:"DRIV_FIT_VIOLATION_RATIO" json:"DRIV_FIT_VIOLATION_RATIO,omitempty" toml:"DRIV_FIT_VIOLATION_RATIO" yaml:"DRIV_FIT_VIOLATION_RATIO,omitempty"`
	HOS_DRIV_VIOLATION_RATIO        types.NullDecimal `boil:"HOS_DRIV_VIOLATION_RATIO" json:"HOS_DRIV_VIOLATION_RATIO,omitempty" toml:"HOS_DRIV_VIOLATION_RATIO" yaml:"HOS_DRIV_VIOLATION_RATIO,omitempty"`
	COMPLIANCE_SCORE_12MO_AGO       types.NullDecimal `boil:"COMPLIANCE_SCORE_12MO_AGO" json:"COMPLIANCE_SCORE_12MO_AGO,omitempty" toml:"COMPLIANCE_SCORE_12MO_AGO" yaml:"COMPLIANCE_SCORE_12MO_AGO,omitempty"`
	VEH_MAINT_VIOLATION_RATIO       types.NullDecimal `boil:"VEH_MAINT_VIOLATION_RATIO" json:"VEH_MAINT_VIOLATION_RATIO,omitempty" toml:"VEH_MAINT_VIOLATION_RATIO" yaml:"VEH_MAINT_VIOLATION_RATIO,omitempty"`
	VIOLATIONS_PER_INSPECTION       types.NullDecimal `boil:"VIOLATIONS_PER_INSPECTION" json:"VIOLATIONS_PER_INSPECTION,omitempty" toml:"VIOLATIONS_PER_INSPECTION" yaml:"VIOLATIONS_PER_INSPECTION,omitempty"`
	CONTR_SUBST_VIOLATION_RATIO     types.NullDecimal `boil:"CONTR_SUBST_VIOLATION_RATIO" json:"CONTR_SUBST_VIOLATION_RATIO,omitempty" toml:"CONTR_SUBST_VIOLATION_RATIO" yaml:"CONTR_SUBST_VIOLATION_RATIO,omitempty"`
	UNSAFE_DRIV_VIOLATION_RATIO     types.NullDecimal `boil:"UNSAFE_DRIV_VIOLATION_RATIO" json:"UNSAFE_DRIV_VIOLATION_RATIO,omitempty" toml:"UNSAFE_DRIV_VIOLATION_RATIO" yaml:"UNSAFE_DRIV_VIOLATION_RATIO,omitempty"`
	COMPLIANCE_SCORE_GROWTH_6TO3MO  types.NullDecimal `boil:"COMPLIANCE_SCORE_GROWTH_6TO3MO" json:"COMPLIANCE_SCORE_GROWTH_6TO3MO,omitempty" toml:"COMPLIANCE_SCORE_GROWTH_6TO3MO" yaml:"COMPLIANCE_SCORE_GROWTH_6TO3MO,omitempty"`
	COMPLIANCE_SCORE_GROWTH_9TO3MO  types.NullDecimal `boil:"COMPLIANCE_SCORE_GROWTH_9TO3MO" json:"COMPLIANCE_SCORE_GROWTH_9TO3MO,omitempty" toml:"COMPLIANCE_SCORE_GROWTH_9TO3MO" yaml:"COMPLIANCE_SCORE_GROWTH_9TO3MO,omitempty"`
	COMPLIANCE_SCORE_GROWTH_12TO3MO types.NullDecimal `boil:"COMPLIANCE_SCORE_GROWTH_12TO3MO" json:"COMPLIANCE_SCORE_GROWTH_12TO3MO,omitempty" toml:"COMPLIANCE_SCORE_GROWTH_12TO3MO" yaml:"COMPLIANCE_SCORE_GROWTH_12TO3MO,omitempty"`

	R *fMCSAFEATURES_3MO_V1R `boil:"" json:"" toml:"" yaml:""`
	L fMCSAFEATURES_3MO_V1L  `boil:"-" json:"-" toml:"-" yaml:"-"`
}

var FMCSA_FEATURES_3MO_V1Columns = struct {
	END_DATE                        string
	HAZMAT_AC                       string
	DOT_NUMBER                      string
	START_DATE                      string
	UNIQUE_KEY                      string
	UPDATED_AT                      string
	PHY_COUNTRY                     string
	CARRIER_TYPE                    string
	HAZMAT_GROUP                    string
	HAZMAT_MEASURE                  string
	DRIVER_OOS_RATE                 string
	PHY_COUNTRY_ENC                 string
	CARRIER_TYPE_ENC                string
	COMPLIANCE_SCORE                string
	DRIV_FIT_MEASURE                string
	HOS_DRIV_MEASURE                string
	VEHICLE_OOS_RATE                string
	CRASH_IND_MEASURE               string
	HAZMAT_CONCLUSIVE               string
	IS_HAZMAT_CARRIER               string
	VEH_MAINT_MEASURE               string
	VEHICLE_RISK_SCORE              string
	CONTR_SUBST_MEASURE             string
	DRIV_FIT_CONCLUSIVE             string
	HAZMAT_CARRIER_FLAG             string
	HOS_DRIV_CONCLUSIVE             string
	CRASH_IND_CONCLUSIVE            string
	INSP_VIOLATION_RATIO            string
	SUBSTANCE_RISK_SCORE            string
	VEH_MAINT_CONCLUSIVE            string
	BEHAVIORAL_RISK_SCORE           string
	CONTR_SUBST_CONCLUSIVE          string
	HAZMAT_VIOLATION_RATIO          string
	UNSAFE_DRIV_CONCLUSIVE          string
	COMPLIANCE_SCORE_3MO_AGO        string
	COMPLIANCE_SCORE_6MO_AGO        string
	COMPLIANCE_SCORE_9MO_AGO        string
	DRIV_FIT_VIOLATION_RATIO        string
	HOS_DRIV_VIOLATION_RATIO        string
	COMPLIANCE_SCORE_12MO_AGO       string
	VEH_MAINT_VIOLATION_RATIO       string
	VIOLATIONS_PER_INSPECTION       string
	CONTR_SUBST_VIOLATION_RATIO     string
	UNSAFE_DRIV_VIOLATION_RATIO     string
	COMPLIANCE_SCORE_GROWTH_6TO3MO  string
	COMPLIANCE_SCORE_GROWTH_9TO3MO  string
	COMPLIANCE_SCORE_GROWTH_12TO3MO string
}{
	END_DATE:                        "END_DATE",
	HAZMAT_AC:                       "HAZMAT_AC",
	DOT_NUMBER:                      "DOT_NUMBER",
	START_DATE:                      "START_DATE",
	UNIQUE_KEY:                      "UNIQUE_KEY",
	UPDATED_AT:                      "UPDATED_AT",
	PHY_COUNTRY:                     "PHY_COUNTRY",
	CARRIER_TYPE:                    "CARRIER_TYPE",
	HAZMAT_GROUP:                    "HAZMAT_GROUP",
	HAZMAT_MEASURE:                  "HAZMAT_MEASURE",
	DRIVER_OOS_RATE:                 "DRIVER_OOS_RATE",
	PHY_COUNTRY_ENC:                 "PHY_COUNTRY_ENC",
	CARRIER_TYPE_ENC:                "CARRIER_TYPE_ENC",
	COMPLIANCE_SCORE:                "COMPLIANCE_SCORE",
	DRIV_FIT_MEASURE:                "DRIV_FIT_MEASURE",
	HOS_DRIV_MEASURE:                "HOS_DRIV_MEASURE",
	VEHICLE_OOS_RATE:                "VEHICLE_OOS_RATE",
	CRASH_IND_MEASURE:               "CRASH_IND_MEASURE",
	HAZMAT_CONCLUSIVE:               "HAZMAT_CONCLUSIVE",
	IS_HAZMAT_CARRIER:               "IS_HAZMAT_CARRIER",
	VEH_MAINT_MEASURE:               "VEH_MAINT_MEASURE",
	VEHICLE_RISK_SCORE:              "VEHICLE_RISK_SCORE",
	CONTR_SUBST_MEASURE:             "CONTR_SUBST_MEASURE",
	DRIV_FIT_CONCLUSIVE:             "DRIV_FIT_CONCLUSIVE",
	HAZMAT_CARRIER_FLAG:             "HAZMAT_CARRIER_FLAG",
	HOS_DRIV_CONCLUSIVE:             "HOS_DRIV_CONCLUSIVE",
	CRASH_IND_CONCLUSIVE:            "CRASH_IND_CONCLUSIVE",
	INSP_VIOLATION_RATIO:            "INSP_VIOLATION_RATIO",
	SUBSTANCE_RISK_SCORE:            "SUBSTANCE_RISK_SCORE",
	VEH_MAINT_CONCLUSIVE:            "VEH_MAINT_CONCLUSIVE",
	BEHAVIORAL_RISK_SCORE:           "BEHAVIORAL_RISK_SCORE",
	CONTR_SUBST_CONCLUSIVE:          "CONTR_SUBST_CONCLUSIVE",
	HAZMAT_VIOLATION_RATIO:          "HAZMAT_VIOLATION_RATIO",
	UNSAFE_DRIV_CONCLUSIVE:          "UNSAFE_DRIV_CONCLUSIVE",
	COMPLIANCE_SCORE_3MO_AGO:        "COMPLIANCE_SCORE_3MO_AGO",
	COMPLIANCE_SCORE_6MO_AGO:        "COMPLIANCE_SCORE_6MO_AGO",
	COMPLIANCE_SCORE_9MO_AGO:        "COMPLIANCE_SCORE_9MO_AGO",
	DRIV_FIT_VIOLATION_RATIO:        "DRIV_FIT_VIOLATION_RATIO",
	HOS_DRIV_VIOLATION_RATIO:        "HOS_DRIV_VIOLATION_RATIO",
	COMPLIANCE_SCORE_12MO_AGO:       "COMPLIANCE_SCORE_12MO_AGO",
	VEH_MAINT_VIOLATION_RATIO:       "VEH_MAINT_VIOLATION_RATIO",
	VIOLATIONS_PER_INSPECTION:       "VIOLATIONS_PER_INSPECTION",
	CONTR_SUBST_VIOLATION_RATIO:     "CONTR_SUBST_VIOLATION_RATIO",
	UNSAFE_DRIV_VIOLATION_RATIO:     "UNSAFE_DRIV_VIOLATION_RATIO",
	COMPLIANCE_SCORE_GROWTH_6TO3MO:  "COMPLIANCE_SCORE_GROWTH_6TO3MO",
	COMPLIANCE_SCORE_GROWTH_9TO3MO:  "COMPLIANCE_SCORE_GROWTH_9TO3MO",
	COMPLIANCE_SCORE_GROWTH_12TO3MO: "COMPLIANCE_SCORE_GROWTH_12TO3MO",
}

var FMCSA_FEATURES_3MO_V1TableColumns = struct {
	END_DATE                        string
	HAZMAT_AC                       string
	DOT_NUMBER                      string
	START_DATE                      string
	UNIQUE_KEY                      string
	UPDATED_AT                      string
	PHY_COUNTRY                     string
	CARRIER_TYPE                    string
	HAZMAT_GROUP                    string
	HAZMAT_MEASURE                  string
	DRIVER_OOS_RATE                 string
	PHY_COUNTRY_ENC                 string
	CARRIER_TYPE_ENC                string
	COMPLIANCE_SCORE                string
	DRIV_FIT_MEASURE                string
	HOS_DRIV_MEASURE                string
	VEHICLE_OOS_RATE                string
	CRASH_IND_MEASURE               string
	HAZMAT_CONCLUSIVE               string
	IS_HAZMAT_CARRIER               string
	VEH_MAINT_MEASURE               string
	VEHICLE_RISK_SCORE              string
	CONTR_SUBST_MEASURE             string
	DRIV_FIT_CONCLUSIVE             string
	HAZMAT_CARRIER_FLAG             string
	HOS_DRIV_CONCLUSIVE             string
	CRASH_IND_CONCLUSIVE            string
	INSP_VIOLATION_RATIO            string
	SUBSTANCE_RISK_SCORE            string
	VEH_MAINT_CONCLUSIVE            string
	BEHAVIORAL_RISK_SCORE           string
	CONTR_SUBST_CONCLUSIVE          string
	HAZMAT_VIOLATION_RATIO          string
	UNSAFE_DRIV_CONCLUSIVE          string
	COMPLIANCE_SCORE_3MO_AGO        string
	COMPLIANCE_SCORE_6MO_AGO        string
	COMPLIANCE_SCORE_9MO_AGO        string
	DRIV_FIT_VIOLATION_RATIO        string
	HOS_DRIV_VIOLATION_RATIO        string
	COMPLIANCE_SCORE_12MO_AGO       string
	VEH_MAINT_VIOLATION_RATIO       string
	VIOLATIONS_PER_INSPECTION       string
	CONTR_SUBST_VIOLATION_RATIO     string
	UNSAFE_DRIV_VIOLATION_RATIO     string
	COMPLIANCE_SCORE_GROWTH_6TO3MO  string
	COMPLIANCE_SCORE_GROWTH_9TO3MO  string
	COMPLIANCE_SCORE_GROWTH_12TO3MO string
}{
	END_DATE:                        "FMCSA_FEATURES_3MO_V1.END_DATE",
	HAZMAT_AC:                       "FMCSA_FEATURES_3MO_V1.HAZMAT_AC",
	DOT_NUMBER:                      "FMCSA_FEATURES_3MO_V1.DOT_NUMBER",
	START_DATE:                      "FMCSA_FEATURES_3MO_V1.START_DATE",
	UNIQUE_KEY:                      "FMCSA_FEATURES_3MO_V1.UNIQUE_KEY",
	UPDATED_AT:                      "FMCSA_FEATURES_3MO_V1.UPDATED_AT",
	PHY_COUNTRY:                     "FMCSA_FEATURES_3MO_V1.PHY_COUNTRY",
	CARRIER_TYPE:                    "FMCSA_FEATURES_3MO_V1.CARRIER_TYPE",
	HAZMAT_GROUP:                    "FMCSA_FEATURES_3MO_V1.HAZMAT_GROUP",
	HAZMAT_MEASURE:                  "FMCSA_FEATURES_3MO_V1.HAZMAT_MEASURE",
	DRIVER_OOS_RATE:                 "FMCSA_FEATURES_3MO_V1.DRIVER_OOS_RATE",
	PHY_COUNTRY_ENC:                 "FMCSA_FEATURES_3MO_V1.PHY_COUNTRY_ENC",
	CARRIER_TYPE_ENC:                "FMCSA_FEATURES_3MO_V1.CARRIER_TYPE_ENC",
	COMPLIANCE_SCORE:                "FMCSA_FEATURES_3MO_V1.COMPLIANCE_SCORE",
	DRIV_FIT_MEASURE:                "FMCSA_FEATURES_3MO_V1.DRIV_FIT_MEASURE",
	HOS_DRIV_MEASURE:                "FMCSA_FEATURES_3MO_V1.HOS_DRIV_MEASURE",
	VEHICLE_OOS_RATE:                "FMCSA_FEATURES_3MO_V1.VEHICLE_OOS_RATE",
	CRASH_IND_MEASURE:               "FMCSA_FEATURES_3MO_V1.CRASH_IND_MEASURE",
	HAZMAT_CONCLUSIVE:               "FMCSA_FEATURES_3MO_V1.HAZMAT_CONCLUSIVE",
	IS_HAZMAT_CARRIER:               "FMCSA_FEATURES_3MO_V1.IS_HAZMAT_CARRIER",
	VEH_MAINT_MEASURE:               "FMCSA_FEATURES_3MO_V1.VEH_MAINT_MEASURE",
	VEHICLE_RISK_SCORE:              "FMCSA_FEATURES_3MO_V1.VEHICLE_RISK_SCORE",
	CONTR_SUBST_MEASURE:             "FMCSA_FEATURES_3MO_V1.CONTR_SUBST_MEASURE",
	DRIV_FIT_CONCLUSIVE:             "FMCSA_FEATURES_3MO_V1.DRIV_FIT_CONCLUSIVE",
	HAZMAT_CARRIER_FLAG:             "FMCSA_FEATURES_3MO_V1.HAZMAT_CARRIER_FLAG",
	HOS_DRIV_CONCLUSIVE:             "FMCSA_FEATURES_3MO_V1.HOS_DRIV_CONCLUSIVE",
	CRASH_IND_CONCLUSIVE:            "FMCSA_FEATURES_3MO_V1.CRASH_IND_CONCLUSIVE",
	INSP_VIOLATION_RATIO:            "FMCSA_FEATURES_3MO_V1.INSP_VIOLATION_RATIO",
	SUBSTANCE_RISK_SCORE:            "FMCSA_FEATURES_3MO_V1.SUBSTANCE_RISK_SCORE",
	VEH_MAINT_CONCLUSIVE:            "FMCSA_FEATURES_3MO_V1.VEH_MAINT_CONCLUSIVE",
	BEHAVIORAL_RISK_SCORE:           "FMCSA_FEATURES_3MO_V1.BEHAVIORAL_RISK_SCORE",
	CONTR_SUBST_CONCLUSIVE:          "FMCSA_FEATURES_3MO_V1.CONTR_SUBST_CONCLUSIVE",
	HAZMAT_VIOLATION_RATIO:          "FMCSA_FEATURES_3MO_V1.HAZMAT_VIOLATION_RATIO",
	UNSAFE_DRIV_CONCLUSIVE:          "FMCSA_FEATURES_3MO_V1.UNSAFE_DRIV_CONCLUSIVE",
	COMPLIANCE_SCORE_3MO_AGO:        "FMCSA_FEATURES_3MO_V1.COMPLIANCE_SCORE_3MO_AGO",
	COMPLIANCE_SCORE_6MO_AGO:        "FMCSA_FEATURES_3MO_V1.COMPLIANCE_SCORE_6MO_AGO",
	COMPLIANCE_SCORE_9MO_AGO:        "FMCSA_FEATURES_3MO_V1.COMPLIANCE_SCORE_9MO_AGO",
	DRIV_FIT_VIOLATION_RATIO:        "FMCSA_FEATURES_3MO_V1.DRIV_FIT_VIOLATION_RATIO",
	HOS_DRIV_VIOLATION_RATIO:        "FMCSA_FEATURES_3MO_V1.HOS_DRIV_VIOLATION_RATIO",
	COMPLIANCE_SCORE_12MO_AGO:       "FMCSA_FEATURES_3MO_V1.COMPLIANCE_SCORE_12MO_AGO",
	VEH_MAINT_VIOLATION_RATIO:       "FMCSA_FEATURES_3MO_V1.VEH_MAINT_VIOLATION_RATIO",
	VIOLATIONS_PER_INSPECTION:       "FMCSA_FEATURES_3MO_V1.VIOLATIONS_PER_INSPECTION",
	CONTR_SUBST_VIOLATION_RATIO:     "FMCSA_FEATURES_3MO_V1.CONTR_SUBST_VIOLATION_RATIO",
	UNSAFE_DRIV_VIOLATION_RATIO:     "FMCSA_FEATURES_3MO_V1.UNSAFE_DRIV_VIOLATION_RATIO",
	COMPLIANCE_SCORE_GROWTH_6TO3MO:  "FMCSA_FEATURES_3MO_V1.COMPLIANCE_SCORE_GROWTH_6TO3MO",
	COMPLIANCE_SCORE_GROWTH_9TO3MO:  "FMCSA_FEATURES_3MO_V1.COMPLIANCE_SCORE_GROWTH_9TO3MO",
	COMPLIANCE_SCORE_GROWTH_12TO3MO: "FMCSA_FEATURES_3MO_V1.COMPLIANCE_SCORE_GROWTH_12TO3MO",
}

// Generated where

var FMCSA_FEATURES_3MO_V1Where = struct {
	END_DATE                        whereHelpertime_Time
	HAZMAT_AC                       whereHelpernull_Int64
	DOT_NUMBER                      whereHelperint64
	START_DATE                      whereHelpertime_Time
	UNIQUE_KEY                      whereHelpernull_String
	UPDATED_AT                      whereHelpernull_Time
	PHY_COUNTRY                     whereHelpernull_String
	CARRIER_TYPE                    whereHelpernull_String
	HAZMAT_GROUP                    whereHelpernull_String
	HAZMAT_MEASURE                  whereHelpertypes_NullDecimal
	DRIVER_OOS_RATE                 whereHelpertypes_NullDecimal
	PHY_COUNTRY_ENC                 whereHelpernull_Int64
	CARRIER_TYPE_ENC                whereHelpernull_Int64
	COMPLIANCE_SCORE                whereHelpertypes_NullDecimal
	DRIV_FIT_MEASURE                whereHelpertypes_NullDecimal
	HOS_DRIV_MEASURE                whereHelpertypes_NullDecimal
	VEHICLE_OOS_RATE                whereHelpertypes_NullDecimal
	CRASH_IND_MEASURE               whereHelpertypes_NullDecimal
	HAZMAT_CONCLUSIVE               whereHelpernull_Int64
	IS_HAZMAT_CARRIER               whereHelpernull_Int64
	VEH_MAINT_MEASURE               whereHelpertypes_NullDecimal
	VEHICLE_RISK_SCORE              whereHelpertypes_NullDecimal
	CONTR_SUBST_MEASURE             whereHelpertypes_NullDecimal
	DRIV_FIT_CONCLUSIVE             whereHelpernull_Int64
	HAZMAT_CARRIER_FLAG             whereHelpernull_Int64
	HOS_DRIV_CONCLUSIVE             whereHelpernull_Int64
	CRASH_IND_CONCLUSIVE            whereHelpernull_Int64
	INSP_VIOLATION_RATIO            whereHelpertypes_NullDecimal
	SUBSTANCE_RISK_SCORE            whereHelpertypes_NullDecimal
	VEH_MAINT_CONCLUSIVE            whereHelpernull_Int64
	BEHAVIORAL_RISK_SCORE           whereHelpertypes_NullDecimal
	CONTR_SUBST_CONCLUSIVE          whereHelpernull_Int64
	HAZMAT_VIOLATION_RATIO          whereHelpertypes_NullDecimal
	UNSAFE_DRIV_CONCLUSIVE          whereHelpernull_Int64
	COMPLIANCE_SCORE_3MO_AGO        whereHelpertypes_NullDecimal
	COMPLIANCE_SCORE_6MO_AGO        whereHelpertypes_NullDecimal
	COMPLIANCE_SCORE_9MO_AGO        whereHelpertypes_NullDecimal
	DRIV_FIT_VIOLATION_RATIO        whereHelpertypes_NullDecimal
	HOS_DRIV_VIOLATION_RATIO        whereHelpertypes_NullDecimal
	COMPLIANCE_SCORE_12MO_AGO       whereHelpertypes_NullDecimal
	VEH_MAINT_VIOLATION_RATIO       whereHelpertypes_NullDecimal
	VIOLATIONS_PER_INSPECTION       whereHelpertypes_NullDecimal
	CONTR_SUBST_VIOLATION_RATIO     whereHelpertypes_NullDecimal
	UNSAFE_DRIV_VIOLATION_RATIO     whereHelpertypes_NullDecimal
	COMPLIANCE_SCORE_GROWTH_6TO3MO  whereHelpertypes_NullDecimal
	COMPLIANCE_SCORE_GROWTH_9TO3MO  whereHelpertypes_NullDecimal
	COMPLIANCE_SCORE_GROWTH_12TO3MO whereHelpertypes_NullDecimal
}{
	END_DATE:                        whereHelpertime_Time{field: "\"FMCSA_FEATURES_3MO_V1\".\"END_DATE\""},
	HAZMAT_AC:                       whereHelpernull_Int64{field: "\"FMCSA_FEATURES_3MO_V1\".\"HAZMAT_AC\""},
	DOT_NUMBER:                      whereHelperint64{field: "\"FMCSA_FEATURES_3MO_V1\".\"DOT_NUMBER\""},
	START_DATE:                      whereHelpertime_Time{field: "\"FMCSA_FEATURES_3MO_V1\".\"START_DATE\""},
	UNIQUE_KEY:                      whereHelpernull_String{field: "\"FMCSA_FEATURES_3MO_V1\".\"UNIQUE_KEY\""},
	UPDATED_AT:                      whereHelpernull_Time{field: "\"FMCSA_FEATURES_3MO_V1\".\"UPDATED_AT\""},
	PHY_COUNTRY:                     whereHelpernull_String{field: "\"FMCSA_FEATURES_3MO_V1\".\"PHY_COUNTRY\""},
	CARRIER_TYPE:                    whereHelpernull_String{field: "\"FMCSA_FEATURES_3MO_V1\".\"CARRIER_TYPE\""},
	HAZMAT_GROUP:                    whereHelpernull_String{field: "\"FMCSA_FEATURES_3MO_V1\".\"HAZMAT_GROUP\""},
	HAZMAT_MEASURE:                  whereHelpertypes_NullDecimal{field: "\"FMCSA_FEATURES_3MO_V1\".\"HAZMAT_MEASURE\""},
	DRIVER_OOS_RATE:                 whereHelpertypes_NullDecimal{field: "\"FMCSA_FEATURES_3MO_V1\".\"DRIVER_OOS_RATE\""},
	PHY_COUNTRY_ENC:                 whereHelpernull_Int64{field: "\"FMCSA_FEATURES_3MO_V1\".\"PHY_COUNTRY_ENC\""},
	CARRIER_TYPE_ENC:                whereHelpernull_Int64{field: "\"FMCSA_FEATURES_3MO_V1\".\"CARRIER_TYPE_ENC\""},
	COMPLIANCE_SCORE:                whereHelpertypes_NullDecimal{field: "\"FMCSA_FEATURES_3MO_V1\".\"COMPLIANCE_SCORE\""},
	DRIV_FIT_MEASURE:                whereHelpertypes_NullDecimal{field: "\"FMCSA_FEATURES_3MO_V1\".\"DRIV_FIT_MEASURE\""},
	HOS_DRIV_MEASURE:                whereHelpertypes_NullDecimal{field: "\"FMCSA_FEATURES_3MO_V1\".\"HOS_DRIV_MEASURE\""},
	VEHICLE_OOS_RATE:                whereHelpertypes_NullDecimal{field: "\"FMCSA_FEATURES_3MO_V1\".\"VEHICLE_OOS_RATE\""},
	CRASH_IND_MEASURE:               whereHelpertypes_NullDecimal{field: "\"FMCSA_FEATURES_3MO_V1\".\"CRASH_IND_MEASURE\""},
	HAZMAT_CONCLUSIVE:               whereHelpernull_Int64{field: "\"FMCSA_FEATURES_3MO_V1\".\"HAZMAT_CONCLUSIVE\""},
	IS_HAZMAT_CARRIER:               whereHelpernull_Int64{field: "\"FMCSA_FEATURES_3MO_V1\".\"IS_HAZMAT_CARRIER\""},
	VEH_MAINT_MEASURE:               whereHelpertypes_NullDecimal{field: "\"FMCSA_FEATURES_3MO_V1\".\"VEH_MAINT_MEASURE\""},
	VEHICLE_RISK_SCORE:              whereHelpertypes_NullDecimal{field: "\"FMCSA_FEATURES_3MO_V1\".\"VEHICLE_RISK_SCORE\""},
	CONTR_SUBST_MEASURE:             whereHelpertypes_NullDecimal{field: "\"FMCSA_FEATURES_3MO_V1\".\"CONTR_SUBST_MEASURE\""},
	DRIV_FIT_CONCLUSIVE:             whereHelpernull_Int64{field: "\"FMCSA_FEATURES_3MO_V1\".\"DRIV_FIT_CONCLUSIVE\""},
	HAZMAT_CARRIER_FLAG:             whereHelpernull_Int64{field: "\"FMCSA_FEATURES_3MO_V1\".\"HAZMAT_CARRIER_FLAG\""},
	HOS_DRIV_CONCLUSIVE:             whereHelpernull_Int64{field: "\"FMCSA_FEATURES_3MO_V1\".\"HOS_DRIV_CONCLUSIVE\""},
	CRASH_IND_CONCLUSIVE:            whereHelpernull_Int64{field: "\"FMCSA_FEATURES_3MO_V1\".\"CRASH_IND_CONCLUSIVE\""},
	INSP_VIOLATION_RATIO:            whereHelpertypes_NullDecimal{field: "\"FMCSA_FEATURES_3MO_V1\".\"INSP_VIOLATION_RATIO\""},
	SUBSTANCE_RISK_SCORE:            whereHelpertypes_NullDecimal{field: "\"FMCSA_FEATURES_3MO_V1\".\"SUBSTANCE_RISK_SCORE\""},
	VEH_MAINT_CONCLUSIVE:            whereHelpernull_Int64{field: "\"FMCSA_FEATURES_3MO_V1\".\"VEH_MAINT_CONCLUSIVE\""},
	BEHAVIORAL_RISK_SCORE:           whereHelpertypes_NullDecimal{field: "\"FMCSA_FEATURES_3MO_V1\".\"BEHAVIORAL_RISK_SCORE\""},
	CONTR_SUBST_CONCLUSIVE:          whereHelpernull_Int64{field: "\"FMCSA_FEATURES_3MO_V1\".\"CONTR_SUBST_CONCLUSIVE\""},
	HAZMAT_VIOLATION_RATIO:          whereHelpertypes_NullDecimal{field: "\"FMCSA_FEATURES_3MO_V1\".\"HAZMAT_VIOLATION_RATIO\""},
	UNSAFE_DRIV_CONCLUSIVE:          whereHelpernull_Int64{field: "\"FMCSA_FEATURES_3MO_V1\".\"UNSAFE_DRIV_CONCLUSIVE\""},
	COMPLIANCE_SCORE_3MO_AGO:        whereHelpertypes_NullDecimal{field: "\"FMCSA_FEATURES_3MO_V1\".\"COMPLIANCE_SCORE_3MO_AGO\""},
	COMPLIANCE_SCORE_6MO_AGO:        whereHelpertypes_NullDecimal{field: "\"FMCSA_FEATURES_3MO_V1\".\"COMPLIANCE_SCORE_6MO_AGO\""},
	COMPLIANCE_SCORE_9MO_AGO:        whereHelpertypes_NullDecimal{field: "\"FMCSA_FEATURES_3MO_V1\".\"COMPLIANCE_SCORE_9MO_AGO\""},
	DRIV_FIT_VIOLATION_RATIO:        whereHelpertypes_NullDecimal{field: "\"FMCSA_FEATURES_3MO_V1\".\"DRIV_FIT_VIOLATION_RATIO\""},
	HOS_DRIV_VIOLATION_RATIO:        whereHelpertypes_NullDecimal{field: "\"FMCSA_FEATURES_3MO_V1\".\"HOS_DRIV_VIOLATION_RATIO\""},
	COMPLIANCE_SCORE_12MO_AGO:       whereHelpertypes_NullDecimal{field: "\"FMCSA_FEATURES_3MO_V1\".\"COMPLIANCE_SCORE_12MO_AGO\""},
	VEH_MAINT_VIOLATION_RATIO:       whereHelpertypes_NullDecimal{field: "\"FMCSA_FEATURES_3MO_V1\".\"VEH_MAINT_VIOLATION_RATIO\""},
	VIOLATIONS_PER_INSPECTION:       whereHelpertypes_NullDecimal{field: "\"FMCSA_FEATURES_3MO_V1\".\"VIOLATIONS_PER_INSPECTION\""},
	CONTR_SUBST_VIOLATION_RATIO:     whereHelpertypes_NullDecimal{field: "\"FMCSA_FEATURES_3MO_V1\".\"CONTR_SUBST_VIOLATION_RATIO\""},
	UNSAFE_DRIV_VIOLATION_RATIO:     whereHelpertypes_NullDecimal{field: "\"FMCSA_FEATURES_3MO_V1\".\"UNSAFE_DRIV_VIOLATION_RATIO\""},
	COMPLIANCE_SCORE_GROWTH_6TO3MO:  whereHelpertypes_NullDecimal{field: "\"FMCSA_FEATURES_3MO_V1\".\"COMPLIANCE_SCORE_GROWTH_6TO3MO\""},
	COMPLIANCE_SCORE_GROWTH_9TO3MO:  whereHelpertypes_NullDecimal{field: "\"FMCSA_FEATURES_3MO_V1\".\"COMPLIANCE_SCORE_GROWTH_9TO3MO\""},
	COMPLIANCE_SCORE_GROWTH_12TO3MO: whereHelpertypes_NullDecimal{field: "\"FMCSA_FEATURES_3MO_V1\".\"COMPLIANCE_SCORE_GROWTH_12TO3MO\""},
}

// FMCSA_FEATURES_3MO_V1Rels is where relationship names are stored.
var FMCSA_FEATURES_3MO_V1Rels = struct {
}{}

// fMCSAFEATURES_3MO_V1R is where relationships are stored.
type fMCSAFEATURES_3MO_V1R struct {
}

// NewStruct creates a new relationship struct
func (*fMCSAFEATURES_3MO_V1R) NewStruct() *fMCSAFEATURES_3MO_V1R {
	return &fMCSAFEATURES_3MO_V1R{}
}

// fMCSAFEATURES_3MO_V1L is where Load methods for each relationship are stored.
type fMCSAFEATURES_3MO_V1L struct{}

var (
	fMCSAFEATURES_3MO_V1AllColumns            = []string{"END_DATE", "HAZMAT_AC", "DOT_NUMBER", "START_DATE", "UNIQUE_KEY", "UPDATED_AT", "PHY_COUNTRY", "CARRIER_TYPE", "HAZMAT_GROUP", "HAZMAT_MEASURE", "DRIVER_OOS_RATE", "PHY_COUNTRY_ENC", "CARRIER_TYPE_ENC", "COMPLIANCE_SCORE", "DRIV_FIT_MEASURE", "HOS_DRIV_MEASURE", "VEHICLE_OOS_RATE", "CRASH_IND_MEASURE", "HAZMAT_CONCLUSIVE", "IS_HAZMAT_CARRIER", "VEH_MAINT_MEASURE", "VEHICLE_RISK_SCORE", "CONTR_SUBST_MEASURE", "DRIV_FIT_CONCLUSIVE", "HAZMAT_CARRIER_FLAG", "HOS_DRIV_CONCLUSIVE", "CRASH_IND_CONCLUSIVE", "INSP_VIOLATION_RATIO", "SUBSTANCE_RISK_SCORE", "VEH_MAINT_CONCLUSIVE", "BEHAVIORAL_RISK_SCORE", "CONTR_SUBST_CONCLUSIVE", "HAZMAT_VIOLATION_RATIO", "UNSAFE_DRIV_CONCLUSIVE", "COMPLIANCE_SCORE_3MO_AGO", "COMPLIANCE_SCORE_6MO_AGO", "COMPLIANCE_SCORE_9MO_AGO", "DRIV_FIT_VIOLATION_RATIO", "HOS_DRIV_VIOLATION_RATIO", "COMPLIANCE_SCORE_12MO_AGO", "VEH_MAINT_VIOLATION_RATIO", "VIOLATIONS_PER_INSPECTION", "CONTR_SUBST_VIOLATION_RATIO", "UNSAFE_DRIV_VIOLATION_RATIO", "COMPLIANCE_SCORE_GROWTH_6TO3MO", "COMPLIANCE_SCORE_GROWTH_9TO3MO", "COMPLIANCE_SCORE_GROWTH_12TO3MO"}
	fMCSAFEATURES_3MO_V1ColumnsWithoutDefault = []string{"END_DATE", "DOT_NUMBER", "START_DATE"}
	fMCSAFEATURES_3MO_V1ColumnsWithDefault    = []string{"HAZMAT_AC", "UNIQUE_KEY", "UPDATED_AT", "PHY_COUNTRY", "CARRIER_TYPE", "HAZMAT_GROUP", "HAZMAT_MEASURE", "DRIVER_OOS_RATE", "PHY_COUNTRY_ENC", "CARRIER_TYPE_ENC", "COMPLIANCE_SCORE", "DRIV_FIT_MEASURE", "HOS_DRIV_MEASURE", "VEHICLE_OOS_RATE", "CRASH_IND_MEASURE", "HAZMAT_CONCLUSIVE", "IS_HAZMAT_CARRIER", "VEH_MAINT_MEASURE", "VEHICLE_RISK_SCORE", "CONTR_SUBST_MEASURE", "DRIV_FIT_CONCLUSIVE", "HAZMAT_CARRIER_FLAG", "HOS_DRIV_CONCLUSIVE", "CRASH_IND_CONCLUSIVE", "INSP_VIOLATION_RATIO", "SUBSTANCE_RISK_SCORE", "VEH_MAINT_CONCLUSIVE", "BEHAVIORAL_RISK_SCORE", "CONTR_SUBST_CONCLUSIVE", "HAZMAT_VIOLATION_RATIO", "UNSAFE_DRIV_CONCLUSIVE", "COMPLIANCE_SCORE_3MO_AGO", "COMPLIANCE_SCORE_6MO_AGO", "COMPLIANCE_SCORE_9MO_AGO", "DRIV_FIT_VIOLATION_RATIO", "HOS_DRIV_VIOLATION_RATIO", "COMPLIANCE_SCORE_12MO_AGO", "VEH_MAINT_VIOLATION_RATIO", "VIOLATIONS_PER_INSPECTION", "CONTR_SUBST_VIOLATION_RATIO", "UNSAFE_DRIV_VIOLATION_RATIO", "COMPLIANCE_SCORE_GROWTH_6TO3MO", "COMPLIANCE_SCORE_GROWTH_9TO3MO", "COMPLIANCE_SCORE_GROWTH_12TO3MO"}
	fMCSAFEATURES_3MO_V1PrimaryKeyColumns     = []string{"DOT_NUMBER", "START_DATE", "END_DATE"}
	fMCSAFEATURES_3MO_V1GeneratedColumns      = []string{}
)

type (
	// FMCSA_FEATURES_3MO_V1Slice is an alias for a slice of pointers to FMCSA_FEATURES_3MO_V1.
	// This should almost always be used instead of []FMCSA_FEATURES_3MO_V1.
	FMCSA_FEATURES_3MO_V1Slice []*FMCSA_FEATURES_3MO_V1
	// FMCSA_FEATURES_3MO_V1Hook is the signature for custom FMCSA_FEATURES_3MO_V1 hook methods
	FMCSA_FEATURES_3MO_V1Hook func(context.Context, boil.ContextExecutor, *FMCSA_FEATURES_3MO_V1) error

	fMCSAFEATURES_3MO_V1Query struct {
		*queries.Query
	}
)

// Cache for insert, update and upsert
var (
	fMCSAFEATURES_3MO_V1Type                 = reflect.TypeOf(&FMCSA_FEATURES_3MO_V1{})
	fMCSAFEATURES_3MO_V1Mapping              = queries.MakeStructMapping(fMCSAFEATURES_3MO_V1Type)
	fMCSAFEATURES_3MO_V1PrimaryKeyMapping, _ = queries.BindMapping(fMCSAFEATURES_3MO_V1Type, fMCSAFEATURES_3MO_V1Mapping, fMCSAFEATURES_3MO_V1PrimaryKeyColumns)
	fMCSAFEATURES_3MO_V1InsertCacheMut       sync.RWMutex
	fMCSAFEATURES_3MO_V1InsertCache          = make(map[string]insertCache)
	fMCSAFEATURES_3MO_V1UpdateCacheMut       sync.RWMutex
	fMCSAFEATURES_3MO_V1UpdateCache          = make(map[string]updateCache)
	fMCSAFEATURES_3MO_V1UpsertCacheMut       sync.RWMutex
	fMCSAFEATURES_3MO_V1UpsertCache          = make(map[string]insertCache)
)

var (
	// Force time package dependency for automated UpdatedAt/CreatedAt.
	_ = time.Second
	// Force qmhelper dependency for where clause generation (which doesn't
	// always happen)
	_ = qmhelper.Where
)

var fMCSAFEATURES_3MO_V1AfterSelectHooks []FMCSA_FEATURES_3MO_V1Hook

var fMCSAFEATURES_3MO_V1BeforeInsertHooks []FMCSA_FEATURES_3MO_V1Hook
var fMCSAFEATURES_3MO_V1AfterInsertHooks []FMCSA_FEATURES_3MO_V1Hook

var fMCSAFEATURES_3MO_V1BeforeUpdateHooks []FMCSA_FEATURES_3MO_V1Hook
var fMCSAFEATURES_3MO_V1AfterUpdateHooks []FMCSA_FEATURES_3MO_V1Hook

var fMCSAFEATURES_3MO_V1BeforeDeleteHooks []FMCSA_FEATURES_3MO_V1Hook
var fMCSAFEATURES_3MO_V1AfterDeleteHooks []FMCSA_FEATURES_3MO_V1Hook

var fMCSAFEATURES_3MO_V1BeforeUpsertHooks []FMCSA_FEATURES_3MO_V1Hook
var fMCSAFEATURES_3MO_V1AfterUpsertHooks []FMCSA_FEATURES_3MO_V1Hook

// doAfterSelectHooks executes all "after Select" hooks.
func (o *FMCSA_FEATURES_3MO_V1) doAfterSelectHooks(ctx context.Context, exec boil.ContextExecutor) (err error) {
	if boil.HooksAreSkipped(ctx) {
		return nil
	}

	for _, hook := range fMCSAFEATURES_3MO_V1AfterSelectHooks {
		if err := hook(ctx, exec, o); err != nil {
			return err
		}
	}

	return nil
}

// doBeforeInsertHooks executes all "before insert" hooks.
func (o *FMCSA_FEATURES_3MO_V1) doBeforeInsertHooks(ctx context.Context, exec boil.ContextExecutor) (err error) {
	if boil.HooksAreSkipped(ctx) {
		return nil
	}

	for _, hook := range fMCSAFEATURES_3MO_V1BeforeInsertHooks {
		if err := hook(ctx, exec, o); err != nil {
			return err
		}
	}

	return nil
}

// doAfterInsertHooks executes all "after Insert" hooks.
func (o *FMCSA_FEATURES_3MO_V1) doAfterInsertHooks(ctx context.Context, exec boil.ContextExecutor) (err error) {
	if boil.HooksAreSkipped(ctx) {
		return nil
	}

	for _, hook := range fMCSAFEATURES_3MO_V1AfterInsertHooks {
		if err := hook(ctx, exec, o); err != nil {
			return err
		}
	}

	return nil
}

// doBeforeUpdateHooks executes all "before Update" hooks.
func (o *FMCSA_FEATURES_3MO_V1) doBeforeUpdateHooks(ctx context.Context, exec boil.ContextExecutor) (err error) {
	if boil.HooksAreSkipped(ctx) {
		return nil
	}

	for _, hook := range fMCSAFEATURES_3MO_V1BeforeUpdateHooks {
		if err := hook(ctx, exec, o); err != nil {
			return err
		}
	}

	return nil
}

// doAfterUpdateHooks executes all "after Update" hooks.
func (o *FMCSA_FEATURES_3MO_V1) doAfterUpdateHooks(ctx context.Context, exec boil.ContextExecutor) (err error) {
	if boil.HooksAreSkipped(ctx) {
		return nil
	}

	for _, hook := range fMCSAFEATURES_3MO_V1AfterUpdateHooks {
		if err := hook(ctx, exec, o); err != nil {
			return err
		}
	}

	return nil
}

// doBeforeDeleteHooks executes all "before Delete" hooks.
func (o *FMCSA_FEATURES_3MO_V1) doBeforeDeleteHooks(ctx context.Context, exec boil.ContextExecutor) (err error) {
	if boil.HooksAreSkipped(ctx) {
		return nil
	}

	for _, hook := range fMCSAFEATURES_3MO_V1BeforeDeleteHooks {
		if err := hook(ctx, exec, o); err != nil {
			return err
		}
	}

	return nil
}

// doAfterDeleteHooks executes all "after Delete" hooks.
func (o *FMCSA_FEATURES_3MO_V1) doAfterDeleteHooks(ctx context.Context, exec boil.ContextExecutor) (err error) {
	if boil.HooksAreSkipped(ctx) {
		return nil
	}

	for _, hook := range fMCSAFEATURES_3MO_V1AfterDeleteHooks {
		if err := hook(ctx, exec, o); err != nil {
			return err
		}
	}

	return nil
}

// doBeforeUpsertHooks executes all "before Upsert" hooks.
func (o *FMCSA_FEATURES_3MO_V1) doBeforeUpsertHooks(ctx context.Context, exec boil.ContextExecutor) (err error) {
	if boil.HooksAreSkipped(ctx) {
		return nil
	}

	for _, hook := range fMCSAFEATURES_3MO_V1BeforeUpsertHooks {
		if err := hook(ctx, exec, o); err != nil {
			return err
		}
	}

	return nil
}

// doAfterUpsertHooks executes all "after Upsert" hooks.
func (o *FMCSA_FEATURES_3MO_V1) doAfterUpsertHooks(ctx context.Context, exec boil.ContextExecutor) (err error) {
	if boil.HooksAreSkipped(ctx) {
		return nil
	}

	for _, hook := range fMCSAFEATURES_3MO_V1AfterUpsertHooks {
		if err := hook(ctx, exec, o); err != nil {
			return err
		}
	}

	return nil
}

// AddFMCSA_FEATURES_3MO_V1Hook registers your hook function for all future operations.
func AddFMCSA_FEATURES_3MO_V1Hook(hookPoint boil.HookPoint, fMCSAFEATURES_3MO_V1Hook FMCSA_FEATURES_3MO_V1Hook) {
	switch hookPoint {
	case boil.AfterSelectHook:
		fMCSAFEATURES_3MO_V1AfterSelectHooks = append(fMCSAFEATURES_3MO_V1AfterSelectHooks, fMCSAFEATURES_3MO_V1Hook)
	case boil.BeforeInsertHook:
		fMCSAFEATURES_3MO_V1BeforeInsertHooks = append(fMCSAFEATURES_3MO_V1BeforeInsertHooks, fMCSAFEATURES_3MO_V1Hook)
	case boil.AfterInsertHook:
		fMCSAFEATURES_3MO_V1AfterInsertHooks = append(fMCSAFEATURES_3MO_V1AfterInsertHooks, fMCSAFEATURES_3MO_V1Hook)
	case boil.BeforeUpdateHook:
		fMCSAFEATURES_3MO_V1BeforeUpdateHooks = append(fMCSAFEATURES_3MO_V1BeforeUpdateHooks, fMCSAFEATURES_3MO_V1Hook)
	case boil.AfterUpdateHook:
		fMCSAFEATURES_3MO_V1AfterUpdateHooks = append(fMCSAFEATURES_3MO_V1AfterUpdateHooks, fMCSAFEATURES_3MO_V1Hook)
	case boil.BeforeDeleteHook:
		fMCSAFEATURES_3MO_V1BeforeDeleteHooks = append(fMCSAFEATURES_3MO_V1BeforeDeleteHooks, fMCSAFEATURES_3MO_V1Hook)
	case boil.AfterDeleteHook:
		fMCSAFEATURES_3MO_V1AfterDeleteHooks = append(fMCSAFEATURES_3MO_V1AfterDeleteHooks, fMCSAFEATURES_3MO_V1Hook)
	case boil.BeforeUpsertHook:
		fMCSAFEATURES_3MO_V1BeforeUpsertHooks = append(fMCSAFEATURES_3MO_V1BeforeUpsertHooks, fMCSAFEATURES_3MO_V1Hook)
	case boil.AfterUpsertHook:
		fMCSAFEATURES_3MO_V1AfterUpsertHooks = append(fMCSAFEATURES_3MO_V1AfterUpsertHooks, fMCSAFEATURES_3MO_V1Hook)
	}
}

// One returns a single fMCSAFEATURES_3MO_V1 record from the query.
func (q fMCSAFEATURES_3MO_V1Query) One(ctx context.Context, exec boil.ContextExecutor) (*FMCSA_FEATURES_3MO_V1, error) {
	o := &FMCSA_FEATURES_3MO_V1{}

	queries.SetLimit(q.Query, 1)

	err := q.Bind(ctx, exec, o)
	if err != nil {
		if errors.Is(err, sql.ErrNoRows) {
			return nil, sql.ErrNoRows
		}
		return nil, errors.Wrap(err, "ds: failed to execute a one query for FMCSA_FEATURES_3MO_V1")
	}

	if err := o.doAfterSelectHooks(ctx, exec); err != nil {
		return o, err
	}

	return o, nil
}

// All returns all FMCSA_FEATURES_3MO_V1 records from the query.
func (q fMCSAFEATURES_3MO_V1Query) All(ctx context.Context, exec boil.ContextExecutor) (FMCSA_FEATURES_3MO_V1Slice, error) {
	var o []*FMCSA_FEATURES_3MO_V1

	err := q.Bind(ctx, exec, &o)
	if err != nil {
		return nil, errors.Wrap(err, "ds: failed to assign all query results to FMCSA_FEATURES_3MO_V1 slice")
	}

	if len(fMCSAFEATURES_3MO_V1AfterSelectHooks) != 0 {
		for _, obj := range o {
			if err := obj.doAfterSelectHooks(ctx, exec); err != nil {
				return o, err
			}
		}
	}

	return o, nil
}

// Count returns the count of all FMCSA_FEATURES_3MO_V1 records in the query.
func (q fMCSAFEATURES_3MO_V1Query) Count(ctx context.Context, exec boil.ContextExecutor) (int64, error) {
	var count int64

	queries.SetSelect(q.Query, nil)
	queries.SetCount(q.Query)

	err := q.Query.QueryRowContext(ctx, exec).Scan(&count)
	if err != nil {
		return 0, errors.Wrap(err, "ds: failed to count FMCSA_FEATURES_3MO_V1 rows")
	}

	return count, nil
}

// Exists checks if the row exists in the table.
func (q fMCSAFEATURES_3MO_V1Query) Exists(ctx context.Context, exec boil.ContextExecutor) (bool, error) {
	var count int64

	queries.SetSelect(q.Query, nil)
	queries.SetCount(q.Query)
	queries.SetLimit(q.Query, 1)

	err := q.Query.QueryRowContext(ctx, exec).Scan(&count)
	if err != nil {
		return false, errors.Wrap(err, "ds: failed to check if FMCSA_FEATURES_3MO_V1 exists")
	}

	return count > 0, nil
}

// FMCSAFEATURES3MOV1S retrieves all the records using an executor.
func FMCSAFEATURES3MOV1S(mods ...qm.QueryMod) fMCSAFEATURES_3MO_V1Query {
	mods = append(mods, qm.From("\"FMCSA_FEATURES_3MO_V1\""))
	q := NewQuery(mods...)
	if len(queries.GetSelect(q)) == 0 {
		queries.SetSelect(q, []string{"\"FMCSA_FEATURES_3MO_V1\".*"})
	}

	return fMCSAFEATURES_3MO_V1Query{q}
}

// FindFMCSA_FEATURES_3MO_V1 retrieves a single record by ID with an executor.
// If selectCols is empty Find will return all columns.
func FindFMCSA_FEATURES_3MO_V1(ctx context.Context, exec boil.ContextExecutor, dOTNUMBER int64, sTARTDATE time.Time, eNDDATE time.Time, selectCols ...string) (*FMCSA_FEATURES_3MO_V1, error) {
	fMCSAFEATURES_3MO_V1Obj := &FMCSA_FEATURES_3MO_V1{}

	sel := "*"
	if len(selectCols) > 0 {
		sel = strings.Join(strmangle.IdentQuoteSlice(dialect.LQ, dialect.RQ, selectCols), ",")
	}
	query := fmt.Sprintf(
		"select %s from \"FMCSA_FEATURES_3MO_V1\" where \"DOT_NUMBER\"=$1 AND \"START_DATE\"=$2 AND \"END_DATE\"=$3", sel,
	)

	q := queries.Raw(query, dOTNUMBER, sTARTDATE, eNDDATE)

	err := q.Bind(ctx, exec, fMCSAFEATURES_3MO_V1Obj)
	if err != nil {
		if errors.Is(err, sql.ErrNoRows) {
			return nil, sql.ErrNoRows
		}
		return nil, errors.Wrap(err, "ds: unable to select from FMCSA_FEATURES_3MO_V1")
	}

	if err = fMCSAFEATURES_3MO_V1Obj.doAfterSelectHooks(ctx, exec); err != nil {
		return fMCSAFEATURES_3MO_V1Obj, err
	}

	return fMCSAFEATURES_3MO_V1Obj, nil
}

// Insert a single record using an executor.
// See boil.Columns.InsertColumnSet documentation to understand column list inference for inserts.
func (o *FMCSA_FEATURES_3MO_V1) Insert(ctx context.Context, exec boil.ContextExecutor, columns boil.Columns) error {
	if o == nil {
		return errors.New("ds: no FMCSA_FEATURES_3MO_V1 provided for insertion")
	}

	var err error

	if err := o.doBeforeInsertHooks(ctx, exec); err != nil {
		return err
	}

	nzDefaults := queries.NonZeroDefaultSet(fMCSAFEATURES_3MO_V1ColumnsWithDefault, o)

	key := makeCacheKey(columns, nzDefaults)
	fMCSAFEATURES_3MO_V1InsertCacheMut.RLock()
	cache, cached := fMCSAFEATURES_3MO_V1InsertCache[key]
	fMCSAFEATURES_3MO_V1InsertCacheMut.RUnlock()

	if !cached {
		wl, returnColumns := columns.InsertColumnSet(
			fMCSAFEATURES_3MO_V1AllColumns,
			fMCSAFEATURES_3MO_V1ColumnsWithDefault,
			fMCSAFEATURES_3MO_V1ColumnsWithoutDefault,
			nzDefaults,
		)

		cache.valueMapping, err = queries.BindMapping(fMCSAFEATURES_3MO_V1Type, fMCSAFEATURES_3MO_V1Mapping, wl)
		if err != nil {
			return err
		}
		cache.retMapping, err = queries.BindMapping(fMCSAFEATURES_3MO_V1Type, fMCSAFEATURES_3MO_V1Mapping, returnColumns)
		if err != nil {
			return err
		}
		if len(wl) != 0 {
			cache.query = fmt.Sprintf("INSERT INTO \"FMCSA_FEATURES_3MO_V1\" (\"%s\") %%sVALUES (%s)%%s", strings.Join(wl, "\",\""), strmangle.Placeholders(dialect.UseIndexPlaceholders, len(wl), 1, 1))
		} else {
			cache.query = "INSERT INTO \"FMCSA_FEATURES_3MO_V1\" %sDEFAULT VALUES%s"
		}

		var queryOutput, queryReturning string

		if len(cache.retMapping) != 0 {
			queryReturning = fmt.Sprintf(" RETURNING \"%s\"", strings.Join(returnColumns, "\",\""))
		}

		cache.query = fmt.Sprintf(cache.query, queryOutput, queryReturning)
	}

	value := reflect.Indirect(reflect.ValueOf(o))
	vals := queries.ValuesFromMapping(value, cache.valueMapping)

	if boil.IsDebug(ctx) {
		writer := boil.DebugWriterFrom(ctx)
		fmt.Fprintln(writer, cache.query)
		fmt.Fprintln(writer, vals)
	}

	if len(cache.retMapping) != 0 {
		err = exec.QueryRowContext(ctx, cache.query, vals...).Scan(queries.PtrsFromMapping(value, cache.retMapping)...)
	} else {
		_, err = exec.ExecContext(ctx, cache.query, vals...)
	}

	if err != nil {
		return errors.Wrap(err, "ds: unable to insert into FMCSA_FEATURES_3MO_V1")
	}

	if !cached {
		fMCSAFEATURES_3MO_V1InsertCacheMut.Lock()
		fMCSAFEATURES_3MO_V1InsertCache[key] = cache
		fMCSAFEATURES_3MO_V1InsertCacheMut.Unlock()
	}

	return o.doAfterInsertHooks(ctx, exec)
}

// Update uses an executor to update the FMCSA_FEATURES_3MO_V1.
// See boil.Columns.UpdateColumnSet documentation to understand column list inference for updates.
// Update does not automatically update the record in case of default values. Use .Reload() to refresh the records.
func (o *FMCSA_FEATURES_3MO_V1) Update(ctx context.Context, exec boil.ContextExecutor, columns boil.Columns) (int64, error) {
	var err error
	if err = o.doBeforeUpdateHooks(ctx, exec); err != nil {
		return 0, err
	}
	key := makeCacheKey(columns, nil)
	fMCSAFEATURES_3MO_V1UpdateCacheMut.RLock()
	cache, cached := fMCSAFEATURES_3MO_V1UpdateCache[key]
	fMCSAFEATURES_3MO_V1UpdateCacheMut.RUnlock()

	if !cached {
		wl := columns.UpdateColumnSet(
			fMCSAFEATURES_3MO_V1AllColumns,
			fMCSAFEATURES_3MO_V1PrimaryKeyColumns,
		)

		if !columns.IsWhitelist() {
			wl = strmangle.SetComplement(wl, []string{"created_at"})
		}
		if len(wl) == 0 {
			return 0, errors.New("ds: unable to update FMCSA_FEATURES_3MO_V1, could not build whitelist")
		}

		cache.query = fmt.Sprintf("UPDATE \"FMCSA_FEATURES_3MO_V1\" SET %s WHERE %s",
			strmangle.SetParamNames("\"", "\"", 1, wl),
			strmangle.WhereClause("\"", "\"", len(wl)+1, fMCSAFEATURES_3MO_V1PrimaryKeyColumns),
		)
		cache.valueMapping, err = queries.BindMapping(fMCSAFEATURES_3MO_V1Type, fMCSAFEATURES_3MO_V1Mapping, append(wl, fMCSAFEATURES_3MO_V1PrimaryKeyColumns...))
		if err != nil {
			return 0, err
		}
	}

	values := queries.ValuesFromMapping(reflect.Indirect(reflect.ValueOf(o)), cache.valueMapping)

	if boil.IsDebug(ctx) {
		writer := boil.DebugWriterFrom(ctx)
		fmt.Fprintln(writer, cache.query)
		fmt.Fprintln(writer, values)
	}
	var result sql.Result
	result, err = exec.ExecContext(ctx, cache.query, values...)
	if err != nil {
		return 0, errors.Wrap(err, "ds: unable to update FMCSA_FEATURES_3MO_V1 row")
	}

	rowsAff, err := result.RowsAffected()
	if err != nil {
		return 0, errors.Wrap(err, "ds: failed to get rows affected by update for FMCSA_FEATURES_3MO_V1")
	}

	if !cached {
		fMCSAFEATURES_3MO_V1UpdateCacheMut.Lock()
		fMCSAFEATURES_3MO_V1UpdateCache[key] = cache
		fMCSAFEATURES_3MO_V1UpdateCacheMut.Unlock()
	}

	return rowsAff, o.doAfterUpdateHooks(ctx, exec)
}

// UpdateAll updates all rows with the specified column values.
func (q fMCSAFEATURES_3MO_V1Query) UpdateAll(ctx context.Context, exec boil.ContextExecutor, cols M) (int64, error) {
	queries.SetUpdate(q.Query, cols)

	result, err := q.Query.ExecContext(ctx, exec)
	if err != nil {
		return 0, errors.Wrap(err, "ds: unable to update all for FMCSA_FEATURES_3MO_V1")
	}

	rowsAff, err := result.RowsAffected()
	if err != nil {
		return 0, errors.Wrap(err, "ds: unable to retrieve rows affected for FMCSA_FEATURES_3MO_V1")
	}

	return rowsAff, nil
}

// UpdateAll updates all rows with the specified column values, using an executor.
func (o FMCSA_FEATURES_3MO_V1Slice) UpdateAll(ctx context.Context, exec boil.ContextExecutor, cols M) (int64, error) {
	ln := int64(len(o))
	if ln == 0 {
		return 0, nil
	}

	if len(cols) == 0 {
		return 0, errors.New("ds: update all requires at least one column argument")
	}

	colNames := make([]string, len(cols))
	args := make([]interface{}, len(cols))

	i := 0
	for name, value := range cols {
		colNames[i] = name
		args[i] = value
		i++
	}

	// Append all of the primary key values for each column
	for _, obj := range o {
		pkeyArgs := queries.ValuesFromMapping(reflect.Indirect(reflect.ValueOf(obj)), fMCSAFEATURES_3MO_V1PrimaryKeyMapping)
		args = append(args, pkeyArgs...)
	}

	sql := fmt.Sprintf("UPDATE \"FMCSA_FEATURES_3MO_V1\" SET %s WHERE %s",
		strmangle.SetParamNames("\"", "\"", 1, colNames),
		strmangle.WhereClauseRepeated(string(dialect.LQ), string(dialect.RQ), len(colNames)+1, fMCSAFEATURES_3MO_V1PrimaryKeyColumns, len(o)))

	if boil.IsDebug(ctx) {
		writer := boil.DebugWriterFrom(ctx)
		fmt.Fprintln(writer, sql)
		fmt.Fprintln(writer, args...)
	}
	result, err := exec.ExecContext(ctx, sql, args...)
	if err != nil {
		return 0, errors.Wrap(err, "ds: unable to update all in fMCSAFEATURES_3MO_V1 slice")
	}

	rowsAff, err := result.RowsAffected()
	if err != nil {
		return 0, errors.Wrap(err, "ds: unable to retrieve rows affected all in update all fMCSAFEATURES_3MO_V1")
	}
	return rowsAff, nil
}

// Upsert attempts an insert using an executor, and does an update or ignore on conflict.
// See boil.Columns documentation for how to properly use updateColumns and insertColumns.
func (o *FMCSA_FEATURES_3MO_V1) Upsert(ctx context.Context, exec boil.ContextExecutor, updateOnConflict bool, conflictColumns []string, updateColumns, insertColumns boil.Columns) error {
	if o == nil {
		return errors.New("ds: no FMCSA_FEATURES_3MO_V1 provided for upsert")
	}

	if err := o.doBeforeUpsertHooks(ctx, exec); err != nil {
		return err
	}

	nzDefaults := queries.NonZeroDefaultSet(fMCSAFEATURES_3MO_V1ColumnsWithDefault, o)

	// Build cache key in-line uglily - mysql vs psql problems
	buf := strmangle.GetBuffer()
	if updateOnConflict {
		buf.WriteByte('t')
	} else {
		buf.WriteByte('f')
	}
	buf.WriteByte('.')
	for _, c := range conflictColumns {
		buf.WriteString(c)
	}
	buf.WriteByte('.')
	buf.WriteString(strconv.Itoa(updateColumns.Kind))
	for _, c := range updateColumns.Cols {
		buf.WriteString(c)
	}
	buf.WriteByte('.')
	buf.WriteString(strconv.Itoa(insertColumns.Kind))
	for _, c := range insertColumns.Cols {
		buf.WriteString(c)
	}
	buf.WriteByte('.')
	for _, c := range nzDefaults {
		buf.WriteString(c)
	}
	key := buf.String()
	strmangle.PutBuffer(buf)

	fMCSAFEATURES_3MO_V1UpsertCacheMut.RLock()
	cache, cached := fMCSAFEATURES_3MO_V1UpsertCache[key]
	fMCSAFEATURES_3MO_V1UpsertCacheMut.RUnlock()

	var err error

	if !cached {
		insert, ret := insertColumns.InsertColumnSet(
			fMCSAFEATURES_3MO_V1AllColumns,
			fMCSAFEATURES_3MO_V1ColumnsWithDefault,
			fMCSAFEATURES_3MO_V1ColumnsWithoutDefault,
			nzDefaults,
		)

		update := updateColumns.UpdateColumnSet(
			fMCSAFEATURES_3MO_V1AllColumns,
			fMCSAFEATURES_3MO_V1PrimaryKeyColumns,
		)

		if updateOnConflict && len(update) == 0 {
			return errors.New("ds: unable to upsert FMCSA_FEATURES_3MO_V1, could not build update column list")
		}

		conflict := conflictColumns
		if len(conflict) == 0 {
			conflict = make([]string, len(fMCSAFEATURES_3MO_V1PrimaryKeyColumns))
			copy(conflict, fMCSAFEATURES_3MO_V1PrimaryKeyColumns)
		}
		cache.query = buildUpsertQueryPostgres(dialect, "\"FMCSA_FEATURES_3MO_V1\"", updateOnConflict, ret, update, conflict, insert)

		cache.valueMapping, err = queries.BindMapping(fMCSAFEATURES_3MO_V1Type, fMCSAFEATURES_3MO_V1Mapping, insert)
		if err != nil {
			return err
		}
		if len(ret) != 0 {
			cache.retMapping, err = queries.BindMapping(fMCSAFEATURES_3MO_V1Type, fMCSAFEATURES_3MO_V1Mapping, ret)
			if err != nil {
				return err
			}
		}
	}

	value := reflect.Indirect(reflect.ValueOf(o))
	vals := queries.ValuesFromMapping(value, cache.valueMapping)
	var returns []interface{}
	if len(cache.retMapping) != 0 {
		returns = queries.PtrsFromMapping(value, cache.retMapping)
	}

	if boil.IsDebug(ctx) {
		writer := boil.DebugWriterFrom(ctx)
		fmt.Fprintln(writer, cache.query)
		fmt.Fprintln(writer, vals)
	}
	if len(cache.retMapping) != 0 {
		err = exec.QueryRowContext(ctx, cache.query, vals...).Scan(returns...)
		if errors.Is(err, sql.ErrNoRows) {
			err = nil // Postgres doesn't return anything when there's no update
		}
	} else {
		_, err = exec.ExecContext(ctx, cache.query, vals...)
	}
	if err != nil {
		return errors.Wrap(err, "ds: unable to upsert FMCSA_FEATURES_3MO_V1")
	}

	if !cached {
		fMCSAFEATURES_3MO_V1UpsertCacheMut.Lock()
		fMCSAFEATURES_3MO_V1UpsertCache[key] = cache
		fMCSAFEATURES_3MO_V1UpsertCacheMut.Unlock()
	}

	return o.doAfterUpsertHooks(ctx, exec)
}

// Delete deletes a single FMCSA_FEATURES_3MO_V1 record with an executor.
// Delete will match against the primary key column to find the record to delete.
func (o *FMCSA_FEATURES_3MO_V1) Delete(ctx context.Context, exec boil.ContextExecutor) (int64, error) {
	if o == nil {
		return 0, errors.New("ds: no FMCSA_FEATURES_3MO_V1 provided for delete")
	}

	if err := o.doBeforeDeleteHooks(ctx, exec); err != nil {
		return 0, err
	}

	args := queries.ValuesFromMapping(reflect.Indirect(reflect.ValueOf(o)), fMCSAFEATURES_3MO_V1PrimaryKeyMapping)
	sql := "DELETE FROM \"FMCSA_FEATURES_3MO_V1\" WHERE \"DOT_NUMBER\"=$1 AND \"START_DATE\"=$2 AND \"END_DATE\"=$3"

	if boil.IsDebug(ctx) {
		writer := boil.DebugWriterFrom(ctx)
		fmt.Fprintln(writer, sql)
		fmt.Fprintln(writer, args...)
	}
	result, err := exec.ExecContext(ctx, sql, args...)
	if err != nil {
		return 0, errors.Wrap(err, "ds: unable to delete from FMCSA_FEATURES_3MO_V1")
	}

	rowsAff, err := result.RowsAffected()
	if err != nil {
		return 0, errors.Wrap(err, "ds: failed to get rows affected by delete for FMCSA_FEATURES_3MO_V1")
	}

	if err := o.doAfterDeleteHooks(ctx, exec); err != nil {
		return 0, err
	}

	return rowsAff, nil
}

// DeleteAll deletes all matching rows.
func (q fMCSAFEATURES_3MO_V1Query) DeleteAll(ctx context.Context, exec boil.ContextExecutor) (int64, error) {
	if q.Query == nil {
		return 0, errors.New("ds: no fMCSAFEATURES_3MO_V1Query provided for delete all")
	}

	queries.SetDelete(q.Query)

	result, err := q.Query.ExecContext(ctx, exec)
	if err != nil {
		return 0, errors.Wrap(err, "ds: unable to delete all from FMCSA_FEATURES_3MO_V1")
	}

	rowsAff, err := result.RowsAffected()
	if err != nil {
		return 0, errors.Wrap(err, "ds: failed to get rows affected by deleteall for FMCSA_FEATURES_3MO_V1")
	}

	return rowsAff, nil
}

// DeleteAll deletes all rows in the slice, using an executor.
func (o FMCSA_FEATURES_3MO_V1Slice) DeleteAll(ctx context.Context, exec boil.ContextExecutor) (int64, error) {
	if len(o) == 0 {
		return 0, nil
	}

	if len(fMCSAFEATURES_3MO_V1BeforeDeleteHooks) != 0 {
		for _, obj := range o {
			if err := obj.doBeforeDeleteHooks(ctx, exec); err != nil {
				return 0, err
			}
		}
	}

	var args []interface{}
	for _, obj := range o {
		pkeyArgs := queries.ValuesFromMapping(reflect.Indirect(reflect.ValueOf(obj)), fMCSAFEATURES_3MO_V1PrimaryKeyMapping)
		args = append(args, pkeyArgs...)
	}

	sql := "DELETE FROM \"FMCSA_FEATURES_3MO_V1\" WHERE " +
		strmangle.WhereClauseRepeated(string(dialect.LQ), string(dialect.RQ), 1, fMCSAFEATURES_3MO_V1PrimaryKeyColumns, len(o))

	if boil.IsDebug(ctx) {
		writer := boil.DebugWriterFrom(ctx)
		fmt.Fprintln(writer, sql)
		fmt.Fprintln(writer, args)
	}
	result, err := exec.ExecContext(ctx, sql, args...)
	if err != nil {
		return 0, errors.Wrap(err, "ds: unable to delete all from fMCSAFEATURES_3MO_V1 slice")
	}

	rowsAff, err := result.RowsAffected()
	if err != nil {
		return 0, errors.Wrap(err, "ds: failed to get rows affected by deleteall for FMCSA_FEATURES_3MO_V1")
	}

	if len(fMCSAFEATURES_3MO_V1AfterDeleteHooks) != 0 {
		for _, obj := range o {
			if err := obj.doAfterDeleteHooks(ctx, exec); err != nil {
				return 0, err
			}
		}
	}

	return rowsAff, nil
}

// Reload refetches the object from the database
// using the primary keys with an executor.
func (o *FMCSA_FEATURES_3MO_V1) Reload(ctx context.Context, exec boil.ContextExecutor) error {
	ret, err := FindFMCSA_FEATURES_3MO_V1(ctx, exec, o.DOT_NUMBER, o.START_DATE, o.END_DATE)
	if err != nil {
		return err
	}

	*o = *ret
	return nil
}

// ReloadAll refetches every row with matching primary key column values
// and overwrites the original object slice with the newly updated slice.
func (o *FMCSA_FEATURES_3MO_V1Slice) ReloadAll(ctx context.Context, exec boil.ContextExecutor) error {
	if o == nil || len(*o) == 0 {
		return nil
	}

	slice := FMCSA_FEATURES_3MO_V1Slice{}
	var args []interface{}
	for _, obj := range *o {
		pkeyArgs := queries.ValuesFromMapping(reflect.Indirect(reflect.ValueOf(obj)), fMCSAFEATURES_3MO_V1PrimaryKeyMapping)
		args = append(args, pkeyArgs...)
	}

	sql := "SELECT \"FMCSA_FEATURES_3MO_V1\".* FROM \"FMCSA_FEATURES_3MO_V1\" WHERE " +
		strmangle.WhereClauseRepeated(string(dialect.LQ), string(dialect.RQ), 1, fMCSAFEATURES_3MO_V1PrimaryKeyColumns, len(*o))

	q := queries.Raw(sql, args...)

	err := q.Bind(ctx, exec, &slice)
	if err != nil {
		return errors.Wrap(err, "ds: unable to reload all in FMCSA_FEATURES_3MO_V1Slice")
	}

	*o = slice

	return nil
}

// FMCSA_FEATURES_3MO_V1Exists checks if the FMCSA_FEATURES_3MO_V1 row exists.
func FMCSA_FEATURES_3MO_V1Exists(ctx context.Context, exec boil.ContextExecutor, dOTNUMBER int64, sTARTDATE time.Time, eNDDATE time.Time) (bool, error) {
	var exists bool
	sql := "select exists(select 1 from \"FMCSA_FEATURES_3MO_V1\" where \"DOT_NUMBER\"=$1 AND \"START_DATE\"=$2 AND \"END_DATE\"=$3 limit 1)"

	if boil.IsDebug(ctx) {
		writer := boil.DebugWriterFrom(ctx)
		fmt.Fprintln(writer, sql)
		fmt.Fprintln(writer, dOTNUMBER, sTARTDATE, eNDDATE)
	}
	row := exec.QueryRowContext(ctx, sql, dOTNUMBER, sTARTDATE, eNDDATE)

	err := row.Scan(&exists)
	if err != nil {
		return false, errors.Wrap(err, "ds: unable to check if FMCSA_FEATURES_3MO_V1 exists")
	}

	return exists, nil
}
