// Code generated by SQLBoiler (https://github.com/volatiletech/sqlboiler). DO NOT EDIT.
// This file is meant to be re-generated in place and/or deleted at any time.

package safer_watch

import (
	"context"
	"database/sql"
	"fmt"
	"reflect"
	"strconv"
	"strings"
	"sync"
	"time"

	"github.com/friendsofgo/errors"
	"github.com/volatiletech/null/v8"
	"github.com/volatiletech/sqlboiler/v4/boil"
	"github.com/volatiletech/sqlboiler/v4/queries"
	"github.com/volatiletech/sqlboiler/v4/queries/qm"
	"github.com/volatiletech/sqlboiler/v4/queries/qmhelper"
	"github.com/volatiletech/strmangle"
)

// Report is an object representing the database table.
type Report struct {
	ID                   string      `boil:"id" json:"id" toml:"id" yaml:"id"`
	DotNumber            int         `boil:"dot_number" json:"dot_number" toml:"dot_number" yaml:"dot_number"`
	PolicyNumber         null.String `boil:"policy_number" json:"policy_number,omitempty" toml:"policy_number" yaml:"policy_number,omitempty"`
	ProducerName         null.String `boil:"producer_name" json:"producer_name,omitempty" toml:"producer_name" yaml:"producer_name,omitempty"`
	ProducerAddress      null.String `boil:"producer_address" json:"producer_address,omitempty" toml:"producer_address" yaml:"producer_address,omitempty"`
	ProducerCity         null.String `boil:"producer_city" json:"producer_city,omitempty" toml:"producer_city" yaml:"producer_city,omitempty"`
	ProducerState        null.String `boil:"producer_state" json:"producer_state,omitempty" toml:"producer_state" yaml:"producer_state,omitempty"`
	ProducerZip          null.String `boil:"producer_zip" json:"producer_zip,omitempty" toml:"producer_zip" yaml:"producer_zip,omitempty"`
	ProducerPhone        null.String `boil:"producer_phone" json:"producer_phone,omitempty" toml:"producer_phone" yaml:"producer_phone,omitempty"`
	ProducerFax          null.String `boil:"producer_fax" json:"producer_fax,omitempty" toml:"producer_fax" yaml:"producer_fax,omitempty"`
	ProducerEmail        null.String `boil:"producer_email" json:"producer_email,omitempty" toml:"producer_email" yaml:"producer_email,omitempty"`
	Underwriter          null.String `boil:"underwriter" json:"underwriter,omitempty" toml:"underwriter" yaml:"underwriter,omitempty"`
	PolicyType           null.String `boil:"policy_type" json:"policy_type,omitempty" toml:"policy_type" yaml:"policy_type,omitempty"`
	PolicyExpirationDate null.String `boil:"policy_expiration_date" json:"policy_expiration_date,omitempty" toml:"policy_expiration_date" yaml:"policy_expiration_date,omitempty"`
	PolicyRating         null.String `boil:"policy_rating" json:"policy_rating,omitempty" toml:"policy_rating" yaml:"policy_rating,omitempty"`
	PolicyLimit          null.String `boil:"policy_limit" json:"policy_limit,omitempty" toml:"policy_limit" yaml:"policy_limit,omitempty"`
	PolicyDeductible     null.String `boil:"policy_deductible" json:"policy_deductible,omitempty" toml:"policy_deductible" yaml:"policy_deductible,omitempty"`

	R *reportR `boil:"" json:"" toml:"" yaml:""`
	L reportL  `boil:"-" json:"-" toml:"-" yaml:"-"`
}

var ReportColumns = struct {
	ID                   string
	DotNumber            string
	PolicyNumber         string
	ProducerName         string
	ProducerAddress      string
	ProducerCity         string
	ProducerState        string
	ProducerZip          string
	ProducerPhone        string
	ProducerFax          string
	ProducerEmail        string
	Underwriter          string
	PolicyType           string
	PolicyExpirationDate string
	PolicyRating         string
	PolicyLimit          string
	PolicyDeductible     string
}{
	ID:                   "id",
	DotNumber:            "dot_number",
	PolicyNumber:         "policy_number",
	ProducerName:         "producer_name",
	ProducerAddress:      "producer_address",
	ProducerCity:         "producer_city",
	ProducerState:        "producer_state",
	ProducerZip:          "producer_zip",
	ProducerPhone:        "producer_phone",
	ProducerFax:          "producer_fax",
	ProducerEmail:        "producer_email",
	Underwriter:          "underwriter",
	PolicyType:           "policy_type",
	PolicyExpirationDate: "policy_expiration_date",
	PolicyRating:         "policy_rating",
	PolicyLimit:          "policy_limit",
	PolicyDeductible:     "policy_deductible",
}

var ReportTableColumns = struct {
	ID                   string
	DotNumber            string
	PolicyNumber         string
	ProducerName         string
	ProducerAddress      string
	ProducerCity         string
	ProducerState        string
	ProducerZip          string
	ProducerPhone        string
	ProducerFax          string
	ProducerEmail        string
	Underwriter          string
	PolicyType           string
	PolicyExpirationDate string
	PolicyRating         string
	PolicyLimit          string
	PolicyDeductible     string
}{
	ID:                   "report.id",
	DotNumber:            "report.dot_number",
	PolicyNumber:         "report.policy_number",
	ProducerName:         "report.producer_name",
	ProducerAddress:      "report.producer_address",
	ProducerCity:         "report.producer_city",
	ProducerState:        "report.producer_state",
	ProducerZip:          "report.producer_zip",
	ProducerPhone:        "report.producer_phone",
	ProducerFax:          "report.producer_fax",
	ProducerEmail:        "report.producer_email",
	Underwriter:          "report.underwriter",
	PolicyType:           "report.policy_type",
	PolicyExpirationDate: "report.policy_expiration_date",
	PolicyRating:         "report.policy_rating",
	PolicyLimit:          "report.policy_limit",
	PolicyDeductible:     "report.policy_deductible",
}

// Generated where

var ReportWhere = struct {
	ID                   whereHelperstring
	DotNumber            whereHelperint
	PolicyNumber         whereHelpernull_String
	ProducerName         whereHelpernull_String
	ProducerAddress      whereHelpernull_String
	ProducerCity         whereHelpernull_String
	ProducerState        whereHelpernull_String
	ProducerZip          whereHelpernull_String
	ProducerPhone        whereHelpernull_String
	ProducerFax          whereHelpernull_String
	ProducerEmail        whereHelpernull_String
	Underwriter          whereHelpernull_String
	PolicyType           whereHelpernull_String
	PolicyExpirationDate whereHelpernull_String
	PolicyRating         whereHelpernull_String
	PolicyLimit          whereHelpernull_String
	PolicyDeductible     whereHelpernull_String
}{
	ID:                   whereHelperstring{field: "\"safer_watch\".\"report\".\"id\""},
	DotNumber:            whereHelperint{field: "\"safer_watch\".\"report\".\"dot_number\""},
	PolicyNumber:         whereHelpernull_String{field: "\"safer_watch\".\"report\".\"policy_number\""},
	ProducerName:         whereHelpernull_String{field: "\"safer_watch\".\"report\".\"producer_name\""},
	ProducerAddress:      whereHelpernull_String{field: "\"safer_watch\".\"report\".\"producer_address\""},
	ProducerCity:         whereHelpernull_String{field: "\"safer_watch\".\"report\".\"producer_city\""},
	ProducerState:        whereHelpernull_String{field: "\"safer_watch\".\"report\".\"producer_state\""},
	ProducerZip:          whereHelpernull_String{field: "\"safer_watch\".\"report\".\"producer_zip\""},
	ProducerPhone:        whereHelpernull_String{field: "\"safer_watch\".\"report\".\"producer_phone\""},
	ProducerFax:          whereHelpernull_String{field: "\"safer_watch\".\"report\".\"producer_fax\""},
	ProducerEmail:        whereHelpernull_String{field: "\"safer_watch\".\"report\".\"producer_email\""},
	Underwriter:          whereHelpernull_String{field: "\"safer_watch\".\"report\".\"underwriter\""},
	PolicyType:           whereHelpernull_String{field: "\"safer_watch\".\"report\".\"policy_type\""},
	PolicyExpirationDate: whereHelpernull_String{field: "\"safer_watch\".\"report\".\"policy_expiration_date\""},
	PolicyRating:         whereHelpernull_String{field: "\"safer_watch\".\"report\".\"policy_rating\""},
	PolicyLimit:          whereHelpernull_String{field: "\"safer_watch\".\"report\".\"policy_limit\""},
	PolicyDeductible:     whereHelpernull_String{field: "\"safer_watch\".\"report\".\"policy_deductible\""},
}

// ReportRels is where relationship names are stored.
var ReportRels = struct {
	DotNumberRegistry string
}{
	DotNumberRegistry: "DotNumberRegistry",
}

// reportR is where relationships are stored.
type reportR struct {
	DotNumberRegistry *Registry `boil:"DotNumberRegistry" json:"DotNumberRegistry" toml:"DotNumberRegistry" yaml:"DotNumberRegistry"`
}

// NewStruct creates a new relationship struct
func (*reportR) NewStruct() *reportR {
	return &reportR{}
}

// reportL is where Load methods for each relationship are stored.
type reportL struct{}

var (
	reportAllColumns            = []string{"id", "dot_number", "policy_number", "producer_name", "producer_address", "producer_city", "producer_state", "producer_zip", "producer_phone", "producer_fax", "producer_email", "underwriter", "policy_type", "policy_expiration_date", "policy_rating", "policy_limit", "policy_deductible"}
	reportColumnsWithoutDefault = []string{"id", "dot_number"}
	reportColumnsWithDefault    = []string{"policy_number", "producer_name", "producer_address", "producer_city", "producer_state", "producer_zip", "producer_phone", "producer_fax", "producer_email", "underwriter", "policy_type", "policy_expiration_date", "policy_rating", "policy_limit", "policy_deductible"}
	reportPrimaryKeyColumns     = []string{"id"}
	reportGeneratedColumns      = []string{}
)

type (
	// ReportSlice is an alias for a slice of pointers to Report.
	// This should almost always be used instead of []Report.
	ReportSlice []*Report
	// ReportHook is the signature for custom Report hook methods
	ReportHook func(context.Context, boil.ContextExecutor, *Report) error

	reportQuery struct {
		*queries.Query
	}
)

// Cache for insert, update and upsert
var (
	reportType                 = reflect.TypeOf(&Report{})
	reportMapping              = queries.MakeStructMapping(reportType)
	reportPrimaryKeyMapping, _ = queries.BindMapping(reportType, reportMapping, reportPrimaryKeyColumns)
	reportInsertCacheMut       sync.RWMutex
	reportInsertCache          = make(map[string]insertCache)
	reportUpdateCacheMut       sync.RWMutex
	reportUpdateCache          = make(map[string]updateCache)
	reportUpsertCacheMut       sync.RWMutex
	reportUpsertCache          = make(map[string]insertCache)
)

var (
	// Force time package dependency for automated UpdatedAt/CreatedAt.
	_ = time.Second
	// Force qmhelper dependency for where clause generation (which doesn't
	// always happen)
	_ = qmhelper.Where
)

var reportAfterSelectHooks []ReportHook

var reportBeforeInsertHooks []ReportHook
var reportAfterInsertHooks []ReportHook

var reportBeforeUpdateHooks []ReportHook
var reportAfterUpdateHooks []ReportHook

var reportBeforeDeleteHooks []ReportHook
var reportAfterDeleteHooks []ReportHook

var reportBeforeUpsertHooks []ReportHook
var reportAfterUpsertHooks []ReportHook

// doAfterSelectHooks executes all "after Select" hooks.
func (o *Report) doAfterSelectHooks(ctx context.Context, exec boil.ContextExecutor) (err error) {
	if boil.HooksAreSkipped(ctx) {
		return nil
	}

	for _, hook := range reportAfterSelectHooks {
		if err := hook(ctx, exec, o); err != nil {
			return err
		}
	}

	return nil
}

// doBeforeInsertHooks executes all "before insert" hooks.
func (o *Report) doBeforeInsertHooks(ctx context.Context, exec boil.ContextExecutor) (err error) {
	if boil.HooksAreSkipped(ctx) {
		return nil
	}

	for _, hook := range reportBeforeInsertHooks {
		if err := hook(ctx, exec, o); err != nil {
			return err
		}
	}

	return nil
}

// doAfterInsertHooks executes all "after Insert" hooks.
func (o *Report) doAfterInsertHooks(ctx context.Context, exec boil.ContextExecutor) (err error) {
	if boil.HooksAreSkipped(ctx) {
		return nil
	}

	for _, hook := range reportAfterInsertHooks {
		if err := hook(ctx, exec, o); err != nil {
			return err
		}
	}

	return nil
}

// doBeforeUpdateHooks executes all "before Update" hooks.
func (o *Report) doBeforeUpdateHooks(ctx context.Context, exec boil.ContextExecutor) (err error) {
	if boil.HooksAreSkipped(ctx) {
		return nil
	}

	for _, hook := range reportBeforeUpdateHooks {
		if err := hook(ctx, exec, o); err != nil {
			return err
		}
	}

	return nil
}

// doAfterUpdateHooks executes all "after Update" hooks.
func (o *Report) doAfterUpdateHooks(ctx context.Context, exec boil.ContextExecutor) (err error) {
	if boil.HooksAreSkipped(ctx) {
		return nil
	}

	for _, hook := range reportAfterUpdateHooks {
		if err := hook(ctx, exec, o); err != nil {
			return err
		}
	}

	return nil
}

// doBeforeDeleteHooks executes all "before Delete" hooks.
func (o *Report) doBeforeDeleteHooks(ctx context.Context, exec boil.ContextExecutor) (err error) {
	if boil.HooksAreSkipped(ctx) {
		return nil
	}

	for _, hook := range reportBeforeDeleteHooks {
		if err := hook(ctx, exec, o); err != nil {
			return err
		}
	}

	return nil
}

// doAfterDeleteHooks executes all "after Delete" hooks.
func (o *Report) doAfterDeleteHooks(ctx context.Context, exec boil.ContextExecutor) (err error) {
	if boil.HooksAreSkipped(ctx) {
		return nil
	}

	for _, hook := range reportAfterDeleteHooks {
		if err := hook(ctx, exec, o); err != nil {
			return err
		}
	}

	return nil
}

// doBeforeUpsertHooks executes all "before Upsert" hooks.
func (o *Report) doBeforeUpsertHooks(ctx context.Context, exec boil.ContextExecutor) (err error) {
	if boil.HooksAreSkipped(ctx) {
		return nil
	}

	for _, hook := range reportBeforeUpsertHooks {
		if err := hook(ctx, exec, o); err != nil {
			return err
		}
	}

	return nil
}

// doAfterUpsertHooks executes all "after Upsert" hooks.
func (o *Report) doAfterUpsertHooks(ctx context.Context, exec boil.ContextExecutor) (err error) {
	if boil.HooksAreSkipped(ctx) {
		return nil
	}

	for _, hook := range reportAfterUpsertHooks {
		if err := hook(ctx, exec, o); err != nil {
			return err
		}
	}

	return nil
}

// AddReportHook registers your hook function for all future operations.
func AddReportHook(hookPoint boil.HookPoint, reportHook ReportHook) {
	switch hookPoint {
	case boil.AfterSelectHook:
		reportAfterSelectHooks = append(reportAfterSelectHooks, reportHook)
	case boil.BeforeInsertHook:
		reportBeforeInsertHooks = append(reportBeforeInsertHooks, reportHook)
	case boil.AfterInsertHook:
		reportAfterInsertHooks = append(reportAfterInsertHooks, reportHook)
	case boil.BeforeUpdateHook:
		reportBeforeUpdateHooks = append(reportBeforeUpdateHooks, reportHook)
	case boil.AfterUpdateHook:
		reportAfterUpdateHooks = append(reportAfterUpdateHooks, reportHook)
	case boil.BeforeDeleteHook:
		reportBeforeDeleteHooks = append(reportBeforeDeleteHooks, reportHook)
	case boil.AfterDeleteHook:
		reportAfterDeleteHooks = append(reportAfterDeleteHooks, reportHook)
	case boil.BeforeUpsertHook:
		reportBeforeUpsertHooks = append(reportBeforeUpsertHooks, reportHook)
	case boil.AfterUpsertHook:
		reportAfterUpsertHooks = append(reportAfterUpsertHooks, reportHook)
	}
}

// One returns a single report record from the query.
func (q reportQuery) One(ctx context.Context, exec boil.ContextExecutor) (*Report, error) {
	o := &Report{}

	queries.SetLimit(q.Query, 1)

	err := q.Bind(ctx, exec, o)
	if err != nil {
		if errors.Is(err, sql.ErrNoRows) {
			return nil, sql.ErrNoRows
		}
		return nil, errors.Wrap(err, "safer_watch: failed to execute a one query for report")
	}

	if err := o.doAfterSelectHooks(ctx, exec); err != nil {
		return o, err
	}

	return o, nil
}

// All returns all Report records from the query.
func (q reportQuery) All(ctx context.Context, exec boil.ContextExecutor) (ReportSlice, error) {
	var o []*Report

	err := q.Bind(ctx, exec, &o)
	if err != nil {
		return nil, errors.Wrap(err, "safer_watch: failed to assign all query results to Report slice")
	}

	if len(reportAfterSelectHooks) != 0 {
		for _, obj := range o {
			if err := obj.doAfterSelectHooks(ctx, exec); err != nil {
				return o, err
			}
		}
	}

	return o, nil
}

// Count returns the count of all Report records in the query.
func (q reportQuery) Count(ctx context.Context, exec boil.ContextExecutor) (int64, error) {
	var count int64

	queries.SetSelect(q.Query, nil)
	queries.SetCount(q.Query)

	err := q.Query.QueryRowContext(ctx, exec).Scan(&count)
	if err != nil {
		return 0, errors.Wrap(err, "safer_watch: failed to count report rows")
	}

	return count, nil
}

// Exists checks if the row exists in the table.
func (q reportQuery) Exists(ctx context.Context, exec boil.ContextExecutor) (bool, error) {
	var count int64

	queries.SetSelect(q.Query, nil)
	queries.SetCount(q.Query)
	queries.SetLimit(q.Query, 1)

	err := q.Query.QueryRowContext(ctx, exec).Scan(&count)
	if err != nil {
		return false, errors.Wrap(err, "safer_watch: failed to check if report exists")
	}

	return count > 0, nil
}

// DotNumberRegistry pointed to by the foreign key.
func (o *Report) DotNumberRegistry(mods ...qm.QueryMod) registryQuery {
	queryMods := []qm.QueryMod{
		qm.Where("\"dot_number\" = ?", o.DotNumber),
	}

	queryMods = append(queryMods, mods...)

	return Registries(queryMods...)
}

// LoadDotNumberRegistry allows an eager lookup of values, cached into the
// loaded structs of the objects. This is for an N-1 relationship.
func (reportL) LoadDotNumberRegistry(ctx context.Context, e boil.ContextExecutor, singular bool, maybeReport interface{}, mods queries.Applicator) error {
	var slice []*Report
	var object *Report

	if singular {
		object = maybeReport.(*Report)
	} else {
		slice = *maybeReport.(*[]*Report)
	}

	args := make([]interface{}, 0, 1)
	if singular {
		if object.R == nil {
			object.R = &reportR{}
		}
		args = append(args, object.DotNumber)

	} else {
	Outer:
		for _, obj := range slice {
			if obj.R == nil {
				obj.R = &reportR{}
			}

			for _, a := range args {
				if a == obj.DotNumber {
					continue Outer
				}
			}

			args = append(args, obj.DotNumber)

		}
	}

	if len(args) == 0 {
		return nil
	}

	query := NewQuery(
		qm.From(`safer_watch.registry`),
		qm.WhereIn(`safer_watch.registry.dot_number in ?`, args...),
	)
	if mods != nil {
		mods.Apply(query)
	}

	results, err := query.QueryContext(ctx, e)
	if err != nil {
		return errors.Wrap(err, "failed to eager load Registry")
	}

	var resultSlice []*Registry
	if err = queries.Bind(results, &resultSlice); err != nil {
		return errors.Wrap(err, "failed to bind eager loaded slice Registry")
	}

	if err = results.Close(); err != nil {
		return errors.Wrap(err, "failed to close results of eager load for registry")
	}
	if err = results.Err(); err != nil {
		return errors.Wrap(err, "error occurred during iteration of eager loaded relations for registry")
	}

	if len(reportAfterSelectHooks) != 0 {
		for _, obj := range resultSlice {
			if err := obj.doAfterSelectHooks(ctx, e); err != nil {
				return err
			}
		}
	}

	if len(resultSlice) == 0 {
		return nil
	}

	if singular {
		foreign := resultSlice[0]
		object.R.DotNumberRegistry = foreign
		if foreign.R == nil {
			foreign.R = &registryR{}
		}
		foreign.R.DotNumberReports = append(foreign.R.DotNumberReports, object)
		return nil
	}

	for _, local := range slice {
		for _, foreign := range resultSlice {
			if local.DotNumber == foreign.DotNumber {
				local.R.DotNumberRegistry = foreign
				if foreign.R == nil {
					foreign.R = &registryR{}
				}
				foreign.R.DotNumberReports = append(foreign.R.DotNumberReports, local)
				break
			}
		}
	}

	return nil
}

// SetDotNumberRegistry of the report to the related item.
// Sets o.R.DotNumberRegistry to related.
// Adds o to related.R.DotNumberReports.
func (o *Report) SetDotNumberRegistry(ctx context.Context, exec boil.ContextExecutor, insert bool, related *Registry) error {
	var err error
	if insert {
		if err = related.Insert(ctx, exec, boil.Infer()); err != nil {
			return errors.Wrap(err, "failed to insert into foreign table")
		}
	}

	updateQuery := fmt.Sprintf(
		"UPDATE \"safer_watch\".\"report\" SET %s WHERE %s",
		strmangle.SetParamNames("\"", "\"", 1, []string{"dot_number"}),
		strmangle.WhereClause("\"", "\"", 2, reportPrimaryKeyColumns),
	)
	values := []interface{}{related.DotNumber, o.ID}

	if boil.IsDebug(ctx) {
		writer := boil.DebugWriterFrom(ctx)
		fmt.Fprintln(writer, updateQuery)
		fmt.Fprintln(writer, values)
	}
	if _, err = exec.ExecContext(ctx, updateQuery, values...); err != nil {
		return errors.Wrap(err, "failed to update local table")
	}

	o.DotNumber = related.DotNumber
	if o.R == nil {
		o.R = &reportR{
			DotNumberRegistry: related,
		}
	} else {
		o.R.DotNumberRegistry = related
	}

	if related.R == nil {
		related.R = &registryR{
			DotNumberReports: ReportSlice{o},
		}
	} else {
		related.R.DotNumberReports = append(related.R.DotNumberReports, o)
	}

	return nil
}

// Reports retrieves all the records using an executor.
func Reports(mods ...qm.QueryMod) reportQuery {
	mods = append(mods, qm.From("\"safer_watch\".\"report\""))
	q := NewQuery(mods...)
	if len(queries.GetSelect(q)) == 0 {
		queries.SetSelect(q, []string{"\"safer_watch\".\"report\".*"})
	}

	return reportQuery{q}
}

// FindReport retrieves a single record by ID with an executor.
// If selectCols is empty Find will return all columns.
func FindReport(ctx context.Context, exec boil.ContextExecutor, iD string, selectCols ...string) (*Report, error) {
	reportObj := &Report{}

	sel := "*"
	if len(selectCols) > 0 {
		sel = strings.Join(strmangle.IdentQuoteSlice(dialect.LQ, dialect.RQ, selectCols), ",")
	}
	query := fmt.Sprintf(
		"select %s from \"safer_watch\".\"report\" where \"id\"=$1", sel,
	)

	q := queries.Raw(query, iD)

	err := q.Bind(ctx, exec, reportObj)
	if err != nil {
		if errors.Is(err, sql.ErrNoRows) {
			return nil, sql.ErrNoRows
		}
		return nil, errors.Wrap(err, "safer_watch: unable to select from report")
	}

	if err = reportObj.doAfterSelectHooks(ctx, exec); err != nil {
		return reportObj, err
	}

	return reportObj, nil
}

// Insert a single record using an executor.
// See boil.Columns.InsertColumnSet documentation to understand column list inference for inserts.
func (o *Report) Insert(ctx context.Context, exec boil.ContextExecutor, columns boil.Columns) error {
	if o == nil {
		return errors.New("safer_watch: no report provided for insertion")
	}

	var err error

	if err := o.doBeforeInsertHooks(ctx, exec); err != nil {
		return err
	}

	nzDefaults := queries.NonZeroDefaultSet(reportColumnsWithDefault, o)

	key := makeCacheKey(columns, nzDefaults)
	reportInsertCacheMut.RLock()
	cache, cached := reportInsertCache[key]
	reportInsertCacheMut.RUnlock()

	if !cached {
		wl, returnColumns := columns.InsertColumnSet(
			reportAllColumns,
			reportColumnsWithDefault,
			reportColumnsWithoutDefault,
			nzDefaults,
		)

		cache.valueMapping, err = queries.BindMapping(reportType, reportMapping, wl)
		if err != nil {
			return err
		}
		cache.retMapping, err = queries.BindMapping(reportType, reportMapping, returnColumns)
		if err != nil {
			return err
		}
		if len(wl) != 0 {
			cache.query = fmt.Sprintf("INSERT INTO \"safer_watch\".\"report\" (\"%s\") %%sVALUES (%s)%%s", strings.Join(wl, "\",\""), strmangle.Placeholders(dialect.UseIndexPlaceholders, len(wl), 1, 1))
		} else {
			cache.query = "INSERT INTO \"safer_watch\".\"report\" %sDEFAULT VALUES%s"
		}

		var queryOutput, queryReturning string

		if len(cache.retMapping) != 0 {
			queryReturning = fmt.Sprintf(" RETURNING \"%s\"", strings.Join(returnColumns, "\",\""))
		}

		cache.query = fmt.Sprintf(cache.query, queryOutput, queryReturning)
	}

	value := reflect.Indirect(reflect.ValueOf(o))
	vals := queries.ValuesFromMapping(value, cache.valueMapping)

	if boil.IsDebug(ctx) {
		writer := boil.DebugWriterFrom(ctx)
		fmt.Fprintln(writer, cache.query)
		fmt.Fprintln(writer, vals)
	}

	if len(cache.retMapping) != 0 {
		err = exec.QueryRowContext(ctx, cache.query, vals...).Scan(queries.PtrsFromMapping(value, cache.retMapping)...)
	} else {
		_, err = exec.ExecContext(ctx, cache.query, vals...)
	}

	if err != nil {
		return errors.Wrap(err, "safer_watch: unable to insert into report")
	}

	if !cached {
		reportInsertCacheMut.Lock()
		reportInsertCache[key] = cache
		reportInsertCacheMut.Unlock()
	}

	return o.doAfterInsertHooks(ctx, exec)
}

// Update uses an executor to update the Report.
// See boil.Columns.UpdateColumnSet documentation to understand column list inference for updates.
// Update does not automatically update the record in case of default values. Use .Reload() to refresh the records.
func (o *Report) Update(ctx context.Context, exec boil.ContextExecutor, columns boil.Columns) (int64, error) {
	var err error
	if err = o.doBeforeUpdateHooks(ctx, exec); err != nil {
		return 0, err
	}
	key := makeCacheKey(columns, nil)
	reportUpdateCacheMut.RLock()
	cache, cached := reportUpdateCache[key]
	reportUpdateCacheMut.RUnlock()

	if !cached {
		wl := columns.UpdateColumnSet(
			reportAllColumns,
			reportPrimaryKeyColumns,
		)

		if !columns.IsWhitelist() {
			wl = strmangle.SetComplement(wl, []string{"created_at"})
		}
		if len(wl) == 0 {
			return 0, errors.New("safer_watch: unable to update report, could not build whitelist")
		}

		cache.query = fmt.Sprintf("UPDATE \"safer_watch\".\"report\" SET %s WHERE %s",
			strmangle.SetParamNames("\"", "\"", 1, wl),
			strmangle.WhereClause("\"", "\"", len(wl)+1, reportPrimaryKeyColumns),
		)
		cache.valueMapping, err = queries.BindMapping(reportType, reportMapping, append(wl, reportPrimaryKeyColumns...))
		if err != nil {
			return 0, err
		}
	}

	values := queries.ValuesFromMapping(reflect.Indirect(reflect.ValueOf(o)), cache.valueMapping)

	if boil.IsDebug(ctx) {
		writer := boil.DebugWriterFrom(ctx)
		fmt.Fprintln(writer, cache.query)
		fmt.Fprintln(writer, values)
	}
	var result sql.Result
	result, err = exec.ExecContext(ctx, cache.query, values...)
	if err != nil {
		return 0, errors.Wrap(err, "safer_watch: unable to update report row")
	}

	rowsAff, err := result.RowsAffected()
	if err != nil {
		return 0, errors.Wrap(err, "safer_watch: failed to get rows affected by update for report")
	}

	if !cached {
		reportUpdateCacheMut.Lock()
		reportUpdateCache[key] = cache
		reportUpdateCacheMut.Unlock()
	}

	return rowsAff, o.doAfterUpdateHooks(ctx, exec)
}

// UpdateAll updates all rows with the specified column values.
func (q reportQuery) UpdateAll(ctx context.Context, exec boil.ContextExecutor, cols M) (int64, error) {
	queries.SetUpdate(q.Query, cols)

	result, err := q.Query.ExecContext(ctx, exec)
	if err != nil {
		return 0, errors.Wrap(err, "safer_watch: unable to update all for report")
	}

	rowsAff, err := result.RowsAffected()
	if err != nil {
		return 0, errors.Wrap(err, "safer_watch: unable to retrieve rows affected for report")
	}

	return rowsAff, nil
}

// UpdateAll updates all rows with the specified column values, using an executor.
func (o ReportSlice) UpdateAll(ctx context.Context, exec boil.ContextExecutor, cols M) (int64, error) {
	ln := int64(len(o))
	if ln == 0 {
		return 0, nil
	}

	if len(cols) == 0 {
		return 0, errors.New("safer_watch: update all requires at least one column argument")
	}

	colNames := make([]string, len(cols))
	args := make([]interface{}, len(cols))

	i := 0
	for name, value := range cols {
		colNames[i] = name
		args[i] = value
		i++
	}

	// Append all of the primary key values for each column
	for _, obj := range o {
		pkeyArgs := queries.ValuesFromMapping(reflect.Indirect(reflect.ValueOf(obj)), reportPrimaryKeyMapping)
		args = append(args, pkeyArgs...)
	}

	sql := fmt.Sprintf("UPDATE \"safer_watch\".\"report\" SET %s WHERE %s",
		strmangle.SetParamNames("\"", "\"", 1, colNames),
		strmangle.WhereClauseRepeated(string(dialect.LQ), string(dialect.RQ), len(colNames)+1, reportPrimaryKeyColumns, len(o)))

	if boil.IsDebug(ctx) {
		writer := boil.DebugWriterFrom(ctx)
		fmt.Fprintln(writer, sql)
		fmt.Fprintln(writer, args...)
	}
	result, err := exec.ExecContext(ctx, sql, args...)
	if err != nil {
		return 0, errors.Wrap(err, "safer_watch: unable to update all in report slice")
	}

	rowsAff, err := result.RowsAffected()
	if err != nil {
		return 0, errors.Wrap(err, "safer_watch: unable to retrieve rows affected all in update all report")
	}
	return rowsAff, nil
}

// Upsert attempts an insert using an executor, and does an update or ignore on conflict.
// See boil.Columns documentation for how to properly use updateColumns and insertColumns.
func (o *Report) Upsert(ctx context.Context, exec boil.ContextExecutor, updateOnConflict bool, conflictColumns []string, updateColumns, insertColumns boil.Columns) error {
	if o == nil {
		return errors.New("safer_watch: no report provided for upsert")
	}

	if err := o.doBeforeUpsertHooks(ctx, exec); err != nil {
		return err
	}

	nzDefaults := queries.NonZeroDefaultSet(reportColumnsWithDefault, o)

	// Build cache key in-line uglily - mysql vs psql problems
	buf := strmangle.GetBuffer()
	if updateOnConflict {
		buf.WriteByte('t')
	} else {
		buf.WriteByte('f')
	}
	buf.WriteByte('.')
	for _, c := range conflictColumns {
		buf.WriteString(c)
	}
	buf.WriteByte('.')
	buf.WriteString(strconv.Itoa(updateColumns.Kind))
	for _, c := range updateColumns.Cols {
		buf.WriteString(c)
	}
	buf.WriteByte('.')
	buf.WriteString(strconv.Itoa(insertColumns.Kind))
	for _, c := range insertColumns.Cols {
		buf.WriteString(c)
	}
	buf.WriteByte('.')
	for _, c := range nzDefaults {
		buf.WriteString(c)
	}
	key := buf.String()
	strmangle.PutBuffer(buf)

	reportUpsertCacheMut.RLock()
	cache, cached := reportUpsertCache[key]
	reportUpsertCacheMut.RUnlock()

	var err error

	if !cached {
		insert, ret := insertColumns.InsertColumnSet(
			reportAllColumns,
			reportColumnsWithDefault,
			reportColumnsWithoutDefault,
			nzDefaults,
		)

		update := updateColumns.UpdateColumnSet(
			reportAllColumns,
			reportPrimaryKeyColumns,
		)

		if updateOnConflict && len(update) == 0 {
			return errors.New("safer_watch: unable to upsert report, could not build update column list")
		}

		conflict := conflictColumns
		if len(conflict) == 0 {
			conflict = make([]string, len(reportPrimaryKeyColumns))
			copy(conflict, reportPrimaryKeyColumns)
		}
		cache.query = buildUpsertQueryPostgres(dialect, "\"safer_watch\".\"report\"", updateOnConflict, ret, update, conflict, insert)

		cache.valueMapping, err = queries.BindMapping(reportType, reportMapping, insert)
		if err != nil {
			return err
		}
		if len(ret) != 0 {
			cache.retMapping, err = queries.BindMapping(reportType, reportMapping, ret)
			if err != nil {
				return err
			}
		}
	}

	value := reflect.Indirect(reflect.ValueOf(o))
	vals := queries.ValuesFromMapping(value, cache.valueMapping)
	var returns []interface{}
	if len(cache.retMapping) != 0 {
		returns = queries.PtrsFromMapping(value, cache.retMapping)
	}

	if boil.IsDebug(ctx) {
		writer := boil.DebugWriterFrom(ctx)
		fmt.Fprintln(writer, cache.query)
		fmt.Fprintln(writer, vals)
	}
	if len(cache.retMapping) != 0 {
		err = exec.QueryRowContext(ctx, cache.query, vals...).Scan(returns...)
		if errors.Is(err, sql.ErrNoRows) {
			err = nil // Postgres doesn't return anything when there's no update
		}
	} else {
		_, err = exec.ExecContext(ctx, cache.query, vals...)
	}
	if err != nil {
		return errors.Wrap(err, "safer_watch: unable to upsert report")
	}

	if !cached {
		reportUpsertCacheMut.Lock()
		reportUpsertCache[key] = cache
		reportUpsertCacheMut.Unlock()
	}

	return o.doAfterUpsertHooks(ctx, exec)
}

// Delete deletes a single Report record with an executor.
// Delete will match against the primary key column to find the record to delete.
func (o *Report) Delete(ctx context.Context, exec boil.ContextExecutor) (int64, error) {
	if o == nil {
		return 0, errors.New("safer_watch: no Report provided for delete")
	}

	if err := o.doBeforeDeleteHooks(ctx, exec); err != nil {
		return 0, err
	}

	args := queries.ValuesFromMapping(reflect.Indirect(reflect.ValueOf(o)), reportPrimaryKeyMapping)
	sql := "DELETE FROM \"safer_watch\".\"report\" WHERE \"id\"=$1"

	if boil.IsDebug(ctx) {
		writer := boil.DebugWriterFrom(ctx)
		fmt.Fprintln(writer, sql)
		fmt.Fprintln(writer, args...)
	}
	result, err := exec.ExecContext(ctx, sql, args...)
	if err != nil {
		return 0, errors.Wrap(err, "safer_watch: unable to delete from report")
	}

	rowsAff, err := result.RowsAffected()
	if err != nil {
		return 0, errors.Wrap(err, "safer_watch: failed to get rows affected by delete for report")
	}

	if err := o.doAfterDeleteHooks(ctx, exec); err != nil {
		return 0, err
	}

	return rowsAff, nil
}

// DeleteAll deletes all matching rows.
func (q reportQuery) DeleteAll(ctx context.Context, exec boil.ContextExecutor) (int64, error) {
	if q.Query == nil {
		return 0, errors.New("safer_watch: no reportQuery provided for delete all")
	}

	queries.SetDelete(q.Query)

	result, err := q.Query.ExecContext(ctx, exec)
	if err != nil {
		return 0, errors.Wrap(err, "safer_watch: unable to delete all from report")
	}

	rowsAff, err := result.RowsAffected()
	if err != nil {
		return 0, errors.Wrap(err, "safer_watch: failed to get rows affected by deleteall for report")
	}

	return rowsAff, nil
}

// DeleteAll deletes all rows in the slice, using an executor.
func (o ReportSlice) DeleteAll(ctx context.Context, exec boil.ContextExecutor) (int64, error) {
	if len(o) == 0 {
		return 0, nil
	}

	if len(reportBeforeDeleteHooks) != 0 {
		for _, obj := range o {
			if err := obj.doBeforeDeleteHooks(ctx, exec); err != nil {
				return 0, err
			}
		}
	}

	var args []interface{}
	for _, obj := range o {
		pkeyArgs := queries.ValuesFromMapping(reflect.Indirect(reflect.ValueOf(obj)), reportPrimaryKeyMapping)
		args = append(args, pkeyArgs...)
	}

	sql := "DELETE FROM \"safer_watch\".\"report\" WHERE " +
		strmangle.WhereClauseRepeated(string(dialect.LQ), string(dialect.RQ), 1, reportPrimaryKeyColumns, len(o))

	if boil.IsDebug(ctx) {
		writer := boil.DebugWriterFrom(ctx)
		fmt.Fprintln(writer, sql)
		fmt.Fprintln(writer, args)
	}
	result, err := exec.ExecContext(ctx, sql, args...)
	if err != nil {
		return 0, errors.Wrap(err, "safer_watch: unable to delete all from report slice")
	}

	rowsAff, err := result.RowsAffected()
	if err != nil {
		return 0, errors.Wrap(err, "safer_watch: failed to get rows affected by deleteall for report")
	}

	if len(reportAfterDeleteHooks) != 0 {
		for _, obj := range o {
			if err := obj.doAfterDeleteHooks(ctx, exec); err != nil {
				return 0, err
			}
		}
	}

	return rowsAff, nil
}

// Reload refetches the object from the database
// using the primary keys with an executor.
func (o *Report) Reload(ctx context.Context, exec boil.ContextExecutor) error {
	ret, err := FindReport(ctx, exec, o.ID)
	if err != nil {
		return err
	}

	*o = *ret
	return nil
}

// ReloadAll refetches every row with matching primary key column values
// and overwrites the original object slice with the newly updated slice.
func (o *ReportSlice) ReloadAll(ctx context.Context, exec boil.ContextExecutor) error {
	if o == nil || len(*o) == 0 {
		return nil
	}

	slice := ReportSlice{}
	var args []interface{}
	for _, obj := range *o {
		pkeyArgs := queries.ValuesFromMapping(reflect.Indirect(reflect.ValueOf(obj)), reportPrimaryKeyMapping)
		args = append(args, pkeyArgs...)
	}

	sql := "SELECT \"safer_watch\".\"report\".* FROM \"safer_watch\".\"report\" WHERE " +
		strmangle.WhereClauseRepeated(string(dialect.LQ), string(dialect.RQ), 1, reportPrimaryKeyColumns, len(*o))

	q := queries.Raw(sql, args...)

	err := q.Bind(ctx, exec, &slice)
	if err != nil {
		return errors.Wrap(err, "safer_watch: unable to reload all in ReportSlice")
	}

	*o = slice

	return nil
}

// ReportExists checks if the Report row exists.
func ReportExists(ctx context.Context, exec boil.ContextExecutor, iD string) (bool, error) {
	var exists bool
	sql := "select exists(select 1 from \"safer_watch\".\"report\" where \"id\"=$1 limit 1)"

	if boil.IsDebug(ctx) {
		writer := boil.DebugWriterFrom(ctx)
		fmt.Fprintln(writer, sql)
		fmt.Fprintln(writer, iD)
	}
	row := exec.QueryRowContext(ctx, sql, iD)

	err := row.Scan(&exists)
	if err != nil {
		return false, errors.Wrap(err, "safer_watch: unable to check if report exists")
	}

	return exists, nil
}
