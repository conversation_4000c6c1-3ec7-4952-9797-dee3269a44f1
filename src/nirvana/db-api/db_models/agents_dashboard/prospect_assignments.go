// Code generated by SQLBoiler (https://github.com/volatiletech/sqlboiler). DO NOT EDIT.
// This file is meant to be re-generated in place and/or deleted at any time.

package agents_dashboard

import (
	"context"
	"database/sql"
	"fmt"
	"reflect"
	"strconv"
	"strings"
	"sync"
	"time"

	"github.com/friendsofgo/errors"
	"github.com/volatiletech/null/v8"
	"github.com/volatiletech/sqlboiler/v4/boil"
	"github.com/volatiletech/sqlboiler/v4/queries"
	"github.com/volatiletech/sqlboiler/v4/queries/qm"
	"github.com/volatiletech/sqlboiler/v4/queries/qmhelper"
	"github.com/volatiletech/strmangle"
)

// ProspectAssignment is an object representing the database table.
type ProspectAssignment struct {
	ID          string    `boil:"id" json:"id" toml:"id" yaml:"id"`
	ProspectID  string    `boil:"prospect_id" json:"prospect_id" toml:"prospect_id" yaml:"prospect_id"`
	AgentID     string    `boil:"agent_id" json:"agent_id" toml:"agent_id" yaml:"agent_id"`
	AssignedAt  null.Time `boil:"assigned_at" json:"assigned_at,omitempty" toml:"assigned_at" yaml:"assigned_at,omitempty"`
	UpdatedAt   null.Time `boil:"updated_at" json:"updated_at,omitempty" toml:"updated_at" yaml:"updated_at,omitempty"`
	DismissedAt null.Time `boil:"dismissed_at" json:"dismissed_at,omitempty" toml:"dismissed_at" yaml:"dismissed_at,omitempty"`
	ArchivedAt  null.Time `boil:"archived_at" json:"archived_at,omitempty" toml:"archived_at" yaml:"archived_at,omitempty"`

	R *prospectAssignmentR `boil:"" json:"" toml:"" yaml:""`
	L prospectAssignmentL  `boil:"-" json:"-" toml:"-" yaml:"-"`
}

var ProspectAssignmentColumns = struct {
	ID          string
	ProspectID  string
	AgentID     string
	AssignedAt  string
	UpdatedAt   string
	DismissedAt string
	ArchivedAt  string
}{
	ID:          "id",
	ProspectID:  "prospect_id",
	AgentID:     "agent_id",
	AssignedAt:  "assigned_at",
	UpdatedAt:   "updated_at",
	DismissedAt: "dismissed_at",
	ArchivedAt:  "archived_at",
}

var ProspectAssignmentTableColumns = struct {
	ID          string
	ProspectID  string
	AgentID     string
	AssignedAt  string
	UpdatedAt   string
	DismissedAt string
	ArchivedAt  string
}{
	ID:          "prospect_assignments.id",
	ProspectID:  "prospect_assignments.prospect_id",
	AgentID:     "prospect_assignments.agent_id",
	AssignedAt:  "prospect_assignments.assigned_at",
	UpdatedAt:   "prospect_assignments.updated_at",
	DismissedAt: "prospect_assignments.dismissed_at",
	ArchivedAt:  "prospect_assignments.archived_at",
}

// Generated where

type whereHelperstring struct{ field string }

func (w whereHelperstring) EQ(x string) qm.QueryMod  { return qmhelper.Where(w.field, qmhelper.EQ, x) }
func (w whereHelperstring) NEQ(x string) qm.QueryMod { return qmhelper.Where(w.field, qmhelper.NEQ, x) }
func (w whereHelperstring) LT(x string) qm.QueryMod  { return qmhelper.Where(w.field, qmhelper.LT, x) }
func (w whereHelperstring) LTE(x string) qm.QueryMod { return qmhelper.Where(w.field, qmhelper.LTE, x) }
func (w whereHelperstring) GT(x string) qm.QueryMod  { return qmhelper.Where(w.field, qmhelper.GT, x) }
func (w whereHelperstring) GTE(x string) qm.QueryMod { return qmhelper.Where(w.field, qmhelper.GTE, x) }
func (w whereHelperstring) IN(slice []string) qm.QueryMod {
	values := make([]interface{}, 0, len(slice))
	for _, value := range slice {
		values = append(values, value)
	}
	return qm.WhereIn(fmt.Sprintf("%s IN ?", w.field), values...)
}
func (w whereHelperstring) NIN(slice []string) qm.QueryMod {
	values := make([]interface{}, 0, len(slice))
	for _, value := range slice {
		values = append(values, value)
	}
	return qm.WhereNotIn(fmt.Sprintf("%s NOT IN ?", w.field), values...)
}

type whereHelpernull_Time struct{ field string }

func (w whereHelpernull_Time) EQ(x null.Time) qm.QueryMod {
	return qmhelper.WhereNullEQ(w.field, false, x)
}
func (w whereHelpernull_Time) NEQ(x null.Time) qm.QueryMod {
	return qmhelper.WhereNullEQ(w.field, true, x)
}
func (w whereHelpernull_Time) LT(x null.Time) qm.QueryMod {
	return qmhelper.Where(w.field, qmhelper.LT, x)
}
func (w whereHelpernull_Time) LTE(x null.Time) qm.QueryMod {
	return qmhelper.Where(w.field, qmhelper.LTE, x)
}
func (w whereHelpernull_Time) GT(x null.Time) qm.QueryMod {
	return qmhelper.Where(w.field, qmhelper.GT, x)
}
func (w whereHelpernull_Time) GTE(x null.Time) qm.QueryMod {
	return qmhelper.Where(w.field, qmhelper.GTE, x)
}

func (w whereHelpernull_Time) IsNull() qm.QueryMod    { return qmhelper.WhereIsNull(w.field) }
func (w whereHelpernull_Time) IsNotNull() qm.QueryMod { return qmhelper.WhereIsNotNull(w.field) }

var ProspectAssignmentWhere = struct {
	ID          whereHelperstring
	ProspectID  whereHelperstring
	AgentID     whereHelperstring
	AssignedAt  whereHelpernull_Time
	UpdatedAt   whereHelpernull_Time
	DismissedAt whereHelpernull_Time
	ArchivedAt  whereHelpernull_Time
}{
	ID:          whereHelperstring{field: "\"agents_dashboard\".\"prospect_assignments\".\"id\""},
	ProspectID:  whereHelperstring{field: "\"agents_dashboard\".\"prospect_assignments\".\"prospect_id\""},
	AgentID:     whereHelperstring{field: "\"agents_dashboard\".\"prospect_assignments\".\"agent_id\""},
	AssignedAt:  whereHelpernull_Time{field: "\"agents_dashboard\".\"prospect_assignments\".\"assigned_at\""},
	UpdatedAt:   whereHelpernull_Time{field: "\"agents_dashboard\".\"prospect_assignments\".\"updated_at\""},
	DismissedAt: whereHelpernull_Time{field: "\"agents_dashboard\".\"prospect_assignments\".\"dismissed_at\""},
	ArchivedAt:  whereHelpernull_Time{field: "\"agents_dashboard\".\"prospect_assignments\".\"archived_at\""},
}

// ProspectAssignmentRels is where relationship names are stored.
var ProspectAssignmentRels = struct {
	Prospect string
}{
	Prospect: "Prospect",
}

// prospectAssignmentR is where relationships are stored.
type prospectAssignmentR struct {
	Prospect *Prospect `boil:"Prospect" json:"Prospect" toml:"Prospect" yaml:"Prospect"`
}

// NewStruct creates a new relationship struct
func (*prospectAssignmentR) NewStruct() *prospectAssignmentR {
	return &prospectAssignmentR{}
}

// prospectAssignmentL is where Load methods for each relationship are stored.
type prospectAssignmentL struct{}

var (
	prospectAssignmentAllColumns            = []string{"id", "prospect_id", "agent_id", "assigned_at", "updated_at", "dismissed_at", "archived_at"}
	prospectAssignmentColumnsWithoutDefault = []string{"id", "prospect_id", "agent_id"}
	prospectAssignmentColumnsWithDefault    = []string{"assigned_at", "updated_at", "dismissed_at", "archived_at"}
	prospectAssignmentPrimaryKeyColumns     = []string{"id"}
	prospectAssignmentGeneratedColumns      = []string{}
)

type (
	// ProspectAssignmentSlice is an alias for a slice of pointers to ProspectAssignment.
	// This should almost always be used instead of []ProspectAssignment.
	ProspectAssignmentSlice []*ProspectAssignment
	// ProspectAssignmentHook is the signature for custom ProspectAssignment hook methods
	ProspectAssignmentHook func(context.Context, boil.ContextExecutor, *ProspectAssignment) error

	prospectAssignmentQuery struct {
		*queries.Query
	}
)

// Cache for insert, update and upsert
var (
	prospectAssignmentType                 = reflect.TypeOf(&ProspectAssignment{})
	prospectAssignmentMapping              = queries.MakeStructMapping(prospectAssignmentType)
	prospectAssignmentPrimaryKeyMapping, _ = queries.BindMapping(prospectAssignmentType, prospectAssignmentMapping, prospectAssignmentPrimaryKeyColumns)
	prospectAssignmentInsertCacheMut       sync.RWMutex
	prospectAssignmentInsertCache          = make(map[string]insertCache)
	prospectAssignmentUpdateCacheMut       sync.RWMutex
	prospectAssignmentUpdateCache          = make(map[string]updateCache)
	prospectAssignmentUpsertCacheMut       sync.RWMutex
	prospectAssignmentUpsertCache          = make(map[string]insertCache)
)

var (
	// Force time package dependency for automated UpdatedAt/CreatedAt.
	_ = time.Second
	// Force qmhelper dependency for where clause generation (which doesn't
	// always happen)
	_ = qmhelper.Where
)

var prospectAssignmentAfterSelectHooks []ProspectAssignmentHook

var prospectAssignmentBeforeInsertHooks []ProspectAssignmentHook
var prospectAssignmentAfterInsertHooks []ProspectAssignmentHook

var prospectAssignmentBeforeUpdateHooks []ProspectAssignmentHook
var prospectAssignmentAfterUpdateHooks []ProspectAssignmentHook

var prospectAssignmentBeforeDeleteHooks []ProspectAssignmentHook
var prospectAssignmentAfterDeleteHooks []ProspectAssignmentHook

var prospectAssignmentBeforeUpsertHooks []ProspectAssignmentHook
var prospectAssignmentAfterUpsertHooks []ProspectAssignmentHook

// doAfterSelectHooks executes all "after Select" hooks.
func (o *ProspectAssignment) doAfterSelectHooks(ctx context.Context, exec boil.ContextExecutor) (err error) {
	if boil.HooksAreSkipped(ctx) {
		return nil
	}

	for _, hook := range prospectAssignmentAfterSelectHooks {
		if err := hook(ctx, exec, o); err != nil {
			return err
		}
	}

	return nil
}

// doBeforeInsertHooks executes all "before insert" hooks.
func (o *ProspectAssignment) doBeforeInsertHooks(ctx context.Context, exec boil.ContextExecutor) (err error) {
	if boil.HooksAreSkipped(ctx) {
		return nil
	}

	for _, hook := range prospectAssignmentBeforeInsertHooks {
		if err := hook(ctx, exec, o); err != nil {
			return err
		}
	}

	return nil
}

// doAfterInsertHooks executes all "after Insert" hooks.
func (o *ProspectAssignment) doAfterInsertHooks(ctx context.Context, exec boil.ContextExecutor) (err error) {
	if boil.HooksAreSkipped(ctx) {
		return nil
	}

	for _, hook := range prospectAssignmentAfterInsertHooks {
		if err := hook(ctx, exec, o); err != nil {
			return err
		}
	}

	return nil
}

// doBeforeUpdateHooks executes all "before Update" hooks.
func (o *ProspectAssignment) doBeforeUpdateHooks(ctx context.Context, exec boil.ContextExecutor) (err error) {
	if boil.HooksAreSkipped(ctx) {
		return nil
	}

	for _, hook := range prospectAssignmentBeforeUpdateHooks {
		if err := hook(ctx, exec, o); err != nil {
			return err
		}
	}

	return nil
}

// doAfterUpdateHooks executes all "after Update" hooks.
func (o *ProspectAssignment) doAfterUpdateHooks(ctx context.Context, exec boil.ContextExecutor) (err error) {
	if boil.HooksAreSkipped(ctx) {
		return nil
	}

	for _, hook := range prospectAssignmentAfterUpdateHooks {
		if err := hook(ctx, exec, o); err != nil {
			return err
		}
	}

	return nil
}

// doBeforeDeleteHooks executes all "before Delete" hooks.
func (o *ProspectAssignment) doBeforeDeleteHooks(ctx context.Context, exec boil.ContextExecutor) (err error) {
	if boil.HooksAreSkipped(ctx) {
		return nil
	}

	for _, hook := range prospectAssignmentBeforeDeleteHooks {
		if err := hook(ctx, exec, o); err != nil {
			return err
		}
	}

	return nil
}

// doAfterDeleteHooks executes all "after Delete" hooks.
func (o *ProspectAssignment) doAfterDeleteHooks(ctx context.Context, exec boil.ContextExecutor) (err error) {
	if boil.HooksAreSkipped(ctx) {
		return nil
	}

	for _, hook := range prospectAssignmentAfterDeleteHooks {
		if err := hook(ctx, exec, o); err != nil {
			return err
		}
	}

	return nil
}

// doBeforeUpsertHooks executes all "before Upsert" hooks.
func (o *ProspectAssignment) doBeforeUpsertHooks(ctx context.Context, exec boil.ContextExecutor) (err error) {
	if boil.HooksAreSkipped(ctx) {
		return nil
	}

	for _, hook := range prospectAssignmentBeforeUpsertHooks {
		if err := hook(ctx, exec, o); err != nil {
			return err
		}
	}

	return nil
}

// doAfterUpsertHooks executes all "after Upsert" hooks.
func (o *ProspectAssignment) doAfterUpsertHooks(ctx context.Context, exec boil.ContextExecutor) (err error) {
	if boil.HooksAreSkipped(ctx) {
		return nil
	}

	for _, hook := range prospectAssignmentAfterUpsertHooks {
		if err := hook(ctx, exec, o); err != nil {
			return err
		}
	}

	return nil
}

// AddProspectAssignmentHook registers your hook function for all future operations.
func AddProspectAssignmentHook(hookPoint boil.HookPoint, prospectAssignmentHook ProspectAssignmentHook) {
	switch hookPoint {
	case boil.AfterSelectHook:
		prospectAssignmentAfterSelectHooks = append(prospectAssignmentAfterSelectHooks, prospectAssignmentHook)
	case boil.BeforeInsertHook:
		prospectAssignmentBeforeInsertHooks = append(prospectAssignmentBeforeInsertHooks, prospectAssignmentHook)
	case boil.AfterInsertHook:
		prospectAssignmentAfterInsertHooks = append(prospectAssignmentAfterInsertHooks, prospectAssignmentHook)
	case boil.BeforeUpdateHook:
		prospectAssignmentBeforeUpdateHooks = append(prospectAssignmentBeforeUpdateHooks, prospectAssignmentHook)
	case boil.AfterUpdateHook:
		prospectAssignmentAfterUpdateHooks = append(prospectAssignmentAfterUpdateHooks, prospectAssignmentHook)
	case boil.BeforeDeleteHook:
		prospectAssignmentBeforeDeleteHooks = append(prospectAssignmentBeforeDeleteHooks, prospectAssignmentHook)
	case boil.AfterDeleteHook:
		prospectAssignmentAfterDeleteHooks = append(prospectAssignmentAfterDeleteHooks, prospectAssignmentHook)
	case boil.BeforeUpsertHook:
		prospectAssignmentBeforeUpsertHooks = append(prospectAssignmentBeforeUpsertHooks, prospectAssignmentHook)
	case boil.AfterUpsertHook:
		prospectAssignmentAfterUpsertHooks = append(prospectAssignmentAfterUpsertHooks, prospectAssignmentHook)
	}
}

// One returns a single prospectAssignment record from the query.
func (q prospectAssignmentQuery) One(ctx context.Context, exec boil.ContextExecutor) (*ProspectAssignment, error) {
	o := &ProspectAssignment{}

	queries.SetLimit(q.Query, 1)

	err := q.Bind(ctx, exec, o)
	if err != nil {
		if errors.Is(err, sql.ErrNoRows) {
			return nil, sql.ErrNoRows
		}
		return nil, errors.Wrap(err, "agents_dashboard: failed to execute a one query for prospect_assignments")
	}

	if err := o.doAfterSelectHooks(ctx, exec); err != nil {
		return o, err
	}

	return o, nil
}

// All returns all ProspectAssignment records from the query.
func (q prospectAssignmentQuery) All(ctx context.Context, exec boil.ContextExecutor) (ProspectAssignmentSlice, error) {
	var o []*ProspectAssignment

	err := q.Bind(ctx, exec, &o)
	if err != nil {
		return nil, errors.Wrap(err, "agents_dashboard: failed to assign all query results to ProspectAssignment slice")
	}

	if len(prospectAssignmentAfterSelectHooks) != 0 {
		for _, obj := range o {
			if err := obj.doAfterSelectHooks(ctx, exec); err != nil {
				return o, err
			}
		}
	}

	return o, nil
}

// Count returns the count of all ProspectAssignment records in the query.
func (q prospectAssignmentQuery) Count(ctx context.Context, exec boil.ContextExecutor) (int64, error) {
	var count int64

	queries.SetSelect(q.Query, nil)
	queries.SetCount(q.Query)

	err := q.Query.QueryRowContext(ctx, exec).Scan(&count)
	if err != nil {
		return 0, errors.Wrap(err, "agents_dashboard: failed to count prospect_assignments rows")
	}

	return count, nil
}

// Exists checks if the row exists in the table.
func (q prospectAssignmentQuery) Exists(ctx context.Context, exec boil.ContextExecutor) (bool, error) {
	var count int64

	queries.SetSelect(q.Query, nil)
	queries.SetCount(q.Query)
	queries.SetLimit(q.Query, 1)

	err := q.Query.QueryRowContext(ctx, exec).Scan(&count)
	if err != nil {
		return false, errors.Wrap(err, "agents_dashboard: failed to check if prospect_assignments exists")
	}

	return count > 0, nil
}

// Prospect pointed to by the foreign key.
func (o *ProspectAssignment) Prospect(mods ...qm.QueryMod) prospectQuery {
	queryMods := []qm.QueryMod{
		qm.Where("\"id\" = ?", o.ProspectID),
	}

	queryMods = append(queryMods, mods...)

	return Prospects(queryMods...)
}

// LoadProspect allows an eager lookup of values, cached into the
// loaded structs of the objects. This is for an N-1 relationship.
func (prospectAssignmentL) LoadProspect(ctx context.Context, e boil.ContextExecutor, singular bool, maybeProspectAssignment interface{}, mods queries.Applicator) error {
	var slice []*ProspectAssignment
	var object *ProspectAssignment

	if singular {
		object = maybeProspectAssignment.(*ProspectAssignment)
	} else {
		slice = *maybeProspectAssignment.(*[]*ProspectAssignment)
	}

	args := make([]interface{}, 0, 1)
	if singular {
		if object.R == nil {
			object.R = &prospectAssignmentR{}
		}
		args = append(args, object.ProspectID)

	} else {
	Outer:
		for _, obj := range slice {
			if obj.R == nil {
				obj.R = &prospectAssignmentR{}
			}

			for _, a := range args {
				if a == obj.ProspectID {
					continue Outer
				}
			}

			args = append(args, obj.ProspectID)

		}
	}

	if len(args) == 0 {
		return nil
	}

	query := NewQuery(
		qm.From(`agents_dashboard.prospects`),
		qm.WhereIn(`agents_dashboard.prospects.id in ?`, args...),
	)
	if mods != nil {
		mods.Apply(query)
	}

	results, err := query.QueryContext(ctx, e)
	if err != nil {
		return errors.Wrap(err, "failed to eager load Prospect")
	}

	var resultSlice []*Prospect
	if err = queries.Bind(results, &resultSlice); err != nil {
		return errors.Wrap(err, "failed to bind eager loaded slice Prospect")
	}

	if err = results.Close(); err != nil {
		return errors.Wrap(err, "failed to close results of eager load for prospects")
	}
	if err = results.Err(); err != nil {
		return errors.Wrap(err, "error occurred during iteration of eager loaded relations for prospects")
	}

	if len(prospectAssignmentAfterSelectHooks) != 0 {
		for _, obj := range resultSlice {
			if err := obj.doAfterSelectHooks(ctx, e); err != nil {
				return err
			}
		}
	}

	if len(resultSlice) == 0 {
		return nil
	}

	if singular {
		foreign := resultSlice[0]
		object.R.Prospect = foreign
		if foreign.R == nil {
			foreign.R = &prospectR{}
		}
		foreign.R.ProspectAssignments = append(foreign.R.ProspectAssignments, object)
		return nil
	}

	for _, local := range slice {
		for _, foreign := range resultSlice {
			if local.ProspectID == foreign.ID {
				local.R.Prospect = foreign
				if foreign.R == nil {
					foreign.R = &prospectR{}
				}
				foreign.R.ProspectAssignments = append(foreign.R.ProspectAssignments, local)
				break
			}
		}
	}

	return nil
}

// SetProspect of the prospectAssignment to the related item.
// Sets o.R.Prospect to related.
// Adds o to related.R.ProspectAssignments.
func (o *ProspectAssignment) SetProspect(ctx context.Context, exec boil.ContextExecutor, insert bool, related *Prospect) error {
	var err error
	if insert {
		if err = related.Insert(ctx, exec, boil.Infer()); err != nil {
			return errors.Wrap(err, "failed to insert into foreign table")
		}
	}

	updateQuery := fmt.Sprintf(
		"UPDATE \"agents_dashboard\".\"prospect_assignments\" SET %s WHERE %s",
		strmangle.SetParamNames("\"", "\"", 1, []string{"prospect_id"}),
		strmangle.WhereClause("\"", "\"", 2, prospectAssignmentPrimaryKeyColumns),
	)
	values := []interface{}{related.ID, o.ID}

	if boil.IsDebug(ctx) {
		writer := boil.DebugWriterFrom(ctx)
		fmt.Fprintln(writer, updateQuery)
		fmt.Fprintln(writer, values)
	}
	if _, err = exec.ExecContext(ctx, updateQuery, values...); err != nil {
		return errors.Wrap(err, "failed to update local table")
	}

	o.ProspectID = related.ID
	if o.R == nil {
		o.R = &prospectAssignmentR{
			Prospect: related,
		}
	} else {
		o.R.Prospect = related
	}

	if related.R == nil {
		related.R = &prospectR{
			ProspectAssignments: ProspectAssignmentSlice{o},
		}
	} else {
		related.R.ProspectAssignments = append(related.R.ProspectAssignments, o)
	}

	return nil
}

// ProspectAssignments retrieves all the records using an executor.
func ProspectAssignments(mods ...qm.QueryMod) prospectAssignmentQuery {
	mods = append(mods, qm.From("\"agents_dashboard\".\"prospect_assignments\""))
	q := NewQuery(mods...)
	if len(queries.GetSelect(q)) == 0 {
		queries.SetSelect(q, []string{"\"agents_dashboard\".\"prospect_assignments\".*"})
	}

	return prospectAssignmentQuery{q}
}

// FindProspectAssignment retrieves a single record by ID with an executor.
// If selectCols is empty Find will return all columns.
func FindProspectAssignment(ctx context.Context, exec boil.ContextExecutor, iD string, selectCols ...string) (*ProspectAssignment, error) {
	prospectAssignmentObj := &ProspectAssignment{}

	sel := "*"
	if len(selectCols) > 0 {
		sel = strings.Join(strmangle.IdentQuoteSlice(dialect.LQ, dialect.RQ, selectCols), ",")
	}
	query := fmt.Sprintf(
		"select %s from \"agents_dashboard\".\"prospect_assignments\" where \"id\"=$1", sel,
	)

	q := queries.Raw(query, iD)

	err := q.Bind(ctx, exec, prospectAssignmentObj)
	if err != nil {
		if errors.Is(err, sql.ErrNoRows) {
			return nil, sql.ErrNoRows
		}
		return nil, errors.Wrap(err, "agents_dashboard: unable to select from prospect_assignments")
	}

	if err = prospectAssignmentObj.doAfterSelectHooks(ctx, exec); err != nil {
		return prospectAssignmentObj, err
	}

	return prospectAssignmentObj, nil
}

// Insert a single record using an executor.
// See boil.Columns.InsertColumnSet documentation to understand column list inference for inserts.
func (o *ProspectAssignment) Insert(ctx context.Context, exec boil.ContextExecutor, columns boil.Columns) error {
	if o == nil {
		return errors.New("agents_dashboard: no prospect_assignments provided for insertion")
	}

	var err error
	if !boil.TimestampsAreSkipped(ctx) {
		currTime := time.Now().In(boil.GetLocation())

		if queries.MustTime(o.UpdatedAt).IsZero() {
			queries.SetScanner(&o.UpdatedAt, currTime)
		}
	}

	if err := o.doBeforeInsertHooks(ctx, exec); err != nil {
		return err
	}

	nzDefaults := queries.NonZeroDefaultSet(prospectAssignmentColumnsWithDefault, o)

	key := makeCacheKey(columns, nzDefaults)
	prospectAssignmentInsertCacheMut.RLock()
	cache, cached := prospectAssignmentInsertCache[key]
	prospectAssignmentInsertCacheMut.RUnlock()

	if !cached {
		wl, returnColumns := columns.InsertColumnSet(
			prospectAssignmentAllColumns,
			prospectAssignmentColumnsWithDefault,
			prospectAssignmentColumnsWithoutDefault,
			nzDefaults,
		)

		cache.valueMapping, err = queries.BindMapping(prospectAssignmentType, prospectAssignmentMapping, wl)
		if err != nil {
			return err
		}
		cache.retMapping, err = queries.BindMapping(prospectAssignmentType, prospectAssignmentMapping, returnColumns)
		if err != nil {
			return err
		}
		if len(wl) != 0 {
			cache.query = fmt.Sprintf("INSERT INTO \"agents_dashboard\".\"prospect_assignments\" (\"%s\") %%sVALUES (%s)%%s", strings.Join(wl, "\",\""), strmangle.Placeholders(dialect.UseIndexPlaceholders, len(wl), 1, 1))
		} else {
			cache.query = "INSERT INTO \"agents_dashboard\".\"prospect_assignments\" %sDEFAULT VALUES%s"
		}

		var queryOutput, queryReturning string

		if len(cache.retMapping) != 0 {
			queryReturning = fmt.Sprintf(" RETURNING \"%s\"", strings.Join(returnColumns, "\",\""))
		}

		cache.query = fmt.Sprintf(cache.query, queryOutput, queryReturning)
	}

	value := reflect.Indirect(reflect.ValueOf(o))
	vals := queries.ValuesFromMapping(value, cache.valueMapping)

	if boil.IsDebug(ctx) {
		writer := boil.DebugWriterFrom(ctx)
		fmt.Fprintln(writer, cache.query)
		fmt.Fprintln(writer, vals)
	}

	if len(cache.retMapping) != 0 {
		err = exec.QueryRowContext(ctx, cache.query, vals...).Scan(queries.PtrsFromMapping(value, cache.retMapping)...)
	} else {
		_, err = exec.ExecContext(ctx, cache.query, vals...)
	}

	if err != nil {
		return errors.Wrap(err, "agents_dashboard: unable to insert into prospect_assignments")
	}

	if !cached {
		prospectAssignmentInsertCacheMut.Lock()
		prospectAssignmentInsertCache[key] = cache
		prospectAssignmentInsertCacheMut.Unlock()
	}

	return o.doAfterInsertHooks(ctx, exec)
}

// Update uses an executor to update the ProspectAssignment.
// See boil.Columns.UpdateColumnSet documentation to understand column list inference for updates.
// Update does not automatically update the record in case of default values. Use .Reload() to refresh the records.
func (o *ProspectAssignment) Update(ctx context.Context, exec boil.ContextExecutor, columns boil.Columns) (int64, error) {
	if !boil.TimestampsAreSkipped(ctx) {
		currTime := time.Now().In(boil.GetLocation())

		queries.SetScanner(&o.UpdatedAt, currTime)
	}

	var err error
	if err = o.doBeforeUpdateHooks(ctx, exec); err != nil {
		return 0, err
	}
	key := makeCacheKey(columns, nil)
	prospectAssignmentUpdateCacheMut.RLock()
	cache, cached := prospectAssignmentUpdateCache[key]
	prospectAssignmentUpdateCacheMut.RUnlock()

	if !cached {
		wl := columns.UpdateColumnSet(
			prospectAssignmentAllColumns,
			prospectAssignmentPrimaryKeyColumns,
		)

		if !columns.IsWhitelist() {
			wl = strmangle.SetComplement(wl, []string{"created_at"})
		}
		if len(wl) == 0 {
			return 0, errors.New("agents_dashboard: unable to update prospect_assignments, could not build whitelist")
		}

		cache.query = fmt.Sprintf("UPDATE \"agents_dashboard\".\"prospect_assignments\" SET %s WHERE %s",
			strmangle.SetParamNames("\"", "\"", 1, wl),
			strmangle.WhereClause("\"", "\"", len(wl)+1, prospectAssignmentPrimaryKeyColumns),
		)
		cache.valueMapping, err = queries.BindMapping(prospectAssignmentType, prospectAssignmentMapping, append(wl, prospectAssignmentPrimaryKeyColumns...))
		if err != nil {
			return 0, err
		}
	}

	values := queries.ValuesFromMapping(reflect.Indirect(reflect.ValueOf(o)), cache.valueMapping)

	if boil.IsDebug(ctx) {
		writer := boil.DebugWriterFrom(ctx)
		fmt.Fprintln(writer, cache.query)
		fmt.Fprintln(writer, values)
	}
	var result sql.Result
	result, err = exec.ExecContext(ctx, cache.query, values...)
	if err != nil {
		return 0, errors.Wrap(err, "agents_dashboard: unable to update prospect_assignments row")
	}

	rowsAff, err := result.RowsAffected()
	if err != nil {
		return 0, errors.Wrap(err, "agents_dashboard: failed to get rows affected by update for prospect_assignments")
	}

	if !cached {
		prospectAssignmentUpdateCacheMut.Lock()
		prospectAssignmentUpdateCache[key] = cache
		prospectAssignmentUpdateCacheMut.Unlock()
	}

	return rowsAff, o.doAfterUpdateHooks(ctx, exec)
}

// UpdateAll updates all rows with the specified column values.
func (q prospectAssignmentQuery) UpdateAll(ctx context.Context, exec boil.ContextExecutor, cols M) (int64, error) {
	queries.SetUpdate(q.Query, cols)

	result, err := q.Query.ExecContext(ctx, exec)
	if err != nil {
		return 0, errors.Wrap(err, "agents_dashboard: unable to update all for prospect_assignments")
	}

	rowsAff, err := result.RowsAffected()
	if err != nil {
		return 0, errors.Wrap(err, "agents_dashboard: unable to retrieve rows affected for prospect_assignments")
	}

	return rowsAff, nil
}

// UpdateAll updates all rows with the specified column values, using an executor.
func (o ProspectAssignmentSlice) UpdateAll(ctx context.Context, exec boil.ContextExecutor, cols M) (int64, error) {
	ln := int64(len(o))
	if ln == 0 {
		return 0, nil
	}

	if len(cols) == 0 {
		return 0, errors.New("agents_dashboard: update all requires at least one column argument")
	}

	colNames := make([]string, len(cols))
	args := make([]interface{}, len(cols))

	i := 0
	for name, value := range cols {
		colNames[i] = name
		args[i] = value
		i++
	}

	// Append all of the primary key values for each column
	for _, obj := range o {
		pkeyArgs := queries.ValuesFromMapping(reflect.Indirect(reflect.ValueOf(obj)), prospectAssignmentPrimaryKeyMapping)
		args = append(args, pkeyArgs...)
	}

	sql := fmt.Sprintf("UPDATE \"agents_dashboard\".\"prospect_assignments\" SET %s WHERE %s",
		strmangle.SetParamNames("\"", "\"", 1, colNames),
		strmangle.WhereClauseRepeated(string(dialect.LQ), string(dialect.RQ), len(colNames)+1, prospectAssignmentPrimaryKeyColumns, len(o)))

	if boil.IsDebug(ctx) {
		writer := boil.DebugWriterFrom(ctx)
		fmt.Fprintln(writer, sql)
		fmt.Fprintln(writer, args...)
	}
	result, err := exec.ExecContext(ctx, sql, args...)
	if err != nil {
		return 0, errors.Wrap(err, "agents_dashboard: unable to update all in prospectAssignment slice")
	}

	rowsAff, err := result.RowsAffected()
	if err != nil {
		return 0, errors.Wrap(err, "agents_dashboard: unable to retrieve rows affected all in update all prospectAssignment")
	}
	return rowsAff, nil
}

// Upsert attempts an insert using an executor, and does an update or ignore on conflict.
// See boil.Columns documentation for how to properly use updateColumns and insertColumns.
func (o *ProspectAssignment) Upsert(ctx context.Context, exec boil.ContextExecutor, updateOnConflict bool, conflictColumns []string, updateColumns, insertColumns boil.Columns) error {
	if o == nil {
		return errors.New("agents_dashboard: no prospect_assignments provided for upsert")
	}
	if !boil.TimestampsAreSkipped(ctx) {
		currTime := time.Now().In(boil.GetLocation())

		queries.SetScanner(&o.UpdatedAt, currTime)
	}

	if err := o.doBeforeUpsertHooks(ctx, exec); err != nil {
		return err
	}

	nzDefaults := queries.NonZeroDefaultSet(prospectAssignmentColumnsWithDefault, o)

	// Build cache key in-line uglily - mysql vs psql problems
	buf := strmangle.GetBuffer()
	if updateOnConflict {
		buf.WriteByte('t')
	} else {
		buf.WriteByte('f')
	}
	buf.WriteByte('.')
	for _, c := range conflictColumns {
		buf.WriteString(c)
	}
	buf.WriteByte('.')
	buf.WriteString(strconv.Itoa(updateColumns.Kind))
	for _, c := range updateColumns.Cols {
		buf.WriteString(c)
	}
	buf.WriteByte('.')
	buf.WriteString(strconv.Itoa(insertColumns.Kind))
	for _, c := range insertColumns.Cols {
		buf.WriteString(c)
	}
	buf.WriteByte('.')
	for _, c := range nzDefaults {
		buf.WriteString(c)
	}
	key := buf.String()
	strmangle.PutBuffer(buf)

	prospectAssignmentUpsertCacheMut.RLock()
	cache, cached := prospectAssignmentUpsertCache[key]
	prospectAssignmentUpsertCacheMut.RUnlock()

	var err error

	if !cached {
		insert, ret := insertColumns.InsertColumnSet(
			prospectAssignmentAllColumns,
			prospectAssignmentColumnsWithDefault,
			prospectAssignmentColumnsWithoutDefault,
			nzDefaults,
		)

		update := updateColumns.UpdateColumnSet(
			prospectAssignmentAllColumns,
			prospectAssignmentPrimaryKeyColumns,
		)

		if updateOnConflict && len(update) == 0 {
			return errors.New("agents_dashboard: unable to upsert prospect_assignments, could not build update column list")
		}

		conflict := conflictColumns
		if len(conflict) == 0 {
			conflict = make([]string, len(prospectAssignmentPrimaryKeyColumns))
			copy(conflict, prospectAssignmentPrimaryKeyColumns)
		}
		cache.query = buildUpsertQueryPostgres(dialect, "\"agents_dashboard\".\"prospect_assignments\"", updateOnConflict, ret, update, conflict, insert)

		cache.valueMapping, err = queries.BindMapping(prospectAssignmentType, prospectAssignmentMapping, insert)
		if err != nil {
			return err
		}
		if len(ret) != 0 {
			cache.retMapping, err = queries.BindMapping(prospectAssignmentType, prospectAssignmentMapping, ret)
			if err != nil {
				return err
			}
		}
	}

	value := reflect.Indirect(reflect.ValueOf(o))
	vals := queries.ValuesFromMapping(value, cache.valueMapping)
	var returns []interface{}
	if len(cache.retMapping) != 0 {
		returns = queries.PtrsFromMapping(value, cache.retMapping)
	}

	if boil.IsDebug(ctx) {
		writer := boil.DebugWriterFrom(ctx)
		fmt.Fprintln(writer, cache.query)
		fmt.Fprintln(writer, vals)
	}
	if len(cache.retMapping) != 0 {
		err = exec.QueryRowContext(ctx, cache.query, vals...).Scan(returns...)
		if errors.Is(err, sql.ErrNoRows) {
			err = nil // Postgres doesn't return anything when there's no update
		}
	} else {
		_, err = exec.ExecContext(ctx, cache.query, vals...)
	}
	if err != nil {
		return errors.Wrap(err, "agents_dashboard: unable to upsert prospect_assignments")
	}

	if !cached {
		prospectAssignmentUpsertCacheMut.Lock()
		prospectAssignmentUpsertCache[key] = cache
		prospectAssignmentUpsertCacheMut.Unlock()
	}

	return o.doAfterUpsertHooks(ctx, exec)
}

// Delete deletes a single ProspectAssignment record with an executor.
// Delete will match against the primary key column to find the record to delete.
func (o *ProspectAssignment) Delete(ctx context.Context, exec boil.ContextExecutor) (int64, error) {
	if o == nil {
		return 0, errors.New("agents_dashboard: no ProspectAssignment provided for delete")
	}

	if err := o.doBeforeDeleteHooks(ctx, exec); err != nil {
		return 0, err
	}

	args := queries.ValuesFromMapping(reflect.Indirect(reflect.ValueOf(o)), prospectAssignmentPrimaryKeyMapping)
	sql := "DELETE FROM \"agents_dashboard\".\"prospect_assignments\" WHERE \"id\"=$1"

	if boil.IsDebug(ctx) {
		writer := boil.DebugWriterFrom(ctx)
		fmt.Fprintln(writer, sql)
		fmt.Fprintln(writer, args...)
	}
	result, err := exec.ExecContext(ctx, sql, args...)
	if err != nil {
		return 0, errors.Wrap(err, "agents_dashboard: unable to delete from prospect_assignments")
	}

	rowsAff, err := result.RowsAffected()
	if err != nil {
		return 0, errors.Wrap(err, "agents_dashboard: failed to get rows affected by delete for prospect_assignments")
	}

	if err := o.doAfterDeleteHooks(ctx, exec); err != nil {
		return 0, err
	}

	return rowsAff, nil
}

// DeleteAll deletes all matching rows.
func (q prospectAssignmentQuery) DeleteAll(ctx context.Context, exec boil.ContextExecutor) (int64, error) {
	if q.Query == nil {
		return 0, errors.New("agents_dashboard: no prospectAssignmentQuery provided for delete all")
	}

	queries.SetDelete(q.Query)

	result, err := q.Query.ExecContext(ctx, exec)
	if err != nil {
		return 0, errors.Wrap(err, "agents_dashboard: unable to delete all from prospect_assignments")
	}

	rowsAff, err := result.RowsAffected()
	if err != nil {
		return 0, errors.Wrap(err, "agents_dashboard: failed to get rows affected by deleteall for prospect_assignments")
	}

	return rowsAff, nil
}

// DeleteAll deletes all rows in the slice, using an executor.
func (o ProspectAssignmentSlice) DeleteAll(ctx context.Context, exec boil.ContextExecutor) (int64, error) {
	if len(o) == 0 {
		return 0, nil
	}

	if len(prospectAssignmentBeforeDeleteHooks) != 0 {
		for _, obj := range o {
			if err := obj.doBeforeDeleteHooks(ctx, exec); err != nil {
				return 0, err
			}
		}
	}

	var args []interface{}
	for _, obj := range o {
		pkeyArgs := queries.ValuesFromMapping(reflect.Indirect(reflect.ValueOf(obj)), prospectAssignmentPrimaryKeyMapping)
		args = append(args, pkeyArgs...)
	}

	sql := "DELETE FROM \"agents_dashboard\".\"prospect_assignments\" WHERE " +
		strmangle.WhereClauseRepeated(string(dialect.LQ), string(dialect.RQ), 1, prospectAssignmentPrimaryKeyColumns, len(o))

	if boil.IsDebug(ctx) {
		writer := boil.DebugWriterFrom(ctx)
		fmt.Fprintln(writer, sql)
		fmt.Fprintln(writer, args)
	}
	result, err := exec.ExecContext(ctx, sql, args...)
	if err != nil {
		return 0, errors.Wrap(err, "agents_dashboard: unable to delete all from prospectAssignment slice")
	}

	rowsAff, err := result.RowsAffected()
	if err != nil {
		return 0, errors.Wrap(err, "agents_dashboard: failed to get rows affected by deleteall for prospect_assignments")
	}

	if len(prospectAssignmentAfterDeleteHooks) != 0 {
		for _, obj := range o {
			if err := obj.doAfterDeleteHooks(ctx, exec); err != nil {
				return 0, err
			}
		}
	}

	return rowsAff, nil
}

// Reload refetches the object from the database
// using the primary keys with an executor.
func (o *ProspectAssignment) Reload(ctx context.Context, exec boil.ContextExecutor) error {
	ret, err := FindProspectAssignment(ctx, exec, o.ID)
	if err != nil {
		return err
	}

	*o = *ret
	return nil
}

// ReloadAll refetches every row with matching primary key column values
// and overwrites the original object slice with the newly updated slice.
func (o *ProspectAssignmentSlice) ReloadAll(ctx context.Context, exec boil.ContextExecutor) error {
	if o == nil || len(*o) == 0 {
		return nil
	}

	slice := ProspectAssignmentSlice{}
	var args []interface{}
	for _, obj := range *o {
		pkeyArgs := queries.ValuesFromMapping(reflect.Indirect(reflect.ValueOf(obj)), prospectAssignmentPrimaryKeyMapping)
		args = append(args, pkeyArgs...)
	}

	sql := "SELECT \"agents_dashboard\".\"prospect_assignments\".* FROM \"agents_dashboard\".\"prospect_assignments\" WHERE " +
		strmangle.WhereClauseRepeated(string(dialect.LQ), string(dialect.RQ), 1, prospectAssignmentPrimaryKeyColumns, len(*o))

	q := queries.Raw(sql, args...)

	err := q.Bind(ctx, exec, &slice)
	if err != nil {
		return errors.Wrap(err, "agents_dashboard: unable to reload all in ProspectAssignmentSlice")
	}

	*o = slice

	return nil
}

// ProspectAssignmentExists checks if the ProspectAssignment row exists.
func ProspectAssignmentExists(ctx context.Context, exec boil.ContextExecutor, iD string) (bool, error) {
	var exists bool
	sql := "select exists(select 1 from \"agents_dashboard\".\"prospect_assignments\" where \"id\"=$1 limit 1)"

	if boil.IsDebug(ctx) {
		writer := boil.DebugWriterFrom(ctx)
		fmt.Fprintln(writer, sql)
		fmt.Fprintln(writer, iD)
	}
	row := exec.QueryRowContext(ctx, sql, iD)

	err := row.Scan(&exists)
	if err != nil {
		return false, errors.Wrap(err, "agents_dashboard: unable to check if prospect_assignments exists")
	}

	return exists, nil
}
