// Code generated by SQLBoiler (https://github.com/volatiletech/sqlboiler). DO NOT EDIT.
// This file is meant to be re-generated in place and/or deleted at any time.

package keeptruckin

import (
	"context"
	"database/sql"
	"fmt"
	"reflect"
	"strconv"
	"strings"
	"sync"
	"time"

	"github.com/friendsofgo/errors"
	"github.com/volatiletech/sqlboiler/v4/boil"
	"github.com/volatiletech/sqlboiler/v4/queries"
	"github.com/volatiletech/sqlboiler/v4/queries/qm"
	"github.com/volatiletech/sqlboiler/v4/queries/qmhelper"
	"github.com/volatiletech/sqlboiler/v4/types"
	"github.com/volatiletech/strmangle"
)

// Organization is an object representing the database table.
type Organization struct {
	ID        string     `boil:"id" json:"id" toml:"id" yaml:"id"`
	Name      string     `boil:"name" json:"name" toml:"name" yaml:"name"`
	HandleID  string     `boil:"handle_id" json:"handle_id" toml:"handle_id" yaml:"handle_id"`
	DotNumber string     `boil:"dot_number" json:"dot_number" toml:"dot_number" yaml:"dot_number"`
	IsTracked bool       `boil:"is_tracked" json:"is_tracked" toml:"is_tracked" yaml:"is_tracked"`
	Raw       types.JSON `boil:"raw" json:"raw" toml:"raw" yaml:"raw"`
	CreatedAt time.Time  `boil:"created_at" json:"created_at" toml:"created_at" yaml:"created_at"`
	UpdatedAt time.Time  `boil:"updated_at" json:"updated_at" toml:"updated_at" yaml:"updated_at"`

	R *organizationR `boil:"" json:"" toml:"" yaml:""`
	L organizationL  `boil:"-" json:"-" toml:"-" yaml:"-"`
}

var OrganizationColumns = struct {
	ID        string
	Name      string
	HandleID  string
	DotNumber string
	IsTracked string
	Raw       string
	CreatedAt string
	UpdatedAt string
}{
	ID:        "id",
	Name:      "name",
	HandleID:  "handle_id",
	DotNumber: "dot_number",
	IsTracked: "is_tracked",
	Raw:       "raw",
	CreatedAt: "created_at",
	UpdatedAt: "updated_at",
}

var OrganizationTableColumns = struct {
	ID        string
	Name      string
	HandleID  string
	DotNumber string
	IsTracked string
	Raw       string
	CreatedAt string
	UpdatedAt string
}{
	ID:        "organization.id",
	Name:      "organization.name",
	HandleID:  "organization.handle_id",
	DotNumber: "organization.dot_number",
	IsTracked: "organization.is_tracked",
	Raw:       "organization.raw",
	CreatedAt: "organization.created_at",
	UpdatedAt: "organization.updated_at",
}

// Generated where

var OrganizationWhere = struct {
	ID        whereHelperstring
	Name      whereHelperstring
	HandleID  whereHelperstring
	DotNumber whereHelperstring
	IsTracked whereHelperbool
	Raw       whereHelpertypes_JSON
	CreatedAt whereHelpertime_Time
	UpdatedAt whereHelpertime_Time
}{
	ID:        whereHelperstring{field: "\"keeptruckin\".\"organization\".\"id\""},
	Name:      whereHelperstring{field: "\"keeptruckin\".\"organization\".\"name\""},
	HandleID:  whereHelperstring{field: "\"keeptruckin\".\"organization\".\"handle_id\""},
	DotNumber: whereHelperstring{field: "\"keeptruckin\".\"organization\".\"dot_number\""},
	IsTracked: whereHelperbool{field: "\"keeptruckin\".\"organization\".\"is_tracked\""},
	Raw:       whereHelpertypes_JSON{field: "\"keeptruckin\".\"organization\".\"raw\""},
	CreatedAt: whereHelpertime_Time{field: "\"keeptruckin\".\"organization\".\"created_at\""},
	UpdatedAt: whereHelpertime_Time{field: "\"keeptruckin\".\"organization\".\"updated_at\""},
}

// OrganizationRels is where relationship names are stored.
var OrganizationRels = struct {
	OrgDrivers  string
	OrgVehicles string
}{
	OrgDrivers:  "OrgDrivers",
	OrgVehicles: "OrgVehicles",
}

// organizationR is where relationships are stored.
type organizationR struct {
	OrgDrivers  DriverSlice  `boil:"OrgDrivers" json:"OrgDrivers" toml:"OrgDrivers" yaml:"OrgDrivers"`
	OrgVehicles VehicleSlice `boil:"OrgVehicles" json:"OrgVehicles" toml:"OrgVehicles" yaml:"OrgVehicles"`
}

// NewStruct creates a new relationship struct
func (*organizationR) NewStruct() *organizationR {
	return &organizationR{}
}

// organizationL is where Load methods for each relationship are stored.
type organizationL struct{}

var (
	organizationAllColumns            = []string{"id", "name", "handle_id", "dot_number", "is_tracked", "raw", "created_at", "updated_at"}
	organizationColumnsWithoutDefault = []string{"id", "name", "handle_id", "dot_number", "raw"}
	organizationColumnsWithDefault    = []string{"is_tracked", "created_at", "updated_at"}
	organizationPrimaryKeyColumns     = []string{"id"}
	organizationGeneratedColumns      = []string{}
)

type (
	// OrganizationSlice is an alias for a slice of pointers to Organization.
	// This should almost always be used instead of []Organization.
	OrganizationSlice []*Organization
	// OrganizationHook is the signature for custom Organization hook methods
	OrganizationHook func(context.Context, boil.ContextExecutor, *Organization) error

	organizationQuery struct {
		*queries.Query
	}
)

// Cache for insert, update and upsert
var (
	organizationType                 = reflect.TypeOf(&Organization{})
	organizationMapping              = queries.MakeStructMapping(organizationType)
	organizationPrimaryKeyMapping, _ = queries.BindMapping(organizationType, organizationMapping, organizationPrimaryKeyColumns)
	organizationInsertCacheMut       sync.RWMutex
	organizationInsertCache          = make(map[string]insertCache)
	organizationUpdateCacheMut       sync.RWMutex
	organizationUpdateCache          = make(map[string]updateCache)
	organizationUpsertCacheMut       sync.RWMutex
	organizationUpsertCache          = make(map[string]insertCache)
)

var (
	// Force time package dependency for automated UpdatedAt/CreatedAt.
	_ = time.Second
	// Force qmhelper dependency for where clause generation (which doesn't
	// always happen)
	_ = qmhelper.Where
)

var organizationAfterSelectHooks []OrganizationHook

var organizationBeforeInsertHooks []OrganizationHook
var organizationAfterInsertHooks []OrganizationHook

var organizationBeforeUpdateHooks []OrganizationHook
var organizationAfterUpdateHooks []OrganizationHook

var organizationBeforeDeleteHooks []OrganizationHook
var organizationAfterDeleteHooks []OrganizationHook

var organizationBeforeUpsertHooks []OrganizationHook
var organizationAfterUpsertHooks []OrganizationHook

// doAfterSelectHooks executes all "after Select" hooks.
func (o *Organization) doAfterSelectHooks(ctx context.Context, exec boil.ContextExecutor) (err error) {
	if boil.HooksAreSkipped(ctx) {
		return nil
	}

	for _, hook := range organizationAfterSelectHooks {
		if err := hook(ctx, exec, o); err != nil {
			return err
		}
	}

	return nil
}

// doBeforeInsertHooks executes all "before insert" hooks.
func (o *Organization) doBeforeInsertHooks(ctx context.Context, exec boil.ContextExecutor) (err error) {
	if boil.HooksAreSkipped(ctx) {
		return nil
	}

	for _, hook := range organizationBeforeInsertHooks {
		if err := hook(ctx, exec, o); err != nil {
			return err
		}
	}

	return nil
}

// doAfterInsertHooks executes all "after Insert" hooks.
func (o *Organization) doAfterInsertHooks(ctx context.Context, exec boil.ContextExecutor) (err error) {
	if boil.HooksAreSkipped(ctx) {
		return nil
	}

	for _, hook := range organizationAfterInsertHooks {
		if err := hook(ctx, exec, o); err != nil {
			return err
		}
	}

	return nil
}

// doBeforeUpdateHooks executes all "before Update" hooks.
func (o *Organization) doBeforeUpdateHooks(ctx context.Context, exec boil.ContextExecutor) (err error) {
	if boil.HooksAreSkipped(ctx) {
		return nil
	}

	for _, hook := range organizationBeforeUpdateHooks {
		if err := hook(ctx, exec, o); err != nil {
			return err
		}
	}

	return nil
}

// doAfterUpdateHooks executes all "after Update" hooks.
func (o *Organization) doAfterUpdateHooks(ctx context.Context, exec boil.ContextExecutor) (err error) {
	if boil.HooksAreSkipped(ctx) {
		return nil
	}

	for _, hook := range organizationAfterUpdateHooks {
		if err := hook(ctx, exec, o); err != nil {
			return err
		}
	}

	return nil
}

// doBeforeDeleteHooks executes all "before Delete" hooks.
func (o *Organization) doBeforeDeleteHooks(ctx context.Context, exec boil.ContextExecutor) (err error) {
	if boil.HooksAreSkipped(ctx) {
		return nil
	}

	for _, hook := range organizationBeforeDeleteHooks {
		if err := hook(ctx, exec, o); err != nil {
			return err
		}
	}

	return nil
}

// doAfterDeleteHooks executes all "after Delete" hooks.
func (o *Organization) doAfterDeleteHooks(ctx context.Context, exec boil.ContextExecutor) (err error) {
	if boil.HooksAreSkipped(ctx) {
		return nil
	}

	for _, hook := range organizationAfterDeleteHooks {
		if err := hook(ctx, exec, o); err != nil {
			return err
		}
	}

	return nil
}

// doBeforeUpsertHooks executes all "before Upsert" hooks.
func (o *Organization) doBeforeUpsertHooks(ctx context.Context, exec boil.ContextExecutor) (err error) {
	if boil.HooksAreSkipped(ctx) {
		return nil
	}

	for _, hook := range organizationBeforeUpsertHooks {
		if err := hook(ctx, exec, o); err != nil {
			return err
		}
	}

	return nil
}

// doAfterUpsertHooks executes all "after Upsert" hooks.
func (o *Organization) doAfterUpsertHooks(ctx context.Context, exec boil.ContextExecutor) (err error) {
	if boil.HooksAreSkipped(ctx) {
		return nil
	}

	for _, hook := range organizationAfterUpsertHooks {
		if err := hook(ctx, exec, o); err != nil {
			return err
		}
	}

	return nil
}

// AddOrganizationHook registers your hook function for all future operations.
func AddOrganizationHook(hookPoint boil.HookPoint, organizationHook OrganizationHook) {
	switch hookPoint {
	case boil.AfterSelectHook:
		organizationAfterSelectHooks = append(organizationAfterSelectHooks, organizationHook)
	case boil.BeforeInsertHook:
		organizationBeforeInsertHooks = append(organizationBeforeInsertHooks, organizationHook)
	case boil.AfterInsertHook:
		organizationAfterInsertHooks = append(organizationAfterInsertHooks, organizationHook)
	case boil.BeforeUpdateHook:
		organizationBeforeUpdateHooks = append(organizationBeforeUpdateHooks, organizationHook)
	case boil.AfterUpdateHook:
		organizationAfterUpdateHooks = append(organizationAfterUpdateHooks, organizationHook)
	case boil.BeforeDeleteHook:
		organizationBeforeDeleteHooks = append(organizationBeforeDeleteHooks, organizationHook)
	case boil.AfterDeleteHook:
		organizationAfterDeleteHooks = append(organizationAfterDeleteHooks, organizationHook)
	case boil.BeforeUpsertHook:
		organizationBeforeUpsertHooks = append(organizationBeforeUpsertHooks, organizationHook)
	case boil.AfterUpsertHook:
		organizationAfterUpsertHooks = append(organizationAfterUpsertHooks, organizationHook)
	}
}

// One returns a single organization record from the query.
func (q organizationQuery) One(ctx context.Context, exec boil.ContextExecutor) (*Organization, error) {
	o := &Organization{}

	queries.SetLimit(q.Query, 1)

	err := q.Bind(ctx, exec, o)
	if err != nil {
		if errors.Is(err, sql.ErrNoRows) {
			return nil, sql.ErrNoRows
		}
		return nil, errors.Wrap(err, "keeptruckin: failed to execute a one query for organization")
	}

	if err := o.doAfterSelectHooks(ctx, exec); err != nil {
		return o, err
	}

	return o, nil
}

// All returns all Organization records from the query.
func (q organizationQuery) All(ctx context.Context, exec boil.ContextExecutor) (OrganizationSlice, error) {
	var o []*Organization

	err := q.Bind(ctx, exec, &o)
	if err != nil {
		return nil, errors.Wrap(err, "keeptruckin: failed to assign all query results to Organization slice")
	}

	if len(organizationAfterSelectHooks) != 0 {
		for _, obj := range o {
			if err := obj.doAfterSelectHooks(ctx, exec); err != nil {
				return o, err
			}
		}
	}

	return o, nil
}

// Count returns the count of all Organization records in the query.
func (q organizationQuery) Count(ctx context.Context, exec boil.ContextExecutor) (int64, error) {
	var count int64

	queries.SetSelect(q.Query, nil)
	queries.SetCount(q.Query)

	err := q.Query.QueryRowContext(ctx, exec).Scan(&count)
	if err != nil {
		return 0, errors.Wrap(err, "keeptruckin: failed to count organization rows")
	}

	return count, nil
}

// Exists checks if the row exists in the table.
func (q organizationQuery) Exists(ctx context.Context, exec boil.ContextExecutor) (bool, error) {
	var count int64

	queries.SetSelect(q.Query, nil)
	queries.SetCount(q.Query)
	queries.SetLimit(q.Query, 1)

	err := q.Query.QueryRowContext(ctx, exec).Scan(&count)
	if err != nil {
		return false, errors.Wrap(err, "keeptruckin: failed to check if organization exists")
	}

	return count > 0, nil
}

// OrgDrivers retrieves all the driver's Drivers with an executor via org_id column.
func (o *Organization) OrgDrivers(mods ...qm.QueryMod) driverQuery {
	var queryMods []qm.QueryMod
	if len(mods) != 0 {
		queryMods = append(queryMods, mods...)
	}

	queryMods = append(queryMods,
		qm.Where("\"keeptruckin\".\"driver\".\"org_id\"=?", o.ID),
	)

	return Drivers(queryMods...)
}

// OrgVehicles retrieves all the vehicle's Vehicles with an executor via org_id column.
func (o *Organization) OrgVehicles(mods ...qm.QueryMod) vehicleQuery {
	var queryMods []qm.QueryMod
	if len(mods) != 0 {
		queryMods = append(queryMods, mods...)
	}

	queryMods = append(queryMods,
		qm.Where("\"keeptruckin\".\"vehicle\".\"org_id\"=?", o.ID),
	)

	return Vehicles(queryMods...)
}

// LoadOrgDrivers allows an eager lookup of values, cached into the
// loaded structs of the objects. This is for a 1-M or N-M relationship.
func (organizationL) LoadOrgDrivers(ctx context.Context, e boil.ContextExecutor, singular bool, maybeOrganization interface{}, mods queries.Applicator) error {
	var slice []*Organization
	var object *Organization

	if singular {
		object = maybeOrganization.(*Organization)
	} else {
		slice = *maybeOrganization.(*[]*Organization)
	}

	args := make([]interface{}, 0, 1)
	if singular {
		if object.R == nil {
			object.R = &organizationR{}
		}
		args = append(args, object.ID)
	} else {
	Outer:
		for _, obj := range slice {
			if obj.R == nil {
				obj.R = &organizationR{}
			}

			for _, a := range args {
				if a == obj.ID {
					continue Outer
				}
			}

			args = append(args, obj.ID)
		}
	}

	if len(args) == 0 {
		return nil
	}

	query := NewQuery(
		qm.From(`keeptruckin.driver`),
		qm.WhereIn(`keeptruckin.driver.org_id in ?`, args...),
	)
	if mods != nil {
		mods.Apply(query)
	}

	results, err := query.QueryContext(ctx, e)
	if err != nil {
		return errors.Wrap(err, "failed to eager load driver")
	}

	var resultSlice []*Driver
	if err = queries.Bind(results, &resultSlice); err != nil {
		return errors.Wrap(err, "failed to bind eager loaded slice driver")
	}

	if err = results.Close(); err != nil {
		return errors.Wrap(err, "failed to close results in eager load on driver")
	}
	if err = results.Err(); err != nil {
		return errors.Wrap(err, "error occurred during iteration of eager loaded relations for driver")
	}

	if len(driverAfterSelectHooks) != 0 {
		for _, obj := range resultSlice {
			if err := obj.doAfterSelectHooks(ctx, e); err != nil {
				return err
			}
		}
	}
	if singular {
		object.R.OrgDrivers = resultSlice
		for _, foreign := range resultSlice {
			if foreign.R == nil {
				foreign.R = &driverR{}
			}
			foreign.R.Org = object
		}
		return nil
	}

	for _, foreign := range resultSlice {
		for _, local := range slice {
			if local.ID == foreign.OrgID {
				local.R.OrgDrivers = append(local.R.OrgDrivers, foreign)
				if foreign.R == nil {
					foreign.R = &driverR{}
				}
				foreign.R.Org = local
				break
			}
		}
	}

	return nil
}

// LoadOrgVehicles allows an eager lookup of values, cached into the
// loaded structs of the objects. This is for a 1-M or N-M relationship.
func (organizationL) LoadOrgVehicles(ctx context.Context, e boil.ContextExecutor, singular bool, maybeOrganization interface{}, mods queries.Applicator) error {
	var slice []*Organization
	var object *Organization

	if singular {
		object = maybeOrganization.(*Organization)
	} else {
		slice = *maybeOrganization.(*[]*Organization)
	}

	args := make([]interface{}, 0, 1)
	if singular {
		if object.R == nil {
			object.R = &organizationR{}
		}
		args = append(args, object.ID)
	} else {
	Outer:
		for _, obj := range slice {
			if obj.R == nil {
				obj.R = &organizationR{}
			}

			for _, a := range args {
				if a == obj.ID {
					continue Outer
				}
			}

			args = append(args, obj.ID)
		}
	}

	if len(args) == 0 {
		return nil
	}

	query := NewQuery(
		qm.From(`keeptruckin.vehicle`),
		qm.WhereIn(`keeptruckin.vehicle.org_id in ?`, args...),
	)
	if mods != nil {
		mods.Apply(query)
	}

	results, err := query.QueryContext(ctx, e)
	if err != nil {
		return errors.Wrap(err, "failed to eager load vehicle")
	}

	var resultSlice []*Vehicle
	if err = queries.Bind(results, &resultSlice); err != nil {
		return errors.Wrap(err, "failed to bind eager loaded slice vehicle")
	}

	if err = results.Close(); err != nil {
		return errors.Wrap(err, "failed to close results in eager load on vehicle")
	}
	if err = results.Err(); err != nil {
		return errors.Wrap(err, "error occurred during iteration of eager loaded relations for vehicle")
	}

	if len(vehicleAfterSelectHooks) != 0 {
		for _, obj := range resultSlice {
			if err := obj.doAfterSelectHooks(ctx, e); err != nil {
				return err
			}
		}
	}
	if singular {
		object.R.OrgVehicles = resultSlice
		for _, foreign := range resultSlice {
			if foreign.R == nil {
				foreign.R = &vehicleR{}
			}
			foreign.R.Org = object
		}
		return nil
	}

	for _, foreign := range resultSlice {
		for _, local := range slice {
			if local.ID == foreign.OrgID {
				local.R.OrgVehicles = append(local.R.OrgVehicles, foreign)
				if foreign.R == nil {
					foreign.R = &vehicleR{}
				}
				foreign.R.Org = local
				break
			}
		}
	}

	return nil
}

// AddOrgDrivers adds the given related objects to the existing relationships
// of the organization, optionally inserting them as new records.
// Appends related to o.R.OrgDrivers.
// Sets related.R.Org appropriately.
func (o *Organization) AddOrgDrivers(ctx context.Context, exec boil.ContextExecutor, insert bool, related ...*Driver) error {
	var err error
	for _, rel := range related {
		if insert {
			rel.OrgID = o.ID
			if err = rel.Insert(ctx, exec, boil.Infer()); err != nil {
				return errors.Wrap(err, "failed to insert into foreign table")
			}
		} else {
			updateQuery := fmt.Sprintf(
				"UPDATE \"keeptruckin\".\"driver\" SET %s WHERE %s",
				strmangle.SetParamNames("\"", "\"", 1, []string{"org_id"}),
				strmangle.WhereClause("\"", "\"", 2, driverPrimaryKeyColumns),
			)
			values := []interface{}{o.ID, rel.ID}

			if boil.IsDebug(ctx) {
				writer := boil.DebugWriterFrom(ctx)
				fmt.Fprintln(writer, updateQuery)
				fmt.Fprintln(writer, values)
			}
			if _, err = exec.ExecContext(ctx, updateQuery, values...); err != nil {
				return errors.Wrap(err, "failed to update foreign table")
			}

			rel.OrgID = o.ID
		}
	}

	if o.R == nil {
		o.R = &organizationR{
			OrgDrivers: related,
		}
	} else {
		o.R.OrgDrivers = append(o.R.OrgDrivers, related...)
	}

	for _, rel := range related {
		if rel.R == nil {
			rel.R = &driverR{
				Org: o,
			}
		} else {
			rel.R.Org = o
		}
	}
	return nil
}

// AddOrgVehicles adds the given related objects to the existing relationships
// of the organization, optionally inserting them as new records.
// Appends related to o.R.OrgVehicles.
// Sets related.R.Org appropriately.
func (o *Organization) AddOrgVehicles(ctx context.Context, exec boil.ContextExecutor, insert bool, related ...*Vehicle) error {
	var err error
	for _, rel := range related {
		if insert {
			rel.OrgID = o.ID
			if err = rel.Insert(ctx, exec, boil.Infer()); err != nil {
				return errors.Wrap(err, "failed to insert into foreign table")
			}
		} else {
			updateQuery := fmt.Sprintf(
				"UPDATE \"keeptruckin\".\"vehicle\" SET %s WHERE %s",
				strmangle.SetParamNames("\"", "\"", 1, []string{"org_id"}),
				strmangle.WhereClause("\"", "\"", 2, vehiclePrimaryKeyColumns),
			)
			values := []interface{}{o.ID, rel.ID}

			if boil.IsDebug(ctx) {
				writer := boil.DebugWriterFrom(ctx)
				fmt.Fprintln(writer, updateQuery)
				fmt.Fprintln(writer, values)
			}
			if _, err = exec.ExecContext(ctx, updateQuery, values...); err != nil {
				return errors.Wrap(err, "failed to update foreign table")
			}

			rel.OrgID = o.ID
		}
	}

	if o.R == nil {
		o.R = &organizationR{
			OrgVehicles: related,
		}
	} else {
		o.R.OrgVehicles = append(o.R.OrgVehicles, related...)
	}

	for _, rel := range related {
		if rel.R == nil {
			rel.R = &vehicleR{
				Org: o,
			}
		} else {
			rel.R.Org = o
		}
	}
	return nil
}

// Organizations retrieves all the records using an executor.
func Organizations(mods ...qm.QueryMod) organizationQuery {
	mods = append(mods, qm.From("\"keeptruckin\".\"organization\""))
	q := NewQuery(mods...)
	if len(queries.GetSelect(q)) == 0 {
		queries.SetSelect(q, []string{"\"keeptruckin\".\"organization\".*"})
	}

	return organizationQuery{q}
}

// FindOrganization retrieves a single record by ID with an executor.
// If selectCols is empty Find will return all columns.
func FindOrganization(ctx context.Context, exec boil.ContextExecutor, iD string, selectCols ...string) (*Organization, error) {
	organizationObj := &Organization{}

	sel := "*"
	if len(selectCols) > 0 {
		sel = strings.Join(strmangle.IdentQuoteSlice(dialect.LQ, dialect.RQ, selectCols), ",")
	}
	query := fmt.Sprintf(
		"select %s from \"keeptruckin\".\"organization\" where \"id\"=$1", sel,
	)

	q := queries.Raw(query, iD)

	err := q.Bind(ctx, exec, organizationObj)
	if err != nil {
		if errors.Is(err, sql.ErrNoRows) {
			return nil, sql.ErrNoRows
		}
		return nil, errors.Wrap(err, "keeptruckin: unable to select from organization")
	}

	if err = organizationObj.doAfterSelectHooks(ctx, exec); err != nil {
		return organizationObj, err
	}

	return organizationObj, nil
}

// Insert a single record using an executor.
// See boil.Columns.InsertColumnSet documentation to understand column list inference for inserts.
func (o *Organization) Insert(ctx context.Context, exec boil.ContextExecutor, columns boil.Columns) error {
	if o == nil {
		return errors.New("keeptruckin: no organization provided for insertion")
	}

	var err error
	if !boil.TimestampsAreSkipped(ctx) {
		currTime := time.Now().In(boil.GetLocation())

		if o.CreatedAt.IsZero() {
			o.CreatedAt = currTime
		}
		if o.UpdatedAt.IsZero() {
			o.UpdatedAt = currTime
		}
	}

	if err := o.doBeforeInsertHooks(ctx, exec); err != nil {
		return err
	}

	nzDefaults := queries.NonZeroDefaultSet(organizationColumnsWithDefault, o)

	key := makeCacheKey(columns, nzDefaults)
	organizationInsertCacheMut.RLock()
	cache, cached := organizationInsertCache[key]
	organizationInsertCacheMut.RUnlock()

	if !cached {
		wl, returnColumns := columns.InsertColumnSet(
			organizationAllColumns,
			organizationColumnsWithDefault,
			organizationColumnsWithoutDefault,
			nzDefaults,
		)

		cache.valueMapping, err = queries.BindMapping(organizationType, organizationMapping, wl)
		if err != nil {
			return err
		}
		cache.retMapping, err = queries.BindMapping(organizationType, organizationMapping, returnColumns)
		if err != nil {
			return err
		}
		if len(wl) != 0 {
			cache.query = fmt.Sprintf("INSERT INTO \"keeptruckin\".\"organization\" (\"%s\") %%sVALUES (%s)%%s", strings.Join(wl, "\",\""), strmangle.Placeholders(dialect.UseIndexPlaceholders, len(wl), 1, 1))
		} else {
			cache.query = "INSERT INTO \"keeptruckin\".\"organization\" %sDEFAULT VALUES%s"
		}

		var queryOutput, queryReturning string

		if len(cache.retMapping) != 0 {
			queryReturning = fmt.Sprintf(" RETURNING \"%s\"", strings.Join(returnColumns, "\",\""))
		}

		cache.query = fmt.Sprintf(cache.query, queryOutput, queryReturning)
	}

	value := reflect.Indirect(reflect.ValueOf(o))
	vals := queries.ValuesFromMapping(value, cache.valueMapping)

	if boil.IsDebug(ctx) {
		writer := boil.DebugWriterFrom(ctx)
		fmt.Fprintln(writer, cache.query)
		fmt.Fprintln(writer, vals)
	}

	if len(cache.retMapping) != 0 {
		err = exec.QueryRowContext(ctx, cache.query, vals...).Scan(queries.PtrsFromMapping(value, cache.retMapping)...)
	} else {
		_, err = exec.ExecContext(ctx, cache.query, vals...)
	}

	if err != nil {
		return errors.Wrap(err, "keeptruckin: unable to insert into organization")
	}

	if !cached {
		organizationInsertCacheMut.Lock()
		organizationInsertCache[key] = cache
		organizationInsertCacheMut.Unlock()
	}

	return o.doAfterInsertHooks(ctx, exec)
}

// Update uses an executor to update the Organization.
// See boil.Columns.UpdateColumnSet documentation to understand column list inference for updates.
// Update does not automatically update the record in case of default values. Use .Reload() to refresh the records.
func (o *Organization) Update(ctx context.Context, exec boil.ContextExecutor, columns boil.Columns) (int64, error) {
	if !boil.TimestampsAreSkipped(ctx) {
		currTime := time.Now().In(boil.GetLocation())

		o.UpdatedAt = currTime
	}

	var err error
	if err = o.doBeforeUpdateHooks(ctx, exec); err != nil {
		return 0, err
	}
	key := makeCacheKey(columns, nil)
	organizationUpdateCacheMut.RLock()
	cache, cached := organizationUpdateCache[key]
	organizationUpdateCacheMut.RUnlock()

	if !cached {
		wl := columns.UpdateColumnSet(
			organizationAllColumns,
			organizationPrimaryKeyColumns,
		)

		if !columns.IsWhitelist() {
			wl = strmangle.SetComplement(wl, []string{"created_at"})
		}
		if len(wl) == 0 {
			return 0, errors.New("keeptruckin: unable to update organization, could not build whitelist")
		}

		cache.query = fmt.Sprintf("UPDATE \"keeptruckin\".\"organization\" SET %s WHERE %s",
			strmangle.SetParamNames("\"", "\"", 1, wl),
			strmangle.WhereClause("\"", "\"", len(wl)+1, organizationPrimaryKeyColumns),
		)
		cache.valueMapping, err = queries.BindMapping(organizationType, organizationMapping, append(wl, organizationPrimaryKeyColumns...))
		if err != nil {
			return 0, err
		}
	}

	values := queries.ValuesFromMapping(reflect.Indirect(reflect.ValueOf(o)), cache.valueMapping)

	if boil.IsDebug(ctx) {
		writer := boil.DebugWriterFrom(ctx)
		fmt.Fprintln(writer, cache.query)
		fmt.Fprintln(writer, values)
	}
	var result sql.Result
	result, err = exec.ExecContext(ctx, cache.query, values...)
	if err != nil {
		return 0, errors.Wrap(err, "keeptruckin: unable to update organization row")
	}

	rowsAff, err := result.RowsAffected()
	if err != nil {
		return 0, errors.Wrap(err, "keeptruckin: failed to get rows affected by update for organization")
	}

	if !cached {
		organizationUpdateCacheMut.Lock()
		organizationUpdateCache[key] = cache
		organizationUpdateCacheMut.Unlock()
	}

	return rowsAff, o.doAfterUpdateHooks(ctx, exec)
}

// UpdateAll updates all rows with the specified column values.
func (q organizationQuery) UpdateAll(ctx context.Context, exec boil.ContextExecutor, cols M) (int64, error) {
	queries.SetUpdate(q.Query, cols)

	result, err := q.Query.ExecContext(ctx, exec)
	if err != nil {
		return 0, errors.Wrap(err, "keeptruckin: unable to update all for organization")
	}

	rowsAff, err := result.RowsAffected()
	if err != nil {
		return 0, errors.Wrap(err, "keeptruckin: unable to retrieve rows affected for organization")
	}

	return rowsAff, nil
}

// UpdateAll updates all rows with the specified column values, using an executor.
func (o OrganizationSlice) UpdateAll(ctx context.Context, exec boil.ContextExecutor, cols M) (int64, error) {
	ln := int64(len(o))
	if ln == 0 {
		return 0, nil
	}

	if len(cols) == 0 {
		return 0, errors.New("keeptruckin: update all requires at least one column argument")
	}

	colNames := make([]string, len(cols))
	args := make([]interface{}, len(cols))

	i := 0
	for name, value := range cols {
		colNames[i] = name
		args[i] = value
		i++
	}

	// Append all of the primary key values for each column
	for _, obj := range o {
		pkeyArgs := queries.ValuesFromMapping(reflect.Indirect(reflect.ValueOf(obj)), organizationPrimaryKeyMapping)
		args = append(args, pkeyArgs...)
	}

	sql := fmt.Sprintf("UPDATE \"keeptruckin\".\"organization\" SET %s WHERE %s",
		strmangle.SetParamNames("\"", "\"", 1, colNames),
		strmangle.WhereClauseRepeated(string(dialect.LQ), string(dialect.RQ), len(colNames)+1, organizationPrimaryKeyColumns, len(o)))

	if boil.IsDebug(ctx) {
		writer := boil.DebugWriterFrom(ctx)
		fmt.Fprintln(writer, sql)
		fmt.Fprintln(writer, args...)
	}
	result, err := exec.ExecContext(ctx, sql, args...)
	if err != nil {
		return 0, errors.Wrap(err, "keeptruckin: unable to update all in organization slice")
	}

	rowsAff, err := result.RowsAffected()
	if err != nil {
		return 0, errors.Wrap(err, "keeptruckin: unable to retrieve rows affected all in update all organization")
	}
	return rowsAff, nil
}

// Upsert attempts an insert using an executor, and does an update or ignore on conflict.
// See boil.Columns documentation for how to properly use updateColumns and insertColumns.
func (o *Organization) Upsert(ctx context.Context, exec boil.ContextExecutor, updateOnConflict bool, conflictColumns []string, updateColumns, insertColumns boil.Columns) error {
	if o == nil {
		return errors.New("keeptruckin: no organization provided for upsert")
	}
	if !boil.TimestampsAreSkipped(ctx) {
		currTime := time.Now().In(boil.GetLocation())

		if o.CreatedAt.IsZero() {
			o.CreatedAt = currTime
		}
		o.UpdatedAt = currTime
	}

	if err := o.doBeforeUpsertHooks(ctx, exec); err != nil {
		return err
	}

	nzDefaults := queries.NonZeroDefaultSet(organizationColumnsWithDefault, o)

	// Build cache key in-line uglily - mysql vs psql problems
	buf := strmangle.GetBuffer()
	if updateOnConflict {
		buf.WriteByte('t')
	} else {
		buf.WriteByte('f')
	}
	buf.WriteByte('.')
	for _, c := range conflictColumns {
		buf.WriteString(c)
	}
	buf.WriteByte('.')
	buf.WriteString(strconv.Itoa(updateColumns.Kind))
	for _, c := range updateColumns.Cols {
		buf.WriteString(c)
	}
	buf.WriteByte('.')
	buf.WriteString(strconv.Itoa(insertColumns.Kind))
	for _, c := range insertColumns.Cols {
		buf.WriteString(c)
	}
	buf.WriteByte('.')
	for _, c := range nzDefaults {
		buf.WriteString(c)
	}
	key := buf.String()
	strmangle.PutBuffer(buf)

	organizationUpsertCacheMut.RLock()
	cache, cached := organizationUpsertCache[key]
	organizationUpsertCacheMut.RUnlock()

	var err error

	if !cached {
		insert, ret := insertColumns.InsertColumnSet(
			organizationAllColumns,
			organizationColumnsWithDefault,
			organizationColumnsWithoutDefault,
			nzDefaults,
		)

		update := updateColumns.UpdateColumnSet(
			organizationAllColumns,
			organizationPrimaryKeyColumns,
		)

		if updateOnConflict && len(update) == 0 {
			return errors.New("keeptruckin: unable to upsert organization, could not build update column list")
		}

		conflict := conflictColumns
		if len(conflict) == 0 {
			conflict = make([]string, len(organizationPrimaryKeyColumns))
			copy(conflict, organizationPrimaryKeyColumns)
		}
		cache.query = buildUpsertQueryPostgres(dialect, "\"keeptruckin\".\"organization\"", updateOnConflict, ret, update, conflict, insert)

		cache.valueMapping, err = queries.BindMapping(organizationType, organizationMapping, insert)
		if err != nil {
			return err
		}
		if len(ret) != 0 {
			cache.retMapping, err = queries.BindMapping(organizationType, organizationMapping, ret)
			if err != nil {
				return err
			}
		}
	}

	value := reflect.Indirect(reflect.ValueOf(o))
	vals := queries.ValuesFromMapping(value, cache.valueMapping)
	var returns []interface{}
	if len(cache.retMapping) != 0 {
		returns = queries.PtrsFromMapping(value, cache.retMapping)
	}

	if boil.IsDebug(ctx) {
		writer := boil.DebugWriterFrom(ctx)
		fmt.Fprintln(writer, cache.query)
		fmt.Fprintln(writer, vals)
	}
	if len(cache.retMapping) != 0 {
		err = exec.QueryRowContext(ctx, cache.query, vals...).Scan(returns...)
		if errors.Is(err, sql.ErrNoRows) {
			err = nil // Postgres doesn't return anything when there's no update
		}
	} else {
		_, err = exec.ExecContext(ctx, cache.query, vals...)
	}
	if err != nil {
		return errors.Wrap(err, "keeptruckin: unable to upsert organization")
	}

	if !cached {
		organizationUpsertCacheMut.Lock()
		organizationUpsertCache[key] = cache
		organizationUpsertCacheMut.Unlock()
	}

	return o.doAfterUpsertHooks(ctx, exec)
}

// Delete deletes a single Organization record with an executor.
// Delete will match against the primary key column to find the record to delete.
func (o *Organization) Delete(ctx context.Context, exec boil.ContextExecutor) (int64, error) {
	if o == nil {
		return 0, errors.New("keeptruckin: no Organization provided for delete")
	}

	if err := o.doBeforeDeleteHooks(ctx, exec); err != nil {
		return 0, err
	}

	args := queries.ValuesFromMapping(reflect.Indirect(reflect.ValueOf(o)), organizationPrimaryKeyMapping)
	sql := "DELETE FROM \"keeptruckin\".\"organization\" WHERE \"id\"=$1"

	if boil.IsDebug(ctx) {
		writer := boil.DebugWriterFrom(ctx)
		fmt.Fprintln(writer, sql)
		fmt.Fprintln(writer, args...)
	}
	result, err := exec.ExecContext(ctx, sql, args...)
	if err != nil {
		return 0, errors.Wrap(err, "keeptruckin: unable to delete from organization")
	}

	rowsAff, err := result.RowsAffected()
	if err != nil {
		return 0, errors.Wrap(err, "keeptruckin: failed to get rows affected by delete for organization")
	}

	if err := o.doAfterDeleteHooks(ctx, exec); err != nil {
		return 0, err
	}

	return rowsAff, nil
}

// DeleteAll deletes all matching rows.
func (q organizationQuery) DeleteAll(ctx context.Context, exec boil.ContextExecutor) (int64, error) {
	if q.Query == nil {
		return 0, errors.New("keeptruckin: no organizationQuery provided for delete all")
	}

	queries.SetDelete(q.Query)

	result, err := q.Query.ExecContext(ctx, exec)
	if err != nil {
		return 0, errors.Wrap(err, "keeptruckin: unable to delete all from organization")
	}

	rowsAff, err := result.RowsAffected()
	if err != nil {
		return 0, errors.Wrap(err, "keeptruckin: failed to get rows affected by deleteall for organization")
	}

	return rowsAff, nil
}

// DeleteAll deletes all rows in the slice, using an executor.
func (o OrganizationSlice) DeleteAll(ctx context.Context, exec boil.ContextExecutor) (int64, error) {
	if len(o) == 0 {
		return 0, nil
	}

	if len(organizationBeforeDeleteHooks) != 0 {
		for _, obj := range o {
			if err := obj.doBeforeDeleteHooks(ctx, exec); err != nil {
				return 0, err
			}
		}
	}

	var args []interface{}
	for _, obj := range o {
		pkeyArgs := queries.ValuesFromMapping(reflect.Indirect(reflect.ValueOf(obj)), organizationPrimaryKeyMapping)
		args = append(args, pkeyArgs...)
	}

	sql := "DELETE FROM \"keeptruckin\".\"organization\" WHERE " +
		strmangle.WhereClauseRepeated(string(dialect.LQ), string(dialect.RQ), 1, organizationPrimaryKeyColumns, len(o))

	if boil.IsDebug(ctx) {
		writer := boil.DebugWriterFrom(ctx)
		fmt.Fprintln(writer, sql)
		fmt.Fprintln(writer, args)
	}
	result, err := exec.ExecContext(ctx, sql, args...)
	if err != nil {
		return 0, errors.Wrap(err, "keeptruckin: unable to delete all from organization slice")
	}

	rowsAff, err := result.RowsAffected()
	if err != nil {
		return 0, errors.Wrap(err, "keeptruckin: failed to get rows affected by deleteall for organization")
	}

	if len(organizationAfterDeleteHooks) != 0 {
		for _, obj := range o {
			if err := obj.doAfterDeleteHooks(ctx, exec); err != nil {
				return 0, err
			}
		}
	}

	return rowsAff, nil
}

// Reload refetches the object from the database
// using the primary keys with an executor.
func (o *Organization) Reload(ctx context.Context, exec boil.ContextExecutor) error {
	ret, err := FindOrganization(ctx, exec, o.ID)
	if err != nil {
		return err
	}

	*o = *ret
	return nil
}

// ReloadAll refetches every row with matching primary key column values
// and overwrites the original object slice with the newly updated slice.
func (o *OrganizationSlice) ReloadAll(ctx context.Context, exec boil.ContextExecutor) error {
	if o == nil || len(*o) == 0 {
		return nil
	}

	slice := OrganizationSlice{}
	var args []interface{}
	for _, obj := range *o {
		pkeyArgs := queries.ValuesFromMapping(reflect.Indirect(reflect.ValueOf(obj)), organizationPrimaryKeyMapping)
		args = append(args, pkeyArgs...)
	}

	sql := "SELECT \"keeptruckin\".\"organization\".* FROM \"keeptruckin\".\"organization\" WHERE " +
		strmangle.WhereClauseRepeated(string(dialect.LQ), string(dialect.RQ), 1, organizationPrimaryKeyColumns, len(*o))

	q := queries.Raw(sql, args...)

	err := q.Bind(ctx, exec, &slice)
	if err != nil {
		return errors.Wrap(err, "keeptruckin: unable to reload all in OrganizationSlice")
	}

	*o = slice

	return nil
}

// OrganizationExists checks if the Organization row exists.
func OrganizationExists(ctx context.Context, exec boil.ContextExecutor, iD string) (bool, error) {
	var exists bool
	sql := "select exists(select 1 from \"keeptruckin\".\"organization\" where \"id\"=$1 limit 1)"

	if boil.IsDebug(ctx) {
		writer := boil.DebugWriterFrom(ctx)
		fmt.Fprintln(writer, sql)
		fmt.Fprintln(writer, iD)
	}
	row := exec.QueryRowContext(ctx, sql, iD)

	err := row.Scan(&exists)
	if err != nil {
		return false, errors.Wrap(err, "keeptruckin: unable to check if organization exists")
	}

	return exists, nil
}
