// Code generated by SQLBoiler (https://github.com/volatiletech/sqlboiler). DO NOT EDIT.
// This file is meant to be re-generated in place and/or deleted at any time.

package experiments

import (
	"context"
	"database/sql"
	"fmt"
	"reflect"
	"strconv"
	"strings"
	"sync"
	"time"

	"github.com/friendsofgo/errors"
	"github.com/volatiletech/null/v8"
	"github.com/volatiletech/sqlboiler/v4/boil"
	"github.com/volatiletech/sqlboiler/v4/queries"
	"github.com/volatiletech/sqlboiler/v4/queries/qm"
	"github.com/volatiletech/sqlboiler/v4/queries/qmhelper"
	"github.com/volatiletech/strmangle"
)

// ExperimentDetail is an object representing the database table.
type ExperimentDetail struct {
	ID               string      `boil:"id" json:"id" toml:"id" yaml:"id"`
	Name             string      `boil:"name" json:"name" toml:"name" yaml:"name"`
	Description      null.String `boil:"description" json:"description,omitempty" toml:"description" yaml:"description,omitempty"`
	CreatedBy        string      `boil:"created_by" json:"created_by" toml:"created_by" yaml:"created_by"`
	Enabled          bool        `boil:"enabled" json:"enabled" toml:"enabled" yaml:"enabled"`
	Domain           string      `boil:"domain" json:"domain" toml:"domain" yaml:"domain"`
	CreatedAt        time.Time   `boil:"created_at" json:"created_at" toml:"created_at" yaml:"created_at"`
	UpdatedAt        time.Time   `boil:"updated_at" json:"updated_at" toml:"updated_at" yaml:"updated_at"`
	SubjectType      string      `boil:"subject_type" json:"subject_type" toml:"subject_type" yaml:"subject_type"`
	AllocationMethod string      `boil:"allocation_method" json:"allocation_method" toml:"allocation_method" yaml:"allocation_method"`

	R *experimentDetailR `boil:"" json:"" toml:"" yaml:""`
	L experimentDetailL  `boil:"-" json:"-" toml:"-" yaml:"-"`
}

var ExperimentDetailColumns = struct {
	ID               string
	Name             string
	Description      string
	CreatedBy        string
	Enabled          string
	Domain           string
	CreatedAt        string
	UpdatedAt        string
	SubjectType      string
	AllocationMethod string
}{
	ID:               "id",
	Name:             "name",
	Description:      "description",
	CreatedBy:        "created_by",
	Enabled:          "enabled",
	Domain:           "domain",
	CreatedAt:        "created_at",
	UpdatedAt:        "updated_at",
	SubjectType:      "subject_type",
	AllocationMethod: "allocation_method",
}

var ExperimentDetailTableColumns = struct {
	ID               string
	Name             string
	Description      string
	CreatedBy        string
	Enabled          string
	Domain           string
	CreatedAt        string
	UpdatedAt        string
	SubjectType      string
	AllocationMethod string
}{
	ID:               "experiment_details.id",
	Name:             "experiment_details.name",
	Description:      "experiment_details.description",
	CreatedBy:        "experiment_details.created_by",
	Enabled:          "experiment_details.enabled",
	Domain:           "experiment_details.domain",
	CreatedAt:        "experiment_details.created_at",
	UpdatedAt:        "experiment_details.updated_at",
	SubjectType:      "experiment_details.subject_type",
	AllocationMethod: "experiment_details.allocation_method",
}

// Generated where

var ExperimentDetailWhere = struct {
	ID               whereHelperstring
	Name             whereHelperstring
	Description      whereHelpernull_String
	CreatedBy        whereHelperstring
	Enabled          whereHelperbool
	Domain           whereHelperstring
	CreatedAt        whereHelpertime_Time
	UpdatedAt        whereHelpertime_Time
	SubjectType      whereHelperstring
	AllocationMethod whereHelperstring
}{
	ID:               whereHelperstring{field: "\"experiments\".\"experiment_details\".\"id\""},
	Name:             whereHelperstring{field: "\"experiments\".\"experiment_details\".\"name\""},
	Description:      whereHelpernull_String{field: "\"experiments\".\"experiment_details\".\"description\""},
	CreatedBy:        whereHelperstring{field: "\"experiments\".\"experiment_details\".\"created_by\""},
	Enabled:          whereHelperbool{field: "\"experiments\".\"experiment_details\".\"enabled\""},
	Domain:           whereHelperstring{field: "\"experiments\".\"experiment_details\".\"domain\""},
	CreatedAt:        whereHelpertime_Time{field: "\"experiments\".\"experiment_details\".\"created_at\""},
	UpdatedAt:        whereHelpertime_Time{field: "\"experiments\".\"experiment_details\".\"updated_at\""},
	SubjectType:      whereHelperstring{field: "\"experiments\".\"experiment_details\".\"subject_type\""},
	AllocationMethod: whereHelperstring{field: "\"experiments\".\"experiment_details\".\"allocation_method\""},
}

// ExperimentDetailRels is where relationship names are stored.
var ExperimentDetailRels = struct {
	ExperimentApplicabilities string
	ExperimentAssignments     string
	ExperimentEvents          string
	ExperimentStatusHistories string
	ExperimentVariants        string
}{
	ExperimentApplicabilities: "ExperimentApplicabilities",
	ExperimentAssignments:     "ExperimentAssignments",
	ExperimentEvents:          "ExperimentEvents",
	ExperimentStatusHistories: "ExperimentStatusHistories",
	ExperimentVariants:        "ExperimentVariants",
}

// experimentDetailR is where relationships are stored.
type experimentDetailR struct {
	ExperimentApplicabilities ApplicabilitySlice `boil:"ExperimentApplicabilities" json:"ExperimentApplicabilities" toml:"ExperimentApplicabilities" yaml:"ExperimentApplicabilities"`
	ExperimentAssignments     AssignmentSlice    `boil:"ExperimentAssignments" json:"ExperimentAssignments" toml:"ExperimentAssignments" yaml:"ExperimentAssignments"`
	ExperimentEvents          EventSlice         `boil:"ExperimentEvents" json:"ExperimentEvents" toml:"ExperimentEvents" yaml:"ExperimentEvents"`
	ExperimentStatusHistories StatusHistorySlice `boil:"ExperimentStatusHistories" json:"ExperimentStatusHistories" toml:"ExperimentStatusHistories" yaml:"ExperimentStatusHistories"`
	ExperimentVariants        VariantSlice       `boil:"ExperimentVariants" json:"ExperimentVariants" toml:"ExperimentVariants" yaml:"ExperimentVariants"`
}

// NewStruct creates a new relationship struct
func (*experimentDetailR) NewStruct() *experimentDetailR {
	return &experimentDetailR{}
}

// experimentDetailL is where Load methods for each relationship are stored.
type experimentDetailL struct{}

var (
	experimentDetailAllColumns            = []string{"id", "name", "description", "created_by", "enabled", "domain", "created_at", "updated_at", "subject_type", "allocation_method"}
	experimentDetailColumnsWithoutDefault = []string{"id", "name", "created_by", "enabled", "domain", "created_at", "updated_at"}
	experimentDetailColumnsWithDefault    = []string{"description", "subject_type", "allocation_method"}
	experimentDetailPrimaryKeyColumns     = []string{"id"}
	experimentDetailGeneratedColumns      = []string{}
)

type (
	// ExperimentDetailSlice is an alias for a slice of pointers to ExperimentDetail.
	// This should almost always be used instead of []ExperimentDetail.
	ExperimentDetailSlice []*ExperimentDetail
	// ExperimentDetailHook is the signature for custom ExperimentDetail hook methods
	ExperimentDetailHook func(context.Context, boil.ContextExecutor, *ExperimentDetail) error

	experimentDetailQuery struct {
		*queries.Query
	}
)

// Cache for insert, update and upsert
var (
	experimentDetailType                 = reflect.TypeOf(&ExperimentDetail{})
	experimentDetailMapping              = queries.MakeStructMapping(experimentDetailType)
	experimentDetailPrimaryKeyMapping, _ = queries.BindMapping(experimentDetailType, experimentDetailMapping, experimentDetailPrimaryKeyColumns)
	experimentDetailInsertCacheMut       sync.RWMutex
	experimentDetailInsertCache          = make(map[string]insertCache)
	experimentDetailUpdateCacheMut       sync.RWMutex
	experimentDetailUpdateCache          = make(map[string]updateCache)
	experimentDetailUpsertCacheMut       sync.RWMutex
	experimentDetailUpsertCache          = make(map[string]insertCache)
)

var (
	// Force time package dependency for automated UpdatedAt/CreatedAt.
	_ = time.Second
	// Force qmhelper dependency for where clause generation (which doesn't
	// always happen)
	_ = qmhelper.Where
)

var experimentDetailAfterSelectHooks []ExperimentDetailHook

var experimentDetailBeforeInsertHooks []ExperimentDetailHook
var experimentDetailAfterInsertHooks []ExperimentDetailHook

var experimentDetailBeforeUpdateHooks []ExperimentDetailHook
var experimentDetailAfterUpdateHooks []ExperimentDetailHook

var experimentDetailBeforeDeleteHooks []ExperimentDetailHook
var experimentDetailAfterDeleteHooks []ExperimentDetailHook

var experimentDetailBeforeUpsertHooks []ExperimentDetailHook
var experimentDetailAfterUpsertHooks []ExperimentDetailHook

// doAfterSelectHooks executes all "after Select" hooks.
func (o *ExperimentDetail) doAfterSelectHooks(ctx context.Context, exec boil.ContextExecutor) (err error) {
	if boil.HooksAreSkipped(ctx) {
		return nil
	}

	for _, hook := range experimentDetailAfterSelectHooks {
		if err := hook(ctx, exec, o); err != nil {
			return err
		}
	}

	return nil
}

// doBeforeInsertHooks executes all "before insert" hooks.
func (o *ExperimentDetail) doBeforeInsertHooks(ctx context.Context, exec boil.ContextExecutor) (err error) {
	if boil.HooksAreSkipped(ctx) {
		return nil
	}

	for _, hook := range experimentDetailBeforeInsertHooks {
		if err := hook(ctx, exec, o); err != nil {
			return err
		}
	}

	return nil
}

// doAfterInsertHooks executes all "after Insert" hooks.
func (o *ExperimentDetail) doAfterInsertHooks(ctx context.Context, exec boil.ContextExecutor) (err error) {
	if boil.HooksAreSkipped(ctx) {
		return nil
	}

	for _, hook := range experimentDetailAfterInsertHooks {
		if err := hook(ctx, exec, o); err != nil {
			return err
		}
	}

	return nil
}

// doBeforeUpdateHooks executes all "before Update" hooks.
func (o *ExperimentDetail) doBeforeUpdateHooks(ctx context.Context, exec boil.ContextExecutor) (err error) {
	if boil.HooksAreSkipped(ctx) {
		return nil
	}

	for _, hook := range experimentDetailBeforeUpdateHooks {
		if err := hook(ctx, exec, o); err != nil {
			return err
		}
	}

	return nil
}

// doAfterUpdateHooks executes all "after Update" hooks.
func (o *ExperimentDetail) doAfterUpdateHooks(ctx context.Context, exec boil.ContextExecutor) (err error) {
	if boil.HooksAreSkipped(ctx) {
		return nil
	}

	for _, hook := range experimentDetailAfterUpdateHooks {
		if err := hook(ctx, exec, o); err != nil {
			return err
		}
	}

	return nil
}

// doBeforeDeleteHooks executes all "before Delete" hooks.
func (o *ExperimentDetail) doBeforeDeleteHooks(ctx context.Context, exec boil.ContextExecutor) (err error) {
	if boil.HooksAreSkipped(ctx) {
		return nil
	}

	for _, hook := range experimentDetailBeforeDeleteHooks {
		if err := hook(ctx, exec, o); err != nil {
			return err
		}
	}

	return nil
}

// doAfterDeleteHooks executes all "after Delete" hooks.
func (o *ExperimentDetail) doAfterDeleteHooks(ctx context.Context, exec boil.ContextExecutor) (err error) {
	if boil.HooksAreSkipped(ctx) {
		return nil
	}

	for _, hook := range experimentDetailAfterDeleteHooks {
		if err := hook(ctx, exec, o); err != nil {
			return err
		}
	}

	return nil
}

// doBeforeUpsertHooks executes all "before Upsert" hooks.
func (o *ExperimentDetail) doBeforeUpsertHooks(ctx context.Context, exec boil.ContextExecutor) (err error) {
	if boil.HooksAreSkipped(ctx) {
		return nil
	}

	for _, hook := range experimentDetailBeforeUpsertHooks {
		if err := hook(ctx, exec, o); err != nil {
			return err
		}
	}

	return nil
}

// doAfterUpsertHooks executes all "after Upsert" hooks.
func (o *ExperimentDetail) doAfterUpsertHooks(ctx context.Context, exec boil.ContextExecutor) (err error) {
	if boil.HooksAreSkipped(ctx) {
		return nil
	}

	for _, hook := range experimentDetailAfterUpsertHooks {
		if err := hook(ctx, exec, o); err != nil {
			return err
		}
	}

	return nil
}

// AddExperimentDetailHook registers your hook function for all future operations.
func AddExperimentDetailHook(hookPoint boil.HookPoint, experimentDetailHook ExperimentDetailHook) {
	switch hookPoint {
	case boil.AfterSelectHook:
		experimentDetailAfterSelectHooks = append(experimentDetailAfterSelectHooks, experimentDetailHook)
	case boil.BeforeInsertHook:
		experimentDetailBeforeInsertHooks = append(experimentDetailBeforeInsertHooks, experimentDetailHook)
	case boil.AfterInsertHook:
		experimentDetailAfterInsertHooks = append(experimentDetailAfterInsertHooks, experimentDetailHook)
	case boil.BeforeUpdateHook:
		experimentDetailBeforeUpdateHooks = append(experimentDetailBeforeUpdateHooks, experimentDetailHook)
	case boil.AfterUpdateHook:
		experimentDetailAfterUpdateHooks = append(experimentDetailAfterUpdateHooks, experimentDetailHook)
	case boil.BeforeDeleteHook:
		experimentDetailBeforeDeleteHooks = append(experimentDetailBeforeDeleteHooks, experimentDetailHook)
	case boil.AfterDeleteHook:
		experimentDetailAfterDeleteHooks = append(experimentDetailAfterDeleteHooks, experimentDetailHook)
	case boil.BeforeUpsertHook:
		experimentDetailBeforeUpsertHooks = append(experimentDetailBeforeUpsertHooks, experimentDetailHook)
	case boil.AfterUpsertHook:
		experimentDetailAfterUpsertHooks = append(experimentDetailAfterUpsertHooks, experimentDetailHook)
	}
}

// One returns a single experimentDetail record from the query.
func (q experimentDetailQuery) One(ctx context.Context, exec boil.ContextExecutor) (*ExperimentDetail, error) {
	o := &ExperimentDetail{}

	queries.SetLimit(q.Query, 1)

	err := q.Bind(ctx, exec, o)
	if err != nil {
		if errors.Is(err, sql.ErrNoRows) {
			return nil, sql.ErrNoRows
		}
		return nil, errors.Wrap(err, "experiments: failed to execute a one query for experiment_details")
	}

	if err := o.doAfterSelectHooks(ctx, exec); err != nil {
		return o, err
	}

	return o, nil
}

// All returns all ExperimentDetail records from the query.
func (q experimentDetailQuery) All(ctx context.Context, exec boil.ContextExecutor) (ExperimentDetailSlice, error) {
	var o []*ExperimentDetail

	err := q.Bind(ctx, exec, &o)
	if err != nil {
		return nil, errors.Wrap(err, "experiments: failed to assign all query results to ExperimentDetail slice")
	}

	if len(experimentDetailAfterSelectHooks) != 0 {
		for _, obj := range o {
			if err := obj.doAfterSelectHooks(ctx, exec); err != nil {
				return o, err
			}
		}
	}

	return o, nil
}

// Count returns the count of all ExperimentDetail records in the query.
func (q experimentDetailQuery) Count(ctx context.Context, exec boil.ContextExecutor) (int64, error) {
	var count int64

	queries.SetSelect(q.Query, nil)
	queries.SetCount(q.Query)

	err := q.Query.QueryRowContext(ctx, exec).Scan(&count)
	if err != nil {
		return 0, errors.Wrap(err, "experiments: failed to count experiment_details rows")
	}

	return count, nil
}

// Exists checks if the row exists in the table.
func (q experimentDetailQuery) Exists(ctx context.Context, exec boil.ContextExecutor) (bool, error) {
	var count int64

	queries.SetSelect(q.Query, nil)
	queries.SetCount(q.Query)
	queries.SetLimit(q.Query, 1)

	err := q.Query.QueryRowContext(ctx, exec).Scan(&count)
	if err != nil {
		return false, errors.Wrap(err, "experiments: failed to check if experiment_details exists")
	}

	return count > 0, nil
}

// ExperimentApplicabilities retrieves all the applicability's Applicabilities with an executor via experiment_id column.
func (o *ExperimentDetail) ExperimentApplicabilities(mods ...qm.QueryMod) applicabilityQuery {
	var queryMods []qm.QueryMod
	if len(mods) != 0 {
		queryMods = append(queryMods, mods...)
	}

	queryMods = append(queryMods,
		qm.Where("\"experiments\".\"applicabilities\".\"experiment_id\"=?", o.ID),
	)

	return Applicabilities(queryMods...)
}

// ExperimentAssignments retrieves all the assignment's Assignments with an executor via experiment_id column.
func (o *ExperimentDetail) ExperimentAssignments(mods ...qm.QueryMod) assignmentQuery {
	var queryMods []qm.QueryMod
	if len(mods) != 0 {
		queryMods = append(queryMods, mods...)
	}

	queryMods = append(queryMods,
		qm.Where("\"experiments\".\"assignments\".\"experiment_id\"=?", o.ID),
	)

	return Assignments(queryMods...)
}

// ExperimentEvents retrieves all the event's Events with an executor via experiment_id column.
func (o *ExperimentDetail) ExperimentEvents(mods ...qm.QueryMod) eventQuery {
	var queryMods []qm.QueryMod
	if len(mods) != 0 {
		queryMods = append(queryMods, mods...)
	}

	queryMods = append(queryMods,
		qm.Where("\"experiments\".\"events\".\"experiment_id\"=?", o.ID),
	)

	return Events(queryMods...)
}

// ExperimentStatusHistories retrieves all the status_history's StatusHistories with an executor via experiment_id column.
func (o *ExperimentDetail) ExperimentStatusHistories(mods ...qm.QueryMod) statusHistoryQuery {
	var queryMods []qm.QueryMod
	if len(mods) != 0 {
		queryMods = append(queryMods, mods...)
	}

	queryMods = append(queryMods,
		qm.Where("\"experiments\".\"status_histories\".\"experiment_id\"=?", o.ID),
	)

	return StatusHistories(queryMods...)
}

// ExperimentVariants retrieves all the variant's Variants with an executor via experiment_id column.
func (o *ExperimentDetail) ExperimentVariants(mods ...qm.QueryMod) variantQuery {
	var queryMods []qm.QueryMod
	if len(mods) != 0 {
		queryMods = append(queryMods, mods...)
	}

	queryMods = append(queryMods,
		qm.Where("\"experiments\".\"variants\".\"experiment_id\"=?", o.ID),
	)

	return Variants(queryMods...)
}

// LoadExperimentApplicabilities allows an eager lookup of values, cached into the
// loaded structs of the objects. This is for a 1-M or N-M relationship.
func (experimentDetailL) LoadExperimentApplicabilities(ctx context.Context, e boil.ContextExecutor, singular bool, maybeExperimentDetail interface{}, mods queries.Applicator) error {
	var slice []*ExperimentDetail
	var object *ExperimentDetail

	if singular {
		object = maybeExperimentDetail.(*ExperimentDetail)
	} else {
		slice = *maybeExperimentDetail.(*[]*ExperimentDetail)
	}

	args := make([]interface{}, 0, 1)
	if singular {
		if object.R == nil {
			object.R = &experimentDetailR{}
		}
		args = append(args, object.ID)
	} else {
	Outer:
		for _, obj := range slice {
			if obj.R == nil {
				obj.R = &experimentDetailR{}
			}

			for _, a := range args {
				if a == obj.ID {
					continue Outer
				}
			}

			args = append(args, obj.ID)
		}
	}

	if len(args) == 0 {
		return nil
	}

	query := NewQuery(
		qm.From(`experiments.applicabilities`),
		qm.WhereIn(`experiments.applicabilities.experiment_id in ?`, args...),
	)
	if mods != nil {
		mods.Apply(query)
	}

	results, err := query.QueryContext(ctx, e)
	if err != nil {
		return errors.Wrap(err, "failed to eager load applicabilities")
	}

	var resultSlice []*Applicability
	if err = queries.Bind(results, &resultSlice); err != nil {
		return errors.Wrap(err, "failed to bind eager loaded slice applicabilities")
	}

	if err = results.Close(); err != nil {
		return errors.Wrap(err, "failed to close results in eager load on applicabilities")
	}
	if err = results.Err(); err != nil {
		return errors.Wrap(err, "error occurred during iteration of eager loaded relations for applicabilities")
	}

	if len(applicabilityAfterSelectHooks) != 0 {
		for _, obj := range resultSlice {
			if err := obj.doAfterSelectHooks(ctx, e); err != nil {
				return err
			}
		}
	}
	if singular {
		object.R.ExperimentApplicabilities = resultSlice
		for _, foreign := range resultSlice {
			if foreign.R == nil {
				foreign.R = &applicabilityR{}
			}
			foreign.R.Experiment = object
		}
		return nil
	}

	for _, foreign := range resultSlice {
		for _, local := range slice {
			if local.ID == foreign.ExperimentID {
				local.R.ExperimentApplicabilities = append(local.R.ExperimentApplicabilities, foreign)
				if foreign.R == nil {
					foreign.R = &applicabilityR{}
				}
				foreign.R.Experiment = local
				break
			}
		}
	}

	return nil
}

// LoadExperimentAssignments allows an eager lookup of values, cached into the
// loaded structs of the objects. This is for a 1-M or N-M relationship.
func (experimentDetailL) LoadExperimentAssignments(ctx context.Context, e boil.ContextExecutor, singular bool, maybeExperimentDetail interface{}, mods queries.Applicator) error {
	var slice []*ExperimentDetail
	var object *ExperimentDetail

	if singular {
		object = maybeExperimentDetail.(*ExperimentDetail)
	} else {
		slice = *maybeExperimentDetail.(*[]*ExperimentDetail)
	}

	args := make([]interface{}, 0, 1)
	if singular {
		if object.R == nil {
			object.R = &experimentDetailR{}
		}
		args = append(args, object.ID)
	} else {
	Outer:
		for _, obj := range slice {
			if obj.R == nil {
				obj.R = &experimentDetailR{}
			}

			for _, a := range args {
				if a == obj.ID {
					continue Outer
				}
			}

			args = append(args, obj.ID)
		}
	}

	if len(args) == 0 {
		return nil
	}

	query := NewQuery(
		qm.From(`experiments.assignments`),
		qm.WhereIn(`experiments.assignments.experiment_id in ?`, args...),
	)
	if mods != nil {
		mods.Apply(query)
	}

	results, err := query.QueryContext(ctx, e)
	if err != nil {
		return errors.Wrap(err, "failed to eager load assignments")
	}

	var resultSlice []*Assignment
	if err = queries.Bind(results, &resultSlice); err != nil {
		return errors.Wrap(err, "failed to bind eager loaded slice assignments")
	}

	if err = results.Close(); err != nil {
		return errors.Wrap(err, "failed to close results in eager load on assignments")
	}
	if err = results.Err(); err != nil {
		return errors.Wrap(err, "error occurred during iteration of eager loaded relations for assignments")
	}

	if len(assignmentAfterSelectHooks) != 0 {
		for _, obj := range resultSlice {
			if err := obj.doAfterSelectHooks(ctx, e); err != nil {
				return err
			}
		}
	}
	if singular {
		object.R.ExperimentAssignments = resultSlice
		for _, foreign := range resultSlice {
			if foreign.R == nil {
				foreign.R = &assignmentR{}
			}
			foreign.R.Experiment = object
		}
		return nil
	}

	for _, foreign := range resultSlice {
		for _, local := range slice {
			if local.ID == foreign.ExperimentID {
				local.R.ExperimentAssignments = append(local.R.ExperimentAssignments, foreign)
				if foreign.R == nil {
					foreign.R = &assignmentR{}
				}
				foreign.R.Experiment = local
				break
			}
		}
	}

	return nil
}

// LoadExperimentEvents allows an eager lookup of values, cached into the
// loaded structs of the objects. This is for a 1-M or N-M relationship.
func (experimentDetailL) LoadExperimentEvents(ctx context.Context, e boil.ContextExecutor, singular bool, maybeExperimentDetail interface{}, mods queries.Applicator) error {
	var slice []*ExperimentDetail
	var object *ExperimentDetail

	if singular {
		object = maybeExperimentDetail.(*ExperimentDetail)
	} else {
		slice = *maybeExperimentDetail.(*[]*ExperimentDetail)
	}

	args := make([]interface{}, 0, 1)
	if singular {
		if object.R == nil {
			object.R = &experimentDetailR{}
		}
		args = append(args, object.ID)
	} else {
	Outer:
		for _, obj := range slice {
			if obj.R == nil {
				obj.R = &experimentDetailR{}
			}

			for _, a := range args {
				if a == obj.ID {
					continue Outer
				}
			}

			args = append(args, obj.ID)
		}
	}

	if len(args) == 0 {
		return nil
	}

	query := NewQuery(
		qm.From(`experiments.events`),
		qm.WhereIn(`experiments.events.experiment_id in ?`, args...),
	)
	if mods != nil {
		mods.Apply(query)
	}

	results, err := query.QueryContext(ctx, e)
	if err != nil {
		return errors.Wrap(err, "failed to eager load events")
	}

	var resultSlice []*Event
	if err = queries.Bind(results, &resultSlice); err != nil {
		return errors.Wrap(err, "failed to bind eager loaded slice events")
	}

	if err = results.Close(); err != nil {
		return errors.Wrap(err, "failed to close results in eager load on events")
	}
	if err = results.Err(); err != nil {
		return errors.Wrap(err, "error occurred during iteration of eager loaded relations for events")
	}

	if len(eventAfterSelectHooks) != 0 {
		for _, obj := range resultSlice {
			if err := obj.doAfterSelectHooks(ctx, e); err != nil {
				return err
			}
		}
	}
	if singular {
		object.R.ExperimentEvents = resultSlice
		for _, foreign := range resultSlice {
			if foreign.R == nil {
				foreign.R = &eventR{}
			}
			foreign.R.Experiment = object
		}
		return nil
	}

	for _, foreign := range resultSlice {
		for _, local := range slice {
			if local.ID == foreign.ExperimentID {
				local.R.ExperimentEvents = append(local.R.ExperimentEvents, foreign)
				if foreign.R == nil {
					foreign.R = &eventR{}
				}
				foreign.R.Experiment = local
				break
			}
		}
	}

	return nil
}

// LoadExperimentStatusHistories allows an eager lookup of values, cached into the
// loaded structs of the objects. This is for a 1-M or N-M relationship.
func (experimentDetailL) LoadExperimentStatusHistories(ctx context.Context, e boil.ContextExecutor, singular bool, maybeExperimentDetail interface{}, mods queries.Applicator) error {
	var slice []*ExperimentDetail
	var object *ExperimentDetail

	if singular {
		object = maybeExperimentDetail.(*ExperimentDetail)
	} else {
		slice = *maybeExperimentDetail.(*[]*ExperimentDetail)
	}

	args := make([]interface{}, 0, 1)
	if singular {
		if object.R == nil {
			object.R = &experimentDetailR{}
		}
		args = append(args, object.ID)
	} else {
	Outer:
		for _, obj := range slice {
			if obj.R == nil {
				obj.R = &experimentDetailR{}
			}

			for _, a := range args {
				if a == obj.ID {
					continue Outer
				}
			}

			args = append(args, obj.ID)
		}
	}

	if len(args) == 0 {
		return nil
	}

	query := NewQuery(
		qm.From(`experiments.status_histories`),
		qm.WhereIn(`experiments.status_histories.experiment_id in ?`, args...),
	)
	if mods != nil {
		mods.Apply(query)
	}

	results, err := query.QueryContext(ctx, e)
	if err != nil {
		return errors.Wrap(err, "failed to eager load status_histories")
	}

	var resultSlice []*StatusHistory
	if err = queries.Bind(results, &resultSlice); err != nil {
		return errors.Wrap(err, "failed to bind eager loaded slice status_histories")
	}

	if err = results.Close(); err != nil {
		return errors.Wrap(err, "failed to close results in eager load on status_histories")
	}
	if err = results.Err(); err != nil {
		return errors.Wrap(err, "error occurred during iteration of eager loaded relations for status_histories")
	}

	if len(statusHistoryAfterSelectHooks) != 0 {
		for _, obj := range resultSlice {
			if err := obj.doAfterSelectHooks(ctx, e); err != nil {
				return err
			}
		}
	}
	if singular {
		object.R.ExperimentStatusHistories = resultSlice
		for _, foreign := range resultSlice {
			if foreign.R == nil {
				foreign.R = &statusHistoryR{}
			}
			foreign.R.Experiment = object
		}
		return nil
	}

	for _, foreign := range resultSlice {
		for _, local := range slice {
			if local.ID == foreign.ExperimentID {
				local.R.ExperimentStatusHistories = append(local.R.ExperimentStatusHistories, foreign)
				if foreign.R == nil {
					foreign.R = &statusHistoryR{}
				}
				foreign.R.Experiment = local
				break
			}
		}
	}

	return nil
}

// LoadExperimentVariants allows an eager lookup of values, cached into the
// loaded structs of the objects. This is for a 1-M or N-M relationship.
func (experimentDetailL) LoadExperimentVariants(ctx context.Context, e boil.ContextExecutor, singular bool, maybeExperimentDetail interface{}, mods queries.Applicator) error {
	var slice []*ExperimentDetail
	var object *ExperimentDetail

	if singular {
		object = maybeExperimentDetail.(*ExperimentDetail)
	} else {
		slice = *maybeExperimentDetail.(*[]*ExperimentDetail)
	}

	args := make([]interface{}, 0, 1)
	if singular {
		if object.R == nil {
			object.R = &experimentDetailR{}
		}
		args = append(args, object.ID)
	} else {
	Outer:
		for _, obj := range slice {
			if obj.R == nil {
				obj.R = &experimentDetailR{}
			}

			for _, a := range args {
				if a == obj.ID {
					continue Outer
				}
			}

			args = append(args, obj.ID)
		}
	}

	if len(args) == 0 {
		return nil
	}

	query := NewQuery(
		qm.From(`experiments.variants`),
		qm.WhereIn(`experiments.variants.experiment_id in ?`, args...),
	)
	if mods != nil {
		mods.Apply(query)
	}

	results, err := query.QueryContext(ctx, e)
	if err != nil {
		return errors.Wrap(err, "failed to eager load variants")
	}

	var resultSlice []*Variant
	if err = queries.Bind(results, &resultSlice); err != nil {
		return errors.Wrap(err, "failed to bind eager loaded slice variants")
	}

	if err = results.Close(); err != nil {
		return errors.Wrap(err, "failed to close results in eager load on variants")
	}
	if err = results.Err(); err != nil {
		return errors.Wrap(err, "error occurred during iteration of eager loaded relations for variants")
	}

	if len(variantAfterSelectHooks) != 0 {
		for _, obj := range resultSlice {
			if err := obj.doAfterSelectHooks(ctx, e); err != nil {
				return err
			}
		}
	}
	if singular {
		object.R.ExperimentVariants = resultSlice
		for _, foreign := range resultSlice {
			if foreign.R == nil {
				foreign.R = &variantR{}
			}
			foreign.R.Experiment = object
		}
		return nil
	}

	for _, foreign := range resultSlice {
		for _, local := range slice {
			if local.ID == foreign.ExperimentID {
				local.R.ExperimentVariants = append(local.R.ExperimentVariants, foreign)
				if foreign.R == nil {
					foreign.R = &variantR{}
				}
				foreign.R.Experiment = local
				break
			}
		}
	}

	return nil
}

// AddExperimentApplicabilities adds the given related objects to the existing relationships
// of the experiment_detail, optionally inserting them as new records.
// Appends related to o.R.ExperimentApplicabilities.
// Sets related.R.Experiment appropriately.
func (o *ExperimentDetail) AddExperimentApplicabilities(ctx context.Context, exec boil.ContextExecutor, insert bool, related ...*Applicability) error {
	var err error
	for _, rel := range related {
		if insert {
			rel.ExperimentID = o.ID
			if err = rel.Insert(ctx, exec, boil.Infer()); err != nil {
				return errors.Wrap(err, "failed to insert into foreign table")
			}
		} else {
			updateQuery := fmt.Sprintf(
				"UPDATE \"experiments\".\"applicabilities\" SET %s WHERE %s",
				strmangle.SetParamNames("\"", "\"", 1, []string{"experiment_id"}),
				strmangle.WhereClause("\"", "\"", 2, applicabilityPrimaryKeyColumns),
			)
			values := []interface{}{o.ID, rel.ID}

			if boil.IsDebug(ctx) {
				writer := boil.DebugWriterFrom(ctx)
				fmt.Fprintln(writer, updateQuery)
				fmt.Fprintln(writer, values)
			}
			if _, err = exec.ExecContext(ctx, updateQuery, values...); err != nil {
				return errors.Wrap(err, "failed to update foreign table")
			}

			rel.ExperimentID = o.ID
		}
	}

	if o.R == nil {
		o.R = &experimentDetailR{
			ExperimentApplicabilities: related,
		}
	} else {
		o.R.ExperimentApplicabilities = append(o.R.ExperimentApplicabilities, related...)
	}

	for _, rel := range related {
		if rel.R == nil {
			rel.R = &applicabilityR{
				Experiment: o,
			}
		} else {
			rel.R.Experiment = o
		}
	}
	return nil
}

// AddExperimentAssignments adds the given related objects to the existing relationships
// of the experiment_detail, optionally inserting them as new records.
// Appends related to o.R.ExperimentAssignments.
// Sets related.R.Experiment appropriately.
func (o *ExperimentDetail) AddExperimentAssignments(ctx context.Context, exec boil.ContextExecutor, insert bool, related ...*Assignment) error {
	var err error
	for _, rel := range related {
		if insert {
			rel.ExperimentID = o.ID
			if err = rel.Insert(ctx, exec, boil.Infer()); err != nil {
				return errors.Wrap(err, "failed to insert into foreign table")
			}
		} else {
			updateQuery := fmt.Sprintf(
				"UPDATE \"experiments\".\"assignments\" SET %s WHERE %s",
				strmangle.SetParamNames("\"", "\"", 1, []string{"experiment_id"}),
				strmangle.WhereClause("\"", "\"", 2, assignmentPrimaryKeyColumns),
			)
			values := []interface{}{o.ID, rel.ID}

			if boil.IsDebug(ctx) {
				writer := boil.DebugWriterFrom(ctx)
				fmt.Fprintln(writer, updateQuery)
				fmt.Fprintln(writer, values)
			}
			if _, err = exec.ExecContext(ctx, updateQuery, values...); err != nil {
				return errors.Wrap(err, "failed to update foreign table")
			}

			rel.ExperimentID = o.ID
		}
	}

	if o.R == nil {
		o.R = &experimentDetailR{
			ExperimentAssignments: related,
		}
	} else {
		o.R.ExperimentAssignments = append(o.R.ExperimentAssignments, related...)
	}

	for _, rel := range related {
		if rel.R == nil {
			rel.R = &assignmentR{
				Experiment: o,
			}
		} else {
			rel.R.Experiment = o
		}
	}
	return nil
}

// AddExperimentEvents adds the given related objects to the existing relationships
// of the experiment_detail, optionally inserting them as new records.
// Appends related to o.R.ExperimentEvents.
// Sets related.R.Experiment appropriately.
func (o *ExperimentDetail) AddExperimentEvents(ctx context.Context, exec boil.ContextExecutor, insert bool, related ...*Event) error {
	var err error
	for _, rel := range related {
		if insert {
			rel.ExperimentID = o.ID
			if err = rel.Insert(ctx, exec, boil.Infer()); err != nil {
				return errors.Wrap(err, "failed to insert into foreign table")
			}
		} else {
			updateQuery := fmt.Sprintf(
				"UPDATE \"experiments\".\"events\" SET %s WHERE %s",
				strmangle.SetParamNames("\"", "\"", 1, []string{"experiment_id"}),
				strmangle.WhereClause("\"", "\"", 2, eventPrimaryKeyColumns),
			)
			values := []interface{}{o.ID, rel.ID}

			if boil.IsDebug(ctx) {
				writer := boil.DebugWriterFrom(ctx)
				fmt.Fprintln(writer, updateQuery)
				fmt.Fprintln(writer, values)
			}
			if _, err = exec.ExecContext(ctx, updateQuery, values...); err != nil {
				return errors.Wrap(err, "failed to update foreign table")
			}

			rel.ExperimentID = o.ID
		}
	}

	if o.R == nil {
		o.R = &experimentDetailR{
			ExperimentEvents: related,
		}
	} else {
		o.R.ExperimentEvents = append(o.R.ExperimentEvents, related...)
	}

	for _, rel := range related {
		if rel.R == nil {
			rel.R = &eventR{
				Experiment: o,
			}
		} else {
			rel.R.Experiment = o
		}
	}
	return nil
}

// AddExperimentStatusHistories adds the given related objects to the existing relationships
// of the experiment_detail, optionally inserting them as new records.
// Appends related to o.R.ExperimentStatusHistories.
// Sets related.R.Experiment appropriately.
func (o *ExperimentDetail) AddExperimentStatusHistories(ctx context.Context, exec boil.ContextExecutor, insert bool, related ...*StatusHistory) error {
	var err error
	for _, rel := range related {
		if insert {
			rel.ExperimentID = o.ID
			if err = rel.Insert(ctx, exec, boil.Infer()); err != nil {
				return errors.Wrap(err, "failed to insert into foreign table")
			}
		} else {
			updateQuery := fmt.Sprintf(
				"UPDATE \"experiments\".\"status_histories\" SET %s WHERE %s",
				strmangle.SetParamNames("\"", "\"", 1, []string{"experiment_id"}),
				strmangle.WhereClause("\"", "\"", 2, statusHistoryPrimaryKeyColumns),
			)
			values := []interface{}{o.ID, rel.ID}

			if boil.IsDebug(ctx) {
				writer := boil.DebugWriterFrom(ctx)
				fmt.Fprintln(writer, updateQuery)
				fmt.Fprintln(writer, values)
			}
			if _, err = exec.ExecContext(ctx, updateQuery, values...); err != nil {
				return errors.Wrap(err, "failed to update foreign table")
			}

			rel.ExperimentID = o.ID
		}
	}

	if o.R == nil {
		o.R = &experimentDetailR{
			ExperimentStatusHistories: related,
		}
	} else {
		o.R.ExperimentStatusHistories = append(o.R.ExperimentStatusHistories, related...)
	}

	for _, rel := range related {
		if rel.R == nil {
			rel.R = &statusHistoryR{
				Experiment: o,
			}
		} else {
			rel.R.Experiment = o
		}
	}
	return nil
}

// AddExperimentVariants adds the given related objects to the existing relationships
// of the experiment_detail, optionally inserting them as new records.
// Appends related to o.R.ExperimentVariants.
// Sets related.R.Experiment appropriately.
func (o *ExperimentDetail) AddExperimentVariants(ctx context.Context, exec boil.ContextExecutor, insert bool, related ...*Variant) error {
	var err error
	for _, rel := range related {
		if insert {
			rel.ExperimentID = o.ID
			if err = rel.Insert(ctx, exec, boil.Infer()); err != nil {
				return errors.Wrap(err, "failed to insert into foreign table")
			}
		} else {
			updateQuery := fmt.Sprintf(
				"UPDATE \"experiments\".\"variants\" SET %s WHERE %s",
				strmangle.SetParamNames("\"", "\"", 1, []string{"experiment_id"}),
				strmangle.WhereClause("\"", "\"", 2, variantPrimaryKeyColumns),
			)
			values := []interface{}{o.ID, rel.ID}

			if boil.IsDebug(ctx) {
				writer := boil.DebugWriterFrom(ctx)
				fmt.Fprintln(writer, updateQuery)
				fmt.Fprintln(writer, values)
			}
			if _, err = exec.ExecContext(ctx, updateQuery, values...); err != nil {
				return errors.Wrap(err, "failed to update foreign table")
			}

			rel.ExperimentID = o.ID
		}
	}

	if o.R == nil {
		o.R = &experimentDetailR{
			ExperimentVariants: related,
		}
	} else {
		o.R.ExperimentVariants = append(o.R.ExperimentVariants, related...)
	}

	for _, rel := range related {
		if rel.R == nil {
			rel.R = &variantR{
				Experiment: o,
			}
		} else {
			rel.R.Experiment = o
		}
	}
	return nil
}

// ExperimentDetails retrieves all the records using an executor.
func ExperimentDetails(mods ...qm.QueryMod) experimentDetailQuery {
	mods = append(mods, qm.From("\"experiments\".\"experiment_details\""))
	q := NewQuery(mods...)
	if len(queries.GetSelect(q)) == 0 {
		queries.SetSelect(q, []string{"\"experiments\".\"experiment_details\".*"})
	}

	return experimentDetailQuery{q}
}

// FindExperimentDetail retrieves a single record by ID with an executor.
// If selectCols is empty Find will return all columns.
func FindExperimentDetail(ctx context.Context, exec boil.ContextExecutor, iD string, selectCols ...string) (*ExperimentDetail, error) {
	experimentDetailObj := &ExperimentDetail{}

	sel := "*"
	if len(selectCols) > 0 {
		sel = strings.Join(strmangle.IdentQuoteSlice(dialect.LQ, dialect.RQ, selectCols), ",")
	}
	query := fmt.Sprintf(
		"select %s from \"experiments\".\"experiment_details\" where \"id\"=$1", sel,
	)

	q := queries.Raw(query, iD)

	err := q.Bind(ctx, exec, experimentDetailObj)
	if err != nil {
		if errors.Is(err, sql.ErrNoRows) {
			return nil, sql.ErrNoRows
		}
		return nil, errors.Wrap(err, "experiments: unable to select from experiment_details")
	}

	if err = experimentDetailObj.doAfterSelectHooks(ctx, exec); err != nil {
		return experimentDetailObj, err
	}

	return experimentDetailObj, nil
}

// Insert a single record using an executor.
// See boil.Columns.InsertColumnSet documentation to understand column list inference for inserts.
func (o *ExperimentDetail) Insert(ctx context.Context, exec boil.ContextExecutor, columns boil.Columns) error {
	if o == nil {
		return errors.New("experiments: no experiment_details provided for insertion")
	}

	var err error
	if !boil.TimestampsAreSkipped(ctx) {
		currTime := time.Now().In(boil.GetLocation())

		if o.CreatedAt.IsZero() {
			o.CreatedAt = currTime
		}
		if o.UpdatedAt.IsZero() {
			o.UpdatedAt = currTime
		}
	}

	if err := o.doBeforeInsertHooks(ctx, exec); err != nil {
		return err
	}

	nzDefaults := queries.NonZeroDefaultSet(experimentDetailColumnsWithDefault, o)

	key := makeCacheKey(columns, nzDefaults)
	experimentDetailInsertCacheMut.RLock()
	cache, cached := experimentDetailInsertCache[key]
	experimentDetailInsertCacheMut.RUnlock()

	if !cached {
		wl, returnColumns := columns.InsertColumnSet(
			experimentDetailAllColumns,
			experimentDetailColumnsWithDefault,
			experimentDetailColumnsWithoutDefault,
			nzDefaults,
		)

		cache.valueMapping, err = queries.BindMapping(experimentDetailType, experimentDetailMapping, wl)
		if err != nil {
			return err
		}
		cache.retMapping, err = queries.BindMapping(experimentDetailType, experimentDetailMapping, returnColumns)
		if err != nil {
			return err
		}
		if len(wl) != 0 {
			cache.query = fmt.Sprintf("INSERT INTO \"experiments\".\"experiment_details\" (\"%s\") %%sVALUES (%s)%%s", strings.Join(wl, "\",\""), strmangle.Placeholders(dialect.UseIndexPlaceholders, len(wl), 1, 1))
		} else {
			cache.query = "INSERT INTO \"experiments\".\"experiment_details\" %sDEFAULT VALUES%s"
		}

		var queryOutput, queryReturning string

		if len(cache.retMapping) != 0 {
			queryReturning = fmt.Sprintf(" RETURNING \"%s\"", strings.Join(returnColumns, "\",\""))
		}

		cache.query = fmt.Sprintf(cache.query, queryOutput, queryReturning)
	}

	value := reflect.Indirect(reflect.ValueOf(o))
	vals := queries.ValuesFromMapping(value, cache.valueMapping)

	if boil.IsDebug(ctx) {
		writer := boil.DebugWriterFrom(ctx)
		fmt.Fprintln(writer, cache.query)
		fmt.Fprintln(writer, vals)
	}

	if len(cache.retMapping) != 0 {
		err = exec.QueryRowContext(ctx, cache.query, vals...).Scan(queries.PtrsFromMapping(value, cache.retMapping)...)
	} else {
		_, err = exec.ExecContext(ctx, cache.query, vals...)
	}

	if err != nil {
		return errors.Wrap(err, "experiments: unable to insert into experiment_details")
	}

	if !cached {
		experimentDetailInsertCacheMut.Lock()
		experimentDetailInsertCache[key] = cache
		experimentDetailInsertCacheMut.Unlock()
	}

	return o.doAfterInsertHooks(ctx, exec)
}

// Update uses an executor to update the ExperimentDetail.
// See boil.Columns.UpdateColumnSet documentation to understand column list inference for updates.
// Update does not automatically update the record in case of default values. Use .Reload() to refresh the records.
func (o *ExperimentDetail) Update(ctx context.Context, exec boil.ContextExecutor, columns boil.Columns) (int64, error) {
	if !boil.TimestampsAreSkipped(ctx) {
		currTime := time.Now().In(boil.GetLocation())

		o.UpdatedAt = currTime
	}

	var err error
	if err = o.doBeforeUpdateHooks(ctx, exec); err != nil {
		return 0, err
	}
	key := makeCacheKey(columns, nil)
	experimentDetailUpdateCacheMut.RLock()
	cache, cached := experimentDetailUpdateCache[key]
	experimentDetailUpdateCacheMut.RUnlock()

	if !cached {
		wl := columns.UpdateColumnSet(
			experimentDetailAllColumns,
			experimentDetailPrimaryKeyColumns,
		)

		if !columns.IsWhitelist() {
			wl = strmangle.SetComplement(wl, []string{"created_at"})
		}
		if len(wl) == 0 {
			return 0, errors.New("experiments: unable to update experiment_details, could not build whitelist")
		}

		cache.query = fmt.Sprintf("UPDATE \"experiments\".\"experiment_details\" SET %s WHERE %s",
			strmangle.SetParamNames("\"", "\"", 1, wl),
			strmangle.WhereClause("\"", "\"", len(wl)+1, experimentDetailPrimaryKeyColumns),
		)
		cache.valueMapping, err = queries.BindMapping(experimentDetailType, experimentDetailMapping, append(wl, experimentDetailPrimaryKeyColumns...))
		if err != nil {
			return 0, err
		}
	}

	values := queries.ValuesFromMapping(reflect.Indirect(reflect.ValueOf(o)), cache.valueMapping)

	if boil.IsDebug(ctx) {
		writer := boil.DebugWriterFrom(ctx)
		fmt.Fprintln(writer, cache.query)
		fmt.Fprintln(writer, values)
	}
	var result sql.Result
	result, err = exec.ExecContext(ctx, cache.query, values...)
	if err != nil {
		return 0, errors.Wrap(err, "experiments: unable to update experiment_details row")
	}

	rowsAff, err := result.RowsAffected()
	if err != nil {
		return 0, errors.Wrap(err, "experiments: failed to get rows affected by update for experiment_details")
	}

	if !cached {
		experimentDetailUpdateCacheMut.Lock()
		experimentDetailUpdateCache[key] = cache
		experimentDetailUpdateCacheMut.Unlock()
	}

	return rowsAff, o.doAfterUpdateHooks(ctx, exec)
}

// UpdateAll updates all rows with the specified column values.
func (q experimentDetailQuery) UpdateAll(ctx context.Context, exec boil.ContextExecutor, cols M) (int64, error) {
	queries.SetUpdate(q.Query, cols)

	result, err := q.Query.ExecContext(ctx, exec)
	if err != nil {
		return 0, errors.Wrap(err, "experiments: unable to update all for experiment_details")
	}

	rowsAff, err := result.RowsAffected()
	if err != nil {
		return 0, errors.Wrap(err, "experiments: unable to retrieve rows affected for experiment_details")
	}

	return rowsAff, nil
}

// UpdateAll updates all rows with the specified column values, using an executor.
func (o ExperimentDetailSlice) UpdateAll(ctx context.Context, exec boil.ContextExecutor, cols M) (int64, error) {
	ln := int64(len(o))
	if ln == 0 {
		return 0, nil
	}

	if len(cols) == 0 {
		return 0, errors.New("experiments: update all requires at least one column argument")
	}

	colNames := make([]string, len(cols))
	args := make([]interface{}, len(cols))

	i := 0
	for name, value := range cols {
		colNames[i] = name
		args[i] = value
		i++
	}

	// Append all of the primary key values for each column
	for _, obj := range o {
		pkeyArgs := queries.ValuesFromMapping(reflect.Indirect(reflect.ValueOf(obj)), experimentDetailPrimaryKeyMapping)
		args = append(args, pkeyArgs...)
	}

	sql := fmt.Sprintf("UPDATE \"experiments\".\"experiment_details\" SET %s WHERE %s",
		strmangle.SetParamNames("\"", "\"", 1, colNames),
		strmangle.WhereClauseRepeated(string(dialect.LQ), string(dialect.RQ), len(colNames)+1, experimentDetailPrimaryKeyColumns, len(o)))

	if boil.IsDebug(ctx) {
		writer := boil.DebugWriterFrom(ctx)
		fmt.Fprintln(writer, sql)
		fmt.Fprintln(writer, args...)
	}
	result, err := exec.ExecContext(ctx, sql, args...)
	if err != nil {
		return 0, errors.Wrap(err, "experiments: unable to update all in experimentDetail slice")
	}

	rowsAff, err := result.RowsAffected()
	if err != nil {
		return 0, errors.Wrap(err, "experiments: unable to retrieve rows affected all in update all experimentDetail")
	}
	return rowsAff, nil
}

// Upsert attempts an insert using an executor, and does an update or ignore on conflict.
// See boil.Columns documentation for how to properly use updateColumns and insertColumns.
func (o *ExperimentDetail) Upsert(ctx context.Context, exec boil.ContextExecutor, updateOnConflict bool, conflictColumns []string, updateColumns, insertColumns boil.Columns) error {
	if o == nil {
		return errors.New("experiments: no experiment_details provided for upsert")
	}
	if !boil.TimestampsAreSkipped(ctx) {
		currTime := time.Now().In(boil.GetLocation())

		if o.CreatedAt.IsZero() {
			o.CreatedAt = currTime
		}
		o.UpdatedAt = currTime
	}

	if err := o.doBeforeUpsertHooks(ctx, exec); err != nil {
		return err
	}

	nzDefaults := queries.NonZeroDefaultSet(experimentDetailColumnsWithDefault, o)

	// Build cache key in-line uglily - mysql vs psql problems
	buf := strmangle.GetBuffer()
	if updateOnConflict {
		buf.WriteByte('t')
	} else {
		buf.WriteByte('f')
	}
	buf.WriteByte('.')
	for _, c := range conflictColumns {
		buf.WriteString(c)
	}
	buf.WriteByte('.')
	buf.WriteString(strconv.Itoa(updateColumns.Kind))
	for _, c := range updateColumns.Cols {
		buf.WriteString(c)
	}
	buf.WriteByte('.')
	buf.WriteString(strconv.Itoa(insertColumns.Kind))
	for _, c := range insertColumns.Cols {
		buf.WriteString(c)
	}
	buf.WriteByte('.')
	for _, c := range nzDefaults {
		buf.WriteString(c)
	}
	key := buf.String()
	strmangle.PutBuffer(buf)

	experimentDetailUpsertCacheMut.RLock()
	cache, cached := experimentDetailUpsertCache[key]
	experimentDetailUpsertCacheMut.RUnlock()

	var err error

	if !cached {
		insert, ret := insertColumns.InsertColumnSet(
			experimentDetailAllColumns,
			experimentDetailColumnsWithDefault,
			experimentDetailColumnsWithoutDefault,
			nzDefaults,
		)

		update := updateColumns.UpdateColumnSet(
			experimentDetailAllColumns,
			experimentDetailPrimaryKeyColumns,
		)

		if updateOnConflict && len(update) == 0 {
			return errors.New("experiments: unable to upsert experiment_details, could not build update column list")
		}

		conflict := conflictColumns
		if len(conflict) == 0 {
			conflict = make([]string, len(experimentDetailPrimaryKeyColumns))
			copy(conflict, experimentDetailPrimaryKeyColumns)
		}
		cache.query = buildUpsertQueryPostgres(dialect, "\"experiments\".\"experiment_details\"", updateOnConflict, ret, update, conflict, insert)

		cache.valueMapping, err = queries.BindMapping(experimentDetailType, experimentDetailMapping, insert)
		if err != nil {
			return err
		}
		if len(ret) != 0 {
			cache.retMapping, err = queries.BindMapping(experimentDetailType, experimentDetailMapping, ret)
			if err != nil {
				return err
			}
		}
	}

	value := reflect.Indirect(reflect.ValueOf(o))
	vals := queries.ValuesFromMapping(value, cache.valueMapping)
	var returns []interface{}
	if len(cache.retMapping) != 0 {
		returns = queries.PtrsFromMapping(value, cache.retMapping)
	}

	if boil.IsDebug(ctx) {
		writer := boil.DebugWriterFrom(ctx)
		fmt.Fprintln(writer, cache.query)
		fmt.Fprintln(writer, vals)
	}
	if len(cache.retMapping) != 0 {
		err = exec.QueryRowContext(ctx, cache.query, vals...).Scan(returns...)
		if errors.Is(err, sql.ErrNoRows) {
			err = nil // Postgres doesn't return anything when there's no update
		}
	} else {
		_, err = exec.ExecContext(ctx, cache.query, vals...)
	}
	if err != nil {
		return errors.Wrap(err, "experiments: unable to upsert experiment_details")
	}

	if !cached {
		experimentDetailUpsertCacheMut.Lock()
		experimentDetailUpsertCache[key] = cache
		experimentDetailUpsertCacheMut.Unlock()
	}

	return o.doAfterUpsertHooks(ctx, exec)
}

// Delete deletes a single ExperimentDetail record with an executor.
// Delete will match against the primary key column to find the record to delete.
func (o *ExperimentDetail) Delete(ctx context.Context, exec boil.ContextExecutor) (int64, error) {
	if o == nil {
		return 0, errors.New("experiments: no ExperimentDetail provided for delete")
	}

	if err := o.doBeforeDeleteHooks(ctx, exec); err != nil {
		return 0, err
	}

	args := queries.ValuesFromMapping(reflect.Indirect(reflect.ValueOf(o)), experimentDetailPrimaryKeyMapping)
	sql := "DELETE FROM \"experiments\".\"experiment_details\" WHERE \"id\"=$1"

	if boil.IsDebug(ctx) {
		writer := boil.DebugWriterFrom(ctx)
		fmt.Fprintln(writer, sql)
		fmt.Fprintln(writer, args...)
	}
	result, err := exec.ExecContext(ctx, sql, args...)
	if err != nil {
		return 0, errors.Wrap(err, "experiments: unable to delete from experiment_details")
	}

	rowsAff, err := result.RowsAffected()
	if err != nil {
		return 0, errors.Wrap(err, "experiments: failed to get rows affected by delete for experiment_details")
	}

	if err := o.doAfterDeleteHooks(ctx, exec); err != nil {
		return 0, err
	}

	return rowsAff, nil
}

// DeleteAll deletes all matching rows.
func (q experimentDetailQuery) DeleteAll(ctx context.Context, exec boil.ContextExecutor) (int64, error) {
	if q.Query == nil {
		return 0, errors.New("experiments: no experimentDetailQuery provided for delete all")
	}

	queries.SetDelete(q.Query)

	result, err := q.Query.ExecContext(ctx, exec)
	if err != nil {
		return 0, errors.Wrap(err, "experiments: unable to delete all from experiment_details")
	}

	rowsAff, err := result.RowsAffected()
	if err != nil {
		return 0, errors.Wrap(err, "experiments: failed to get rows affected by deleteall for experiment_details")
	}

	return rowsAff, nil
}

// DeleteAll deletes all rows in the slice, using an executor.
func (o ExperimentDetailSlice) DeleteAll(ctx context.Context, exec boil.ContextExecutor) (int64, error) {
	if len(o) == 0 {
		return 0, nil
	}

	if len(experimentDetailBeforeDeleteHooks) != 0 {
		for _, obj := range o {
			if err := obj.doBeforeDeleteHooks(ctx, exec); err != nil {
				return 0, err
			}
		}
	}

	var args []interface{}
	for _, obj := range o {
		pkeyArgs := queries.ValuesFromMapping(reflect.Indirect(reflect.ValueOf(obj)), experimentDetailPrimaryKeyMapping)
		args = append(args, pkeyArgs...)
	}

	sql := "DELETE FROM \"experiments\".\"experiment_details\" WHERE " +
		strmangle.WhereClauseRepeated(string(dialect.LQ), string(dialect.RQ), 1, experimentDetailPrimaryKeyColumns, len(o))

	if boil.IsDebug(ctx) {
		writer := boil.DebugWriterFrom(ctx)
		fmt.Fprintln(writer, sql)
		fmt.Fprintln(writer, args)
	}
	result, err := exec.ExecContext(ctx, sql, args...)
	if err != nil {
		return 0, errors.Wrap(err, "experiments: unable to delete all from experimentDetail slice")
	}

	rowsAff, err := result.RowsAffected()
	if err != nil {
		return 0, errors.Wrap(err, "experiments: failed to get rows affected by deleteall for experiment_details")
	}

	if len(experimentDetailAfterDeleteHooks) != 0 {
		for _, obj := range o {
			if err := obj.doAfterDeleteHooks(ctx, exec); err != nil {
				return 0, err
			}
		}
	}

	return rowsAff, nil
}

// Reload refetches the object from the database
// using the primary keys with an executor.
func (o *ExperimentDetail) Reload(ctx context.Context, exec boil.ContextExecutor) error {
	ret, err := FindExperimentDetail(ctx, exec, o.ID)
	if err != nil {
		return err
	}

	*o = *ret
	return nil
}

// ReloadAll refetches every row with matching primary key column values
// and overwrites the original object slice with the newly updated slice.
func (o *ExperimentDetailSlice) ReloadAll(ctx context.Context, exec boil.ContextExecutor) error {
	if o == nil || len(*o) == 0 {
		return nil
	}

	slice := ExperimentDetailSlice{}
	var args []interface{}
	for _, obj := range *o {
		pkeyArgs := queries.ValuesFromMapping(reflect.Indirect(reflect.ValueOf(obj)), experimentDetailPrimaryKeyMapping)
		args = append(args, pkeyArgs...)
	}

	sql := "SELECT \"experiments\".\"experiment_details\".* FROM \"experiments\".\"experiment_details\" WHERE " +
		strmangle.WhereClauseRepeated(string(dialect.LQ), string(dialect.RQ), 1, experimentDetailPrimaryKeyColumns, len(*o))

	q := queries.Raw(sql, args...)

	err := q.Bind(ctx, exec, &slice)
	if err != nil {
		return errors.Wrap(err, "experiments: unable to reload all in ExperimentDetailSlice")
	}

	*o = slice

	return nil
}

// ExperimentDetailExists checks if the ExperimentDetail row exists.
func ExperimentDetailExists(ctx context.Context, exec boil.ContextExecutor, iD string) (bool, error) {
	var exists bool
	sql := "select exists(select 1 from \"experiments\".\"experiment_details\" where \"id\"=$1 limit 1)"

	if boil.IsDebug(ctx) {
		writer := boil.DebugWriterFrom(ctx)
		fmt.Fprintln(writer, sql)
		fmt.Fprintln(writer, iD)
	}
	row := exec.QueryRowContext(ctx, sql, iD)

	err := row.Scan(&exists)
	if err != nil {
		return false, errors.Wrap(err, "experiments: unable to check if experiment_details exists")
	}

	return exists, nil
}
