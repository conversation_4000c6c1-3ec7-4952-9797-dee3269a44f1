// Code generated by SQLBoiler (https://github.com/volatiletech/sqlboiler). DO NOT EDIT.
// This file is meant to be re-generated in place and/or deleted at any time.

package account

import (
	"context"
	"database/sql"
	"fmt"
	"reflect"
	"strconv"
	"strings"
	"sync"
	"time"

	"github.com/friendsofgo/errors"
	"github.com/volatiletech/sqlboiler/v4/boil"
	"github.com/volatiletech/sqlboiler/v4/queries"
	"github.com/volatiletech/sqlboiler/v4/queries/qm"
	"github.com/volatiletech/sqlboiler/v4/queries/qmhelper"
	"github.com/volatiletech/strmangle"
)

// AccountAlias is an object representing the database table.
type AccountAlias struct {
	AliasAccountID     string    `boil:"alias_account_id" json:"alias_account_id" toml:"alias_account_id" yaml:"alias_account_id"`
	CanonicalAccountID string    `boil:"canonical_account_id" json:"canonical_account_id" toml:"canonical_account_id" yaml:"canonical_account_id"`
	CreatedAt          time.Time `boil:"created_at" json:"created_at" toml:"created_at" yaml:"created_at"`

	R *accountAliasR `boil:"" json:"" toml:"" yaml:""`
	L accountAliasL  `boil:"-" json:"-" toml:"-" yaml:"-"`
}

var AccountAliasColumns = struct {
	AliasAccountID     string
	CanonicalAccountID string
	CreatedAt          string
}{
	AliasAccountID:     "alias_account_id",
	CanonicalAccountID: "canonical_account_id",
	CreatedAt:          "created_at",
}

var AccountAliasTableColumns = struct {
	AliasAccountID     string
	CanonicalAccountID string
	CreatedAt          string
}{
	AliasAccountID:     "account_aliases.alias_account_id",
	CanonicalAccountID: "account_aliases.canonical_account_id",
	CreatedAt:          "account_aliases.created_at",
}

// Generated where

type whereHelperstring struct{ field string }

func (w whereHelperstring) EQ(x string) qm.QueryMod  { return qmhelper.Where(w.field, qmhelper.EQ, x) }
func (w whereHelperstring) NEQ(x string) qm.QueryMod { return qmhelper.Where(w.field, qmhelper.NEQ, x) }
func (w whereHelperstring) LT(x string) qm.QueryMod  { return qmhelper.Where(w.field, qmhelper.LT, x) }
func (w whereHelperstring) LTE(x string) qm.QueryMod { return qmhelper.Where(w.field, qmhelper.LTE, x) }
func (w whereHelperstring) GT(x string) qm.QueryMod  { return qmhelper.Where(w.field, qmhelper.GT, x) }
func (w whereHelperstring) GTE(x string) qm.QueryMod { return qmhelper.Where(w.field, qmhelper.GTE, x) }
func (w whereHelperstring) IN(slice []string) qm.QueryMod {
	values := make([]interface{}, 0, len(slice))
	for _, value := range slice {
		values = append(values, value)
	}
	return qm.WhereIn(fmt.Sprintf("%s IN ?", w.field), values...)
}
func (w whereHelperstring) NIN(slice []string) qm.QueryMod {
	values := make([]interface{}, 0, len(slice))
	for _, value := range slice {
		values = append(values, value)
	}
	return qm.WhereNotIn(fmt.Sprintf("%s NOT IN ?", w.field), values...)
}

type whereHelpertime_Time struct{ field string }

func (w whereHelpertime_Time) EQ(x time.Time) qm.QueryMod {
	return qmhelper.Where(w.field, qmhelper.EQ, x)
}
func (w whereHelpertime_Time) NEQ(x time.Time) qm.QueryMod {
	return qmhelper.Where(w.field, qmhelper.NEQ, x)
}
func (w whereHelpertime_Time) LT(x time.Time) qm.QueryMod {
	return qmhelper.Where(w.field, qmhelper.LT, x)
}
func (w whereHelpertime_Time) LTE(x time.Time) qm.QueryMod {
	return qmhelper.Where(w.field, qmhelper.LTE, x)
}
func (w whereHelpertime_Time) GT(x time.Time) qm.QueryMod {
	return qmhelper.Where(w.field, qmhelper.GT, x)
}
func (w whereHelpertime_Time) GTE(x time.Time) qm.QueryMod {
	return qmhelper.Where(w.field, qmhelper.GTE, x)
}

var AccountAliasWhere = struct {
	AliasAccountID     whereHelperstring
	CanonicalAccountID whereHelperstring
	CreatedAt          whereHelpertime_Time
}{
	AliasAccountID:     whereHelperstring{field: "\"account\".\"account_aliases\".\"alias_account_id\""},
	CanonicalAccountID: whereHelperstring{field: "\"account\".\"account_aliases\".\"canonical_account_id\""},
	CreatedAt:          whereHelpertime_Time{field: "\"account\".\"account_aliases\".\"created_at\""},
}

// AccountAliasRels is where relationship names are stored.
var AccountAliasRels = struct {
	CanonicalAccount string
}{
	CanonicalAccount: "CanonicalAccount",
}

// accountAliasR is where relationships are stored.
type accountAliasR struct {
	CanonicalAccount *Account `boil:"CanonicalAccount" json:"CanonicalAccount" toml:"CanonicalAccount" yaml:"CanonicalAccount"`
}

// NewStruct creates a new relationship struct
func (*accountAliasR) NewStruct() *accountAliasR {
	return &accountAliasR{}
}

// accountAliasL is where Load methods for each relationship are stored.
type accountAliasL struct{}

var (
	accountAliasAllColumns            = []string{"alias_account_id", "canonical_account_id", "created_at"}
	accountAliasColumnsWithoutDefault = []string{"alias_account_id", "canonical_account_id"}
	accountAliasColumnsWithDefault    = []string{"created_at"}
	accountAliasPrimaryKeyColumns     = []string{"alias_account_id"}
	accountAliasGeneratedColumns      = []string{}
)

type (
	// AccountAliasSlice is an alias for a slice of pointers to AccountAlias.
	// This should almost always be used instead of []AccountAlias.
	AccountAliasSlice []*AccountAlias
	// AccountAliasHook is the signature for custom AccountAlias hook methods
	AccountAliasHook func(context.Context, boil.ContextExecutor, *AccountAlias) error

	accountAliasQuery struct {
		*queries.Query
	}
)

// Cache for insert, update and upsert
var (
	accountAliasType                 = reflect.TypeOf(&AccountAlias{})
	accountAliasMapping              = queries.MakeStructMapping(accountAliasType)
	accountAliasPrimaryKeyMapping, _ = queries.BindMapping(accountAliasType, accountAliasMapping, accountAliasPrimaryKeyColumns)
	accountAliasInsertCacheMut       sync.RWMutex
	accountAliasInsertCache          = make(map[string]insertCache)
	accountAliasUpdateCacheMut       sync.RWMutex
	accountAliasUpdateCache          = make(map[string]updateCache)
	accountAliasUpsertCacheMut       sync.RWMutex
	accountAliasUpsertCache          = make(map[string]insertCache)
)

var (
	// Force time package dependency for automated UpdatedAt/CreatedAt.
	_ = time.Second
	// Force qmhelper dependency for where clause generation (which doesn't
	// always happen)
	_ = qmhelper.Where
)

var accountAliasAfterSelectHooks []AccountAliasHook

var accountAliasBeforeInsertHooks []AccountAliasHook
var accountAliasAfterInsertHooks []AccountAliasHook

var accountAliasBeforeUpdateHooks []AccountAliasHook
var accountAliasAfterUpdateHooks []AccountAliasHook

var accountAliasBeforeDeleteHooks []AccountAliasHook
var accountAliasAfterDeleteHooks []AccountAliasHook

var accountAliasBeforeUpsertHooks []AccountAliasHook
var accountAliasAfterUpsertHooks []AccountAliasHook

// doAfterSelectHooks executes all "after Select" hooks.
func (o *AccountAlias) doAfterSelectHooks(ctx context.Context, exec boil.ContextExecutor) (err error) {
	if boil.HooksAreSkipped(ctx) {
		return nil
	}

	for _, hook := range accountAliasAfterSelectHooks {
		if err := hook(ctx, exec, o); err != nil {
			return err
		}
	}

	return nil
}

// doBeforeInsertHooks executes all "before insert" hooks.
func (o *AccountAlias) doBeforeInsertHooks(ctx context.Context, exec boil.ContextExecutor) (err error) {
	if boil.HooksAreSkipped(ctx) {
		return nil
	}

	for _, hook := range accountAliasBeforeInsertHooks {
		if err := hook(ctx, exec, o); err != nil {
			return err
		}
	}

	return nil
}

// doAfterInsertHooks executes all "after Insert" hooks.
func (o *AccountAlias) doAfterInsertHooks(ctx context.Context, exec boil.ContextExecutor) (err error) {
	if boil.HooksAreSkipped(ctx) {
		return nil
	}

	for _, hook := range accountAliasAfterInsertHooks {
		if err := hook(ctx, exec, o); err != nil {
			return err
		}
	}

	return nil
}

// doBeforeUpdateHooks executes all "before Update" hooks.
func (o *AccountAlias) doBeforeUpdateHooks(ctx context.Context, exec boil.ContextExecutor) (err error) {
	if boil.HooksAreSkipped(ctx) {
		return nil
	}

	for _, hook := range accountAliasBeforeUpdateHooks {
		if err := hook(ctx, exec, o); err != nil {
			return err
		}
	}

	return nil
}

// doAfterUpdateHooks executes all "after Update" hooks.
func (o *AccountAlias) doAfterUpdateHooks(ctx context.Context, exec boil.ContextExecutor) (err error) {
	if boil.HooksAreSkipped(ctx) {
		return nil
	}

	for _, hook := range accountAliasAfterUpdateHooks {
		if err := hook(ctx, exec, o); err != nil {
			return err
		}
	}

	return nil
}

// doBeforeDeleteHooks executes all "before Delete" hooks.
func (o *AccountAlias) doBeforeDeleteHooks(ctx context.Context, exec boil.ContextExecutor) (err error) {
	if boil.HooksAreSkipped(ctx) {
		return nil
	}

	for _, hook := range accountAliasBeforeDeleteHooks {
		if err := hook(ctx, exec, o); err != nil {
			return err
		}
	}

	return nil
}

// doAfterDeleteHooks executes all "after Delete" hooks.
func (o *AccountAlias) doAfterDeleteHooks(ctx context.Context, exec boil.ContextExecutor) (err error) {
	if boil.HooksAreSkipped(ctx) {
		return nil
	}

	for _, hook := range accountAliasAfterDeleteHooks {
		if err := hook(ctx, exec, o); err != nil {
			return err
		}
	}

	return nil
}

// doBeforeUpsertHooks executes all "before Upsert" hooks.
func (o *AccountAlias) doBeforeUpsertHooks(ctx context.Context, exec boil.ContextExecutor) (err error) {
	if boil.HooksAreSkipped(ctx) {
		return nil
	}

	for _, hook := range accountAliasBeforeUpsertHooks {
		if err := hook(ctx, exec, o); err != nil {
			return err
		}
	}

	return nil
}

// doAfterUpsertHooks executes all "after Upsert" hooks.
func (o *AccountAlias) doAfterUpsertHooks(ctx context.Context, exec boil.ContextExecutor) (err error) {
	if boil.HooksAreSkipped(ctx) {
		return nil
	}

	for _, hook := range accountAliasAfterUpsertHooks {
		if err := hook(ctx, exec, o); err != nil {
			return err
		}
	}

	return nil
}

// AddAccountAliasHook registers your hook function for all future operations.
func AddAccountAliasHook(hookPoint boil.HookPoint, accountAliasHook AccountAliasHook) {
	switch hookPoint {
	case boil.AfterSelectHook:
		accountAliasAfterSelectHooks = append(accountAliasAfterSelectHooks, accountAliasHook)
	case boil.BeforeInsertHook:
		accountAliasBeforeInsertHooks = append(accountAliasBeforeInsertHooks, accountAliasHook)
	case boil.AfterInsertHook:
		accountAliasAfterInsertHooks = append(accountAliasAfterInsertHooks, accountAliasHook)
	case boil.BeforeUpdateHook:
		accountAliasBeforeUpdateHooks = append(accountAliasBeforeUpdateHooks, accountAliasHook)
	case boil.AfterUpdateHook:
		accountAliasAfterUpdateHooks = append(accountAliasAfterUpdateHooks, accountAliasHook)
	case boil.BeforeDeleteHook:
		accountAliasBeforeDeleteHooks = append(accountAliasBeforeDeleteHooks, accountAliasHook)
	case boil.AfterDeleteHook:
		accountAliasAfterDeleteHooks = append(accountAliasAfterDeleteHooks, accountAliasHook)
	case boil.BeforeUpsertHook:
		accountAliasBeforeUpsertHooks = append(accountAliasBeforeUpsertHooks, accountAliasHook)
	case boil.AfterUpsertHook:
		accountAliasAfterUpsertHooks = append(accountAliasAfterUpsertHooks, accountAliasHook)
	}
}

// One returns a single accountAlias record from the query.
func (q accountAliasQuery) One(ctx context.Context, exec boil.ContextExecutor) (*AccountAlias, error) {
	o := &AccountAlias{}

	queries.SetLimit(q.Query, 1)

	err := q.Bind(ctx, exec, o)
	if err != nil {
		if errors.Is(err, sql.ErrNoRows) {
			return nil, sql.ErrNoRows
		}
		return nil, errors.Wrap(err, "account: failed to execute a one query for account_aliases")
	}

	if err := o.doAfterSelectHooks(ctx, exec); err != nil {
		return o, err
	}

	return o, nil
}

// All returns all AccountAlias records from the query.
func (q accountAliasQuery) All(ctx context.Context, exec boil.ContextExecutor) (AccountAliasSlice, error) {
	var o []*AccountAlias

	err := q.Bind(ctx, exec, &o)
	if err != nil {
		return nil, errors.Wrap(err, "account: failed to assign all query results to AccountAlias slice")
	}

	if len(accountAliasAfterSelectHooks) != 0 {
		for _, obj := range o {
			if err := obj.doAfterSelectHooks(ctx, exec); err != nil {
				return o, err
			}
		}
	}

	return o, nil
}

// Count returns the count of all AccountAlias records in the query.
func (q accountAliasQuery) Count(ctx context.Context, exec boil.ContextExecutor) (int64, error) {
	var count int64

	queries.SetSelect(q.Query, nil)
	queries.SetCount(q.Query)

	err := q.Query.QueryRowContext(ctx, exec).Scan(&count)
	if err != nil {
		return 0, errors.Wrap(err, "account: failed to count account_aliases rows")
	}

	return count, nil
}

// Exists checks if the row exists in the table.
func (q accountAliasQuery) Exists(ctx context.Context, exec boil.ContextExecutor) (bool, error) {
	var count int64

	queries.SetSelect(q.Query, nil)
	queries.SetCount(q.Query)
	queries.SetLimit(q.Query, 1)

	err := q.Query.QueryRowContext(ctx, exec).Scan(&count)
	if err != nil {
		return false, errors.Wrap(err, "account: failed to check if account_aliases exists")
	}

	return count > 0, nil
}

// CanonicalAccount pointed to by the foreign key.
func (o *AccountAlias) CanonicalAccount(mods ...qm.QueryMod) accountQuery {
	queryMods := []qm.QueryMod{
		qm.Where("\"account_id\" = ?", o.CanonicalAccountID),
	}

	queryMods = append(queryMods, mods...)

	return Accounts(queryMods...)
}

// LoadCanonicalAccount allows an eager lookup of values, cached into the
// loaded structs of the objects. This is for an N-1 relationship.
func (accountAliasL) LoadCanonicalAccount(ctx context.Context, e boil.ContextExecutor, singular bool, maybeAccountAlias interface{}, mods queries.Applicator) error {
	var slice []*AccountAlias
	var object *AccountAlias

	if singular {
		object = maybeAccountAlias.(*AccountAlias)
	} else {
		slice = *maybeAccountAlias.(*[]*AccountAlias)
	}

	args := make([]interface{}, 0, 1)
	if singular {
		if object.R == nil {
			object.R = &accountAliasR{}
		}
		args = append(args, object.CanonicalAccountID)

	} else {
	Outer:
		for _, obj := range slice {
			if obj.R == nil {
				obj.R = &accountAliasR{}
			}

			for _, a := range args {
				if a == obj.CanonicalAccountID {
					continue Outer
				}
			}

			args = append(args, obj.CanonicalAccountID)

		}
	}

	if len(args) == 0 {
		return nil
	}

	query := NewQuery(
		qm.From(`account.accounts`),
		qm.WhereIn(`account.accounts.account_id in ?`, args...),
	)
	if mods != nil {
		mods.Apply(query)
	}

	results, err := query.QueryContext(ctx, e)
	if err != nil {
		return errors.Wrap(err, "failed to eager load Account")
	}

	var resultSlice []*Account
	if err = queries.Bind(results, &resultSlice); err != nil {
		return errors.Wrap(err, "failed to bind eager loaded slice Account")
	}

	if err = results.Close(); err != nil {
		return errors.Wrap(err, "failed to close results of eager load for accounts")
	}
	if err = results.Err(); err != nil {
		return errors.Wrap(err, "error occurred during iteration of eager loaded relations for accounts")
	}

	if len(accountAliasAfterSelectHooks) != 0 {
		for _, obj := range resultSlice {
			if err := obj.doAfterSelectHooks(ctx, e); err != nil {
				return err
			}
		}
	}

	if len(resultSlice) == 0 {
		return nil
	}

	if singular {
		foreign := resultSlice[0]
		object.R.CanonicalAccount = foreign
		if foreign.R == nil {
			foreign.R = &accountR{}
		}
		foreign.R.CanonicalAccountAccountAliases = append(foreign.R.CanonicalAccountAccountAliases, object)
		return nil
	}

	for _, local := range slice {
		for _, foreign := range resultSlice {
			if local.CanonicalAccountID == foreign.AccountID {
				local.R.CanonicalAccount = foreign
				if foreign.R == nil {
					foreign.R = &accountR{}
				}
				foreign.R.CanonicalAccountAccountAliases = append(foreign.R.CanonicalAccountAccountAliases, local)
				break
			}
		}
	}

	return nil
}

// SetCanonicalAccount of the accountAlias to the related item.
// Sets o.R.CanonicalAccount to related.
// Adds o to related.R.CanonicalAccountAccountAliases.
func (o *AccountAlias) SetCanonicalAccount(ctx context.Context, exec boil.ContextExecutor, insert bool, related *Account) error {
	var err error
	if insert {
		if err = related.Insert(ctx, exec, boil.Infer()); err != nil {
			return errors.Wrap(err, "failed to insert into foreign table")
		}
	}

	updateQuery := fmt.Sprintf(
		"UPDATE \"account\".\"account_aliases\" SET %s WHERE %s",
		strmangle.SetParamNames("\"", "\"", 1, []string{"canonical_account_id"}),
		strmangle.WhereClause("\"", "\"", 2, accountAliasPrimaryKeyColumns),
	)
	values := []interface{}{related.AccountID, o.AliasAccountID}

	if boil.IsDebug(ctx) {
		writer := boil.DebugWriterFrom(ctx)
		fmt.Fprintln(writer, updateQuery)
		fmt.Fprintln(writer, values)
	}
	if _, err = exec.ExecContext(ctx, updateQuery, values...); err != nil {
		return errors.Wrap(err, "failed to update local table")
	}

	o.CanonicalAccountID = related.AccountID
	if o.R == nil {
		o.R = &accountAliasR{
			CanonicalAccount: related,
		}
	} else {
		o.R.CanonicalAccount = related
	}

	if related.R == nil {
		related.R = &accountR{
			CanonicalAccountAccountAliases: AccountAliasSlice{o},
		}
	} else {
		related.R.CanonicalAccountAccountAliases = append(related.R.CanonicalAccountAccountAliases, o)
	}

	return nil
}

// AccountAliases retrieves all the records using an executor.
func AccountAliases(mods ...qm.QueryMod) accountAliasQuery {
	mods = append(mods, qm.From("\"account\".\"account_aliases\""))
	q := NewQuery(mods...)
	if len(queries.GetSelect(q)) == 0 {
		queries.SetSelect(q, []string{"\"account\".\"account_aliases\".*"})
	}

	return accountAliasQuery{q}
}

// FindAccountAlias retrieves a single record by ID with an executor.
// If selectCols is empty Find will return all columns.
func FindAccountAlias(ctx context.Context, exec boil.ContextExecutor, aliasAccountID string, selectCols ...string) (*AccountAlias, error) {
	accountAliasObj := &AccountAlias{}

	sel := "*"
	if len(selectCols) > 0 {
		sel = strings.Join(strmangle.IdentQuoteSlice(dialect.LQ, dialect.RQ, selectCols), ",")
	}
	query := fmt.Sprintf(
		"select %s from \"account\".\"account_aliases\" where \"alias_account_id\"=$1", sel,
	)

	q := queries.Raw(query, aliasAccountID)

	err := q.Bind(ctx, exec, accountAliasObj)
	if err != nil {
		if errors.Is(err, sql.ErrNoRows) {
			return nil, sql.ErrNoRows
		}
		return nil, errors.Wrap(err, "account: unable to select from account_aliases")
	}

	if err = accountAliasObj.doAfterSelectHooks(ctx, exec); err != nil {
		return accountAliasObj, err
	}

	return accountAliasObj, nil
}

// Insert a single record using an executor.
// See boil.Columns.InsertColumnSet documentation to understand column list inference for inserts.
func (o *AccountAlias) Insert(ctx context.Context, exec boil.ContextExecutor, columns boil.Columns) error {
	if o == nil {
		return errors.New("account: no account_aliases provided for insertion")
	}

	var err error
	if !boil.TimestampsAreSkipped(ctx) {
		currTime := time.Now().In(boil.GetLocation())

		if o.CreatedAt.IsZero() {
			o.CreatedAt = currTime
		}
	}

	if err := o.doBeforeInsertHooks(ctx, exec); err != nil {
		return err
	}

	nzDefaults := queries.NonZeroDefaultSet(accountAliasColumnsWithDefault, o)

	key := makeCacheKey(columns, nzDefaults)
	accountAliasInsertCacheMut.RLock()
	cache, cached := accountAliasInsertCache[key]
	accountAliasInsertCacheMut.RUnlock()

	if !cached {
		wl, returnColumns := columns.InsertColumnSet(
			accountAliasAllColumns,
			accountAliasColumnsWithDefault,
			accountAliasColumnsWithoutDefault,
			nzDefaults,
		)

		cache.valueMapping, err = queries.BindMapping(accountAliasType, accountAliasMapping, wl)
		if err != nil {
			return err
		}
		cache.retMapping, err = queries.BindMapping(accountAliasType, accountAliasMapping, returnColumns)
		if err != nil {
			return err
		}
		if len(wl) != 0 {
			cache.query = fmt.Sprintf("INSERT INTO \"account\".\"account_aliases\" (\"%s\") %%sVALUES (%s)%%s", strings.Join(wl, "\",\""), strmangle.Placeholders(dialect.UseIndexPlaceholders, len(wl), 1, 1))
		} else {
			cache.query = "INSERT INTO \"account\".\"account_aliases\" %sDEFAULT VALUES%s"
		}

		var queryOutput, queryReturning string

		if len(cache.retMapping) != 0 {
			queryReturning = fmt.Sprintf(" RETURNING \"%s\"", strings.Join(returnColumns, "\",\""))
		}

		cache.query = fmt.Sprintf(cache.query, queryOutput, queryReturning)
	}

	value := reflect.Indirect(reflect.ValueOf(o))
	vals := queries.ValuesFromMapping(value, cache.valueMapping)

	if boil.IsDebug(ctx) {
		writer := boil.DebugWriterFrom(ctx)
		fmt.Fprintln(writer, cache.query)
		fmt.Fprintln(writer, vals)
	}

	if len(cache.retMapping) != 0 {
		err = exec.QueryRowContext(ctx, cache.query, vals...).Scan(queries.PtrsFromMapping(value, cache.retMapping)...)
	} else {
		_, err = exec.ExecContext(ctx, cache.query, vals...)
	}

	if err != nil {
		return errors.Wrap(err, "account: unable to insert into account_aliases")
	}

	if !cached {
		accountAliasInsertCacheMut.Lock()
		accountAliasInsertCache[key] = cache
		accountAliasInsertCacheMut.Unlock()
	}

	return o.doAfterInsertHooks(ctx, exec)
}

// Update uses an executor to update the AccountAlias.
// See boil.Columns.UpdateColumnSet documentation to understand column list inference for updates.
// Update does not automatically update the record in case of default values. Use .Reload() to refresh the records.
func (o *AccountAlias) Update(ctx context.Context, exec boil.ContextExecutor, columns boil.Columns) (int64, error) {
	var err error
	if err = o.doBeforeUpdateHooks(ctx, exec); err != nil {
		return 0, err
	}
	key := makeCacheKey(columns, nil)
	accountAliasUpdateCacheMut.RLock()
	cache, cached := accountAliasUpdateCache[key]
	accountAliasUpdateCacheMut.RUnlock()

	if !cached {
		wl := columns.UpdateColumnSet(
			accountAliasAllColumns,
			accountAliasPrimaryKeyColumns,
		)

		if !columns.IsWhitelist() {
			wl = strmangle.SetComplement(wl, []string{"created_at"})
		}
		if len(wl) == 0 {
			return 0, errors.New("account: unable to update account_aliases, could not build whitelist")
		}

		cache.query = fmt.Sprintf("UPDATE \"account\".\"account_aliases\" SET %s WHERE %s",
			strmangle.SetParamNames("\"", "\"", 1, wl),
			strmangle.WhereClause("\"", "\"", len(wl)+1, accountAliasPrimaryKeyColumns),
		)
		cache.valueMapping, err = queries.BindMapping(accountAliasType, accountAliasMapping, append(wl, accountAliasPrimaryKeyColumns...))
		if err != nil {
			return 0, err
		}
	}

	values := queries.ValuesFromMapping(reflect.Indirect(reflect.ValueOf(o)), cache.valueMapping)

	if boil.IsDebug(ctx) {
		writer := boil.DebugWriterFrom(ctx)
		fmt.Fprintln(writer, cache.query)
		fmt.Fprintln(writer, values)
	}
	var result sql.Result
	result, err = exec.ExecContext(ctx, cache.query, values...)
	if err != nil {
		return 0, errors.Wrap(err, "account: unable to update account_aliases row")
	}

	rowsAff, err := result.RowsAffected()
	if err != nil {
		return 0, errors.Wrap(err, "account: failed to get rows affected by update for account_aliases")
	}

	if !cached {
		accountAliasUpdateCacheMut.Lock()
		accountAliasUpdateCache[key] = cache
		accountAliasUpdateCacheMut.Unlock()
	}

	return rowsAff, o.doAfterUpdateHooks(ctx, exec)
}

// UpdateAll updates all rows with the specified column values.
func (q accountAliasQuery) UpdateAll(ctx context.Context, exec boil.ContextExecutor, cols M) (int64, error) {
	queries.SetUpdate(q.Query, cols)

	result, err := q.Query.ExecContext(ctx, exec)
	if err != nil {
		return 0, errors.Wrap(err, "account: unable to update all for account_aliases")
	}

	rowsAff, err := result.RowsAffected()
	if err != nil {
		return 0, errors.Wrap(err, "account: unable to retrieve rows affected for account_aliases")
	}

	return rowsAff, nil
}

// UpdateAll updates all rows with the specified column values, using an executor.
func (o AccountAliasSlice) UpdateAll(ctx context.Context, exec boil.ContextExecutor, cols M) (int64, error) {
	ln := int64(len(o))
	if ln == 0 {
		return 0, nil
	}

	if len(cols) == 0 {
		return 0, errors.New("account: update all requires at least one column argument")
	}

	colNames := make([]string, len(cols))
	args := make([]interface{}, len(cols))

	i := 0
	for name, value := range cols {
		colNames[i] = name
		args[i] = value
		i++
	}

	// Append all of the primary key values for each column
	for _, obj := range o {
		pkeyArgs := queries.ValuesFromMapping(reflect.Indirect(reflect.ValueOf(obj)), accountAliasPrimaryKeyMapping)
		args = append(args, pkeyArgs...)
	}

	sql := fmt.Sprintf("UPDATE \"account\".\"account_aliases\" SET %s WHERE %s",
		strmangle.SetParamNames("\"", "\"", 1, colNames),
		strmangle.WhereClauseRepeated(string(dialect.LQ), string(dialect.RQ), len(colNames)+1, accountAliasPrimaryKeyColumns, len(o)))

	if boil.IsDebug(ctx) {
		writer := boil.DebugWriterFrom(ctx)
		fmt.Fprintln(writer, sql)
		fmt.Fprintln(writer, args...)
	}
	result, err := exec.ExecContext(ctx, sql, args...)
	if err != nil {
		return 0, errors.Wrap(err, "account: unable to update all in accountAlias slice")
	}

	rowsAff, err := result.RowsAffected()
	if err != nil {
		return 0, errors.Wrap(err, "account: unable to retrieve rows affected all in update all accountAlias")
	}
	return rowsAff, nil
}

// Upsert attempts an insert using an executor, and does an update or ignore on conflict.
// See boil.Columns documentation for how to properly use updateColumns and insertColumns.
func (o *AccountAlias) Upsert(ctx context.Context, exec boil.ContextExecutor, updateOnConflict bool, conflictColumns []string, updateColumns, insertColumns boil.Columns) error {
	if o == nil {
		return errors.New("account: no account_aliases provided for upsert")
	}
	if !boil.TimestampsAreSkipped(ctx) {
		currTime := time.Now().In(boil.GetLocation())

		if o.CreatedAt.IsZero() {
			o.CreatedAt = currTime
		}
	}

	if err := o.doBeforeUpsertHooks(ctx, exec); err != nil {
		return err
	}

	nzDefaults := queries.NonZeroDefaultSet(accountAliasColumnsWithDefault, o)

	// Build cache key in-line uglily - mysql vs psql problems
	buf := strmangle.GetBuffer()
	if updateOnConflict {
		buf.WriteByte('t')
	} else {
		buf.WriteByte('f')
	}
	buf.WriteByte('.')
	for _, c := range conflictColumns {
		buf.WriteString(c)
	}
	buf.WriteByte('.')
	buf.WriteString(strconv.Itoa(updateColumns.Kind))
	for _, c := range updateColumns.Cols {
		buf.WriteString(c)
	}
	buf.WriteByte('.')
	buf.WriteString(strconv.Itoa(insertColumns.Kind))
	for _, c := range insertColumns.Cols {
		buf.WriteString(c)
	}
	buf.WriteByte('.')
	for _, c := range nzDefaults {
		buf.WriteString(c)
	}
	key := buf.String()
	strmangle.PutBuffer(buf)

	accountAliasUpsertCacheMut.RLock()
	cache, cached := accountAliasUpsertCache[key]
	accountAliasUpsertCacheMut.RUnlock()

	var err error

	if !cached {
		insert, ret := insertColumns.InsertColumnSet(
			accountAliasAllColumns,
			accountAliasColumnsWithDefault,
			accountAliasColumnsWithoutDefault,
			nzDefaults,
		)

		update := updateColumns.UpdateColumnSet(
			accountAliasAllColumns,
			accountAliasPrimaryKeyColumns,
		)

		if updateOnConflict && len(update) == 0 {
			return errors.New("account: unable to upsert account_aliases, could not build update column list")
		}

		conflict := conflictColumns
		if len(conflict) == 0 {
			conflict = make([]string, len(accountAliasPrimaryKeyColumns))
			copy(conflict, accountAliasPrimaryKeyColumns)
		}
		cache.query = buildUpsertQueryPostgres(dialect, "\"account\".\"account_aliases\"", updateOnConflict, ret, update, conflict, insert)

		cache.valueMapping, err = queries.BindMapping(accountAliasType, accountAliasMapping, insert)
		if err != nil {
			return err
		}
		if len(ret) != 0 {
			cache.retMapping, err = queries.BindMapping(accountAliasType, accountAliasMapping, ret)
			if err != nil {
				return err
			}
		}
	}

	value := reflect.Indirect(reflect.ValueOf(o))
	vals := queries.ValuesFromMapping(value, cache.valueMapping)
	var returns []interface{}
	if len(cache.retMapping) != 0 {
		returns = queries.PtrsFromMapping(value, cache.retMapping)
	}

	if boil.IsDebug(ctx) {
		writer := boil.DebugWriterFrom(ctx)
		fmt.Fprintln(writer, cache.query)
		fmt.Fprintln(writer, vals)
	}
	if len(cache.retMapping) != 0 {
		err = exec.QueryRowContext(ctx, cache.query, vals...).Scan(returns...)
		if errors.Is(err, sql.ErrNoRows) {
			err = nil // Postgres doesn't return anything when there's no update
		}
	} else {
		_, err = exec.ExecContext(ctx, cache.query, vals...)
	}
	if err != nil {
		return errors.Wrap(err, "account: unable to upsert account_aliases")
	}

	if !cached {
		accountAliasUpsertCacheMut.Lock()
		accountAliasUpsertCache[key] = cache
		accountAliasUpsertCacheMut.Unlock()
	}

	return o.doAfterUpsertHooks(ctx, exec)
}

// Delete deletes a single AccountAlias record with an executor.
// Delete will match against the primary key column to find the record to delete.
func (o *AccountAlias) Delete(ctx context.Context, exec boil.ContextExecutor) (int64, error) {
	if o == nil {
		return 0, errors.New("account: no AccountAlias provided for delete")
	}

	if err := o.doBeforeDeleteHooks(ctx, exec); err != nil {
		return 0, err
	}

	args := queries.ValuesFromMapping(reflect.Indirect(reflect.ValueOf(o)), accountAliasPrimaryKeyMapping)
	sql := "DELETE FROM \"account\".\"account_aliases\" WHERE \"alias_account_id\"=$1"

	if boil.IsDebug(ctx) {
		writer := boil.DebugWriterFrom(ctx)
		fmt.Fprintln(writer, sql)
		fmt.Fprintln(writer, args...)
	}
	result, err := exec.ExecContext(ctx, sql, args...)
	if err != nil {
		return 0, errors.Wrap(err, "account: unable to delete from account_aliases")
	}

	rowsAff, err := result.RowsAffected()
	if err != nil {
		return 0, errors.Wrap(err, "account: failed to get rows affected by delete for account_aliases")
	}

	if err := o.doAfterDeleteHooks(ctx, exec); err != nil {
		return 0, err
	}

	return rowsAff, nil
}

// DeleteAll deletes all matching rows.
func (q accountAliasQuery) DeleteAll(ctx context.Context, exec boil.ContextExecutor) (int64, error) {
	if q.Query == nil {
		return 0, errors.New("account: no accountAliasQuery provided for delete all")
	}

	queries.SetDelete(q.Query)

	result, err := q.Query.ExecContext(ctx, exec)
	if err != nil {
		return 0, errors.Wrap(err, "account: unable to delete all from account_aliases")
	}

	rowsAff, err := result.RowsAffected()
	if err != nil {
		return 0, errors.Wrap(err, "account: failed to get rows affected by deleteall for account_aliases")
	}

	return rowsAff, nil
}

// DeleteAll deletes all rows in the slice, using an executor.
func (o AccountAliasSlice) DeleteAll(ctx context.Context, exec boil.ContextExecutor) (int64, error) {
	if len(o) == 0 {
		return 0, nil
	}

	if len(accountAliasBeforeDeleteHooks) != 0 {
		for _, obj := range o {
			if err := obj.doBeforeDeleteHooks(ctx, exec); err != nil {
				return 0, err
			}
		}
	}

	var args []interface{}
	for _, obj := range o {
		pkeyArgs := queries.ValuesFromMapping(reflect.Indirect(reflect.ValueOf(obj)), accountAliasPrimaryKeyMapping)
		args = append(args, pkeyArgs...)
	}

	sql := "DELETE FROM \"account\".\"account_aliases\" WHERE " +
		strmangle.WhereClauseRepeated(string(dialect.LQ), string(dialect.RQ), 1, accountAliasPrimaryKeyColumns, len(o))

	if boil.IsDebug(ctx) {
		writer := boil.DebugWriterFrom(ctx)
		fmt.Fprintln(writer, sql)
		fmt.Fprintln(writer, args)
	}
	result, err := exec.ExecContext(ctx, sql, args...)
	if err != nil {
		return 0, errors.Wrap(err, "account: unable to delete all from accountAlias slice")
	}

	rowsAff, err := result.RowsAffected()
	if err != nil {
		return 0, errors.Wrap(err, "account: failed to get rows affected by deleteall for account_aliases")
	}

	if len(accountAliasAfterDeleteHooks) != 0 {
		for _, obj := range o {
			if err := obj.doAfterDeleteHooks(ctx, exec); err != nil {
				return 0, err
			}
		}
	}

	return rowsAff, nil
}

// Reload refetches the object from the database
// using the primary keys with an executor.
func (o *AccountAlias) Reload(ctx context.Context, exec boil.ContextExecutor) error {
	ret, err := FindAccountAlias(ctx, exec, o.AliasAccountID)
	if err != nil {
		return err
	}

	*o = *ret
	return nil
}

// ReloadAll refetches every row with matching primary key column values
// and overwrites the original object slice with the newly updated slice.
func (o *AccountAliasSlice) ReloadAll(ctx context.Context, exec boil.ContextExecutor) error {
	if o == nil || len(*o) == 0 {
		return nil
	}

	slice := AccountAliasSlice{}
	var args []interface{}
	for _, obj := range *o {
		pkeyArgs := queries.ValuesFromMapping(reflect.Indirect(reflect.ValueOf(obj)), accountAliasPrimaryKeyMapping)
		args = append(args, pkeyArgs...)
	}

	sql := "SELECT \"account\".\"account_aliases\".* FROM \"account\".\"account_aliases\" WHERE " +
		strmangle.WhereClauseRepeated(string(dialect.LQ), string(dialect.RQ), 1, accountAliasPrimaryKeyColumns, len(*o))

	q := queries.Raw(sql, args...)

	err := q.Bind(ctx, exec, &slice)
	if err != nil {
		return errors.Wrap(err, "account: unable to reload all in AccountAliasSlice")
	}

	*o = slice

	return nil
}

// AccountAliasExists checks if the AccountAlias row exists.
func AccountAliasExists(ctx context.Context, exec boil.ContextExecutor, aliasAccountID string) (bool, error) {
	var exists bool
	sql := "select exists(select 1 from \"account\".\"account_aliases\" where \"alias_account_id\"=$1 limit 1)"

	if boil.IsDebug(ctx) {
		writer := boil.DebugWriterFrom(ctx)
		fmt.Fprintln(writer, sql)
		fmt.Fprintln(writer, aliasAccountID)
	}
	row := exec.QueryRowContext(ctx, sql, aliasAccountID)

	err := row.Scan(&exists)
	if err != nil {
		return false, errors.Wrap(err, "account: unable to check if account_aliases exists")
	}

	return exists, nil
}
