// Code generated by SQLBoiler (https://github.com/volatiletech/sqlboiler). DO NOT EDIT.
// This file is meant to be re-generated in place and/or deleted at any time.

package parsed_loss_runs

import (
	"context"
	"database/sql"
	"fmt"
	"reflect"
	"strconv"
	"strings"
	"sync"
	"time"

	"github.com/friendsofgo/errors"
	"github.com/volatiletech/null/v8"
	"github.com/volatiletech/sqlboiler/v4/boil"
	"github.com/volatiletech/sqlboiler/v4/queries"
	"github.com/volatiletech/sqlboiler/v4/queries/qm"
	"github.com/volatiletech/sqlboiler/v4/queries/qmhelper"
	"github.com/volatiletech/sqlboiler/v4/types"
	"github.com/volatiletech/strmangle"
)

// ProcessedLoss is an object representing the database table.
type ProcessedLoss struct {
	ID                     string            `boil:"id" json:"id" toml:"id" yaml:"id"`
	AggregationID          string            `boil:"aggregation_id" json:"aggregation_id" toml:"aggregation_id" yaml:"aggregation_id"`
	PeriodStartDate        null.Time         `boil:"period_start_date" json:"period_start_date,omitempty" toml:"period_start_date" yaml:"period_start_date,omitempty"`
	PeriodEndDate          null.Time         `boil:"period_end_date" json:"period_end_date,omitempty" toml:"period_end_date" yaml:"period_end_date,omitempty"`
	Coverage               null.String       `boil:"coverage" json:"coverage,omitempty" toml:"coverage" yaml:"coverage,omitempty"`
	PolicyNo               null.String       `boil:"policy_no" json:"policy_no,omitempty" toml:"policy_no" yaml:"policy_no,omitempty"`
	Lob                    null.String       `boil:"lob" json:"lob,omitempty" toml:"lob" yaml:"lob,omitempty"`
	Insurer                null.String       `boil:"insurer" json:"insurer,omitempty" toml:"insurer" yaml:"insurer,omitempty"`
	Insured                null.String       `boil:"insured" json:"insured,omitempty" toml:"insured" yaml:"insured,omitempty"`
	Agent                  null.String       `boil:"agent" json:"agent,omitempty" toml:"agent" yaml:"agent,omitempty"`
	EffDate                null.Time         `boil:"eff_date" json:"eff_date,omitempty" toml:"eff_date" yaml:"eff_date,omitempty"`
	ExpDate                null.Time         `boil:"exp_date" json:"exp_date,omitempty" toml:"exp_date" yaml:"exp_date,omitempty"`
	ReportGenerationDate   null.Time         `boil:"report_generation_date" json:"report_generation_date,omitempty" toml:"report_generation_date" yaml:"report_generation_date,omitempty"`
	CancelDate             null.Time         `boil:"cancel_date" json:"cancel_date,omitempty" toml:"cancel_date" yaml:"cancel_date,omitempty"`
	NoLoss                 int               `boil:"no_loss" json:"no_loss" toml:"no_loss" yaml:"no_loss"`
	PolicyTotalPaid        null.Float64      `boil:"policy_total_paid" json:"policy_total_paid,omitempty" toml:"policy_total_paid" yaml:"policy_total_paid,omitempty"`
	PolicyTotalReserve     null.Float64      `boil:"policy_total_reserve" json:"policy_total_reserve,omitempty" toml:"policy_total_reserve" yaml:"policy_total_reserve,omitempty"`
	PolicyTotalRecovered   null.Float64      `boil:"policy_total_recovered" json:"policy_total_recovered,omitempty" toml:"policy_total_recovered" yaml:"policy_total_recovered,omitempty"`
	PolicyTotalIncurred    float64           `boil:"policy_total_incurred" json:"policy_total_incurred" toml:"policy_total_incurred" yaml:"policy_total_incurred"`
	ClaimID                null.String       `boil:"claim_id" json:"claim_id,omitempty" toml:"claim_id" yaml:"claim_id,omitempty"`
	OccurrenceID           string            `boil:"occurrence_id" json:"occurrence_id" toml:"occurrence_id" yaml:"occurrence_id"`
	DateOfLoss             null.Time         `boil:"date_of_loss" json:"date_of_loss,omitempty" toml:"date_of_loss" yaml:"date_of_loss,omitempty"`
	DateReported           null.Time         `boil:"date_reported" json:"date_reported,omitempty" toml:"date_reported" yaml:"date_reported,omitempty"`
	TimeOfLoss             null.Time         `boil:"time_of_loss" json:"time_of_loss,omitempty" toml:"time_of_loss" yaml:"time_of_loss,omitempty"`
	ClosedDate             null.Time         `boil:"closed_date" json:"closed_date,omitempty" toml:"closed_date" yaml:"closed_date,omitempty"`
	CauseOfLossSummary     null.String       `boil:"cause_of_loss_summary" json:"cause_of_loss_summary,omitempty" toml:"cause_of_loss_summary" yaml:"cause_of_loss_summary,omitempty"`
	CauseOfLossDescription null.String       `boil:"cause_of_loss_description" json:"cause_of_loss_description,omitempty" toml:"cause_of_loss_description" yaml:"cause_of_loss_description,omitempty"`
	Vin                    null.String       `boil:"vin" json:"vin,omitempty" toml:"vin" yaml:"vin,omitempty"`
	Type                   null.String       `boil:"type" json:"type,omitempty" toml:"type" yaml:"type,omitempty"`
	DriverID               null.String       `boil:"driver_id" json:"driver_id,omitempty" toml:"driver_id" yaml:"driver_id,omitempty"`
	Name                   null.String       `boil:"name" json:"name,omitempty" toml:"name" yaml:"name,omitempty"`
	Age                    null.Int          `boil:"age" json:"age,omitempty" toml:"age" yaml:"age,omitempty"`
	Gender                 null.String       `boil:"gender" json:"gender,omitempty" toml:"gender" yaml:"gender,omitempty"`
	Dob                    null.Time         `boil:"dob" json:"dob,omitempty" toml:"dob" yaml:"dob,omitempty"`
	HiringDate             null.Time         `boil:"hiring_date" json:"hiring_date,omitempty" toml:"hiring_date" yaml:"hiring_date,omitempty"`
	Street                 null.String       `boil:"street" json:"street,omitempty" toml:"street" yaml:"street,omitempty"`
	CityCounty             null.String       `boil:"city_county" json:"city_county,omitempty" toml:"city_county" yaml:"city_county,omitempty"`
	State                  null.String       `boil:"state" json:"state,omitempty" toml:"state" yaml:"state,omitempty"`
	Zip                    null.String       `boil:"zip" json:"zip,omitempty" toml:"zip" yaml:"zip,omitempty"`
	LossLocation           null.String       `boil:"loss_location" json:"loss_location,omitempty" toml:"loss_location" yaml:"loss_location,omitempty"`
	ClaimStatus            null.String       `boil:"claim_status" json:"claim_status,omitempty" toml:"claim_status" yaml:"claim_status,omitempty"`
	Claimant               null.String       `boil:"claimant" json:"claimant,omitempty" toml:"claimant" yaml:"claimant,omitempty"`
	CoverageInferred       null.String       `boil:"coverage_inferred" json:"coverage_inferred,omitempty" toml:"coverage_inferred" yaml:"coverage_inferred,omitempty"`
	ClaimLossType          null.String       `boil:"claim_loss_type" json:"claim_loss_type,omitempty" toml:"claim_loss_type" yaml:"claim_loss_type,omitempty"`
	ClaimLossTypeInferred  null.String       `boil:"claim_loss_type_inferred" json:"claim_loss_type_inferred,omitempty" toml:"claim_loss_type_inferred" yaml:"claim_loss_type_inferred,omitempty"`
	Examiner               null.String       `boil:"examiner" json:"examiner,omitempty" toml:"examiner" yaml:"examiner,omitempty"`
	LossReserved           null.Float64      `boil:"loss_reserved" json:"loss_reserved,omitempty" toml:"loss_reserved" yaml:"loss_reserved,omitempty"`
	LossPaid               null.Float64      `boil:"loss_paid" json:"loss_paid,omitempty" toml:"loss_paid" yaml:"loss_paid,omitempty"`
	ExpenseReserve         null.Float64      `boil:"expense_reserve" json:"expense_reserve,omitempty" toml:"expense_reserve" yaml:"expense_reserve,omitempty"`
	ExpensePaid            null.Float64      `boil:"expense_paid" json:"expense_paid,omitempty" toml:"expense_paid" yaml:"expense_paid,omitempty"`
	LossTotalReserve       null.Float64      `boil:"loss_total_reserve" json:"loss_total_reserve,omitempty" toml:"loss_total_reserve" yaml:"loss_total_reserve,omitempty"`
	LossTotalPaid          null.Float64      `boil:"loss_total_paid" json:"loss_total_paid,omitempty" toml:"loss_total_paid" yaml:"loss_total_paid,omitempty"`
	LossTotalRecovered     null.Float64      `boil:"loss_total_recovered" json:"loss_total_recovered,omitempty" toml:"loss_total_recovered" yaml:"loss_total_recovered,omitempty"`
	LossTotalIncurred      float64           `boil:"loss_total_incurred" json:"loss_total_incurred" toml:"loss_total_incurred" yaml:"loss_total_incurred"`
	DeductibleAmount       null.Float64      `boil:"deductible_amount" json:"deductible_amount,omitempty" toml:"deductible_amount" yaml:"deductible_amount,omitempty"`
	SubrogationAmount      null.Float64      `boil:"subrogation_amount" json:"subrogation_amount,omitempty" toml:"subrogation_amount" yaml:"subrogation_amount,omitempty"`
	SalvageAmount          null.Float64      `boil:"salvage_amount" json:"salvage_amount,omitempty" toml:"salvage_amount" yaml:"salvage_amount,omitempty"`
	OtherRecovery          null.Float64      `boil:"other_recovery" json:"other_recovery,omitempty" toml:"other_recovery" yaml:"other_recovery,omitempty"`
	LegalReserve           null.Float64      `boil:"legal_reserve" json:"legal_reserve,omitempty" toml:"legal_reserve" yaml:"legal_reserve,omitempty"`
	LegalPaid              null.Float64      `boil:"legal_paid" json:"legal_paid,omitempty" toml:"legal_paid" yaml:"legal_paid,omitempty"`
	LegalIncurred          null.Float64      `boil:"legal_incurred" json:"legal_incurred,omitempty" toml:"legal_incurred" yaml:"legal_incurred,omitempty"`
	AlaeExpenseReserve     null.Float64      `boil:"alae_expense_reserve" json:"alae_expense_reserve,omitempty" toml:"alae_expense_reserve" yaml:"alae_expense_reserve,omitempty"`
	AlaePaid               null.Float64      `boil:"alae_paid" json:"alae_paid,omitempty" toml:"alae_paid" yaml:"alae_paid,omitempty"`
	AlaeIncurred           null.Float64      `boil:"alae_incurred" json:"alae_incurred,omitempty" toml:"alae_incurred" yaml:"alae_incurred,omitempty"`
	OtherExpenseReserve    null.Float64      `boil:"other_expense_reserve" json:"other_expense_reserve,omitempty" toml:"other_expense_reserve" yaml:"other_expense_reserve,omitempty"`
	OtherExpensePaid       null.Float64      `boil:"other_expense_paid" json:"other_expense_paid,omitempty" toml:"other_expense_paid" yaml:"other_expense_paid,omitempty"`
	OtherExpenseIncurred   null.Float64      `boil:"other_expense_incurred" json:"other_expense_incurred,omitempty" toml:"other_expense_incurred" yaml:"other_expense_incurred,omitempty"`
	ConfidenceInfo         types.JSON        `boil:"confidence_info" json:"confidence_info" toml:"confidence_info" yaml:"confidence_info"`
	CreatedAt              time.Time         `boil:"created_at" json:"created_at" toml:"created_at" yaml:"created_at"`
	Unmapped               null.Bool         `boil:"unmapped" json:"unmapped,omitempty" toml:"unmapped" yaml:"unmapped,omitempty"`
	UnmappedReasons        types.StringArray `boil:"unmapped_reasons" json:"unmapped_reasons,omitempty" toml:"unmapped_reasons" yaml:"unmapped_reasons,omitempty"`

	R *processedLossR `boil:"" json:"" toml:"" yaml:""`
	L processedLossL  `boil:"-" json:"-" toml:"-" yaml:"-"`
}

var ProcessedLossColumns = struct {
	ID                     string
	AggregationID          string
	PeriodStartDate        string
	PeriodEndDate          string
	Coverage               string
	PolicyNo               string
	Lob                    string
	Insurer                string
	Insured                string
	Agent                  string
	EffDate                string
	ExpDate                string
	ReportGenerationDate   string
	CancelDate             string
	NoLoss                 string
	PolicyTotalPaid        string
	PolicyTotalReserve     string
	PolicyTotalRecovered   string
	PolicyTotalIncurred    string
	ClaimID                string
	OccurrenceID           string
	DateOfLoss             string
	DateReported           string
	TimeOfLoss             string
	ClosedDate             string
	CauseOfLossSummary     string
	CauseOfLossDescription string
	Vin                    string
	Type                   string
	DriverID               string
	Name                   string
	Age                    string
	Gender                 string
	Dob                    string
	HiringDate             string
	Street                 string
	CityCounty             string
	State                  string
	Zip                    string
	LossLocation           string
	ClaimStatus            string
	Claimant               string
	CoverageInferred       string
	ClaimLossType          string
	ClaimLossTypeInferred  string
	Examiner               string
	LossReserved           string
	LossPaid               string
	ExpenseReserve         string
	ExpensePaid            string
	LossTotalReserve       string
	LossTotalPaid          string
	LossTotalRecovered     string
	LossTotalIncurred      string
	DeductibleAmount       string
	SubrogationAmount      string
	SalvageAmount          string
	OtherRecovery          string
	LegalReserve           string
	LegalPaid              string
	LegalIncurred          string
	AlaeExpenseReserve     string
	AlaePaid               string
	AlaeIncurred           string
	OtherExpenseReserve    string
	OtherExpensePaid       string
	OtherExpenseIncurred   string
	ConfidenceInfo         string
	CreatedAt              string
	Unmapped               string
	UnmappedReasons        string
}{
	ID:                     "id",
	AggregationID:          "aggregation_id",
	PeriodStartDate:        "period_start_date",
	PeriodEndDate:          "period_end_date",
	Coverage:               "coverage",
	PolicyNo:               "policy_no",
	Lob:                    "lob",
	Insurer:                "insurer",
	Insured:                "insured",
	Agent:                  "agent",
	EffDate:                "eff_date",
	ExpDate:                "exp_date",
	ReportGenerationDate:   "report_generation_date",
	CancelDate:             "cancel_date",
	NoLoss:                 "no_loss",
	PolicyTotalPaid:        "policy_total_paid",
	PolicyTotalReserve:     "policy_total_reserve",
	PolicyTotalRecovered:   "policy_total_recovered",
	PolicyTotalIncurred:    "policy_total_incurred",
	ClaimID:                "claim_id",
	OccurrenceID:           "occurrence_id",
	DateOfLoss:             "date_of_loss",
	DateReported:           "date_reported",
	TimeOfLoss:             "time_of_loss",
	ClosedDate:             "closed_date",
	CauseOfLossSummary:     "cause_of_loss_summary",
	CauseOfLossDescription: "cause_of_loss_description",
	Vin:                    "vin",
	Type:                   "type",
	DriverID:               "driver_id",
	Name:                   "name",
	Age:                    "age",
	Gender:                 "gender",
	Dob:                    "dob",
	HiringDate:             "hiring_date",
	Street:                 "street",
	CityCounty:             "city_county",
	State:                  "state",
	Zip:                    "zip",
	LossLocation:           "loss_location",
	ClaimStatus:            "claim_status",
	Claimant:               "claimant",
	CoverageInferred:       "coverage_inferred",
	ClaimLossType:          "claim_loss_type",
	ClaimLossTypeInferred:  "claim_loss_type_inferred",
	Examiner:               "examiner",
	LossReserved:           "loss_reserved",
	LossPaid:               "loss_paid",
	ExpenseReserve:         "expense_reserve",
	ExpensePaid:            "expense_paid",
	LossTotalReserve:       "loss_total_reserve",
	LossTotalPaid:          "loss_total_paid",
	LossTotalRecovered:     "loss_total_recovered",
	LossTotalIncurred:      "loss_total_incurred",
	DeductibleAmount:       "deductible_amount",
	SubrogationAmount:      "subrogation_amount",
	SalvageAmount:          "salvage_amount",
	OtherRecovery:          "other_recovery",
	LegalReserve:           "legal_reserve",
	LegalPaid:              "legal_paid",
	LegalIncurred:          "legal_incurred",
	AlaeExpenseReserve:     "alae_expense_reserve",
	AlaePaid:               "alae_paid",
	AlaeIncurred:           "alae_incurred",
	OtherExpenseReserve:    "other_expense_reserve",
	OtherExpensePaid:       "other_expense_paid",
	OtherExpenseIncurred:   "other_expense_incurred",
	ConfidenceInfo:         "confidence_info",
	CreatedAt:              "created_at",
	Unmapped:               "unmapped",
	UnmappedReasons:        "unmapped_reasons",
}

var ProcessedLossTableColumns = struct {
	ID                     string
	AggregationID          string
	PeriodStartDate        string
	PeriodEndDate          string
	Coverage               string
	PolicyNo               string
	Lob                    string
	Insurer                string
	Insured                string
	Agent                  string
	EffDate                string
	ExpDate                string
	ReportGenerationDate   string
	CancelDate             string
	NoLoss                 string
	PolicyTotalPaid        string
	PolicyTotalReserve     string
	PolicyTotalRecovered   string
	PolicyTotalIncurred    string
	ClaimID                string
	OccurrenceID           string
	DateOfLoss             string
	DateReported           string
	TimeOfLoss             string
	ClosedDate             string
	CauseOfLossSummary     string
	CauseOfLossDescription string
	Vin                    string
	Type                   string
	DriverID               string
	Name                   string
	Age                    string
	Gender                 string
	Dob                    string
	HiringDate             string
	Street                 string
	CityCounty             string
	State                  string
	Zip                    string
	LossLocation           string
	ClaimStatus            string
	Claimant               string
	CoverageInferred       string
	ClaimLossType          string
	ClaimLossTypeInferred  string
	Examiner               string
	LossReserved           string
	LossPaid               string
	ExpenseReserve         string
	ExpensePaid            string
	LossTotalReserve       string
	LossTotalPaid          string
	LossTotalRecovered     string
	LossTotalIncurred      string
	DeductibleAmount       string
	SubrogationAmount      string
	SalvageAmount          string
	OtherRecovery          string
	LegalReserve           string
	LegalPaid              string
	LegalIncurred          string
	AlaeExpenseReserve     string
	AlaePaid               string
	AlaeIncurred           string
	OtherExpenseReserve    string
	OtherExpensePaid       string
	OtherExpenseIncurred   string
	ConfidenceInfo         string
	CreatedAt              string
	Unmapped               string
	UnmappedReasons        string
}{
	ID:                     "processed_loss.id",
	AggregationID:          "processed_loss.aggregation_id",
	PeriodStartDate:        "processed_loss.period_start_date",
	PeriodEndDate:          "processed_loss.period_end_date",
	Coverage:               "processed_loss.coverage",
	PolicyNo:               "processed_loss.policy_no",
	Lob:                    "processed_loss.lob",
	Insurer:                "processed_loss.insurer",
	Insured:                "processed_loss.insured",
	Agent:                  "processed_loss.agent",
	EffDate:                "processed_loss.eff_date",
	ExpDate:                "processed_loss.exp_date",
	ReportGenerationDate:   "processed_loss.report_generation_date",
	CancelDate:             "processed_loss.cancel_date",
	NoLoss:                 "processed_loss.no_loss",
	PolicyTotalPaid:        "processed_loss.policy_total_paid",
	PolicyTotalReserve:     "processed_loss.policy_total_reserve",
	PolicyTotalRecovered:   "processed_loss.policy_total_recovered",
	PolicyTotalIncurred:    "processed_loss.policy_total_incurred",
	ClaimID:                "processed_loss.claim_id",
	OccurrenceID:           "processed_loss.occurrence_id",
	DateOfLoss:             "processed_loss.date_of_loss",
	DateReported:           "processed_loss.date_reported",
	TimeOfLoss:             "processed_loss.time_of_loss",
	ClosedDate:             "processed_loss.closed_date",
	CauseOfLossSummary:     "processed_loss.cause_of_loss_summary",
	CauseOfLossDescription: "processed_loss.cause_of_loss_description",
	Vin:                    "processed_loss.vin",
	Type:                   "processed_loss.type",
	DriverID:               "processed_loss.driver_id",
	Name:                   "processed_loss.name",
	Age:                    "processed_loss.age",
	Gender:                 "processed_loss.gender",
	Dob:                    "processed_loss.dob",
	HiringDate:             "processed_loss.hiring_date",
	Street:                 "processed_loss.street",
	CityCounty:             "processed_loss.city_county",
	State:                  "processed_loss.state",
	Zip:                    "processed_loss.zip",
	LossLocation:           "processed_loss.loss_location",
	ClaimStatus:            "processed_loss.claim_status",
	Claimant:               "processed_loss.claimant",
	CoverageInferred:       "processed_loss.coverage_inferred",
	ClaimLossType:          "processed_loss.claim_loss_type",
	ClaimLossTypeInferred:  "processed_loss.claim_loss_type_inferred",
	Examiner:               "processed_loss.examiner",
	LossReserved:           "processed_loss.loss_reserved",
	LossPaid:               "processed_loss.loss_paid",
	ExpenseReserve:         "processed_loss.expense_reserve",
	ExpensePaid:            "processed_loss.expense_paid",
	LossTotalReserve:       "processed_loss.loss_total_reserve",
	LossTotalPaid:          "processed_loss.loss_total_paid",
	LossTotalRecovered:     "processed_loss.loss_total_recovered",
	LossTotalIncurred:      "processed_loss.loss_total_incurred",
	DeductibleAmount:       "processed_loss.deductible_amount",
	SubrogationAmount:      "processed_loss.subrogation_amount",
	SalvageAmount:          "processed_loss.salvage_amount",
	OtherRecovery:          "processed_loss.other_recovery",
	LegalReserve:           "processed_loss.legal_reserve",
	LegalPaid:              "processed_loss.legal_paid",
	LegalIncurred:          "processed_loss.legal_incurred",
	AlaeExpenseReserve:     "processed_loss.alae_expense_reserve",
	AlaePaid:               "processed_loss.alae_paid",
	AlaeIncurred:           "processed_loss.alae_incurred",
	OtherExpenseReserve:    "processed_loss.other_expense_reserve",
	OtherExpensePaid:       "processed_loss.other_expense_paid",
	OtherExpenseIncurred:   "processed_loss.other_expense_incurred",
	ConfidenceInfo:         "processed_loss.confidence_info",
	CreatedAt:              "processed_loss.created_at",
	Unmapped:               "processed_loss.unmapped",
	UnmappedReasons:        "processed_loss.unmapped_reasons",
}

// Generated where

type whereHelpernull_Bool struct{ field string }

func (w whereHelpernull_Bool) EQ(x null.Bool) qm.QueryMod {
	return qmhelper.WhereNullEQ(w.field, false, x)
}
func (w whereHelpernull_Bool) NEQ(x null.Bool) qm.QueryMod {
	return qmhelper.WhereNullEQ(w.field, true, x)
}
func (w whereHelpernull_Bool) LT(x null.Bool) qm.QueryMod {
	return qmhelper.Where(w.field, qmhelper.LT, x)
}
func (w whereHelpernull_Bool) LTE(x null.Bool) qm.QueryMod {
	return qmhelper.Where(w.field, qmhelper.LTE, x)
}
func (w whereHelpernull_Bool) GT(x null.Bool) qm.QueryMod {
	return qmhelper.Where(w.field, qmhelper.GT, x)
}
func (w whereHelpernull_Bool) GTE(x null.Bool) qm.QueryMod {
	return qmhelper.Where(w.field, qmhelper.GTE, x)
}

func (w whereHelpernull_Bool) IsNull() qm.QueryMod    { return qmhelper.WhereIsNull(w.field) }
func (w whereHelpernull_Bool) IsNotNull() qm.QueryMod { return qmhelper.WhereIsNotNull(w.field) }

type whereHelpertypes_StringArray struct{ field string }

func (w whereHelpertypes_StringArray) EQ(x types.StringArray) qm.QueryMod {
	return qmhelper.WhereNullEQ(w.field, false, x)
}
func (w whereHelpertypes_StringArray) NEQ(x types.StringArray) qm.QueryMod {
	return qmhelper.WhereNullEQ(w.field, true, x)
}
func (w whereHelpertypes_StringArray) LT(x types.StringArray) qm.QueryMod {
	return qmhelper.Where(w.field, qmhelper.LT, x)
}
func (w whereHelpertypes_StringArray) LTE(x types.StringArray) qm.QueryMod {
	return qmhelper.Where(w.field, qmhelper.LTE, x)
}
func (w whereHelpertypes_StringArray) GT(x types.StringArray) qm.QueryMod {
	return qmhelper.Where(w.field, qmhelper.GT, x)
}
func (w whereHelpertypes_StringArray) GTE(x types.StringArray) qm.QueryMod {
	return qmhelper.Where(w.field, qmhelper.GTE, x)
}

func (w whereHelpertypes_StringArray) IsNull() qm.QueryMod { return qmhelper.WhereIsNull(w.field) }
func (w whereHelpertypes_StringArray) IsNotNull() qm.QueryMod {
	return qmhelper.WhereIsNotNull(w.field)
}

var ProcessedLossWhere = struct {
	ID                     whereHelperstring
	AggregationID          whereHelperstring
	PeriodStartDate        whereHelpernull_Time
	PeriodEndDate          whereHelpernull_Time
	Coverage               whereHelpernull_String
	PolicyNo               whereHelpernull_String
	Lob                    whereHelpernull_String
	Insurer                whereHelpernull_String
	Insured                whereHelpernull_String
	Agent                  whereHelpernull_String
	EffDate                whereHelpernull_Time
	ExpDate                whereHelpernull_Time
	ReportGenerationDate   whereHelpernull_Time
	CancelDate             whereHelpernull_Time
	NoLoss                 whereHelperint
	PolicyTotalPaid        whereHelpernull_Float64
	PolicyTotalReserve     whereHelpernull_Float64
	PolicyTotalRecovered   whereHelpernull_Float64
	PolicyTotalIncurred    whereHelperfloat64
	ClaimID                whereHelpernull_String
	OccurrenceID           whereHelperstring
	DateOfLoss             whereHelpernull_Time
	DateReported           whereHelpernull_Time
	TimeOfLoss             whereHelpernull_Time
	ClosedDate             whereHelpernull_Time
	CauseOfLossSummary     whereHelpernull_String
	CauseOfLossDescription whereHelpernull_String
	Vin                    whereHelpernull_String
	Type                   whereHelpernull_String
	DriverID               whereHelpernull_String
	Name                   whereHelpernull_String
	Age                    whereHelpernull_Int
	Gender                 whereHelpernull_String
	Dob                    whereHelpernull_Time
	HiringDate             whereHelpernull_Time
	Street                 whereHelpernull_String
	CityCounty             whereHelpernull_String
	State                  whereHelpernull_String
	Zip                    whereHelpernull_String
	LossLocation           whereHelpernull_String
	ClaimStatus            whereHelpernull_String
	Claimant               whereHelpernull_String
	CoverageInferred       whereHelpernull_String
	ClaimLossType          whereHelpernull_String
	ClaimLossTypeInferred  whereHelpernull_String
	Examiner               whereHelpernull_String
	LossReserved           whereHelpernull_Float64
	LossPaid               whereHelpernull_Float64
	ExpenseReserve         whereHelpernull_Float64
	ExpensePaid            whereHelpernull_Float64
	LossTotalReserve       whereHelpernull_Float64
	LossTotalPaid          whereHelpernull_Float64
	LossTotalRecovered     whereHelpernull_Float64
	LossTotalIncurred      whereHelperfloat64
	DeductibleAmount       whereHelpernull_Float64
	SubrogationAmount      whereHelpernull_Float64
	SalvageAmount          whereHelpernull_Float64
	OtherRecovery          whereHelpernull_Float64
	LegalReserve           whereHelpernull_Float64
	LegalPaid              whereHelpernull_Float64
	LegalIncurred          whereHelpernull_Float64
	AlaeExpenseReserve     whereHelpernull_Float64
	AlaePaid               whereHelpernull_Float64
	AlaeIncurred           whereHelpernull_Float64
	OtherExpenseReserve    whereHelpernull_Float64
	OtherExpensePaid       whereHelpernull_Float64
	OtherExpenseIncurred   whereHelpernull_Float64
	ConfidenceInfo         whereHelpertypes_JSON
	CreatedAt              whereHelpertime_Time
	Unmapped               whereHelpernull_Bool
	UnmappedReasons        whereHelpertypes_StringArray
}{
	ID:                     whereHelperstring{field: "\"parsed_loss_runs\".\"processed_loss\".\"id\""},
	AggregationID:          whereHelperstring{field: "\"parsed_loss_runs\".\"processed_loss\".\"aggregation_id\""},
	PeriodStartDate:        whereHelpernull_Time{field: "\"parsed_loss_runs\".\"processed_loss\".\"period_start_date\""},
	PeriodEndDate:          whereHelpernull_Time{field: "\"parsed_loss_runs\".\"processed_loss\".\"period_end_date\""},
	Coverage:               whereHelpernull_String{field: "\"parsed_loss_runs\".\"processed_loss\".\"coverage\""},
	PolicyNo:               whereHelpernull_String{field: "\"parsed_loss_runs\".\"processed_loss\".\"policy_no\""},
	Lob:                    whereHelpernull_String{field: "\"parsed_loss_runs\".\"processed_loss\".\"lob\""},
	Insurer:                whereHelpernull_String{field: "\"parsed_loss_runs\".\"processed_loss\".\"insurer\""},
	Insured:                whereHelpernull_String{field: "\"parsed_loss_runs\".\"processed_loss\".\"insured\""},
	Agent:                  whereHelpernull_String{field: "\"parsed_loss_runs\".\"processed_loss\".\"agent\""},
	EffDate:                whereHelpernull_Time{field: "\"parsed_loss_runs\".\"processed_loss\".\"eff_date\""},
	ExpDate:                whereHelpernull_Time{field: "\"parsed_loss_runs\".\"processed_loss\".\"exp_date\""},
	ReportGenerationDate:   whereHelpernull_Time{field: "\"parsed_loss_runs\".\"processed_loss\".\"report_generation_date\""},
	CancelDate:             whereHelpernull_Time{field: "\"parsed_loss_runs\".\"processed_loss\".\"cancel_date\""},
	NoLoss:                 whereHelperint{field: "\"parsed_loss_runs\".\"processed_loss\".\"no_loss\""},
	PolicyTotalPaid:        whereHelpernull_Float64{field: "\"parsed_loss_runs\".\"processed_loss\".\"policy_total_paid\""},
	PolicyTotalReserve:     whereHelpernull_Float64{field: "\"parsed_loss_runs\".\"processed_loss\".\"policy_total_reserve\""},
	PolicyTotalRecovered:   whereHelpernull_Float64{field: "\"parsed_loss_runs\".\"processed_loss\".\"policy_total_recovered\""},
	PolicyTotalIncurred:    whereHelperfloat64{field: "\"parsed_loss_runs\".\"processed_loss\".\"policy_total_incurred\""},
	ClaimID:                whereHelpernull_String{field: "\"parsed_loss_runs\".\"processed_loss\".\"claim_id\""},
	OccurrenceID:           whereHelperstring{field: "\"parsed_loss_runs\".\"processed_loss\".\"occurrence_id\""},
	DateOfLoss:             whereHelpernull_Time{field: "\"parsed_loss_runs\".\"processed_loss\".\"date_of_loss\""},
	DateReported:           whereHelpernull_Time{field: "\"parsed_loss_runs\".\"processed_loss\".\"date_reported\""},
	TimeOfLoss:             whereHelpernull_Time{field: "\"parsed_loss_runs\".\"processed_loss\".\"time_of_loss\""},
	ClosedDate:             whereHelpernull_Time{field: "\"parsed_loss_runs\".\"processed_loss\".\"closed_date\""},
	CauseOfLossSummary:     whereHelpernull_String{field: "\"parsed_loss_runs\".\"processed_loss\".\"cause_of_loss_summary\""},
	CauseOfLossDescription: whereHelpernull_String{field: "\"parsed_loss_runs\".\"processed_loss\".\"cause_of_loss_description\""},
	Vin:                    whereHelpernull_String{field: "\"parsed_loss_runs\".\"processed_loss\".\"vin\""},
	Type:                   whereHelpernull_String{field: "\"parsed_loss_runs\".\"processed_loss\".\"type\""},
	DriverID:               whereHelpernull_String{field: "\"parsed_loss_runs\".\"processed_loss\".\"driver_id\""},
	Name:                   whereHelpernull_String{field: "\"parsed_loss_runs\".\"processed_loss\".\"name\""},
	Age:                    whereHelpernull_Int{field: "\"parsed_loss_runs\".\"processed_loss\".\"age\""},
	Gender:                 whereHelpernull_String{field: "\"parsed_loss_runs\".\"processed_loss\".\"gender\""},
	Dob:                    whereHelpernull_Time{field: "\"parsed_loss_runs\".\"processed_loss\".\"dob\""},
	HiringDate:             whereHelpernull_Time{field: "\"parsed_loss_runs\".\"processed_loss\".\"hiring_date\""},
	Street:                 whereHelpernull_String{field: "\"parsed_loss_runs\".\"processed_loss\".\"street\""},
	CityCounty:             whereHelpernull_String{field: "\"parsed_loss_runs\".\"processed_loss\".\"city_county\""},
	State:                  whereHelpernull_String{field: "\"parsed_loss_runs\".\"processed_loss\".\"state\""},
	Zip:                    whereHelpernull_String{field: "\"parsed_loss_runs\".\"processed_loss\".\"zip\""},
	LossLocation:           whereHelpernull_String{field: "\"parsed_loss_runs\".\"processed_loss\".\"loss_location\""},
	ClaimStatus:            whereHelpernull_String{field: "\"parsed_loss_runs\".\"processed_loss\".\"claim_status\""},
	Claimant:               whereHelpernull_String{field: "\"parsed_loss_runs\".\"processed_loss\".\"claimant\""},
	CoverageInferred:       whereHelpernull_String{field: "\"parsed_loss_runs\".\"processed_loss\".\"coverage_inferred\""},
	ClaimLossType:          whereHelpernull_String{field: "\"parsed_loss_runs\".\"processed_loss\".\"claim_loss_type\""},
	ClaimLossTypeInferred:  whereHelpernull_String{field: "\"parsed_loss_runs\".\"processed_loss\".\"claim_loss_type_inferred\""},
	Examiner:               whereHelpernull_String{field: "\"parsed_loss_runs\".\"processed_loss\".\"examiner\""},
	LossReserved:           whereHelpernull_Float64{field: "\"parsed_loss_runs\".\"processed_loss\".\"loss_reserved\""},
	LossPaid:               whereHelpernull_Float64{field: "\"parsed_loss_runs\".\"processed_loss\".\"loss_paid\""},
	ExpenseReserve:         whereHelpernull_Float64{field: "\"parsed_loss_runs\".\"processed_loss\".\"expense_reserve\""},
	ExpensePaid:            whereHelpernull_Float64{field: "\"parsed_loss_runs\".\"processed_loss\".\"expense_paid\""},
	LossTotalReserve:       whereHelpernull_Float64{field: "\"parsed_loss_runs\".\"processed_loss\".\"loss_total_reserve\""},
	LossTotalPaid:          whereHelpernull_Float64{field: "\"parsed_loss_runs\".\"processed_loss\".\"loss_total_paid\""},
	LossTotalRecovered:     whereHelpernull_Float64{field: "\"parsed_loss_runs\".\"processed_loss\".\"loss_total_recovered\""},
	LossTotalIncurred:      whereHelperfloat64{field: "\"parsed_loss_runs\".\"processed_loss\".\"loss_total_incurred\""},
	DeductibleAmount:       whereHelpernull_Float64{field: "\"parsed_loss_runs\".\"processed_loss\".\"deductible_amount\""},
	SubrogationAmount:      whereHelpernull_Float64{field: "\"parsed_loss_runs\".\"processed_loss\".\"subrogation_amount\""},
	SalvageAmount:          whereHelpernull_Float64{field: "\"parsed_loss_runs\".\"processed_loss\".\"salvage_amount\""},
	OtherRecovery:          whereHelpernull_Float64{field: "\"parsed_loss_runs\".\"processed_loss\".\"other_recovery\""},
	LegalReserve:           whereHelpernull_Float64{field: "\"parsed_loss_runs\".\"processed_loss\".\"legal_reserve\""},
	LegalPaid:              whereHelpernull_Float64{field: "\"parsed_loss_runs\".\"processed_loss\".\"legal_paid\""},
	LegalIncurred:          whereHelpernull_Float64{field: "\"parsed_loss_runs\".\"processed_loss\".\"legal_incurred\""},
	AlaeExpenseReserve:     whereHelpernull_Float64{field: "\"parsed_loss_runs\".\"processed_loss\".\"alae_expense_reserve\""},
	AlaePaid:               whereHelpernull_Float64{field: "\"parsed_loss_runs\".\"processed_loss\".\"alae_paid\""},
	AlaeIncurred:           whereHelpernull_Float64{field: "\"parsed_loss_runs\".\"processed_loss\".\"alae_incurred\""},
	OtherExpenseReserve:    whereHelpernull_Float64{field: "\"parsed_loss_runs\".\"processed_loss\".\"other_expense_reserve\""},
	OtherExpensePaid:       whereHelpernull_Float64{field: "\"parsed_loss_runs\".\"processed_loss\".\"other_expense_paid\""},
	OtherExpenseIncurred:   whereHelpernull_Float64{field: "\"parsed_loss_runs\".\"processed_loss\".\"other_expense_incurred\""},
	ConfidenceInfo:         whereHelpertypes_JSON{field: "\"parsed_loss_runs\".\"processed_loss\".\"confidence_info\""},
	CreatedAt:              whereHelpertime_Time{field: "\"parsed_loss_runs\".\"processed_loss\".\"created_at\""},
	Unmapped:               whereHelpernull_Bool{field: "\"parsed_loss_runs\".\"processed_loss\".\"unmapped\""},
	UnmappedReasons:        whereHelpertypes_StringArray{field: "\"parsed_loss_runs\".\"processed_loss\".\"unmapped_reasons\""},
}

// ProcessedLossRels is where relationship names are stored.
var ProcessedLossRels = struct {
	Aggregation string
}{
	Aggregation: "Aggregation",
}

// processedLossR is where relationships are stored.
type processedLossR struct {
	Aggregation *Aggregation `boil:"Aggregation" json:"Aggregation" toml:"Aggregation" yaml:"Aggregation"`
}

// NewStruct creates a new relationship struct
func (*processedLossR) NewStruct() *processedLossR {
	return &processedLossR{}
}

// processedLossL is where Load methods for each relationship are stored.
type processedLossL struct{}

var (
	processedLossAllColumns            = []string{"id", "aggregation_id", "period_start_date", "period_end_date", "coverage", "policy_no", "lob", "insurer", "insured", "agent", "eff_date", "exp_date", "report_generation_date", "cancel_date", "no_loss", "policy_total_paid", "policy_total_reserve", "policy_total_recovered", "policy_total_incurred", "claim_id", "occurrence_id", "date_of_loss", "date_reported", "time_of_loss", "closed_date", "cause_of_loss_summary", "cause_of_loss_description", "vin", "type", "driver_id", "name", "age", "gender", "dob", "hiring_date", "street", "city_county", "state", "zip", "loss_location", "claim_status", "claimant", "coverage_inferred", "claim_loss_type", "claim_loss_type_inferred", "examiner", "loss_reserved", "loss_paid", "expense_reserve", "expense_paid", "loss_total_reserve", "loss_total_paid", "loss_total_recovered", "loss_total_incurred", "deductible_amount", "subrogation_amount", "salvage_amount", "other_recovery", "legal_reserve", "legal_paid", "legal_incurred", "alae_expense_reserve", "alae_paid", "alae_incurred", "other_expense_reserve", "other_expense_paid", "other_expense_incurred", "confidence_info", "created_at", "unmapped", "unmapped_reasons"}
	processedLossColumnsWithoutDefault = []string{"id", "aggregation_id", "no_loss", "policy_total_incurred", "occurrence_id", "loss_total_incurred", "confidence_info", "created_at"}
	processedLossColumnsWithDefault    = []string{"period_start_date", "period_end_date", "coverage", "policy_no", "lob", "insurer", "insured", "agent", "eff_date", "exp_date", "report_generation_date", "cancel_date", "policy_total_paid", "policy_total_reserve", "policy_total_recovered", "claim_id", "date_of_loss", "date_reported", "time_of_loss", "closed_date", "cause_of_loss_summary", "cause_of_loss_description", "vin", "type", "driver_id", "name", "age", "gender", "dob", "hiring_date", "street", "city_county", "state", "zip", "loss_location", "claim_status", "claimant", "coverage_inferred", "claim_loss_type", "claim_loss_type_inferred", "examiner", "loss_reserved", "loss_paid", "expense_reserve", "expense_paid", "loss_total_reserve", "loss_total_paid", "loss_total_recovered", "deductible_amount", "subrogation_amount", "salvage_amount", "other_recovery", "legal_reserve", "legal_paid", "legal_incurred", "alae_expense_reserve", "alae_paid", "alae_incurred", "other_expense_reserve", "other_expense_paid", "other_expense_incurred", "unmapped", "unmapped_reasons"}
	processedLossPrimaryKeyColumns     = []string{"id"}
	processedLossGeneratedColumns      = []string{}
)

type (
	// ProcessedLossSlice is an alias for a slice of pointers to ProcessedLoss.
	// This should almost always be used instead of []ProcessedLoss.
	ProcessedLossSlice []*ProcessedLoss
	// ProcessedLossHook is the signature for custom ProcessedLoss hook methods
	ProcessedLossHook func(context.Context, boil.ContextExecutor, *ProcessedLoss) error

	processedLossQuery struct {
		*queries.Query
	}
)

// Cache for insert, update and upsert
var (
	processedLossType                 = reflect.TypeOf(&ProcessedLoss{})
	processedLossMapping              = queries.MakeStructMapping(processedLossType)
	processedLossPrimaryKeyMapping, _ = queries.BindMapping(processedLossType, processedLossMapping, processedLossPrimaryKeyColumns)
	processedLossInsertCacheMut       sync.RWMutex
	processedLossInsertCache          = make(map[string]insertCache)
	processedLossUpdateCacheMut       sync.RWMutex
	processedLossUpdateCache          = make(map[string]updateCache)
	processedLossUpsertCacheMut       sync.RWMutex
	processedLossUpsertCache          = make(map[string]insertCache)
)

var (
	// Force time package dependency for automated UpdatedAt/CreatedAt.
	_ = time.Second
	// Force qmhelper dependency for where clause generation (which doesn't
	// always happen)
	_ = qmhelper.Where
)

var processedLossAfterSelectHooks []ProcessedLossHook

var processedLossBeforeInsertHooks []ProcessedLossHook
var processedLossAfterInsertHooks []ProcessedLossHook

var processedLossBeforeUpdateHooks []ProcessedLossHook
var processedLossAfterUpdateHooks []ProcessedLossHook

var processedLossBeforeDeleteHooks []ProcessedLossHook
var processedLossAfterDeleteHooks []ProcessedLossHook

var processedLossBeforeUpsertHooks []ProcessedLossHook
var processedLossAfterUpsertHooks []ProcessedLossHook

// doAfterSelectHooks executes all "after Select" hooks.
func (o *ProcessedLoss) doAfterSelectHooks(ctx context.Context, exec boil.ContextExecutor) (err error) {
	if boil.HooksAreSkipped(ctx) {
		return nil
	}

	for _, hook := range processedLossAfterSelectHooks {
		if err := hook(ctx, exec, o); err != nil {
			return err
		}
	}

	return nil
}

// doBeforeInsertHooks executes all "before insert" hooks.
func (o *ProcessedLoss) doBeforeInsertHooks(ctx context.Context, exec boil.ContextExecutor) (err error) {
	if boil.HooksAreSkipped(ctx) {
		return nil
	}

	for _, hook := range processedLossBeforeInsertHooks {
		if err := hook(ctx, exec, o); err != nil {
			return err
		}
	}

	return nil
}

// doAfterInsertHooks executes all "after Insert" hooks.
func (o *ProcessedLoss) doAfterInsertHooks(ctx context.Context, exec boil.ContextExecutor) (err error) {
	if boil.HooksAreSkipped(ctx) {
		return nil
	}

	for _, hook := range processedLossAfterInsertHooks {
		if err := hook(ctx, exec, o); err != nil {
			return err
		}
	}

	return nil
}

// doBeforeUpdateHooks executes all "before Update" hooks.
func (o *ProcessedLoss) doBeforeUpdateHooks(ctx context.Context, exec boil.ContextExecutor) (err error) {
	if boil.HooksAreSkipped(ctx) {
		return nil
	}

	for _, hook := range processedLossBeforeUpdateHooks {
		if err := hook(ctx, exec, o); err != nil {
			return err
		}
	}

	return nil
}

// doAfterUpdateHooks executes all "after Update" hooks.
func (o *ProcessedLoss) doAfterUpdateHooks(ctx context.Context, exec boil.ContextExecutor) (err error) {
	if boil.HooksAreSkipped(ctx) {
		return nil
	}

	for _, hook := range processedLossAfterUpdateHooks {
		if err := hook(ctx, exec, o); err != nil {
			return err
		}
	}

	return nil
}

// doBeforeDeleteHooks executes all "before Delete" hooks.
func (o *ProcessedLoss) doBeforeDeleteHooks(ctx context.Context, exec boil.ContextExecutor) (err error) {
	if boil.HooksAreSkipped(ctx) {
		return nil
	}

	for _, hook := range processedLossBeforeDeleteHooks {
		if err := hook(ctx, exec, o); err != nil {
			return err
		}
	}

	return nil
}

// doAfterDeleteHooks executes all "after Delete" hooks.
func (o *ProcessedLoss) doAfterDeleteHooks(ctx context.Context, exec boil.ContextExecutor) (err error) {
	if boil.HooksAreSkipped(ctx) {
		return nil
	}

	for _, hook := range processedLossAfterDeleteHooks {
		if err := hook(ctx, exec, o); err != nil {
			return err
		}
	}

	return nil
}

// doBeforeUpsertHooks executes all "before Upsert" hooks.
func (o *ProcessedLoss) doBeforeUpsertHooks(ctx context.Context, exec boil.ContextExecutor) (err error) {
	if boil.HooksAreSkipped(ctx) {
		return nil
	}

	for _, hook := range processedLossBeforeUpsertHooks {
		if err := hook(ctx, exec, o); err != nil {
			return err
		}
	}

	return nil
}

// doAfterUpsertHooks executes all "after Upsert" hooks.
func (o *ProcessedLoss) doAfterUpsertHooks(ctx context.Context, exec boil.ContextExecutor) (err error) {
	if boil.HooksAreSkipped(ctx) {
		return nil
	}

	for _, hook := range processedLossAfterUpsertHooks {
		if err := hook(ctx, exec, o); err != nil {
			return err
		}
	}

	return nil
}

// AddProcessedLossHook registers your hook function for all future operations.
func AddProcessedLossHook(hookPoint boil.HookPoint, processedLossHook ProcessedLossHook) {
	switch hookPoint {
	case boil.AfterSelectHook:
		processedLossAfterSelectHooks = append(processedLossAfterSelectHooks, processedLossHook)
	case boil.BeforeInsertHook:
		processedLossBeforeInsertHooks = append(processedLossBeforeInsertHooks, processedLossHook)
	case boil.AfterInsertHook:
		processedLossAfterInsertHooks = append(processedLossAfterInsertHooks, processedLossHook)
	case boil.BeforeUpdateHook:
		processedLossBeforeUpdateHooks = append(processedLossBeforeUpdateHooks, processedLossHook)
	case boil.AfterUpdateHook:
		processedLossAfterUpdateHooks = append(processedLossAfterUpdateHooks, processedLossHook)
	case boil.BeforeDeleteHook:
		processedLossBeforeDeleteHooks = append(processedLossBeforeDeleteHooks, processedLossHook)
	case boil.AfterDeleteHook:
		processedLossAfterDeleteHooks = append(processedLossAfterDeleteHooks, processedLossHook)
	case boil.BeforeUpsertHook:
		processedLossBeforeUpsertHooks = append(processedLossBeforeUpsertHooks, processedLossHook)
	case boil.AfterUpsertHook:
		processedLossAfterUpsertHooks = append(processedLossAfterUpsertHooks, processedLossHook)
	}
}

// One returns a single processedLoss record from the query.
func (q processedLossQuery) One(ctx context.Context, exec boil.ContextExecutor) (*ProcessedLoss, error) {
	o := &ProcessedLoss{}

	queries.SetLimit(q.Query, 1)

	err := q.Bind(ctx, exec, o)
	if err != nil {
		if errors.Is(err, sql.ErrNoRows) {
			return nil, sql.ErrNoRows
		}
		return nil, errors.Wrap(err, "parsed_loss_runs: failed to execute a one query for processed_loss")
	}

	if err := o.doAfterSelectHooks(ctx, exec); err != nil {
		return o, err
	}

	return o, nil
}

// All returns all ProcessedLoss records from the query.
func (q processedLossQuery) All(ctx context.Context, exec boil.ContextExecutor) (ProcessedLossSlice, error) {
	var o []*ProcessedLoss

	err := q.Bind(ctx, exec, &o)
	if err != nil {
		return nil, errors.Wrap(err, "parsed_loss_runs: failed to assign all query results to ProcessedLoss slice")
	}

	if len(processedLossAfterSelectHooks) != 0 {
		for _, obj := range o {
			if err := obj.doAfterSelectHooks(ctx, exec); err != nil {
				return o, err
			}
		}
	}

	return o, nil
}

// Count returns the count of all ProcessedLoss records in the query.
func (q processedLossQuery) Count(ctx context.Context, exec boil.ContextExecutor) (int64, error) {
	var count int64

	queries.SetSelect(q.Query, nil)
	queries.SetCount(q.Query)

	err := q.Query.QueryRowContext(ctx, exec).Scan(&count)
	if err != nil {
		return 0, errors.Wrap(err, "parsed_loss_runs: failed to count processed_loss rows")
	}

	return count, nil
}

// Exists checks if the row exists in the table.
func (q processedLossQuery) Exists(ctx context.Context, exec boil.ContextExecutor) (bool, error) {
	var count int64

	queries.SetSelect(q.Query, nil)
	queries.SetCount(q.Query)
	queries.SetLimit(q.Query, 1)

	err := q.Query.QueryRowContext(ctx, exec).Scan(&count)
	if err != nil {
		return false, errors.Wrap(err, "parsed_loss_runs: failed to check if processed_loss exists")
	}

	return count > 0, nil
}

// Aggregation pointed to by the foreign key.
func (o *ProcessedLoss) Aggregation(mods ...qm.QueryMod) aggregationQuery {
	queryMods := []qm.QueryMod{
		qm.Where("\"id\" = ?", o.AggregationID),
	}

	queryMods = append(queryMods, mods...)

	return Aggregations(queryMods...)
}

// LoadAggregation allows an eager lookup of values, cached into the
// loaded structs of the objects. This is for an N-1 relationship.
func (processedLossL) LoadAggregation(ctx context.Context, e boil.ContextExecutor, singular bool, maybeProcessedLoss interface{}, mods queries.Applicator) error {
	var slice []*ProcessedLoss
	var object *ProcessedLoss

	if singular {
		object = maybeProcessedLoss.(*ProcessedLoss)
	} else {
		slice = *maybeProcessedLoss.(*[]*ProcessedLoss)
	}

	args := make([]interface{}, 0, 1)
	if singular {
		if object.R == nil {
			object.R = &processedLossR{}
		}
		args = append(args, object.AggregationID)

	} else {
	Outer:
		for _, obj := range slice {
			if obj.R == nil {
				obj.R = &processedLossR{}
			}

			for _, a := range args {
				if a == obj.AggregationID {
					continue Outer
				}
			}

			args = append(args, obj.AggregationID)

		}
	}

	if len(args) == 0 {
		return nil
	}

	query := NewQuery(
		qm.From(`parsed_loss_runs.aggregation`),
		qm.WhereIn(`parsed_loss_runs.aggregation.id in ?`, args...),
	)
	if mods != nil {
		mods.Apply(query)
	}

	results, err := query.QueryContext(ctx, e)
	if err != nil {
		return errors.Wrap(err, "failed to eager load Aggregation")
	}

	var resultSlice []*Aggregation
	if err = queries.Bind(results, &resultSlice); err != nil {
		return errors.Wrap(err, "failed to bind eager loaded slice Aggregation")
	}

	if err = results.Close(); err != nil {
		return errors.Wrap(err, "failed to close results of eager load for aggregation")
	}
	if err = results.Err(); err != nil {
		return errors.Wrap(err, "error occurred during iteration of eager loaded relations for aggregation")
	}

	if len(processedLossAfterSelectHooks) != 0 {
		for _, obj := range resultSlice {
			if err := obj.doAfterSelectHooks(ctx, e); err != nil {
				return err
			}
		}
	}

	if len(resultSlice) == 0 {
		return nil
	}

	if singular {
		foreign := resultSlice[0]
		object.R.Aggregation = foreign
		if foreign.R == nil {
			foreign.R = &aggregationR{}
		}
		foreign.R.ProcessedLosses = append(foreign.R.ProcessedLosses, object)
		return nil
	}

	for _, local := range slice {
		for _, foreign := range resultSlice {
			if local.AggregationID == foreign.ID {
				local.R.Aggregation = foreign
				if foreign.R == nil {
					foreign.R = &aggregationR{}
				}
				foreign.R.ProcessedLosses = append(foreign.R.ProcessedLosses, local)
				break
			}
		}
	}

	return nil
}

// SetAggregation of the processedLoss to the related item.
// Sets o.R.Aggregation to related.
// Adds o to related.R.ProcessedLosses.
func (o *ProcessedLoss) SetAggregation(ctx context.Context, exec boil.ContextExecutor, insert bool, related *Aggregation) error {
	var err error
	if insert {
		if err = related.Insert(ctx, exec, boil.Infer()); err != nil {
			return errors.Wrap(err, "failed to insert into foreign table")
		}
	}

	updateQuery := fmt.Sprintf(
		"UPDATE \"parsed_loss_runs\".\"processed_loss\" SET %s WHERE %s",
		strmangle.SetParamNames("\"", "\"", 1, []string{"aggregation_id"}),
		strmangle.WhereClause("\"", "\"", 2, processedLossPrimaryKeyColumns),
	)
	values := []interface{}{related.ID, o.ID}

	if boil.IsDebug(ctx) {
		writer := boil.DebugWriterFrom(ctx)
		fmt.Fprintln(writer, updateQuery)
		fmt.Fprintln(writer, values)
	}
	if _, err = exec.ExecContext(ctx, updateQuery, values...); err != nil {
		return errors.Wrap(err, "failed to update local table")
	}

	o.AggregationID = related.ID
	if o.R == nil {
		o.R = &processedLossR{
			Aggregation: related,
		}
	} else {
		o.R.Aggregation = related
	}

	if related.R == nil {
		related.R = &aggregationR{
			ProcessedLosses: ProcessedLossSlice{o},
		}
	} else {
		related.R.ProcessedLosses = append(related.R.ProcessedLosses, o)
	}

	return nil
}

// ProcessedLosses retrieves all the records using an executor.
func ProcessedLosses(mods ...qm.QueryMod) processedLossQuery {
	mods = append(mods, qm.From("\"parsed_loss_runs\".\"processed_loss\""))
	q := NewQuery(mods...)
	if len(queries.GetSelect(q)) == 0 {
		queries.SetSelect(q, []string{"\"parsed_loss_runs\".\"processed_loss\".*"})
	}

	return processedLossQuery{q}
}

// FindProcessedLoss retrieves a single record by ID with an executor.
// If selectCols is empty Find will return all columns.
func FindProcessedLoss(ctx context.Context, exec boil.ContextExecutor, iD string, selectCols ...string) (*ProcessedLoss, error) {
	processedLossObj := &ProcessedLoss{}

	sel := "*"
	if len(selectCols) > 0 {
		sel = strings.Join(strmangle.IdentQuoteSlice(dialect.LQ, dialect.RQ, selectCols), ",")
	}
	query := fmt.Sprintf(
		"select %s from \"parsed_loss_runs\".\"processed_loss\" where \"id\"=$1", sel,
	)

	q := queries.Raw(query, iD)

	err := q.Bind(ctx, exec, processedLossObj)
	if err != nil {
		if errors.Is(err, sql.ErrNoRows) {
			return nil, sql.ErrNoRows
		}
		return nil, errors.Wrap(err, "parsed_loss_runs: unable to select from processed_loss")
	}

	if err = processedLossObj.doAfterSelectHooks(ctx, exec); err != nil {
		return processedLossObj, err
	}

	return processedLossObj, nil
}

// Insert a single record using an executor.
// See boil.Columns.InsertColumnSet documentation to understand column list inference for inserts.
func (o *ProcessedLoss) Insert(ctx context.Context, exec boil.ContextExecutor, columns boil.Columns) error {
	if o == nil {
		return errors.New("parsed_loss_runs: no processed_loss provided for insertion")
	}

	var err error
	if !boil.TimestampsAreSkipped(ctx) {
		currTime := time.Now().In(boil.GetLocation())

		if o.CreatedAt.IsZero() {
			o.CreatedAt = currTime
		}
	}

	if err := o.doBeforeInsertHooks(ctx, exec); err != nil {
		return err
	}

	nzDefaults := queries.NonZeroDefaultSet(processedLossColumnsWithDefault, o)

	key := makeCacheKey(columns, nzDefaults)
	processedLossInsertCacheMut.RLock()
	cache, cached := processedLossInsertCache[key]
	processedLossInsertCacheMut.RUnlock()

	if !cached {
		wl, returnColumns := columns.InsertColumnSet(
			processedLossAllColumns,
			processedLossColumnsWithDefault,
			processedLossColumnsWithoutDefault,
			nzDefaults,
		)

		cache.valueMapping, err = queries.BindMapping(processedLossType, processedLossMapping, wl)
		if err != nil {
			return err
		}
		cache.retMapping, err = queries.BindMapping(processedLossType, processedLossMapping, returnColumns)
		if err != nil {
			return err
		}
		if len(wl) != 0 {
			cache.query = fmt.Sprintf("INSERT INTO \"parsed_loss_runs\".\"processed_loss\" (\"%s\") %%sVALUES (%s)%%s", strings.Join(wl, "\",\""), strmangle.Placeholders(dialect.UseIndexPlaceholders, len(wl), 1, 1))
		} else {
			cache.query = "INSERT INTO \"parsed_loss_runs\".\"processed_loss\" %sDEFAULT VALUES%s"
		}

		var queryOutput, queryReturning string

		if len(cache.retMapping) != 0 {
			queryReturning = fmt.Sprintf(" RETURNING \"%s\"", strings.Join(returnColumns, "\",\""))
		}

		cache.query = fmt.Sprintf(cache.query, queryOutput, queryReturning)
	}

	value := reflect.Indirect(reflect.ValueOf(o))
	vals := queries.ValuesFromMapping(value, cache.valueMapping)

	if boil.IsDebug(ctx) {
		writer := boil.DebugWriterFrom(ctx)
		fmt.Fprintln(writer, cache.query)
		fmt.Fprintln(writer, vals)
	}

	if len(cache.retMapping) != 0 {
		err = exec.QueryRowContext(ctx, cache.query, vals...).Scan(queries.PtrsFromMapping(value, cache.retMapping)...)
	} else {
		_, err = exec.ExecContext(ctx, cache.query, vals...)
	}

	if err != nil {
		return errors.Wrap(err, "parsed_loss_runs: unable to insert into processed_loss")
	}

	if !cached {
		processedLossInsertCacheMut.Lock()
		processedLossInsertCache[key] = cache
		processedLossInsertCacheMut.Unlock()
	}

	return o.doAfterInsertHooks(ctx, exec)
}

// Update uses an executor to update the ProcessedLoss.
// See boil.Columns.UpdateColumnSet documentation to understand column list inference for updates.
// Update does not automatically update the record in case of default values. Use .Reload() to refresh the records.
func (o *ProcessedLoss) Update(ctx context.Context, exec boil.ContextExecutor, columns boil.Columns) (int64, error) {
	var err error
	if err = o.doBeforeUpdateHooks(ctx, exec); err != nil {
		return 0, err
	}
	key := makeCacheKey(columns, nil)
	processedLossUpdateCacheMut.RLock()
	cache, cached := processedLossUpdateCache[key]
	processedLossUpdateCacheMut.RUnlock()

	if !cached {
		wl := columns.UpdateColumnSet(
			processedLossAllColumns,
			processedLossPrimaryKeyColumns,
		)

		if !columns.IsWhitelist() {
			wl = strmangle.SetComplement(wl, []string{"created_at"})
		}
		if len(wl) == 0 {
			return 0, errors.New("parsed_loss_runs: unable to update processed_loss, could not build whitelist")
		}

		cache.query = fmt.Sprintf("UPDATE \"parsed_loss_runs\".\"processed_loss\" SET %s WHERE %s",
			strmangle.SetParamNames("\"", "\"", 1, wl),
			strmangle.WhereClause("\"", "\"", len(wl)+1, processedLossPrimaryKeyColumns),
		)
		cache.valueMapping, err = queries.BindMapping(processedLossType, processedLossMapping, append(wl, processedLossPrimaryKeyColumns...))
		if err != nil {
			return 0, err
		}
	}

	values := queries.ValuesFromMapping(reflect.Indirect(reflect.ValueOf(o)), cache.valueMapping)

	if boil.IsDebug(ctx) {
		writer := boil.DebugWriterFrom(ctx)
		fmt.Fprintln(writer, cache.query)
		fmt.Fprintln(writer, values)
	}
	var result sql.Result
	result, err = exec.ExecContext(ctx, cache.query, values...)
	if err != nil {
		return 0, errors.Wrap(err, "parsed_loss_runs: unable to update processed_loss row")
	}

	rowsAff, err := result.RowsAffected()
	if err != nil {
		return 0, errors.Wrap(err, "parsed_loss_runs: failed to get rows affected by update for processed_loss")
	}

	if !cached {
		processedLossUpdateCacheMut.Lock()
		processedLossUpdateCache[key] = cache
		processedLossUpdateCacheMut.Unlock()
	}

	return rowsAff, o.doAfterUpdateHooks(ctx, exec)
}

// UpdateAll updates all rows with the specified column values.
func (q processedLossQuery) UpdateAll(ctx context.Context, exec boil.ContextExecutor, cols M) (int64, error) {
	queries.SetUpdate(q.Query, cols)

	result, err := q.Query.ExecContext(ctx, exec)
	if err != nil {
		return 0, errors.Wrap(err, "parsed_loss_runs: unable to update all for processed_loss")
	}

	rowsAff, err := result.RowsAffected()
	if err != nil {
		return 0, errors.Wrap(err, "parsed_loss_runs: unable to retrieve rows affected for processed_loss")
	}

	return rowsAff, nil
}

// UpdateAll updates all rows with the specified column values, using an executor.
func (o ProcessedLossSlice) UpdateAll(ctx context.Context, exec boil.ContextExecutor, cols M) (int64, error) {
	ln := int64(len(o))
	if ln == 0 {
		return 0, nil
	}

	if len(cols) == 0 {
		return 0, errors.New("parsed_loss_runs: update all requires at least one column argument")
	}

	colNames := make([]string, len(cols))
	args := make([]interface{}, len(cols))

	i := 0
	for name, value := range cols {
		colNames[i] = name
		args[i] = value
		i++
	}

	// Append all of the primary key values for each column
	for _, obj := range o {
		pkeyArgs := queries.ValuesFromMapping(reflect.Indirect(reflect.ValueOf(obj)), processedLossPrimaryKeyMapping)
		args = append(args, pkeyArgs...)
	}

	sql := fmt.Sprintf("UPDATE \"parsed_loss_runs\".\"processed_loss\" SET %s WHERE %s",
		strmangle.SetParamNames("\"", "\"", 1, colNames),
		strmangle.WhereClauseRepeated(string(dialect.LQ), string(dialect.RQ), len(colNames)+1, processedLossPrimaryKeyColumns, len(o)))

	if boil.IsDebug(ctx) {
		writer := boil.DebugWriterFrom(ctx)
		fmt.Fprintln(writer, sql)
		fmt.Fprintln(writer, args...)
	}
	result, err := exec.ExecContext(ctx, sql, args...)
	if err != nil {
		return 0, errors.Wrap(err, "parsed_loss_runs: unable to update all in processedLoss slice")
	}

	rowsAff, err := result.RowsAffected()
	if err != nil {
		return 0, errors.Wrap(err, "parsed_loss_runs: unable to retrieve rows affected all in update all processedLoss")
	}
	return rowsAff, nil
}

// Upsert attempts an insert using an executor, and does an update or ignore on conflict.
// See boil.Columns documentation for how to properly use updateColumns and insertColumns.
func (o *ProcessedLoss) Upsert(ctx context.Context, exec boil.ContextExecutor, updateOnConflict bool, conflictColumns []string, updateColumns, insertColumns boil.Columns) error {
	if o == nil {
		return errors.New("parsed_loss_runs: no processed_loss provided for upsert")
	}
	if !boil.TimestampsAreSkipped(ctx) {
		currTime := time.Now().In(boil.GetLocation())

		if o.CreatedAt.IsZero() {
			o.CreatedAt = currTime
		}
	}

	if err := o.doBeforeUpsertHooks(ctx, exec); err != nil {
		return err
	}

	nzDefaults := queries.NonZeroDefaultSet(processedLossColumnsWithDefault, o)

	// Build cache key in-line uglily - mysql vs psql problems
	buf := strmangle.GetBuffer()
	if updateOnConflict {
		buf.WriteByte('t')
	} else {
		buf.WriteByte('f')
	}
	buf.WriteByte('.')
	for _, c := range conflictColumns {
		buf.WriteString(c)
	}
	buf.WriteByte('.')
	buf.WriteString(strconv.Itoa(updateColumns.Kind))
	for _, c := range updateColumns.Cols {
		buf.WriteString(c)
	}
	buf.WriteByte('.')
	buf.WriteString(strconv.Itoa(insertColumns.Kind))
	for _, c := range insertColumns.Cols {
		buf.WriteString(c)
	}
	buf.WriteByte('.')
	for _, c := range nzDefaults {
		buf.WriteString(c)
	}
	key := buf.String()
	strmangle.PutBuffer(buf)

	processedLossUpsertCacheMut.RLock()
	cache, cached := processedLossUpsertCache[key]
	processedLossUpsertCacheMut.RUnlock()

	var err error

	if !cached {
		insert, ret := insertColumns.InsertColumnSet(
			processedLossAllColumns,
			processedLossColumnsWithDefault,
			processedLossColumnsWithoutDefault,
			nzDefaults,
		)

		update := updateColumns.UpdateColumnSet(
			processedLossAllColumns,
			processedLossPrimaryKeyColumns,
		)

		if updateOnConflict && len(update) == 0 {
			return errors.New("parsed_loss_runs: unable to upsert processed_loss, could not build update column list")
		}

		conflict := conflictColumns
		if len(conflict) == 0 {
			conflict = make([]string, len(processedLossPrimaryKeyColumns))
			copy(conflict, processedLossPrimaryKeyColumns)
		}
		cache.query = buildUpsertQueryPostgres(dialect, "\"parsed_loss_runs\".\"processed_loss\"", updateOnConflict, ret, update, conflict, insert)

		cache.valueMapping, err = queries.BindMapping(processedLossType, processedLossMapping, insert)
		if err != nil {
			return err
		}
		if len(ret) != 0 {
			cache.retMapping, err = queries.BindMapping(processedLossType, processedLossMapping, ret)
			if err != nil {
				return err
			}
		}
	}

	value := reflect.Indirect(reflect.ValueOf(o))
	vals := queries.ValuesFromMapping(value, cache.valueMapping)
	var returns []interface{}
	if len(cache.retMapping) != 0 {
		returns = queries.PtrsFromMapping(value, cache.retMapping)
	}

	if boil.IsDebug(ctx) {
		writer := boil.DebugWriterFrom(ctx)
		fmt.Fprintln(writer, cache.query)
		fmt.Fprintln(writer, vals)
	}
	if len(cache.retMapping) != 0 {
		err = exec.QueryRowContext(ctx, cache.query, vals...).Scan(returns...)
		if errors.Is(err, sql.ErrNoRows) {
			err = nil // Postgres doesn't return anything when there's no update
		}
	} else {
		_, err = exec.ExecContext(ctx, cache.query, vals...)
	}
	if err != nil {
		return errors.Wrap(err, "parsed_loss_runs: unable to upsert processed_loss")
	}

	if !cached {
		processedLossUpsertCacheMut.Lock()
		processedLossUpsertCache[key] = cache
		processedLossUpsertCacheMut.Unlock()
	}

	return o.doAfterUpsertHooks(ctx, exec)
}

// Delete deletes a single ProcessedLoss record with an executor.
// Delete will match against the primary key column to find the record to delete.
func (o *ProcessedLoss) Delete(ctx context.Context, exec boil.ContextExecutor) (int64, error) {
	if o == nil {
		return 0, errors.New("parsed_loss_runs: no ProcessedLoss provided for delete")
	}

	if err := o.doBeforeDeleteHooks(ctx, exec); err != nil {
		return 0, err
	}

	args := queries.ValuesFromMapping(reflect.Indirect(reflect.ValueOf(o)), processedLossPrimaryKeyMapping)
	sql := "DELETE FROM \"parsed_loss_runs\".\"processed_loss\" WHERE \"id\"=$1"

	if boil.IsDebug(ctx) {
		writer := boil.DebugWriterFrom(ctx)
		fmt.Fprintln(writer, sql)
		fmt.Fprintln(writer, args...)
	}
	result, err := exec.ExecContext(ctx, sql, args...)
	if err != nil {
		return 0, errors.Wrap(err, "parsed_loss_runs: unable to delete from processed_loss")
	}

	rowsAff, err := result.RowsAffected()
	if err != nil {
		return 0, errors.Wrap(err, "parsed_loss_runs: failed to get rows affected by delete for processed_loss")
	}

	if err := o.doAfterDeleteHooks(ctx, exec); err != nil {
		return 0, err
	}

	return rowsAff, nil
}

// DeleteAll deletes all matching rows.
func (q processedLossQuery) DeleteAll(ctx context.Context, exec boil.ContextExecutor) (int64, error) {
	if q.Query == nil {
		return 0, errors.New("parsed_loss_runs: no processedLossQuery provided for delete all")
	}

	queries.SetDelete(q.Query)

	result, err := q.Query.ExecContext(ctx, exec)
	if err != nil {
		return 0, errors.Wrap(err, "parsed_loss_runs: unable to delete all from processed_loss")
	}

	rowsAff, err := result.RowsAffected()
	if err != nil {
		return 0, errors.Wrap(err, "parsed_loss_runs: failed to get rows affected by deleteall for processed_loss")
	}

	return rowsAff, nil
}

// DeleteAll deletes all rows in the slice, using an executor.
func (o ProcessedLossSlice) DeleteAll(ctx context.Context, exec boil.ContextExecutor) (int64, error) {
	if len(o) == 0 {
		return 0, nil
	}

	if len(processedLossBeforeDeleteHooks) != 0 {
		for _, obj := range o {
			if err := obj.doBeforeDeleteHooks(ctx, exec); err != nil {
				return 0, err
			}
		}
	}

	var args []interface{}
	for _, obj := range o {
		pkeyArgs := queries.ValuesFromMapping(reflect.Indirect(reflect.ValueOf(obj)), processedLossPrimaryKeyMapping)
		args = append(args, pkeyArgs...)
	}

	sql := "DELETE FROM \"parsed_loss_runs\".\"processed_loss\" WHERE " +
		strmangle.WhereClauseRepeated(string(dialect.LQ), string(dialect.RQ), 1, processedLossPrimaryKeyColumns, len(o))

	if boil.IsDebug(ctx) {
		writer := boil.DebugWriterFrom(ctx)
		fmt.Fprintln(writer, sql)
		fmt.Fprintln(writer, args)
	}
	result, err := exec.ExecContext(ctx, sql, args...)
	if err != nil {
		return 0, errors.Wrap(err, "parsed_loss_runs: unable to delete all from processedLoss slice")
	}

	rowsAff, err := result.RowsAffected()
	if err != nil {
		return 0, errors.Wrap(err, "parsed_loss_runs: failed to get rows affected by deleteall for processed_loss")
	}

	if len(processedLossAfterDeleteHooks) != 0 {
		for _, obj := range o {
			if err := obj.doAfterDeleteHooks(ctx, exec); err != nil {
				return 0, err
			}
		}
	}

	return rowsAff, nil
}

// Reload refetches the object from the database
// using the primary keys with an executor.
func (o *ProcessedLoss) Reload(ctx context.Context, exec boil.ContextExecutor) error {
	ret, err := FindProcessedLoss(ctx, exec, o.ID)
	if err != nil {
		return err
	}

	*o = *ret
	return nil
}

// ReloadAll refetches every row with matching primary key column values
// and overwrites the original object slice with the newly updated slice.
func (o *ProcessedLossSlice) ReloadAll(ctx context.Context, exec boil.ContextExecutor) error {
	if o == nil || len(*o) == 0 {
		return nil
	}

	slice := ProcessedLossSlice{}
	var args []interface{}
	for _, obj := range *o {
		pkeyArgs := queries.ValuesFromMapping(reflect.Indirect(reflect.ValueOf(obj)), processedLossPrimaryKeyMapping)
		args = append(args, pkeyArgs...)
	}

	sql := "SELECT \"parsed_loss_runs\".\"processed_loss\".* FROM \"parsed_loss_runs\".\"processed_loss\" WHERE " +
		strmangle.WhereClauseRepeated(string(dialect.LQ), string(dialect.RQ), 1, processedLossPrimaryKeyColumns, len(*o))

	q := queries.Raw(sql, args...)

	err := q.Bind(ctx, exec, &slice)
	if err != nil {
		return errors.Wrap(err, "parsed_loss_runs: unable to reload all in ProcessedLossSlice")
	}

	*o = slice

	return nil
}

// ProcessedLossExists checks if the ProcessedLoss row exists.
func ProcessedLossExists(ctx context.Context, exec boil.ContextExecutor, iD string) (bool, error) {
	var exists bool
	sql := "select exists(select 1 from \"parsed_loss_runs\".\"processed_loss\" where \"id\"=$1 limit 1)"

	if boil.IsDebug(ctx) {
		writer := boil.DebugWriterFrom(ctx)
		fmt.Fprintln(writer, sql)
		fmt.Fprintln(writer, iD)
	}
	row := exec.QueryRowContext(ctx, sql, iD)

	err := row.Scan(&exists)
	if err != nil {
		return false, errors.Wrap(err, "parsed_loss_runs: unable to check if processed_loss exists")
	}

	return exists, nil
}
