// Code generated by SQLBoiler (https://github.com/volatiletech/sqlboiler). DO NOT EDIT.
// This file is meant to be re-generated in place and/or deleted at any time.

package telematics

import (
	"context"
	"database/sql"
	"fmt"
	"reflect"
	"strconv"
	"strings"
	"sync"
	"time"

	"github.com/friendsofgo/errors"
	"github.com/volatiletech/sqlboiler/v4/boil"
	"github.com/volatiletech/sqlboiler/v4/queries"
	"github.com/volatiletech/sqlboiler/v4/queries/qm"
	"github.com/volatiletech/sqlboiler/v4/queries/qmhelper"
	"github.com/volatiletech/sqlboiler/v4/types"
	"github.com/volatiletech/strmangle"
)

// CMTVehicleScore is an object representing the database table.
type CMTVehicleScore struct {
	HandleID                    string            `boil:"handle_id" json:"handle_id" toml:"handle_id" yaml:"handle_id"`
	Vin                         string            `boil:"vin" json:"vin" toml:"vin" yaml:"vin"`
	ScoreDate                   time.Time         `boil:"score_date" json:"score_date" toml:"score_date" yaml:"score_date"`
	RollingWindow               string            `boil:"rolling_window" json:"rolling_window" toml:"rolling_window" yaml:"rolling_window"`
	TripDistanceKM              float64           `boil:"trip_distance_km" json:"trip_distance_km" toml:"trip_distance_km" yaml:"trip_distance_km"`
	TripDurationSec             float64           `boil:"trip_duration_sec" json:"trip_duration_sec" toml:"trip_duration_sec" yaml:"trip_duration_sec"`
	TripCount                   int               `boil:"trip_count" json:"trip_count" toml:"trip_count" yaml:"trip_count"`
	VehicleScore                int               `boil:"vehicle_score" json:"vehicle_score" toml:"vehicle_score" yaml:"vehicle_score"`
	ScoreEligibility            types.StringArray `boil:"score_eligibility" json:"score_eligibility" toml:"score_eligibility" yaml:"score_eligibility"`
	ScoreAttrExposure           int               `boil:"score_attr_exposure" json:"score_attr_exposure" toml:"score_attr_exposure" yaml:"score_attr_exposure"`
	ScoreAttrBraking            int               `boil:"score_attr_braking" json:"score_attr_braking" toml:"score_attr_braking" yaml:"score_attr_braking"`
	ScoreAttrContextualSpeeding int               `boil:"score_attr_contextual_speeding" json:"score_attr_contextual_speeding" toml:"score_attr_contextual_speeding" yaml:"score_attr_contextual_speeding"`
	ScoreAttrNightFatigue       int               `boil:"score_attr_night_fatigue" json:"score_attr_night_fatigue" toml:"score_attr_night_fatigue" yaml:"score_attr_night_fatigue"`
	ScoreAttrRoadRisk           int               `boil:"score_attr_road_risk" json:"score_attr_road_risk" toml:"score_attr_road_risk" yaml:"score_attr_road_risk"`
	CMTJobID                    string            `boil:"cmt_job_id" json:"cmt_job_id" toml:"cmt_job_id" yaml:"cmt_job_id"`
	JobRunID                    string            `boil:"job_run_id" json:"job_run_id" toml:"job_run_id" yaml:"job_run_id"`
	LastUpdatedAt               time.Time         `boil:"last_updated_at" json:"last_updated_at" toml:"last_updated_at" yaml:"last_updated_at"`

	R *cmtVehicleScoreR `boil:"" json:"" toml:"" yaml:""`
	L cmtVehicleScoreL  `boil:"-" json:"-" toml:"-" yaml:"-"`
}

var CMTVehicleScoreColumns = struct {
	HandleID                    string
	Vin                         string
	ScoreDate                   string
	RollingWindow               string
	TripDistanceKM              string
	TripDurationSec             string
	TripCount                   string
	VehicleScore                string
	ScoreEligibility            string
	ScoreAttrExposure           string
	ScoreAttrBraking            string
	ScoreAttrContextualSpeeding string
	ScoreAttrNightFatigue       string
	ScoreAttrRoadRisk           string
	CMTJobID                    string
	JobRunID                    string
	LastUpdatedAt               string
}{
	HandleID:                    "handle_id",
	Vin:                         "vin",
	ScoreDate:                   "score_date",
	RollingWindow:               "rolling_window",
	TripDistanceKM:              "trip_distance_km",
	TripDurationSec:             "trip_duration_sec",
	TripCount:                   "trip_count",
	VehicleScore:                "vehicle_score",
	ScoreEligibility:            "score_eligibility",
	ScoreAttrExposure:           "score_attr_exposure",
	ScoreAttrBraking:            "score_attr_braking",
	ScoreAttrContextualSpeeding: "score_attr_contextual_speeding",
	ScoreAttrNightFatigue:       "score_attr_night_fatigue",
	ScoreAttrRoadRisk:           "score_attr_road_risk",
	CMTJobID:                    "cmt_job_id",
	JobRunID:                    "job_run_id",
	LastUpdatedAt:               "last_updated_at",
}

var CMTVehicleScoreTableColumns = struct {
	HandleID                    string
	Vin                         string
	ScoreDate                   string
	RollingWindow               string
	TripDistanceKM              string
	TripDurationSec             string
	TripCount                   string
	VehicleScore                string
	ScoreEligibility            string
	ScoreAttrExposure           string
	ScoreAttrBraking            string
	ScoreAttrContextualSpeeding string
	ScoreAttrNightFatigue       string
	ScoreAttrRoadRisk           string
	CMTJobID                    string
	JobRunID                    string
	LastUpdatedAt               string
}{
	HandleID:                    "cmt_vehicle_scores.handle_id",
	Vin:                         "cmt_vehicle_scores.vin",
	ScoreDate:                   "cmt_vehicle_scores.score_date",
	RollingWindow:               "cmt_vehicle_scores.rolling_window",
	TripDistanceKM:              "cmt_vehicle_scores.trip_distance_km",
	TripDurationSec:             "cmt_vehicle_scores.trip_duration_sec",
	TripCount:                   "cmt_vehicle_scores.trip_count",
	VehicleScore:                "cmt_vehicle_scores.vehicle_score",
	ScoreEligibility:            "cmt_vehicle_scores.score_eligibility",
	ScoreAttrExposure:           "cmt_vehicle_scores.score_attr_exposure",
	ScoreAttrBraking:            "cmt_vehicle_scores.score_attr_braking",
	ScoreAttrContextualSpeeding: "cmt_vehicle_scores.score_attr_contextual_speeding",
	ScoreAttrNightFatigue:       "cmt_vehicle_scores.score_attr_night_fatigue",
	ScoreAttrRoadRisk:           "cmt_vehicle_scores.score_attr_road_risk",
	CMTJobID:                    "cmt_vehicle_scores.cmt_job_id",
	JobRunID:                    "cmt_vehicle_scores.job_run_id",
	LastUpdatedAt:               "cmt_vehicle_scores.last_updated_at",
}

// Generated where

var CMTVehicleScoreWhere = struct {
	HandleID                    whereHelperstring
	Vin                         whereHelperstring
	ScoreDate                   whereHelpertime_Time
	RollingWindow               whereHelperstring
	TripDistanceKM              whereHelperfloat64
	TripDurationSec             whereHelperfloat64
	TripCount                   whereHelperint
	VehicleScore                whereHelperint
	ScoreEligibility            whereHelpertypes_StringArray
	ScoreAttrExposure           whereHelperint
	ScoreAttrBraking            whereHelperint
	ScoreAttrContextualSpeeding whereHelperint
	ScoreAttrNightFatigue       whereHelperint
	ScoreAttrRoadRisk           whereHelperint
	CMTJobID                    whereHelperstring
	JobRunID                    whereHelperstring
	LastUpdatedAt               whereHelpertime_Time
}{
	HandleID:                    whereHelperstring{field: "\"telematics\".\"cmt_vehicle_scores\".\"handle_id\""},
	Vin:                         whereHelperstring{field: "\"telematics\".\"cmt_vehicle_scores\".\"vin\""},
	ScoreDate:                   whereHelpertime_Time{field: "\"telematics\".\"cmt_vehicle_scores\".\"score_date\""},
	RollingWindow:               whereHelperstring{field: "\"telematics\".\"cmt_vehicle_scores\".\"rolling_window\""},
	TripDistanceKM:              whereHelperfloat64{field: "\"telematics\".\"cmt_vehicle_scores\".\"trip_distance_km\""},
	TripDurationSec:             whereHelperfloat64{field: "\"telematics\".\"cmt_vehicle_scores\".\"trip_duration_sec\""},
	TripCount:                   whereHelperint{field: "\"telematics\".\"cmt_vehicle_scores\".\"trip_count\""},
	VehicleScore:                whereHelperint{field: "\"telematics\".\"cmt_vehicle_scores\".\"vehicle_score\""},
	ScoreEligibility:            whereHelpertypes_StringArray{field: "\"telematics\".\"cmt_vehicle_scores\".\"score_eligibility\""},
	ScoreAttrExposure:           whereHelperint{field: "\"telematics\".\"cmt_vehicle_scores\".\"score_attr_exposure\""},
	ScoreAttrBraking:            whereHelperint{field: "\"telematics\".\"cmt_vehicle_scores\".\"score_attr_braking\""},
	ScoreAttrContextualSpeeding: whereHelperint{field: "\"telematics\".\"cmt_vehicle_scores\".\"score_attr_contextual_speeding\""},
	ScoreAttrNightFatigue:       whereHelperint{field: "\"telematics\".\"cmt_vehicle_scores\".\"score_attr_night_fatigue\""},
	ScoreAttrRoadRisk:           whereHelperint{field: "\"telematics\".\"cmt_vehicle_scores\".\"score_attr_road_risk\""},
	CMTJobID:                    whereHelperstring{field: "\"telematics\".\"cmt_vehicle_scores\".\"cmt_job_id\""},
	JobRunID:                    whereHelperstring{field: "\"telematics\".\"cmt_vehicle_scores\".\"job_run_id\""},
	LastUpdatedAt:               whereHelpertime_Time{field: "\"telematics\".\"cmt_vehicle_scores\".\"last_updated_at\""},
}

// CMTVehicleScoreRels is where relationship names are stored.
var CMTVehicleScoreRels = struct {
}{}

// cmtVehicleScoreR is where relationships are stored.
type cmtVehicleScoreR struct {
}

// NewStruct creates a new relationship struct
func (*cmtVehicleScoreR) NewStruct() *cmtVehicleScoreR {
	return &cmtVehicleScoreR{}
}

// cmtVehicleScoreL is where Load methods for each relationship are stored.
type cmtVehicleScoreL struct{}

var (
	cmtVehicleScoreAllColumns            = []string{"handle_id", "vin", "score_date", "rolling_window", "trip_distance_km", "trip_duration_sec", "trip_count", "vehicle_score", "score_eligibility", "score_attr_exposure", "score_attr_braking", "score_attr_contextual_speeding", "score_attr_night_fatigue", "score_attr_road_risk", "cmt_job_id", "job_run_id", "last_updated_at"}
	cmtVehicleScoreColumnsWithoutDefault = []string{"handle_id", "vin", "score_date", "rolling_window", "trip_distance_km", "trip_duration_sec", "trip_count", "vehicle_score", "score_eligibility", "cmt_job_id", "job_run_id", "last_updated_at"}
	cmtVehicleScoreColumnsWithDefault    = []string{"score_attr_exposure", "score_attr_braking", "score_attr_contextual_speeding", "score_attr_night_fatigue", "score_attr_road_risk"}
	cmtVehicleScorePrimaryKeyColumns     = []string{"handle_id", "vin", "score_date"}
	cmtVehicleScoreGeneratedColumns      = []string{}
)

type (
	// CMTVehicleScoreSlice is an alias for a slice of pointers to CMTVehicleScore.
	// This should almost always be used instead of []CMTVehicleScore.
	CMTVehicleScoreSlice []*CMTVehicleScore
	// CMTVehicleScoreHook is the signature for custom CMTVehicleScore hook methods
	CMTVehicleScoreHook func(context.Context, boil.ContextExecutor, *CMTVehicleScore) error

	cmtVehicleScoreQuery struct {
		*queries.Query
	}
)

// Cache for insert, update and upsert
var (
	cmtVehicleScoreType                 = reflect.TypeOf(&CMTVehicleScore{})
	cmtVehicleScoreMapping              = queries.MakeStructMapping(cmtVehicleScoreType)
	cmtVehicleScorePrimaryKeyMapping, _ = queries.BindMapping(cmtVehicleScoreType, cmtVehicleScoreMapping, cmtVehicleScorePrimaryKeyColumns)
	cmtVehicleScoreInsertCacheMut       sync.RWMutex
	cmtVehicleScoreInsertCache          = make(map[string]insertCache)
	cmtVehicleScoreUpdateCacheMut       sync.RWMutex
	cmtVehicleScoreUpdateCache          = make(map[string]updateCache)
	cmtVehicleScoreUpsertCacheMut       sync.RWMutex
	cmtVehicleScoreUpsertCache          = make(map[string]insertCache)
)

var (
	// Force time package dependency for automated UpdatedAt/CreatedAt.
	_ = time.Second
	// Force qmhelper dependency for where clause generation (which doesn't
	// always happen)
	_ = qmhelper.Where
)

var cmtVehicleScoreAfterSelectHooks []CMTVehicleScoreHook

var cmtVehicleScoreBeforeInsertHooks []CMTVehicleScoreHook
var cmtVehicleScoreAfterInsertHooks []CMTVehicleScoreHook

var cmtVehicleScoreBeforeUpdateHooks []CMTVehicleScoreHook
var cmtVehicleScoreAfterUpdateHooks []CMTVehicleScoreHook

var cmtVehicleScoreBeforeDeleteHooks []CMTVehicleScoreHook
var cmtVehicleScoreAfterDeleteHooks []CMTVehicleScoreHook

var cmtVehicleScoreBeforeUpsertHooks []CMTVehicleScoreHook
var cmtVehicleScoreAfterUpsertHooks []CMTVehicleScoreHook

// doAfterSelectHooks executes all "after Select" hooks.
func (o *CMTVehicleScore) doAfterSelectHooks(ctx context.Context, exec boil.ContextExecutor) (err error) {
	if boil.HooksAreSkipped(ctx) {
		return nil
	}

	for _, hook := range cmtVehicleScoreAfterSelectHooks {
		if err := hook(ctx, exec, o); err != nil {
			return err
		}
	}

	return nil
}

// doBeforeInsertHooks executes all "before insert" hooks.
func (o *CMTVehicleScore) doBeforeInsertHooks(ctx context.Context, exec boil.ContextExecutor) (err error) {
	if boil.HooksAreSkipped(ctx) {
		return nil
	}

	for _, hook := range cmtVehicleScoreBeforeInsertHooks {
		if err := hook(ctx, exec, o); err != nil {
			return err
		}
	}

	return nil
}

// doAfterInsertHooks executes all "after Insert" hooks.
func (o *CMTVehicleScore) doAfterInsertHooks(ctx context.Context, exec boil.ContextExecutor) (err error) {
	if boil.HooksAreSkipped(ctx) {
		return nil
	}

	for _, hook := range cmtVehicleScoreAfterInsertHooks {
		if err := hook(ctx, exec, o); err != nil {
			return err
		}
	}

	return nil
}

// doBeforeUpdateHooks executes all "before Update" hooks.
func (o *CMTVehicleScore) doBeforeUpdateHooks(ctx context.Context, exec boil.ContextExecutor) (err error) {
	if boil.HooksAreSkipped(ctx) {
		return nil
	}

	for _, hook := range cmtVehicleScoreBeforeUpdateHooks {
		if err := hook(ctx, exec, o); err != nil {
			return err
		}
	}

	return nil
}

// doAfterUpdateHooks executes all "after Update" hooks.
func (o *CMTVehicleScore) doAfterUpdateHooks(ctx context.Context, exec boil.ContextExecutor) (err error) {
	if boil.HooksAreSkipped(ctx) {
		return nil
	}

	for _, hook := range cmtVehicleScoreAfterUpdateHooks {
		if err := hook(ctx, exec, o); err != nil {
			return err
		}
	}

	return nil
}

// doBeforeDeleteHooks executes all "before Delete" hooks.
func (o *CMTVehicleScore) doBeforeDeleteHooks(ctx context.Context, exec boil.ContextExecutor) (err error) {
	if boil.HooksAreSkipped(ctx) {
		return nil
	}

	for _, hook := range cmtVehicleScoreBeforeDeleteHooks {
		if err := hook(ctx, exec, o); err != nil {
			return err
		}
	}

	return nil
}

// doAfterDeleteHooks executes all "after Delete" hooks.
func (o *CMTVehicleScore) doAfterDeleteHooks(ctx context.Context, exec boil.ContextExecutor) (err error) {
	if boil.HooksAreSkipped(ctx) {
		return nil
	}

	for _, hook := range cmtVehicleScoreAfterDeleteHooks {
		if err := hook(ctx, exec, o); err != nil {
			return err
		}
	}

	return nil
}

// doBeforeUpsertHooks executes all "before Upsert" hooks.
func (o *CMTVehicleScore) doBeforeUpsertHooks(ctx context.Context, exec boil.ContextExecutor) (err error) {
	if boil.HooksAreSkipped(ctx) {
		return nil
	}

	for _, hook := range cmtVehicleScoreBeforeUpsertHooks {
		if err := hook(ctx, exec, o); err != nil {
			return err
		}
	}

	return nil
}

// doAfterUpsertHooks executes all "after Upsert" hooks.
func (o *CMTVehicleScore) doAfterUpsertHooks(ctx context.Context, exec boil.ContextExecutor) (err error) {
	if boil.HooksAreSkipped(ctx) {
		return nil
	}

	for _, hook := range cmtVehicleScoreAfterUpsertHooks {
		if err := hook(ctx, exec, o); err != nil {
			return err
		}
	}

	return nil
}

// AddCMTVehicleScoreHook registers your hook function for all future operations.
func AddCMTVehicleScoreHook(hookPoint boil.HookPoint, cmtVehicleScoreHook CMTVehicleScoreHook) {
	switch hookPoint {
	case boil.AfterSelectHook:
		cmtVehicleScoreAfterSelectHooks = append(cmtVehicleScoreAfterSelectHooks, cmtVehicleScoreHook)
	case boil.BeforeInsertHook:
		cmtVehicleScoreBeforeInsertHooks = append(cmtVehicleScoreBeforeInsertHooks, cmtVehicleScoreHook)
	case boil.AfterInsertHook:
		cmtVehicleScoreAfterInsertHooks = append(cmtVehicleScoreAfterInsertHooks, cmtVehicleScoreHook)
	case boil.BeforeUpdateHook:
		cmtVehicleScoreBeforeUpdateHooks = append(cmtVehicleScoreBeforeUpdateHooks, cmtVehicleScoreHook)
	case boil.AfterUpdateHook:
		cmtVehicleScoreAfterUpdateHooks = append(cmtVehicleScoreAfterUpdateHooks, cmtVehicleScoreHook)
	case boil.BeforeDeleteHook:
		cmtVehicleScoreBeforeDeleteHooks = append(cmtVehicleScoreBeforeDeleteHooks, cmtVehicleScoreHook)
	case boil.AfterDeleteHook:
		cmtVehicleScoreAfterDeleteHooks = append(cmtVehicleScoreAfterDeleteHooks, cmtVehicleScoreHook)
	case boil.BeforeUpsertHook:
		cmtVehicleScoreBeforeUpsertHooks = append(cmtVehicleScoreBeforeUpsertHooks, cmtVehicleScoreHook)
	case boil.AfterUpsertHook:
		cmtVehicleScoreAfterUpsertHooks = append(cmtVehicleScoreAfterUpsertHooks, cmtVehicleScoreHook)
	}
}

// One returns a single cmtVehicleScore record from the query.
func (q cmtVehicleScoreQuery) One(ctx context.Context, exec boil.ContextExecutor) (*CMTVehicleScore, error) {
	o := &CMTVehicleScore{}

	queries.SetLimit(q.Query, 1)

	err := q.Bind(ctx, exec, o)
	if err != nil {
		if errors.Is(err, sql.ErrNoRows) {
			return nil, sql.ErrNoRows
		}
		return nil, errors.Wrap(err, "telematics: failed to execute a one query for cmt_vehicle_scores")
	}

	if err := o.doAfterSelectHooks(ctx, exec); err != nil {
		return o, err
	}

	return o, nil
}

// All returns all CMTVehicleScore records from the query.
func (q cmtVehicleScoreQuery) All(ctx context.Context, exec boil.ContextExecutor) (CMTVehicleScoreSlice, error) {
	var o []*CMTVehicleScore

	err := q.Bind(ctx, exec, &o)
	if err != nil {
		return nil, errors.Wrap(err, "telematics: failed to assign all query results to CMTVehicleScore slice")
	}

	if len(cmtVehicleScoreAfterSelectHooks) != 0 {
		for _, obj := range o {
			if err := obj.doAfterSelectHooks(ctx, exec); err != nil {
				return o, err
			}
		}
	}

	return o, nil
}

// Count returns the count of all CMTVehicleScore records in the query.
func (q cmtVehicleScoreQuery) Count(ctx context.Context, exec boil.ContextExecutor) (int64, error) {
	var count int64

	queries.SetSelect(q.Query, nil)
	queries.SetCount(q.Query)

	err := q.Query.QueryRowContext(ctx, exec).Scan(&count)
	if err != nil {
		return 0, errors.Wrap(err, "telematics: failed to count cmt_vehicle_scores rows")
	}

	return count, nil
}

// Exists checks if the row exists in the table.
func (q cmtVehicleScoreQuery) Exists(ctx context.Context, exec boil.ContextExecutor) (bool, error) {
	var count int64

	queries.SetSelect(q.Query, nil)
	queries.SetCount(q.Query)
	queries.SetLimit(q.Query, 1)

	err := q.Query.QueryRowContext(ctx, exec).Scan(&count)
	if err != nil {
		return false, errors.Wrap(err, "telematics: failed to check if cmt_vehicle_scores exists")
	}

	return count > 0, nil
}

// CMTVehicleScores retrieves all the records using an executor.
func CMTVehicleScores(mods ...qm.QueryMod) cmtVehicleScoreQuery {
	mods = append(mods, qm.From("\"telematics\".\"cmt_vehicle_scores\""))
	q := NewQuery(mods...)
	if len(queries.GetSelect(q)) == 0 {
		queries.SetSelect(q, []string{"\"telematics\".\"cmt_vehicle_scores\".*"})
	}

	return cmtVehicleScoreQuery{q}
}

// FindCMTVehicleScore retrieves a single record by ID with an executor.
// If selectCols is empty Find will return all columns.
func FindCMTVehicleScore(ctx context.Context, exec boil.ContextExecutor, handleID string, vin string, scoreDate time.Time, selectCols ...string) (*CMTVehicleScore, error) {
	cmtVehicleScoreObj := &CMTVehicleScore{}

	sel := "*"
	if len(selectCols) > 0 {
		sel = strings.Join(strmangle.IdentQuoteSlice(dialect.LQ, dialect.RQ, selectCols), ",")
	}
	query := fmt.Sprintf(
		"select %s from \"telematics\".\"cmt_vehicle_scores\" where \"handle_id\"=$1 AND \"vin\"=$2 AND \"score_date\"=$3", sel,
	)

	q := queries.Raw(query, handleID, vin, scoreDate)

	err := q.Bind(ctx, exec, cmtVehicleScoreObj)
	if err != nil {
		if errors.Is(err, sql.ErrNoRows) {
			return nil, sql.ErrNoRows
		}
		return nil, errors.Wrap(err, "telematics: unable to select from cmt_vehicle_scores")
	}

	if err = cmtVehicleScoreObj.doAfterSelectHooks(ctx, exec); err != nil {
		return cmtVehicleScoreObj, err
	}

	return cmtVehicleScoreObj, nil
}

// Insert a single record using an executor.
// See boil.Columns.InsertColumnSet documentation to understand column list inference for inserts.
func (o *CMTVehicleScore) Insert(ctx context.Context, exec boil.ContextExecutor, columns boil.Columns) error {
	if o == nil {
		return errors.New("telematics: no cmt_vehicle_scores provided for insertion")
	}

	var err error

	if err := o.doBeforeInsertHooks(ctx, exec); err != nil {
		return err
	}

	nzDefaults := queries.NonZeroDefaultSet(cmtVehicleScoreColumnsWithDefault, o)

	key := makeCacheKey(columns, nzDefaults)
	cmtVehicleScoreInsertCacheMut.RLock()
	cache, cached := cmtVehicleScoreInsertCache[key]
	cmtVehicleScoreInsertCacheMut.RUnlock()

	if !cached {
		wl, returnColumns := columns.InsertColumnSet(
			cmtVehicleScoreAllColumns,
			cmtVehicleScoreColumnsWithDefault,
			cmtVehicleScoreColumnsWithoutDefault,
			nzDefaults,
		)

		cache.valueMapping, err = queries.BindMapping(cmtVehicleScoreType, cmtVehicleScoreMapping, wl)
		if err != nil {
			return err
		}
		cache.retMapping, err = queries.BindMapping(cmtVehicleScoreType, cmtVehicleScoreMapping, returnColumns)
		if err != nil {
			return err
		}
		if len(wl) != 0 {
			cache.query = fmt.Sprintf("INSERT INTO \"telematics\".\"cmt_vehicle_scores\" (\"%s\") %%sVALUES (%s)%%s", strings.Join(wl, "\",\""), strmangle.Placeholders(dialect.UseIndexPlaceholders, len(wl), 1, 1))
		} else {
			cache.query = "INSERT INTO \"telematics\".\"cmt_vehicle_scores\" %sDEFAULT VALUES%s"
		}

		var queryOutput, queryReturning string

		if len(cache.retMapping) != 0 {
			queryReturning = fmt.Sprintf(" RETURNING \"%s\"", strings.Join(returnColumns, "\",\""))
		}

		cache.query = fmt.Sprintf(cache.query, queryOutput, queryReturning)
	}

	value := reflect.Indirect(reflect.ValueOf(o))
	vals := queries.ValuesFromMapping(value, cache.valueMapping)

	if boil.IsDebug(ctx) {
		writer := boil.DebugWriterFrom(ctx)
		fmt.Fprintln(writer, cache.query)
		fmt.Fprintln(writer, vals)
	}

	if len(cache.retMapping) != 0 {
		err = exec.QueryRowContext(ctx, cache.query, vals...).Scan(queries.PtrsFromMapping(value, cache.retMapping)...)
	} else {
		_, err = exec.ExecContext(ctx, cache.query, vals...)
	}

	if err != nil {
		return errors.Wrap(err, "telematics: unable to insert into cmt_vehicle_scores")
	}

	if !cached {
		cmtVehicleScoreInsertCacheMut.Lock()
		cmtVehicleScoreInsertCache[key] = cache
		cmtVehicleScoreInsertCacheMut.Unlock()
	}

	return o.doAfterInsertHooks(ctx, exec)
}

// Update uses an executor to update the CMTVehicleScore.
// See boil.Columns.UpdateColumnSet documentation to understand column list inference for updates.
// Update does not automatically update the record in case of default values. Use .Reload() to refresh the records.
func (o *CMTVehicleScore) Update(ctx context.Context, exec boil.ContextExecutor, columns boil.Columns) (int64, error) {
	var err error
	if err = o.doBeforeUpdateHooks(ctx, exec); err != nil {
		return 0, err
	}
	key := makeCacheKey(columns, nil)
	cmtVehicleScoreUpdateCacheMut.RLock()
	cache, cached := cmtVehicleScoreUpdateCache[key]
	cmtVehicleScoreUpdateCacheMut.RUnlock()

	if !cached {
		wl := columns.UpdateColumnSet(
			cmtVehicleScoreAllColumns,
			cmtVehicleScorePrimaryKeyColumns,
		)

		if !columns.IsWhitelist() {
			wl = strmangle.SetComplement(wl, []string{"created_at"})
		}
		if len(wl) == 0 {
			return 0, errors.New("telematics: unable to update cmt_vehicle_scores, could not build whitelist")
		}

		cache.query = fmt.Sprintf("UPDATE \"telematics\".\"cmt_vehicle_scores\" SET %s WHERE %s",
			strmangle.SetParamNames("\"", "\"", 1, wl),
			strmangle.WhereClause("\"", "\"", len(wl)+1, cmtVehicleScorePrimaryKeyColumns),
		)
		cache.valueMapping, err = queries.BindMapping(cmtVehicleScoreType, cmtVehicleScoreMapping, append(wl, cmtVehicleScorePrimaryKeyColumns...))
		if err != nil {
			return 0, err
		}
	}

	values := queries.ValuesFromMapping(reflect.Indirect(reflect.ValueOf(o)), cache.valueMapping)

	if boil.IsDebug(ctx) {
		writer := boil.DebugWriterFrom(ctx)
		fmt.Fprintln(writer, cache.query)
		fmt.Fprintln(writer, values)
	}
	var result sql.Result
	result, err = exec.ExecContext(ctx, cache.query, values...)
	if err != nil {
		return 0, errors.Wrap(err, "telematics: unable to update cmt_vehicle_scores row")
	}

	rowsAff, err := result.RowsAffected()
	if err != nil {
		return 0, errors.Wrap(err, "telematics: failed to get rows affected by update for cmt_vehicle_scores")
	}

	if !cached {
		cmtVehicleScoreUpdateCacheMut.Lock()
		cmtVehicleScoreUpdateCache[key] = cache
		cmtVehicleScoreUpdateCacheMut.Unlock()
	}

	return rowsAff, o.doAfterUpdateHooks(ctx, exec)
}

// UpdateAll updates all rows with the specified column values.
func (q cmtVehicleScoreQuery) UpdateAll(ctx context.Context, exec boil.ContextExecutor, cols M) (int64, error) {
	queries.SetUpdate(q.Query, cols)

	result, err := q.Query.ExecContext(ctx, exec)
	if err != nil {
		return 0, errors.Wrap(err, "telematics: unable to update all for cmt_vehicle_scores")
	}

	rowsAff, err := result.RowsAffected()
	if err != nil {
		return 0, errors.Wrap(err, "telematics: unable to retrieve rows affected for cmt_vehicle_scores")
	}

	return rowsAff, nil
}

// UpdateAll updates all rows with the specified column values, using an executor.
func (o CMTVehicleScoreSlice) UpdateAll(ctx context.Context, exec boil.ContextExecutor, cols M) (int64, error) {
	ln := int64(len(o))
	if ln == 0 {
		return 0, nil
	}

	if len(cols) == 0 {
		return 0, errors.New("telematics: update all requires at least one column argument")
	}

	colNames := make([]string, len(cols))
	args := make([]interface{}, len(cols))

	i := 0
	for name, value := range cols {
		colNames[i] = name
		args[i] = value
		i++
	}

	// Append all of the primary key values for each column
	for _, obj := range o {
		pkeyArgs := queries.ValuesFromMapping(reflect.Indirect(reflect.ValueOf(obj)), cmtVehicleScorePrimaryKeyMapping)
		args = append(args, pkeyArgs...)
	}

	sql := fmt.Sprintf("UPDATE \"telematics\".\"cmt_vehicle_scores\" SET %s WHERE %s",
		strmangle.SetParamNames("\"", "\"", 1, colNames),
		strmangle.WhereClauseRepeated(string(dialect.LQ), string(dialect.RQ), len(colNames)+1, cmtVehicleScorePrimaryKeyColumns, len(o)))

	if boil.IsDebug(ctx) {
		writer := boil.DebugWriterFrom(ctx)
		fmt.Fprintln(writer, sql)
		fmt.Fprintln(writer, args...)
	}
	result, err := exec.ExecContext(ctx, sql, args...)
	if err != nil {
		return 0, errors.Wrap(err, "telematics: unable to update all in cmtVehicleScore slice")
	}

	rowsAff, err := result.RowsAffected()
	if err != nil {
		return 0, errors.Wrap(err, "telematics: unable to retrieve rows affected all in update all cmtVehicleScore")
	}
	return rowsAff, nil
}

// Upsert attempts an insert using an executor, and does an update or ignore on conflict.
// See boil.Columns documentation for how to properly use updateColumns and insertColumns.
func (o *CMTVehicleScore) Upsert(ctx context.Context, exec boil.ContextExecutor, updateOnConflict bool, conflictColumns []string, updateColumns, insertColumns boil.Columns) error {
	if o == nil {
		return errors.New("telematics: no cmt_vehicle_scores provided for upsert")
	}

	if err := o.doBeforeUpsertHooks(ctx, exec); err != nil {
		return err
	}

	nzDefaults := queries.NonZeroDefaultSet(cmtVehicleScoreColumnsWithDefault, o)

	// Build cache key in-line uglily - mysql vs psql problems
	buf := strmangle.GetBuffer()
	if updateOnConflict {
		buf.WriteByte('t')
	} else {
		buf.WriteByte('f')
	}
	buf.WriteByte('.')
	for _, c := range conflictColumns {
		buf.WriteString(c)
	}
	buf.WriteByte('.')
	buf.WriteString(strconv.Itoa(updateColumns.Kind))
	for _, c := range updateColumns.Cols {
		buf.WriteString(c)
	}
	buf.WriteByte('.')
	buf.WriteString(strconv.Itoa(insertColumns.Kind))
	for _, c := range insertColumns.Cols {
		buf.WriteString(c)
	}
	buf.WriteByte('.')
	for _, c := range nzDefaults {
		buf.WriteString(c)
	}
	key := buf.String()
	strmangle.PutBuffer(buf)

	cmtVehicleScoreUpsertCacheMut.RLock()
	cache, cached := cmtVehicleScoreUpsertCache[key]
	cmtVehicleScoreUpsertCacheMut.RUnlock()

	var err error

	if !cached {
		insert, ret := insertColumns.InsertColumnSet(
			cmtVehicleScoreAllColumns,
			cmtVehicleScoreColumnsWithDefault,
			cmtVehicleScoreColumnsWithoutDefault,
			nzDefaults,
		)

		update := updateColumns.UpdateColumnSet(
			cmtVehicleScoreAllColumns,
			cmtVehicleScorePrimaryKeyColumns,
		)

		if updateOnConflict && len(update) == 0 {
			return errors.New("telematics: unable to upsert cmt_vehicle_scores, could not build update column list")
		}

		conflict := conflictColumns
		if len(conflict) == 0 {
			conflict = make([]string, len(cmtVehicleScorePrimaryKeyColumns))
			copy(conflict, cmtVehicleScorePrimaryKeyColumns)
		}
		cache.query = buildUpsertQueryPostgres(dialect, "\"telematics\".\"cmt_vehicle_scores\"", updateOnConflict, ret, update, conflict, insert)

		cache.valueMapping, err = queries.BindMapping(cmtVehicleScoreType, cmtVehicleScoreMapping, insert)
		if err != nil {
			return err
		}
		if len(ret) != 0 {
			cache.retMapping, err = queries.BindMapping(cmtVehicleScoreType, cmtVehicleScoreMapping, ret)
			if err != nil {
				return err
			}
		}
	}

	value := reflect.Indirect(reflect.ValueOf(o))
	vals := queries.ValuesFromMapping(value, cache.valueMapping)
	var returns []interface{}
	if len(cache.retMapping) != 0 {
		returns = queries.PtrsFromMapping(value, cache.retMapping)
	}

	if boil.IsDebug(ctx) {
		writer := boil.DebugWriterFrom(ctx)
		fmt.Fprintln(writer, cache.query)
		fmt.Fprintln(writer, vals)
	}
	if len(cache.retMapping) != 0 {
		err = exec.QueryRowContext(ctx, cache.query, vals...).Scan(returns...)
		if errors.Is(err, sql.ErrNoRows) {
			err = nil // Postgres doesn't return anything when there's no update
		}
	} else {
		_, err = exec.ExecContext(ctx, cache.query, vals...)
	}
	if err != nil {
		return errors.Wrap(err, "telematics: unable to upsert cmt_vehicle_scores")
	}

	if !cached {
		cmtVehicleScoreUpsertCacheMut.Lock()
		cmtVehicleScoreUpsertCache[key] = cache
		cmtVehicleScoreUpsertCacheMut.Unlock()
	}

	return o.doAfterUpsertHooks(ctx, exec)
}

// Delete deletes a single CMTVehicleScore record with an executor.
// Delete will match against the primary key column to find the record to delete.
func (o *CMTVehicleScore) Delete(ctx context.Context, exec boil.ContextExecutor) (int64, error) {
	if o == nil {
		return 0, errors.New("telematics: no CMTVehicleScore provided for delete")
	}

	if err := o.doBeforeDeleteHooks(ctx, exec); err != nil {
		return 0, err
	}

	args := queries.ValuesFromMapping(reflect.Indirect(reflect.ValueOf(o)), cmtVehicleScorePrimaryKeyMapping)
	sql := "DELETE FROM \"telematics\".\"cmt_vehicle_scores\" WHERE \"handle_id\"=$1 AND \"vin\"=$2 AND \"score_date\"=$3"

	if boil.IsDebug(ctx) {
		writer := boil.DebugWriterFrom(ctx)
		fmt.Fprintln(writer, sql)
		fmt.Fprintln(writer, args...)
	}
	result, err := exec.ExecContext(ctx, sql, args...)
	if err != nil {
		return 0, errors.Wrap(err, "telematics: unable to delete from cmt_vehicle_scores")
	}

	rowsAff, err := result.RowsAffected()
	if err != nil {
		return 0, errors.Wrap(err, "telematics: failed to get rows affected by delete for cmt_vehicle_scores")
	}

	if err := o.doAfterDeleteHooks(ctx, exec); err != nil {
		return 0, err
	}

	return rowsAff, nil
}

// DeleteAll deletes all matching rows.
func (q cmtVehicleScoreQuery) DeleteAll(ctx context.Context, exec boil.ContextExecutor) (int64, error) {
	if q.Query == nil {
		return 0, errors.New("telematics: no cmtVehicleScoreQuery provided for delete all")
	}

	queries.SetDelete(q.Query)

	result, err := q.Query.ExecContext(ctx, exec)
	if err != nil {
		return 0, errors.Wrap(err, "telematics: unable to delete all from cmt_vehicle_scores")
	}

	rowsAff, err := result.RowsAffected()
	if err != nil {
		return 0, errors.Wrap(err, "telematics: failed to get rows affected by deleteall for cmt_vehicle_scores")
	}

	return rowsAff, nil
}

// DeleteAll deletes all rows in the slice, using an executor.
func (o CMTVehicleScoreSlice) DeleteAll(ctx context.Context, exec boil.ContextExecutor) (int64, error) {
	if len(o) == 0 {
		return 0, nil
	}

	if len(cmtVehicleScoreBeforeDeleteHooks) != 0 {
		for _, obj := range o {
			if err := obj.doBeforeDeleteHooks(ctx, exec); err != nil {
				return 0, err
			}
		}
	}

	var args []interface{}
	for _, obj := range o {
		pkeyArgs := queries.ValuesFromMapping(reflect.Indirect(reflect.ValueOf(obj)), cmtVehicleScorePrimaryKeyMapping)
		args = append(args, pkeyArgs...)
	}

	sql := "DELETE FROM \"telematics\".\"cmt_vehicle_scores\" WHERE " +
		strmangle.WhereClauseRepeated(string(dialect.LQ), string(dialect.RQ), 1, cmtVehicleScorePrimaryKeyColumns, len(o))

	if boil.IsDebug(ctx) {
		writer := boil.DebugWriterFrom(ctx)
		fmt.Fprintln(writer, sql)
		fmt.Fprintln(writer, args)
	}
	result, err := exec.ExecContext(ctx, sql, args...)
	if err != nil {
		return 0, errors.Wrap(err, "telematics: unable to delete all from cmtVehicleScore slice")
	}

	rowsAff, err := result.RowsAffected()
	if err != nil {
		return 0, errors.Wrap(err, "telematics: failed to get rows affected by deleteall for cmt_vehicle_scores")
	}

	if len(cmtVehicleScoreAfterDeleteHooks) != 0 {
		for _, obj := range o {
			if err := obj.doAfterDeleteHooks(ctx, exec); err != nil {
				return 0, err
			}
		}
	}

	return rowsAff, nil
}

// Reload refetches the object from the database
// using the primary keys with an executor.
func (o *CMTVehicleScore) Reload(ctx context.Context, exec boil.ContextExecutor) error {
	ret, err := FindCMTVehicleScore(ctx, exec, o.HandleID, o.Vin, o.ScoreDate)
	if err != nil {
		return err
	}

	*o = *ret
	return nil
}

// ReloadAll refetches every row with matching primary key column values
// and overwrites the original object slice with the newly updated slice.
func (o *CMTVehicleScoreSlice) ReloadAll(ctx context.Context, exec boil.ContextExecutor) error {
	if o == nil || len(*o) == 0 {
		return nil
	}

	slice := CMTVehicleScoreSlice{}
	var args []interface{}
	for _, obj := range *o {
		pkeyArgs := queries.ValuesFromMapping(reflect.Indirect(reflect.ValueOf(obj)), cmtVehicleScorePrimaryKeyMapping)
		args = append(args, pkeyArgs...)
	}

	sql := "SELECT \"telematics\".\"cmt_vehicle_scores\".* FROM \"telematics\".\"cmt_vehicle_scores\" WHERE " +
		strmangle.WhereClauseRepeated(string(dialect.LQ), string(dialect.RQ), 1, cmtVehicleScorePrimaryKeyColumns, len(*o))

	q := queries.Raw(sql, args...)

	err := q.Bind(ctx, exec, &slice)
	if err != nil {
		return errors.Wrap(err, "telematics: unable to reload all in CMTVehicleScoreSlice")
	}

	*o = slice

	return nil
}

// CMTVehicleScoreExists checks if the CMTVehicleScore row exists.
func CMTVehicleScoreExists(ctx context.Context, exec boil.ContextExecutor, handleID string, vin string, scoreDate time.Time) (bool, error) {
	var exists bool
	sql := "select exists(select 1 from \"telematics\".\"cmt_vehicle_scores\" where \"handle_id\"=$1 AND \"vin\"=$2 AND \"score_date\"=$3 limit 1)"

	if boil.IsDebug(ctx) {
		writer := boil.DebugWriterFrom(ctx)
		fmt.Fprintln(writer, sql)
		fmt.Fprintln(writer, handleID, vin, scoreDate)
	}
	row := exec.QueryRowContext(ctx, sql, handleID, vin, scoreDate)

	err := row.Scan(&exists)
	if err != nil {
		return false, errors.Wrap(err, "telematics: unable to check if cmt_vehicle_scores exists")
	}

	return exists, nil
}
