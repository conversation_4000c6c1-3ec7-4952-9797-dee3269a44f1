// Code generated by SQLBoiler (https://github.com/volatiletech/sqlboiler). DO NOT EDIT.
// This file is meant to be re-generated in place and/or deleted at any time.

package account

import (
	"context"
	"database/sql"
	"fmt"
	"reflect"
	"strconv"
	"strings"
	"sync"
	"time"

	"github.com/friendsofgo/errors"
	"github.com/volatiletech/null/v8"
	"github.com/volatiletech/sqlboiler/v4/boil"
	"github.com/volatiletech/sqlboiler/v4/queries"
	"github.com/volatiletech/sqlboiler/v4/queries/qm"
	"github.com/volatiletech/sqlboiler/v4/queries/qmhelper"
	"github.com/volatiletech/sqlboiler/v4/types"
	"github.com/volatiletech/strmangle"
)

// Contact is an object representing the database table.
type Contact struct {
	ContactID string      `boil:"contact_id" json:"contact_id" toml:"contact_id" yaml:"contact_id"`
	AccountID string      `boil:"account_id" json:"account_id" toml:"account_id" yaml:"account_id"`
	FirstName null.String `boil:"first_name" json:"first_name,omitempty" toml:"first_name" yaml:"first_name,omitempty"`
	LastName  null.String `boil:"last_name" json:"last_name,omitempty" toml:"last_name" yaml:"last_name,omitempty"`
	Title     null.String `boil:"title" json:"title,omitempty" toml:"title" yaml:"title,omitempty"`
	Phone     null.String `boil:"phone" json:"phone,omitempty" toml:"phone" yaml:"phone,omitempty"`
	Email     null.String `boil:"email" json:"email,omitempty" toml:"email" yaml:"email,omitempty"`
	Role      string      `boil:"role" json:"role" toml:"role" yaml:"role"`
	Metadata  types.JSON  `boil:"metadata" json:"metadata" toml:"metadata" yaml:"metadata"`
	CreatedAt time.Time   `boil:"created_at" json:"created_at" toml:"created_at" yaml:"created_at"`
	UpdatedAt time.Time   `boil:"updated_at" json:"updated_at" toml:"updated_at" yaml:"updated_at"`

	R *contactR `boil:"" json:"" toml:"" yaml:""`
	L contactL  `boil:"-" json:"-" toml:"-" yaml:"-"`
}

var ContactColumns = struct {
	ContactID string
	AccountID string
	FirstName string
	LastName  string
	Title     string
	Phone     string
	Email     string
	Role      string
	Metadata  string
	CreatedAt string
	UpdatedAt string
}{
	ContactID: "contact_id",
	AccountID: "account_id",
	FirstName: "first_name",
	LastName:  "last_name",
	Title:     "title",
	Phone:     "phone",
	Email:     "email",
	Role:      "role",
	Metadata:  "metadata",
	CreatedAt: "created_at",
	UpdatedAt: "updated_at",
}

var ContactTableColumns = struct {
	ContactID string
	AccountID string
	FirstName string
	LastName  string
	Title     string
	Phone     string
	Email     string
	Role      string
	Metadata  string
	CreatedAt string
	UpdatedAt string
}{
	ContactID: "contacts.contact_id",
	AccountID: "contacts.account_id",
	FirstName: "contacts.first_name",
	LastName:  "contacts.last_name",
	Title:     "contacts.title",
	Phone:     "contacts.phone",
	Email:     "contacts.email",
	Role:      "contacts.role",
	Metadata:  "contacts.metadata",
	CreatedAt: "contacts.created_at",
	UpdatedAt: "contacts.updated_at",
}

// Generated where

var ContactWhere = struct {
	ContactID whereHelperstring
	AccountID whereHelperstring
	FirstName whereHelpernull_String
	LastName  whereHelpernull_String
	Title     whereHelpernull_String
	Phone     whereHelpernull_String
	Email     whereHelpernull_String
	Role      whereHelperstring
	Metadata  whereHelpertypes_JSON
	CreatedAt whereHelpertime_Time
	UpdatedAt whereHelpertime_Time
}{
	ContactID: whereHelperstring{field: "\"account\".\"contacts\".\"contact_id\""},
	AccountID: whereHelperstring{field: "\"account\".\"contacts\".\"account_id\""},
	FirstName: whereHelpernull_String{field: "\"account\".\"contacts\".\"first_name\""},
	LastName:  whereHelpernull_String{field: "\"account\".\"contacts\".\"last_name\""},
	Title:     whereHelpernull_String{field: "\"account\".\"contacts\".\"title\""},
	Phone:     whereHelpernull_String{field: "\"account\".\"contacts\".\"phone\""},
	Email:     whereHelpernull_String{field: "\"account\".\"contacts\".\"email\""},
	Role:      whereHelperstring{field: "\"account\".\"contacts\".\"role\""},
	Metadata:  whereHelpertypes_JSON{field: "\"account\".\"contacts\".\"metadata\""},
	CreatedAt: whereHelpertime_Time{field: "\"account\".\"contacts\".\"created_at\""},
	UpdatedAt: whereHelpertime_Time{field: "\"account\".\"contacts\".\"updated_at\""},
}

// ContactRels is where relationship names are stored.
var ContactRels = struct {
	Account string
}{
	Account: "Account",
}

// contactR is where relationships are stored.
type contactR struct {
	Account *Account `boil:"Account" json:"Account" toml:"Account" yaml:"Account"`
}

// NewStruct creates a new relationship struct
func (*contactR) NewStruct() *contactR {
	return &contactR{}
}

// contactL is where Load methods for each relationship are stored.
type contactL struct{}

var (
	contactAllColumns            = []string{"contact_id", "account_id", "first_name", "last_name", "title", "phone", "email", "role", "metadata", "created_at", "updated_at"}
	contactColumnsWithoutDefault = []string{"contact_id", "account_id"}
	contactColumnsWithDefault    = []string{"first_name", "last_name", "title", "phone", "email", "role", "metadata", "created_at", "updated_at"}
	contactPrimaryKeyColumns     = []string{"contact_id"}
	contactGeneratedColumns      = []string{}
)

type (
	// ContactSlice is an alias for a slice of pointers to Contact.
	// This should almost always be used instead of []Contact.
	ContactSlice []*Contact
	// ContactHook is the signature for custom Contact hook methods
	ContactHook func(context.Context, boil.ContextExecutor, *Contact) error

	contactQuery struct {
		*queries.Query
	}
)

// Cache for insert, update and upsert
var (
	contactType                 = reflect.TypeOf(&Contact{})
	contactMapping              = queries.MakeStructMapping(contactType)
	contactPrimaryKeyMapping, _ = queries.BindMapping(contactType, contactMapping, contactPrimaryKeyColumns)
	contactInsertCacheMut       sync.RWMutex
	contactInsertCache          = make(map[string]insertCache)
	contactUpdateCacheMut       sync.RWMutex
	contactUpdateCache          = make(map[string]updateCache)
	contactUpsertCacheMut       sync.RWMutex
	contactUpsertCache          = make(map[string]insertCache)
)

var (
	// Force time package dependency for automated UpdatedAt/CreatedAt.
	_ = time.Second
	// Force qmhelper dependency for where clause generation (which doesn't
	// always happen)
	_ = qmhelper.Where
)

var contactAfterSelectHooks []ContactHook

var contactBeforeInsertHooks []ContactHook
var contactAfterInsertHooks []ContactHook

var contactBeforeUpdateHooks []ContactHook
var contactAfterUpdateHooks []ContactHook

var contactBeforeDeleteHooks []ContactHook
var contactAfterDeleteHooks []ContactHook

var contactBeforeUpsertHooks []ContactHook
var contactAfterUpsertHooks []ContactHook

// doAfterSelectHooks executes all "after Select" hooks.
func (o *Contact) doAfterSelectHooks(ctx context.Context, exec boil.ContextExecutor) (err error) {
	if boil.HooksAreSkipped(ctx) {
		return nil
	}

	for _, hook := range contactAfterSelectHooks {
		if err := hook(ctx, exec, o); err != nil {
			return err
		}
	}

	return nil
}

// doBeforeInsertHooks executes all "before insert" hooks.
func (o *Contact) doBeforeInsertHooks(ctx context.Context, exec boil.ContextExecutor) (err error) {
	if boil.HooksAreSkipped(ctx) {
		return nil
	}

	for _, hook := range contactBeforeInsertHooks {
		if err := hook(ctx, exec, o); err != nil {
			return err
		}
	}

	return nil
}

// doAfterInsertHooks executes all "after Insert" hooks.
func (o *Contact) doAfterInsertHooks(ctx context.Context, exec boil.ContextExecutor) (err error) {
	if boil.HooksAreSkipped(ctx) {
		return nil
	}

	for _, hook := range contactAfterInsertHooks {
		if err := hook(ctx, exec, o); err != nil {
			return err
		}
	}

	return nil
}

// doBeforeUpdateHooks executes all "before Update" hooks.
func (o *Contact) doBeforeUpdateHooks(ctx context.Context, exec boil.ContextExecutor) (err error) {
	if boil.HooksAreSkipped(ctx) {
		return nil
	}

	for _, hook := range contactBeforeUpdateHooks {
		if err := hook(ctx, exec, o); err != nil {
			return err
		}
	}

	return nil
}

// doAfterUpdateHooks executes all "after Update" hooks.
func (o *Contact) doAfterUpdateHooks(ctx context.Context, exec boil.ContextExecutor) (err error) {
	if boil.HooksAreSkipped(ctx) {
		return nil
	}

	for _, hook := range contactAfterUpdateHooks {
		if err := hook(ctx, exec, o); err != nil {
			return err
		}
	}

	return nil
}

// doBeforeDeleteHooks executes all "before Delete" hooks.
func (o *Contact) doBeforeDeleteHooks(ctx context.Context, exec boil.ContextExecutor) (err error) {
	if boil.HooksAreSkipped(ctx) {
		return nil
	}

	for _, hook := range contactBeforeDeleteHooks {
		if err := hook(ctx, exec, o); err != nil {
			return err
		}
	}

	return nil
}

// doAfterDeleteHooks executes all "after Delete" hooks.
func (o *Contact) doAfterDeleteHooks(ctx context.Context, exec boil.ContextExecutor) (err error) {
	if boil.HooksAreSkipped(ctx) {
		return nil
	}

	for _, hook := range contactAfterDeleteHooks {
		if err := hook(ctx, exec, o); err != nil {
			return err
		}
	}

	return nil
}

// doBeforeUpsertHooks executes all "before Upsert" hooks.
func (o *Contact) doBeforeUpsertHooks(ctx context.Context, exec boil.ContextExecutor) (err error) {
	if boil.HooksAreSkipped(ctx) {
		return nil
	}

	for _, hook := range contactBeforeUpsertHooks {
		if err := hook(ctx, exec, o); err != nil {
			return err
		}
	}

	return nil
}

// doAfterUpsertHooks executes all "after Upsert" hooks.
func (o *Contact) doAfterUpsertHooks(ctx context.Context, exec boil.ContextExecutor) (err error) {
	if boil.HooksAreSkipped(ctx) {
		return nil
	}

	for _, hook := range contactAfterUpsertHooks {
		if err := hook(ctx, exec, o); err != nil {
			return err
		}
	}

	return nil
}

// AddContactHook registers your hook function for all future operations.
func AddContactHook(hookPoint boil.HookPoint, contactHook ContactHook) {
	switch hookPoint {
	case boil.AfterSelectHook:
		contactAfterSelectHooks = append(contactAfterSelectHooks, contactHook)
	case boil.BeforeInsertHook:
		contactBeforeInsertHooks = append(contactBeforeInsertHooks, contactHook)
	case boil.AfterInsertHook:
		contactAfterInsertHooks = append(contactAfterInsertHooks, contactHook)
	case boil.BeforeUpdateHook:
		contactBeforeUpdateHooks = append(contactBeforeUpdateHooks, contactHook)
	case boil.AfterUpdateHook:
		contactAfterUpdateHooks = append(contactAfterUpdateHooks, contactHook)
	case boil.BeforeDeleteHook:
		contactBeforeDeleteHooks = append(contactBeforeDeleteHooks, contactHook)
	case boil.AfterDeleteHook:
		contactAfterDeleteHooks = append(contactAfterDeleteHooks, contactHook)
	case boil.BeforeUpsertHook:
		contactBeforeUpsertHooks = append(contactBeforeUpsertHooks, contactHook)
	case boil.AfterUpsertHook:
		contactAfterUpsertHooks = append(contactAfterUpsertHooks, contactHook)
	}
}

// One returns a single contact record from the query.
func (q contactQuery) One(ctx context.Context, exec boil.ContextExecutor) (*Contact, error) {
	o := &Contact{}

	queries.SetLimit(q.Query, 1)

	err := q.Bind(ctx, exec, o)
	if err != nil {
		if errors.Is(err, sql.ErrNoRows) {
			return nil, sql.ErrNoRows
		}
		return nil, errors.Wrap(err, "account: failed to execute a one query for contacts")
	}

	if err := o.doAfterSelectHooks(ctx, exec); err != nil {
		return o, err
	}

	return o, nil
}

// All returns all Contact records from the query.
func (q contactQuery) All(ctx context.Context, exec boil.ContextExecutor) (ContactSlice, error) {
	var o []*Contact

	err := q.Bind(ctx, exec, &o)
	if err != nil {
		return nil, errors.Wrap(err, "account: failed to assign all query results to Contact slice")
	}

	if len(contactAfterSelectHooks) != 0 {
		for _, obj := range o {
			if err := obj.doAfterSelectHooks(ctx, exec); err != nil {
				return o, err
			}
		}
	}

	return o, nil
}

// Count returns the count of all Contact records in the query.
func (q contactQuery) Count(ctx context.Context, exec boil.ContextExecutor) (int64, error) {
	var count int64

	queries.SetSelect(q.Query, nil)
	queries.SetCount(q.Query)

	err := q.Query.QueryRowContext(ctx, exec).Scan(&count)
	if err != nil {
		return 0, errors.Wrap(err, "account: failed to count contacts rows")
	}

	return count, nil
}

// Exists checks if the row exists in the table.
func (q contactQuery) Exists(ctx context.Context, exec boil.ContextExecutor) (bool, error) {
	var count int64

	queries.SetSelect(q.Query, nil)
	queries.SetCount(q.Query)
	queries.SetLimit(q.Query, 1)

	err := q.Query.QueryRowContext(ctx, exec).Scan(&count)
	if err != nil {
		return false, errors.Wrap(err, "account: failed to check if contacts exists")
	}

	return count > 0, nil
}

// Account pointed to by the foreign key.
func (o *Contact) Account(mods ...qm.QueryMod) accountQuery {
	queryMods := []qm.QueryMod{
		qm.Where("\"account_id\" = ?", o.AccountID),
	}

	queryMods = append(queryMods, mods...)

	return Accounts(queryMods...)
}

// LoadAccount allows an eager lookup of values, cached into the
// loaded structs of the objects. This is for an N-1 relationship.
func (contactL) LoadAccount(ctx context.Context, e boil.ContextExecutor, singular bool, maybeContact interface{}, mods queries.Applicator) error {
	var slice []*Contact
	var object *Contact

	if singular {
		object = maybeContact.(*Contact)
	} else {
		slice = *maybeContact.(*[]*Contact)
	}

	args := make([]interface{}, 0, 1)
	if singular {
		if object.R == nil {
			object.R = &contactR{}
		}
		args = append(args, object.AccountID)

	} else {
	Outer:
		for _, obj := range slice {
			if obj.R == nil {
				obj.R = &contactR{}
			}

			for _, a := range args {
				if a == obj.AccountID {
					continue Outer
				}
			}

			args = append(args, obj.AccountID)

		}
	}

	if len(args) == 0 {
		return nil
	}

	query := NewQuery(
		qm.From(`account.accounts`),
		qm.WhereIn(`account.accounts.account_id in ?`, args...),
	)
	if mods != nil {
		mods.Apply(query)
	}

	results, err := query.QueryContext(ctx, e)
	if err != nil {
		return errors.Wrap(err, "failed to eager load Account")
	}

	var resultSlice []*Account
	if err = queries.Bind(results, &resultSlice); err != nil {
		return errors.Wrap(err, "failed to bind eager loaded slice Account")
	}

	if err = results.Close(); err != nil {
		return errors.Wrap(err, "failed to close results of eager load for accounts")
	}
	if err = results.Err(); err != nil {
		return errors.Wrap(err, "error occurred during iteration of eager loaded relations for accounts")
	}

	if len(contactAfterSelectHooks) != 0 {
		for _, obj := range resultSlice {
			if err := obj.doAfterSelectHooks(ctx, e); err != nil {
				return err
			}
		}
	}

	if len(resultSlice) == 0 {
		return nil
	}

	if singular {
		foreign := resultSlice[0]
		object.R.Account = foreign
		if foreign.R == nil {
			foreign.R = &accountR{}
		}
		foreign.R.Contacts = append(foreign.R.Contacts, object)
		return nil
	}

	for _, local := range slice {
		for _, foreign := range resultSlice {
			if local.AccountID == foreign.AccountID {
				local.R.Account = foreign
				if foreign.R == nil {
					foreign.R = &accountR{}
				}
				foreign.R.Contacts = append(foreign.R.Contacts, local)
				break
			}
		}
	}

	return nil
}

// SetAccount of the contact to the related item.
// Sets o.R.Account to related.
// Adds o to related.R.Contacts.
func (o *Contact) SetAccount(ctx context.Context, exec boil.ContextExecutor, insert bool, related *Account) error {
	var err error
	if insert {
		if err = related.Insert(ctx, exec, boil.Infer()); err != nil {
			return errors.Wrap(err, "failed to insert into foreign table")
		}
	}

	updateQuery := fmt.Sprintf(
		"UPDATE \"account\".\"contacts\" SET %s WHERE %s",
		strmangle.SetParamNames("\"", "\"", 1, []string{"account_id"}),
		strmangle.WhereClause("\"", "\"", 2, contactPrimaryKeyColumns),
	)
	values := []interface{}{related.AccountID, o.ContactID}

	if boil.IsDebug(ctx) {
		writer := boil.DebugWriterFrom(ctx)
		fmt.Fprintln(writer, updateQuery)
		fmt.Fprintln(writer, values)
	}
	if _, err = exec.ExecContext(ctx, updateQuery, values...); err != nil {
		return errors.Wrap(err, "failed to update local table")
	}

	o.AccountID = related.AccountID
	if o.R == nil {
		o.R = &contactR{
			Account: related,
		}
	} else {
		o.R.Account = related
	}

	if related.R == nil {
		related.R = &accountR{
			Contacts: ContactSlice{o},
		}
	} else {
		related.R.Contacts = append(related.R.Contacts, o)
	}

	return nil
}

// Contacts retrieves all the records using an executor.
func Contacts(mods ...qm.QueryMod) contactQuery {
	mods = append(mods, qm.From("\"account\".\"contacts\""))
	q := NewQuery(mods...)
	if len(queries.GetSelect(q)) == 0 {
		queries.SetSelect(q, []string{"\"account\".\"contacts\".*"})
	}

	return contactQuery{q}
}

// FindContact retrieves a single record by ID with an executor.
// If selectCols is empty Find will return all columns.
func FindContact(ctx context.Context, exec boil.ContextExecutor, contactID string, selectCols ...string) (*Contact, error) {
	contactObj := &Contact{}

	sel := "*"
	if len(selectCols) > 0 {
		sel = strings.Join(strmangle.IdentQuoteSlice(dialect.LQ, dialect.RQ, selectCols), ",")
	}
	query := fmt.Sprintf(
		"select %s from \"account\".\"contacts\" where \"contact_id\"=$1", sel,
	)

	q := queries.Raw(query, contactID)

	err := q.Bind(ctx, exec, contactObj)
	if err != nil {
		if errors.Is(err, sql.ErrNoRows) {
			return nil, sql.ErrNoRows
		}
		return nil, errors.Wrap(err, "account: unable to select from contacts")
	}

	if err = contactObj.doAfterSelectHooks(ctx, exec); err != nil {
		return contactObj, err
	}

	return contactObj, nil
}

// Insert a single record using an executor.
// See boil.Columns.InsertColumnSet documentation to understand column list inference for inserts.
func (o *Contact) Insert(ctx context.Context, exec boil.ContextExecutor, columns boil.Columns) error {
	if o == nil {
		return errors.New("account: no contacts provided for insertion")
	}

	var err error
	if !boil.TimestampsAreSkipped(ctx) {
		currTime := time.Now().In(boil.GetLocation())

		if o.CreatedAt.IsZero() {
			o.CreatedAt = currTime
		}
		if o.UpdatedAt.IsZero() {
			o.UpdatedAt = currTime
		}
	}

	if err := o.doBeforeInsertHooks(ctx, exec); err != nil {
		return err
	}

	nzDefaults := queries.NonZeroDefaultSet(contactColumnsWithDefault, o)

	key := makeCacheKey(columns, nzDefaults)
	contactInsertCacheMut.RLock()
	cache, cached := contactInsertCache[key]
	contactInsertCacheMut.RUnlock()

	if !cached {
		wl, returnColumns := columns.InsertColumnSet(
			contactAllColumns,
			contactColumnsWithDefault,
			contactColumnsWithoutDefault,
			nzDefaults,
		)

		cache.valueMapping, err = queries.BindMapping(contactType, contactMapping, wl)
		if err != nil {
			return err
		}
		cache.retMapping, err = queries.BindMapping(contactType, contactMapping, returnColumns)
		if err != nil {
			return err
		}
		if len(wl) != 0 {
			cache.query = fmt.Sprintf("INSERT INTO \"account\".\"contacts\" (\"%s\") %%sVALUES (%s)%%s", strings.Join(wl, "\",\""), strmangle.Placeholders(dialect.UseIndexPlaceholders, len(wl), 1, 1))
		} else {
			cache.query = "INSERT INTO \"account\".\"contacts\" %sDEFAULT VALUES%s"
		}

		var queryOutput, queryReturning string

		if len(cache.retMapping) != 0 {
			queryReturning = fmt.Sprintf(" RETURNING \"%s\"", strings.Join(returnColumns, "\",\""))
		}

		cache.query = fmt.Sprintf(cache.query, queryOutput, queryReturning)
	}

	value := reflect.Indirect(reflect.ValueOf(o))
	vals := queries.ValuesFromMapping(value, cache.valueMapping)

	if boil.IsDebug(ctx) {
		writer := boil.DebugWriterFrom(ctx)
		fmt.Fprintln(writer, cache.query)
		fmt.Fprintln(writer, vals)
	}

	if len(cache.retMapping) != 0 {
		err = exec.QueryRowContext(ctx, cache.query, vals...).Scan(queries.PtrsFromMapping(value, cache.retMapping)...)
	} else {
		_, err = exec.ExecContext(ctx, cache.query, vals...)
	}

	if err != nil {
		return errors.Wrap(err, "account: unable to insert into contacts")
	}

	if !cached {
		contactInsertCacheMut.Lock()
		contactInsertCache[key] = cache
		contactInsertCacheMut.Unlock()
	}

	return o.doAfterInsertHooks(ctx, exec)
}

// Update uses an executor to update the Contact.
// See boil.Columns.UpdateColumnSet documentation to understand column list inference for updates.
// Update does not automatically update the record in case of default values. Use .Reload() to refresh the records.
func (o *Contact) Update(ctx context.Context, exec boil.ContextExecutor, columns boil.Columns) (int64, error) {
	if !boil.TimestampsAreSkipped(ctx) {
		currTime := time.Now().In(boil.GetLocation())

		o.UpdatedAt = currTime
	}

	var err error
	if err = o.doBeforeUpdateHooks(ctx, exec); err != nil {
		return 0, err
	}
	key := makeCacheKey(columns, nil)
	contactUpdateCacheMut.RLock()
	cache, cached := contactUpdateCache[key]
	contactUpdateCacheMut.RUnlock()

	if !cached {
		wl := columns.UpdateColumnSet(
			contactAllColumns,
			contactPrimaryKeyColumns,
		)

		if !columns.IsWhitelist() {
			wl = strmangle.SetComplement(wl, []string{"created_at"})
		}
		if len(wl) == 0 {
			return 0, errors.New("account: unable to update contacts, could not build whitelist")
		}

		cache.query = fmt.Sprintf("UPDATE \"account\".\"contacts\" SET %s WHERE %s",
			strmangle.SetParamNames("\"", "\"", 1, wl),
			strmangle.WhereClause("\"", "\"", len(wl)+1, contactPrimaryKeyColumns),
		)
		cache.valueMapping, err = queries.BindMapping(contactType, contactMapping, append(wl, contactPrimaryKeyColumns...))
		if err != nil {
			return 0, err
		}
	}

	values := queries.ValuesFromMapping(reflect.Indirect(reflect.ValueOf(o)), cache.valueMapping)

	if boil.IsDebug(ctx) {
		writer := boil.DebugWriterFrom(ctx)
		fmt.Fprintln(writer, cache.query)
		fmt.Fprintln(writer, values)
	}
	var result sql.Result
	result, err = exec.ExecContext(ctx, cache.query, values...)
	if err != nil {
		return 0, errors.Wrap(err, "account: unable to update contacts row")
	}

	rowsAff, err := result.RowsAffected()
	if err != nil {
		return 0, errors.Wrap(err, "account: failed to get rows affected by update for contacts")
	}

	if !cached {
		contactUpdateCacheMut.Lock()
		contactUpdateCache[key] = cache
		contactUpdateCacheMut.Unlock()
	}

	return rowsAff, o.doAfterUpdateHooks(ctx, exec)
}

// UpdateAll updates all rows with the specified column values.
func (q contactQuery) UpdateAll(ctx context.Context, exec boil.ContextExecutor, cols M) (int64, error) {
	queries.SetUpdate(q.Query, cols)

	result, err := q.Query.ExecContext(ctx, exec)
	if err != nil {
		return 0, errors.Wrap(err, "account: unable to update all for contacts")
	}

	rowsAff, err := result.RowsAffected()
	if err != nil {
		return 0, errors.Wrap(err, "account: unable to retrieve rows affected for contacts")
	}

	return rowsAff, nil
}

// UpdateAll updates all rows with the specified column values, using an executor.
func (o ContactSlice) UpdateAll(ctx context.Context, exec boil.ContextExecutor, cols M) (int64, error) {
	ln := int64(len(o))
	if ln == 0 {
		return 0, nil
	}

	if len(cols) == 0 {
		return 0, errors.New("account: update all requires at least one column argument")
	}

	colNames := make([]string, len(cols))
	args := make([]interface{}, len(cols))

	i := 0
	for name, value := range cols {
		colNames[i] = name
		args[i] = value
		i++
	}

	// Append all of the primary key values for each column
	for _, obj := range o {
		pkeyArgs := queries.ValuesFromMapping(reflect.Indirect(reflect.ValueOf(obj)), contactPrimaryKeyMapping)
		args = append(args, pkeyArgs...)
	}

	sql := fmt.Sprintf("UPDATE \"account\".\"contacts\" SET %s WHERE %s",
		strmangle.SetParamNames("\"", "\"", 1, colNames),
		strmangle.WhereClauseRepeated(string(dialect.LQ), string(dialect.RQ), len(colNames)+1, contactPrimaryKeyColumns, len(o)))

	if boil.IsDebug(ctx) {
		writer := boil.DebugWriterFrom(ctx)
		fmt.Fprintln(writer, sql)
		fmt.Fprintln(writer, args...)
	}
	result, err := exec.ExecContext(ctx, sql, args...)
	if err != nil {
		return 0, errors.Wrap(err, "account: unable to update all in contact slice")
	}

	rowsAff, err := result.RowsAffected()
	if err != nil {
		return 0, errors.Wrap(err, "account: unable to retrieve rows affected all in update all contact")
	}
	return rowsAff, nil
}

// Upsert attempts an insert using an executor, and does an update or ignore on conflict.
// See boil.Columns documentation for how to properly use updateColumns and insertColumns.
func (o *Contact) Upsert(ctx context.Context, exec boil.ContextExecutor, updateOnConflict bool, conflictColumns []string, updateColumns, insertColumns boil.Columns) error {
	if o == nil {
		return errors.New("account: no contacts provided for upsert")
	}
	if !boil.TimestampsAreSkipped(ctx) {
		currTime := time.Now().In(boil.GetLocation())

		if o.CreatedAt.IsZero() {
			o.CreatedAt = currTime
		}
		o.UpdatedAt = currTime
	}

	if err := o.doBeforeUpsertHooks(ctx, exec); err != nil {
		return err
	}

	nzDefaults := queries.NonZeroDefaultSet(contactColumnsWithDefault, o)

	// Build cache key in-line uglily - mysql vs psql problems
	buf := strmangle.GetBuffer()
	if updateOnConflict {
		buf.WriteByte('t')
	} else {
		buf.WriteByte('f')
	}
	buf.WriteByte('.')
	for _, c := range conflictColumns {
		buf.WriteString(c)
	}
	buf.WriteByte('.')
	buf.WriteString(strconv.Itoa(updateColumns.Kind))
	for _, c := range updateColumns.Cols {
		buf.WriteString(c)
	}
	buf.WriteByte('.')
	buf.WriteString(strconv.Itoa(insertColumns.Kind))
	for _, c := range insertColumns.Cols {
		buf.WriteString(c)
	}
	buf.WriteByte('.')
	for _, c := range nzDefaults {
		buf.WriteString(c)
	}
	key := buf.String()
	strmangle.PutBuffer(buf)

	contactUpsertCacheMut.RLock()
	cache, cached := contactUpsertCache[key]
	contactUpsertCacheMut.RUnlock()

	var err error

	if !cached {
		insert, ret := insertColumns.InsertColumnSet(
			contactAllColumns,
			contactColumnsWithDefault,
			contactColumnsWithoutDefault,
			nzDefaults,
		)

		update := updateColumns.UpdateColumnSet(
			contactAllColumns,
			contactPrimaryKeyColumns,
		)

		if updateOnConflict && len(update) == 0 {
			return errors.New("account: unable to upsert contacts, could not build update column list")
		}

		conflict := conflictColumns
		if len(conflict) == 0 {
			conflict = make([]string, len(contactPrimaryKeyColumns))
			copy(conflict, contactPrimaryKeyColumns)
		}
		cache.query = buildUpsertQueryPostgres(dialect, "\"account\".\"contacts\"", updateOnConflict, ret, update, conflict, insert)

		cache.valueMapping, err = queries.BindMapping(contactType, contactMapping, insert)
		if err != nil {
			return err
		}
		if len(ret) != 0 {
			cache.retMapping, err = queries.BindMapping(contactType, contactMapping, ret)
			if err != nil {
				return err
			}
		}
	}

	value := reflect.Indirect(reflect.ValueOf(o))
	vals := queries.ValuesFromMapping(value, cache.valueMapping)
	var returns []interface{}
	if len(cache.retMapping) != 0 {
		returns = queries.PtrsFromMapping(value, cache.retMapping)
	}

	if boil.IsDebug(ctx) {
		writer := boil.DebugWriterFrom(ctx)
		fmt.Fprintln(writer, cache.query)
		fmt.Fprintln(writer, vals)
	}
	if len(cache.retMapping) != 0 {
		err = exec.QueryRowContext(ctx, cache.query, vals...).Scan(returns...)
		if errors.Is(err, sql.ErrNoRows) {
			err = nil // Postgres doesn't return anything when there's no update
		}
	} else {
		_, err = exec.ExecContext(ctx, cache.query, vals...)
	}
	if err != nil {
		return errors.Wrap(err, "account: unable to upsert contacts")
	}

	if !cached {
		contactUpsertCacheMut.Lock()
		contactUpsertCache[key] = cache
		contactUpsertCacheMut.Unlock()
	}

	return o.doAfterUpsertHooks(ctx, exec)
}

// Delete deletes a single Contact record with an executor.
// Delete will match against the primary key column to find the record to delete.
func (o *Contact) Delete(ctx context.Context, exec boil.ContextExecutor) (int64, error) {
	if o == nil {
		return 0, errors.New("account: no Contact provided for delete")
	}

	if err := o.doBeforeDeleteHooks(ctx, exec); err != nil {
		return 0, err
	}

	args := queries.ValuesFromMapping(reflect.Indirect(reflect.ValueOf(o)), contactPrimaryKeyMapping)
	sql := "DELETE FROM \"account\".\"contacts\" WHERE \"contact_id\"=$1"

	if boil.IsDebug(ctx) {
		writer := boil.DebugWriterFrom(ctx)
		fmt.Fprintln(writer, sql)
		fmt.Fprintln(writer, args...)
	}
	result, err := exec.ExecContext(ctx, sql, args...)
	if err != nil {
		return 0, errors.Wrap(err, "account: unable to delete from contacts")
	}

	rowsAff, err := result.RowsAffected()
	if err != nil {
		return 0, errors.Wrap(err, "account: failed to get rows affected by delete for contacts")
	}

	if err := o.doAfterDeleteHooks(ctx, exec); err != nil {
		return 0, err
	}

	return rowsAff, nil
}

// DeleteAll deletes all matching rows.
func (q contactQuery) DeleteAll(ctx context.Context, exec boil.ContextExecutor) (int64, error) {
	if q.Query == nil {
		return 0, errors.New("account: no contactQuery provided for delete all")
	}

	queries.SetDelete(q.Query)

	result, err := q.Query.ExecContext(ctx, exec)
	if err != nil {
		return 0, errors.Wrap(err, "account: unable to delete all from contacts")
	}

	rowsAff, err := result.RowsAffected()
	if err != nil {
		return 0, errors.Wrap(err, "account: failed to get rows affected by deleteall for contacts")
	}

	return rowsAff, nil
}

// DeleteAll deletes all rows in the slice, using an executor.
func (o ContactSlice) DeleteAll(ctx context.Context, exec boil.ContextExecutor) (int64, error) {
	if len(o) == 0 {
		return 0, nil
	}

	if len(contactBeforeDeleteHooks) != 0 {
		for _, obj := range o {
			if err := obj.doBeforeDeleteHooks(ctx, exec); err != nil {
				return 0, err
			}
		}
	}

	var args []interface{}
	for _, obj := range o {
		pkeyArgs := queries.ValuesFromMapping(reflect.Indirect(reflect.ValueOf(obj)), contactPrimaryKeyMapping)
		args = append(args, pkeyArgs...)
	}

	sql := "DELETE FROM \"account\".\"contacts\" WHERE " +
		strmangle.WhereClauseRepeated(string(dialect.LQ), string(dialect.RQ), 1, contactPrimaryKeyColumns, len(o))

	if boil.IsDebug(ctx) {
		writer := boil.DebugWriterFrom(ctx)
		fmt.Fprintln(writer, sql)
		fmt.Fprintln(writer, args)
	}
	result, err := exec.ExecContext(ctx, sql, args...)
	if err != nil {
		return 0, errors.Wrap(err, "account: unable to delete all from contact slice")
	}

	rowsAff, err := result.RowsAffected()
	if err != nil {
		return 0, errors.Wrap(err, "account: failed to get rows affected by deleteall for contacts")
	}

	if len(contactAfterDeleteHooks) != 0 {
		for _, obj := range o {
			if err := obj.doAfterDeleteHooks(ctx, exec); err != nil {
				return 0, err
			}
		}
	}

	return rowsAff, nil
}

// Reload refetches the object from the database
// using the primary keys with an executor.
func (o *Contact) Reload(ctx context.Context, exec boil.ContextExecutor) error {
	ret, err := FindContact(ctx, exec, o.ContactID)
	if err != nil {
		return err
	}

	*o = *ret
	return nil
}

// ReloadAll refetches every row with matching primary key column values
// and overwrites the original object slice with the newly updated slice.
func (o *ContactSlice) ReloadAll(ctx context.Context, exec boil.ContextExecutor) error {
	if o == nil || len(*o) == 0 {
		return nil
	}

	slice := ContactSlice{}
	var args []interface{}
	for _, obj := range *o {
		pkeyArgs := queries.ValuesFromMapping(reflect.Indirect(reflect.ValueOf(obj)), contactPrimaryKeyMapping)
		args = append(args, pkeyArgs...)
	}

	sql := "SELECT \"account\".\"contacts\".* FROM \"account\".\"contacts\" WHERE " +
		strmangle.WhereClauseRepeated(string(dialect.LQ), string(dialect.RQ), 1, contactPrimaryKeyColumns, len(*o))

	q := queries.Raw(sql, args...)

	err := q.Bind(ctx, exec, &slice)
	if err != nil {
		return errors.Wrap(err, "account: unable to reload all in ContactSlice")
	}

	*o = slice

	return nil
}

// ContactExists checks if the Contact row exists.
func ContactExists(ctx context.Context, exec boil.ContextExecutor, contactID string) (bool, error) {
	var exists bool
	sql := "select exists(select 1 from \"account\".\"contacts\" where \"contact_id\"=$1 limit 1)"

	if boil.IsDebug(ctx) {
		writer := boil.DebugWriterFrom(ctx)
		fmt.Fprintln(writer, sql)
		fmt.Fprintln(writer, contactID)
	}
	row := exec.QueryRowContext(ctx, sql, contactID)

	err := row.Scan(&exists)
	if err != nil {
		return false, errors.Wrap(err, "account: unable to check if contacts exists")
	}

	return exists, nil
}
