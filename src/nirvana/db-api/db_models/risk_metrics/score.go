// Code generated by SQLBoiler (https://github.com/volatiletech/sqlboiler). DO NOT EDIT.
// This file is meant to be re-generated in place and/or deleted at any time.

package risk_metrics

import (
	"context"
	"database/sql"
	"fmt"
	"reflect"
	"strconv"
	"strings"
	"sync"
	"time"

	"github.com/friendsofgo/errors"
	"github.com/volatiletech/sqlboiler/v4/boil"
	"github.com/volatiletech/sqlboiler/v4/queries"
	"github.com/volatiletech/sqlboiler/v4/queries/qm"
	"github.com/volatiletech/sqlboiler/v4/queries/qmhelper"
	"github.com/volatiletech/strmangle"
)

// Score is an object representing the database table.
type Score struct {
	ID        string    `boil:"id" json:"id" toml:"id" yaml:"id"`
	DotNumber int       `boil:"dot_number" json:"dot_number" toml:"dot_number" yaml:"dot_number"`
	Name      string    `boil:"name" json:"name" toml:"name" yaml:"name"`
	Version   string    `boil:"version" json:"version" toml:"version" yaml:"version"`
	Value     string    `boil:"value" json:"value" toml:"value" yaml:"value"`
	CreatedAt time.Time `boil:"created_at" json:"created_at" toml:"created_at" yaml:"created_at"`

	R *scoreR `boil:"" json:"" toml:"" yaml:""`
	L scoreL  `boil:"-" json:"-" toml:"-" yaml:"-"`
}

var ScoreColumns = struct {
	ID        string
	DotNumber string
	Name      string
	Version   string
	Value     string
	CreatedAt string
}{
	ID:        "id",
	DotNumber: "dot_number",
	Name:      "name",
	Version:   "version",
	Value:     "value",
	CreatedAt: "created_at",
}

var ScoreTableColumns = struct {
	ID        string
	DotNumber string
	Name      string
	Version   string
	Value     string
	CreatedAt string
}{
	ID:        "score.id",
	DotNumber: "score.dot_number",
	Name:      "score.name",
	Version:   "score.version",
	Value:     "score.value",
	CreatedAt: "score.created_at",
}

// Generated where

type whereHelperint struct{ field string }

func (w whereHelperint) EQ(x int) qm.QueryMod  { return qmhelper.Where(w.field, qmhelper.EQ, x) }
func (w whereHelperint) NEQ(x int) qm.QueryMod { return qmhelper.Where(w.field, qmhelper.NEQ, x) }
func (w whereHelperint) LT(x int) qm.QueryMod  { return qmhelper.Where(w.field, qmhelper.LT, x) }
func (w whereHelperint) LTE(x int) qm.QueryMod { return qmhelper.Where(w.field, qmhelper.LTE, x) }
func (w whereHelperint) GT(x int) qm.QueryMod  { return qmhelper.Where(w.field, qmhelper.GT, x) }
func (w whereHelperint) GTE(x int) qm.QueryMod { return qmhelper.Where(w.field, qmhelper.GTE, x) }
func (w whereHelperint) IN(slice []int) qm.QueryMod {
	values := make([]interface{}, 0, len(slice))
	for _, value := range slice {
		values = append(values, value)
	}
	return qm.WhereIn(fmt.Sprintf("%s IN ?", w.field), values...)
}
func (w whereHelperint) NIN(slice []int) qm.QueryMod {
	values := make([]interface{}, 0, len(slice))
	for _, value := range slice {
		values = append(values, value)
	}
	return qm.WhereNotIn(fmt.Sprintf("%s NOT IN ?", w.field), values...)
}

var ScoreWhere = struct {
	ID        whereHelperstring
	DotNumber whereHelperint
	Name      whereHelperstring
	Version   whereHelperstring
	Value     whereHelperstring
	CreatedAt whereHelpertime_Time
}{
	ID:        whereHelperstring{field: "\"risk_metrics\".\"score\".\"id\""},
	DotNumber: whereHelperint{field: "\"risk_metrics\".\"score\".\"dot_number\""},
	Name:      whereHelperstring{field: "\"risk_metrics\".\"score\".\"name\""},
	Version:   whereHelperstring{field: "\"risk_metrics\".\"score\".\"version\""},
	Value:     whereHelperstring{field: "\"risk_metrics\".\"score\".\"value\""},
	CreatedAt: whereHelpertime_Time{field: "\"risk_metrics\".\"score\".\"created_at\""},
}

// ScoreRels is where relationship names are stored.
var ScoreRels = struct {
}{}

// scoreR is where relationships are stored.
type scoreR struct {
}

// NewStruct creates a new relationship struct
func (*scoreR) NewStruct() *scoreR {
	return &scoreR{}
}

// scoreL is where Load methods for each relationship are stored.
type scoreL struct{}

var (
	scoreAllColumns            = []string{"id", "dot_number", "name", "version", "value", "created_at"}
	scoreColumnsWithoutDefault = []string{"id", "dot_number", "name", "version", "value", "created_at"}
	scoreColumnsWithDefault    = []string{}
	scorePrimaryKeyColumns     = []string{"id"}
	scoreGeneratedColumns      = []string{}
)

type (
	// ScoreSlice is an alias for a slice of pointers to Score.
	// This should almost always be used instead of []Score.
	ScoreSlice []*Score
	// ScoreHook is the signature for custom Score hook methods
	ScoreHook func(context.Context, boil.ContextExecutor, *Score) error

	scoreQuery struct {
		*queries.Query
	}
)

// Cache for insert, update and upsert
var (
	scoreType                 = reflect.TypeOf(&Score{})
	scoreMapping              = queries.MakeStructMapping(scoreType)
	scorePrimaryKeyMapping, _ = queries.BindMapping(scoreType, scoreMapping, scorePrimaryKeyColumns)
	scoreInsertCacheMut       sync.RWMutex
	scoreInsertCache          = make(map[string]insertCache)
	scoreUpdateCacheMut       sync.RWMutex
	scoreUpdateCache          = make(map[string]updateCache)
	scoreUpsertCacheMut       sync.RWMutex
	scoreUpsertCache          = make(map[string]insertCache)
)

var (
	// Force time package dependency for automated UpdatedAt/CreatedAt.
	_ = time.Second
	// Force qmhelper dependency for where clause generation (which doesn't
	// always happen)
	_ = qmhelper.Where
)

var scoreAfterSelectHooks []ScoreHook

var scoreBeforeInsertHooks []ScoreHook
var scoreAfterInsertHooks []ScoreHook

var scoreBeforeUpdateHooks []ScoreHook
var scoreAfterUpdateHooks []ScoreHook

var scoreBeforeDeleteHooks []ScoreHook
var scoreAfterDeleteHooks []ScoreHook

var scoreBeforeUpsertHooks []ScoreHook
var scoreAfterUpsertHooks []ScoreHook

// doAfterSelectHooks executes all "after Select" hooks.
func (o *Score) doAfterSelectHooks(ctx context.Context, exec boil.ContextExecutor) (err error) {
	if boil.HooksAreSkipped(ctx) {
		return nil
	}

	for _, hook := range scoreAfterSelectHooks {
		if err := hook(ctx, exec, o); err != nil {
			return err
		}
	}

	return nil
}

// doBeforeInsertHooks executes all "before insert" hooks.
func (o *Score) doBeforeInsertHooks(ctx context.Context, exec boil.ContextExecutor) (err error) {
	if boil.HooksAreSkipped(ctx) {
		return nil
	}

	for _, hook := range scoreBeforeInsertHooks {
		if err := hook(ctx, exec, o); err != nil {
			return err
		}
	}

	return nil
}

// doAfterInsertHooks executes all "after Insert" hooks.
func (o *Score) doAfterInsertHooks(ctx context.Context, exec boil.ContextExecutor) (err error) {
	if boil.HooksAreSkipped(ctx) {
		return nil
	}

	for _, hook := range scoreAfterInsertHooks {
		if err := hook(ctx, exec, o); err != nil {
			return err
		}
	}

	return nil
}

// doBeforeUpdateHooks executes all "before Update" hooks.
func (o *Score) doBeforeUpdateHooks(ctx context.Context, exec boil.ContextExecutor) (err error) {
	if boil.HooksAreSkipped(ctx) {
		return nil
	}

	for _, hook := range scoreBeforeUpdateHooks {
		if err := hook(ctx, exec, o); err != nil {
			return err
		}
	}

	return nil
}

// doAfterUpdateHooks executes all "after Update" hooks.
func (o *Score) doAfterUpdateHooks(ctx context.Context, exec boil.ContextExecutor) (err error) {
	if boil.HooksAreSkipped(ctx) {
		return nil
	}

	for _, hook := range scoreAfterUpdateHooks {
		if err := hook(ctx, exec, o); err != nil {
			return err
		}
	}

	return nil
}

// doBeforeDeleteHooks executes all "before Delete" hooks.
func (o *Score) doBeforeDeleteHooks(ctx context.Context, exec boil.ContextExecutor) (err error) {
	if boil.HooksAreSkipped(ctx) {
		return nil
	}

	for _, hook := range scoreBeforeDeleteHooks {
		if err := hook(ctx, exec, o); err != nil {
			return err
		}
	}

	return nil
}

// doAfterDeleteHooks executes all "after Delete" hooks.
func (o *Score) doAfterDeleteHooks(ctx context.Context, exec boil.ContextExecutor) (err error) {
	if boil.HooksAreSkipped(ctx) {
		return nil
	}

	for _, hook := range scoreAfterDeleteHooks {
		if err := hook(ctx, exec, o); err != nil {
			return err
		}
	}

	return nil
}

// doBeforeUpsertHooks executes all "before Upsert" hooks.
func (o *Score) doBeforeUpsertHooks(ctx context.Context, exec boil.ContextExecutor) (err error) {
	if boil.HooksAreSkipped(ctx) {
		return nil
	}

	for _, hook := range scoreBeforeUpsertHooks {
		if err := hook(ctx, exec, o); err != nil {
			return err
		}
	}

	return nil
}

// doAfterUpsertHooks executes all "after Upsert" hooks.
func (o *Score) doAfterUpsertHooks(ctx context.Context, exec boil.ContextExecutor) (err error) {
	if boil.HooksAreSkipped(ctx) {
		return nil
	}

	for _, hook := range scoreAfterUpsertHooks {
		if err := hook(ctx, exec, o); err != nil {
			return err
		}
	}

	return nil
}

// AddScoreHook registers your hook function for all future operations.
func AddScoreHook(hookPoint boil.HookPoint, scoreHook ScoreHook) {
	switch hookPoint {
	case boil.AfterSelectHook:
		scoreAfterSelectHooks = append(scoreAfterSelectHooks, scoreHook)
	case boil.BeforeInsertHook:
		scoreBeforeInsertHooks = append(scoreBeforeInsertHooks, scoreHook)
	case boil.AfterInsertHook:
		scoreAfterInsertHooks = append(scoreAfterInsertHooks, scoreHook)
	case boil.BeforeUpdateHook:
		scoreBeforeUpdateHooks = append(scoreBeforeUpdateHooks, scoreHook)
	case boil.AfterUpdateHook:
		scoreAfterUpdateHooks = append(scoreAfterUpdateHooks, scoreHook)
	case boil.BeforeDeleteHook:
		scoreBeforeDeleteHooks = append(scoreBeforeDeleteHooks, scoreHook)
	case boil.AfterDeleteHook:
		scoreAfterDeleteHooks = append(scoreAfterDeleteHooks, scoreHook)
	case boil.BeforeUpsertHook:
		scoreBeforeUpsertHooks = append(scoreBeforeUpsertHooks, scoreHook)
	case boil.AfterUpsertHook:
		scoreAfterUpsertHooks = append(scoreAfterUpsertHooks, scoreHook)
	}
}

// One returns a single score record from the query.
func (q scoreQuery) One(ctx context.Context, exec boil.ContextExecutor) (*Score, error) {
	o := &Score{}

	queries.SetLimit(q.Query, 1)

	err := q.Bind(ctx, exec, o)
	if err != nil {
		if errors.Is(err, sql.ErrNoRows) {
			return nil, sql.ErrNoRows
		}
		return nil, errors.Wrap(err, "risk_metrics: failed to execute a one query for score")
	}

	if err := o.doAfterSelectHooks(ctx, exec); err != nil {
		return o, err
	}

	return o, nil
}

// All returns all Score records from the query.
func (q scoreQuery) All(ctx context.Context, exec boil.ContextExecutor) (ScoreSlice, error) {
	var o []*Score

	err := q.Bind(ctx, exec, &o)
	if err != nil {
		return nil, errors.Wrap(err, "risk_metrics: failed to assign all query results to Score slice")
	}

	if len(scoreAfterSelectHooks) != 0 {
		for _, obj := range o {
			if err := obj.doAfterSelectHooks(ctx, exec); err != nil {
				return o, err
			}
		}
	}

	return o, nil
}

// Count returns the count of all Score records in the query.
func (q scoreQuery) Count(ctx context.Context, exec boil.ContextExecutor) (int64, error) {
	var count int64

	queries.SetSelect(q.Query, nil)
	queries.SetCount(q.Query)

	err := q.Query.QueryRowContext(ctx, exec).Scan(&count)
	if err != nil {
		return 0, errors.Wrap(err, "risk_metrics: failed to count score rows")
	}

	return count, nil
}

// Exists checks if the row exists in the table.
func (q scoreQuery) Exists(ctx context.Context, exec boil.ContextExecutor) (bool, error) {
	var count int64

	queries.SetSelect(q.Query, nil)
	queries.SetCount(q.Query)
	queries.SetLimit(q.Query, 1)

	err := q.Query.QueryRowContext(ctx, exec).Scan(&count)
	if err != nil {
		return false, errors.Wrap(err, "risk_metrics: failed to check if score exists")
	}

	return count > 0, nil
}

// Scores retrieves all the records using an executor.
func Scores(mods ...qm.QueryMod) scoreQuery {
	mods = append(mods, qm.From("\"risk_metrics\".\"score\""))
	q := NewQuery(mods...)
	if len(queries.GetSelect(q)) == 0 {
		queries.SetSelect(q, []string{"\"risk_metrics\".\"score\".*"})
	}

	return scoreQuery{q}
}

// FindScore retrieves a single record by ID with an executor.
// If selectCols is empty Find will return all columns.
func FindScore(ctx context.Context, exec boil.ContextExecutor, iD string, selectCols ...string) (*Score, error) {
	scoreObj := &Score{}

	sel := "*"
	if len(selectCols) > 0 {
		sel = strings.Join(strmangle.IdentQuoteSlice(dialect.LQ, dialect.RQ, selectCols), ",")
	}
	query := fmt.Sprintf(
		"select %s from \"risk_metrics\".\"score\" where \"id\"=$1", sel,
	)

	q := queries.Raw(query, iD)

	err := q.Bind(ctx, exec, scoreObj)
	if err != nil {
		if errors.Is(err, sql.ErrNoRows) {
			return nil, sql.ErrNoRows
		}
		return nil, errors.Wrap(err, "risk_metrics: unable to select from score")
	}

	if err = scoreObj.doAfterSelectHooks(ctx, exec); err != nil {
		return scoreObj, err
	}

	return scoreObj, nil
}

// Insert a single record using an executor.
// See boil.Columns.InsertColumnSet documentation to understand column list inference for inserts.
func (o *Score) Insert(ctx context.Context, exec boil.ContextExecutor, columns boil.Columns) error {
	if o == nil {
		return errors.New("risk_metrics: no score provided for insertion")
	}

	var err error
	if !boil.TimestampsAreSkipped(ctx) {
		currTime := time.Now().In(boil.GetLocation())

		if o.CreatedAt.IsZero() {
			o.CreatedAt = currTime
		}
	}

	if err := o.doBeforeInsertHooks(ctx, exec); err != nil {
		return err
	}

	nzDefaults := queries.NonZeroDefaultSet(scoreColumnsWithDefault, o)

	key := makeCacheKey(columns, nzDefaults)
	scoreInsertCacheMut.RLock()
	cache, cached := scoreInsertCache[key]
	scoreInsertCacheMut.RUnlock()

	if !cached {
		wl, returnColumns := columns.InsertColumnSet(
			scoreAllColumns,
			scoreColumnsWithDefault,
			scoreColumnsWithoutDefault,
			nzDefaults,
		)

		cache.valueMapping, err = queries.BindMapping(scoreType, scoreMapping, wl)
		if err != nil {
			return err
		}
		cache.retMapping, err = queries.BindMapping(scoreType, scoreMapping, returnColumns)
		if err != nil {
			return err
		}
		if len(wl) != 0 {
			cache.query = fmt.Sprintf("INSERT INTO \"risk_metrics\".\"score\" (\"%s\") %%sVALUES (%s)%%s", strings.Join(wl, "\",\""), strmangle.Placeholders(dialect.UseIndexPlaceholders, len(wl), 1, 1))
		} else {
			cache.query = "INSERT INTO \"risk_metrics\".\"score\" %sDEFAULT VALUES%s"
		}

		var queryOutput, queryReturning string

		if len(cache.retMapping) != 0 {
			queryReturning = fmt.Sprintf(" RETURNING \"%s\"", strings.Join(returnColumns, "\",\""))
		}

		cache.query = fmt.Sprintf(cache.query, queryOutput, queryReturning)
	}

	value := reflect.Indirect(reflect.ValueOf(o))
	vals := queries.ValuesFromMapping(value, cache.valueMapping)

	if boil.IsDebug(ctx) {
		writer := boil.DebugWriterFrom(ctx)
		fmt.Fprintln(writer, cache.query)
		fmt.Fprintln(writer, vals)
	}

	if len(cache.retMapping) != 0 {
		err = exec.QueryRowContext(ctx, cache.query, vals...).Scan(queries.PtrsFromMapping(value, cache.retMapping)...)
	} else {
		_, err = exec.ExecContext(ctx, cache.query, vals...)
	}

	if err != nil {
		return errors.Wrap(err, "risk_metrics: unable to insert into score")
	}

	if !cached {
		scoreInsertCacheMut.Lock()
		scoreInsertCache[key] = cache
		scoreInsertCacheMut.Unlock()
	}

	return o.doAfterInsertHooks(ctx, exec)
}

// Update uses an executor to update the Score.
// See boil.Columns.UpdateColumnSet documentation to understand column list inference for updates.
// Update does not automatically update the record in case of default values. Use .Reload() to refresh the records.
func (o *Score) Update(ctx context.Context, exec boil.ContextExecutor, columns boil.Columns) (int64, error) {
	var err error
	if err = o.doBeforeUpdateHooks(ctx, exec); err != nil {
		return 0, err
	}
	key := makeCacheKey(columns, nil)
	scoreUpdateCacheMut.RLock()
	cache, cached := scoreUpdateCache[key]
	scoreUpdateCacheMut.RUnlock()

	if !cached {
		wl := columns.UpdateColumnSet(
			scoreAllColumns,
			scorePrimaryKeyColumns,
		)

		if !columns.IsWhitelist() {
			wl = strmangle.SetComplement(wl, []string{"created_at"})
		}
		if len(wl) == 0 {
			return 0, errors.New("risk_metrics: unable to update score, could not build whitelist")
		}

		cache.query = fmt.Sprintf("UPDATE \"risk_metrics\".\"score\" SET %s WHERE %s",
			strmangle.SetParamNames("\"", "\"", 1, wl),
			strmangle.WhereClause("\"", "\"", len(wl)+1, scorePrimaryKeyColumns),
		)
		cache.valueMapping, err = queries.BindMapping(scoreType, scoreMapping, append(wl, scorePrimaryKeyColumns...))
		if err != nil {
			return 0, err
		}
	}

	values := queries.ValuesFromMapping(reflect.Indirect(reflect.ValueOf(o)), cache.valueMapping)

	if boil.IsDebug(ctx) {
		writer := boil.DebugWriterFrom(ctx)
		fmt.Fprintln(writer, cache.query)
		fmt.Fprintln(writer, values)
	}
	var result sql.Result
	result, err = exec.ExecContext(ctx, cache.query, values...)
	if err != nil {
		return 0, errors.Wrap(err, "risk_metrics: unable to update score row")
	}

	rowsAff, err := result.RowsAffected()
	if err != nil {
		return 0, errors.Wrap(err, "risk_metrics: failed to get rows affected by update for score")
	}

	if !cached {
		scoreUpdateCacheMut.Lock()
		scoreUpdateCache[key] = cache
		scoreUpdateCacheMut.Unlock()
	}

	return rowsAff, o.doAfterUpdateHooks(ctx, exec)
}

// UpdateAll updates all rows with the specified column values.
func (q scoreQuery) UpdateAll(ctx context.Context, exec boil.ContextExecutor, cols M) (int64, error) {
	queries.SetUpdate(q.Query, cols)

	result, err := q.Query.ExecContext(ctx, exec)
	if err != nil {
		return 0, errors.Wrap(err, "risk_metrics: unable to update all for score")
	}

	rowsAff, err := result.RowsAffected()
	if err != nil {
		return 0, errors.Wrap(err, "risk_metrics: unable to retrieve rows affected for score")
	}

	return rowsAff, nil
}

// UpdateAll updates all rows with the specified column values, using an executor.
func (o ScoreSlice) UpdateAll(ctx context.Context, exec boil.ContextExecutor, cols M) (int64, error) {
	ln := int64(len(o))
	if ln == 0 {
		return 0, nil
	}

	if len(cols) == 0 {
		return 0, errors.New("risk_metrics: update all requires at least one column argument")
	}

	colNames := make([]string, len(cols))
	args := make([]interface{}, len(cols))

	i := 0
	for name, value := range cols {
		colNames[i] = name
		args[i] = value
		i++
	}

	// Append all of the primary key values for each column
	for _, obj := range o {
		pkeyArgs := queries.ValuesFromMapping(reflect.Indirect(reflect.ValueOf(obj)), scorePrimaryKeyMapping)
		args = append(args, pkeyArgs...)
	}

	sql := fmt.Sprintf("UPDATE \"risk_metrics\".\"score\" SET %s WHERE %s",
		strmangle.SetParamNames("\"", "\"", 1, colNames),
		strmangle.WhereClauseRepeated(string(dialect.LQ), string(dialect.RQ), len(colNames)+1, scorePrimaryKeyColumns, len(o)))

	if boil.IsDebug(ctx) {
		writer := boil.DebugWriterFrom(ctx)
		fmt.Fprintln(writer, sql)
		fmt.Fprintln(writer, args...)
	}
	result, err := exec.ExecContext(ctx, sql, args...)
	if err != nil {
		return 0, errors.Wrap(err, "risk_metrics: unable to update all in score slice")
	}

	rowsAff, err := result.RowsAffected()
	if err != nil {
		return 0, errors.Wrap(err, "risk_metrics: unable to retrieve rows affected all in update all score")
	}
	return rowsAff, nil
}

// Upsert attempts an insert using an executor, and does an update or ignore on conflict.
// See boil.Columns documentation for how to properly use updateColumns and insertColumns.
func (o *Score) Upsert(ctx context.Context, exec boil.ContextExecutor, updateOnConflict bool, conflictColumns []string, updateColumns, insertColumns boil.Columns) error {
	if o == nil {
		return errors.New("risk_metrics: no score provided for upsert")
	}
	if !boil.TimestampsAreSkipped(ctx) {
		currTime := time.Now().In(boil.GetLocation())

		if o.CreatedAt.IsZero() {
			o.CreatedAt = currTime
		}
	}

	if err := o.doBeforeUpsertHooks(ctx, exec); err != nil {
		return err
	}

	nzDefaults := queries.NonZeroDefaultSet(scoreColumnsWithDefault, o)

	// Build cache key in-line uglily - mysql vs psql problems
	buf := strmangle.GetBuffer()
	if updateOnConflict {
		buf.WriteByte('t')
	} else {
		buf.WriteByte('f')
	}
	buf.WriteByte('.')
	for _, c := range conflictColumns {
		buf.WriteString(c)
	}
	buf.WriteByte('.')
	buf.WriteString(strconv.Itoa(updateColumns.Kind))
	for _, c := range updateColumns.Cols {
		buf.WriteString(c)
	}
	buf.WriteByte('.')
	buf.WriteString(strconv.Itoa(insertColumns.Kind))
	for _, c := range insertColumns.Cols {
		buf.WriteString(c)
	}
	buf.WriteByte('.')
	for _, c := range nzDefaults {
		buf.WriteString(c)
	}
	key := buf.String()
	strmangle.PutBuffer(buf)

	scoreUpsertCacheMut.RLock()
	cache, cached := scoreUpsertCache[key]
	scoreUpsertCacheMut.RUnlock()

	var err error

	if !cached {
		insert, ret := insertColumns.InsertColumnSet(
			scoreAllColumns,
			scoreColumnsWithDefault,
			scoreColumnsWithoutDefault,
			nzDefaults,
		)

		update := updateColumns.UpdateColumnSet(
			scoreAllColumns,
			scorePrimaryKeyColumns,
		)

		if updateOnConflict && len(update) == 0 {
			return errors.New("risk_metrics: unable to upsert score, could not build update column list")
		}

		conflict := conflictColumns
		if len(conflict) == 0 {
			conflict = make([]string, len(scorePrimaryKeyColumns))
			copy(conflict, scorePrimaryKeyColumns)
		}
		cache.query = buildUpsertQueryPostgres(dialect, "\"risk_metrics\".\"score\"", updateOnConflict, ret, update, conflict, insert)

		cache.valueMapping, err = queries.BindMapping(scoreType, scoreMapping, insert)
		if err != nil {
			return err
		}
		if len(ret) != 0 {
			cache.retMapping, err = queries.BindMapping(scoreType, scoreMapping, ret)
			if err != nil {
				return err
			}
		}
	}

	value := reflect.Indirect(reflect.ValueOf(o))
	vals := queries.ValuesFromMapping(value, cache.valueMapping)
	var returns []interface{}
	if len(cache.retMapping) != 0 {
		returns = queries.PtrsFromMapping(value, cache.retMapping)
	}

	if boil.IsDebug(ctx) {
		writer := boil.DebugWriterFrom(ctx)
		fmt.Fprintln(writer, cache.query)
		fmt.Fprintln(writer, vals)
	}
	if len(cache.retMapping) != 0 {
		err = exec.QueryRowContext(ctx, cache.query, vals...).Scan(returns...)
		if errors.Is(err, sql.ErrNoRows) {
			err = nil // Postgres doesn't return anything when there's no update
		}
	} else {
		_, err = exec.ExecContext(ctx, cache.query, vals...)
	}
	if err != nil {
		return errors.Wrap(err, "risk_metrics: unable to upsert score")
	}

	if !cached {
		scoreUpsertCacheMut.Lock()
		scoreUpsertCache[key] = cache
		scoreUpsertCacheMut.Unlock()
	}

	return o.doAfterUpsertHooks(ctx, exec)
}

// Delete deletes a single Score record with an executor.
// Delete will match against the primary key column to find the record to delete.
func (o *Score) Delete(ctx context.Context, exec boil.ContextExecutor) (int64, error) {
	if o == nil {
		return 0, errors.New("risk_metrics: no Score provided for delete")
	}

	if err := o.doBeforeDeleteHooks(ctx, exec); err != nil {
		return 0, err
	}

	args := queries.ValuesFromMapping(reflect.Indirect(reflect.ValueOf(o)), scorePrimaryKeyMapping)
	sql := "DELETE FROM \"risk_metrics\".\"score\" WHERE \"id\"=$1"

	if boil.IsDebug(ctx) {
		writer := boil.DebugWriterFrom(ctx)
		fmt.Fprintln(writer, sql)
		fmt.Fprintln(writer, args...)
	}
	result, err := exec.ExecContext(ctx, sql, args...)
	if err != nil {
		return 0, errors.Wrap(err, "risk_metrics: unable to delete from score")
	}

	rowsAff, err := result.RowsAffected()
	if err != nil {
		return 0, errors.Wrap(err, "risk_metrics: failed to get rows affected by delete for score")
	}

	if err := o.doAfterDeleteHooks(ctx, exec); err != nil {
		return 0, err
	}

	return rowsAff, nil
}

// DeleteAll deletes all matching rows.
func (q scoreQuery) DeleteAll(ctx context.Context, exec boil.ContextExecutor) (int64, error) {
	if q.Query == nil {
		return 0, errors.New("risk_metrics: no scoreQuery provided for delete all")
	}

	queries.SetDelete(q.Query)

	result, err := q.Query.ExecContext(ctx, exec)
	if err != nil {
		return 0, errors.Wrap(err, "risk_metrics: unable to delete all from score")
	}

	rowsAff, err := result.RowsAffected()
	if err != nil {
		return 0, errors.Wrap(err, "risk_metrics: failed to get rows affected by deleteall for score")
	}

	return rowsAff, nil
}

// DeleteAll deletes all rows in the slice, using an executor.
func (o ScoreSlice) DeleteAll(ctx context.Context, exec boil.ContextExecutor) (int64, error) {
	if len(o) == 0 {
		return 0, nil
	}

	if len(scoreBeforeDeleteHooks) != 0 {
		for _, obj := range o {
			if err := obj.doBeforeDeleteHooks(ctx, exec); err != nil {
				return 0, err
			}
		}
	}

	var args []interface{}
	for _, obj := range o {
		pkeyArgs := queries.ValuesFromMapping(reflect.Indirect(reflect.ValueOf(obj)), scorePrimaryKeyMapping)
		args = append(args, pkeyArgs...)
	}

	sql := "DELETE FROM \"risk_metrics\".\"score\" WHERE " +
		strmangle.WhereClauseRepeated(string(dialect.LQ), string(dialect.RQ), 1, scorePrimaryKeyColumns, len(o))

	if boil.IsDebug(ctx) {
		writer := boil.DebugWriterFrom(ctx)
		fmt.Fprintln(writer, sql)
		fmt.Fprintln(writer, args)
	}
	result, err := exec.ExecContext(ctx, sql, args...)
	if err != nil {
		return 0, errors.Wrap(err, "risk_metrics: unable to delete all from score slice")
	}

	rowsAff, err := result.RowsAffected()
	if err != nil {
		return 0, errors.Wrap(err, "risk_metrics: failed to get rows affected by deleteall for score")
	}

	if len(scoreAfterDeleteHooks) != 0 {
		for _, obj := range o {
			if err := obj.doAfterDeleteHooks(ctx, exec); err != nil {
				return 0, err
			}
		}
	}

	return rowsAff, nil
}

// Reload refetches the object from the database
// using the primary keys with an executor.
func (o *Score) Reload(ctx context.Context, exec boil.ContextExecutor) error {
	ret, err := FindScore(ctx, exec, o.ID)
	if err != nil {
		return err
	}

	*o = *ret
	return nil
}

// ReloadAll refetches every row with matching primary key column values
// and overwrites the original object slice with the newly updated slice.
func (o *ScoreSlice) ReloadAll(ctx context.Context, exec boil.ContextExecutor) error {
	if o == nil || len(*o) == 0 {
		return nil
	}

	slice := ScoreSlice{}
	var args []interface{}
	for _, obj := range *o {
		pkeyArgs := queries.ValuesFromMapping(reflect.Indirect(reflect.ValueOf(obj)), scorePrimaryKeyMapping)
		args = append(args, pkeyArgs...)
	}

	sql := "SELECT \"risk_metrics\".\"score\".* FROM \"risk_metrics\".\"score\" WHERE " +
		strmangle.WhereClauseRepeated(string(dialect.LQ), string(dialect.RQ), 1, scorePrimaryKeyColumns, len(*o))

	q := queries.Raw(sql, args...)

	err := q.Bind(ctx, exec, &slice)
	if err != nil {
		return errors.Wrap(err, "risk_metrics: unable to reload all in ScoreSlice")
	}

	*o = slice

	return nil
}

// ScoreExists checks if the Score row exists.
func ScoreExists(ctx context.Context, exec boil.ContextExecutor, iD string) (bool, error) {
	var exists bool
	sql := "select exists(select 1 from \"risk_metrics\".\"score\" where \"id\"=$1 limit 1)"

	if boil.IsDebug(ctx) {
		writer := boil.DebugWriterFrom(ctx)
		fmt.Fprintln(writer, sql)
		fmt.Fprintln(writer, iD)
	}
	row := exec.QueryRowContext(ctx, sql, iD)

	err := row.Scan(&exists)
	if err != nil {
		return false, errors.Wrap(err, "risk_metrics: unable to check if score exists")
	}

	return exists, nil
}
