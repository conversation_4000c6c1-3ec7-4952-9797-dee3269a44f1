// Code generated by SQLBoiler (https://github.com/volatiletech/sqlboiler). DO NOT EDIT.
// This file is meant to be re-generated in place and/or deleted at any time.

package account

import (
	"context"
	"database/sql"
	"fmt"
	"reflect"
	"strconv"
	"strings"
	"sync"
	"time"

	"github.com/friendsofgo/errors"
	"github.com/volatiletech/sqlboiler/v4/boil"
	"github.com/volatiletech/sqlboiler/v4/queries"
	"github.com/volatiletech/sqlboiler/v4/queries/qm"
	"github.com/volatiletech/sqlboiler/v4/queries/qmhelper"
	"github.com/volatiletech/sqlboiler/v4/types"
	"github.com/volatiletech/strmangle"
)

// Account is an object representing the database table.
type Account struct {
	AccountID   string     `boil:"account_id" json:"account_id" toml:"account_id" yaml:"account_id"`
	Name        string     `boil:"name" json:"name" toml:"name" yaml:"name"`
	ShortID     string     `boil:"short_id" json:"short_id" toml:"short_id" yaml:"short_id"`
	Status      string     `boil:"status" json:"status" toml:"status" yaml:"status"`
	AddressNorm string     `boil:"address_norm" json:"address_norm" toml:"address_norm" yaml:"address_norm"`
	Address     types.JSON `boil:"address" json:"address" toml:"address" yaml:"address"`
	CreatedAt   time.Time  `boil:"created_at" json:"created_at" toml:"created_at" yaml:"created_at"`
	UpdatedAt   time.Time  `boil:"updated_at" json:"updated_at" toml:"updated_at" yaml:"updated_at"`

	R *accountR `boil:"" json:"" toml:"" yaml:""`
	L accountL  `boil:"-" json:"-" toml:"-" yaml:"-"`
}

var AccountColumns = struct {
	AccountID   string
	Name        string
	ShortID     string
	Status      string
	AddressNorm string
	Address     string
	CreatedAt   string
	UpdatedAt   string
}{
	AccountID:   "account_id",
	Name:        "name",
	ShortID:     "short_id",
	Status:      "status",
	AddressNorm: "address_norm",
	Address:     "address",
	CreatedAt:   "created_at",
	UpdatedAt:   "updated_at",
}

var AccountTableColumns = struct {
	AccountID   string
	Name        string
	ShortID     string
	Status      string
	AddressNorm string
	Address     string
	CreatedAt   string
	UpdatedAt   string
}{
	AccountID:   "accounts.account_id",
	Name:        "accounts.name",
	ShortID:     "accounts.short_id",
	Status:      "accounts.status",
	AddressNorm: "accounts.address_norm",
	Address:     "accounts.address",
	CreatedAt:   "accounts.created_at",
	UpdatedAt:   "accounts.updated_at",
}

// Generated where

var AccountWhere = struct {
	AccountID   whereHelperstring
	Name        whereHelperstring
	ShortID     whereHelperstring
	Status      whereHelperstring
	AddressNorm whereHelperstring
	Address     whereHelpertypes_JSON
	CreatedAt   whereHelpertime_Time
	UpdatedAt   whereHelpertime_Time
}{
	AccountID:   whereHelperstring{field: "\"account\".\"accounts\".\"account_id\""},
	Name:        whereHelperstring{field: "\"account\".\"accounts\".\"name\""},
	ShortID:     whereHelperstring{field: "\"account\".\"accounts\".\"short_id\""},
	Status:      whereHelperstring{field: "\"account\".\"accounts\".\"status\""},
	AddressNorm: whereHelperstring{field: "\"account\".\"accounts\".\"address_norm\""},
	Address:     whereHelpertypes_JSON{field: "\"account\".\"accounts\".\"address\""},
	CreatedAt:   whereHelpertime_Time{field: "\"account\".\"accounts\".\"created_at\""},
	UpdatedAt:   whereHelpertime_Time{field: "\"account\".\"accounts\".\"updated_at\""},
}

// AccountRels is where relationship names are stored.
var AccountRels = struct {
	CanonicalAccountAccountAliases         string
	AccountIdentifiers                     string
	SourceAccountAccountMergeHistories     string
	TargetAccountAccountMergeHistories     string
	CandidateAccountAccountPossibleMatches string
	MatchedAccountAccountPossibleMatches   string
	AccountPrograms                        string
	Contacts                               string
}{
	CanonicalAccountAccountAliases:         "CanonicalAccountAccountAliases",
	AccountIdentifiers:                     "AccountIdentifiers",
	SourceAccountAccountMergeHistories:     "SourceAccountAccountMergeHistories",
	TargetAccountAccountMergeHistories:     "TargetAccountAccountMergeHistories",
	CandidateAccountAccountPossibleMatches: "CandidateAccountAccountPossibleMatches",
	MatchedAccountAccountPossibleMatches:   "MatchedAccountAccountPossibleMatches",
	AccountPrograms:                        "AccountPrograms",
	Contacts:                               "Contacts",
}

// accountR is where relationships are stored.
type accountR struct {
	CanonicalAccountAccountAliases         AccountAliasSlice         `boil:"CanonicalAccountAccountAliases" json:"CanonicalAccountAccountAliases" toml:"CanonicalAccountAccountAliases" yaml:"CanonicalAccountAccountAliases"`
	AccountIdentifiers                     AccountIdentifierSlice    `boil:"AccountIdentifiers" json:"AccountIdentifiers" toml:"AccountIdentifiers" yaml:"AccountIdentifiers"`
	SourceAccountAccountMergeHistories     AccountMergeHistorySlice  `boil:"SourceAccountAccountMergeHistories" json:"SourceAccountAccountMergeHistories" toml:"SourceAccountAccountMergeHistories" yaml:"SourceAccountAccountMergeHistories"`
	TargetAccountAccountMergeHistories     AccountMergeHistorySlice  `boil:"TargetAccountAccountMergeHistories" json:"TargetAccountAccountMergeHistories" toml:"TargetAccountAccountMergeHistories" yaml:"TargetAccountAccountMergeHistories"`
	CandidateAccountAccountPossibleMatches AccountPossibleMatchSlice `boil:"CandidateAccountAccountPossibleMatches" json:"CandidateAccountAccountPossibleMatches" toml:"CandidateAccountAccountPossibleMatches" yaml:"CandidateAccountAccountPossibleMatches"`
	MatchedAccountAccountPossibleMatches   AccountPossibleMatchSlice `boil:"MatchedAccountAccountPossibleMatches" json:"MatchedAccountAccountPossibleMatches" toml:"MatchedAccountAccountPossibleMatches" yaml:"MatchedAccountAccountPossibleMatches"`
	AccountPrograms                        AccountProgramSlice       `boil:"AccountPrograms" json:"AccountPrograms" toml:"AccountPrograms" yaml:"AccountPrograms"`
	Contacts                               ContactSlice              `boil:"Contacts" json:"Contacts" toml:"Contacts" yaml:"Contacts"`
}

// NewStruct creates a new relationship struct
func (*accountR) NewStruct() *accountR {
	return &accountR{}
}

// accountL is where Load methods for each relationship are stored.
type accountL struct{}

var (
	accountAllColumns            = []string{"account_id", "name", "short_id", "status", "address_norm", "address", "created_at", "updated_at"}
	accountColumnsWithoutDefault = []string{"account_id", "name", "short_id"}
	accountColumnsWithDefault    = []string{"status", "address_norm", "address", "created_at", "updated_at"}
	accountPrimaryKeyColumns     = []string{"account_id"}
	accountGeneratedColumns      = []string{}
)

type (
	// AccountSlice is an alias for a slice of pointers to Account.
	// This should almost always be used instead of []Account.
	AccountSlice []*Account
	// AccountHook is the signature for custom Account hook methods
	AccountHook func(context.Context, boil.ContextExecutor, *Account) error

	accountQuery struct {
		*queries.Query
	}
)

// Cache for insert, update and upsert
var (
	accountType                 = reflect.TypeOf(&Account{})
	accountMapping              = queries.MakeStructMapping(accountType)
	accountPrimaryKeyMapping, _ = queries.BindMapping(accountType, accountMapping, accountPrimaryKeyColumns)
	accountInsertCacheMut       sync.RWMutex
	accountInsertCache          = make(map[string]insertCache)
	accountUpdateCacheMut       sync.RWMutex
	accountUpdateCache          = make(map[string]updateCache)
	accountUpsertCacheMut       sync.RWMutex
	accountUpsertCache          = make(map[string]insertCache)
)

var (
	// Force time package dependency for automated UpdatedAt/CreatedAt.
	_ = time.Second
	// Force qmhelper dependency for where clause generation (which doesn't
	// always happen)
	_ = qmhelper.Where
)

var accountAfterSelectHooks []AccountHook

var accountBeforeInsertHooks []AccountHook
var accountAfterInsertHooks []AccountHook

var accountBeforeUpdateHooks []AccountHook
var accountAfterUpdateHooks []AccountHook

var accountBeforeDeleteHooks []AccountHook
var accountAfterDeleteHooks []AccountHook

var accountBeforeUpsertHooks []AccountHook
var accountAfterUpsertHooks []AccountHook

// doAfterSelectHooks executes all "after Select" hooks.
func (o *Account) doAfterSelectHooks(ctx context.Context, exec boil.ContextExecutor) (err error) {
	if boil.HooksAreSkipped(ctx) {
		return nil
	}

	for _, hook := range accountAfterSelectHooks {
		if err := hook(ctx, exec, o); err != nil {
			return err
		}
	}

	return nil
}

// doBeforeInsertHooks executes all "before insert" hooks.
func (o *Account) doBeforeInsertHooks(ctx context.Context, exec boil.ContextExecutor) (err error) {
	if boil.HooksAreSkipped(ctx) {
		return nil
	}

	for _, hook := range accountBeforeInsertHooks {
		if err := hook(ctx, exec, o); err != nil {
			return err
		}
	}

	return nil
}

// doAfterInsertHooks executes all "after Insert" hooks.
func (o *Account) doAfterInsertHooks(ctx context.Context, exec boil.ContextExecutor) (err error) {
	if boil.HooksAreSkipped(ctx) {
		return nil
	}

	for _, hook := range accountAfterInsertHooks {
		if err := hook(ctx, exec, o); err != nil {
			return err
		}
	}

	return nil
}

// doBeforeUpdateHooks executes all "before Update" hooks.
func (o *Account) doBeforeUpdateHooks(ctx context.Context, exec boil.ContextExecutor) (err error) {
	if boil.HooksAreSkipped(ctx) {
		return nil
	}

	for _, hook := range accountBeforeUpdateHooks {
		if err := hook(ctx, exec, o); err != nil {
			return err
		}
	}

	return nil
}

// doAfterUpdateHooks executes all "after Update" hooks.
func (o *Account) doAfterUpdateHooks(ctx context.Context, exec boil.ContextExecutor) (err error) {
	if boil.HooksAreSkipped(ctx) {
		return nil
	}

	for _, hook := range accountAfterUpdateHooks {
		if err := hook(ctx, exec, o); err != nil {
			return err
		}
	}

	return nil
}

// doBeforeDeleteHooks executes all "before Delete" hooks.
func (o *Account) doBeforeDeleteHooks(ctx context.Context, exec boil.ContextExecutor) (err error) {
	if boil.HooksAreSkipped(ctx) {
		return nil
	}

	for _, hook := range accountBeforeDeleteHooks {
		if err := hook(ctx, exec, o); err != nil {
			return err
		}
	}

	return nil
}

// doAfterDeleteHooks executes all "after Delete" hooks.
func (o *Account) doAfterDeleteHooks(ctx context.Context, exec boil.ContextExecutor) (err error) {
	if boil.HooksAreSkipped(ctx) {
		return nil
	}

	for _, hook := range accountAfterDeleteHooks {
		if err := hook(ctx, exec, o); err != nil {
			return err
		}
	}

	return nil
}

// doBeforeUpsertHooks executes all "before Upsert" hooks.
func (o *Account) doBeforeUpsertHooks(ctx context.Context, exec boil.ContextExecutor) (err error) {
	if boil.HooksAreSkipped(ctx) {
		return nil
	}

	for _, hook := range accountBeforeUpsertHooks {
		if err := hook(ctx, exec, o); err != nil {
			return err
		}
	}

	return nil
}

// doAfterUpsertHooks executes all "after Upsert" hooks.
func (o *Account) doAfterUpsertHooks(ctx context.Context, exec boil.ContextExecutor) (err error) {
	if boil.HooksAreSkipped(ctx) {
		return nil
	}

	for _, hook := range accountAfterUpsertHooks {
		if err := hook(ctx, exec, o); err != nil {
			return err
		}
	}

	return nil
}

// AddAccountHook registers your hook function for all future operations.
func AddAccountHook(hookPoint boil.HookPoint, accountHook AccountHook) {
	switch hookPoint {
	case boil.AfterSelectHook:
		accountAfterSelectHooks = append(accountAfterSelectHooks, accountHook)
	case boil.BeforeInsertHook:
		accountBeforeInsertHooks = append(accountBeforeInsertHooks, accountHook)
	case boil.AfterInsertHook:
		accountAfterInsertHooks = append(accountAfterInsertHooks, accountHook)
	case boil.BeforeUpdateHook:
		accountBeforeUpdateHooks = append(accountBeforeUpdateHooks, accountHook)
	case boil.AfterUpdateHook:
		accountAfterUpdateHooks = append(accountAfterUpdateHooks, accountHook)
	case boil.BeforeDeleteHook:
		accountBeforeDeleteHooks = append(accountBeforeDeleteHooks, accountHook)
	case boil.AfterDeleteHook:
		accountAfterDeleteHooks = append(accountAfterDeleteHooks, accountHook)
	case boil.BeforeUpsertHook:
		accountBeforeUpsertHooks = append(accountBeforeUpsertHooks, accountHook)
	case boil.AfterUpsertHook:
		accountAfterUpsertHooks = append(accountAfterUpsertHooks, accountHook)
	}
}

// One returns a single account record from the query.
func (q accountQuery) One(ctx context.Context, exec boil.ContextExecutor) (*Account, error) {
	o := &Account{}

	queries.SetLimit(q.Query, 1)

	err := q.Bind(ctx, exec, o)
	if err != nil {
		if errors.Is(err, sql.ErrNoRows) {
			return nil, sql.ErrNoRows
		}
		return nil, errors.Wrap(err, "account: failed to execute a one query for accounts")
	}

	if err := o.doAfterSelectHooks(ctx, exec); err != nil {
		return o, err
	}

	return o, nil
}

// All returns all Account records from the query.
func (q accountQuery) All(ctx context.Context, exec boil.ContextExecutor) (AccountSlice, error) {
	var o []*Account

	err := q.Bind(ctx, exec, &o)
	if err != nil {
		return nil, errors.Wrap(err, "account: failed to assign all query results to Account slice")
	}

	if len(accountAfterSelectHooks) != 0 {
		for _, obj := range o {
			if err := obj.doAfterSelectHooks(ctx, exec); err != nil {
				return o, err
			}
		}
	}

	return o, nil
}

// Count returns the count of all Account records in the query.
func (q accountQuery) Count(ctx context.Context, exec boil.ContextExecutor) (int64, error) {
	var count int64

	queries.SetSelect(q.Query, nil)
	queries.SetCount(q.Query)

	err := q.Query.QueryRowContext(ctx, exec).Scan(&count)
	if err != nil {
		return 0, errors.Wrap(err, "account: failed to count accounts rows")
	}

	return count, nil
}

// Exists checks if the row exists in the table.
func (q accountQuery) Exists(ctx context.Context, exec boil.ContextExecutor) (bool, error) {
	var count int64

	queries.SetSelect(q.Query, nil)
	queries.SetCount(q.Query)
	queries.SetLimit(q.Query, 1)

	err := q.Query.QueryRowContext(ctx, exec).Scan(&count)
	if err != nil {
		return false, errors.Wrap(err, "account: failed to check if accounts exists")
	}

	return count > 0, nil
}

// CanonicalAccountAccountAliases retrieves all the account_alias's AccountAliases with an executor via canonical_account_id column.
func (o *Account) CanonicalAccountAccountAliases(mods ...qm.QueryMod) accountAliasQuery {
	var queryMods []qm.QueryMod
	if len(mods) != 0 {
		queryMods = append(queryMods, mods...)
	}

	queryMods = append(queryMods,
		qm.Where("\"account\".\"account_aliases\".\"canonical_account_id\"=?", o.AccountID),
	)

	return AccountAliases(queryMods...)
}

// AccountIdentifiers retrieves all the account_identifier's AccountIdentifiers with an executor.
func (o *Account) AccountIdentifiers(mods ...qm.QueryMod) accountIdentifierQuery {
	var queryMods []qm.QueryMod
	if len(mods) != 0 {
		queryMods = append(queryMods, mods...)
	}

	queryMods = append(queryMods,
		qm.Where("\"account\".\"account_identifiers\".\"account_id\"=?", o.AccountID),
	)

	return AccountIdentifiers(queryMods...)
}

// SourceAccountAccountMergeHistories retrieves all the account_merge_history's AccountMergeHistories with an executor via source_account_id column.
func (o *Account) SourceAccountAccountMergeHistories(mods ...qm.QueryMod) accountMergeHistoryQuery {
	var queryMods []qm.QueryMod
	if len(mods) != 0 {
		queryMods = append(queryMods, mods...)
	}

	queryMods = append(queryMods,
		qm.Where("\"account\".\"account_merge_history\".\"source_account_id\"=?", o.AccountID),
	)

	return AccountMergeHistories(queryMods...)
}

// TargetAccountAccountMergeHistories retrieves all the account_merge_history's AccountMergeHistories with an executor via target_account_id column.
func (o *Account) TargetAccountAccountMergeHistories(mods ...qm.QueryMod) accountMergeHistoryQuery {
	var queryMods []qm.QueryMod
	if len(mods) != 0 {
		queryMods = append(queryMods, mods...)
	}

	queryMods = append(queryMods,
		qm.Where("\"account\".\"account_merge_history\".\"target_account_id\"=?", o.AccountID),
	)

	return AccountMergeHistories(queryMods...)
}

// CandidateAccountAccountPossibleMatches retrieves all the account_possible_match's AccountPossibleMatches with an executor via candidate_account_id column.
func (o *Account) CandidateAccountAccountPossibleMatches(mods ...qm.QueryMod) accountPossibleMatchQuery {
	var queryMods []qm.QueryMod
	if len(mods) != 0 {
		queryMods = append(queryMods, mods...)
	}

	queryMods = append(queryMods,
		qm.Where("\"account\".\"account_possible_matches\".\"candidate_account_id\"=?", o.AccountID),
	)

	return AccountPossibleMatches(queryMods...)
}

// MatchedAccountAccountPossibleMatches retrieves all the account_possible_match's AccountPossibleMatches with an executor via matched_account_id column.
func (o *Account) MatchedAccountAccountPossibleMatches(mods ...qm.QueryMod) accountPossibleMatchQuery {
	var queryMods []qm.QueryMod
	if len(mods) != 0 {
		queryMods = append(queryMods, mods...)
	}

	queryMods = append(queryMods,
		qm.Where("\"account\".\"account_possible_matches\".\"matched_account_id\"=?", o.AccountID),
	)

	return AccountPossibleMatches(queryMods...)
}

// AccountPrograms retrieves all the account_program's AccountPrograms with an executor.
func (o *Account) AccountPrograms(mods ...qm.QueryMod) accountProgramQuery {
	var queryMods []qm.QueryMod
	if len(mods) != 0 {
		queryMods = append(queryMods, mods...)
	}

	queryMods = append(queryMods,
		qm.Where("\"account\".\"account_programs\".\"account_id\"=?", o.AccountID),
	)

	return AccountPrograms(queryMods...)
}

// Contacts retrieves all the contact's Contacts with an executor.
func (o *Account) Contacts(mods ...qm.QueryMod) contactQuery {
	var queryMods []qm.QueryMod
	if len(mods) != 0 {
		queryMods = append(queryMods, mods...)
	}

	queryMods = append(queryMods,
		qm.Where("\"account\".\"contacts\".\"account_id\"=?", o.AccountID),
	)

	return Contacts(queryMods...)
}

// LoadCanonicalAccountAccountAliases allows an eager lookup of values, cached into the
// loaded structs of the objects. This is for a 1-M or N-M relationship.
func (accountL) LoadCanonicalAccountAccountAliases(ctx context.Context, e boil.ContextExecutor, singular bool, maybeAccount interface{}, mods queries.Applicator) error {
	var slice []*Account
	var object *Account

	if singular {
		object = maybeAccount.(*Account)
	} else {
		slice = *maybeAccount.(*[]*Account)
	}

	args := make([]interface{}, 0, 1)
	if singular {
		if object.R == nil {
			object.R = &accountR{}
		}
		args = append(args, object.AccountID)
	} else {
	Outer:
		for _, obj := range slice {
			if obj.R == nil {
				obj.R = &accountR{}
			}

			for _, a := range args {
				if a == obj.AccountID {
					continue Outer
				}
			}

			args = append(args, obj.AccountID)
		}
	}

	if len(args) == 0 {
		return nil
	}

	query := NewQuery(
		qm.From(`account.account_aliases`),
		qm.WhereIn(`account.account_aliases.canonical_account_id in ?`, args...),
	)
	if mods != nil {
		mods.Apply(query)
	}

	results, err := query.QueryContext(ctx, e)
	if err != nil {
		return errors.Wrap(err, "failed to eager load account_aliases")
	}

	var resultSlice []*AccountAlias
	if err = queries.Bind(results, &resultSlice); err != nil {
		return errors.Wrap(err, "failed to bind eager loaded slice account_aliases")
	}

	if err = results.Close(); err != nil {
		return errors.Wrap(err, "failed to close results in eager load on account_aliases")
	}
	if err = results.Err(); err != nil {
		return errors.Wrap(err, "error occurred during iteration of eager loaded relations for account_aliases")
	}

	if len(accountAliasAfterSelectHooks) != 0 {
		for _, obj := range resultSlice {
			if err := obj.doAfterSelectHooks(ctx, e); err != nil {
				return err
			}
		}
	}
	if singular {
		object.R.CanonicalAccountAccountAliases = resultSlice
		for _, foreign := range resultSlice {
			if foreign.R == nil {
				foreign.R = &accountAliasR{}
			}
			foreign.R.CanonicalAccount = object
		}
		return nil
	}

	for _, foreign := range resultSlice {
		for _, local := range slice {
			if local.AccountID == foreign.CanonicalAccountID {
				local.R.CanonicalAccountAccountAliases = append(local.R.CanonicalAccountAccountAliases, foreign)
				if foreign.R == nil {
					foreign.R = &accountAliasR{}
				}
				foreign.R.CanonicalAccount = local
				break
			}
		}
	}

	return nil
}

// LoadAccountIdentifiers allows an eager lookup of values, cached into the
// loaded structs of the objects. This is for a 1-M or N-M relationship.
func (accountL) LoadAccountIdentifiers(ctx context.Context, e boil.ContextExecutor, singular bool, maybeAccount interface{}, mods queries.Applicator) error {
	var slice []*Account
	var object *Account

	if singular {
		object = maybeAccount.(*Account)
	} else {
		slice = *maybeAccount.(*[]*Account)
	}

	args := make([]interface{}, 0, 1)
	if singular {
		if object.R == nil {
			object.R = &accountR{}
		}
		args = append(args, object.AccountID)
	} else {
	Outer:
		for _, obj := range slice {
			if obj.R == nil {
				obj.R = &accountR{}
			}

			for _, a := range args {
				if a == obj.AccountID {
					continue Outer
				}
			}

			args = append(args, obj.AccountID)
		}
	}

	if len(args) == 0 {
		return nil
	}

	query := NewQuery(
		qm.From(`account.account_identifiers`),
		qm.WhereIn(`account.account_identifiers.account_id in ?`, args...),
	)
	if mods != nil {
		mods.Apply(query)
	}

	results, err := query.QueryContext(ctx, e)
	if err != nil {
		return errors.Wrap(err, "failed to eager load account_identifiers")
	}

	var resultSlice []*AccountIdentifier
	if err = queries.Bind(results, &resultSlice); err != nil {
		return errors.Wrap(err, "failed to bind eager loaded slice account_identifiers")
	}

	if err = results.Close(); err != nil {
		return errors.Wrap(err, "failed to close results in eager load on account_identifiers")
	}
	if err = results.Err(); err != nil {
		return errors.Wrap(err, "error occurred during iteration of eager loaded relations for account_identifiers")
	}

	if len(accountIdentifierAfterSelectHooks) != 0 {
		for _, obj := range resultSlice {
			if err := obj.doAfterSelectHooks(ctx, e); err != nil {
				return err
			}
		}
	}
	if singular {
		object.R.AccountIdentifiers = resultSlice
		for _, foreign := range resultSlice {
			if foreign.R == nil {
				foreign.R = &accountIdentifierR{}
			}
			foreign.R.Account = object
		}
		return nil
	}

	for _, foreign := range resultSlice {
		for _, local := range slice {
			if local.AccountID == foreign.AccountID {
				local.R.AccountIdentifiers = append(local.R.AccountIdentifiers, foreign)
				if foreign.R == nil {
					foreign.R = &accountIdentifierR{}
				}
				foreign.R.Account = local
				break
			}
		}
	}

	return nil
}

// LoadSourceAccountAccountMergeHistories allows an eager lookup of values, cached into the
// loaded structs of the objects. This is for a 1-M or N-M relationship.
func (accountL) LoadSourceAccountAccountMergeHistories(ctx context.Context, e boil.ContextExecutor, singular bool, maybeAccount interface{}, mods queries.Applicator) error {
	var slice []*Account
	var object *Account

	if singular {
		object = maybeAccount.(*Account)
	} else {
		slice = *maybeAccount.(*[]*Account)
	}

	args := make([]interface{}, 0, 1)
	if singular {
		if object.R == nil {
			object.R = &accountR{}
		}
		args = append(args, object.AccountID)
	} else {
	Outer:
		for _, obj := range slice {
			if obj.R == nil {
				obj.R = &accountR{}
			}

			for _, a := range args {
				if a == obj.AccountID {
					continue Outer
				}
			}

			args = append(args, obj.AccountID)
		}
	}

	if len(args) == 0 {
		return nil
	}

	query := NewQuery(
		qm.From(`account.account_merge_history`),
		qm.WhereIn(`account.account_merge_history.source_account_id in ?`, args...),
	)
	if mods != nil {
		mods.Apply(query)
	}

	results, err := query.QueryContext(ctx, e)
	if err != nil {
		return errors.Wrap(err, "failed to eager load account_merge_history")
	}

	var resultSlice []*AccountMergeHistory
	if err = queries.Bind(results, &resultSlice); err != nil {
		return errors.Wrap(err, "failed to bind eager loaded slice account_merge_history")
	}

	if err = results.Close(); err != nil {
		return errors.Wrap(err, "failed to close results in eager load on account_merge_history")
	}
	if err = results.Err(); err != nil {
		return errors.Wrap(err, "error occurred during iteration of eager loaded relations for account_merge_history")
	}

	if len(accountMergeHistoryAfterSelectHooks) != 0 {
		for _, obj := range resultSlice {
			if err := obj.doAfterSelectHooks(ctx, e); err != nil {
				return err
			}
		}
	}
	if singular {
		object.R.SourceAccountAccountMergeHistories = resultSlice
		for _, foreign := range resultSlice {
			if foreign.R == nil {
				foreign.R = &accountMergeHistoryR{}
			}
			foreign.R.SourceAccount = object
		}
		return nil
	}

	for _, foreign := range resultSlice {
		for _, local := range slice {
			if local.AccountID == foreign.SourceAccountID {
				local.R.SourceAccountAccountMergeHistories = append(local.R.SourceAccountAccountMergeHistories, foreign)
				if foreign.R == nil {
					foreign.R = &accountMergeHistoryR{}
				}
				foreign.R.SourceAccount = local
				break
			}
		}
	}

	return nil
}

// LoadTargetAccountAccountMergeHistories allows an eager lookup of values, cached into the
// loaded structs of the objects. This is for a 1-M or N-M relationship.
func (accountL) LoadTargetAccountAccountMergeHistories(ctx context.Context, e boil.ContextExecutor, singular bool, maybeAccount interface{}, mods queries.Applicator) error {
	var slice []*Account
	var object *Account

	if singular {
		object = maybeAccount.(*Account)
	} else {
		slice = *maybeAccount.(*[]*Account)
	}

	args := make([]interface{}, 0, 1)
	if singular {
		if object.R == nil {
			object.R = &accountR{}
		}
		args = append(args, object.AccountID)
	} else {
	Outer:
		for _, obj := range slice {
			if obj.R == nil {
				obj.R = &accountR{}
			}

			for _, a := range args {
				if a == obj.AccountID {
					continue Outer
				}
			}

			args = append(args, obj.AccountID)
		}
	}

	if len(args) == 0 {
		return nil
	}

	query := NewQuery(
		qm.From(`account.account_merge_history`),
		qm.WhereIn(`account.account_merge_history.target_account_id in ?`, args...),
	)
	if mods != nil {
		mods.Apply(query)
	}

	results, err := query.QueryContext(ctx, e)
	if err != nil {
		return errors.Wrap(err, "failed to eager load account_merge_history")
	}

	var resultSlice []*AccountMergeHistory
	if err = queries.Bind(results, &resultSlice); err != nil {
		return errors.Wrap(err, "failed to bind eager loaded slice account_merge_history")
	}

	if err = results.Close(); err != nil {
		return errors.Wrap(err, "failed to close results in eager load on account_merge_history")
	}
	if err = results.Err(); err != nil {
		return errors.Wrap(err, "error occurred during iteration of eager loaded relations for account_merge_history")
	}

	if len(accountMergeHistoryAfterSelectHooks) != 0 {
		for _, obj := range resultSlice {
			if err := obj.doAfterSelectHooks(ctx, e); err != nil {
				return err
			}
		}
	}
	if singular {
		object.R.TargetAccountAccountMergeHistories = resultSlice
		for _, foreign := range resultSlice {
			if foreign.R == nil {
				foreign.R = &accountMergeHistoryR{}
			}
			foreign.R.TargetAccount = object
		}
		return nil
	}

	for _, foreign := range resultSlice {
		for _, local := range slice {
			if local.AccountID == foreign.TargetAccountID {
				local.R.TargetAccountAccountMergeHistories = append(local.R.TargetAccountAccountMergeHistories, foreign)
				if foreign.R == nil {
					foreign.R = &accountMergeHistoryR{}
				}
				foreign.R.TargetAccount = local
				break
			}
		}
	}

	return nil
}

// LoadCandidateAccountAccountPossibleMatches allows an eager lookup of values, cached into the
// loaded structs of the objects. This is for a 1-M or N-M relationship.
func (accountL) LoadCandidateAccountAccountPossibleMatches(ctx context.Context, e boil.ContextExecutor, singular bool, maybeAccount interface{}, mods queries.Applicator) error {
	var slice []*Account
	var object *Account

	if singular {
		object = maybeAccount.(*Account)
	} else {
		slice = *maybeAccount.(*[]*Account)
	}

	args := make([]interface{}, 0, 1)
	if singular {
		if object.R == nil {
			object.R = &accountR{}
		}
		args = append(args, object.AccountID)
	} else {
	Outer:
		for _, obj := range slice {
			if obj.R == nil {
				obj.R = &accountR{}
			}

			for _, a := range args {
				if a == obj.AccountID {
					continue Outer
				}
			}

			args = append(args, obj.AccountID)
		}
	}

	if len(args) == 0 {
		return nil
	}

	query := NewQuery(
		qm.From(`account.account_possible_matches`),
		qm.WhereIn(`account.account_possible_matches.candidate_account_id in ?`, args...),
	)
	if mods != nil {
		mods.Apply(query)
	}

	results, err := query.QueryContext(ctx, e)
	if err != nil {
		return errors.Wrap(err, "failed to eager load account_possible_matches")
	}

	var resultSlice []*AccountPossibleMatch
	if err = queries.Bind(results, &resultSlice); err != nil {
		return errors.Wrap(err, "failed to bind eager loaded slice account_possible_matches")
	}

	if err = results.Close(); err != nil {
		return errors.Wrap(err, "failed to close results in eager load on account_possible_matches")
	}
	if err = results.Err(); err != nil {
		return errors.Wrap(err, "error occurred during iteration of eager loaded relations for account_possible_matches")
	}

	if len(accountPossibleMatchAfterSelectHooks) != 0 {
		for _, obj := range resultSlice {
			if err := obj.doAfterSelectHooks(ctx, e); err != nil {
				return err
			}
		}
	}
	if singular {
		object.R.CandidateAccountAccountPossibleMatches = resultSlice
		for _, foreign := range resultSlice {
			if foreign.R == nil {
				foreign.R = &accountPossibleMatchR{}
			}
			foreign.R.CandidateAccount = object
		}
		return nil
	}

	for _, foreign := range resultSlice {
		for _, local := range slice {
			if local.AccountID == foreign.CandidateAccountID {
				local.R.CandidateAccountAccountPossibleMatches = append(local.R.CandidateAccountAccountPossibleMatches, foreign)
				if foreign.R == nil {
					foreign.R = &accountPossibleMatchR{}
				}
				foreign.R.CandidateAccount = local
				break
			}
		}
	}

	return nil
}

// LoadMatchedAccountAccountPossibleMatches allows an eager lookup of values, cached into the
// loaded structs of the objects. This is for a 1-M or N-M relationship.
func (accountL) LoadMatchedAccountAccountPossibleMatches(ctx context.Context, e boil.ContextExecutor, singular bool, maybeAccount interface{}, mods queries.Applicator) error {
	var slice []*Account
	var object *Account

	if singular {
		object = maybeAccount.(*Account)
	} else {
		slice = *maybeAccount.(*[]*Account)
	}

	args := make([]interface{}, 0, 1)
	if singular {
		if object.R == nil {
			object.R = &accountR{}
		}
		args = append(args, object.AccountID)
	} else {
	Outer:
		for _, obj := range slice {
			if obj.R == nil {
				obj.R = &accountR{}
			}

			for _, a := range args {
				if a == obj.AccountID {
					continue Outer
				}
			}

			args = append(args, obj.AccountID)
		}
	}

	if len(args) == 0 {
		return nil
	}

	query := NewQuery(
		qm.From(`account.account_possible_matches`),
		qm.WhereIn(`account.account_possible_matches.matched_account_id in ?`, args...),
	)
	if mods != nil {
		mods.Apply(query)
	}

	results, err := query.QueryContext(ctx, e)
	if err != nil {
		return errors.Wrap(err, "failed to eager load account_possible_matches")
	}

	var resultSlice []*AccountPossibleMatch
	if err = queries.Bind(results, &resultSlice); err != nil {
		return errors.Wrap(err, "failed to bind eager loaded slice account_possible_matches")
	}

	if err = results.Close(); err != nil {
		return errors.Wrap(err, "failed to close results in eager load on account_possible_matches")
	}
	if err = results.Err(); err != nil {
		return errors.Wrap(err, "error occurred during iteration of eager loaded relations for account_possible_matches")
	}

	if len(accountPossibleMatchAfterSelectHooks) != 0 {
		for _, obj := range resultSlice {
			if err := obj.doAfterSelectHooks(ctx, e); err != nil {
				return err
			}
		}
	}
	if singular {
		object.R.MatchedAccountAccountPossibleMatches = resultSlice
		for _, foreign := range resultSlice {
			if foreign.R == nil {
				foreign.R = &accountPossibleMatchR{}
			}
			foreign.R.MatchedAccount = object
		}
		return nil
	}

	for _, foreign := range resultSlice {
		for _, local := range slice {
			if local.AccountID == foreign.MatchedAccountID {
				local.R.MatchedAccountAccountPossibleMatches = append(local.R.MatchedAccountAccountPossibleMatches, foreign)
				if foreign.R == nil {
					foreign.R = &accountPossibleMatchR{}
				}
				foreign.R.MatchedAccount = local
				break
			}
		}
	}

	return nil
}

// LoadAccountPrograms allows an eager lookup of values, cached into the
// loaded structs of the objects. This is for a 1-M or N-M relationship.
func (accountL) LoadAccountPrograms(ctx context.Context, e boil.ContextExecutor, singular bool, maybeAccount interface{}, mods queries.Applicator) error {
	var slice []*Account
	var object *Account

	if singular {
		object = maybeAccount.(*Account)
	} else {
		slice = *maybeAccount.(*[]*Account)
	}

	args := make([]interface{}, 0, 1)
	if singular {
		if object.R == nil {
			object.R = &accountR{}
		}
		args = append(args, object.AccountID)
	} else {
	Outer:
		for _, obj := range slice {
			if obj.R == nil {
				obj.R = &accountR{}
			}

			for _, a := range args {
				if a == obj.AccountID {
					continue Outer
				}
			}

			args = append(args, obj.AccountID)
		}
	}

	if len(args) == 0 {
		return nil
	}

	query := NewQuery(
		qm.From(`account.account_programs`),
		qm.WhereIn(`account.account_programs.account_id in ?`, args...),
	)
	if mods != nil {
		mods.Apply(query)
	}

	results, err := query.QueryContext(ctx, e)
	if err != nil {
		return errors.Wrap(err, "failed to eager load account_programs")
	}

	var resultSlice []*AccountProgram
	if err = queries.Bind(results, &resultSlice); err != nil {
		return errors.Wrap(err, "failed to bind eager loaded slice account_programs")
	}

	if err = results.Close(); err != nil {
		return errors.Wrap(err, "failed to close results in eager load on account_programs")
	}
	if err = results.Err(); err != nil {
		return errors.Wrap(err, "error occurred during iteration of eager loaded relations for account_programs")
	}

	if len(accountProgramAfterSelectHooks) != 0 {
		for _, obj := range resultSlice {
			if err := obj.doAfterSelectHooks(ctx, e); err != nil {
				return err
			}
		}
	}
	if singular {
		object.R.AccountPrograms = resultSlice
		for _, foreign := range resultSlice {
			if foreign.R == nil {
				foreign.R = &accountProgramR{}
			}
			foreign.R.Account = object
		}
		return nil
	}

	for _, foreign := range resultSlice {
		for _, local := range slice {
			if local.AccountID == foreign.AccountID {
				local.R.AccountPrograms = append(local.R.AccountPrograms, foreign)
				if foreign.R == nil {
					foreign.R = &accountProgramR{}
				}
				foreign.R.Account = local
				break
			}
		}
	}

	return nil
}

// LoadContacts allows an eager lookup of values, cached into the
// loaded structs of the objects. This is for a 1-M or N-M relationship.
func (accountL) LoadContacts(ctx context.Context, e boil.ContextExecutor, singular bool, maybeAccount interface{}, mods queries.Applicator) error {
	var slice []*Account
	var object *Account

	if singular {
		object = maybeAccount.(*Account)
	} else {
		slice = *maybeAccount.(*[]*Account)
	}

	args := make([]interface{}, 0, 1)
	if singular {
		if object.R == nil {
			object.R = &accountR{}
		}
		args = append(args, object.AccountID)
	} else {
	Outer:
		for _, obj := range slice {
			if obj.R == nil {
				obj.R = &accountR{}
			}

			for _, a := range args {
				if a == obj.AccountID {
					continue Outer
				}
			}

			args = append(args, obj.AccountID)
		}
	}

	if len(args) == 0 {
		return nil
	}

	query := NewQuery(
		qm.From(`account.contacts`),
		qm.WhereIn(`account.contacts.account_id in ?`, args...),
	)
	if mods != nil {
		mods.Apply(query)
	}

	results, err := query.QueryContext(ctx, e)
	if err != nil {
		return errors.Wrap(err, "failed to eager load contacts")
	}

	var resultSlice []*Contact
	if err = queries.Bind(results, &resultSlice); err != nil {
		return errors.Wrap(err, "failed to bind eager loaded slice contacts")
	}

	if err = results.Close(); err != nil {
		return errors.Wrap(err, "failed to close results in eager load on contacts")
	}
	if err = results.Err(); err != nil {
		return errors.Wrap(err, "error occurred during iteration of eager loaded relations for contacts")
	}

	if len(contactAfterSelectHooks) != 0 {
		for _, obj := range resultSlice {
			if err := obj.doAfterSelectHooks(ctx, e); err != nil {
				return err
			}
		}
	}
	if singular {
		object.R.Contacts = resultSlice
		for _, foreign := range resultSlice {
			if foreign.R == nil {
				foreign.R = &contactR{}
			}
			foreign.R.Account = object
		}
		return nil
	}

	for _, foreign := range resultSlice {
		for _, local := range slice {
			if local.AccountID == foreign.AccountID {
				local.R.Contacts = append(local.R.Contacts, foreign)
				if foreign.R == nil {
					foreign.R = &contactR{}
				}
				foreign.R.Account = local
				break
			}
		}
	}

	return nil
}

// AddCanonicalAccountAccountAliases adds the given related objects to the existing relationships
// of the account, optionally inserting them as new records.
// Appends related to o.R.CanonicalAccountAccountAliases.
// Sets related.R.CanonicalAccount appropriately.
func (o *Account) AddCanonicalAccountAccountAliases(ctx context.Context, exec boil.ContextExecutor, insert bool, related ...*AccountAlias) error {
	var err error
	for _, rel := range related {
		if insert {
			rel.CanonicalAccountID = o.AccountID
			if err = rel.Insert(ctx, exec, boil.Infer()); err != nil {
				return errors.Wrap(err, "failed to insert into foreign table")
			}
		} else {
			updateQuery := fmt.Sprintf(
				"UPDATE \"account\".\"account_aliases\" SET %s WHERE %s",
				strmangle.SetParamNames("\"", "\"", 1, []string{"canonical_account_id"}),
				strmangle.WhereClause("\"", "\"", 2, accountAliasPrimaryKeyColumns),
			)
			values := []interface{}{o.AccountID, rel.AliasAccountID}

			if boil.IsDebug(ctx) {
				writer := boil.DebugWriterFrom(ctx)
				fmt.Fprintln(writer, updateQuery)
				fmt.Fprintln(writer, values)
			}
			if _, err = exec.ExecContext(ctx, updateQuery, values...); err != nil {
				return errors.Wrap(err, "failed to update foreign table")
			}

			rel.CanonicalAccountID = o.AccountID
		}
	}

	if o.R == nil {
		o.R = &accountR{
			CanonicalAccountAccountAliases: related,
		}
	} else {
		o.R.CanonicalAccountAccountAliases = append(o.R.CanonicalAccountAccountAliases, related...)
	}

	for _, rel := range related {
		if rel.R == nil {
			rel.R = &accountAliasR{
				CanonicalAccount: o,
			}
		} else {
			rel.R.CanonicalAccount = o
		}
	}
	return nil
}

// AddAccountIdentifiers adds the given related objects to the existing relationships
// of the account, optionally inserting them as new records.
// Appends related to o.R.AccountIdentifiers.
// Sets related.R.Account appropriately.
func (o *Account) AddAccountIdentifiers(ctx context.Context, exec boil.ContextExecutor, insert bool, related ...*AccountIdentifier) error {
	var err error
	for _, rel := range related {
		if insert {
			rel.AccountID = o.AccountID
			if err = rel.Insert(ctx, exec, boil.Infer()); err != nil {
				return errors.Wrap(err, "failed to insert into foreign table")
			}
		} else {
			updateQuery := fmt.Sprintf(
				"UPDATE \"account\".\"account_identifiers\" SET %s WHERE %s",
				strmangle.SetParamNames("\"", "\"", 1, []string{"account_id"}),
				strmangle.WhereClause("\"", "\"", 2, accountIdentifierPrimaryKeyColumns),
			)
			values := []interface{}{o.AccountID, rel.AccountIdentifierID}

			if boil.IsDebug(ctx) {
				writer := boil.DebugWriterFrom(ctx)
				fmt.Fprintln(writer, updateQuery)
				fmt.Fprintln(writer, values)
			}
			if _, err = exec.ExecContext(ctx, updateQuery, values...); err != nil {
				return errors.Wrap(err, "failed to update foreign table")
			}

			rel.AccountID = o.AccountID
		}
	}

	if o.R == nil {
		o.R = &accountR{
			AccountIdentifiers: related,
		}
	} else {
		o.R.AccountIdentifiers = append(o.R.AccountIdentifiers, related...)
	}

	for _, rel := range related {
		if rel.R == nil {
			rel.R = &accountIdentifierR{
				Account: o,
			}
		} else {
			rel.R.Account = o
		}
	}
	return nil
}

// AddSourceAccountAccountMergeHistories adds the given related objects to the existing relationships
// of the account, optionally inserting them as new records.
// Appends related to o.R.SourceAccountAccountMergeHistories.
// Sets related.R.SourceAccount appropriately.
func (o *Account) AddSourceAccountAccountMergeHistories(ctx context.Context, exec boil.ContextExecutor, insert bool, related ...*AccountMergeHistory) error {
	var err error
	for _, rel := range related {
		if insert {
			rel.SourceAccountID = o.AccountID
			if err = rel.Insert(ctx, exec, boil.Infer()); err != nil {
				return errors.Wrap(err, "failed to insert into foreign table")
			}
		} else {
			updateQuery := fmt.Sprintf(
				"UPDATE \"account\".\"account_merge_history\" SET %s WHERE %s",
				strmangle.SetParamNames("\"", "\"", 1, []string{"source_account_id"}),
				strmangle.WhereClause("\"", "\"", 2, accountMergeHistoryPrimaryKeyColumns),
			)
			values := []interface{}{o.AccountID, rel.MergeID}

			if boil.IsDebug(ctx) {
				writer := boil.DebugWriterFrom(ctx)
				fmt.Fprintln(writer, updateQuery)
				fmt.Fprintln(writer, values)
			}
			if _, err = exec.ExecContext(ctx, updateQuery, values...); err != nil {
				return errors.Wrap(err, "failed to update foreign table")
			}

			rel.SourceAccountID = o.AccountID
		}
	}

	if o.R == nil {
		o.R = &accountR{
			SourceAccountAccountMergeHistories: related,
		}
	} else {
		o.R.SourceAccountAccountMergeHistories = append(o.R.SourceAccountAccountMergeHistories, related...)
	}

	for _, rel := range related {
		if rel.R == nil {
			rel.R = &accountMergeHistoryR{
				SourceAccount: o,
			}
		} else {
			rel.R.SourceAccount = o
		}
	}
	return nil
}

// AddTargetAccountAccountMergeHistories adds the given related objects to the existing relationships
// of the account, optionally inserting them as new records.
// Appends related to o.R.TargetAccountAccountMergeHistories.
// Sets related.R.TargetAccount appropriately.
func (o *Account) AddTargetAccountAccountMergeHistories(ctx context.Context, exec boil.ContextExecutor, insert bool, related ...*AccountMergeHistory) error {
	var err error
	for _, rel := range related {
		if insert {
			rel.TargetAccountID = o.AccountID
			if err = rel.Insert(ctx, exec, boil.Infer()); err != nil {
				return errors.Wrap(err, "failed to insert into foreign table")
			}
		} else {
			updateQuery := fmt.Sprintf(
				"UPDATE \"account\".\"account_merge_history\" SET %s WHERE %s",
				strmangle.SetParamNames("\"", "\"", 1, []string{"target_account_id"}),
				strmangle.WhereClause("\"", "\"", 2, accountMergeHistoryPrimaryKeyColumns),
			)
			values := []interface{}{o.AccountID, rel.MergeID}

			if boil.IsDebug(ctx) {
				writer := boil.DebugWriterFrom(ctx)
				fmt.Fprintln(writer, updateQuery)
				fmt.Fprintln(writer, values)
			}
			if _, err = exec.ExecContext(ctx, updateQuery, values...); err != nil {
				return errors.Wrap(err, "failed to update foreign table")
			}

			rel.TargetAccountID = o.AccountID
		}
	}

	if o.R == nil {
		o.R = &accountR{
			TargetAccountAccountMergeHistories: related,
		}
	} else {
		o.R.TargetAccountAccountMergeHistories = append(o.R.TargetAccountAccountMergeHistories, related...)
	}

	for _, rel := range related {
		if rel.R == nil {
			rel.R = &accountMergeHistoryR{
				TargetAccount: o,
			}
		} else {
			rel.R.TargetAccount = o
		}
	}
	return nil
}

// AddCandidateAccountAccountPossibleMatches adds the given related objects to the existing relationships
// of the account, optionally inserting them as new records.
// Appends related to o.R.CandidateAccountAccountPossibleMatches.
// Sets related.R.CandidateAccount appropriately.
func (o *Account) AddCandidateAccountAccountPossibleMatches(ctx context.Context, exec boil.ContextExecutor, insert bool, related ...*AccountPossibleMatch) error {
	var err error
	for _, rel := range related {
		if insert {
			rel.CandidateAccountID = o.AccountID
			if err = rel.Insert(ctx, exec, boil.Infer()); err != nil {
				return errors.Wrap(err, "failed to insert into foreign table")
			}
		} else {
			updateQuery := fmt.Sprintf(
				"UPDATE \"account\".\"account_possible_matches\" SET %s WHERE %s",
				strmangle.SetParamNames("\"", "\"", 1, []string{"candidate_account_id"}),
				strmangle.WhereClause("\"", "\"", 2, accountPossibleMatchPrimaryKeyColumns),
			)
			values := []interface{}{o.AccountID, rel.MatchID}

			if boil.IsDebug(ctx) {
				writer := boil.DebugWriterFrom(ctx)
				fmt.Fprintln(writer, updateQuery)
				fmt.Fprintln(writer, values)
			}
			if _, err = exec.ExecContext(ctx, updateQuery, values...); err != nil {
				return errors.Wrap(err, "failed to update foreign table")
			}

			rel.CandidateAccountID = o.AccountID
		}
	}

	if o.R == nil {
		o.R = &accountR{
			CandidateAccountAccountPossibleMatches: related,
		}
	} else {
		o.R.CandidateAccountAccountPossibleMatches = append(o.R.CandidateAccountAccountPossibleMatches, related...)
	}

	for _, rel := range related {
		if rel.R == nil {
			rel.R = &accountPossibleMatchR{
				CandidateAccount: o,
			}
		} else {
			rel.R.CandidateAccount = o
		}
	}
	return nil
}

// AddMatchedAccountAccountPossibleMatches adds the given related objects to the existing relationships
// of the account, optionally inserting them as new records.
// Appends related to o.R.MatchedAccountAccountPossibleMatches.
// Sets related.R.MatchedAccount appropriately.
func (o *Account) AddMatchedAccountAccountPossibleMatches(ctx context.Context, exec boil.ContextExecutor, insert bool, related ...*AccountPossibleMatch) error {
	var err error
	for _, rel := range related {
		if insert {
			rel.MatchedAccountID = o.AccountID
			if err = rel.Insert(ctx, exec, boil.Infer()); err != nil {
				return errors.Wrap(err, "failed to insert into foreign table")
			}
		} else {
			updateQuery := fmt.Sprintf(
				"UPDATE \"account\".\"account_possible_matches\" SET %s WHERE %s",
				strmangle.SetParamNames("\"", "\"", 1, []string{"matched_account_id"}),
				strmangle.WhereClause("\"", "\"", 2, accountPossibleMatchPrimaryKeyColumns),
			)
			values := []interface{}{o.AccountID, rel.MatchID}

			if boil.IsDebug(ctx) {
				writer := boil.DebugWriterFrom(ctx)
				fmt.Fprintln(writer, updateQuery)
				fmt.Fprintln(writer, values)
			}
			if _, err = exec.ExecContext(ctx, updateQuery, values...); err != nil {
				return errors.Wrap(err, "failed to update foreign table")
			}

			rel.MatchedAccountID = o.AccountID
		}
	}

	if o.R == nil {
		o.R = &accountR{
			MatchedAccountAccountPossibleMatches: related,
		}
	} else {
		o.R.MatchedAccountAccountPossibleMatches = append(o.R.MatchedAccountAccountPossibleMatches, related...)
	}

	for _, rel := range related {
		if rel.R == nil {
			rel.R = &accountPossibleMatchR{
				MatchedAccount: o,
			}
		} else {
			rel.R.MatchedAccount = o
		}
	}
	return nil
}

// AddAccountPrograms adds the given related objects to the existing relationships
// of the account, optionally inserting them as new records.
// Appends related to o.R.AccountPrograms.
// Sets related.R.Account appropriately.
func (o *Account) AddAccountPrograms(ctx context.Context, exec boil.ContextExecutor, insert bool, related ...*AccountProgram) error {
	var err error
	for _, rel := range related {
		if insert {
			rel.AccountID = o.AccountID
			if err = rel.Insert(ctx, exec, boil.Infer()); err != nil {
				return errors.Wrap(err, "failed to insert into foreign table")
			}
		} else {
			updateQuery := fmt.Sprintf(
				"UPDATE \"account\".\"account_programs\" SET %s WHERE %s",
				strmangle.SetParamNames("\"", "\"", 1, []string{"account_id"}),
				strmangle.WhereClause("\"", "\"", 2, accountProgramPrimaryKeyColumns),
			)
			values := []interface{}{o.AccountID, rel.AccountProgramID}

			if boil.IsDebug(ctx) {
				writer := boil.DebugWriterFrom(ctx)
				fmt.Fprintln(writer, updateQuery)
				fmt.Fprintln(writer, values)
			}
			if _, err = exec.ExecContext(ctx, updateQuery, values...); err != nil {
				return errors.Wrap(err, "failed to update foreign table")
			}

			rel.AccountID = o.AccountID
		}
	}

	if o.R == nil {
		o.R = &accountR{
			AccountPrograms: related,
		}
	} else {
		o.R.AccountPrograms = append(o.R.AccountPrograms, related...)
	}

	for _, rel := range related {
		if rel.R == nil {
			rel.R = &accountProgramR{
				Account: o,
			}
		} else {
			rel.R.Account = o
		}
	}
	return nil
}

// AddContacts adds the given related objects to the existing relationships
// of the account, optionally inserting them as new records.
// Appends related to o.R.Contacts.
// Sets related.R.Account appropriately.
func (o *Account) AddContacts(ctx context.Context, exec boil.ContextExecutor, insert bool, related ...*Contact) error {
	var err error
	for _, rel := range related {
		if insert {
			rel.AccountID = o.AccountID
			if err = rel.Insert(ctx, exec, boil.Infer()); err != nil {
				return errors.Wrap(err, "failed to insert into foreign table")
			}
		} else {
			updateQuery := fmt.Sprintf(
				"UPDATE \"account\".\"contacts\" SET %s WHERE %s",
				strmangle.SetParamNames("\"", "\"", 1, []string{"account_id"}),
				strmangle.WhereClause("\"", "\"", 2, contactPrimaryKeyColumns),
			)
			values := []interface{}{o.AccountID, rel.ContactID}

			if boil.IsDebug(ctx) {
				writer := boil.DebugWriterFrom(ctx)
				fmt.Fprintln(writer, updateQuery)
				fmt.Fprintln(writer, values)
			}
			if _, err = exec.ExecContext(ctx, updateQuery, values...); err != nil {
				return errors.Wrap(err, "failed to update foreign table")
			}

			rel.AccountID = o.AccountID
		}
	}

	if o.R == nil {
		o.R = &accountR{
			Contacts: related,
		}
	} else {
		o.R.Contacts = append(o.R.Contacts, related...)
	}

	for _, rel := range related {
		if rel.R == nil {
			rel.R = &contactR{
				Account: o,
			}
		} else {
			rel.R.Account = o
		}
	}
	return nil
}

// Accounts retrieves all the records using an executor.
func Accounts(mods ...qm.QueryMod) accountQuery {
	mods = append(mods, qm.From("\"account\".\"accounts\""))
	q := NewQuery(mods...)
	if len(queries.GetSelect(q)) == 0 {
		queries.SetSelect(q, []string{"\"account\".\"accounts\".*"})
	}

	return accountQuery{q}
}

// FindAccount retrieves a single record by ID with an executor.
// If selectCols is empty Find will return all columns.
func FindAccount(ctx context.Context, exec boil.ContextExecutor, accountID string, selectCols ...string) (*Account, error) {
	accountObj := &Account{}

	sel := "*"
	if len(selectCols) > 0 {
		sel = strings.Join(strmangle.IdentQuoteSlice(dialect.LQ, dialect.RQ, selectCols), ",")
	}
	query := fmt.Sprintf(
		"select %s from \"account\".\"accounts\" where \"account_id\"=$1", sel,
	)

	q := queries.Raw(query, accountID)

	err := q.Bind(ctx, exec, accountObj)
	if err != nil {
		if errors.Is(err, sql.ErrNoRows) {
			return nil, sql.ErrNoRows
		}
		return nil, errors.Wrap(err, "account: unable to select from accounts")
	}

	if err = accountObj.doAfterSelectHooks(ctx, exec); err != nil {
		return accountObj, err
	}

	return accountObj, nil
}

// Insert a single record using an executor.
// See boil.Columns.InsertColumnSet documentation to understand column list inference for inserts.
func (o *Account) Insert(ctx context.Context, exec boil.ContextExecutor, columns boil.Columns) error {
	if o == nil {
		return errors.New("account: no accounts provided for insertion")
	}

	var err error
	if !boil.TimestampsAreSkipped(ctx) {
		currTime := time.Now().In(boil.GetLocation())

		if o.CreatedAt.IsZero() {
			o.CreatedAt = currTime
		}
		if o.UpdatedAt.IsZero() {
			o.UpdatedAt = currTime
		}
	}

	if err := o.doBeforeInsertHooks(ctx, exec); err != nil {
		return err
	}

	nzDefaults := queries.NonZeroDefaultSet(accountColumnsWithDefault, o)

	key := makeCacheKey(columns, nzDefaults)
	accountInsertCacheMut.RLock()
	cache, cached := accountInsertCache[key]
	accountInsertCacheMut.RUnlock()

	if !cached {
		wl, returnColumns := columns.InsertColumnSet(
			accountAllColumns,
			accountColumnsWithDefault,
			accountColumnsWithoutDefault,
			nzDefaults,
		)

		cache.valueMapping, err = queries.BindMapping(accountType, accountMapping, wl)
		if err != nil {
			return err
		}
		cache.retMapping, err = queries.BindMapping(accountType, accountMapping, returnColumns)
		if err != nil {
			return err
		}
		if len(wl) != 0 {
			cache.query = fmt.Sprintf("INSERT INTO \"account\".\"accounts\" (\"%s\") %%sVALUES (%s)%%s", strings.Join(wl, "\",\""), strmangle.Placeholders(dialect.UseIndexPlaceholders, len(wl), 1, 1))
		} else {
			cache.query = "INSERT INTO \"account\".\"accounts\" %sDEFAULT VALUES%s"
		}

		var queryOutput, queryReturning string

		if len(cache.retMapping) != 0 {
			queryReturning = fmt.Sprintf(" RETURNING \"%s\"", strings.Join(returnColumns, "\",\""))
		}

		cache.query = fmt.Sprintf(cache.query, queryOutput, queryReturning)
	}

	value := reflect.Indirect(reflect.ValueOf(o))
	vals := queries.ValuesFromMapping(value, cache.valueMapping)

	if boil.IsDebug(ctx) {
		writer := boil.DebugWriterFrom(ctx)
		fmt.Fprintln(writer, cache.query)
		fmt.Fprintln(writer, vals)
	}

	if len(cache.retMapping) != 0 {
		err = exec.QueryRowContext(ctx, cache.query, vals...).Scan(queries.PtrsFromMapping(value, cache.retMapping)...)
	} else {
		_, err = exec.ExecContext(ctx, cache.query, vals...)
	}

	if err != nil {
		return errors.Wrap(err, "account: unable to insert into accounts")
	}

	if !cached {
		accountInsertCacheMut.Lock()
		accountInsertCache[key] = cache
		accountInsertCacheMut.Unlock()
	}

	return o.doAfterInsertHooks(ctx, exec)
}

// Update uses an executor to update the Account.
// See boil.Columns.UpdateColumnSet documentation to understand column list inference for updates.
// Update does not automatically update the record in case of default values. Use .Reload() to refresh the records.
func (o *Account) Update(ctx context.Context, exec boil.ContextExecutor, columns boil.Columns) (int64, error) {
	if !boil.TimestampsAreSkipped(ctx) {
		currTime := time.Now().In(boil.GetLocation())

		o.UpdatedAt = currTime
	}

	var err error
	if err = o.doBeforeUpdateHooks(ctx, exec); err != nil {
		return 0, err
	}
	key := makeCacheKey(columns, nil)
	accountUpdateCacheMut.RLock()
	cache, cached := accountUpdateCache[key]
	accountUpdateCacheMut.RUnlock()

	if !cached {
		wl := columns.UpdateColumnSet(
			accountAllColumns,
			accountPrimaryKeyColumns,
		)

		if !columns.IsWhitelist() {
			wl = strmangle.SetComplement(wl, []string{"created_at"})
		}
		if len(wl) == 0 {
			return 0, errors.New("account: unable to update accounts, could not build whitelist")
		}

		cache.query = fmt.Sprintf("UPDATE \"account\".\"accounts\" SET %s WHERE %s",
			strmangle.SetParamNames("\"", "\"", 1, wl),
			strmangle.WhereClause("\"", "\"", len(wl)+1, accountPrimaryKeyColumns),
		)
		cache.valueMapping, err = queries.BindMapping(accountType, accountMapping, append(wl, accountPrimaryKeyColumns...))
		if err != nil {
			return 0, err
		}
	}

	values := queries.ValuesFromMapping(reflect.Indirect(reflect.ValueOf(o)), cache.valueMapping)

	if boil.IsDebug(ctx) {
		writer := boil.DebugWriterFrom(ctx)
		fmt.Fprintln(writer, cache.query)
		fmt.Fprintln(writer, values)
	}
	var result sql.Result
	result, err = exec.ExecContext(ctx, cache.query, values...)
	if err != nil {
		return 0, errors.Wrap(err, "account: unable to update accounts row")
	}

	rowsAff, err := result.RowsAffected()
	if err != nil {
		return 0, errors.Wrap(err, "account: failed to get rows affected by update for accounts")
	}

	if !cached {
		accountUpdateCacheMut.Lock()
		accountUpdateCache[key] = cache
		accountUpdateCacheMut.Unlock()
	}

	return rowsAff, o.doAfterUpdateHooks(ctx, exec)
}

// UpdateAll updates all rows with the specified column values.
func (q accountQuery) UpdateAll(ctx context.Context, exec boil.ContextExecutor, cols M) (int64, error) {
	queries.SetUpdate(q.Query, cols)

	result, err := q.Query.ExecContext(ctx, exec)
	if err != nil {
		return 0, errors.Wrap(err, "account: unable to update all for accounts")
	}

	rowsAff, err := result.RowsAffected()
	if err != nil {
		return 0, errors.Wrap(err, "account: unable to retrieve rows affected for accounts")
	}

	return rowsAff, nil
}

// UpdateAll updates all rows with the specified column values, using an executor.
func (o AccountSlice) UpdateAll(ctx context.Context, exec boil.ContextExecutor, cols M) (int64, error) {
	ln := int64(len(o))
	if ln == 0 {
		return 0, nil
	}

	if len(cols) == 0 {
		return 0, errors.New("account: update all requires at least one column argument")
	}

	colNames := make([]string, len(cols))
	args := make([]interface{}, len(cols))

	i := 0
	for name, value := range cols {
		colNames[i] = name
		args[i] = value
		i++
	}

	// Append all of the primary key values for each column
	for _, obj := range o {
		pkeyArgs := queries.ValuesFromMapping(reflect.Indirect(reflect.ValueOf(obj)), accountPrimaryKeyMapping)
		args = append(args, pkeyArgs...)
	}

	sql := fmt.Sprintf("UPDATE \"account\".\"accounts\" SET %s WHERE %s",
		strmangle.SetParamNames("\"", "\"", 1, colNames),
		strmangle.WhereClauseRepeated(string(dialect.LQ), string(dialect.RQ), len(colNames)+1, accountPrimaryKeyColumns, len(o)))

	if boil.IsDebug(ctx) {
		writer := boil.DebugWriterFrom(ctx)
		fmt.Fprintln(writer, sql)
		fmt.Fprintln(writer, args...)
	}
	result, err := exec.ExecContext(ctx, sql, args...)
	if err != nil {
		return 0, errors.Wrap(err, "account: unable to update all in account slice")
	}

	rowsAff, err := result.RowsAffected()
	if err != nil {
		return 0, errors.Wrap(err, "account: unable to retrieve rows affected all in update all account")
	}
	return rowsAff, nil
}

// Upsert attempts an insert using an executor, and does an update or ignore on conflict.
// See boil.Columns documentation for how to properly use updateColumns and insertColumns.
func (o *Account) Upsert(ctx context.Context, exec boil.ContextExecutor, updateOnConflict bool, conflictColumns []string, updateColumns, insertColumns boil.Columns) error {
	if o == nil {
		return errors.New("account: no accounts provided for upsert")
	}
	if !boil.TimestampsAreSkipped(ctx) {
		currTime := time.Now().In(boil.GetLocation())

		if o.CreatedAt.IsZero() {
			o.CreatedAt = currTime
		}
		o.UpdatedAt = currTime
	}

	if err := o.doBeforeUpsertHooks(ctx, exec); err != nil {
		return err
	}

	nzDefaults := queries.NonZeroDefaultSet(accountColumnsWithDefault, o)

	// Build cache key in-line uglily - mysql vs psql problems
	buf := strmangle.GetBuffer()
	if updateOnConflict {
		buf.WriteByte('t')
	} else {
		buf.WriteByte('f')
	}
	buf.WriteByte('.')
	for _, c := range conflictColumns {
		buf.WriteString(c)
	}
	buf.WriteByte('.')
	buf.WriteString(strconv.Itoa(updateColumns.Kind))
	for _, c := range updateColumns.Cols {
		buf.WriteString(c)
	}
	buf.WriteByte('.')
	buf.WriteString(strconv.Itoa(insertColumns.Kind))
	for _, c := range insertColumns.Cols {
		buf.WriteString(c)
	}
	buf.WriteByte('.')
	for _, c := range nzDefaults {
		buf.WriteString(c)
	}
	key := buf.String()
	strmangle.PutBuffer(buf)

	accountUpsertCacheMut.RLock()
	cache, cached := accountUpsertCache[key]
	accountUpsertCacheMut.RUnlock()

	var err error

	if !cached {
		insert, ret := insertColumns.InsertColumnSet(
			accountAllColumns,
			accountColumnsWithDefault,
			accountColumnsWithoutDefault,
			nzDefaults,
		)

		update := updateColumns.UpdateColumnSet(
			accountAllColumns,
			accountPrimaryKeyColumns,
		)

		if updateOnConflict && len(update) == 0 {
			return errors.New("account: unable to upsert accounts, could not build update column list")
		}

		conflict := conflictColumns
		if len(conflict) == 0 {
			conflict = make([]string, len(accountPrimaryKeyColumns))
			copy(conflict, accountPrimaryKeyColumns)
		}
		cache.query = buildUpsertQueryPostgres(dialect, "\"account\".\"accounts\"", updateOnConflict, ret, update, conflict, insert)

		cache.valueMapping, err = queries.BindMapping(accountType, accountMapping, insert)
		if err != nil {
			return err
		}
		if len(ret) != 0 {
			cache.retMapping, err = queries.BindMapping(accountType, accountMapping, ret)
			if err != nil {
				return err
			}
		}
	}

	value := reflect.Indirect(reflect.ValueOf(o))
	vals := queries.ValuesFromMapping(value, cache.valueMapping)
	var returns []interface{}
	if len(cache.retMapping) != 0 {
		returns = queries.PtrsFromMapping(value, cache.retMapping)
	}

	if boil.IsDebug(ctx) {
		writer := boil.DebugWriterFrom(ctx)
		fmt.Fprintln(writer, cache.query)
		fmt.Fprintln(writer, vals)
	}
	if len(cache.retMapping) != 0 {
		err = exec.QueryRowContext(ctx, cache.query, vals...).Scan(returns...)
		if errors.Is(err, sql.ErrNoRows) {
			err = nil // Postgres doesn't return anything when there's no update
		}
	} else {
		_, err = exec.ExecContext(ctx, cache.query, vals...)
	}
	if err != nil {
		return errors.Wrap(err, "account: unable to upsert accounts")
	}

	if !cached {
		accountUpsertCacheMut.Lock()
		accountUpsertCache[key] = cache
		accountUpsertCacheMut.Unlock()
	}

	return o.doAfterUpsertHooks(ctx, exec)
}

// Delete deletes a single Account record with an executor.
// Delete will match against the primary key column to find the record to delete.
func (o *Account) Delete(ctx context.Context, exec boil.ContextExecutor) (int64, error) {
	if o == nil {
		return 0, errors.New("account: no Account provided for delete")
	}

	if err := o.doBeforeDeleteHooks(ctx, exec); err != nil {
		return 0, err
	}

	args := queries.ValuesFromMapping(reflect.Indirect(reflect.ValueOf(o)), accountPrimaryKeyMapping)
	sql := "DELETE FROM \"account\".\"accounts\" WHERE \"account_id\"=$1"

	if boil.IsDebug(ctx) {
		writer := boil.DebugWriterFrom(ctx)
		fmt.Fprintln(writer, sql)
		fmt.Fprintln(writer, args...)
	}
	result, err := exec.ExecContext(ctx, sql, args...)
	if err != nil {
		return 0, errors.Wrap(err, "account: unable to delete from accounts")
	}

	rowsAff, err := result.RowsAffected()
	if err != nil {
		return 0, errors.Wrap(err, "account: failed to get rows affected by delete for accounts")
	}

	if err := o.doAfterDeleteHooks(ctx, exec); err != nil {
		return 0, err
	}

	return rowsAff, nil
}

// DeleteAll deletes all matching rows.
func (q accountQuery) DeleteAll(ctx context.Context, exec boil.ContextExecutor) (int64, error) {
	if q.Query == nil {
		return 0, errors.New("account: no accountQuery provided for delete all")
	}

	queries.SetDelete(q.Query)

	result, err := q.Query.ExecContext(ctx, exec)
	if err != nil {
		return 0, errors.Wrap(err, "account: unable to delete all from accounts")
	}

	rowsAff, err := result.RowsAffected()
	if err != nil {
		return 0, errors.Wrap(err, "account: failed to get rows affected by deleteall for accounts")
	}

	return rowsAff, nil
}

// DeleteAll deletes all rows in the slice, using an executor.
func (o AccountSlice) DeleteAll(ctx context.Context, exec boil.ContextExecutor) (int64, error) {
	if len(o) == 0 {
		return 0, nil
	}

	if len(accountBeforeDeleteHooks) != 0 {
		for _, obj := range o {
			if err := obj.doBeforeDeleteHooks(ctx, exec); err != nil {
				return 0, err
			}
		}
	}

	var args []interface{}
	for _, obj := range o {
		pkeyArgs := queries.ValuesFromMapping(reflect.Indirect(reflect.ValueOf(obj)), accountPrimaryKeyMapping)
		args = append(args, pkeyArgs...)
	}

	sql := "DELETE FROM \"account\".\"accounts\" WHERE " +
		strmangle.WhereClauseRepeated(string(dialect.LQ), string(dialect.RQ), 1, accountPrimaryKeyColumns, len(o))

	if boil.IsDebug(ctx) {
		writer := boil.DebugWriterFrom(ctx)
		fmt.Fprintln(writer, sql)
		fmt.Fprintln(writer, args)
	}
	result, err := exec.ExecContext(ctx, sql, args...)
	if err != nil {
		return 0, errors.Wrap(err, "account: unable to delete all from account slice")
	}

	rowsAff, err := result.RowsAffected()
	if err != nil {
		return 0, errors.Wrap(err, "account: failed to get rows affected by deleteall for accounts")
	}

	if len(accountAfterDeleteHooks) != 0 {
		for _, obj := range o {
			if err := obj.doAfterDeleteHooks(ctx, exec); err != nil {
				return 0, err
			}
		}
	}

	return rowsAff, nil
}

// Reload refetches the object from the database
// using the primary keys with an executor.
func (o *Account) Reload(ctx context.Context, exec boil.ContextExecutor) error {
	ret, err := FindAccount(ctx, exec, o.AccountID)
	if err != nil {
		return err
	}

	*o = *ret
	return nil
}

// ReloadAll refetches every row with matching primary key column values
// and overwrites the original object slice with the newly updated slice.
func (o *AccountSlice) ReloadAll(ctx context.Context, exec boil.ContextExecutor) error {
	if o == nil || len(*o) == 0 {
		return nil
	}

	slice := AccountSlice{}
	var args []interface{}
	for _, obj := range *o {
		pkeyArgs := queries.ValuesFromMapping(reflect.Indirect(reflect.ValueOf(obj)), accountPrimaryKeyMapping)
		args = append(args, pkeyArgs...)
	}

	sql := "SELECT \"account\".\"accounts\".* FROM \"account\".\"accounts\" WHERE " +
		strmangle.WhereClauseRepeated(string(dialect.LQ), string(dialect.RQ), 1, accountPrimaryKeyColumns, len(*o))

	q := queries.Raw(sql, args...)

	err := q.Bind(ctx, exec, &slice)
	if err != nil {
		return errors.Wrap(err, "account: unable to reload all in AccountSlice")
	}

	*o = slice

	return nil
}

// AccountExists checks if the Account row exists.
func AccountExists(ctx context.Context, exec boil.ContextExecutor, accountID string) (bool, error) {
	var exists bool
	sql := "select exists(select 1 from \"account\".\"accounts\" where \"account_id\"=$1 limit 1)"

	if boil.IsDebug(ctx) {
		writer := boil.DebugWriterFrom(ctx)
		fmt.Fprintln(writer, sql)
		fmt.Fprintln(writer, accountID)
	}
	row := exec.QueryRowContext(ctx, sql, accountID)

	err := row.Scan(&exists)
	if err != nil {
		return false, errors.Wrap(err, "account: unable to check if accounts exists")
	}

	return exists, nil
}
