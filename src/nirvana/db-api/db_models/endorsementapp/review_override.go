// Code generated by SQLBoiler (https://github.com/volatiletech/sqlboiler). DO NOT EDIT.
// This file is meant to be re-generated in place and/or deleted at any time.

package endorsementapp

import (
	"context"
	"database/sql"
	"fmt"
	"reflect"
	"strconv"
	"strings"
	"sync"
	"time"

	"github.com/friendsofgo/errors"
	"github.com/volatiletech/null/v8"
	"github.com/volatiletech/sqlboiler/v4/boil"
	"github.com/volatiletech/sqlboiler/v4/queries"
	"github.com/volatiletech/sqlboiler/v4/queries/qm"
	"github.com/volatiletech/sqlboiler/v4/queries/qmhelper"
	"github.com/volatiletech/strmangle"
)

// ReviewOverride is an object representing the database table.
type ReviewOverride struct {
	ID               string      `boil:"id" json:"id" toml:"id" yaml:"id"`
	ReviewID         string      `boil:"review_id" json:"review_id" toml:"review_id" yaml:"review_id"`
	OriginalChangeID null.String `boil:"original_change_id" json:"original_change_id,omitempty" toml:"original_change_id" yaml:"original_change_id,omitempty"`
	ChangeData       null.JSON   `boil:"change_data" json:"change_data,omitempty" toml:"change_data" yaml:"change_data,omitempty"`
	IsActive         bool        `boil:"is_active" json:"is_active" toml:"is_active" yaml:"is_active"`
	CreatedAt        time.Time   `boil:"created_at" json:"created_at" toml:"created_at" yaml:"created_at"`
	UpdatedAt        time.Time   `boil:"updated_at" json:"updated_at" toml:"updated_at" yaml:"updated_at"`

	R *reviewOverrideR `boil:"" json:"" toml:"" yaml:""`
	L reviewOverrideL  `boil:"-" json:"-" toml:"-" yaml:"-"`
}

var ReviewOverrideColumns = struct {
	ID               string
	ReviewID         string
	OriginalChangeID string
	ChangeData       string
	IsActive         string
	CreatedAt        string
	UpdatedAt        string
}{
	ID:               "id",
	ReviewID:         "review_id",
	OriginalChangeID: "original_change_id",
	ChangeData:       "change_data",
	IsActive:         "is_active",
	CreatedAt:        "created_at",
	UpdatedAt:        "updated_at",
}

var ReviewOverrideTableColumns = struct {
	ID               string
	ReviewID         string
	OriginalChangeID string
	ChangeData       string
	IsActive         string
	CreatedAt        string
	UpdatedAt        string
}{
	ID:               "review_override.id",
	ReviewID:         "review_override.review_id",
	OriginalChangeID: "review_override.original_change_id",
	ChangeData:       "review_override.change_data",
	IsActive:         "review_override.is_active",
	CreatedAt:        "review_override.created_at",
	UpdatedAt:        "review_override.updated_at",
}

// Generated where

var ReviewOverrideWhere = struct {
	ID               whereHelperstring
	ReviewID         whereHelperstring
	OriginalChangeID whereHelpernull_String
	ChangeData       whereHelpernull_JSON
	IsActive         whereHelperbool
	CreatedAt        whereHelpertime_Time
	UpdatedAt        whereHelpertime_Time
}{
	ID:               whereHelperstring{field: "\"endorsements\".\"review_override\".\"id\""},
	ReviewID:         whereHelperstring{field: "\"endorsements\".\"review_override\".\"review_id\""},
	OriginalChangeID: whereHelpernull_String{field: "\"endorsements\".\"review_override\".\"original_change_id\""},
	ChangeData:       whereHelpernull_JSON{field: "\"endorsements\".\"review_override\".\"change_data\""},
	IsActive:         whereHelperbool{field: "\"endorsements\".\"review_override\".\"is_active\""},
	CreatedAt:        whereHelpertime_Time{field: "\"endorsements\".\"review_override\".\"created_at\""},
	UpdatedAt:        whereHelpertime_Time{field: "\"endorsements\".\"review_override\".\"updated_at\""},
}

// ReviewOverrideRels is where relationship names are stored.
var ReviewOverrideRels = struct {
	OriginalChange string
	Review         string
}{
	OriginalChange: "OriginalChange",
	Review:         "Review",
}

// reviewOverrideR is where relationships are stored.
type reviewOverrideR struct {
	OriginalChange *Change `boil:"OriginalChange" json:"OriginalChange" toml:"OriginalChange" yaml:"OriginalChange"`
	Review         *Review `boil:"Review" json:"Review" toml:"Review" yaml:"Review"`
}

// NewStruct creates a new relationship struct
func (*reviewOverrideR) NewStruct() *reviewOverrideR {
	return &reviewOverrideR{}
}

// reviewOverrideL is where Load methods for each relationship are stored.
type reviewOverrideL struct{}

var (
	reviewOverrideAllColumns            = []string{"id", "review_id", "original_change_id", "change_data", "is_active", "created_at", "updated_at"}
	reviewOverrideColumnsWithoutDefault = []string{"id", "review_id"}
	reviewOverrideColumnsWithDefault    = []string{"original_change_id", "change_data", "is_active", "created_at", "updated_at"}
	reviewOverridePrimaryKeyColumns     = []string{"id"}
	reviewOverrideGeneratedColumns      = []string{}
)

type (
	// ReviewOverrideSlice is an alias for a slice of pointers to ReviewOverride.
	// This should almost always be used instead of []ReviewOverride.
	ReviewOverrideSlice []*ReviewOverride
	// ReviewOverrideHook is the signature for custom ReviewOverride hook methods
	ReviewOverrideHook func(context.Context, boil.ContextExecutor, *ReviewOverride) error

	reviewOverrideQuery struct {
		*queries.Query
	}
)

// Cache for insert, update and upsert
var (
	reviewOverrideType                 = reflect.TypeOf(&ReviewOverride{})
	reviewOverrideMapping              = queries.MakeStructMapping(reviewOverrideType)
	reviewOverridePrimaryKeyMapping, _ = queries.BindMapping(reviewOverrideType, reviewOverrideMapping, reviewOverridePrimaryKeyColumns)
	reviewOverrideInsertCacheMut       sync.RWMutex
	reviewOverrideInsertCache          = make(map[string]insertCache)
	reviewOverrideUpdateCacheMut       sync.RWMutex
	reviewOverrideUpdateCache          = make(map[string]updateCache)
	reviewOverrideUpsertCacheMut       sync.RWMutex
	reviewOverrideUpsertCache          = make(map[string]insertCache)
)

var (
	// Force time package dependency for automated UpdatedAt/CreatedAt.
	_ = time.Second
	// Force qmhelper dependency for where clause generation (which doesn't
	// always happen)
	_ = qmhelper.Where
)

var reviewOverrideAfterSelectHooks []ReviewOverrideHook

var reviewOverrideBeforeInsertHooks []ReviewOverrideHook
var reviewOverrideAfterInsertHooks []ReviewOverrideHook

var reviewOverrideBeforeUpdateHooks []ReviewOverrideHook
var reviewOverrideAfterUpdateHooks []ReviewOverrideHook

var reviewOverrideBeforeDeleteHooks []ReviewOverrideHook
var reviewOverrideAfterDeleteHooks []ReviewOverrideHook

var reviewOverrideBeforeUpsertHooks []ReviewOverrideHook
var reviewOverrideAfterUpsertHooks []ReviewOverrideHook

// doAfterSelectHooks executes all "after Select" hooks.
func (o *ReviewOverride) doAfterSelectHooks(ctx context.Context, exec boil.ContextExecutor) (err error) {
	if boil.HooksAreSkipped(ctx) {
		return nil
	}

	for _, hook := range reviewOverrideAfterSelectHooks {
		if err := hook(ctx, exec, o); err != nil {
			return err
		}
	}

	return nil
}

// doBeforeInsertHooks executes all "before insert" hooks.
func (o *ReviewOverride) doBeforeInsertHooks(ctx context.Context, exec boil.ContextExecutor) (err error) {
	if boil.HooksAreSkipped(ctx) {
		return nil
	}

	for _, hook := range reviewOverrideBeforeInsertHooks {
		if err := hook(ctx, exec, o); err != nil {
			return err
		}
	}

	return nil
}

// doAfterInsertHooks executes all "after Insert" hooks.
func (o *ReviewOverride) doAfterInsertHooks(ctx context.Context, exec boil.ContextExecutor) (err error) {
	if boil.HooksAreSkipped(ctx) {
		return nil
	}

	for _, hook := range reviewOverrideAfterInsertHooks {
		if err := hook(ctx, exec, o); err != nil {
			return err
		}
	}

	return nil
}

// doBeforeUpdateHooks executes all "before Update" hooks.
func (o *ReviewOverride) doBeforeUpdateHooks(ctx context.Context, exec boil.ContextExecutor) (err error) {
	if boil.HooksAreSkipped(ctx) {
		return nil
	}

	for _, hook := range reviewOverrideBeforeUpdateHooks {
		if err := hook(ctx, exec, o); err != nil {
			return err
		}
	}

	return nil
}

// doAfterUpdateHooks executes all "after Update" hooks.
func (o *ReviewOverride) doAfterUpdateHooks(ctx context.Context, exec boil.ContextExecutor) (err error) {
	if boil.HooksAreSkipped(ctx) {
		return nil
	}

	for _, hook := range reviewOverrideAfterUpdateHooks {
		if err := hook(ctx, exec, o); err != nil {
			return err
		}
	}

	return nil
}

// doBeforeDeleteHooks executes all "before Delete" hooks.
func (o *ReviewOverride) doBeforeDeleteHooks(ctx context.Context, exec boil.ContextExecutor) (err error) {
	if boil.HooksAreSkipped(ctx) {
		return nil
	}

	for _, hook := range reviewOverrideBeforeDeleteHooks {
		if err := hook(ctx, exec, o); err != nil {
			return err
		}
	}

	return nil
}

// doAfterDeleteHooks executes all "after Delete" hooks.
func (o *ReviewOverride) doAfterDeleteHooks(ctx context.Context, exec boil.ContextExecutor) (err error) {
	if boil.HooksAreSkipped(ctx) {
		return nil
	}

	for _, hook := range reviewOverrideAfterDeleteHooks {
		if err := hook(ctx, exec, o); err != nil {
			return err
		}
	}

	return nil
}

// doBeforeUpsertHooks executes all "before Upsert" hooks.
func (o *ReviewOverride) doBeforeUpsertHooks(ctx context.Context, exec boil.ContextExecutor) (err error) {
	if boil.HooksAreSkipped(ctx) {
		return nil
	}

	for _, hook := range reviewOverrideBeforeUpsertHooks {
		if err := hook(ctx, exec, o); err != nil {
			return err
		}
	}

	return nil
}

// doAfterUpsertHooks executes all "after Upsert" hooks.
func (o *ReviewOverride) doAfterUpsertHooks(ctx context.Context, exec boil.ContextExecutor) (err error) {
	if boil.HooksAreSkipped(ctx) {
		return nil
	}

	for _, hook := range reviewOverrideAfterUpsertHooks {
		if err := hook(ctx, exec, o); err != nil {
			return err
		}
	}

	return nil
}

// AddReviewOverrideHook registers your hook function for all future operations.
func AddReviewOverrideHook(hookPoint boil.HookPoint, reviewOverrideHook ReviewOverrideHook) {
	switch hookPoint {
	case boil.AfterSelectHook:
		reviewOverrideAfterSelectHooks = append(reviewOverrideAfterSelectHooks, reviewOverrideHook)
	case boil.BeforeInsertHook:
		reviewOverrideBeforeInsertHooks = append(reviewOverrideBeforeInsertHooks, reviewOverrideHook)
	case boil.AfterInsertHook:
		reviewOverrideAfterInsertHooks = append(reviewOverrideAfterInsertHooks, reviewOverrideHook)
	case boil.BeforeUpdateHook:
		reviewOverrideBeforeUpdateHooks = append(reviewOverrideBeforeUpdateHooks, reviewOverrideHook)
	case boil.AfterUpdateHook:
		reviewOverrideAfterUpdateHooks = append(reviewOverrideAfterUpdateHooks, reviewOverrideHook)
	case boil.BeforeDeleteHook:
		reviewOverrideBeforeDeleteHooks = append(reviewOverrideBeforeDeleteHooks, reviewOverrideHook)
	case boil.AfterDeleteHook:
		reviewOverrideAfterDeleteHooks = append(reviewOverrideAfterDeleteHooks, reviewOverrideHook)
	case boil.BeforeUpsertHook:
		reviewOverrideBeforeUpsertHooks = append(reviewOverrideBeforeUpsertHooks, reviewOverrideHook)
	case boil.AfterUpsertHook:
		reviewOverrideAfterUpsertHooks = append(reviewOverrideAfterUpsertHooks, reviewOverrideHook)
	}
}

// One returns a single reviewOverride record from the query.
func (q reviewOverrideQuery) One(ctx context.Context, exec boil.ContextExecutor) (*ReviewOverride, error) {
	o := &ReviewOverride{}

	queries.SetLimit(q.Query, 1)

	err := q.Bind(ctx, exec, o)
	if err != nil {
		if errors.Is(err, sql.ErrNoRows) {
			return nil, sql.ErrNoRows
		}
		return nil, errors.Wrap(err, "endorsementapp: failed to execute a one query for review_override")
	}

	if err := o.doAfterSelectHooks(ctx, exec); err != nil {
		return o, err
	}

	return o, nil
}

// All returns all ReviewOverride records from the query.
func (q reviewOverrideQuery) All(ctx context.Context, exec boil.ContextExecutor) (ReviewOverrideSlice, error) {
	var o []*ReviewOverride

	err := q.Bind(ctx, exec, &o)
	if err != nil {
		return nil, errors.Wrap(err, "endorsementapp: failed to assign all query results to ReviewOverride slice")
	}

	if len(reviewOverrideAfterSelectHooks) != 0 {
		for _, obj := range o {
			if err := obj.doAfterSelectHooks(ctx, exec); err != nil {
				return o, err
			}
		}
	}

	return o, nil
}

// Count returns the count of all ReviewOverride records in the query.
func (q reviewOverrideQuery) Count(ctx context.Context, exec boil.ContextExecutor) (int64, error) {
	var count int64

	queries.SetSelect(q.Query, nil)
	queries.SetCount(q.Query)

	err := q.Query.QueryRowContext(ctx, exec).Scan(&count)
	if err != nil {
		return 0, errors.Wrap(err, "endorsementapp: failed to count review_override rows")
	}

	return count, nil
}

// Exists checks if the row exists in the table.
func (q reviewOverrideQuery) Exists(ctx context.Context, exec boil.ContextExecutor) (bool, error) {
	var count int64

	queries.SetSelect(q.Query, nil)
	queries.SetCount(q.Query)
	queries.SetLimit(q.Query, 1)

	err := q.Query.QueryRowContext(ctx, exec).Scan(&count)
	if err != nil {
		return false, errors.Wrap(err, "endorsementapp: failed to check if review_override exists")
	}

	return count > 0, nil
}

// OriginalChange pointed to by the foreign key.
func (o *ReviewOverride) OriginalChange(mods ...qm.QueryMod) changeQuery {
	queryMods := []qm.QueryMod{
		qm.Where("\"id\" = ?", o.OriginalChangeID),
	}

	queryMods = append(queryMods, mods...)

	return Changes(queryMods...)
}

// Review pointed to by the foreign key.
func (o *ReviewOverride) Review(mods ...qm.QueryMod) reviewQuery {
	queryMods := []qm.QueryMod{
		qm.Where("\"id\" = ?", o.ReviewID),
	}

	queryMods = append(queryMods, mods...)

	return Reviews(queryMods...)
}

// LoadOriginalChange allows an eager lookup of values, cached into the
// loaded structs of the objects. This is for an N-1 relationship.
func (reviewOverrideL) LoadOriginalChange(ctx context.Context, e boil.ContextExecutor, singular bool, maybeReviewOverride interface{}, mods queries.Applicator) error {
	var slice []*ReviewOverride
	var object *ReviewOverride

	if singular {
		object = maybeReviewOverride.(*ReviewOverride)
	} else {
		slice = *maybeReviewOverride.(*[]*ReviewOverride)
	}

	args := make([]interface{}, 0, 1)
	if singular {
		if object.R == nil {
			object.R = &reviewOverrideR{}
		}
		if !queries.IsNil(object.OriginalChangeID) {
			args = append(args, object.OriginalChangeID)
		}

	} else {
	Outer:
		for _, obj := range slice {
			if obj.R == nil {
				obj.R = &reviewOverrideR{}
			}

			for _, a := range args {
				if queries.Equal(a, obj.OriginalChangeID) {
					continue Outer
				}
			}

			if !queries.IsNil(obj.OriginalChangeID) {
				args = append(args, obj.OriginalChangeID)
			}

		}
	}

	if len(args) == 0 {
		return nil
	}

	query := NewQuery(
		qm.From(`endorsements.changes`),
		qm.WhereIn(`endorsements.changes.id in ?`, args...),
	)
	if mods != nil {
		mods.Apply(query)
	}

	results, err := query.QueryContext(ctx, e)
	if err != nil {
		return errors.Wrap(err, "failed to eager load Change")
	}

	var resultSlice []*Change
	if err = queries.Bind(results, &resultSlice); err != nil {
		return errors.Wrap(err, "failed to bind eager loaded slice Change")
	}

	if err = results.Close(); err != nil {
		return errors.Wrap(err, "failed to close results of eager load for changes")
	}
	if err = results.Err(); err != nil {
		return errors.Wrap(err, "error occurred during iteration of eager loaded relations for changes")
	}

	if len(reviewOverrideAfterSelectHooks) != 0 {
		for _, obj := range resultSlice {
			if err := obj.doAfterSelectHooks(ctx, e); err != nil {
				return err
			}
		}
	}

	if len(resultSlice) == 0 {
		return nil
	}

	if singular {
		foreign := resultSlice[0]
		object.R.OriginalChange = foreign
		if foreign.R == nil {
			foreign.R = &changeR{}
		}
		foreign.R.OriginalChangeReviewOverrides = append(foreign.R.OriginalChangeReviewOverrides, object)
		return nil
	}

	for _, local := range slice {
		for _, foreign := range resultSlice {
			if queries.Equal(local.OriginalChangeID, foreign.ID) {
				local.R.OriginalChange = foreign
				if foreign.R == nil {
					foreign.R = &changeR{}
				}
				foreign.R.OriginalChangeReviewOverrides = append(foreign.R.OriginalChangeReviewOverrides, local)
				break
			}
		}
	}

	return nil
}

// LoadReview allows an eager lookup of values, cached into the
// loaded structs of the objects. This is for an N-1 relationship.
func (reviewOverrideL) LoadReview(ctx context.Context, e boil.ContextExecutor, singular bool, maybeReviewOverride interface{}, mods queries.Applicator) error {
	var slice []*ReviewOverride
	var object *ReviewOverride

	if singular {
		object = maybeReviewOverride.(*ReviewOverride)
	} else {
		slice = *maybeReviewOverride.(*[]*ReviewOverride)
	}

	args := make([]interface{}, 0, 1)
	if singular {
		if object.R == nil {
			object.R = &reviewOverrideR{}
		}
		args = append(args, object.ReviewID)

	} else {
	Outer:
		for _, obj := range slice {
			if obj.R == nil {
				obj.R = &reviewOverrideR{}
			}

			for _, a := range args {
				if a == obj.ReviewID {
					continue Outer
				}
			}

			args = append(args, obj.ReviewID)

		}
	}

	if len(args) == 0 {
		return nil
	}

	query := NewQuery(
		qm.From(`endorsements.review`),
		qm.WhereIn(`endorsements.review.id in ?`, args...),
	)
	if mods != nil {
		mods.Apply(query)
	}

	results, err := query.QueryContext(ctx, e)
	if err != nil {
		return errors.Wrap(err, "failed to eager load Review")
	}

	var resultSlice []*Review
	if err = queries.Bind(results, &resultSlice); err != nil {
		return errors.Wrap(err, "failed to bind eager loaded slice Review")
	}

	if err = results.Close(); err != nil {
		return errors.Wrap(err, "failed to close results of eager load for review")
	}
	if err = results.Err(); err != nil {
		return errors.Wrap(err, "error occurred during iteration of eager loaded relations for review")
	}

	if len(reviewOverrideAfterSelectHooks) != 0 {
		for _, obj := range resultSlice {
			if err := obj.doAfterSelectHooks(ctx, e); err != nil {
				return err
			}
		}
	}

	if len(resultSlice) == 0 {
		return nil
	}

	if singular {
		foreign := resultSlice[0]
		object.R.Review = foreign
		if foreign.R == nil {
			foreign.R = &reviewR{}
		}
		foreign.R.ReviewOverrides = append(foreign.R.ReviewOverrides, object)
		return nil
	}

	for _, local := range slice {
		for _, foreign := range resultSlice {
			if local.ReviewID == foreign.ID {
				local.R.Review = foreign
				if foreign.R == nil {
					foreign.R = &reviewR{}
				}
				foreign.R.ReviewOverrides = append(foreign.R.ReviewOverrides, local)
				break
			}
		}
	}

	return nil
}

// SetOriginalChange of the reviewOverride to the related item.
// Sets o.R.OriginalChange to related.
// Adds o to related.R.OriginalChangeReviewOverrides.
func (o *ReviewOverride) SetOriginalChange(ctx context.Context, exec boil.ContextExecutor, insert bool, related *Change) error {
	var err error
	if insert {
		if err = related.Insert(ctx, exec, boil.Infer()); err != nil {
			return errors.Wrap(err, "failed to insert into foreign table")
		}
	}

	updateQuery := fmt.Sprintf(
		"UPDATE \"endorsements\".\"review_override\" SET %s WHERE %s",
		strmangle.SetParamNames("\"", "\"", 1, []string{"original_change_id"}),
		strmangle.WhereClause("\"", "\"", 2, reviewOverridePrimaryKeyColumns),
	)
	values := []interface{}{related.ID, o.ID}

	if boil.IsDebug(ctx) {
		writer := boil.DebugWriterFrom(ctx)
		fmt.Fprintln(writer, updateQuery)
		fmt.Fprintln(writer, values)
	}
	if _, err = exec.ExecContext(ctx, updateQuery, values...); err != nil {
		return errors.Wrap(err, "failed to update local table")
	}

	queries.Assign(&o.OriginalChangeID, related.ID)
	if o.R == nil {
		o.R = &reviewOverrideR{
			OriginalChange: related,
		}
	} else {
		o.R.OriginalChange = related
	}

	if related.R == nil {
		related.R = &changeR{
			OriginalChangeReviewOverrides: ReviewOverrideSlice{o},
		}
	} else {
		related.R.OriginalChangeReviewOverrides = append(related.R.OriginalChangeReviewOverrides, o)
	}

	return nil
}

// RemoveOriginalChange relationship.
// Sets o.R.OriginalChange to nil.
// Removes o from all passed in related items' relationships struct.
func (o *ReviewOverride) RemoveOriginalChange(ctx context.Context, exec boil.ContextExecutor, related *Change) error {
	var err error

	queries.SetScanner(&o.OriginalChangeID, nil)
	if _, err = o.Update(ctx, exec, boil.Whitelist("original_change_id")); err != nil {
		return errors.Wrap(err, "failed to update local table")
	}

	if o.R != nil {
		o.R.OriginalChange = nil
	}
	if related == nil || related.R == nil {
		return nil
	}

	for i, ri := range related.R.OriginalChangeReviewOverrides {
		if queries.Equal(o.OriginalChangeID, ri.OriginalChangeID) {
			continue
		}

		ln := len(related.R.OriginalChangeReviewOverrides)
		if ln > 1 && i < ln-1 {
			related.R.OriginalChangeReviewOverrides[i] = related.R.OriginalChangeReviewOverrides[ln-1]
		}
		related.R.OriginalChangeReviewOverrides = related.R.OriginalChangeReviewOverrides[:ln-1]
		break
	}
	return nil
}

// SetReview of the reviewOverride to the related item.
// Sets o.R.Review to related.
// Adds o to related.R.ReviewOverrides.
func (o *ReviewOverride) SetReview(ctx context.Context, exec boil.ContextExecutor, insert bool, related *Review) error {
	var err error
	if insert {
		if err = related.Insert(ctx, exec, boil.Infer()); err != nil {
			return errors.Wrap(err, "failed to insert into foreign table")
		}
	}

	updateQuery := fmt.Sprintf(
		"UPDATE \"endorsements\".\"review_override\" SET %s WHERE %s",
		strmangle.SetParamNames("\"", "\"", 1, []string{"review_id"}),
		strmangle.WhereClause("\"", "\"", 2, reviewOverridePrimaryKeyColumns),
	)
	values := []interface{}{related.ID, o.ID}

	if boil.IsDebug(ctx) {
		writer := boil.DebugWriterFrom(ctx)
		fmt.Fprintln(writer, updateQuery)
		fmt.Fprintln(writer, values)
	}
	if _, err = exec.ExecContext(ctx, updateQuery, values...); err != nil {
		return errors.Wrap(err, "failed to update local table")
	}

	o.ReviewID = related.ID
	if o.R == nil {
		o.R = &reviewOverrideR{
			Review: related,
		}
	} else {
		o.R.Review = related
	}

	if related.R == nil {
		related.R = &reviewR{
			ReviewOverrides: ReviewOverrideSlice{o},
		}
	} else {
		related.R.ReviewOverrides = append(related.R.ReviewOverrides, o)
	}

	return nil
}

// ReviewOverrides retrieves all the records using an executor.
func ReviewOverrides(mods ...qm.QueryMod) reviewOverrideQuery {
	mods = append(mods, qm.From("\"endorsements\".\"review_override\""))
	q := NewQuery(mods...)
	if len(queries.GetSelect(q)) == 0 {
		queries.SetSelect(q, []string{"\"endorsements\".\"review_override\".*"})
	}

	return reviewOverrideQuery{q}
}

// FindReviewOverride retrieves a single record by ID with an executor.
// If selectCols is empty Find will return all columns.
func FindReviewOverride(ctx context.Context, exec boil.ContextExecutor, iD string, selectCols ...string) (*ReviewOverride, error) {
	reviewOverrideObj := &ReviewOverride{}

	sel := "*"
	if len(selectCols) > 0 {
		sel = strings.Join(strmangle.IdentQuoteSlice(dialect.LQ, dialect.RQ, selectCols), ",")
	}
	query := fmt.Sprintf(
		"select %s from \"endorsements\".\"review_override\" where \"id\"=$1", sel,
	)

	q := queries.Raw(query, iD)

	err := q.Bind(ctx, exec, reviewOverrideObj)
	if err != nil {
		if errors.Is(err, sql.ErrNoRows) {
			return nil, sql.ErrNoRows
		}
		return nil, errors.Wrap(err, "endorsementapp: unable to select from review_override")
	}

	if err = reviewOverrideObj.doAfterSelectHooks(ctx, exec); err != nil {
		return reviewOverrideObj, err
	}

	return reviewOverrideObj, nil
}

// Insert a single record using an executor.
// See boil.Columns.InsertColumnSet documentation to understand column list inference for inserts.
func (o *ReviewOverride) Insert(ctx context.Context, exec boil.ContextExecutor, columns boil.Columns) error {
	if o == nil {
		return errors.New("endorsementapp: no review_override provided for insertion")
	}

	var err error
	if !boil.TimestampsAreSkipped(ctx) {
		currTime := time.Now().In(boil.GetLocation())

		if o.CreatedAt.IsZero() {
			o.CreatedAt = currTime
		}
		if o.UpdatedAt.IsZero() {
			o.UpdatedAt = currTime
		}
	}

	if err := o.doBeforeInsertHooks(ctx, exec); err != nil {
		return err
	}

	nzDefaults := queries.NonZeroDefaultSet(reviewOverrideColumnsWithDefault, o)

	key := makeCacheKey(columns, nzDefaults)
	reviewOverrideInsertCacheMut.RLock()
	cache, cached := reviewOverrideInsertCache[key]
	reviewOverrideInsertCacheMut.RUnlock()

	if !cached {
		wl, returnColumns := columns.InsertColumnSet(
			reviewOverrideAllColumns,
			reviewOverrideColumnsWithDefault,
			reviewOverrideColumnsWithoutDefault,
			nzDefaults,
		)

		cache.valueMapping, err = queries.BindMapping(reviewOverrideType, reviewOverrideMapping, wl)
		if err != nil {
			return err
		}
		cache.retMapping, err = queries.BindMapping(reviewOverrideType, reviewOverrideMapping, returnColumns)
		if err != nil {
			return err
		}
		if len(wl) != 0 {
			cache.query = fmt.Sprintf("INSERT INTO \"endorsements\".\"review_override\" (\"%s\") %%sVALUES (%s)%%s", strings.Join(wl, "\",\""), strmangle.Placeholders(dialect.UseIndexPlaceholders, len(wl), 1, 1))
		} else {
			cache.query = "INSERT INTO \"endorsements\".\"review_override\" %sDEFAULT VALUES%s"
		}

		var queryOutput, queryReturning string

		if len(cache.retMapping) != 0 {
			queryReturning = fmt.Sprintf(" RETURNING \"%s\"", strings.Join(returnColumns, "\",\""))
		}

		cache.query = fmt.Sprintf(cache.query, queryOutput, queryReturning)
	}

	value := reflect.Indirect(reflect.ValueOf(o))
	vals := queries.ValuesFromMapping(value, cache.valueMapping)

	if boil.IsDebug(ctx) {
		writer := boil.DebugWriterFrom(ctx)
		fmt.Fprintln(writer, cache.query)
		fmt.Fprintln(writer, vals)
	}

	if len(cache.retMapping) != 0 {
		err = exec.QueryRowContext(ctx, cache.query, vals...).Scan(queries.PtrsFromMapping(value, cache.retMapping)...)
	} else {
		_, err = exec.ExecContext(ctx, cache.query, vals...)
	}

	if err != nil {
		return errors.Wrap(err, "endorsementapp: unable to insert into review_override")
	}

	if !cached {
		reviewOverrideInsertCacheMut.Lock()
		reviewOverrideInsertCache[key] = cache
		reviewOverrideInsertCacheMut.Unlock()
	}

	return o.doAfterInsertHooks(ctx, exec)
}

// Update uses an executor to update the ReviewOverride.
// See boil.Columns.UpdateColumnSet documentation to understand column list inference for updates.
// Update does not automatically update the record in case of default values. Use .Reload() to refresh the records.
func (o *ReviewOverride) Update(ctx context.Context, exec boil.ContextExecutor, columns boil.Columns) (int64, error) {
	if !boil.TimestampsAreSkipped(ctx) {
		currTime := time.Now().In(boil.GetLocation())

		o.UpdatedAt = currTime
	}

	var err error
	if err = o.doBeforeUpdateHooks(ctx, exec); err != nil {
		return 0, err
	}
	key := makeCacheKey(columns, nil)
	reviewOverrideUpdateCacheMut.RLock()
	cache, cached := reviewOverrideUpdateCache[key]
	reviewOverrideUpdateCacheMut.RUnlock()

	if !cached {
		wl := columns.UpdateColumnSet(
			reviewOverrideAllColumns,
			reviewOverridePrimaryKeyColumns,
		)

		if !columns.IsWhitelist() {
			wl = strmangle.SetComplement(wl, []string{"created_at"})
		}
		if len(wl) == 0 {
			return 0, errors.New("endorsementapp: unable to update review_override, could not build whitelist")
		}

		cache.query = fmt.Sprintf("UPDATE \"endorsements\".\"review_override\" SET %s WHERE %s",
			strmangle.SetParamNames("\"", "\"", 1, wl),
			strmangle.WhereClause("\"", "\"", len(wl)+1, reviewOverridePrimaryKeyColumns),
		)
		cache.valueMapping, err = queries.BindMapping(reviewOverrideType, reviewOverrideMapping, append(wl, reviewOverridePrimaryKeyColumns...))
		if err != nil {
			return 0, err
		}
	}

	values := queries.ValuesFromMapping(reflect.Indirect(reflect.ValueOf(o)), cache.valueMapping)

	if boil.IsDebug(ctx) {
		writer := boil.DebugWriterFrom(ctx)
		fmt.Fprintln(writer, cache.query)
		fmt.Fprintln(writer, values)
	}
	var result sql.Result
	result, err = exec.ExecContext(ctx, cache.query, values...)
	if err != nil {
		return 0, errors.Wrap(err, "endorsementapp: unable to update review_override row")
	}

	rowsAff, err := result.RowsAffected()
	if err != nil {
		return 0, errors.Wrap(err, "endorsementapp: failed to get rows affected by update for review_override")
	}

	if !cached {
		reviewOverrideUpdateCacheMut.Lock()
		reviewOverrideUpdateCache[key] = cache
		reviewOverrideUpdateCacheMut.Unlock()
	}

	return rowsAff, o.doAfterUpdateHooks(ctx, exec)
}

// UpdateAll updates all rows with the specified column values.
func (q reviewOverrideQuery) UpdateAll(ctx context.Context, exec boil.ContextExecutor, cols M) (int64, error) {
	queries.SetUpdate(q.Query, cols)

	result, err := q.Query.ExecContext(ctx, exec)
	if err != nil {
		return 0, errors.Wrap(err, "endorsementapp: unable to update all for review_override")
	}

	rowsAff, err := result.RowsAffected()
	if err != nil {
		return 0, errors.Wrap(err, "endorsementapp: unable to retrieve rows affected for review_override")
	}

	return rowsAff, nil
}

// UpdateAll updates all rows with the specified column values, using an executor.
func (o ReviewOverrideSlice) UpdateAll(ctx context.Context, exec boil.ContextExecutor, cols M) (int64, error) {
	ln := int64(len(o))
	if ln == 0 {
		return 0, nil
	}

	if len(cols) == 0 {
		return 0, errors.New("endorsementapp: update all requires at least one column argument")
	}

	colNames := make([]string, len(cols))
	args := make([]interface{}, len(cols))

	i := 0
	for name, value := range cols {
		colNames[i] = name
		args[i] = value
		i++
	}

	// Append all of the primary key values for each column
	for _, obj := range o {
		pkeyArgs := queries.ValuesFromMapping(reflect.Indirect(reflect.ValueOf(obj)), reviewOverridePrimaryKeyMapping)
		args = append(args, pkeyArgs...)
	}

	sql := fmt.Sprintf("UPDATE \"endorsements\".\"review_override\" SET %s WHERE %s",
		strmangle.SetParamNames("\"", "\"", 1, colNames),
		strmangle.WhereClauseRepeated(string(dialect.LQ), string(dialect.RQ), len(colNames)+1, reviewOverridePrimaryKeyColumns, len(o)))

	if boil.IsDebug(ctx) {
		writer := boil.DebugWriterFrom(ctx)
		fmt.Fprintln(writer, sql)
		fmt.Fprintln(writer, args...)
	}
	result, err := exec.ExecContext(ctx, sql, args...)
	if err != nil {
		return 0, errors.Wrap(err, "endorsementapp: unable to update all in reviewOverride slice")
	}

	rowsAff, err := result.RowsAffected()
	if err != nil {
		return 0, errors.Wrap(err, "endorsementapp: unable to retrieve rows affected all in update all reviewOverride")
	}
	return rowsAff, nil
}

// Upsert attempts an insert using an executor, and does an update or ignore on conflict.
// See boil.Columns documentation for how to properly use updateColumns and insertColumns.
func (o *ReviewOverride) Upsert(ctx context.Context, exec boil.ContextExecutor, updateOnConflict bool, conflictColumns []string, updateColumns, insertColumns boil.Columns) error {
	if o == nil {
		return errors.New("endorsementapp: no review_override provided for upsert")
	}
	if !boil.TimestampsAreSkipped(ctx) {
		currTime := time.Now().In(boil.GetLocation())

		if o.CreatedAt.IsZero() {
			o.CreatedAt = currTime
		}
		o.UpdatedAt = currTime
	}

	if err := o.doBeforeUpsertHooks(ctx, exec); err != nil {
		return err
	}

	nzDefaults := queries.NonZeroDefaultSet(reviewOverrideColumnsWithDefault, o)

	// Build cache key in-line uglily - mysql vs psql problems
	buf := strmangle.GetBuffer()
	if updateOnConflict {
		buf.WriteByte('t')
	} else {
		buf.WriteByte('f')
	}
	buf.WriteByte('.')
	for _, c := range conflictColumns {
		buf.WriteString(c)
	}
	buf.WriteByte('.')
	buf.WriteString(strconv.Itoa(updateColumns.Kind))
	for _, c := range updateColumns.Cols {
		buf.WriteString(c)
	}
	buf.WriteByte('.')
	buf.WriteString(strconv.Itoa(insertColumns.Kind))
	for _, c := range insertColumns.Cols {
		buf.WriteString(c)
	}
	buf.WriteByte('.')
	for _, c := range nzDefaults {
		buf.WriteString(c)
	}
	key := buf.String()
	strmangle.PutBuffer(buf)

	reviewOverrideUpsertCacheMut.RLock()
	cache, cached := reviewOverrideUpsertCache[key]
	reviewOverrideUpsertCacheMut.RUnlock()

	var err error

	if !cached {
		insert, ret := insertColumns.InsertColumnSet(
			reviewOverrideAllColumns,
			reviewOverrideColumnsWithDefault,
			reviewOverrideColumnsWithoutDefault,
			nzDefaults,
		)

		update := updateColumns.UpdateColumnSet(
			reviewOverrideAllColumns,
			reviewOverridePrimaryKeyColumns,
		)

		if updateOnConflict && len(update) == 0 {
			return errors.New("endorsementapp: unable to upsert review_override, could not build update column list")
		}

		conflict := conflictColumns
		if len(conflict) == 0 {
			conflict = make([]string, len(reviewOverridePrimaryKeyColumns))
			copy(conflict, reviewOverridePrimaryKeyColumns)
		}
		cache.query = buildUpsertQueryPostgres(dialect, "\"endorsements\".\"review_override\"", updateOnConflict, ret, update, conflict, insert)

		cache.valueMapping, err = queries.BindMapping(reviewOverrideType, reviewOverrideMapping, insert)
		if err != nil {
			return err
		}
		if len(ret) != 0 {
			cache.retMapping, err = queries.BindMapping(reviewOverrideType, reviewOverrideMapping, ret)
			if err != nil {
				return err
			}
		}
	}

	value := reflect.Indirect(reflect.ValueOf(o))
	vals := queries.ValuesFromMapping(value, cache.valueMapping)
	var returns []interface{}
	if len(cache.retMapping) != 0 {
		returns = queries.PtrsFromMapping(value, cache.retMapping)
	}

	if boil.IsDebug(ctx) {
		writer := boil.DebugWriterFrom(ctx)
		fmt.Fprintln(writer, cache.query)
		fmt.Fprintln(writer, vals)
	}
	if len(cache.retMapping) != 0 {
		err = exec.QueryRowContext(ctx, cache.query, vals...).Scan(returns...)
		if errors.Is(err, sql.ErrNoRows) {
			err = nil // Postgres doesn't return anything when there's no update
		}
	} else {
		_, err = exec.ExecContext(ctx, cache.query, vals...)
	}
	if err != nil {
		return errors.Wrap(err, "endorsementapp: unable to upsert review_override")
	}

	if !cached {
		reviewOverrideUpsertCacheMut.Lock()
		reviewOverrideUpsertCache[key] = cache
		reviewOverrideUpsertCacheMut.Unlock()
	}

	return o.doAfterUpsertHooks(ctx, exec)
}

// Delete deletes a single ReviewOverride record with an executor.
// Delete will match against the primary key column to find the record to delete.
func (o *ReviewOverride) Delete(ctx context.Context, exec boil.ContextExecutor) (int64, error) {
	if o == nil {
		return 0, errors.New("endorsementapp: no ReviewOverride provided for delete")
	}

	if err := o.doBeforeDeleteHooks(ctx, exec); err != nil {
		return 0, err
	}

	args := queries.ValuesFromMapping(reflect.Indirect(reflect.ValueOf(o)), reviewOverridePrimaryKeyMapping)
	sql := "DELETE FROM \"endorsements\".\"review_override\" WHERE \"id\"=$1"

	if boil.IsDebug(ctx) {
		writer := boil.DebugWriterFrom(ctx)
		fmt.Fprintln(writer, sql)
		fmt.Fprintln(writer, args...)
	}
	result, err := exec.ExecContext(ctx, sql, args...)
	if err != nil {
		return 0, errors.Wrap(err, "endorsementapp: unable to delete from review_override")
	}

	rowsAff, err := result.RowsAffected()
	if err != nil {
		return 0, errors.Wrap(err, "endorsementapp: failed to get rows affected by delete for review_override")
	}

	if err := o.doAfterDeleteHooks(ctx, exec); err != nil {
		return 0, err
	}

	return rowsAff, nil
}

// DeleteAll deletes all matching rows.
func (q reviewOverrideQuery) DeleteAll(ctx context.Context, exec boil.ContextExecutor) (int64, error) {
	if q.Query == nil {
		return 0, errors.New("endorsementapp: no reviewOverrideQuery provided for delete all")
	}

	queries.SetDelete(q.Query)

	result, err := q.Query.ExecContext(ctx, exec)
	if err != nil {
		return 0, errors.Wrap(err, "endorsementapp: unable to delete all from review_override")
	}

	rowsAff, err := result.RowsAffected()
	if err != nil {
		return 0, errors.Wrap(err, "endorsementapp: failed to get rows affected by deleteall for review_override")
	}

	return rowsAff, nil
}

// DeleteAll deletes all rows in the slice, using an executor.
func (o ReviewOverrideSlice) DeleteAll(ctx context.Context, exec boil.ContextExecutor) (int64, error) {
	if len(o) == 0 {
		return 0, nil
	}

	if len(reviewOverrideBeforeDeleteHooks) != 0 {
		for _, obj := range o {
			if err := obj.doBeforeDeleteHooks(ctx, exec); err != nil {
				return 0, err
			}
		}
	}

	var args []interface{}
	for _, obj := range o {
		pkeyArgs := queries.ValuesFromMapping(reflect.Indirect(reflect.ValueOf(obj)), reviewOverridePrimaryKeyMapping)
		args = append(args, pkeyArgs...)
	}

	sql := "DELETE FROM \"endorsements\".\"review_override\" WHERE " +
		strmangle.WhereClauseRepeated(string(dialect.LQ), string(dialect.RQ), 1, reviewOverridePrimaryKeyColumns, len(o))

	if boil.IsDebug(ctx) {
		writer := boil.DebugWriterFrom(ctx)
		fmt.Fprintln(writer, sql)
		fmt.Fprintln(writer, args)
	}
	result, err := exec.ExecContext(ctx, sql, args...)
	if err != nil {
		return 0, errors.Wrap(err, "endorsementapp: unable to delete all from reviewOverride slice")
	}

	rowsAff, err := result.RowsAffected()
	if err != nil {
		return 0, errors.Wrap(err, "endorsementapp: failed to get rows affected by deleteall for review_override")
	}

	if len(reviewOverrideAfterDeleteHooks) != 0 {
		for _, obj := range o {
			if err := obj.doAfterDeleteHooks(ctx, exec); err != nil {
				return 0, err
			}
		}
	}

	return rowsAff, nil
}

// Reload refetches the object from the database
// using the primary keys with an executor.
func (o *ReviewOverride) Reload(ctx context.Context, exec boil.ContextExecutor) error {
	ret, err := FindReviewOverride(ctx, exec, o.ID)
	if err != nil {
		return err
	}

	*o = *ret
	return nil
}

// ReloadAll refetches every row with matching primary key column values
// and overwrites the original object slice with the newly updated slice.
func (o *ReviewOverrideSlice) ReloadAll(ctx context.Context, exec boil.ContextExecutor) error {
	if o == nil || len(*o) == 0 {
		return nil
	}

	slice := ReviewOverrideSlice{}
	var args []interface{}
	for _, obj := range *o {
		pkeyArgs := queries.ValuesFromMapping(reflect.Indirect(reflect.ValueOf(obj)), reviewOverridePrimaryKeyMapping)
		args = append(args, pkeyArgs...)
	}

	sql := "SELECT \"endorsements\".\"review_override\".* FROM \"endorsements\".\"review_override\" WHERE " +
		strmangle.WhereClauseRepeated(string(dialect.LQ), string(dialect.RQ), 1, reviewOverridePrimaryKeyColumns, len(*o))

	q := queries.Raw(sql, args...)

	err := q.Bind(ctx, exec, &slice)
	if err != nil {
		return errors.Wrap(err, "endorsementapp: unable to reload all in ReviewOverrideSlice")
	}

	*o = slice

	return nil
}

// ReviewOverrideExists checks if the ReviewOverride row exists.
func ReviewOverrideExists(ctx context.Context, exec boil.ContextExecutor, iD string) (bool, error) {
	var exists bool
	sql := "select exists(select 1 from \"endorsements\".\"review_override\" where \"id\"=$1 limit 1)"

	if boil.IsDebug(ctx) {
		writer := boil.DebugWriterFrom(ctx)
		fmt.Fprintln(writer, sql)
		fmt.Fprintln(writer, iD)
	}
	row := exec.QueryRowContext(ctx, sql, iD)

	err := row.Scan(&exists)
	if err != nil {
		return false, errors.Wrap(err, "endorsementapp: unable to check if review_override exists")
	}

	return exists, nil
}
