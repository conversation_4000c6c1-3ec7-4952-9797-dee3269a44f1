// Code generated by SQLBoiler (https://github.com/volatiletech/sqlboiler). DO NOT EDIT.
// This file is meant to be re-generated in place and/or deleted at any time.

package claims

import (
	"context"
	"database/sql"
	"fmt"
	"reflect"
	"strconv"
	"strings"
	"sync"
	"time"

	"github.com/friendsofgo/errors"
	"github.com/volatiletech/sqlboiler/v4/boil"
	"github.com/volatiletech/sqlboiler/v4/queries"
	"github.com/volatiletech/sqlboiler/v4/queries/qm"
	"github.com/volatiletech/sqlboiler/v4/queries/qmhelper"
	"github.com/volatiletech/strmangle"
)

// BordereauxReport is an object representing the database table.
type BordereauxReport struct {
	ID           string    `boil:"id" json:"id" toml:"id" yaml:"id"`
	GeneratedAt  time.Time `boil:"generated_at" json:"generated_at" toml:"generated_at" yaml:"generated_at"`
	GeneratedBy  string    `boil:"generated_by" json:"generated_by" toml:"generated_by" yaml:"generated_by"`
	Carrier      string    `boil:"carrier" json:"carrier" toml:"carrier" yaml:"carrier"`
	FileHandleID string    `boil:"file_handle_id" json:"file_handle_id" toml:"file_handle_id" yaml:"file_handle_id"`
	CreatedAt    time.Time `boil:"created_at" json:"created_at" toml:"created_at" yaml:"created_at"`

	R *bordereauxReportR `boil:"" json:"" toml:"" yaml:""`
	L bordereauxReportL  `boil:"-" json:"-" toml:"-" yaml:"-"`
}

var BordereauxReportColumns = struct {
	ID           string
	GeneratedAt  string
	GeneratedBy  string
	Carrier      string
	FileHandleID string
	CreatedAt    string
}{
	ID:           "id",
	GeneratedAt:  "generated_at",
	GeneratedBy:  "generated_by",
	Carrier:      "carrier",
	FileHandleID: "file_handle_id",
	CreatedAt:    "created_at",
}

var BordereauxReportTableColumns = struct {
	ID           string
	GeneratedAt  string
	GeneratedBy  string
	Carrier      string
	FileHandleID string
	CreatedAt    string
}{
	ID:           "bordereaux_reports.id",
	GeneratedAt:  "bordereaux_reports.generated_at",
	GeneratedBy:  "bordereaux_reports.generated_by",
	Carrier:      "bordereaux_reports.carrier",
	FileHandleID: "bordereaux_reports.file_handle_id",
	CreatedAt:    "bordereaux_reports.created_at",
}

// Generated where

type whereHelperstring struct{ field string }

func (w whereHelperstring) EQ(x string) qm.QueryMod  { return qmhelper.Where(w.field, qmhelper.EQ, x) }
func (w whereHelperstring) NEQ(x string) qm.QueryMod { return qmhelper.Where(w.field, qmhelper.NEQ, x) }
func (w whereHelperstring) LT(x string) qm.QueryMod  { return qmhelper.Where(w.field, qmhelper.LT, x) }
func (w whereHelperstring) LTE(x string) qm.QueryMod { return qmhelper.Where(w.field, qmhelper.LTE, x) }
func (w whereHelperstring) GT(x string) qm.QueryMod  { return qmhelper.Where(w.field, qmhelper.GT, x) }
func (w whereHelperstring) GTE(x string) qm.QueryMod { return qmhelper.Where(w.field, qmhelper.GTE, x) }
func (w whereHelperstring) IN(slice []string) qm.QueryMod {
	values := make([]interface{}, 0, len(slice))
	for _, value := range slice {
		values = append(values, value)
	}
	return qm.WhereIn(fmt.Sprintf("%s IN ?", w.field), values...)
}
func (w whereHelperstring) NIN(slice []string) qm.QueryMod {
	values := make([]interface{}, 0, len(slice))
	for _, value := range slice {
		values = append(values, value)
	}
	return qm.WhereNotIn(fmt.Sprintf("%s NOT IN ?", w.field), values...)
}

type whereHelpertime_Time struct{ field string }

func (w whereHelpertime_Time) EQ(x time.Time) qm.QueryMod {
	return qmhelper.Where(w.field, qmhelper.EQ, x)
}
func (w whereHelpertime_Time) NEQ(x time.Time) qm.QueryMod {
	return qmhelper.Where(w.field, qmhelper.NEQ, x)
}
func (w whereHelpertime_Time) LT(x time.Time) qm.QueryMod {
	return qmhelper.Where(w.field, qmhelper.LT, x)
}
func (w whereHelpertime_Time) LTE(x time.Time) qm.QueryMod {
	return qmhelper.Where(w.field, qmhelper.LTE, x)
}
func (w whereHelpertime_Time) GT(x time.Time) qm.QueryMod {
	return qmhelper.Where(w.field, qmhelper.GT, x)
}
func (w whereHelpertime_Time) GTE(x time.Time) qm.QueryMod {
	return qmhelper.Where(w.field, qmhelper.GTE, x)
}

var BordereauxReportWhere = struct {
	ID           whereHelperstring
	GeneratedAt  whereHelpertime_Time
	GeneratedBy  whereHelperstring
	Carrier      whereHelperstring
	FileHandleID whereHelperstring
	CreatedAt    whereHelpertime_Time
}{
	ID:           whereHelperstring{field: "\"claims\".\"bordereaux_reports\".\"id\""},
	GeneratedAt:  whereHelpertime_Time{field: "\"claims\".\"bordereaux_reports\".\"generated_at\""},
	GeneratedBy:  whereHelperstring{field: "\"claims\".\"bordereaux_reports\".\"generated_by\""},
	Carrier:      whereHelperstring{field: "\"claims\".\"bordereaux_reports\".\"carrier\""},
	FileHandleID: whereHelperstring{field: "\"claims\".\"bordereaux_reports\".\"file_handle_id\""},
	CreatedAt:    whereHelpertime_Time{field: "\"claims\".\"bordereaux_reports\".\"created_at\""},
}

// BordereauxReportRels is where relationship names are stored.
var BordereauxReportRels = struct {
}{}

// bordereauxReportR is where relationships are stored.
type bordereauxReportR struct {
}

// NewStruct creates a new relationship struct
func (*bordereauxReportR) NewStruct() *bordereauxReportR {
	return &bordereauxReportR{}
}

// bordereauxReportL is where Load methods for each relationship are stored.
type bordereauxReportL struct{}

var (
	bordereauxReportAllColumns            = []string{"id", "generated_at", "generated_by", "carrier", "file_handle_id", "created_at"}
	bordereauxReportColumnsWithoutDefault = []string{"generated_at", "generated_by", "carrier", "file_handle_id"}
	bordereauxReportColumnsWithDefault    = []string{"id", "created_at"}
	bordereauxReportPrimaryKeyColumns     = []string{"id"}
	bordereauxReportGeneratedColumns      = []string{}
)

type (
	// BordereauxReportSlice is an alias for a slice of pointers to BordereauxReport.
	// This should almost always be used instead of []BordereauxReport.
	BordereauxReportSlice []*BordereauxReport
	// BordereauxReportHook is the signature for custom BordereauxReport hook methods
	BordereauxReportHook func(context.Context, boil.ContextExecutor, *BordereauxReport) error

	bordereauxReportQuery struct {
		*queries.Query
	}
)

// Cache for insert, update and upsert
var (
	bordereauxReportType                 = reflect.TypeOf(&BordereauxReport{})
	bordereauxReportMapping              = queries.MakeStructMapping(bordereauxReportType)
	bordereauxReportPrimaryKeyMapping, _ = queries.BindMapping(bordereauxReportType, bordereauxReportMapping, bordereauxReportPrimaryKeyColumns)
	bordereauxReportInsertCacheMut       sync.RWMutex
	bordereauxReportInsertCache          = make(map[string]insertCache)
	bordereauxReportUpdateCacheMut       sync.RWMutex
	bordereauxReportUpdateCache          = make(map[string]updateCache)
	bordereauxReportUpsertCacheMut       sync.RWMutex
	bordereauxReportUpsertCache          = make(map[string]insertCache)
)

var (
	// Force time package dependency for automated UpdatedAt/CreatedAt.
	_ = time.Second
	// Force qmhelper dependency for where clause generation (which doesn't
	// always happen)
	_ = qmhelper.Where
)

var bordereauxReportAfterSelectHooks []BordereauxReportHook

var bordereauxReportBeforeInsertHooks []BordereauxReportHook
var bordereauxReportAfterInsertHooks []BordereauxReportHook

var bordereauxReportBeforeUpdateHooks []BordereauxReportHook
var bordereauxReportAfterUpdateHooks []BordereauxReportHook

var bordereauxReportBeforeDeleteHooks []BordereauxReportHook
var bordereauxReportAfterDeleteHooks []BordereauxReportHook

var bordereauxReportBeforeUpsertHooks []BordereauxReportHook
var bordereauxReportAfterUpsertHooks []BordereauxReportHook

// doAfterSelectHooks executes all "after Select" hooks.
func (o *BordereauxReport) doAfterSelectHooks(ctx context.Context, exec boil.ContextExecutor) (err error) {
	if boil.HooksAreSkipped(ctx) {
		return nil
	}

	for _, hook := range bordereauxReportAfterSelectHooks {
		if err := hook(ctx, exec, o); err != nil {
			return err
		}
	}

	return nil
}

// doBeforeInsertHooks executes all "before insert" hooks.
func (o *BordereauxReport) doBeforeInsertHooks(ctx context.Context, exec boil.ContextExecutor) (err error) {
	if boil.HooksAreSkipped(ctx) {
		return nil
	}

	for _, hook := range bordereauxReportBeforeInsertHooks {
		if err := hook(ctx, exec, o); err != nil {
			return err
		}
	}

	return nil
}

// doAfterInsertHooks executes all "after Insert" hooks.
func (o *BordereauxReport) doAfterInsertHooks(ctx context.Context, exec boil.ContextExecutor) (err error) {
	if boil.HooksAreSkipped(ctx) {
		return nil
	}

	for _, hook := range bordereauxReportAfterInsertHooks {
		if err := hook(ctx, exec, o); err != nil {
			return err
		}
	}

	return nil
}

// doBeforeUpdateHooks executes all "before Update" hooks.
func (o *BordereauxReport) doBeforeUpdateHooks(ctx context.Context, exec boil.ContextExecutor) (err error) {
	if boil.HooksAreSkipped(ctx) {
		return nil
	}

	for _, hook := range bordereauxReportBeforeUpdateHooks {
		if err := hook(ctx, exec, o); err != nil {
			return err
		}
	}

	return nil
}

// doAfterUpdateHooks executes all "after Update" hooks.
func (o *BordereauxReport) doAfterUpdateHooks(ctx context.Context, exec boil.ContextExecutor) (err error) {
	if boil.HooksAreSkipped(ctx) {
		return nil
	}

	for _, hook := range bordereauxReportAfterUpdateHooks {
		if err := hook(ctx, exec, o); err != nil {
			return err
		}
	}

	return nil
}

// doBeforeDeleteHooks executes all "before Delete" hooks.
func (o *BordereauxReport) doBeforeDeleteHooks(ctx context.Context, exec boil.ContextExecutor) (err error) {
	if boil.HooksAreSkipped(ctx) {
		return nil
	}

	for _, hook := range bordereauxReportBeforeDeleteHooks {
		if err := hook(ctx, exec, o); err != nil {
			return err
		}
	}

	return nil
}

// doAfterDeleteHooks executes all "after Delete" hooks.
func (o *BordereauxReport) doAfterDeleteHooks(ctx context.Context, exec boil.ContextExecutor) (err error) {
	if boil.HooksAreSkipped(ctx) {
		return nil
	}

	for _, hook := range bordereauxReportAfterDeleteHooks {
		if err := hook(ctx, exec, o); err != nil {
			return err
		}
	}

	return nil
}

// doBeforeUpsertHooks executes all "before Upsert" hooks.
func (o *BordereauxReport) doBeforeUpsertHooks(ctx context.Context, exec boil.ContextExecutor) (err error) {
	if boil.HooksAreSkipped(ctx) {
		return nil
	}

	for _, hook := range bordereauxReportBeforeUpsertHooks {
		if err := hook(ctx, exec, o); err != nil {
			return err
		}
	}

	return nil
}

// doAfterUpsertHooks executes all "after Upsert" hooks.
func (o *BordereauxReport) doAfterUpsertHooks(ctx context.Context, exec boil.ContextExecutor) (err error) {
	if boil.HooksAreSkipped(ctx) {
		return nil
	}

	for _, hook := range bordereauxReportAfterUpsertHooks {
		if err := hook(ctx, exec, o); err != nil {
			return err
		}
	}

	return nil
}

// AddBordereauxReportHook registers your hook function for all future operations.
func AddBordereauxReportHook(hookPoint boil.HookPoint, bordereauxReportHook BordereauxReportHook) {
	switch hookPoint {
	case boil.AfterSelectHook:
		bordereauxReportAfterSelectHooks = append(bordereauxReportAfterSelectHooks, bordereauxReportHook)
	case boil.BeforeInsertHook:
		bordereauxReportBeforeInsertHooks = append(bordereauxReportBeforeInsertHooks, bordereauxReportHook)
	case boil.AfterInsertHook:
		bordereauxReportAfterInsertHooks = append(bordereauxReportAfterInsertHooks, bordereauxReportHook)
	case boil.BeforeUpdateHook:
		bordereauxReportBeforeUpdateHooks = append(bordereauxReportBeforeUpdateHooks, bordereauxReportHook)
	case boil.AfterUpdateHook:
		bordereauxReportAfterUpdateHooks = append(bordereauxReportAfterUpdateHooks, bordereauxReportHook)
	case boil.BeforeDeleteHook:
		bordereauxReportBeforeDeleteHooks = append(bordereauxReportBeforeDeleteHooks, bordereauxReportHook)
	case boil.AfterDeleteHook:
		bordereauxReportAfterDeleteHooks = append(bordereauxReportAfterDeleteHooks, bordereauxReportHook)
	case boil.BeforeUpsertHook:
		bordereauxReportBeforeUpsertHooks = append(bordereauxReportBeforeUpsertHooks, bordereauxReportHook)
	case boil.AfterUpsertHook:
		bordereauxReportAfterUpsertHooks = append(bordereauxReportAfterUpsertHooks, bordereauxReportHook)
	}
}

// One returns a single bordereauxReport record from the query.
func (q bordereauxReportQuery) One(ctx context.Context, exec boil.ContextExecutor) (*BordereauxReport, error) {
	o := &BordereauxReport{}

	queries.SetLimit(q.Query, 1)

	err := q.Bind(ctx, exec, o)
	if err != nil {
		if errors.Is(err, sql.ErrNoRows) {
			return nil, sql.ErrNoRows
		}
		return nil, errors.Wrap(err, "claims: failed to execute a one query for bordereaux_reports")
	}

	if err := o.doAfterSelectHooks(ctx, exec); err != nil {
		return o, err
	}

	return o, nil
}

// All returns all BordereauxReport records from the query.
func (q bordereauxReportQuery) All(ctx context.Context, exec boil.ContextExecutor) (BordereauxReportSlice, error) {
	var o []*BordereauxReport

	err := q.Bind(ctx, exec, &o)
	if err != nil {
		return nil, errors.Wrap(err, "claims: failed to assign all query results to BordereauxReport slice")
	}

	if len(bordereauxReportAfterSelectHooks) != 0 {
		for _, obj := range o {
			if err := obj.doAfterSelectHooks(ctx, exec); err != nil {
				return o, err
			}
		}
	}

	return o, nil
}

// Count returns the count of all BordereauxReport records in the query.
func (q bordereauxReportQuery) Count(ctx context.Context, exec boil.ContextExecutor) (int64, error) {
	var count int64

	queries.SetSelect(q.Query, nil)
	queries.SetCount(q.Query)

	err := q.Query.QueryRowContext(ctx, exec).Scan(&count)
	if err != nil {
		return 0, errors.Wrap(err, "claims: failed to count bordereaux_reports rows")
	}

	return count, nil
}

// Exists checks if the row exists in the table.
func (q bordereauxReportQuery) Exists(ctx context.Context, exec boil.ContextExecutor) (bool, error) {
	var count int64

	queries.SetSelect(q.Query, nil)
	queries.SetCount(q.Query)
	queries.SetLimit(q.Query, 1)

	err := q.Query.QueryRowContext(ctx, exec).Scan(&count)
	if err != nil {
		return false, errors.Wrap(err, "claims: failed to check if bordereaux_reports exists")
	}

	return count > 0, nil
}

// BordereauxReports retrieves all the records using an executor.
func BordereauxReports(mods ...qm.QueryMod) bordereauxReportQuery {
	mods = append(mods, qm.From("\"claims\".\"bordereaux_reports\""))
	q := NewQuery(mods...)
	if len(queries.GetSelect(q)) == 0 {
		queries.SetSelect(q, []string{"\"claims\".\"bordereaux_reports\".*"})
	}

	return bordereauxReportQuery{q}
}

// FindBordereauxReport retrieves a single record by ID with an executor.
// If selectCols is empty Find will return all columns.
func FindBordereauxReport(ctx context.Context, exec boil.ContextExecutor, iD string, selectCols ...string) (*BordereauxReport, error) {
	bordereauxReportObj := &BordereauxReport{}

	sel := "*"
	if len(selectCols) > 0 {
		sel = strings.Join(strmangle.IdentQuoteSlice(dialect.LQ, dialect.RQ, selectCols), ",")
	}
	query := fmt.Sprintf(
		"select %s from \"claims\".\"bordereaux_reports\" where \"id\"=$1", sel,
	)

	q := queries.Raw(query, iD)

	err := q.Bind(ctx, exec, bordereauxReportObj)
	if err != nil {
		if errors.Is(err, sql.ErrNoRows) {
			return nil, sql.ErrNoRows
		}
		return nil, errors.Wrap(err, "claims: unable to select from bordereaux_reports")
	}

	if err = bordereauxReportObj.doAfterSelectHooks(ctx, exec); err != nil {
		return bordereauxReportObj, err
	}

	return bordereauxReportObj, nil
}

// Insert a single record using an executor.
// See boil.Columns.InsertColumnSet documentation to understand column list inference for inserts.
func (o *BordereauxReport) Insert(ctx context.Context, exec boil.ContextExecutor, columns boil.Columns) error {
	if o == nil {
		return errors.New("claims: no bordereaux_reports provided for insertion")
	}

	var err error
	if !boil.TimestampsAreSkipped(ctx) {
		currTime := time.Now().In(boil.GetLocation())

		if o.CreatedAt.IsZero() {
			o.CreatedAt = currTime
		}
	}

	if err := o.doBeforeInsertHooks(ctx, exec); err != nil {
		return err
	}

	nzDefaults := queries.NonZeroDefaultSet(bordereauxReportColumnsWithDefault, o)

	key := makeCacheKey(columns, nzDefaults)
	bordereauxReportInsertCacheMut.RLock()
	cache, cached := bordereauxReportInsertCache[key]
	bordereauxReportInsertCacheMut.RUnlock()

	if !cached {
		wl, returnColumns := columns.InsertColumnSet(
			bordereauxReportAllColumns,
			bordereauxReportColumnsWithDefault,
			bordereauxReportColumnsWithoutDefault,
			nzDefaults,
		)

		cache.valueMapping, err = queries.BindMapping(bordereauxReportType, bordereauxReportMapping, wl)
		if err != nil {
			return err
		}
		cache.retMapping, err = queries.BindMapping(bordereauxReportType, bordereauxReportMapping, returnColumns)
		if err != nil {
			return err
		}
		if len(wl) != 0 {
			cache.query = fmt.Sprintf("INSERT INTO \"claims\".\"bordereaux_reports\" (\"%s\") %%sVALUES (%s)%%s", strings.Join(wl, "\",\""), strmangle.Placeholders(dialect.UseIndexPlaceholders, len(wl), 1, 1))
		} else {
			cache.query = "INSERT INTO \"claims\".\"bordereaux_reports\" %sDEFAULT VALUES%s"
		}

		var queryOutput, queryReturning string

		if len(cache.retMapping) != 0 {
			queryReturning = fmt.Sprintf(" RETURNING \"%s\"", strings.Join(returnColumns, "\",\""))
		}

		cache.query = fmt.Sprintf(cache.query, queryOutput, queryReturning)
	}

	value := reflect.Indirect(reflect.ValueOf(o))
	vals := queries.ValuesFromMapping(value, cache.valueMapping)

	if boil.IsDebug(ctx) {
		writer := boil.DebugWriterFrom(ctx)
		fmt.Fprintln(writer, cache.query)
		fmt.Fprintln(writer, vals)
	}

	if len(cache.retMapping) != 0 {
		err = exec.QueryRowContext(ctx, cache.query, vals...).Scan(queries.PtrsFromMapping(value, cache.retMapping)...)
	} else {
		_, err = exec.ExecContext(ctx, cache.query, vals...)
	}

	if err != nil {
		return errors.Wrap(err, "claims: unable to insert into bordereaux_reports")
	}

	if !cached {
		bordereauxReportInsertCacheMut.Lock()
		bordereauxReportInsertCache[key] = cache
		bordereauxReportInsertCacheMut.Unlock()
	}

	return o.doAfterInsertHooks(ctx, exec)
}

// Update uses an executor to update the BordereauxReport.
// See boil.Columns.UpdateColumnSet documentation to understand column list inference for updates.
// Update does not automatically update the record in case of default values. Use .Reload() to refresh the records.
func (o *BordereauxReport) Update(ctx context.Context, exec boil.ContextExecutor, columns boil.Columns) (int64, error) {
	var err error
	if err = o.doBeforeUpdateHooks(ctx, exec); err != nil {
		return 0, err
	}
	key := makeCacheKey(columns, nil)
	bordereauxReportUpdateCacheMut.RLock()
	cache, cached := bordereauxReportUpdateCache[key]
	bordereauxReportUpdateCacheMut.RUnlock()

	if !cached {
		wl := columns.UpdateColumnSet(
			bordereauxReportAllColumns,
			bordereauxReportPrimaryKeyColumns,
		)

		if !columns.IsWhitelist() {
			wl = strmangle.SetComplement(wl, []string{"created_at"})
		}
		if len(wl) == 0 {
			return 0, errors.New("claims: unable to update bordereaux_reports, could not build whitelist")
		}

		cache.query = fmt.Sprintf("UPDATE \"claims\".\"bordereaux_reports\" SET %s WHERE %s",
			strmangle.SetParamNames("\"", "\"", 1, wl),
			strmangle.WhereClause("\"", "\"", len(wl)+1, bordereauxReportPrimaryKeyColumns),
		)
		cache.valueMapping, err = queries.BindMapping(bordereauxReportType, bordereauxReportMapping, append(wl, bordereauxReportPrimaryKeyColumns...))
		if err != nil {
			return 0, err
		}
	}

	values := queries.ValuesFromMapping(reflect.Indirect(reflect.ValueOf(o)), cache.valueMapping)

	if boil.IsDebug(ctx) {
		writer := boil.DebugWriterFrom(ctx)
		fmt.Fprintln(writer, cache.query)
		fmt.Fprintln(writer, values)
	}
	var result sql.Result
	result, err = exec.ExecContext(ctx, cache.query, values...)
	if err != nil {
		return 0, errors.Wrap(err, "claims: unable to update bordereaux_reports row")
	}

	rowsAff, err := result.RowsAffected()
	if err != nil {
		return 0, errors.Wrap(err, "claims: failed to get rows affected by update for bordereaux_reports")
	}

	if !cached {
		bordereauxReportUpdateCacheMut.Lock()
		bordereauxReportUpdateCache[key] = cache
		bordereauxReportUpdateCacheMut.Unlock()
	}

	return rowsAff, o.doAfterUpdateHooks(ctx, exec)
}

// UpdateAll updates all rows with the specified column values.
func (q bordereauxReportQuery) UpdateAll(ctx context.Context, exec boil.ContextExecutor, cols M) (int64, error) {
	queries.SetUpdate(q.Query, cols)

	result, err := q.Query.ExecContext(ctx, exec)
	if err != nil {
		return 0, errors.Wrap(err, "claims: unable to update all for bordereaux_reports")
	}

	rowsAff, err := result.RowsAffected()
	if err != nil {
		return 0, errors.Wrap(err, "claims: unable to retrieve rows affected for bordereaux_reports")
	}

	return rowsAff, nil
}

// UpdateAll updates all rows with the specified column values, using an executor.
func (o BordereauxReportSlice) UpdateAll(ctx context.Context, exec boil.ContextExecutor, cols M) (int64, error) {
	ln := int64(len(o))
	if ln == 0 {
		return 0, nil
	}

	if len(cols) == 0 {
		return 0, errors.New("claims: update all requires at least one column argument")
	}

	colNames := make([]string, len(cols))
	args := make([]interface{}, len(cols))

	i := 0
	for name, value := range cols {
		colNames[i] = name
		args[i] = value
		i++
	}

	// Append all of the primary key values for each column
	for _, obj := range o {
		pkeyArgs := queries.ValuesFromMapping(reflect.Indirect(reflect.ValueOf(obj)), bordereauxReportPrimaryKeyMapping)
		args = append(args, pkeyArgs...)
	}

	sql := fmt.Sprintf("UPDATE \"claims\".\"bordereaux_reports\" SET %s WHERE %s",
		strmangle.SetParamNames("\"", "\"", 1, colNames),
		strmangle.WhereClauseRepeated(string(dialect.LQ), string(dialect.RQ), len(colNames)+1, bordereauxReportPrimaryKeyColumns, len(o)))

	if boil.IsDebug(ctx) {
		writer := boil.DebugWriterFrom(ctx)
		fmt.Fprintln(writer, sql)
		fmt.Fprintln(writer, args...)
	}
	result, err := exec.ExecContext(ctx, sql, args...)
	if err != nil {
		return 0, errors.Wrap(err, "claims: unable to update all in bordereauxReport slice")
	}

	rowsAff, err := result.RowsAffected()
	if err != nil {
		return 0, errors.Wrap(err, "claims: unable to retrieve rows affected all in update all bordereauxReport")
	}
	return rowsAff, nil
}

// Upsert attempts an insert using an executor, and does an update or ignore on conflict.
// See boil.Columns documentation for how to properly use updateColumns and insertColumns.
func (o *BordereauxReport) Upsert(ctx context.Context, exec boil.ContextExecutor, updateOnConflict bool, conflictColumns []string, updateColumns, insertColumns boil.Columns) error {
	if o == nil {
		return errors.New("claims: no bordereaux_reports provided for upsert")
	}
	if !boil.TimestampsAreSkipped(ctx) {
		currTime := time.Now().In(boil.GetLocation())

		if o.CreatedAt.IsZero() {
			o.CreatedAt = currTime
		}
	}

	if err := o.doBeforeUpsertHooks(ctx, exec); err != nil {
		return err
	}

	nzDefaults := queries.NonZeroDefaultSet(bordereauxReportColumnsWithDefault, o)

	// Build cache key in-line uglily - mysql vs psql problems
	buf := strmangle.GetBuffer()
	if updateOnConflict {
		buf.WriteByte('t')
	} else {
		buf.WriteByte('f')
	}
	buf.WriteByte('.')
	for _, c := range conflictColumns {
		buf.WriteString(c)
	}
	buf.WriteByte('.')
	buf.WriteString(strconv.Itoa(updateColumns.Kind))
	for _, c := range updateColumns.Cols {
		buf.WriteString(c)
	}
	buf.WriteByte('.')
	buf.WriteString(strconv.Itoa(insertColumns.Kind))
	for _, c := range insertColumns.Cols {
		buf.WriteString(c)
	}
	buf.WriteByte('.')
	for _, c := range nzDefaults {
		buf.WriteString(c)
	}
	key := buf.String()
	strmangle.PutBuffer(buf)

	bordereauxReportUpsertCacheMut.RLock()
	cache, cached := bordereauxReportUpsertCache[key]
	bordereauxReportUpsertCacheMut.RUnlock()

	var err error

	if !cached {
		insert, ret := insertColumns.InsertColumnSet(
			bordereauxReportAllColumns,
			bordereauxReportColumnsWithDefault,
			bordereauxReportColumnsWithoutDefault,
			nzDefaults,
		)

		update := updateColumns.UpdateColumnSet(
			bordereauxReportAllColumns,
			bordereauxReportPrimaryKeyColumns,
		)

		if updateOnConflict && len(update) == 0 {
			return errors.New("claims: unable to upsert bordereaux_reports, could not build update column list")
		}

		conflict := conflictColumns
		if len(conflict) == 0 {
			conflict = make([]string, len(bordereauxReportPrimaryKeyColumns))
			copy(conflict, bordereauxReportPrimaryKeyColumns)
		}
		cache.query = buildUpsertQueryPostgres(dialect, "\"claims\".\"bordereaux_reports\"", updateOnConflict, ret, update, conflict, insert)

		cache.valueMapping, err = queries.BindMapping(bordereauxReportType, bordereauxReportMapping, insert)
		if err != nil {
			return err
		}
		if len(ret) != 0 {
			cache.retMapping, err = queries.BindMapping(bordereauxReportType, bordereauxReportMapping, ret)
			if err != nil {
				return err
			}
		}
	}

	value := reflect.Indirect(reflect.ValueOf(o))
	vals := queries.ValuesFromMapping(value, cache.valueMapping)
	var returns []interface{}
	if len(cache.retMapping) != 0 {
		returns = queries.PtrsFromMapping(value, cache.retMapping)
	}

	if boil.IsDebug(ctx) {
		writer := boil.DebugWriterFrom(ctx)
		fmt.Fprintln(writer, cache.query)
		fmt.Fprintln(writer, vals)
	}
	if len(cache.retMapping) != 0 {
		err = exec.QueryRowContext(ctx, cache.query, vals...).Scan(returns...)
		if errors.Is(err, sql.ErrNoRows) {
			err = nil // Postgres doesn't return anything when there's no update
		}
	} else {
		_, err = exec.ExecContext(ctx, cache.query, vals...)
	}
	if err != nil {
		return errors.Wrap(err, "claims: unable to upsert bordereaux_reports")
	}

	if !cached {
		bordereauxReportUpsertCacheMut.Lock()
		bordereauxReportUpsertCache[key] = cache
		bordereauxReportUpsertCacheMut.Unlock()
	}

	return o.doAfterUpsertHooks(ctx, exec)
}

// Delete deletes a single BordereauxReport record with an executor.
// Delete will match against the primary key column to find the record to delete.
func (o *BordereauxReport) Delete(ctx context.Context, exec boil.ContextExecutor) (int64, error) {
	if o == nil {
		return 0, errors.New("claims: no BordereauxReport provided for delete")
	}

	if err := o.doBeforeDeleteHooks(ctx, exec); err != nil {
		return 0, err
	}

	args := queries.ValuesFromMapping(reflect.Indirect(reflect.ValueOf(o)), bordereauxReportPrimaryKeyMapping)
	sql := "DELETE FROM \"claims\".\"bordereaux_reports\" WHERE \"id\"=$1"

	if boil.IsDebug(ctx) {
		writer := boil.DebugWriterFrom(ctx)
		fmt.Fprintln(writer, sql)
		fmt.Fprintln(writer, args...)
	}
	result, err := exec.ExecContext(ctx, sql, args...)
	if err != nil {
		return 0, errors.Wrap(err, "claims: unable to delete from bordereaux_reports")
	}

	rowsAff, err := result.RowsAffected()
	if err != nil {
		return 0, errors.Wrap(err, "claims: failed to get rows affected by delete for bordereaux_reports")
	}

	if err := o.doAfterDeleteHooks(ctx, exec); err != nil {
		return 0, err
	}

	return rowsAff, nil
}

// DeleteAll deletes all matching rows.
func (q bordereauxReportQuery) DeleteAll(ctx context.Context, exec boil.ContextExecutor) (int64, error) {
	if q.Query == nil {
		return 0, errors.New("claims: no bordereauxReportQuery provided for delete all")
	}

	queries.SetDelete(q.Query)

	result, err := q.Query.ExecContext(ctx, exec)
	if err != nil {
		return 0, errors.Wrap(err, "claims: unable to delete all from bordereaux_reports")
	}

	rowsAff, err := result.RowsAffected()
	if err != nil {
		return 0, errors.Wrap(err, "claims: failed to get rows affected by deleteall for bordereaux_reports")
	}

	return rowsAff, nil
}

// DeleteAll deletes all rows in the slice, using an executor.
func (o BordereauxReportSlice) DeleteAll(ctx context.Context, exec boil.ContextExecutor) (int64, error) {
	if len(o) == 0 {
		return 0, nil
	}

	if len(bordereauxReportBeforeDeleteHooks) != 0 {
		for _, obj := range o {
			if err := obj.doBeforeDeleteHooks(ctx, exec); err != nil {
				return 0, err
			}
		}
	}

	var args []interface{}
	for _, obj := range o {
		pkeyArgs := queries.ValuesFromMapping(reflect.Indirect(reflect.ValueOf(obj)), bordereauxReportPrimaryKeyMapping)
		args = append(args, pkeyArgs...)
	}

	sql := "DELETE FROM \"claims\".\"bordereaux_reports\" WHERE " +
		strmangle.WhereClauseRepeated(string(dialect.LQ), string(dialect.RQ), 1, bordereauxReportPrimaryKeyColumns, len(o))

	if boil.IsDebug(ctx) {
		writer := boil.DebugWriterFrom(ctx)
		fmt.Fprintln(writer, sql)
		fmt.Fprintln(writer, args)
	}
	result, err := exec.ExecContext(ctx, sql, args...)
	if err != nil {
		return 0, errors.Wrap(err, "claims: unable to delete all from bordereauxReport slice")
	}

	rowsAff, err := result.RowsAffected()
	if err != nil {
		return 0, errors.Wrap(err, "claims: failed to get rows affected by deleteall for bordereaux_reports")
	}

	if len(bordereauxReportAfterDeleteHooks) != 0 {
		for _, obj := range o {
			if err := obj.doAfterDeleteHooks(ctx, exec); err != nil {
				return 0, err
			}
		}
	}

	return rowsAff, nil
}

// Reload refetches the object from the database
// using the primary keys with an executor.
func (o *BordereauxReport) Reload(ctx context.Context, exec boil.ContextExecutor) error {
	ret, err := FindBordereauxReport(ctx, exec, o.ID)
	if err != nil {
		return err
	}

	*o = *ret
	return nil
}

// ReloadAll refetches every row with matching primary key column values
// and overwrites the original object slice with the newly updated slice.
func (o *BordereauxReportSlice) ReloadAll(ctx context.Context, exec boil.ContextExecutor) error {
	if o == nil || len(*o) == 0 {
		return nil
	}

	slice := BordereauxReportSlice{}
	var args []interface{}
	for _, obj := range *o {
		pkeyArgs := queries.ValuesFromMapping(reflect.Indirect(reflect.ValueOf(obj)), bordereauxReportPrimaryKeyMapping)
		args = append(args, pkeyArgs...)
	}

	sql := "SELECT \"claims\".\"bordereaux_reports\".* FROM \"claims\".\"bordereaux_reports\" WHERE " +
		strmangle.WhereClauseRepeated(string(dialect.LQ), string(dialect.RQ), 1, bordereauxReportPrimaryKeyColumns, len(*o))

	q := queries.Raw(sql, args...)

	err := q.Bind(ctx, exec, &slice)
	if err != nil {
		return errors.Wrap(err, "claims: unable to reload all in BordereauxReportSlice")
	}

	*o = slice

	return nil
}

// BordereauxReportExists checks if the BordereauxReport row exists.
func BordereauxReportExists(ctx context.Context, exec boil.ContextExecutor, iD string) (bool, error) {
	var exists bool
	sql := "select exists(select 1 from \"claims\".\"bordereaux_reports\" where \"id\"=$1 limit 1)"

	if boil.IsDebug(ctx) {
		writer := boil.DebugWriterFrom(ctx)
		fmt.Fprintln(writer, sql)
		fmt.Fprintln(writer, iD)
	}
	row := exec.QueryRowContext(ctx, sql, iD)

	err := row.Scan(&exists)
	if err != nil {
		return false, errors.Wrap(err, "claims: unable to check if bordereaux_reports exists")
	}

	return exists, nil
}
