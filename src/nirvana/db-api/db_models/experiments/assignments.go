// Code generated by SQLBoiler (https://github.com/volatiletech/sqlboiler). DO NOT EDIT.
// This file is meant to be re-generated in place and/or deleted at any time.

package experiments

import (
	"context"
	"database/sql"
	"fmt"
	"reflect"
	"strconv"
	"strings"
	"sync"
	"time"

	"github.com/friendsofgo/errors"
	"github.com/volatiletech/null/v8"
	"github.com/volatiletech/sqlboiler/v4/boil"
	"github.com/volatiletech/sqlboiler/v4/queries"
	"github.com/volatiletech/sqlboiler/v4/queries/qm"
	"github.com/volatiletech/sqlboiler/v4/queries/qmhelper"
	"github.com/volatiletech/strmangle"
)

// Assignment is an object representing the database table.
type Assignment struct {
	ID           string      `boil:"id" json:"id" toml:"id" yaml:"id"`
	ExperimentID string      `boil:"experiment_id" json:"experiment_id" toml:"experiment_id" yaml:"experiment_id"`
	VariantID    string      `boil:"variant_id" json:"variant_id" toml:"variant_id" yaml:"variant_id"`
	SubjectID    string      `boil:"subject_id" json:"subject_id" toml:"subject_id" yaml:"subject_id"`
	Enabled      bool        `boil:"enabled" json:"enabled" toml:"enabled" yaml:"enabled"`
	ChangeReason null.String `boil:"change_reason" json:"change_reason,omitempty" toml:"change_reason" yaml:"change_reason,omitempty"`
	Context      null.JSON   `boil:"context" json:"context,omitempty" toml:"context" yaml:"context,omitempty"`
	CreatedAt    time.Time   `boil:"created_at" json:"created_at" toml:"created_at" yaml:"created_at"`
	Domain       string      `boil:"domain" json:"domain" toml:"domain" yaml:"domain"`

	R *assignmentR `boil:"" json:"" toml:"" yaml:""`
	L assignmentL  `boil:"-" json:"-" toml:"-" yaml:"-"`
}

var AssignmentColumns = struct {
	ID           string
	ExperimentID string
	VariantID    string
	SubjectID    string
	Enabled      string
	ChangeReason string
	Context      string
	CreatedAt    string
	Domain       string
}{
	ID:           "id",
	ExperimentID: "experiment_id",
	VariantID:    "variant_id",
	SubjectID:    "subject_id",
	Enabled:      "enabled",
	ChangeReason: "change_reason",
	Context:      "context",
	CreatedAt:    "created_at",
	Domain:       "domain",
}

var AssignmentTableColumns = struct {
	ID           string
	ExperimentID string
	VariantID    string
	SubjectID    string
	Enabled      string
	ChangeReason string
	Context      string
	CreatedAt    string
	Domain       string
}{
	ID:           "assignments.id",
	ExperimentID: "assignments.experiment_id",
	VariantID:    "assignments.variant_id",
	SubjectID:    "assignments.subject_id",
	Enabled:      "assignments.enabled",
	ChangeReason: "assignments.change_reason",
	Context:      "assignments.context",
	CreatedAt:    "assignments.created_at",
	Domain:       "assignments.domain",
}

// Generated where

type whereHelpernull_String struct{ field string }

func (w whereHelpernull_String) EQ(x null.String) qm.QueryMod {
	return qmhelper.WhereNullEQ(w.field, false, x)
}
func (w whereHelpernull_String) NEQ(x null.String) qm.QueryMod {
	return qmhelper.WhereNullEQ(w.field, true, x)
}
func (w whereHelpernull_String) LT(x null.String) qm.QueryMod {
	return qmhelper.Where(w.field, qmhelper.LT, x)
}
func (w whereHelpernull_String) LTE(x null.String) qm.QueryMod {
	return qmhelper.Where(w.field, qmhelper.LTE, x)
}
func (w whereHelpernull_String) GT(x null.String) qm.QueryMod {
	return qmhelper.Where(w.field, qmhelper.GT, x)
}
func (w whereHelpernull_String) GTE(x null.String) qm.QueryMod {
	return qmhelper.Where(w.field, qmhelper.GTE, x)
}

func (w whereHelpernull_String) IsNull() qm.QueryMod    { return qmhelper.WhereIsNull(w.field) }
func (w whereHelpernull_String) IsNotNull() qm.QueryMod { return qmhelper.WhereIsNotNull(w.field) }

var AssignmentWhere = struct {
	ID           whereHelperstring
	ExperimentID whereHelperstring
	VariantID    whereHelperstring
	SubjectID    whereHelperstring
	Enabled      whereHelperbool
	ChangeReason whereHelpernull_String
	Context      whereHelpernull_JSON
	CreatedAt    whereHelpertime_Time
	Domain       whereHelperstring
}{
	ID:           whereHelperstring{field: "\"experiments\".\"assignments\".\"id\""},
	ExperimentID: whereHelperstring{field: "\"experiments\".\"assignments\".\"experiment_id\""},
	VariantID:    whereHelperstring{field: "\"experiments\".\"assignments\".\"variant_id\""},
	SubjectID:    whereHelperstring{field: "\"experiments\".\"assignments\".\"subject_id\""},
	Enabled:      whereHelperbool{field: "\"experiments\".\"assignments\".\"enabled\""},
	ChangeReason: whereHelpernull_String{field: "\"experiments\".\"assignments\".\"change_reason\""},
	Context:      whereHelpernull_JSON{field: "\"experiments\".\"assignments\".\"context\""},
	CreatedAt:    whereHelpertime_Time{field: "\"experiments\".\"assignments\".\"created_at\""},
	Domain:       whereHelperstring{field: "\"experiments\".\"assignments\".\"domain\""},
}

// AssignmentRels is where relationship names are stored.
var AssignmentRels = struct {
	Experiment string
	Variant    string
}{
	Experiment: "Experiment",
	Variant:    "Variant",
}

// assignmentR is where relationships are stored.
type assignmentR struct {
	Experiment *ExperimentDetail `boil:"Experiment" json:"Experiment" toml:"Experiment" yaml:"Experiment"`
	Variant    *Variant          `boil:"Variant" json:"Variant" toml:"Variant" yaml:"Variant"`
}

// NewStruct creates a new relationship struct
func (*assignmentR) NewStruct() *assignmentR {
	return &assignmentR{}
}

// assignmentL is where Load methods for each relationship are stored.
type assignmentL struct{}

var (
	assignmentAllColumns            = []string{"id", "experiment_id", "variant_id", "subject_id", "enabled", "change_reason", "context", "created_at", "domain"}
	assignmentColumnsWithoutDefault = []string{"id", "experiment_id", "variant_id", "subject_id", "enabled", "created_at"}
	assignmentColumnsWithDefault    = []string{"change_reason", "context", "domain"}
	assignmentPrimaryKeyColumns     = []string{"id"}
	assignmentGeneratedColumns      = []string{}
)

type (
	// AssignmentSlice is an alias for a slice of pointers to Assignment.
	// This should almost always be used instead of []Assignment.
	AssignmentSlice []*Assignment
	// AssignmentHook is the signature for custom Assignment hook methods
	AssignmentHook func(context.Context, boil.ContextExecutor, *Assignment) error

	assignmentQuery struct {
		*queries.Query
	}
)

// Cache for insert, update and upsert
var (
	assignmentType                 = reflect.TypeOf(&Assignment{})
	assignmentMapping              = queries.MakeStructMapping(assignmentType)
	assignmentPrimaryKeyMapping, _ = queries.BindMapping(assignmentType, assignmentMapping, assignmentPrimaryKeyColumns)
	assignmentInsertCacheMut       sync.RWMutex
	assignmentInsertCache          = make(map[string]insertCache)
	assignmentUpdateCacheMut       sync.RWMutex
	assignmentUpdateCache          = make(map[string]updateCache)
	assignmentUpsertCacheMut       sync.RWMutex
	assignmentUpsertCache          = make(map[string]insertCache)
)

var (
	// Force time package dependency for automated UpdatedAt/CreatedAt.
	_ = time.Second
	// Force qmhelper dependency for where clause generation (which doesn't
	// always happen)
	_ = qmhelper.Where
)

var assignmentAfterSelectHooks []AssignmentHook

var assignmentBeforeInsertHooks []AssignmentHook
var assignmentAfterInsertHooks []AssignmentHook

var assignmentBeforeUpdateHooks []AssignmentHook
var assignmentAfterUpdateHooks []AssignmentHook

var assignmentBeforeDeleteHooks []AssignmentHook
var assignmentAfterDeleteHooks []AssignmentHook

var assignmentBeforeUpsertHooks []AssignmentHook
var assignmentAfterUpsertHooks []AssignmentHook

// doAfterSelectHooks executes all "after Select" hooks.
func (o *Assignment) doAfterSelectHooks(ctx context.Context, exec boil.ContextExecutor) (err error) {
	if boil.HooksAreSkipped(ctx) {
		return nil
	}

	for _, hook := range assignmentAfterSelectHooks {
		if err := hook(ctx, exec, o); err != nil {
			return err
		}
	}

	return nil
}

// doBeforeInsertHooks executes all "before insert" hooks.
func (o *Assignment) doBeforeInsertHooks(ctx context.Context, exec boil.ContextExecutor) (err error) {
	if boil.HooksAreSkipped(ctx) {
		return nil
	}

	for _, hook := range assignmentBeforeInsertHooks {
		if err := hook(ctx, exec, o); err != nil {
			return err
		}
	}

	return nil
}

// doAfterInsertHooks executes all "after Insert" hooks.
func (o *Assignment) doAfterInsertHooks(ctx context.Context, exec boil.ContextExecutor) (err error) {
	if boil.HooksAreSkipped(ctx) {
		return nil
	}

	for _, hook := range assignmentAfterInsertHooks {
		if err := hook(ctx, exec, o); err != nil {
			return err
		}
	}

	return nil
}

// doBeforeUpdateHooks executes all "before Update" hooks.
func (o *Assignment) doBeforeUpdateHooks(ctx context.Context, exec boil.ContextExecutor) (err error) {
	if boil.HooksAreSkipped(ctx) {
		return nil
	}

	for _, hook := range assignmentBeforeUpdateHooks {
		if err := hook(ctx, exec, o); err != nil {
			return err
		}
	}

	return nil
}

// doAfterUpdateHooks executes all "after Update" hooks.
func (o *Assignment) doAfterUpdateHooks(ctx context.Context, exec boil.ContextExecutor) (err error) {
	if boil.HooksAreSkipped(ctx) {
		return nil
	}

	for _, hook := range assignmentAfterUpdateHooks {
		if err := hook(ctx, exec, o); err != nil {
			return err
		}
	}

	return nil
}

// doBeforeDeleteHooks executes all "before Delete" hooks.
func (o *Assignment) doBeforeDeleteHooks(ctx context.Context, exec boil.ContextExecutor) (err error) {
	if boil.HooksAreSkipped(ctx) {
		return nil
	}

	for _, hook := range assignmentBeforeDeleteHooks {
		if err := hook(ctx, exec, o); err != nil {
			return err
		}
	}

	return nil
}

// doAfterDeleteHooks executes all "after Delete" hooks.
func (o *Assignment) doAfterDeleteHooks(ctx context.Context, exec boil.ContextExecutor) (err error) {
	if boil.HooksAreSkipped(ctx) {
		return nil
	}

	for _, hook := range assignmentAfterDeleteHooks {
		if err := hook(ctx, exec, o); err != nil {
			return err
		}
	}

	return nil
}

// doBeforeUpsertHooks executes all "before Upsert" hooks.
func (o *Assignment) doBeforeUpsertHooks(ctx context.Context, exec boil.ContextExecutor) (err error) {
	if boil.HooksAreSkipped(ctx) {
		return nil
	}

	for _, hook := range assignmentBeforeUpsertHooks {
		if err := hook(ctx, exec, o); err != nil {
			return err
		}
	}

	return nil
}

// doAfterUpsertHooks executes all "after Upsert" hooks.
func (o *Assignment) doAfterUpsertHooks(ctx context.Context, exec boil.ContextExecutor) (err error) {
	if boil.HooksAreSkipped(ctx) {
		return nil
	}

	for _, hook := range assignmentAfterUpsertHooks {
		if err := hook(ctx, exec, o); err != nil {
			return err
		}
	}

	return nil
}

// AddAssignmentHook registers your hook function for all future operations.
func AddAssignmentHook(hookPoint boil.HookPoint, assignmentHook AssignmentHook) {
	switch hookPoint {
	case boil.AfterSelectHook:
		assignmentAfterSelectHooks = append(assignmentAfterSelectHooks, assignmentHook)
	case boil.BeforeInsertHook:
		assignmentBeforeInsertHooks = append(assignmentBeforeInsertHooks, assignmentHook)
	case boil.AfterInsertHook:
		assignmentAfterInsertHooks = append(assignmentAfterInsertHooks, assignmentHook)
	case boil.BeforeUpdateHook:
		assignmentBeforeUpdateHooks = append(assignmentBeforeUpdateHooks, assignmentHook)
	case boil.AfterUpdateHook:
		assignmentAfterUpdateHooks = append(assignmentAfterUpdateHooks, assignmentHook)
	case boil.BeforeDeleteHook:
		assignmentBeforeDeleteHooks = append(assignmentBeforeDeleteHooks, assignmentHook)
	case boil.AfterDeleteHook:
		assignmentAfterDeleteHooks = append(assignmentAfterDeleteHooks, assignmentHook)
	case boil.BeforeUpsertHook:
		assignmentBeforeUpsertHooks = append(assignmentBeforeUpsertHooks, assignmentHook)
	case boil.AfterUpsertHook:
		assignmentAfterUpsertHooks = append(assignmentAfterUpsertHooks, assignmentHook)
	}
}

// One returns a single assignment record from the query.
func (q assignmentQuery) One(ctx context.Context, exec boil.ContextExecutor) (*Assignment, error) {
	o := &Assignment{}

	queries.SetLimit(q.Query, 1)

	err := q.Bind(ctx, exec, o)
	if err != nil {
		if errors.Is(err, sql.ErrNoRows) {
			return nil, sql.ErrNoRows
		}
		return nil, errors.Wrap(err, "experiments: failed to execute a one query for assignments")
	}

	if err := o.doAfterSelectHooks(ctx, exec); err != nil {
		return o, err
	}

	return o, nil
}

// All returns all Assignment records from the query.
func (q assignmentQuery) All(ctx context.Context, exec boil.ContextExecutor) (AssignmentSlice, error) {
	var o []*Assignment

	err := q.Bind(ctx, exec, &o)
	if err != nil {
		return nil, errors.Wrap(err, "experiments: failed to assign all query results to Assignment slice")
	}

	if len(assignmentAfterSelectHooks) != 0 {
		for _, obj := range o {
			if err := obj.doAfterSelectHooks(ctx, exec); err != nil {
				return o, err
			}
		}
	}

	return o, nil
}

// Count returns the count of all Assignment records in the query.
func (q assignmentQuery) Count(ctx context.Context, exec boil.ContextExecutor) (int64, error) {
	var count int64

	queries.SetSelect(q.Query, nil)
	queries.SetCount(q.Query)

	err := q.Query.QueryRowContext(ctx, exec).Scan(&count)
	if err != nil {
		return 0, errors.Wrap(err, "experiments: failed to count assignments rows")
	}

	return count, nil
}

// Exists checks if the row exists in the table.
func (q assignmentQuery) Exists(ctx context.Context, exec boil.ContextExecutor) (bool, error) {
	var count int64

	queries.SetSelect(q.Query, nil)
	queries.SetCount(q.Query)
	queries.SetLimit(q.Query, 1)

	err := q.Query.QueryRowContext(ctx, exec).Scan(&count)
	if err != nil {
		return false, errors.Wrap(err, "experiments: failed to check if assignments exists")
	}

	return count > 0, nil
}

// Experiment pointed to by the foreign key.
func (o *Assignment) Experiment(mods ...qm.QueryMod) experimentDetailQuery {
	queryMods := []qm.QueryMod{
		qm.Where("\"id\" = ?", o.ExperimentID),
	}

	queryMods = append(queryMods, mods...)

	return ExperimentDetails(queryMods...)
}

// Variant pointed to by the foreign key.
func (o *Assignment) Variant(mods ...qm.QueryMod) variantQuery {
	queryMods := []qm.QueryMod{
		qm.Where("\"id\" = ?", o.VariantID),
	}

	queryMods = append(queryMods, mods...)

	return Variants(queryMods...)
}

// LoadExperiment allows an eager lookup of values, cached into the
// loaded structs of the objects. This is for an N-1 relationship.
func (assignmentL) LoadExperiment(ctx context.Context, e boil.ContextExecutor, singular bool, maybeAssignment interface{}, mods queries.Applicator) error {
	var slice []*Assignment
	var object *Assignment

	if singular {
		object = maybeAssignment.(*Assignment)
	} else {
		slice = *maybeAssignment.(*[]*Assignment)
	}

	args := make([]interface{}, 0, 1)
	if singular {
		if object.R == nil {
			object.R = &assignmentR{}
		}
		args = append(args, object.ExperimentID)

	} else {
	Outer:
		for _, obj := range slice {
			if obj.R == nil {
				obj.R = &assignmentR{}
			}

			for _, a := range args {
				if a == obj.ExperimentID {
					continue Outer
				}
			}

			args = append(args, obj.ExperimentID)

		}
	}

	if len(args) == 0 {
		return nil
	}

	query := NewQuery(
		qm.From(`experiments.experiment_details`),
		qm.WhereIn(`experiments.experiment_details.id in ?`, args...),
	)
	if mods != nil {
		mods.Apply(query)
	}

	results, err := query.QueryContext(ctx, e)
	if err != nil {
		return errors.Wrap(err, "failed to eager load ExperimentDetail")
	}

	var resultSlice []*ExperimentDetail
	if err = queries.Bind(results, &resultSlice); err != nil {
		return errors.Wrap(err, "failed to bind eager loaded slice ExperimentDetail")
	}

	if err = results.Close(); err != nil {
		return errors.Wrap(err, "failed to close results of eager load for experiment_details")
	}
	if err = results.Err(); err != nil {
		return errors.Wrap(err, "error occurred during iteration of eager loaded relations for experiment_details")
	}

	if len(assignmentAfterSelectHooks) != 0 {
		for _, obj := range resultSlice {
			if err := obj.doAfterSelectHooks(ctx, e); err != nil {
				return err
			}
		}
	}

	if len(resultSlice) == 0 {
		return nil
	}

	if singular {
		foreign := resultSlice[0]
		object.R.Experiment = foreign
		if foreign.R == nil {
			foreign.R = &experimentDetailR{}
		}
		foreign.R.ExperimentAssignments = append(foreign.R.ExperimentAssignments, object)
		return nil
	}

	for _, local := range slice {
		for _, foreign := range resultSlice {
			if local.ExperimentID == foreign.ID {
				local.R.Experiment = foreign
				if foreign.R == nil {
					foreign.R = &experimentDetailR{}
				}
				foreign.R.ExperimentAssignments = append(foreign.R.ExperimentAssignments, local)
				break
			}
		}
	}

	return nil
}

// LoadVariant allows an eager lookup of values, cached into the
// loaded structs of the objects. This is for an N-1 relationship.
func (assignmentL) LoadVariant(ctx context.Context, e boil.ContextExecutor, singular bool, maybeAssignment interface{}, mods queries.Applicator) error {
	var slice []*Assignment
	var object *Assignment

	if singular {
		object = maybeAssignment.(*Assignment)
	} else {
		slice = *maybeAssignment.(*[]*Assignment)
	}

	args := make([]interface{}, 0, 1)
	if singular {
		if object.R == nil {
			object.R = &assignmentR{}
		}
		args = append(args, object.VariantID)

	} else {
	Outer:
		for _, obj := range slice {
			if obj.R == nil {
				obj.R = &assignmentR{}
			}

			for _, a := range args {
				if a == obj.VariantID {
					continue Outer
				}
			}

			args = append(args, obj.VariantID)

		}
	}

	if len(args) == 0 {
		return nil
	}

	query := NewQuery(
		qm.From(`experiments.variants`),
		qm.WhereIn(`experiments.variants.id in ?`, args...),
	)
	if mods != nil {
		mods.Apply(query)
	}

	results, err := query.QueryContext(ctx, e)
	if err != nil {
		return errors.Wrap(err, "failed to eager load Variant")
	}

	var resultSlice []*Variant
	if err = queries.Bind(results, &resultSlice); err != nil {
		return errors.Wrap(err, "failed to bind eager loaded slice Variant")
	}

	if err = results.Close(); err != nil {
		return errors.Wrap(err, "failed to close results of eager load for variants")
	}
	if err = results.Err(); err != nil {
		return errors.Wrap(err, "error occurred during iteration of eager loaded relations for variants")
	}

	if len(assignmentAfterSelectHooks) != 0 {
		for _, obj := range resultSlice {
			if err := obj.doAfterSelectHooks(ctx, e); err != nil {
				return err
			}
		}
	}

	if len(resultSlice) == 0 {
		return nil
	}

	if singular {
		foreign := resultSlice[0]
		object.R.Variant = foreign
		if foreign.R == nil {
			foreign.R = &variantR{}
		}
		foreign.R.Assignments = append(foreign.R.Assignments, object)
		return nil
	}

	for _, local := range slice {
		for _, foreign := range resultSlice {
			if local.VariantID == foreign.ID {
				local.R.Variant = foreign
				if foreign.R == nil {
					foreign.R = &variantR{}
				}
				foreign.R.Assignments = append(foreign.R.Assignments, local)
				break
			}
		}
	}

	return nil
}

// SetExperiment of the assignment to the related item.
// Sets o.R.Experiment to related.
// Adds o to related.R.ExperimentAssignments.
func (o *Assignment) SetExperiment(ctx context.Context, exec boil.ContextExecutor, insert bool, related *ExperimentDetail) error {
	var err error
	if insert {
		if err = related.Insert(ctx, exec, boil.Infer()); err != nil {
			return errors.Wrap(err, "failed to insert into foreign table")
		}
	}

	updateQuery := fmt.Sprintf(
		"UPDATE \"experiments\".\"assignments\" SET %s WHERE %s",
		strmangle.SetParamNames("\"", "\"", 1, []string{"experiment_id"}),
		strmangle.WhereClause("\"", "\"", 2, assignmentPrimaryKeyColumns),
	)
	values := []interface{}{related.ID, o.ID}

	if boil.IsDebug(ctx) {
		writer := boil.DebugWriterFrom(ctx)
		fmt.Fprintln(writer, updateQuery)
		fmt.Fprintln(writer, values)
	}
	if _, err = exec.ExecContext(ctx, updateQuery, values...); err != nil {
		return errors.Wrap(err, "failed to update local table")
	}

	o.ExperimentID = related.ID
	if o.R == nil {
		o.R = &assignmentR{
			Experiment: related,
		}
	} else {
		o.R.Experiment = related
	}

	if related.R == nil {
		related.R = &experimentDetailR{
			ExperimentAssignments: AssignmentSlice{o},
		}
	} else {
		related.R.ExperimentAssignments = append(related.R.ExperimentAssignments, o)
	}

	return nil
}

// SetVariant of the assignment to the related item.
// Sets o.R.Variant to related.
// Adds o to related.R.Assignments.
func (o *Assignment) SetVariant(ctx context.Context, exec boil.ContextExecutor, insert bool, related *Variant) error {
	var err error
	if insert {
		if err = related.Insert(ctx, exec, boil.Infer()); err != nil {
			return errors.Wrap(err, "failed to insert into foreign table")
		}
	}

	updateQuery := fmt.Sprintf(
		"UPDATE \"experiments\".\"assignments\" SET %s WHERE %s",
		strmangle.SetParamNames("\"", "\"", 1, []string{"variant_id"}),
		strmangle.WhereClause("\"", "\"", 2, assignmentPrimaryKeyColumns),
	)
	values := []interface{}{related.ID, o.ID}

	if boil.IsDebug(ctx) {
		writer := boil.DebugWriterFrom(ctx)
		fmt.Fprintln(writer, updateQuery)
		fmt.Fprintln(writer, values)
	}
	if _, err = exec.ExecContext(ctx, updateQuery, values...); err != nil {
		return errors.Wrap(err, "failed to update local table")
	}

	o.VariantID = related.ID
	if o.R == nil {
		o.R = &assignmentR{
			Variant: related,
		}
	} else {
		o.R.Variant = related
	}

	if related.R == nil {
		related.R = &variantR{
			Assignments: AssignmentSlice{o},
		}
	} else {
		related.R.Assignments = append(related.R.Assignments, o)
	}

	return nil
}

// Assignments retrieves all the records using an executor.
func Assignments(mods ...qm.QueryMod) assignmentQuery {
	mods = append(mods, qm.From("\"experiments\".\"assignments\""))
	q := NewQuery(mods...)
	if len(queries.GetSelect(q)) == 0 {
		queries.SetSelect(q, []string{"\"experiments\".\"assignments\".*"})
	}

	return assignmentQuery{q}
}

// FindAssignment retrieves a single record by ID with an executor.
// If selectCols is empty Find will return all columns.
func FindAssignment(ctx context.Context, exec boil.ContextExecutor, iD string, selectCols ...string) (*Assignment, error) {
	assignmentObj := &Assignment{}

	sel := "*"
	if len(selectCols) > 0 {
		sel = strings.Join(strmangle.IdentQuoteSlice(dialect.LQ, dialect.RQ, selectCols), ",")
	}
	query := fmt.Sprintf(
		"select %s from \"experiments\".\"assignments\" where \"id\"=$1", sel,
	)

	q := queries.Raw(query, iD)

	err := q.Bind(ctx, exec, assignmentObj)
	if err != nil {
		if errors.Is(err, sql.ErrNoRows) {
			return nil, sql.ErrNoRows
		}
		return nil, errors.Wrap(err, "experiments: unable to select from assignments")
	}

	if err = assignmentObj.doAfterSelectHooks(ctx, exec); err != nil {
		return assignmentObj, err
	}

	return assignmentObj, nil
}

// Insert a single record using an executor.
// See boil.Columns.InsertColumnSet documentation to understand column list inference for inserts.
func (o *Assignment) Insert(ctx context.Context, exec boil.ContextExecutor, columns boil.Columns) error {
	if o == nil {
		return errors.New("experiments: no assignments provided for insertion")
	}

	var err error
	if !boil.TimestampsAreSkipped(ctx) {
		currTime := time.Now().In(boil.GetLocation())

		if o.CreatedAt.IsZero() {
			o.CreatedAt = currTime
		}
	}

	if err := o.doBeforeInsertHooks(ctx, exec); err != nil {
		return err
	}

	nzDefaults := queries.NonZeroDefaultSet(assignmentColumnsWithDefault, o)

	key := makeCacheKey(columns, nzDefaults)
	assignmentInsertCacheMut.RLock()
	cache, cached := assignmentInsertCache[key]
	assignmentInsertCacheMut.RUnlock()

	if !cached {
		wl, returnColumns := columns.InsertColumnSet(
			assignmentAllColumns,
			assignmentColumnsWithDefault,
			assignmentColumnsWithoutDefault,
			nzDefaults,
		)

		cache.valueMapping, err = queries.BindMapping(assignmentType, assignmentMapping, wl)
		if err != nil {
			return err
		}
		cache.retMapping, err = queries.BindMapping(assignmentType, assignmentMapping, returnColumns)
		if err != nil {
			return err
		}
		if len(wl) != 0 {
			cache.query = fmt.Sprintf("INSERT INTO \"experiments\".\"assignments\" (\"%s\") %%sVALUES (%s)%%s", strings.Join(wl, "\",\""), strmangle.Placeholders(dialect.UseIndexPlaceholders, len(wl), 1, 1))
		} else {
			cache.query = "INSERT INTO \"experiments\".\"assignments\" %sDEFAULT VALUES%s"
		}

		var queryOutput, queryReturning string

		if len(cache.retMapping) != 0 {
			queryReturning = fmt.Sprintf(" RETURNING \"%s\"", strings.Join(returnColumns, "\",\""))
		}

		cache.query = fmt.Sprintf(cache.query, queryOutput, queryReturning)
	}

	value := reflect.Indirect(reflect.ValueOf(o))
	vals := queries.ValuesFromMapping(value, cache.valueMapping)

	if boil.IsDebug(ctx) {
		writer := boil.DebugWriterFrom(ctx)
		fmt.Fprintln(writer, cache.query)
		fmt.Fprintln(writer, vals)
	}

	if len(cache.retMapping) != 0 {
		err = exec.QueryRowContext(ctx, cache.query, vals...).Scan(queries.PtrsFromMapping(value, cache.retMapping)...)
	} else {
		_, err = exec.ExecContext(ctx, cache.query, vals...)
	}

	if err != nil {
		return errors.Wrap(err, "experiments: unable to insert into assignments")
	}

	if !cached {
		assignmentInsertCacheMut.Lock()
		assignmentInsertCache[key] = cache
		assignmentInsertCacheMut.Unlock()
	}

	return o.doAfterInsertHooks(ctx, exec)
}

// Update uses an executor to update the Assignment.
// See boil.Columns.UpdateColumnSet documentation to understand column list inference for updates.
// Update does not automatically update the record in case of default values. Use .Reload() to refresh the records.
func (o *Assignment) Update(ctx context.Context, exec boil.ContextExecutor, columns boil.Columns) (int64, error) {
	var err error
	if err = o.doBeforeUpdateHooks(ctx, exec); err != nil {
		return 0, err
	}
	key := makeCacheKey(columns, nil)
	assignmentUpdateCacheMut.RLock()
	cache, cached := assignmentUpdateCache[key]
	assignmentUpdateCacheMut.RUnlock()

	if !cached {
		wl := columns.UpdateColumnSet(
			assignmentAllColumns,
			assignmentPrimaryKeyColumns,
		)

		if !columns.IsWhitelist() {
			wl = strmangle.SetComplement(wl, []string{"created_at"})
		}
		if len(wl) == 0 {
			return 0, errors.New("experiments: unable to update assignments, could not build whitelist")
		}

		cache.query = fmt.Sprintf("UPDATE \"experiments\".\"assignments\" SET %s WHERE %s",
			strmangle.SetParamNames("\"", "\"", 1, wl),
			strmangle.WhereClause("\"", "\"", len(wl)+1, assignmentPrimaryKeyColumns),
		)
		cache.valueMapping, err = queries.BindMapping(assignmentType, assignmentMapping, append(wl, assignmentPrimaryKeyColumns...))
		if err != nil {
			return 0, err
		}
	}

	values := queries.ValuesFromMapping(reflect.Indirect(reflect.ValueOf(o)), cache.valueMapping)

	if boil.IsDebug(ctx) {
		writer := boil.DebugWriterFrom(ctx)
		fmt.Fprintln(writer, cache.query)
		fmt.Fprintln(writer, values)
	}
	var result sql.Result
	result, err = exec.ExecContext(ctx, cache.query, values...)
	if err != nil {
		return 0, errors.Wrap(err, "experiments: unable to update assignments row")
	}

	rowsAff, err := result.RowsAffected()
	if err != nil {
		return 0, errors.Wrap(err, "experiments: failed to get rows affected by update for assignments")
	}

	if !cached {
		assignmentUpdateCacheMut.Lock()
		assignmentUpdateCache[key] = cache
		assignmentUpdateCacheMut.Unlock()
	}

	return rowsAff, o.doAfterUpdateHooks(ctx, exec)
}

// UpdateAll updates all rows with the specified column values.
func (q assignmentQuery) UpdateAll(ctx context.Context, exec boil.ContextExecutor, cols M) (int64, error) {
	queries.SetUpdate(q.Query, cols)

	result, err := q.Query.ExecContext(ctx, exec)
	if err != nil {
		return 0, errors.Wrap(err, "experiments: unable to update all for assignments")
	}

	rowsAff, err := result.RowsAffected()
	if err != nil {
		return 0, errors.Wrap(err, "experiments: unable to retrieve rows affected for assignments")
	}

	return rowsAff, nil
}

// UpdateAll updates all rows with the specified column values, using an executor.
func (o AssignmentSlice) UpdateAll(ctx context.Context, exec boil.ContextExecutor, cols M) (int64, error) {
	ln := int64(len(o))
	if ln == 0 {
		return 0, nil
	}

	if len(cols) == 0 {
		return 0, errors.New("experiments: update all requires at least one column argument")
	}

	colNames := make([]string, len(cols))
	args := make([]interface{}, len(cols))

	i := 0
	for name, value := range cols {
		colNames[i] = name
		args[i] = value
		i++
	}

	// Append all of the primary key values for each column
	for _, obj := range o {
		pkeyArgs := queries.ValuesFromMapping(reflect.Indirect(reflect.ValueOf(obj)), assignmentPrimaryKeyMapping)
		args = append(args, pkeyArgs...)
	}

	sql := fmt.Sprintf("UPDATE \"experiments\".\"assignments\" SET %s WHERE %s",
		strmangle.SetParamNames("\"", "\"", 1, colNames),
		strmangle.WhereClauseRepeated(string(dialect.LQ), string(dialect.RQ), len(colNames)+1, assignmentPrimaryKeyColumns, len(o)))

	if boil.IsDebug(ctx) {
		writer := boil.DebugWriterFrom(ctx)
		fmt.Fprintln(writer, sql)
		fmt.Fprintln(writer, args...)
	}
	result, err := exec.ExecContext(ctx, sql, args...)
	if err != nil {
		return 0, errors.Wrap(err, "experiments: unable to update all in assignment slice")
	}

	rowsAff, err := result.RowsAffected()
	if err != nil {
		return 0, errors.Wrap(err, "experiments: unable to retrieve rows affected all in update all assignment")
	}
	return rowsAff, nil
}

// Upsert attempts an insert using an executor, and does an update or ignore on conflict.
// See boil.Columns documentation for how to properly use updateColumns and insertColumns.
func (o *Assignment) Upsert(ctx context.Context, exec boil.ContextExecutor, updateOnConflict bool, conflictColumns []string, updateColumns, insertColumns boil.Columns) error {
	if o == nil {
		return errors.New("experiments: no assignments provided for upsert")
	}
	if !boil.TimestampsAreSkipped(ctx) {
		currTime := time.Now().In(boil.GetLocation())

		if o.CreatedAt.IsZero() {
			o.CreatedAt = currTime
		}
	}

	if err := o.doBeforeUpsertHooks(ctx, exec); err != nil {
		return err
	}

	nzDefaults := queries.NonZeroDefaultSet(assignmentColumnsWithDefault, o)

	// Build cache key in-line uglily - mysql vs psql problems
	buf := strmangle.GetBuffer()
	if updateOnConflict {
		buf.WriteByte('t')
	} else {
		buf.WriteByte('f')
	}
	buf.WriteByte('.')
	for _, c := range conflictColumns {
		buf.WriteString(c)
	}
	buf.WriteByte('.')
	buf.WriteString(strconv.Itoa(updateColumns.Kind))
	for _, c := range updateColumns.Cols {
		buf.WriteString(c)
	}
	buf.WriteByte('.')
	buf.WriteString(strconv.Itoa(insertColumns.Kind))
	for _, c := range insertColumns.Cols {
		buf.WriteString(c)
	}
	buf.WriteByte('.')
	for _, c := range nzDefaults {
		buf.WriteString(c)
	}
	key := buf.String()
	strmangle.PutBuffer(buf)

	assignmentUpsertCacheMut.RLock()
	cache, cached := assignmentUpsertCache[key]
	assignmentUpsertCacheMut.RUnlock()

	var err error

	if !cached {
		insert, ret := insertColumns.InsertColumnSet(
			assignmentAllColumns,
			assignmentColumnsWithDefault,
			assignmentColumnsWithoutDefault,
			nzDefaults,
		)

		update := updateColumns.UpdateColumnSet(
			assignmentAllColumns,
			assignmentPrimaryKeyColumns,
		)

		if updateOnConflict && len(update) == 0 {
			return errors.New("experiments: unable to upsert assignments, could not build update column list")
		}

		conflict := conflictColumns
		if len(conflict) == 0 {
			conflict = make([]string, len(assignmentPrimaryKeyColumns))
			copy(conflict, assignmentPrimaryKeyColumns)
		}
		cache.query = buildUpsertQueryPostgres(dialect, "\"experiments\".\"assignments\"", updateOnConflict, ret, update, conflict, insert)

		cache.valueMapping, err = queries.BindMapping(assignmentType, assignmentMapping, insert)
		if err != nil {
			return err
		}
		if len(ret) != 0 {
			cache.retMapping, err = queries.BindMapping(assignmentType, assignmentMapping, ret)
			if err != nil {
				return err
			}
		}
	}

	value := reflect.Indirect(reflect.ValueOf(o))
	vals := queries.ValuesFromMapping(value, cache.valueMapping)
	var returns []interface{}
	if len(cache.retMapping) != 0 {
		returns = queries.PtrsFromMapping(value, cache.retMapping)
	}

	if boil.IsDebug(ctx) {
		writer := boil.DebugWriterFrom(ctx)
		fmt.Fprintln(writer, cache.query)
		fmt.Fprintln(writer, vals)
	}
	if len(cache.retMapping) != 0 {
		err = exec.QueryRowContext(ctx, cache.query, vals...).Scan(returns...)
		if errors.Is(err, sql.ErrNoRows) {
			err = nil // Postgres doesn't return anything when there's no update
		}
	} else {
		_, err = exec.ExecContext(ctx, cache.query, vals...)
	}
	if err != nil {
		return errors.Wrap(err, "experiments: unable to upsert assignments")
	}

	if !cached {
		assignmentUpsertCacheMut.Lock()
		assignmentUpsertCache[key] = cache
		assignmentUpsertCacheMut.Unlock()
	}

	return o.doAfterUpsertHooks(ctx, exec)
}

// Delete deletes a single Assignment record with an executor.
// Delete will match against the primary key column to find the record to delete.
func (o *Assignment) Delete(ctx context.Context, exec boil.ContextExecutor) (int64, error) {
	if o == nil {
		return 0, errors.New("experiments: no Assignment provided for delete")
	}

	if err := o.doBeforeDeleteHooks(ctx, exec); err != nil {
		return 0, err
	}

	args := queries.ValuesFromMapping(reflect.Indirect(reflect.ValueOf(o)), assignmentPrimaryKeyMapping)
	sql := "DELETE FROM \"experiments\".\"assignments\" WHERE \"id\"=$1"

	if boil.IsDebug(ctx) {
		writer := boil.DebugWriterFrom(ctx)
		fmt.Fprintln(writer, sql)
		fmt.Fprintln(writer, args...)
	}
	result, err := exec.ExecContext(ctx, sql, args...)
	if err != nil {
		return 0, errors.Wrap(err, "experiments: unable to delete from assignments")
	}

	rowsAff, err := result.RowsAffected()
	if err != nil {
		return 0, errors.Wrap(err, "experiments: failed to get rows affected by delete for assignments")
	}

	if err := o.doAfterDeleteHooks(ctx, exec); err != nil {
		return 0, err
	}

	return rowsAff, nil
}

// DeleteAll deletes all matching rows.
func (q assignmentQuery) DeleteAll(ctx context.Context, exec boil.ContextExecutor) (int64, error) {
	if q.Query == nil {
		return 0, errors.New("experiments: no assignmentQuery provided for delete all")
	}

	queries.SetDelete(q.Query)

	result, err := q.Query.ExecContext(ctx, exec)
	if err != nil {
		return 0, errors.Wrap(err, "experiments: unable to delete all from assignments")
	}

	rowsAff, err := result.RowsAffected()
	if err != nil {
		return 0, errors.Wrap(err, "experiments: failed to get rows affected by deleteall for assignments")
	}

	return rowsAff, nil
}

// DeleteAll deletes all rows in the slice, using an executor.
func (o AssignmentSlice) DeleteAll(ctx context.Context, exec boil.ContextExecutor) (int64, error) {
	if len(o) == 0 {
		return 0, nil
	}

	if len(assignmentBeforeDeleteHooks) != 0 {
		for _, obj := range o {
			if err := obj.doBeforeDeleteHooks(ctx, exec); err != nil {
				return 0, err
			}
		}
	}

	var args []interface{}
	for _, obj := range o {
		pkeyArgs := queries.ValuesFromMapping(reflect.Indirect(reflect.ValueOf(obj)), assignmentPrimaryKeyMapping)
		args = append(args, pkeyArgs...)
	}

	sql := "DELETE FROM \"experiments\".\"assignments\" WHERE " +
		strmangle.WhereClauseRepeated(string(dialect.LQ), string(dialect.RQ), 1, assignmentPrimaryKeyColumns, len(o))

	if boil.IsDebug(ctx) {
		writer := boil.DebugWriterFrom(ctx)
		fmt.Fprintln(writer, sql)
		fmt.Fprintln(writer, args)
	}
	result, err := exec.ExecContext(ctx, sql, args...)
	if err != nil {
		return 0, errors.Wrap(err, "experiments: unable to delete all from assignment slice")
	}

	rowsAff, err := result.RowsAffected()
	if err != nil {
		return 0, errors.Wrap(err, "experiments: failed to get rows affected by deleteall for assignments")
	}

	if len(assignmentAfterDeleteHooks) != 0 {
		for _, obj := range o {
			if err := obj.doAfterDeleteHooks(ctx, exec); err != nil {
				return 0, err
			}
		}
	}

	return rowsAff, nil
}

// Reload refetches the object from the database
// using the primary keys with an executor.
func (o *Assignment) Reload(ctx context.Context, exec boil.ContextExecutor) error {
	ret, err := FindAssignment(ctx, exec, o.ID)
	if err != nil {
		return err
	}

	*o = *ret
	return nil
}

// ReloadAll refetches every row with matching primary key column values
// and overwrites the original object slice with the newly updated slice.
func (o *AssignmentSlice) ReloadAll(ctx context.Context, exec boil.ContextExecutor) error {
	if o == nil || len(*o) == 0 {
		return nil
	}

	slice := AssignmentSlice{}
	var args []interface{}
	for _, obj := range *o {
		pkeyArgs := queries.ValuesFromMapping(reflect.Indirect(reflect.ValueOf(obj)), assignmentPrimaryKeyMapping)
		args = append(args, pkeyArgs...)
	}

	sql := "SELECT \"experiments\".\"assignments\".* FROM \"experiments\".\"assignments\" WHERE " +
		strmangle.WhereClauseRepeated(string(dialect.LQ), string(dialect.RQ), 1, assignmentPrimaryKeyColumns, len(*o))

	q := queries.Raw(sql, args...)

	err := q.Bind(ctx, exec, &slice)
	if err != nil {
		return errors.Wrap(err, "experiments: unable to reload all in AssignmentSlice")
	}

	*o = slice

	return nil
}

// AssignmentExists checks if the Assignment row exists.
func AssignmentExists(ctx context.Context, exec boil.ContextExecutor, iD string) (bool, error) {
	var exists bool
	sql := "select exists(select 1 from \"experiments\".\"assignments\" where \"id\"=$1 limit 1)"

	if boil.IsDebug(ctx) {
		writer := boil.DebugWriterFrom(ctx)
		fmt.Fprintln(writer, sql)
		fmt.Fprintln(writer, iD)
	}
	row := exec.QueryRowContext(ctx, sql, iD)

	err := row.Scan(&exists)
	if err != nil {
		return false, errors.Wrap(err, "experiments: unable to check if assignments exists")
	}

	return exists, nil
}
