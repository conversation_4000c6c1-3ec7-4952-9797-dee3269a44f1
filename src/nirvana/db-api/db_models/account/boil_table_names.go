// Code generated by SQLBoiler (https://github.com/volatiletech/sqlboiler). DO NOT EDIT.
// This file is meant to be re-generated in place and/or deleted at any time.

package account

var TableNames = struct {
	AccountAliases         string
	AccountIdentifiers     string
	AccountMergeHistory    string
	AccountPossibleMatches string
	AccountPrograms        string
	Accounts               string
	Contacts               string
}{
	AccountAliases:         "account_aliases",
	AccountIdentifiers:     "account_identifiers",
	AccountMergeHistory:    "account_merge_history",
	AccountPossibleMatches: "account_possible_matches",
	AccountPrograms:        "account_programs",
	Accounts:               "accounts",
	Contacts:               "contacts",
}
