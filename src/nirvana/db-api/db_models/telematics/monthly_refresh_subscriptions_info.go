// Code generated by SQLBoiler (https://github.com/volatiletech/sqlboiler). DO NOT EDIT.
// This file is meant to be re-generated in place and/or deleted at any time.

package telematics

import (
	"context"
	"database/sql"
	"fmt"
	"reflect"
	"strconv"
	"strings"
	"sync"
	"time"

	"github.com/friendsofgo/errors"
	"github.com/volatiletech/null/v8"
	"github.com/volatiletech/sqlboiler/v4/boil"
	"github.com/volatiletech/sqlboiler/v4/queries"
	"github.com/volatiletech/sqlboiler/v4/queries/qm"
	"github.com/volatiletech/sqlboiler/v4/queries/qmhelper"
	"github.com/volatiletech/strmangle"
)

// MonthlyRefreshSubscriptionsInfo is an object representing the database table.
type MonthlyRefreshSubscriptionsInfo struct {
	RequestID       string    `boil:"request_id" json:"request_id" toml:"request_id" yaml:"request_id"`
	ProgramType     string    `boil:"program_type" json:"program_type" toml:"program_type" yaml:"program_type"`
	PolicyState     string    `boil:"policy_state" json:"policy_state" toml:"policy_state" yaml:"policy_state"`
	PolicyStartDate null.Time `boil:"policy_start_date" json:"policy_start_date,omitempty" toml:"policy_start_date" yaml:"policy_start_date,omitempty"`

	R *monthlyRefreshSubscriptionsInfoR `boil:"" json:"" toml:"" yaml:""`
	L monthlyRefreshSubscriptionsInfoL  `boil:"-" json:"-" toml:"-" yaml:"-"`
}

var MonthlyRefreshSubscriptionsInfoColumns = struct {
	RequestID       string
	ProgramType     string
	PolicyState     string
	PolicyStartDate string
}{
	RequestID:       "request_id",
	ProgramType:     "program_type",
	PolicyState:     "policy_state",
	PolicyStartDate: "policy_start_date",
}

var MonthlyRefreshSubscriptionsInfoTableColumns = struct {
	RequestID       string
	ProgramType     string
	PolicyState     string
	PolicyStartDate string
}{
	RequestID:       "monthly_refresh_subscriptions_info.request_id",
	ProgramType:     "monthly_refresh_subscriptions_info.program_type",
	PolicyState:     "monthly_refresh_subscriptions_info.policy_state",
	PolicyStartDate: "monthly_refresh_subscriptions_info.policy_start_date",
}

// Generated where

var MonthlyRefreshSubscriptionsInfoWhere = struct {
	RequestID       whereHelperstring
	ProgramType     whereHelperstring
	PolicyState     whereHelperstring
	PolicyStartDate whereHelpernull_Time
}{
	RequestID:       whereHelperstring{field: "\"telematics\".\"monthly_refresh_subscriptions_info\".\"request_id\""},
	ProgramType:     whereHelperstring{field: "\"telematics\".\"monthly_refresh_subscriptions_info\".\"program_type\""},
	PolicyState:     whereHelperstring{field: "\"telematics\".\"monthly_refresh_subscriptions_info\".\"policy_state\""},
	PolicyStartDate: whereHelpernull_Time{field: "\"telematics\".\"monthly_refresh_subscriptions_info\".\"policy_start_date\""},
}

// MonthlyRefreshSubscriptionsInfoRels is where relationship names are stored.
var MonthlyRefreshSubscriptionsInfoRels = struct {
	Request string
}{
	Request: "Request",
}

// monthlyRefreshSubscriptionsInfoR is where relationships are stored.
type monthlyRefreshSubscriptionsInfoR struct {
	Request *SubscriptionRequest `boil:"Request" json:"Request" toml:"Request" yaml:"Request"`
}

// NewStruct creates a new relationship struct
func (*monthlyRefreshSubscriptionsInfoR) NewStruct() *monthlyRefreshSubscriptionsInfoR {
	return &monthlyRefreshSubscriptionsInfoR{}
}

// monthlyRefreshSubscriptionsInfoL is where Load methods for each relationship are stored.
type monthlyRefreshSubscriptionsInfoL struct{}

var (
	monthlyRefreshSubscriptionsInfoAllColumns            = []string{"request_id", "program_type", "policy_state", "policy_start_date"}
	monthlyRefreshSubscriptionsInfoColumnsWithoutDefault = []string{"request_id", "program_type", "policy_state"}
	monthlyRefreshSubscriptionsInfoColumnsWithDefault    = []string{"policy_start_date"}
	monthlyRefreshSubscriptionsInfoPrimaryKeyColumns     = []string{"request_id"}
	monthlyRefreshSubscriptionsInfoGeneratedColumns      = []string{}
)

type (
	// MonthlyRefreshSubscriptionsInfoSlice is an alias for a slice of pointers to MonthlyRefreshSubscriptionsInfo.
	// This should almost always be used instead of []MonthlyRefreshSubscriptionsInfo.
	MonthlyRefreshSubscriptionsInfoSlice []*MonthlyRefreshSubscriptionsInfo
	// MonthlyRefreshSubscriptionsInfoHook is the signature for custom MonthlyRefreshSubscriptionsInfo hook methods
	MonthlyRefreshSubscriptionsInfoHook func(context.Context, boil.ContextExecutor, *MonthlyRefreshSubscriptionsInfo) error

	monthlyRefreshSubscriptionsInfoQuery struct {
		*queries.Query
	}
)

// Cache for insert, update and upsert
var (
	monthlyRefreshSubscriptionsInfoType                 = reflect.TypeOf(&MonthlyRefreshSubscriptionsInfo{})
	monthlyRefreshSubscriptionsInfoMapping              = queries.MakeStructMapping(monthlyRefreshSubscriptionsInfoType)
	monthlyRefreshSubscriptionsInfoPrimaryKeyMapping, _ = queries.BindMapping(monthlyRefreshSubscriptionsInfoType, monthlyRefreshSubscriptionsInfoMapping, monthlyRefreshSubscriptionsInfoPrimaryKeyColumns)
	monthlyRefreshSubscriptionsInfoInsertCacheMut       sync.RWMutex
	monthlyRefreshSubscriptionsInfoInsertCache          = make(map[string]insertCache)
	monthlyRefreshSubscriptionsInfoUpdateCacheMut       sync.RWMutex
	monthlyRefreshSubscriptionsInfoUpdateCache          = make(map[string]updateCache)
	monthlyRefreshSubscriptionsInfoUpsertCacheMut       sync.RWMutex
	monthlyRefreshSubscriptionsInfoUpsertCache          = make(map[string]insertCache)
)

var (
	// Force time package dependency for automated UpdatedAt/CreatedAt.
	_ = time.Second
	// Force qmhelper dependency for where clause generation (which doesn't
	// always happen)
	_ = qmhelper.Where
)

var monthlyRefreshSubscriptionsInfoAfterSelectHooks []MonthlyRefreshSubscriptionsInfoHook

var monthlyRefreshSubscriptionsInfoBeforeInsertHooks []MonthlyRefreshSubscriptionsInfoHook
var monthlyRefreshSubscriptionsInfoAfterInsertHooks []MonthlyRefreshSubscriptionsInfoHook

var monthlyRefreshSubscriptionsInfoBeforeUpdateHooks []MonthlyRefreshSubscriptionsInfoHook
var monthlyRefreshSubscriptionsInfoAfterUpdateHooks []MonthlyRefreshSubscriptionsInfoHook

var monthlyRefreshSubscriptionsInfoBeforeDeleteHooks []MonthlyRefreshSubscriptionsInfoHook
var monthlyRefreshSubscriptionsInfoAfterDeleteHooks []MonthlyRefreshSubscriptionsInfoHook

var monthlyRefreshSubscriptionsInfoBeforeUpsertHooks []MonthlyRefreshSubscriptionsInfoHook
var monthlyRefreshSubscriptionsInfoAfterUpsertHooks []MonthlyRefreshSubscriptionsInfoHook

// doAfterSelectHooks executes all "after Select" hooks.
func (o *MonthlyRefreshSubscriptionsInfo) doAfterSelectHooks(ctx context.Context, exec boil.ContextExecutor) (err error) {
	if boil.HooksAreSkipped(ctx) {
		return nil
	}

	for _, hook := range monthlyRefreshSubscriptionsInfoAfterSelectHooks {
		if err := hook(ctx, exec, o); err != nil {
			return err
		}
	}

	return nil
}

// doBeforeInsertHooks executes all "before insert" hooks.
func (o *MonthlyRefreshSubscriptionsInfo) doBeforeInsertHooks(ctx context.Context, exec boil.ContextExecutor) (err error) {
	if boil.HooksAreSkipped(ctx) {
		return nil
	}

	for _, hook := range monthlyRefreshSubscriptionsInfoBeforeInsertHooks {
		if err := hook(ctx, exec, o); err != nil {
			return err
		}
	}

	return nil
}

// doAfterInsertHooks executes all "after Insert" hooks.
func (o *MonthlyRefreshSubscriptionsInfo) doAfterInsertHooks(ctx context.Context, exec boil.ContextExecutor) (err error) {
	if boil.HooksAreSkipped(ctx) {
		return nil
	}

	for _, hook := range monthlyRefreshSubscriptionsInfoAfterInsertHooks {
		if err := hook(ctx, exec, o); err != nil {
			return err
		}
	}

	return nil
}

// doBeforeUpdateHooks executes all "before Update" hooks.
func (o *MonthlyRefreshSubscriptionsInfo) doBeforeUpdateHooks(ctx context.Context, exec boil.ContextExecutor) (err error) {
	if boil.HooksAreSkipped(ctx) {
		return nil
	}

	for _, hook := range monthlyRefreshSubscriptionsInfoBeforeUpdateHooks {
		if err := hook(ctx, exec, o); err != nil {
			return err
		}
	}

	return nil
}

// doAfterUpdateHooks executes all "after Update" hooks.
func (o *MonthlyRefreshSubscriptionsInfo) doAfterUpdateHooks(ctx context.Context, exec boil.ContextExecutor) (err error) {
	if boil.HooksAreSkipped(ctx) {
		return nil
	}

	for _, hook := range monthlyRefreshSubscriptionsInfoAfterUpdateHooks {
		if err := hook(ctx, exec, o); err != nil {
			return err
		}
	}

	return nil
}

// doBeforeDeleteHooks executes all "before Delete" hooks.
func (o *MonthlyRefreshSubscriptionsInfo) doBeforeDeleteHooks(ctx context.Context, exec boil.ContextExecutor) (err error) {
	if boil.HooksAreSkipped(ctx) {
		return nil
	}

	for _, hook := range monthlyRefreshSubscriptionsInfoBeforeDeleteHooks {
		if err := hook(ctx, exec, o); err != nil {
			return err
		}
	}

	return nil
}

// doAfterDeleteHooks executes all "after Delete" hooks.
func (o *MonthlyRefreshSubscriptionsInfo) doAfterDeleteHooks(ctx context.Context, exec boil.ContextExecutor) (err error) {
	if boil.HooksAreSkipped(ctx) {
		return nil
	}

	for _, hook := range monthlyRefreshSubscriptionsInfoAfterDeleteHooks {
		if err := hook(ctx, exec, o); err != nil {
			return err
		}
	}

	return nil
}

// doBeforeUpsertHooks executes all "before Upsert" hooks.
func (o *MonthlyRefreshSubscriptionsInfo) doBeforeUpsertHooks(ctx context.Context, exec boil.ContextExecutor) (err error) {
	if boil.HooksAreSkipped(ctx) {
		return nil
	}

	for _, hook := range monthlyRefreshSubscriptionsInfoBeforeUpsertHooks {
		if err := hook(ctx, exec, o); err != nil {
			return err
		}
	}

	return nil
}

// doAfterUpsertHooks executes all "after Upsert" hooks.
func (o *MonthlyRefreshSubscriptionsInfo) doAfterUpsertHooks(ctx context.Context, exec boil.ContextExecutor) (err error) {
	if boil.HooksAreSkipped(ctx) {
		return nil
	}

	for _, hook := range monthlyRefreshSubscriptionsInfoAfterUpsertHooks {
		if err := hook(ctx, exec, o); err != nil {
			return err
		}
	}

	return nil
}

// AddMonthlyRefreshSubscriptionsInfoHook registers your hook function for all future operations.
func AddMonthlyRefreshSubscriptionsInfoHook(hookPoint boil.HookPoint, monthlyRefreshSubscriptionsInfoHook MonthlyRefreshSubscriptionsInfoHook) {
	switch hookPoint {
	case boil.AfterSelectHook:
		monthlyRefreshSubscriptionsInfoAfterSelectHooks = append(monthlyRefreshSubscriptionsInfoAfterSelectHooks, monthlyRefreshSubscriptionsInfoHook)
	case boil.BeforeInsertHook:
		monthlyRefreshSubscriptionsInfoBeforeInsertHooks = append(monthlyRefreshSubscriptionsInfoBeforeInsertHooks, monthlyRefreshSubscriptionsInfoHook)
	case boil.AfterInsertHook:
		monthlyRefreshSubscriptionsInfoAfterInsertHooks = append(monthlyRefreshSubscriptionsInfoAfterInsertHooks, monthlyRefreshSubscriptionsInfoHook)
	case boil.BeforeUpdateHook:
		monthlyRefreshSubscriptionsInfoBeforeUpdateHooks = append(monthlyRefreshSubscriptionsInfoBeforeUpdateHooks, monthlyRefreshSubscriptionsInfoHook)
	case boil.AfterUpdateHook:
		monthlyRefreshSubscriptionsInfoAfterUpdateHooks = append(monthlyRefreshSubscriptionsInfoAfterUpdateHooks, monthlyRefreshSubscriptionsInfoHook)
	case boil.BeforeDeleteHook:
		monthlyRefreshSubscriptionsInfoBeforeDeleteHooks = append(monthlyRefreshSubscriptionsInfoBeforeDeleteHooks, monthlyRefreshSubscriptionsInfoHook)
	case boil.AfterDeleteHook:
		monthlyRefreshSubscriptionsInfoAfterDeleteHooks = append(monthlyRefreshSubscriptionsInfoAfterDeleteHooks, monthlyRefreshSubscriptionsInfoHook)
	case boil.BeforeUpsertHook:
		monthlyRefreshSubscriptionsInfoBeforeUpsertHooks = append(monthlyRefreshSubscriptionsInfoBeforeUpsertHooks, monthlyRefreshSubscriptionsInfoHook)
	case boil.AfterUpsertHook:
		monthlyRefreshSubscriptionsInfoAfterUpsertHooks = append(monthlyRefreshSubscriptionsInfoAfterUpsertHooks, monthlyRefreshSubscriptionsInfoHook)
	}
}

// One returns a single monthlyRefreshSubscriptionsInfo record from the query.
func (q monthlyRefreshSubscriptionsInfoQuery) One(ctx context.Context, exec boil.ContextExecutor) (*MonthlyRefreshSubscriptionsInfo, error) {
	o := &MonthlyRefreshSubscriptionsInfo{}

	queries.SetLimit(q.Query, 1)

	err := q.Bind(ctx, exec, o)
	if err != nil {
		if errors.Is(err, sql.ErrNoRows) {
			return nil, sql.ErrNoRows
		}
		return nil, errors.Wrap(err, "telematics: failed to execute a one query for monthly_refresh_subscriptions_info")
	}

	if err := o.doAfterSelectHooks(ctx, exec); err != nil {
		return o, err
	}

	return o, nil
}

// All returns all MonthlyRefreshSubscriptionsInfo records from the query.
func (q monthlyRefreshSubscriptionsInfoQuery) All(ctx context.Context, exec boil.ContextExecutor) (MonthlyRefreshSubscriptionsInfoSlice, error) {
	var o []*MonthlyRefreshSubscriptionsInfo

	err := q.Bind(ctx, exec, &o)
	if err != nil {
		return nil, errors.Wrap(err, "telematics: failed to assign all query results to MonthlyRefreshSubscriptionsInfo slice")
	}

	if len(monthlyRefreshSubscriptionsInfoAfterSelectHooks) != 0 {
		for _, obj := range o {
			if err := obj.doAfterSelectHooks(ctx, exec); err != nil {
				return o, err
			}
		}
	}

	return o, nil
}

// Count returns the count of all MonthlyRefreshSubscriptionsInfo records in the query.
func (q monthlyRefreshSubscriptionsInfoQuery) Count(ctx context.Context, exec boil.ContextExecutor) (int64, error) {
	var count int64

	queries.SetSelect(q.Query, nil)
	queries.SetCount(q.Query)

	err := q.Query.QueryRowContext(ctx, exec).Scan(&count)
	if err != nil {
		return 0, errors.Wrap(err, "telematics: failed to count monthly_refresh_subscriptions_info rows")
	}

	return count, nil
}

// Exists checks if the row exists in the table.
func (q monthlyRefreshSubscriptionsInfoQuery) Exists(ctx context.Context, exec boil.ContextExecutor) (bool, error) {
	var count int64

	queries.SetSelect(q.Query, nil)
	queries.SetCount(q.Query)
	queries.SetLimit(q.Query, 1)

	err := q.Query.QueryRowContext(ctx, exec).Scan(&count)
	if err != nil {
		return false, errors.Wrap(err, "telematics: failed to check if monthly_refresh_subscriptions_info exists")
	}

	return count > 0, nil
}

// Request pointed to by the foreign key.
func (o *MonthlyRefreshSubscriptionsInfo) Request(mods ...qm.QueryMod) subscriptionRequestQuery {
	queryMods := []qm.QueryMod{
		qm.Where("\"request_id\" = ?", o.RequestID),
	}

	queryMods = append(queryMods, mods...)

	return SubscriptionRequests(queryMods...)
}

// LoadRequest allows an eager lookup of values, cached into the
// loaded structs of the objects. This is for an N-1 relationship.
func (monthlyRefreshSubscriptionsInfoL) LoadRequest(ctx context.Context, e boil.ContextExecutor, singular bool, maybeMonthlyRefreshSubscriptionsInfo interface{}, mods queries.Applicator) error {
	var slice []*MonthlyRefreshSubscriptionsInfo
	var object *MonthlyRefreshSubscriptionsInfo

	if singular {
		object = maybeMonthlyRefreshSubscriptionsInfo.(*MonthlyRefreshSubscriptionsInfo)
	} else {
		slice = *maybeMonthlyRefreshSubscriptionsInfo.(*[]*MonthlyRefreshSubscriptionsInfo)
	}

	args := make([]interface{}, 0, 1)
	if singular {
		if object.R == nil {
			object.R = &monthlyRefreshSubscriptionsInfoR{}
		}
		args = append(args, object.RequestID)

	} else {
	Outer:
		for _, obj := range slice {
			if obj.R == nil {
				obj.R = &monthlyRefreshSubscriptionsInfoR{}
			}

			for _, a := range args {
				if a == obj.RequestID {
					continue Outer
				}
			}

			args = append(args, obj.RequestID)

		}
	}

	if len(args) == 0 {
		return nil
	}

	query := NewQuery(
		qm.From(`telematics.subscription_requests`),
		qm.WhereIn(`telematics.subscription_requests.request_id in ?`, args...),
	)
	if mods != nil {
		mods.Apply(query)
	}

	results, err := query.QueryContext(ctx, e)
	if err != nil {
		return errors.Wrap(err, "failed to eager load SubscriptionRequest")
	}

	var resultSlice []*SubscriptionRequest
	if err = queries.Bind(results, &resultSlice); err != nil {
		return errors.Wrap(err, "failed to bind eager loaded slice SubscriptionRequest")
	}

	if err = results.Close(); err != nil {
		return errors.Wrap(err, "failed to close results of eager load for subscription_requests")
	}
	if err = results.Err(); err != nil {
		return errors.Wrap(err, "error occurred during iteration of eager loaded relations for subscription_requests")
	}

	if len(monthlyRefreshSubscriptionsInfoAfterSelectHooks) != 0 {
		for _, obj := range resultSlice {
			if err := obj.doAfterSelectHooks(ctx, e); err != nil {
				return err
			}
		}
	}

	if len(resultSlice) == 0 {
		return nil
	}

	if singular {
		foreign := resultSlice[0]
		object.R.Request = foreign
		if foreign.R == nil {
			foreign.R = &subscriptionRequestR{}
		}
		foreign.R.RequestMonthlyRefreshSubscriptionsInfo = object
		return nil
	}

	for _, local := range slice {
		for _, foreign := range resultSlice {
			if local.RequestID == foreign.RequestID {
				local.R.Request = foreign
				if foreign.R == nil {
					foreign.R = &subscriptionRequestR{}
				}
				foreign.R.RequestMonthlyRefreshSubscriptionsInfo = local
				break
			}
		}
	}

	return nil
}

// SetRequest of the monthlyRefreshSubscriptionsInfo to the related item.
// Sets o.R.Request to related.
// Adds o to related.R.RequestMonthlyRefreshSubscriptionsInfo.
func (o *MonthlyRefreshSubscriptionsInfo) SetRequest(ctx context.Context, exec boil.ContextExecutor, insert bool, related *SubscriptionRequest) error {
	var err error
	if insert {
		if err = related.Insert(ctx, exec, boil.Infer()); err != nil {
			return errors.Wrap(err, "failed to insert into foreign table")
		}
	}

	updateQuery := fmt.Sprintf(
		"UPDATE \"telematics\".\"monthly_refresh_subscriptions_info\" SET %s WHERE %s",
		strmangle.SetParamNames("\"", "\"", 1, []string{"request_id"}),
		strmangle.WhereClause("\"", "\"", 2, monthlyRefreshSubscriptionsInfoPrimaryKeyColumns),
	)
	values := []interface{}{related.RequestID, o.RequestID}

	if boil.IsDebug(ctx) {
		writer := boil.DebugWriterFrom(ctx)
		fmt.Fprintln(writer, updateQuery)
		fmt.Fprintln(writer, values)
	}
	if _, err = exec.ExecContext(ctx, updateQuery, values...); err != nil {
		return errors.Wrap(err, "failed to update local table")
	}

	o.RequestID = related.RequestID
	if o.R == nil {
		o.R = &monthlyRefreshSubscriptionsInfoR{
			Request: related,
		}
	} else {
		o.R.Request = related
	}

	if related.R == nil {
		related.R = &subscriptionRequestR{
			RequestMonthlyRefreshSubscriptionsInfo: o,
		}
	} else {
		related.R.RequestMonthlyRefreshSubscriptionsInfo = o
	}

	return nil
}

// MonthlyRefreshSubscriptionsInfos retrieves all the records using an executor.
func MonthlyRefreshSubscriptionsInfos(mods ...qm.QueryMod) monthlyRefreshSubscriptionsInfoQuery {
	mods = append(mods, qm.From("\"telematics\".\"monthly_refresh_subscriptions_info\""))
	q := NewQuery(mods...)
	if len(queries.GetSelect(q)) == 0 {
		queries.SetSelect(q, []string{"\"telematics\".\"monthly_refresh_subscriptions_info\".*"})
	}

	return monthlyRefreshSubscriptionsInfoQuery{q}
}

// FindMonthlyRefreshSubscriptionsInfo retrieves a single record by ID with an executor.
// If selectCols is empty Find will return all columns.
func FindMonthlyRefreshSubscriptionsInfo(ctx context.Context, exec boil.ContextExecutor, requestID string, selectCols ...string) (*MonthlyRefreshSubscriptionsInfo, error) {
	monthlyRefreshSubscriptionsInfoObj := &MonthlyRefreshSubscriptionsInfo{}

	sel := "*"
	if len(selectCols) > 0 {
		sel = strings.Join(strmangle.IdentQuoteSlice(dialect.LQ, dialect.RQ, selectCols), ",")
	}
	query := fmt.Sprintf(
		"select %s from \"telematics\".\"monthly_refresh_subscriptions_info\" where \"request_id\"=$1", sel,
	)

	q := queries.Raw(query, requestID)

	err := q.Bind(ctx, exec, monthlyRefreshSubscriptionsInfoObj)
	if err != nil {
		if errors.Is(err, sql.ErrNoRows) {
			return nil, sql.ErrNoRows
		}
		return nil, errors.Wrap(err, "telematics: unable to select from monthly_refresh_subscriptions_info")
	}

	if err = monthlyRefreshSubscriptionsInfoObj.doAfterSelectHooks(ctx, exec); err != nil {
		return monthlyRefreshSubscriptionsInfoObj, err
	}

	return monthlyRefreshSubscriptionsInfoObj, nil
}

// Insert a single record using an executor.
// See boil.Columns.InsertColumnSet documentation to understand column list inference for inserts.
func (o *MonthlyRefreshSubscriptionsInfo) Insert(ctx context.Context, exec boil.ContextExecutor, columns boil.Columns) error {
	if o == nil {
		return errors.New("telematics: no monthly_refresh_subscriptions_info provided for insertion")
	}

	var err error

	if err := o.doBeforeInsertHooks(ctx, exec); err != nil {
		return err
	}

	nzDefaults := queries.NonZeroDefaultSet(monthlyRefreshSubscriptionsInfoColumnsWithDefault, o)

	key := makeCacheKey(columns, nzDefaults)
	monthlyRefreshSubscriptionsInfoInsertCacheMut.RLock()
	cache, cached := monthlyRefreshSubscriptionsInfoInsertCache[key]
	monthlyRefreshSubscriptionsInfoInsertCacheMut.RUnlock()

	if !cached {
		wl, returnColumns := columns.InsertColumnSet(
			monthlyRefreshSubscriptionsInfoAllColumns,
			monthlyRefreshSubscriptionsInfoColumnsWithDefault,
			monthlyRefreshSubscriptionsInfoColumnsWithoutDefault,
			nzDefaults,
		)

		cache.valueMapping, err = queries.BindMapping(monthlyRefreshSubscriptionsInfoType, monthlyRefreshSubscriptionsInfoMapping, wl)
		if err != nil {
			return err
		}
		cache.retMapping, err = queries.BindMapping(monthlyRefreshSubscriptionsInfoType, monthlyRefreshSubscriptionsInfoMapping, returnColumns)
		if err != nil {
			return err
		}
		if len(wl) != 0 {
			cache.query = fmt.Sprintf("INSERT INTO \"telematics\".\"monthly_refresh_subscriptions_info\" (\"%s\") %%sVALUES (%s)%%s", strings.Join(wl, "\",\""), strmangle.Placeholders(dialect.UseIndexPlaceholders, len(wl), 1, 1))
		} else {
			cache.query = "INSERT INTO \"telematics\".\"monthly_refresh_subscriptions_info\" %sDEFAULT VALUES%s"
		}

		var queryOutput, queryReturning string

		if len(cache.retMapping) != 0 {
			queryReturning = fmt.Sprintf(" RETURNING \"%s\"", strings.Join(returnColumns, "\",\""))
		}

		cache.query = fmt.Sprintf(cache.query, queryOutput, queryReturning)
	}

	value := reflect.Indirect(reflect.ValueOf(o))
	vals := queries.ValuesFromMapping(value, cache.valueMapping)

	if boil.IsDebug(ctx) {
		writer := boil.DebugWriterFrom(ctx)
		fmt.Fprintln(writer, cache.query)
		fmt.Fprintln(writer, vals)
	}

	if len(cache.retMapping) != 0 {
		err = exec.QueryRowContext(ctx, cache.query, vals...).Scan(queries.PtrsFromMapping(value, cache.retMapping)...)
	} else {
		_, err = exec.ExecContext(ctx, cache.query, vals...)
	}

	if err != nil {
		return errors.Wrap(err, "telematics: unable to insert into monthly_refresh_subscriptions_info")
	}

	if !cached {
		monthlyRefreshSubscriptionsInfoInsertCacheMut.Lock()
		monthlyRefreshSubscriptionsInfoInsertCache[key] = cache
		monthlyRefreshSubscriptionsInfoInsertCacheMut.Unlock()
	}

	return o.doAfterInsertHooks(ctx, exec)
}

// Update uses an executor to update the MonthlyRefreshSubscriptionsInfo.
// See boil.Columns.UpdateColumnSet documentation to understand column list inference for updates.
// Update does not automatically update the record in case of default values. Use .Reload() to refresh the records.
func (o *MonthlyRefreshSubscriptionsInfo) Update(ctx context.Context, exec boil.ContextExecutor, columns boil.Columns) (int64, error) {
	var err error
	if err = o.doBeforeUpdateHooks(ctx, exec); err != nil {
		return 0, err
	}
	key := makeCacheKey(columns, nil)
	monthlyRefreshSubscriptionsInfoUpdateCacheMut.RLock()
	cache, cached := monthlyRefreshSubscriptionsInfoUpdateCache[key]
	monthlyRefreshSubscriptionsInfoUpdateCacheMut.RUnlock()

	if !cached {
		wl := columns.UpdateColumnSet(
			monthlyRefreshSubscriptionsInfoAllColumns,
			monthlyRefreshSubscriptionsInfoPrimaryKeyColumns,
		)

		if !columns.IsWhitelist() {
			wl = strmangle.SetComplement(wl, []string{"created_at"})
		}
		if len(wl) == 0 {
			return 0, errors.New("telematics: unable to update monthly_refresh_subscriptions_info, could not build whitelist")
		}

		cache.query = fmt.Sprintf("UPDATE \"telematics\".\"monthly_refresh_subscriptions_info\" SET %s WHERE %s",
			strmangle.SetParamNames("\"", "\"", 1, wl),
			strmangle.WhereClause("\"", "\"", len(wl)+1, monthlyRefreshSubscriptionsInfoPrimaryKeyColumns),
		)
		cache.valueMapping, err = queries.BindMapping(monthlyRefreshSubscriptionsInfoType, monthlyRefreshSubscriptionsInfoMapping, append(wl, monthlyRefreshSubscriptionsInfoPrimaryKeyColumns...))
		if err != nil {
			return 0, err
		}
	}

	values := queries.ValuesFromMapping(reflect.Indirect(reflect.ValueOf(o)), cache.valueMapping)

	if boil.IsDebug(ctx) {
		writer := boil.DebugWriterFrom(ctx)
		fmt.Fprintln(writer, cache.query)
		fmt.Fprintln(writer, values)
	}
	var result sql.Result
	result, err = exec.ExecContext(ctx, cache.query, values...)
	if err != nil {
		return 0, errors.Wrap(err, "telematics: unable to update monthly_refresh_subscriptions_info row")
	}

	rowsAff, err := result.RowsAffected()
	if err != nil {
		return 0, errors.Wrap(err, "telematics: failed to get rows affected by update for monthly_refresh_subscriptions_info")
	}

	if !cached {
		monthlyRefreshSubscriptionsInfoUpdateCacheMut.Lock()
		monthlyRefreshSubscriptionsInfoUpdateCache[key] = cache
		monthlyRefreshSubscriptionsInfoUpdateCacheMut.Unlock()
	}

	return rowsAff, o.doAfterUpdateHooks(ctx, exec)
}

// UpdateAll updates all rows with the specified column values.
func (q monthlyRefreshSubscriptionsInfoQuery) UpdateAll(ctx context.Context, exec boil.ContextExecutor, cols M) (int64, error) {
	queries.SetUpdate(q.Query, cols)

	result, err := q.Query.ExecContext(ctx, exec)
	if err != nil {
		return 0, errors.Wrap(err, "telematics: unable to update all for monthly_refresh_subscriptions_info")
	}

	rowsAff, err := result.RowsAffected()
	if err != nil {
		return 0, errors.Wrap(err, "telematics: unable to retrieve rows affected for monthly_refresh_subscriptions_info")
	}

	return rowsAff, nil
}

// UpdateAll updates all rows with the specified column values, using an executor.
func (o MonthlyRefreshSubscriptionsInfoSlice) UpdateAll(ctx context.Context, exec boil.ContextExecutor, cols M) (int64, error) {
	ln := int64(len(o))
	if ln == 0 {
		return 0, nil
	}

	if len(cols) == 0 {
		return 0, errors.New("telematics: update all requires at least one column argument")
	}

	colNames := make([]string, len(cols))
	args := make([]interface{}, len(cols))

	i := 0
	for name, value := range cols {
		colNames[i] = name
		args[i] = value
		i++
	}

	// Append all of the primary key values for each column
	for _, obj := range o {
		pkeyArgs := queries.ValuesFromMapping(reflect.Indirect(reflect.ValueOf(obj)), monthlyRefreshSubscriptionsInfoPrimaryKeyMapping)
		args = append(args, pkeyArgs...)
	}

	sql := fmt.Sprintf("UPDATE \"telematics\".\"monthly_refresh_subscriptions_info\" SET %s WHERE %s",
		strmangle.SetParamNames("\"", "\"", 1, colNames),
		strmangle.WhereClauseRepeated(string(dialect.LQ), string(dialect.RQ), len(colNames)+1, monthlyRefreshSubscriptionsInfoPrimaryKeyColumns, len(o)))

	if boil.IsDebug(ctx) {
		writer := boil.DebugWriterFrom(ctx)
		fmt.Fprintln(writer, sql)
		fmt.Fprintln(writer, args...)
	}
	result, err := exec.ExecContext(ctx, sql, args...)
	if err != nil {
		return 0, errors.Wrap(err, "telematics: unable to update all in monthlyRefreshSubscriptionsInfo slice")
	}

	rowsAff, err := result.RowsAffected()
	if err != nil {
		return 0, errors.Wrap(err, "telematics: unable to retrieve rows affected all in update all monthlyRefreshSubscriptionsInfo")
	}
	return rowsAff, nil
}

// Upsert attempts an insert using an executor, and does an update or ignore on conflict.
// See boil.Columns documentation for how to properly use updateColumns and insertColumns.
func (o *MonthlyRefreshSubscriptionsInfo) Upsert(ctx context.Context, exec boil.ContextExecutor, updateOnConflict bool, conflictColumns []string, updateColumns, insertColumns boil.Columns) error {
	if o == nil {
		return errors.New("telematics: no monthly_refresh_subscriptions_info provided for upsert")
	}

	if err := o.doBeforeUpsertHooks(ctx, exec); err != nil {
		return err
	}

	nzDefaults := queries.NonZeroDefaultSet(monthlyRefreshSubscriptionsInfoColumnsWithDefault, o)

	// Build cache key in-line uglily - mysql vs psql problems
	buf := strmangle.GetBuffer()
	if updateOnConflict {
		buf.WriteByte('t')
	} else {
		buf.WriteByte('f')
	}
	buf.WriteByte('.')
	for _, c := range conflictColumns {
		buf.WriteString(c)
	}
	buf.WriteByte('.')
	buf.WriteString(strconv.Itoa(updateColumns.Kind))
	for _, c := range updateColumns.Cols {
		buf.WriteString(c)
	}
	buf.WriteByte('.')
	buf.WriteString(strconv.Itoa(insertColumns.Kind))
	for _, c := range insertColumns.Cols {
		buf.WriteString(c)
	}
	buf.WriteByte('.')
	for _, c := range nzDefaults {
		buf.WriteString(c)
	}
	key := buf.String()
	strmangle.PutBuffer(buf)

	monthlyRefreshSubscriptionsInfoUpsertCacheMut.RLock()
	cache, cached := monthlyRefreshSubscriptionsInfoUpsertCache[key]
	monthlyRefreshSubscriptionsInfoUpsertCacheMut.RUnlock()

	var err error

	if !cached {
		insert, ret := insertColumns.InsertColumnSet(
			monthlyRefreshSubscriptionsInfoAllColumns,
			monthlyRefreshSubscriptionsInfoColumnsWithDefault,
			monthlyRefreshSubscriptionsInfoColumnsWithoutDefault,
			nzDefaults,
		)

		update := updateColumns.UpdateColumnSet(
			monthlyRefreshSubscriptionsInfoAllColumns,
			monthlyRefreshSubscriptionsInfoPrimaryKeyColumns,
		)

		if updateOnConflict && len(update) == 0 {
			return errors.New("telematics: unable to upsert monthly_refresh_subscriptions_info, could not build update column list")
		}

		conflict := conflictColumns
		if len(conflict) == 0 {
			conflict = make([]string, len(monthlyRefreshSubscriptionsInfoPrimaryKeyColumns))
			copy(conflict, monthlyRefreshSubscriptionsInfoPrimaryKeyColumns)
		}
		cache.query = buildUpsertQueryPostgres(dialect, "\"telematics\".\"monthly_refresh_subscriptions_info\"", updateOnConflict, ret, update, conflict, insert)

		cache.valueMapping, err = queries.BindMapping(monthlyRefreshSubscriptionsInfoType, monthlyRefreshSubscriptionsInfoMapping, insert)
		if err != nil {
			return err
		}
		if len(ret) != 0 {
			cache.retMapping, err = queries.BindMapping(monthlyRefreshSubscriptionsInfoType, monthlyRefreshSubscriptionsInfoMapping, ret)
			if err != nil {
				return err
			}
		}
	}

	value := reflect.Indirect(reflect.ValueOf(o))
	vals := queries.ValuesFromMapping(value, cache.valueMapping)
	var returns []interface{}
	if len(cache.retMapping) != 0 {
		returns = queries.PtrsFromMapping(value, cache.retMapping)
	}

	if boil.IsDebug(ctx) {
		writer := boil.DebugWriterFrom(ctx)
		fmt.Fprintln(writer, cache.query)
		fmt.Fprintln(writer, vals)
	}
	if len(cache.retMapping) != 0 {
		err = exec.QueryRowContext(ctx, cache.query, vals...).Scan(returns...)
		if errors.Is(err, sql.ErrNoRows) {
			err = nil // Postgres doesn't return anything when there's no update
		}
	} else {
		_, err = exec.ExecContext(ctx, cache.query, vals...)
	}
	if err != nil {
		return errors.Wrap(err, "telematics: unable to upsert monthly_refresh_subscriptions_info")
	}

	if !cached {
		monthlyRefreshSubscriptionsInfoUpsertCacheMut.Lock()
		monthlyRefreshSubscriptionsInfoUpsertCache[key] = cache
		monthlyRefreshSubscriptionsInfoUpsertCacheMut.Unlock()
	}

	return o.doAfterUpsertHooks(ctx, exec)
}

// Delete deletes a single MonthlyRefreshSubscriptionsInfo record with an executor.
// Delete will match against the primary key column to find the record to delete.
func (o *MonthlyRefreshSubscriptionsInfo) Delete(ctx context.Context, exec boil.ContextExecutor) (int64, error) {
	if o == nil {
		return 0, errors.New("telematics: no MonthlyRefreshSubscriptionsInfo provided for delete")
	}

	if err := o.doBeforeDeleteHooks(ctx, exec); err != nil {
		return 0, err
	}

	args := queries.ValuesFromMapping(reflect.Indirect(reflect.ValueOf(o)), monthlyRefreshSubscriptionsInfoPrimaryKeyMapping)
	sql := "DELETE FROM \"telematics\".\"monthly_refresh_subscriptions_info\" WHERE \"request_id\"=$1"

	if boil.IsDebug(ctx) {
		writer := boil.DebugWriterFrom(ctx)
		fmt.Fprintln(writer, sql)
		fmt.Fprintln(writer, args...)
	}
	result, err := exec.ExecContext(ctx, sql, args...)
	if err != nil {
		return 0, errors.Wrap(err, "telematics: unable to delete from monthly_refresh_subscriptions_info")
	}

	rowsAff, err := result.RowsAffected()
	if err != nil {
		return 0, errors.Wrap(err, "telematics: failed to get rows affected by delete for monthly_refresh_subscriptions_info")
	}

	if err := o.doAfterDeleteHooks(ctx, exec); err != nil {
		return 0, err
	}

	return rowsAff, nil
}

// DeleteAll deletes all matching rows.
func (q monthlyRefreshSubscriptionsInfoQuery) DeleteAll(ctx context.Context, exec boil.ContextExecutor) (int64, error) {
	if q.Query == nil {
		return 0, errors.New("telematics: no monthlyRefreshSubscriptionsInfoQuery provided for delete all")
	}

	queries.SetDelete(q.Query)

	result, err := q.Query.ExecContext(ctx, exec)
	if err != nil {
		return 0, errors.Wrap(err, "telematics: unable to delete all from monthly_refresh_subscriptions_info")
	}

	rowsAff, err := result.RowsAffected()
	if err != nil {
		return 0, errors.Wrap(err, "telematics: failed to get rows affected by deleteall for monthly_refresh_subscriptions_info")
	}

	return rowsAff, nil
}

// DeleteAll deletes all rows in the slice, using an executor.
func (o MonthlyRefreshSubscriptionsInfoSlice) DeleteAll(ctx context.Context, exec boil.ContextExecutor) (int64, error) {
	if len(o) == 0 {
		return 0, nil
	}

	if len(monthlyRefreshSubscriptionsInfoBeforeDeleteHooks) != 0 {
		for _, obj := range o {
			if err := obj.doBeforeDeleteHooks(ctx, exec); err != nil {
				return 0, err
			}
		}
	}

	var args []interface{}
	for _, obj := range o {
		pkeyArgs := queries.ValuesFromMapping(reflect.Indirect(reflect.ValueOf(obj)), monthlyRefreshSubscriptionsInfoPrimaryKeyMapping)
		args = append(args, pkeyArgs...)
	}

	sql := "DELETE FROM \"telematics\".\"monthly_refresh_subscriptions_info\" WHERE " +
		strmangle.WhereClauseRepeated(string(dialect.LQ), string(dialect.RQ), 1, monthlyRefreshSubscriptionsInfoPrimaryKeyColumns, len(o))

	if boil.IsDebug(ctx) {
		writer := boil.DebugWriterFrom(ctx)
		fmt.Fprintln(writer, sql)
		fmt.Fprintln(writer, args)
	}
	result, err := exec.ExecContext(ctx, sql, args...)
	if err != nil {
		return 0, errors.Wrap(err, "telematics: unable to delete all from monthlyRefreshSubscriptionsInfo slice")
	}

	rowsAff, err := result.RowsAffected()
	if err != nil {
		return 0, errors.Wrap(err, "telematics: failed to get rows affected by deleteall for monthly_refresh_subscriptions_info")
	}

	if len(monthlyRefreshSubscriptionsInfoAfterDeleteHooks) != 0 {
		for _, obj := range o {
			if err := obj.doAfterDeleteHooks(ctx, exec); err != nil {
				return 0, err
			}
		}
	}

	return rowsAff, nil
}

// Reload refetches the object from the database
// using the primary keys with an executor.
func (o *MonthlyRefreshSubscriptionsInfo) Reload(ctx context.Context, exec boil.ContextExecutor) error {
	ret, err := FindMonthlyRefreshSubscriptionsInfo(ctx, exec, o.RequestID)
	if err != nil {
		return err
	}

	*o = *ret
	return nil
}

// ReloadAll refetches every row with matching primary key column values
// and overwrites the original object slice with the newly updated slice.
func (o *MonthlyRefreshSubscriptionsInfoSlice) ReloadAll(ctx context.Context, exec boil.ContextExecutor) error {
	if o == nil || len(*o) == 0 {
		return nil
	}

	slice := MonthlyRefreshSubscriptionsInfoSlice{}
	var args []interface{}
	for _, obj := range *o {
		pkeyArgs := queries.ValuesFromMapping(reflect.Indirect(reflect.ValueOf(obj)), monthlyRefreshSubscriptionsInfoPrimaryKeyMapping)
		args = append(args, pkeyArgs...)
	}

	sql := "SELECT \"telematics\".\"monthly_refresh_subscriptions_info\".* FROM \"telematics\".\"monthly_refresh_subscriptions_info\" WHERE " +
		strmangle.WhereClauseRepeated(string(dialect.LQ), string(dialect.RQ), 1, monthlyRefreshSubscriptionsInfoPrimaryKeyColumns, len(*o))

	q := queries.Raw(sql, args...)

	err := q.Bind(ctx, exec, &slice)
	if err != nil {
		return errors.Wrap(err, "telematics: unable to reload all in MonthlyRefreshSubscriptionsInfoSlice")
	}

	*o = slice

	return nil
}

// MonthlyRefreshSubscriptionsInfoExists checks if the MonthlyRefreshSubscriptionsInfo row exists.
func MonthlyRefreshSubscriptionsInfoExists(ctx context.Context, exec boil.ContextExecutor, requestID string) (bool, error) {
	var exists bool
	sql := "select exists(select 1 from \"telematics\".\"monthly_refresh_subscriptions_info\" where \"request_id\"=$1 limit 1)"

	if boil.IsDebug(ctx) {
		writer := boil.DebugWriterFrom(ctx)
		fmt.Fprintln(writer, sql)
		fmt.Fprintln(writer, requestID)
	}
	row := exec.QueryRowContext(ctx, sql, requestID)

	err := row.Scan(&exists)
	if err != nil {
		return false, errors.Wrap(err, "telematics: unable to check if monthly_refresh_subscriptions_info exists")
	}

	return exists, nil
}
