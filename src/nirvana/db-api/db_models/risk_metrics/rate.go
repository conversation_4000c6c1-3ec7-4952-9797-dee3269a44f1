// Code generated by SQLBoiler (https://github.com/volatiletech/sqlboiler). DO NOT EDIT.
// This file is meant to be re-generated in place and/or deleted at any time.

package risk_metrics

import (
	"context"
	"database/sql"
	"fmt"
	"reflect"
	"strconv"
	"strings"
	"sync"
	"time"

	"github.com/friendsofgo/errors"
	"github.com/volatiletech/sqlboiler/v4/boil"
	"github.com/volatiletech/sqlboiler/v4/queries"
	"github.com/volatiletech/sqlboiler/v4/queries/qm"
	"github.com/volatiletech/sqlboiler/v4/queries/qmhelper"
	"github.com/volatiletech/sqlboiler/v4/types"
	"github.com/volatiletech/strmangle"
)

// Rate is an object representing the database table.
type Rate struct {
	ID           string        `boil:"id" json:"id" toml:"id" yaml:"id"`
	SubmissionID string        `boil:"submission_id" json:"submission_id" toml:"submission_id" yaml:"submission_id"`
	Type         string        `boil:"type" json:"type" toml:"type" yaml:"type"`
	Stage        string        `boil:"stage" json:"stage" toml:"stage" yaml:"stage"`
	Value        types.Decimal `boil:"value" json:"value" toml:"value" yaml:"value"`
	CreatedAt    time.Time     `boil:"created_at" json:"created_at" toml:"created_at" yaml:"created_at"`

	R *rateR `boil:"" json:"" toml:"" yaml:""`
	L rateL  `boil:"-" json:"-" toml:"-" yaml:"-"`
}

var RateColumns = struct {
	ID           string
	SubmissionID string
	Type         string
	Stage        string
	Value        string
	CreatedAt    string
}{
	ID:           "id",
	SubmissionID: "submission_id",
	Type:         "type",
	Stage:        "stage",
	Value:        "value",
	CreatedAt:    "created_at",
}

var RateTableColumns = struct {
	ID           string
	SubmissionID string
	Type         string
	Stage        string
	Value        string
	CreatedAt    string
}{
	ID:           "rate.id",
	SubmissionID: "rate.submission_id",
	Type:         "rate.type",
	Stage:        "rate.stage",
	Value:        "rate.value",
	CreatedAt:    "rate.created_at",
}

// Generated where

var RateWhere = struct {
	ID           whereHelperstring
	SubmissionID whereHelperstring
	Type         whereHelperstring
	Stage        whereHelperstring
	Value        whereHelpertypes_Decimal
	CreatedAt    whereHelpertime_Time
}{
	ID:           whereHelperstring{field: "\"risk_metrics\".\"rate\".\"id\""},
	SubmissionID: whereHelperstring{field: "\"risk_metrics\".\"rate\".\"submission_id\""},
	Type:         whereHelperstring{field: "\"risk_metrics\".\"rate\".\"type\""},
	Stage:        whereHelperstring{field: "\"risk_metrics\".\"rate\".\"stage\""},
	Value:        whereHelpertypes_Decimal{field: "\"risk_metrics\".\"rate\".\"value\""},
	CreatedAt:    whereHelpertime_Time{field: "\"risk_metrics\".\"rate\".\"created_at\""},
}

// RateRels is where relationship names are stored.
var RateRels = struct {
}{}

// rateR is where relationships are stored.
type rateR struct {
}

// NewStruct creates a new relationship struct
func (*rateR) NewStruct() *rateR {
	return &rateR{}
}

// rateL is where Load methods for each relationship are stored.
type rateL struct{}

var (
	rateAllColumns            = []string{"id", "submission_id", "type", "stage", "value", "created_at"}
	rateColumnsWithoutDefault = []string{"id", "submission_id", "type", "stage", "value", "created_at"}
	rateColumnsWithDefault    = []string{}
	ratePrimaryKeyColumns     = []string{"id"}
	rateGeneratedColumns      = []string{}
)

type (
	// RateSlice is an alias for a slice of pointers to Rate.
	// This should almost always be used instead of []Rate.
	RateSlice []*Rate
	// RateHook is the signature for custom Rate hook methods
	RateHook func(context.Context, boil.ContextExecutor, *Rate) error

	rateQuery struct {
		*queries.Query
	}
)

// Cache for insert, update and upsert
var (
	rateType                 = reflect.TypeOf(&Rate{})
	rateMapping              = queries.MakeStructMapping(rateType)
	ratePrimaryKeyMapping, _ = queries.BindMapping(rateType, rateMapping, ratePrimaryKeyColumns)
	rateInsertCacheMut       sync.RWMutex
	rateInsertCache          = make(map[string]insertCache)
	rateUpdateCacheMut       sync.RWMutex
	rateUpdateCache          = make(map[string]updateCache)
	rateUpsertCacheMut       sync.RWMutex
	rateUpsertCache          = make(map[string]insertCache)
)

var (
	// Force time package dependency for automated UpdatedAt/CreatedAt.
	_ = time.Second
	// Force qmhelper dependency for where clause generation (which doesn't
	// always happen)
	_ = qmhelper.Where
)

var rateAfterSelectHooks []RateHook

var rateBeforeInsertHooks []RateHook
var rateAfterInsertHooks []RateHook

var rateBeforeUpdateHooks []RateHook
var rateAfterUpdateHooks []RateHook

var rateBeforeDeleteHooks []RateHook
var rateAfterDeleteHooks []RateHook

var rateBeforeUpsertHooks []RateHook
var rateAfterUpsertHooks []RateHook

// doAfterSelectHooks executes all "after Select" hooks.
func (o *Rate) doAfterSelectHooks(ctx context.Context, exec boil.ContextExecutor) (err error) {
	if boil.HooksAreSkipped(ctx) {
		return nil
	}

	for _, hook := range rateAfterSelectHooks {
		if err := hook(ctx, exec, o); err != nil {
			return err
		}
	}

	return nil
}

// doBeforeInsertHooks executes all "before insert" hooks.
func (o *Rate) doBeforeInsertHooks(ctx context.Context, exec boil.ContextExecutor) (err error) {
	if boil.HooksAreSkipped(ctx) {
		return nil
	}

	for _, hook := range rateBeforeInsertHooks {
		if err := hook(ctx, exec, o); err != nil {
			return err
		}
	}

	return nil
}

// doAfterInsertHooks executes all "after Insert" hooks.
func (o *Rate) doAfterInsertHooks(ctx context.Context, exec boil.ContextExecutor) (err error) {
	if boil.HooksAreSkipped(ctx) {
		return nil
	}

	for _, hook := range rateAfterInsertHooks {
		if err := hook(ctx, exec, o); err != nil {
			return err
		}
	}

	return nil
}

// doBeforeUpdateHooks executes all "before Update" hooks.
func (o *Rate) doBeforeUpdateHooks(ctx context.Context, exec boil.ContextExecutor) (err error) {
	if boil.HooksAreSkipped(ctx) {
		return nil
	}

	for _, hook := range rateBeforeUpdateHooks {
		if err := hook(ctx, exec, o); err != nil {
			return err
		}
	}

	return nil
}

// doAfterUpdateHooks executes all "after Update" hooks.
func (o *Rate) doAfterUpdateHooks(ctx context.Context, exec boil.ContextExecutor) (err error) {
	if boil.HooksAreSkipped(ctx) {
		return nil
	}

	for _, hook := range rateAfterUpdateHooks {
		if err := hook(ctx, exec, o); err != nil {
			return err
		}
	}

	return nil
}

// doBeforeDeleteHooks executes all "before Delete" hooks.
func (o *Rate) doBeforeDeleteHooks(ctx context.Context, exec boil.ContextExecutor) (err error) {
	if boil.HooksAreSkipped(ctx) {
		return nil
	}

	for _, hook := range rateBeforeDeleteHooks {
		if err := hook(ctx, exec, o); err != nil {
			return err
		}
	}

	return nil
}

// doAfterDeleteHooks executes all "after Delete" hooks.
func (o *Rate) doAfterDeleteHooks(ctx context.Context, exec boil.ContextExecutor) (err error) {
	if boil.HooksAreSkipped(ctx) {
		return nil
	}

	for _, hook := range rateAfterDeleteHooks {
		if err := hook(ctx, exec, o); err != nil {
			return err
		}
	}

	return nil
}

// doBeforeUpsertHooks executes all "before Upsert" hooks.
func (o *Rate) doBeforeUpsertHooks(ctx context.Context, exec boil.ContextExecutor) (err error) {
	if boil.HooksAreSkipped(ctx) {
		return nil
	}

	for _, hook := range rateBeforeUpsertHooks {
		if err := hook(ctx, exec, o); err != nil {
			return err
		}
	}

	return nil
}

// doAfterUpsertHooks executes all "after Upsert" hooks.
func (o *Rate) doAfterUpsertHooks(ctx context.Context, exec boil.ContextExecutor) (err error) {
	if boil.HooksAreSkipped(ctx) {
		return nil
	}

	for _, hook := range rateAfterUpsertHooks {
		if err := hook(ctx, exec, o); err != nil {
			return err
		}
	}

	return nil
}

// AddRateHook registers your hook function for all future operations.
func AddRateHook(hookPoint boil.HookPoint, rateHook RateHook) {
	switch hookPoint {
	case boil.AfterSelectHook:
		rateAfterSelectHooks = append(rateAfterSelectHooks, rateHook)
	case boil.BeforeInsertHook:
		rateBeforeInsertHooks = append(rateBeforeInsertHooks, rateHook)
	case boil.AfterInsertHook:
		rateAfterInsertHooks = append(rateAfterInsertHooks, rateHook)
	case boil.BeforeUpdateHook:
		rateBeforeUpdateHooks = append(rateBeforeUpdateHooks, rateHook)
	case boil.AfterUpdateHook:
		rateAfterUpdateHooks = append(rateAfterUpdateHooks, rateHook)
	case boil.BeforeDeleteHook:
		rateBeforeDeleteHooks = append(rateBeforeDeleteHooks, rateHook)
	case boil.AfterDeleteHook:
		rateAfterDeleteHooks = append(rateAfterDeleteHooks, rateHook)
	case boil.BeforeUpsertHook:
		rateBeforeUpsertHooks = append(rateBeforeUpsertHooks, rateHook)
	case boil.AfterUpsertHook:
		rateAfterUpsertHooks = append(rateAfterUpsertHooks, rateHook)
	}
}

// One returns a single rate record from the query.
func (q rateQuery) One(ctx context.Context, exec boil.ContextExecutor) (*Rate, error) {
	o := &Rate{}

	queries.SetLimit(q.Query, 1)

	err := q.Bind(ctx, exec, o)
	if err != nil {
		if errors.Is(err, sql.ErrNoRows) {
			return nil, sql.ErrNoRows
		}
		return nil, errors.Wrap(err, "risk_metrics: failed to execute a one query for rate")
	}

	if err := o.doAfterSelectHooks(ctx, exec); err != nil {
		return o, err
	}

	return o, nil
}

// All returns all Rate records from the query.
func (q rateQuery) All(ctx context.Context, exec boil.ContextExecutor) (RateSlice, error) {
	var o []*Rate

	err := q.Bind(ctx, exec, &o)
	if err != nil {
		return nil, errors.Wrap(err, "risk_metrics: failed to assign all query results to Rate slice")
	}

	if len(rateAfterSelectHooks) != 0 {
		for _, obj := range o {
			if err := obj.doAfterSelectHooks(ctx, exec); err != nil {
				return o, err
			}
		}
	}

	return o, nil
}

// Count returns the count of all Rate records in the query.
func (q rateQuery) Count(ctx context.Context, exec boil.ContextExecutor) (int64, error) {
	var count int64

	queries.SetSelect(q.Query, nil)
	queries.SetCount(q.Query)

	err := q.Query.QueryRowContext(ctx, exec).Scan(&count)
	if err != nil {
		return 0, errors.Wrap(err, "risk_metrics: failed to count rate rows")
	}

	return count, nil
}

// Exists checks if the row exists in the table.
func (q rateQuery) Exists(ctx context.Context, exec boil.ContextExecutor) (bool, error) {
	var count int64

	queries.SetSelect(q.Query, nil)
	queries.SetCount(q.Query)
	queries.SetLimit(q.Query, 1)

	err := q.Query.QueryRowContext(ctx, exec).Scan(&count)
	if err != nil {
		return false, errors.Wrap(err, "risk_metrics: failed to check if rate exists")
	}

	return count > 0, nil
}

// Rates retrieves all the records using an executor.
func Rates(mods ...qm.QueryMod) rateQuery {
	mods = append(mods, qm.From("\"risk_metrics\".\"rate\""))
	q := NewQuery(mods...)
	if len(queries.GetSelect(q)) == 0 {
		queries.SetSelect(q, []string{"\"risk_metrics\".\"rate\".*"})
	}

	return rateQuery{q}
}

// FindRate retrieves a single record by ID with an executor.
// If selectCols is empty Find will return all columns.
func FindRate(ctx context.Context, exec boil.ContextExecutor, iD string, selectCols ...string) (*Rate, error) {
	rateObj := &Rate{}

	sel := "*"
	if len(selectCols) > 0 {
		sel = strings.Join(strmangle.IdentQuoteSlice(dialect.LQ, dialect.RQ, selectCols), ",")
	}
	query := fmt.Sprintf(
		"select %s from \"risk_metrics\".\"rate\" where \"id\"=$1", sel,
	)

	q := queries.Raw(query, iD)

	err := q.Bind(ctx, exec, rateObj)
	if err != nil {
		if errors.Is(err, sql.ErrNoRows) {
			return nil, sql.ErrNoRows
		}
		return nil, errors.Wrap(err, "risk_metrics: unable to select from rate")
	}

	if err = rateObj.doAfterSelectHooks(ctx, exec); err != nil {
		return rateObj, err
	}

	return rateObj, nil
}

// Insert a single record using an executor.
// See boil.Columns.InsertColumnSet documentation to understand column list inference for inserts.
func (o *Rate) Insert(ctx context.Context, exec boil.ContextExecutor, columns boil.Columns) error {
	if o == nil {
		return errors.New("risk_metrics: no rate provided for insertion")
	}

	var err error
	if !boil.TimestampsAreSkipped(ctx) {
		currTime := time.Now().In(boil.GetLocation())

		if o.CreatedAt.IsZero() {
			o.CreatedAt = currTime
		}
	}

	if err := o.doBeforeInsertHooks(ctx, exec); err != nil {
		return err
	}

	nzDefaults := queries.NonZeroDefaultSet(rateColumnsWithDefault, o)

	key := makeCacheKey(columns, nzDefaults)
	rateInsertCacheMut.RLock()
	cache, cached := rateInsertCache[key]
	rateInsertCacheMut.RUnlock()

	if !cached {
		wl, returnColumns := columns.InsertColumnSet(
			rateAllColumns,
			rateColumnsWithDefault,
			rateColumnsWithoutDefault,
			nzDefaults,
		)

		cache.valueMapping, err = queries.BindMapping(rateType, rateMapping, wl)
		if err != nil {
			return err
		}
		cache.retMapping, err = queries.BindMapping(rateType, rateMapping, returnColumns)
		if err != nil {
			return err
		}
		if len(wl) != 0 {
			cache.query = fmt.Sprintf("INSERT INTO \"risk_metrics\".\"rate\" (\"%s\") %%sVALUES (%s)%%s", strings.Join(wl, "\",\""), strmangle.Placeholders(dialect.UseIndexPlaceholders, len(wl), 1, 1))
		} else {
			cache.query = "INSERT INTO \"risk_metrics\".\"rate\" %sDEFAULT VALUES%s"
		}

		var queryOutput, queryReturning string

		if len(cache.retMapping) != 0 {
			queryReturning = fmt.Sprintf(" RETURNING \"%s\"", strings.Join(returnColumns, "\",\""))
		}

		cache.query = fmt.Sprintf(cache.query, queryOutput, queryReturning)
	}

	value := reflect.Indirect(reflect.ValueOf(o))
	vals := queries.ValuesFromMapping(value, cache.valueMapping)

	if boil.IsDebug(ctx) {
		writer := boil.DebugWriterFrom(ctx)
		fmt.Fprintln(writer, cache.query)
		fmt.Fprintln(writer, vals)
	}

	if len(cache.retMapping) != 0 {
		err = exec.QueryRowContext(ctx, cache.query, vals...).Scan(queries.PtrsFromMapping(value, cache.retMapping)...)
	} else {
		_, err = exec.ExecContext(ctx, cache.query, vals...)
	}

	if err != nil {
		return errors.Wrap(err, "risk_metrics: unable to insert into rate")
	}

	if !cached {
		rateInsertCacheMut.Lock()
		rateInsertCache[key] = cache
		rateInsertCacheMut.Unlock()
	}

	return o.doAfterInsertHooks(ctx, exec)
}

// Update uses an executor to update the Rate.
// See boil.Columns.UpdateColumnSet documentation to understand column list inference for updates.
// Update does not automatically update the record in case of default values. Use .Reload() to refresh the records.
func (o *Rate) Update(ctx context.Context, exec boil.ContextExecutor, columns boil.Columns) (int64, error) {
	var err error
	if err = o.doBeforeUpdateHooks(ctx, exec); err != nil {
		return 0, err
	}
	key := makeCacheKey(columns, nil)
	rateUpdateCacheMut.RLock()
	cache, cached := rateUpdateCache[key]
	rateUpdateCacheMut.RUnlock()

	if !cached {
		wl := columns.UpdateColumnSet(
			rateAllColumns,
			ratePrimaryKeyColumns,
		)

		if !columns.IsWhitelist() {
			wl = strmangle.SetComplement(wl, []string{"created_at"})
		}
		if len(wl) == 0 {
			return 0, errors.New("risk_metrics: unable to update rate, could not build whitelist")
		}

		cache.query = fmt.Sprintf("UPDATE \"risk_metrics\".\"rate\" SET %s WHERE %s",
			strmangle.SetParamNames("\"", "\"", 1, wl),
			strmangle.WhereClause("\"", "\"", len(wl)+1, ratePrimaryKeyColumns),
		)
		cache.valueMapping, err = queries.BindMapping(rateType, rateMapping, append(wl, ratePrimaryKeyColumns...))
		if err != nil {
			return 0, err
		}
	}

	values := queries.ValuesFromMapping(reflect.Indirect(reflect.ValueOf(o)), cache.valueMapping)

	if boil.IsDebug(ctx) {
		writer := boil.DebugWriterFrom(ctx)
		fmt.Fprintln(writer, cache.query)
		fmt.Fprintln(writer, values)
	}
	var result sql.Result
	result, err = exec.ExecContext(ctx, cache.query, values...)
	if err != nil {
		return 0, errors.Wrap(err, "risk_metrics: unable to update rate row")
	}

	rowsAff, err := result.RowsAffected()
	if err != nil {
		return 0, errors.Wrap(err, "risk_metrics: failed to get rows affected by update for rate")
	}

	if !cached {
		rateUpdateCacheMut.Lock()
		rateUpdateCache[key] = cache
		rateUpdateCacheMut.Unlock()
	}

	return rowsAff, o.doAfterUpdateHooks(ctx, exec)
}

// UpdateAll updates all rows with the specified column values.
func (q rateQuery) UpdateAll(ctx context.Context, exec boil.ContextExecutor, cols M) (int64, error) {
	queries.SetUpdate(q.Query, cols)

	result, err := q.Query.ExecContext(ctx, exec)
	if err != nil {
		return 0, errors.Wrap(err, "risk_metrics: unable to update all for rate")
	}

	rowsAff, err := result.RowsAffected()
	if err != nil {
		return 0, errors.Wrap(err, "risk_metrics: unable to retrieve rows affected for rate")
	}

	return rowsAff, nil
}

// UpdateAll updates all rows with the specified column values, using an executor.
func (o RateSlice) UpdateAll(ctx context.Context, exec boil.ContextExecutor, cols M) (int64, error) {
	ln := int64(len(o))
	if ln == 0 {
		return 0, nil
	}

	if len(cols) == 0 {
		return 0, errors.New("risk_metrics: update all requires at least one column argument")
	}

	colNames := make([]string, len(cols))
	args := make([]interface{}, len(cols))

	i := 0
	for name, value := range cols {
		colNames[i] = name
		args[i] = value
		i++
	}

	// Append all of the primary key values for each column
	for _, obj := range o {
		pkeyArgs := queries.ValuesFromMapping(reflect.Indirect(reflect.ValueOf(obj)), ratePrimaryKeyMapping)
		args = append(args, pkeyArgs...)
	}

	sql := fmt.Sprintf("UPDATE \"risk_metrics\".\"rate\" SET %s WHERE %s",
		strmangle.SetParamNames("\"", "\"", 1, colNames),
		strmangle.WhereClauseRepeated(string(dialect.LQ), string(dialect.RQ), len(colNames)+1, ratePrimaryKeyColumns, len(o)))

	if boil.IsDebug(ctx) {
		writer := boil.DebugWriterFrom(ctx)
		fmt.Fprintln(writer, sql)
		fmt.Fprintln(writer, args...)
	}
	result, err := exec.ExecContext(ctx, sql, args...)
	if err != nil {
		return 0, errors.Wrap(err, "risk_metrics: unable to update all in rate slice")
	}

	rowsAff, err := result.RowsAffected()
	if err != nil {
		return 0, errors.Wrap(err, "risk_metrics: unable to retrieve rows affected all in update all rate")
	}
	return rowsAff, nil
}

// Upsert attempts an insert using an executor, and does an update or ignore on conflict.
// See boil.Columns documentation for how to properly use updateColumns and insertColumns.
func (o *Rate) Upsert(ctx context.Context, exec boil.ContextExecutor, updateOnConflict bool, conflictColumns []string, updateColumns, insertColumns boil.Columns) error {
	if o == nil {
		return errors.New("risk_metrics: no rate provided for upsert")
	}
	if !boil.TimestampsAreSkipped(ctx) {
		currTime := time.Now().In(boil.GetLocation())

		if o.CreatedAt.IsZero() {
			o.CreatedAt = currTime
		}
	}

	if err := o.doBeforeUpsertHooks(ctx, exec); err != nil {
		return err
	}

	nzDefaults := queries.NonZeroDefaultSet(rateColumnsWithDefault, o)

	// Build cache key in-line uglily - mysql vs psql problems
	buf := strmangle.GetBuffer()
	if updateOnConflict {
		buf.WriteByte('t')
	} else {
		buf.WriteByte('f')
	}
	buf.WriteByte('.')
	for _, c := range conflictColumns {
		buf.WriteString(c)
	}
	buf.WriteByte('.')
	buf.WriteString(strconv.Itoa(updateColumns.Kind))
	for _, c := range updateColumns.Cols {
		buf.WriteString(c)
	}
	buf.WriteByte('.')
	buf.WriteString(strconv.Itoa(insertColumns.Kind))
	for _, c := range insertColumns.Cols {
		buf.WriteString(c)
	}
	buf.WriteByte('.')
	for _, c := range nzDefaults {
		buf.WriteString(c)
	}
	key := buf.String()
	strmangle.PutBuffer(buf)

	rateUpsertCacheMut.RLock()
	cache, cached := rateUpsertCache[key]
	rateUpsertCacheMut.RUnlock()

	var err error

	if !cached {
		insert, ret := insertColumns.InsertColumnSet(
			rateAllColumns,
			rateColumnsWithDefault,
			rateColumnsWithoutDefault,
			nzDefaults,
		)

		update := updateColumns.UpdateColumnSet(
			rateAllColumns,
			ratePrimaryKeyColumns,
		)

		if updateOnConflict && len(update) == 0 {
			return errors.New("risk_metrics: unable to upsert rate, could not build update column list")
		}

		conflict := conflictColumns
		if len(conflict) == 0 {
			conflict = make([]string, len(ratePrimaryKeyColumns))
			copy(conflict, ratePrimaryKeyColumns)
		}
		cache.query = buildUpsertQueryPostgres(dialect, "\"risk_metrics\".\"rate\"", updateOnConflict, ret, update, conflict, insert)

		cache.valueMapping, err = queries.BindMapping(rateType, rateMapping, insert)
		if err != nil {
			return err
		}
		if len(ret) != 0 {
			cache.retMapping, err = queries.BindMapping(rateType, rateMapping, ret)
			if err != nil {
				return err
			}
		}
	}

	value := reflect.Indirect(reflect.ValueOf(o))
	vals := queries.ValuesFromMapping(value, cache.valueMapping)
	var returns []interface{}
	if len(cache.retMapping) != 0 {
		returns = queries.PtrsFromMapping(value, cache.retMapping)
	}

	if boil.IsDebug(ctx) {
		writer := boil.DebugWriterFrom(ctx)
		fmt.Fprintln(writer, cache.query)
		fmt.Fprintln(writer, vals)
	}
	if len(cache.retMapping) != 0 {
		err = exec.QueryRowContext(ctx, cache.query, vals...).Scan(returns...)
		if errors.Is(err, sql.ErrNoRows) {
			err = nil // Postgres doesn't return anything when there's no update
		}
	} else {
		_, err = exec.ExecContext(ctx, cache.query, vals...)
	}
	if err != nil {
		return errors.Wrap(err, "risk_metrics: unable to upsert rate")
	}

	if !cached {
		rateUpsertCacheMut.Lock()
		rateUpsertCache[key] = cache
		rateUpsertCacheMut.Unlock()
	}

	return o.doAfterUpsertHooks(ctx, exec)
}

// Delete deletes a single Rate record with an executor.
// Delete will match against the primary key column to find the record to delete.
func (o *Rate) Delete(ctx context.Context, exec boil.ContextExecutor) (int64, error) {
	if o == nil {
		return 0, errors.New("risk_metrics: no Rate provided for delete")
	}

	if err := o.doBeforeDeleteHooks(ctx, exec); err != nil {
		return 0, err
	}

	args := queries.ValuesFromMapping(reflect.Indirect(reflect.ValueOf(o)), ratePrimaryKeyMapping)
	sql := "DELETE FROM \"risk_metrics\".\"rate\" WHERE \"id\"=$1"

	if boil.IsDebug(ctx) {
		writer := boil.DebugWriterFrom(ctx)
		fmt.Fprintln(writer, sql)
		fmt.Fprintln(writer, args...)
	}
	result, err := exec.ExecContext(ctx, sql, args...)
	if err != nil {
		return 0, errors.Wrap(err, "risk_metrics: unable to delete from rate")
	}

	rowsAff, err := result.RowsAffected()
	if err != nil {
		return 0, errors.Wrap(err, "risk_metrics: failed to get rows affected by delete for rate")
	}

	if err := o.doAfterDeleteHooks(ctx, exec); err != nil {
		return 0, err
	}

	return rowsAff, nil
}

// DeleteAll deletes all matching rows.
func (q rateQuery) DeleteAll(ctx context.Context, exec boil.ContextExecutor) (int64, error) {
	if q.Query == nil {
		return 0, errors.New("risk_metrics: no rateQuery provided for delete all")
	}

	queries.SetDelete(q.Query)

	result, err := q.Query.ExecContext(ctx, exec)
	if err != nil {
		return 0, errors.Wrap(err, "risk_metrics: unable to delete all from rate")
	}

	rowsAff, err := result.RowsAffected()
	if err != nil {
		return 0, errors.Wrap(err, "risk_metrics: failed to get rows affected by deleteall for rate")
	}

	return rowsAff, nil
}

// DeleteAll deletes all rows in the slice, using an executor.
func (o RateSlice) DeleteAll(ctx context.Context, exec boil.ContextExecutor) (int64, error) {
	if len(o) == 0 {
		return 0, nil
	}

	if len(rateBeforeDeleteHooks) != 0 {
		for _, obj := range o {
			if err := obj.doBeforeDeleteHooks(ctx, exec); err != nil {
				return 0, err
			}
		}
	}

	var args []interface{}
	for _, obj := range o {
		pkeyArgs := queries.ValuesFromMapping(reflect.Indirect(reflect.ValueOf(obj)), ratePrimaryKeyMapping)
		args = append(args, pkeyArgs...)
	}

	sql := "DELETE FROM \"risk_metrics\".\"rate\" WHERE " +
		strmangle.WhereClauseRepeated(string(dialect.LQ), string(dialect.RQ), 1, ratePrimaryKeyColumns, len(o))

	if boil.IsDebug(ctx) {
		writer := boil.DebugWriterFrom(ctx)
		fmt.Fprintln(writer, sql)
		fmt.Fprintln(writer, args)
	}
	result, err := exec.ExecContext(ctx, sql, args...)
	if err != nil {
		return 0, errors.Wrap(err, "risk_metrics: unable to delete all from rate slice")
	}

	rowsAff, err := result.RowsAffected()
	if err != nil {
		return 0, errors.Wrap(err, "risk_metrics: failed to get rows affected by deleteall for rate")
	}

	if len(rateAfterDeleteHooks) != 0 {
		for _, obj := range o {
			if err := obj.doAfterDeleteHooks(ctx, exec); err != nil {
				return 0, err
			}
		}
	}

	return rowsAff, nil
}

// Reload refetches the object from the database
// using the primary keys with an executor.
func (o *Rate) Reload(ctx context.Context, exec boil.ContextExecutor) error {
	ret, err := FindRate(ctx, exec, o.ID)
	if err != nil {
		return err
	}

	*o = *ret
	return nil
}

// ReloadAll refetches every row with matching primary key column values
// and overwrites the original object slice with the newly updated slice.
func (o *RateSlice) ReloadAll(ctx context.Context, exec boil.ContextExecutor) error {
	if o == nil || len(*o) == 0 {
		return nil
	}

	slice := RateSlice{}
	var args []interface{}
	for _, obj := range *o {
		pkeyArgs := queries.ValuesFromMapping(reflect.Indirect(reflect.ValueOf(obj)), ratePrimaryKeyMapping)
		args = append(args, pkeyArgs...)
	}

	sql := "SELECT \"risk_metrics\".\"rate\".* FROM \"risk_metrics\".\"rate\" WHERE " +
		strmangle.WhereClauseRepeated(string(dialect.LQ), string(dialect.RQ), 1, ratePrimaryKeyColumns, len(*o))

	q := queries.Raw(sql, args...)

	err := q.Bind(ctx, exec, &slice)
	if err != nil {
		return errors.Wrap(err, "risk_metrics: unable to reload all in RateSlice")
	}

	*o = slice

	return nil
}

// RateExists checks if the Rate row exists.
func RateExists(ctx context.Context, exec boil.ContextExecutor, iD string) (bool, error) {
	var exists bool
	sql := "select exists(select 1 from \"risk_metrics\".\"rate\" where \"id\"=$1 limit 1)"

	if boil.IsDebug(ctx) {
		writer := boil.DebugWriterFrom(ctx)
		fmt.Fprintln(writer, sql)
		fmt.Fprintln(writer, iD)
	}
	row := exec.QueryRowContext(ctx, sql, iD)

	err := row.Scan(&exists)
	if err != nil {
		return false, errors.Wrap(err, "risk_metrics: unable to check if rate exists")
	}

	return exists, nil
}
