// Code generated by SQLBoiler (https://github.com/volatiletech/sqlboiler). DO NOT EDIT.
// This file is meant to be re-generated in place and/or deleted at any time.

package telematics

import (
	"context"
	"database/sql"
	"fmt"
	"reflect"
	"strconv"
	"strings"
	"sync"
	"time"

	"github.com/friendsofgo/errors"
	"github.com/volatiletech/null/v8"
	"github.com/volatiletech/sqlboiler/v4/boil"
	"github.com/volatiletech/sqlboiler/v4/queries"
	"github.com/volatiletech/sqlboiler/v4/queries/qm"
	"github.com/volatiletech/sqlboiler/v4/queries/qmhelper"
	"github.com/volatiletech/strmangle"
)

// SubscriptionRequest is an object representing the database table.
type SubscriptionRequest struct {
	RequestID        string      `boil:"request_id" json:"request_id" toml:"request_id" yaml:"request_id"`
	SubscriptionID   null.String `boil:"subscription_id" json:"subscription_id,omitempty" toml:"subscription_id" yaml:"subscription_id,omitempty"`
	HandleID         string      `boil:"handle_id" json:"handle_id" toml:"handle_id" yaml:"handle_id"`
	SubscriptionType string      `boil:"subscription_type" json:"subscription_type" toml:"subscription_type" yaml:"subscription_type"`
	FleetName        string      `boil:"fleet_name" json:"fleet_name" toml:"fleet_name" yaml:"fleet_name"`
	DotNumber        int64       `boil:"dot_number" json:"dot_number" toml:"dot_number" yaml:"dot_number"`
	CreatedAt        time.Time   `boil:"created_at" json:"created_at" toml:"created_at" yaml:"created_at"`
	CreatedBy        string      `boil:"created_by" json:"created_by" toml:"created_by" yaml:"created_by"`
	ExpireAt         null.Time   `boil:"expire_at" json:"expire_at,omitempty" toml:"expire_at" yaml:"expire_at,omitempty"`

	R *subscriptionRequestR `boil:"" json:"" toml:"" yaml:""`
	L subscriptionRequestL  `boil:"-" json:"-" toml:"-" yaml:"-"`
}

var SubscriptionRequestColumns = struct {
	RequestID        string
	SubscriptionID   string
	HandleID         string
	SubscriptionType string
	FleetName        string
	DotNumber        string
	CreatedAt        string
	CreatedBy        string
	ExpireAt         string
}{
	RequestID:        "request_id",
	SubscriptionID:   "subscription_id",
	HandleID:         "handle_id",
	SubscriptionType: "subscription_type",
	FleetName:        "fleet_name",
	DotNumber:        "dot_number",
	CreatedAt:        "created_at",
	CreatedBy:        "created_by",
	ExpireAt:         "expire_at",
}

var SubscriptionRequestTableColumns = struct {
	RequestID        string
	SubscriptionID   string
	HandleID         string
	SubscriptionType string
	FleetName        string
	DotNumber        string
	CreatedAt        string
	CreatedBy        string
	ExpireAt         string
}{
	RequestID:        "subscription_requests.request_id",
	SubscriptionID:   "subscription_requests.subscription_id",
	HandleID:         "subscription_requests.handle_id",
	SubscriptionType: "subscription_requests.subscription_type",
	FleetName:        "subscription_requests.fleet_name",
	DotNumber:        "subscription_requests.dot_number",
	CreatedAt:        "subscription_requests.created_at",
	CreatedBy:        "subscription_requests.created_by",
	ExpireAt:         "subscription_requests.expire_at",
}

// Generated where

var SubscriptionRequestWhere = struct {
	RequestID        whereHelperstring
	SubscriptionID   whereHelpernull_String
	HandleID         whereHelperstring
	SubscriptionType whereHelperstring
	FleetName        whereHelperstring
	DotNumber        whereHelperint64
	CreatedAt        whereHelpertime_Time
	CreatedBy        whereHelperstring
	ExpireAt         whereHelpernull_Time
}{
	RequestID:        whereHelperstring{field: "\"telematics\".\"subscription_requests\".\"request_id\""},
	SubscriptionID:   whereHelpernull_String{field: "\"telematics\".\"subscription_requests\".\"subscription_id\""},
	HandleID:         whereHelperstring{field: "\"telematics\".\"subscription_requests\".\"handle_id\""},
	SubscriptionType: whereHelperstring{field: "\"telematics\".\"subscription_requests\".\"subscription_type\""},
	FleetName:        whereHelperstring{field: "\"telematics\".\"subscription_requests\".\"fleet_name\""},
	DotNumber:        whereHelperint64{field: "\"telematics\".\"subscription_requests\".\"dot_number\""},
	CreatedAt:        whereHelpertime_Time{field: "\"telematics\".\"subscription_requests\".\"created_at\""},
	CreatedBy:        whereHelperstring{field: "\"telematics\".\"subscription_requests\".\"created_by\""},
	ExpireAt:         whereHelpernull_Time{field: "\"telematics\".\"subscription_requests\".\"expire_at\""},
}

// SubscriptionRequestRels is where relationship names are stored.
var SubscriptionRequestRels = struct {
	Subscription                           string
	RequestMonthlyRefreshSubscriptionsInfo string
}{
	Subscription:                           "Subscription",
	RequestMonthlyRefreshSubscriptionsInfo: "RequestMonthlyRefreshSubscriptionsInfo",
}

// subscriptionRequestR is where relationships are stored.
type subscriptionRequestR struct {
	Subscription                           *SubscriptionsState              `boil:"Subscription" json:"Subscription" toml:"Subscription" yaml:"Subscription"`
	RequestMonthlyRefreshSubscriptionsInfo *MonthlyRefreshSubscriptionsInfo `boil:"RequestMonthlyRefreshSubscriptionsInfo" json:"RequestMonthlyRefreshSubscriptionsInfo" toml:"RequestMonthlyRefreshSubscriptionsInfo" yaml:"RequestMonthlyRefreshSubscriptionsInfo"`
}

// NewStruct creates a new relationship struct
func (*subscriptionRequestR) NewStruct() *subscriptionRequestR {
	return &subscriptionRequestR{}
}

// subscriptionRequestL is where Load methods for each relationship are stored.
type subscriptionRequestL struct{}

var (
	subscriptionRequestAllColumns            = []string{"request_id", "subscription_id", "handle_id", "subscription_type", "fleet_name", "dot_number", "created_at", "created_by", "expire_at"}
	subscriptionRequestColumnsWithoutDefault = []string{"request_id", "handle_id", "subscription_type", "fleet_name", "dot_number", "created_at", "created_by"}
	subscriptionRequestColumnsWithDefault    = []string{"subscription_id", "expire_at"}
	subscriptionRequestPrimaryKeyColumns     = []string{"request_id"}
	subscriptionRequestGeneratedColumns      = []string{}
)

type (
	// SubscriptionRequestSlice is an alias for a slice of pointers to SubscriptionRequest.
	// This should almost always be used instead of []SubscriptionRequest.
	SubscriptionRequestSlice []*SubscriptionRequest
	// SubscriptionRequestHook is the signature for custom SubscriptionRequest hook methods
	SubscriptionRequestHook func(context.Context, boil.ContextExecutor, *SubscriptionRequest) error

	subscriptionRequestQuery struct {
		*queries.Query
	}
)

// Cache for insert, update and upsert
var (
	subscriptionRequestType                 = reflect.TypeOf(&SubscriptionRequest{})
	subscriptionRequestMapping              = queries.MakeStructMapping(subscriptionRequestType)
	subscriptionRequestPrimaryKeyMapping, _ = queries.BindMapping(subscriptionRequestType, subscriptionRequestMapping, subscriptionRequestPrimaryKeyColumns)
	subscriptionRequestInsertCacheMut       sync.RWMutex
	subscriptionRequestInsertCache          = make(map[string]insertCache)
	subscriptionRequestUpdateCacheMut       sync.RWMutex
	subscriptionRequestUpdateCache          = make(map[string]updateCache)
	subscriptionRequestUpsertCacheMut       sync.RWMutex
	subscriptionRequestUpsertCache          = make(map[string]insertCache)
)

var (
	// Force time package dependency for automated UpdatedAt/CreatedAt.
	_ = time.Second
	// Force qmhelper dependency for where clause generation (which doesn't
	// always happen)
	_ = qmhelper.Where
)

var subscriptionRequestAfterSelectHooks []SubscriptionRequestHook

var subscriptionRequestBeforeInsertHooks []SubscriptionRequestHook
var subscriptionRequestAfterInsertHooks []SubscriptionRequestHook

var subscriptionRequestBeforeUpdateHooks []SubscriptionRequestHook
var subscriptionRequestAfterUpdateHooks []SubscriptionRequestHook

var subscriptionRequestBeforeDeleteHooks []SubscriptionRequestHook
var subscriptionRequestAfterDeleteHooks []SubscriptionRequestHook

var subscriptionRequestBeforeUpsertHooks []SubscriptionRequestHook
var subscriptionRequestAfterUpsertHooks []SubscriptionRequestHook

// doAfterSelectHooks executes all "after Select" hooks.
func (o *SubscriptionRequest) doAfterSelectHooks(ctx context.Context, exec boil.ContextExecutor) (err error) {
	if boil.HooksAreSkipped(ctx) {
		return nil
	}

	for _, hook := range subscriptionRequestAfterSelectHooks {
		if err := hook(ctx, exec, o); err != nil {
			return err
		}
	}

	return nil
}

// doBeforeInsertHooks executes all "before insert" hooks.
func (o *SubscriptionRequest) doBeforeInsertHooks(ctx context.Context, exec boil.ContextExecutor) (err error) {
	if boil.HooksAreSkipped(ctx) {
		return nil
	}

	for _, hook := range subscriptionRequestBeforeInsertHooks {
		if err := hook(ctx, exec, o); err != nil {
			return err
		}
	}

	return nil
}

// doAfterInsertHooks executes all "after Insert" hooks.
func (o *SubscriptionRequest) doAfterInsertHooks(ctx context.Context, exec boil.ContextExecutor) (err error) {
	if boil.HooksAreSkipped(ctx) {
		return nil
	}

	for _, hook := range subscriptionRequestAfterInsertHooks {
		if err := hook(ctx, exec, o); err != nil {
			return err
		}
	}

	return nil
}

// doBeforeUpdateHooks executes all "before Update" hooks.
func (o *SubscriptionRequest) doBeforeUpdateHooks(ctx context.Context, exec boil.ContextExecutor) (err error) {
	if boil.HooksAreSkipped(ctx) {
		return nil
	}

	for _, hook := range subscriptionRequestBeforeUpdateHooks {
		if err := hook(ctx, exec, o); err != nil {
			return err
		}
	}

	return nil
}

// doAfterUpdateHooks executes all "after Update" hooks.
func (o *SubscriptionRequest) doAfterUpdateHooks(ctx context.Context, exec boil.ContextExecutor) (err error) {
	if boil.HooksAreSkipped(ctx) {
		return nil
	}

	for _, hook := range subscriptionRequestAfterUpdateHooks {
		if err := hook(ctx, exec, o); err != nil {
			return err
		}
	}

	return nil
}

// doBeforeDeleteHooks executes all "before Delete" hooks.
func (o *SubscriptionRequest) doBeforeDeleteHooks(ctx context.Context, exec boil.ContextExecutor) (err error) {
	if boil.HooksAreSkipped(ctx) {
		return nil
	}

	for _, hook := range subscriptionRequestBeforeDeleteHooks {
		if err := hook(ctx, exec, o); err != nil {
			return err
		}
	}

	return nil
}

// doAfterDeleteHooks executes all "after Delete" hooks.
func (o *SubscriptionRequest) doAfterDeleteHooks(ctx context.Context, exec boil.ContextExecutor) (err error) {
	if boil.HooksAreSkipped(ctx) {
		return nil
	}

	for _, hook := range subscriptionRequestAfterDeleteHooks {
		if err := hook(ctx, exec, o); err != nil {
			return err
		}
	}

	return nil
}

// doBeforeUpsertHooks executes all "before Upsert" hooks.
func (o *SubscriptionRequest) doBeforeUpsertHooks(ctx context.Context, exec boil.ContextExecutor) (err error) {
	if boil.HooksAreSkipped(ctx) {
		return nil
	}

	for _, hook := range subscriptionRequestBeforeUpsertHooks {
		if err := hook(ctx, exec, o); err != nil {
			return err
		}
	}

	return nil
}

// doAfterUpsertHooks executes all "after Upsert" hooks.
func (o *SubscriptionRequest) doAfterUpsertHooks(ctx context.Context, exec boil.ContextExecutor) (err error) {
	if boil.HooksAreSkipped(ctx) {
		return nil
	}

	for _, hook := range subscriptionRequestAfterUpsertHooks {
		if err := hook(ctx, exec, o); err != nil {
			return err
		}
	}

	return nil
}

// AddSubscriptionRequestHook registers your hook function for all future operations.
func AddSubscriptionRequestHook(hookPoint boil.HookPoint, subscriptionRequestHook SubscriptionRequestHook) {
	switch hookPoint {
	case boil.AfterSelectHook:
		subscriptionRequestAfterSelectHooks = append(subscriptionRequestAfterSelectHooks, subscriptionRequestHook)
	case boil.BeforeInsertHook:
		subscriptionRequestBeforeInsertHooks = append(subscriptionRequestBeforeInsertHooks, subscriptionRequestHook)
	case boil.AfterInsertHook:
		subscriptionRequestAfterInsertHooks = append(subscriptionRequestAfterInsertHooks, subscriptionRequestHook)
	case boil.BeforeUpdateHook:
		subscriptionRequestBeforeUpdateHooks = append(subscriptionRequestBeforeUpdateHooks, subscriptionRequestHook)
	case boil.AfterUpdateHook:
		subscriptionRequestAfterUpdateHooks = append(subscriptionRequestAfterUpdateHooks, subscriptionRequestHook)
	case boil.BeforeDeleteHook:
		subscriptionRequestBeforeDeleteHooks = append(subscriptionRequestBeforeDeleteHooks, subscriptionRequestHook)
	case boil.AfterDeleteHook:
		subscriptionRequestAfterDeleteHooks = append(subscriptionRequestAfterDeleteHooks, subscriptionRequestHook)
	case boil.BeforeUpsertHook:
		subscriptionRequestBeforeUpsertHooks = append(subscriptionRequestBeforeUpsertHooks, subscriptionRequestHook)
	case boil.AfterUpsertHook:
		subscriptionRequestAfterUpsertHooks = append(subscriptionRequestAfterUpsertHooks, subscriptionRequestHook)
	}
}

// One returns a single subscriptionRequest record from the query.
func (q subscriptionRequestQuery) One(ctx context.Context, exec boil.ContextExecutor) (*SubscriptionRequest, error) {
	o := &SubscriptionRequest{}

	queries.SetLimit(q.Query, 1)

	err := q.Bind(ctx, exec, o)
	if err != nil {
		if errors.Is(err, sql.ErrNoRows) {
			return nil, sql.ErrNoRows
		}
		return nil, errors.Wrap(err, "telematics: failed to execute a one query for subscription_requests")
	}

	if err := o.doAfterSelectHooks(ctx, exec); err != nil {
		return o, err
	}

	return o, nil
}

// All returns all SubscriptionRequest records from the query.
func (q subscriptionRequestQuery) All(ctx context.Context, exec boil.ContextExecutor) (SubscriptionRequestSlice, error) {
	var o []*SubscriptionRequest

	err := q.Bind(ctx, exec, &o)
	if err != nil {
		return nil, errors.Wrap(err, "telematics: failed to assign all query results to SubscriptionRequest slice")
	}

	if len(subscriptionRequestAfterSelectHooks) != 0 {
		for _, obj := range o {
			if err := obj.doAfterSelectHooks(ctx, exec); err != nil {
				return o, err
			}
		}
	}

	return o, nil
}

// Count returns the count of all SubscriptionRequest records in the query.
func (q subscriptionRequestQuery) Count(ctx context.Context, exec boil.ContextExecutor) (int64, error) {
	var count int64

	queries.SetSelect(q.Query, nil)
	queries.SetCount(q.Query)

	err := q.Query.QueryRowContext(ctx, exec).Scan(&count)
	if err != nil {
		return 0, errors.Wrap(err, "telematics: failed to count subscription_requests rows")
	}

	return count, nil
}

// Exists checks if the row exists in the table.
func (q subscriptionRequestQuery) Exists(ctx context.Context, exec boil.ContextExecutor) (bool, error) {
	var count int64

	queries.SetSelect(q.Query, nil)
	queries.SetCount(q.Query)
	queries.SetLimit(q.Query, 1)

	err := q.Query.QueryRowContext(ctx, exec).Scan(&count)
	if err != nil {
		return false, errors.Wrap(err, "telematics: failed to check if subscription_requests exists")
	}

	return count > 0, nil
}

// Subscription pointed to by the foreign key.
func (o *SubscriptionRequest) Subscription(mods ...qm.QueryMod) subscriptionsStateQuery {
	queryMods := []qm.QueryMod{
		qm.Where("\"subscription_id\" = ?", o.SubscriptionID),
	}

	queryMods = append(queryMods, mods...)

	return SubscriptionsStates(queryMods...)
}

// RequestMonthlyRefreshSubscriptionsInfo pointed to by the foreign key.
func (o *SubscriptionRequest) RequestMonthlyRefreshSubscriptionsInfo(mods ...qm.QueryMod) monthlyRefreshSubscriptionsInfoQuery {
	queryMods := []qm.QueryMod{
		qm.Where("\"request_id\" = ?", o.RequestID),
	}

	queryMods = append(queryMods, mods...)

	return MonthlyRefreshSubscriptionsInfos(queryMods...)
}

// LoadSubscription allows an eager lookup of values, cached into the
// loaded structs of the objects. This is for an N-1 relationship.
func (subscriptionRequestL) LoadSubscription(ctx context.Context, e boil.ContextExecutor, singular bool, maybeSubscriptionRequest interface{}, mods queries.Applicator) error {
	var slice []*SubscriptionRequest
	var object *SubscriptionRequest

	if singular {
		object = maybeSubscriptionRequest.(*SubscriptionRequest)
	} else {
		slice = *maybeSubscriptionRequest.(*[]*SubscriptionRequest)
	}

	args := make([]interface{}, 0, 1)
	if singular {
		if object.R == nil {
			object.R = &subscriptionRequestR{}
		}
		if !queries.IsNil(object.SubscriptionID) {
			args = append(args, object.SubscriptionID)
		}

	} else {
	Outer:
		for _, obj := range slice {
			if obj.R == nil {
				obj.R = &subscriptionRequestR{}
			}

			for _, a := range args {
				if queries.Equal(a, obj.SubscriptionID) {
					continue Outer
				}
			}

			if !queries.IsNil(obj.SubscriptionID) {
				args = append(args, obj.SubscriptionID)
			}

		}
	}

	if len(args) == 0 {
		return nil
	}

	query := NewQuery(
		qm.From(`telematics.subscriptions_state`),
		qm.WhereIn(`telematics.subscriptions_state.subscription_id in ?`, args...),
	)
	if mods != nil {
		mods.Apply(query)
	}

	results, err := query.QueryContext(ctx, e)
	if err != nil {
		return errors.Wrap(err, "failed to eager load SubscriptionsState")
	}

	var resultSlice []*SubscriptionsState
	if err = queries.Bind(results, &resultSlice); err != nil {
		return errors.Wrap(err, "failed to bind eager loaded slice SubscriptionsState")
	}

	if err = results.Close(); err != nil {
		return errors.Wrap(err, "failed to close results of eager load for subscriptions_state")
	}
	if err = results.Err(); err != nil {
		return errors.Wrap(err, "error occurred during iteration of eager loaded relations for subscriptions_state")
	}

	if len(subscriptionRequestAfterSelectHooks) != 0 {
		for _, obj := range resultSlice {
			if err := obj.doAfterSelectHooks(ctx, e); err != nil {
				return err
			}
		}
	}

	if len(resultSlice) == 0 {
		return nil
	}

	if singular {
		foreign := resultSlice[0]
		object.R.Subscription = foreign
		if foreign.R == nil {
			foreign.R = &subscriptionsStateR{}
		}
		foreign.R.SubscriptionSubscriptionRequests = append(foreign.R.SubscriptionSubscriptionRequests, object)
		return nil
	}

	for _, local := range slice {
		for _, foreign := range resultSlice {
			if queries.Equal(local.SubscriptionID, foreign.SubscriptionID) {
				local.R.Subscription = foreign
				if foreign.R == nil {
					foreign.R = &subscriptionsStateR{}
				}
				foreign.R.SubscriptionSubscriptionRequests = append(foreign.R.SubscriptionSubscriptionRequests, local)
				break
			}
		}
	}

	return nil
}

// LoadRequestMonthlyRefreshSubscriptionsInfo allows an eager lookup of values, cached into the
// loaded structs of the objects. This is for a 1-1 relationship.
func (subscriptionRequestL) LoadRequestMonthlyRefreshSubscriptionsInfo(ctx context.Context, e boil.ContextExecutor, singular bool, maybeSubscriptionRequest interface{}, mods queries.Applicator) error {
	var slice []*SubscriptionRequest
	var object *SubscriptionRequest

	if singular {
		object = maybeSubscriptionRequest.(*SubscriptionRequest)
	} else {
		slice = *maybeSubscriptionRequest.(*[]*SubscriptionRequest)
	}

	args := make([]interface{}, 0, 1)
	if singular {
		if object.R == nil {
			object.R = &subscriptionRequestR{}
		}
		args = append(args, object.RequestID)
	} else {
	Outer:
		for _, obj := range slice {
			if obj.R == nil {
				obj.R = &subscriptionRequestR{}
			}

			for _, a := range args {
				if a == obj.RequestID {
					continue Outer
				}
			}

			args = append(args, obj.RequestID)
		}
	}

	if len(args) == 0 {
		return nil
	}

	query := NewQuery(
		qm.From(`telematics.monthly_refresh_subscriptions_info`),
		qm.WhereIn(`telematics.monthly_refresh_subscriptions_info.request_id in ?`, args...),
	)
	if mods != nil {
		mods.Apply(query)
	}

	results, err := query.QueryContext(ctx, e)
	if err != nil {
		return errors.Wrap(err, "failed to eager load MonthlyRefreshSubscriptionsInfo")
	}

	var resultSlice []*MonthlyRefreshSubscriptionsInfo
	if err = queries.Bind(results, &resultSlice); err != nil {
		return errors.Wrap(err, "failed to bind eager loaded slice MonthlyRefreshSubscriptionsInfo")
	}

	if err = results.Close(); err != nil {
		return errors.Wrap(err, "failed to close results of eager load for monthly_refresh_subscriptions_info")
	}
	if err = results.Err(); err != nil {
		return errors.Wrap(err, "error occurred during iteration of eager loaded relations for monthly_refresh_subscriptions_info")
	}

	if len(subscriptionRequestAfterSelectHooks) != 0 {
		for _, obj := range resultSlice {
			if err := obj.doAfterSelectHooks(ctx, e); err != nil {
				return err
			}
		}
	}

	if len(resultSlice) == 0 {
		return nil
	}

	if singular {
		foreign := resultSlice[0]
		object.R.RequestMonthlyRefreshSubscriptionsInfo = foreign
		if foreign.R == nil {
			foreign.R = &monthlyRefreshSubscriptionsInfoR{}
		}
		foreign.R.Request = object
	}

	for _, local := range slice {
		for _, foreign := range resultSlice {
			if local.RequestID == foreign.RequestID {
				local.R.RequestMonthlyRefreshSubscriptionsInfo = foreign
				if foreign.R == nil {
					foreign.R = &monthlyRefreshSubscriptionsInfoR{}
				}
				foreign.R.Request = local
				break
			}
		}
	}

	return nil
}

// SetSubscription of the subscriptionRequest to the related item.
// Sets o.R.Subscription to related.
// Adds o to related.R.SubscriptionSubscriptionRequests.
func (o *SubscriptionRequest) SetSubscription(ctx context.Context, exec boil.ContextExecutor, insert bool, related *SubscriptionsState) error {
	var err error
	if insert {
		if err = related.Insert(ctx, exec, boil.Infer()); err != nil {
			return errors.Wrap(err, "failed to insert into foreign table")
		}
	}

	updateQuery := fmt.Sprintf(
		"UPDATE \"telematics\".\"subscription_requests\" SET %s WHERE %s",
		strmangle.SetParamNames("\"", "\"", 1, []string{"subscription_id"}),
		strmangle.WhereClause("\"", "\"", 2, subscriptionRequestPrimaryKeyColumns),
	)
	values := []interface{}{related.SubscriptionID, o.RequestID}

	if boil.IsDebug(ctx) {
		writer := boil.DebugWriterFrom(ctx)
		fmt.Fprintln(writer, updateQuery)
		fmt.Fprintln(writer, values)
	}
	if _, err = exec.ExecContext(ctx, updateQuery, values...); err != nil {
		return errors.Wrap(err, "failed to update local table")
	}

	queries.Assign(&o.SubscriptionID, related.SubscriptionID)
	if o.R == nil {
		o.R = &subscriptionRequestR{
			Subscription: related,
		}
	} else {
		o.R.Subscription = related
	}

	if related.R == nil {
		related.R = &subscriptionsStateR{
			SubscriptionSubscriptionRequests: SubscriptionRequestSlice{o},
		}
	} else {
		related.R.SubscriptionSubscriptionRequests = append(related.R.SubscriptionSubscriptionRequests, o)
	}

	return nil
}

// RemoveSubscription relationship.
// Sets o.R.Subscription to nil.
// Removes o from all passed in related items' relationships struct.
func (o *SubscriptionRequest) RemoveSubscription(ctx context.Context, exec boil.ContextExecutor, related *SubscriptionsState) error {
	var err error

	queries.SetScanner(&o.SubscriptionID, nil)
	if _, err = o.Update(ctx, exec, boil.Whitelist("subscription_id")); err != nil {
		return errors.Wrap(err, "failed to update local table")
	}

	if o.R != nil {
		o.R.Subscription = nil
	}
	if related == nil || related.R == nil {
		return nil
	}

	for i, ri := range related.R.SubscriptionSubscriptionRequests {
		if queries.Equal(o.SubscriptionID, ri.SubscriptionID) {
			continue
		}

		ln := len(related.R.SubscriptionSubscriptionRequests)
		if ln > 1 && i < ln-1 {
			related.R.SubscriptionSubscriptionRequests[i] = related.R.SubscriptionSubscriptionRequests[ln-1]
		}
		related.R.SubscriptionSubscriptionRequests = related.R.SubscriptionSubscriptionRequests[:ln-1]
		break
	}
	return nil
}

// SetRequestMonthlyRefreshSubscriptionsInfo of the subscriptionRequest to the related item.
// Sets o.R.RequestMonthlyRefreshSubscriptionsInfo to related.
// Adds o to related.R.Request.
func (o *SubscriptionRequest) SetRequestMonthlyRefreshSubscriptionsInfo(ctx context.Context, exec boil.ContextExecutor, insert bool, related *MonthlyRefreshSubscriptionsInfo) error {
	var err error

	if insert {
		related.RequestID = o.RequestID

		if err = related.Insert(ctx, exec, boil.Infer()); err != nil {
			return errors.Wrap(err, "failed to insert into foreign table")
		}
	} else {
		updateQuery := fmt.Sprintf(
			"UPDATE \"telematics\".\"monthly_refresh_subscriptions_info\" SET %s WHERE %s",
			strmangle.SetParamNames("\"", "\"", 1, []string{"request_id"}),
			strmangle.WhereClause("\"", "\"", 2, monthlyRefreshSubscriptionsInfoPrimaryKeyColumns),
		)
		values := []interface{}{o.RequestID, related.RequestID}

		if boil.IsDebug(ctx) {
			writer := boil.DebugWriterFrom(ctx)
			fmt.Fprintln(writer, updateQuery)
			fmt.Fprintln(writer, values)
		}
		if _, err = exec.ExecContext(ctx, updateQuery, values...); err != nil {
			return errors.Wrap(err, "failed to update foreign table")
		}

		related.RequestID = o.RequestID
	}

	if o.R == nil {
		o.R = &subscriptionRequestR{
			RequestMonthlyRefreshSubscriptionsInfo: related,
		}
	} else {
		o.R.RequestMonthlyRefreshSubscriptionsInfo = related
	}

	if related.R == nil {
		related.R = &monthlyRefreshSubscriptionsInfoR{
			Request: o,
		}
	} else {
		related.R.Request = o
	}
	return nil
}

// SubscriptionRequests retrieves all the records using an executor.
func SubscriptionRequests(mods ...qm.QueryMod) subscriptionRequestQuery {
	mods = append(mods, qm.From("\"telematics\".\"subscription_requests\""))
	q := NewQuery(mods...)
	if len(queries.GetSelect(q)) == 0 {
		queries.SetSelect(q, []string{"\"telematics\".\"subscription_requests\".*"})
	}

	return subscriptionRequestQuery{q}
}

// FindSubscriptionRequest retrieves a single record by ID with an executor.
// If selectCols is empty Find will return all columns.
func FindSubscriptionRequest(ctx context.Context, exec boil.ContextExecutor, requestID string, selectCols ...string) (*SubscriptionRequest, error) {
	subscriptionRequestObj := &SubscriptionRequest{}

	sel := "*"
	if len(selectCols) > 0 {
		sel = strings.Join(strmangle.IdentQuoteSlice(dialect.LQ, dialect.RQ, selectCols), ",")
	}
	query := fmt.Sprintf(
		"select %s from \"telematics\".\"subscription_requests\" where \"request_id\"=$1", sel,
	)

	q := queries.Raw(query, requestID)

	err := q.Bind(ctx, exec, subscriptionRequestObj)
	if err != nil {
		if errors.Is(err, sql.ErrNoRows) {
			return nil, sql.ErrNoRows
		}
		return nil, errors.Wrap(err, "telematics: unable to select from subscription_requests")
	}

	if err = subscriptionRequestObj.doAfterSelectHooks(ctx, exec); err != nil {
		return subscriptionRequestObj, err
	}

	return subscriptionRequestObj, nil
}

// Insert a single record using an executor.
// See boil.Columns.InsertColumnSet documentation to understand column list inference for inserts.
func (o *SubscriptionRequest) Insert(ctx context.Context, exec boil.ContextExecutor, columns boil.Columns) error {
	if o == nil {
		return errors.New("telematics: no subscription_requests provided for insertion")
	}

	var err error
	if !boil.TimestampsAreSkipped(ctx) {
		currTime := time.Now().In(boil.GetLocation())

		if o.CreatedAt.IsZero() {
			o.CreatedAt = currTime
		}
	}

	if err := o.doBeforeInsertHooks(ctx, exec); err != nil {
		return err
	}

	nzDefaults := queries.NonZeroDefaultSet(subscriptionRequestColumnsWithDefault, o)

	key := makeCacheKey(columns, nzDefaults)
	subscriptionRequestInsertCacheMut.RLock()
	cache, cached := subscriptionRequestInsertCache[key]
	subscriptionRequestInsertCacheMut.RUnlock()

	if !cached {
		wl, returnColumns := columns.InsertColumnSet(
			subscriptionRequestAllColumns,
			subscriptionRequestColumnsWithDefault,
			subscriptionRequestColumnsWithoutDefault,
			nzDefaults,
		)

		cache.valueMapping, err = queries.BindMapping(subscriptionRequestType, subscriptionRequestMapping, wl)
		if err != nil {
			return err
		}
		cache.retMapping, err = queries.BindMapping(subscriptionRequestType, subscriptionRequestMapping, returnColumns)
		if err != nil {
			return err
		}
		if len(wl) != 0 {
			cache.query = fmt.Sprintf("INSERT INTO \"telematics\".\"subscription_requests\" (\"%s\") %%sVALUES (%s)%%s", strings.Join(wl, "\",\""), strmangle.Placeholders(dialect.UseIndexPlaceholders, len(wl), 1, 1))
		} else {
			cache.query = "INSERT INTO \"telematics\".\"subscription_requests\" %sDEFAULT VALUES%s"
		}

		var queryOutput, queryReturning string

		if len(cache.retMapping) != 0 {
			queryReturning = fmt.Sprintf(" RETURNING \"%s\"", strings.Join(returnColumns, "\",\""))
		}

		cache.query = fmt.Sprintf(cache.query, queryOutput, queryReturning)
	}

	value := reflect.Indirect(reflect.ValueOf(o))
	vals := queries.ValuesFromMapping(value, cache.valueMapping)

	if boil.IsDebug(ctx) {
		writer := boil.DebugWriterFrom(ctx)
		fmt.Fprintln(writer, cache.query)
		fmt.Fprintln(writer, vals)
	}

	if len(cache.retMapping) != 0 {
		err = exec.QueryRowContext(ctx, cache.query, vals...).Scan(queries.PtrsFromMapping(value, cache.retMapping)...)
	} else {
		_, err = exec.ExecContext(ctx, cache.query, vals...)
	}

	if err != nil {
		return errors.Wrap(err, "telematics: unable to insert into subscription_requests")
	}

	if !cached {
		subscriptionRequestInsertCacheMut.Lock()
		subscriptionRequestInsertCache[key] = cache
		subscriptionRequestInsertCacheMut.Unlock()
	}

	return o.doAfterInsertHooks(ctx, exec)
}

// Update uses an executor to update the SubscriptionRequest.
// See boil.Columns.UpdateColumnSet documentation to understand column list inference for updates.
// Update does not automatically update the record in case of default values. Use .Reload() to refresh the records.
func (o *SubscriptionRequest) Update(ctx context.Context, exec boil.ContextExecutor, columns boil.Columns) (int64, error) {
	var err error
	if err = o.doBeforeUpdateHooks(ctx, exec); err != nil {
		return 0, err
	}
	key := makeCacheKey(columns, nil)
	subscriptionRequestUpdateCacheMut.RLock()
	cache, cached := subscriptionRequestUpdateCache[key]
	subscriptionRequestUpdateCacheMut.RUnlock()

	if !cached {
		wl := columns.UpdateColumnSet(
			subscriptionRequestAllColumns,
			subscriptionRequestPrimaryKeyColumns,
		)

		if !columns.IsWhitelist() {
			wl = strmangle.SetComplement(wl, []string{"created_at"})
		}
		if len(wl) == 0 {
			return 0, errors.New("telematics: unable to update subscription_requests, could not build whitelist")
		}

		cache.query = fmt.Sprintf("UPDATE \"telematics\".\"subscription_requests\" SET %s WHERE %s",
			strmangle.SetParamNames("\"", "\"", 1, wl),
			strmangle.WhereClause("\"", "\"", len(wl)+1, subscriptionRequestPrimaryKeyColumns),
		)
		cache.valueMapping, err = queries.BindMapping(subscriptionRequestType, subscriptionRequestMapping, append(wl, subscriptionRequestPrimaryKeyColumns...))
		if err != nil {
			return 0, err
		}
	}

	values := queries.ValuesFromMapping(reflect.Indirect(reflect.ValueOf(o)), cache.valueMapping)

	if boil.IsDebug(ctx) {
		writer := boil.DebugWriterFrom(ctx)
		fmt.Fprintln(writer, cache.query)
		fmt.Fprintln(writer, values)
	}
	var result sql.Result
	result, err = exec.ExecContext(ctx, cache.query, values...)
	if err != nil {
		return 0, errors.Wrap(err, "telematics: unable to update subscription_requests row")
	}

	rowsAff, err := result.RowsAffected()
	if err != nil {
		return 0, errors.Wrap(err, "telematics: failed to get rows affected by update for subscription_requests")
	}

	if !cached {
		subscriptionRequestUpdateCacheMut.Lock()
		subscriptionRequestUpdateCache[key] = cache
		subscriptionRequestUpdateCacheMut.Unlock()
	}

	return rowsAff, o.doAfterUpdateHooks(ctx, exec)
}

// UpdateAll updates all rows with the specified column values.
func (q subscriptionRequestQuery) UpdateAll(ctx context.Context, exec boil.ContextExecutor, cols M) (int64, error) {
	queries.SetUpdate(q.Query, cols)

	result, err := q.Query.ExecContext(ctx, exec)
	if err != nil {
		return 0, errors.Wrap(err, "telematics: unable to update all for subscription_requests")
	}

	rowsAff, err := result.RowsAffected()
	if err != nil {
		return 0, errors.Wrap(err, "telematics: unable to retrieve rows affected for subscription_requests")
	}

	return rowsAff, nil
}

// UpdateAll updates all rows with the specified column values, using an executor.
func (o SubscriptionRequestSlice) UpdateAll(ctx context.Context, exec boil.ContextExecutor, cols M) (int64, error) {
	ln := int64(len(o))
	if ln == 0 {
		return 0, nil
	}

	if len(cols) == 0 {
		return 0, errors.New("telematics: update all requires at least one column argument")
	}

	colNames := make([]string, len(cols))
	args := make([]interface{}, len(cols))

	i := 0
	for name, value := range cols {
		colNames[i] = name
		args[i] = value
		i++
	}

	// Append all of the primary key values for each column
	for _, obj := range o {
		pkeyArgs := queries.ValuesFromMapping(reflect.Indirect(reflect.ValueOf(obj)), subscriptionRequestPrimaryKeyMapping)
		args = append(args, pkeyArgs...)
	}

	sql := fmt.Sprintf("UPDATE \"telematics\".\"subscription_requests\" SET %s WHERE %s",
		strmangle.SetParamNames("\"", "\"", 1, colNames),
		strmangle.WhereClauseRepeated(string(dialect.LQ), string(dialect.RQ), len(colNames)+1, subscriptionRequestPrimaryKeyColumns, len(o)))

	if boil.IsDebug(ctx) {
		writer := boil.DebugWriterFrom(ctx)
		fmt.Fprintln(writer, sql)
		fmt.Fprintln(writer, args...)
	}
	result, err := exec.ExecContext(ctx, sql, args...)
	if err != nil {
		return 0, errors.Wrap(err, "telematics: unable to update all in subscriptionRequest slice")
	}

	rowsAff, err := result.RowsAffected()
	if err != nil {
		return 0, errors.Wrap(err, "telematics: unable to retrieve rows affected all in update all subscriptionRequest")
	}
	return rowsAff, nil
}

// Upsert attempts an insert using an executor, and does an update or ignore on conflict.
// See boil.Columns documentation for how to properly use updateColumns and insertColumns.
func (o *SubscriptionRequest) Upsert(ctx context.Context, exec boil.ContextExecutor, updateOnConflict bool, conflictColumns []string, updateColumns, insertColumns boil.Columns) error {
	if o == nil {
		return errors.New("telematics: no subscription_requests provided for upsert")
	}
	if !boil.TimestampsAreSkipped(ctx) {
		currTime := time.Now().In(boil.GetLocation())

		if o.CreatedAt.IsZero() {
			o.CreatedAt = currTime
		}
	}

	if err := o.doBeforeUpsertHooks(ctx, exec); err != nil {
		return err
	}

	nzDefaults := queries.NonZeroDefaultSet(subscriptionRequestColumnsWithDefault, o)

	// Build cache key in-line uglily - mysql vs psql problems
	buf := strmangle.GetBuffer()
	if updateOnConflict {
		buf.WriteByte('t')
	} else {
		buf.WriteByte('f')
	}
	buf.WriteByte('.')
	for _, c := range conflictColumns {
		buf.WriteString(c)
	}
	buf.WriteByte('.')
	buf.WriteString(strconv.Itoa(updateColumns.Kind))
	for _, c := range updateColumns.Cols {
		buf.WriteString(c)
	}
	buf.WriteByte('.')
	buf.WriteString(strconv.Itoa(insertColumns.Kind))
	for _, c := range insertColumns.Cols {
		buf.WriteString(c)
	}
	buf.WriteByte('.')
	for _, c := range nzDefaults {
		buf.WriteString(c)
	}
	key := buf.String()
	strmangle.PutBuffer(buf)

	subscriptionRequestUpsertCacheMut.RLock()
	cache, cached := subscriptionRequestUpsertCache[key]
	subscriptionRequestUpsertCacheMut.RUnlock()

	var err error

	if !cached {
		insert, ret := insertColumns.InsertColumnSet(
			subscriptionRequestAllColumns,
			subscriptionRequestColumnsWithDefault,
			subscriptionRequestColumnsWithoutDefault,
			nzDefaults,
		)

		update := updateColumns.UpdateColumnSet(
			subscriptionRequestAllColumns,
			subscriptionRequestPrimaryKeyColumns,
		)

		if updateOnConflict && len(update) == 0 {
			return errors.New("telematics: unable to upsert subscription_requests, could not build update column list")
		}

		conflict := conflictColumns
		if len(conflict) == 0 {
			conflict = make([]string, len(subscriptionRequestPrimaryKeyColumns))
			copy(conflict, subscriptionRequestPrimaryKeyColumns)
		}
		cache.query = buildUpsertQueryPostgres(dialect, "\"telematics\".\"subscription_requests\"", updateOnConflict, ret, update, conflict, insert)

		cache.valueMapping, err = queries.BindMapping(subscriptionRequestType, subscriptionRequestMapping, insert)
		if err != nil {
			return err
		}
		if len(ret) != 0 {
			cache.retMapping, err = queries.BindMapping(subscriptionRequestType, subscriptionRequestMapping, ret)
			if err != nil {
				return err
			}
		}
	}

	value := reflect.Indirect(reflect.ValueOf(o))
	vals := queries.ValuesFromMapping(value, cache.valueMapping)
	var returns []interface{}
	if len(cache.retMapping) != 0 {
		returns = queries.PtrsFromMapping(value, cache.retMapping)
	}

	if boil.IsDebug(ctx) {
		writer := boil.DebugWriterFrom(ctx)
		fmt.Fprintln(writer, cache.query)
		fmt.Fprintln(writer, vals)
	}
	if len(cache.retMapping) != 0 {
		err = exec.QueryRowContext(ctx, cache.query, vals...).Scan(returns...)
		if errors.Is(err, sql.ErrNoRows) {
			err = nil // Postgres doesn't return anything when there's no update
		}
	} else {
		_, err = exec.ExecContext(ctx, cache.query, vals...)
	}
	if err != nil {
		return errors.Wrap(err, "telematics: unable to upsert subscription_requests")
	}

	if !cached {
		subscriptionRequestUpsertCacheMut.Lock()
		subscriptionRequestUpsertCache[key] = cache
		subscriptionRequestUpsertCacheMut.Unlock()
	}

	return o.doAfterUpsertHooks(ctx, exec)
}

// Delete deletes a single SubscriptionRequest record with an executor.
// Delete will match against the primary key column to find the record to delete.
func (o *SubscriptionRequest) Delete(ctx context.Context, exec boil.ContextExecutor) (int64, error) {
	if o == nil {
		return 0, errors.New("telematics: no SubscriptionRequest provided for delete")
	}

	if err := o.doBeforeDeleteHooks(ctx, exec); err != nil {
		return 0, err
	}

	args := queries.ValuesFromMapping(reflect.Indirect(reflect.ValueOf(o)), subscriptionRequestPrimaryKeyMapping)
	sql := "DELETE FROM \"telematics\".\"subscription_requests\" WHERE \"request_id\"=$1"

	if boil.IsDebug(ctx) {
		writer := boil.DebugWriterFrom(ctx)
		fmt.Fprintln(writer, sql)
		fmt.Fprintln(writer, args...)
	}
	result, err := exec.ExecContext(ctx, sql, args...)
	if err != nil {
		return 0, errors.Wrap(err, "telematics: unable to delete from subscription_requests")
	}

	rowsAff, err := result.RowsAffected()
	if err != nil {
		return 0, errors.Wrap(err, "telematics: failed to get rows affected by delete for subscription_requests")
	}

	if err := o.doAfterDeleteHooks(ctx, exec); err != nil {
		return 0, err
	}

	return rowsAff, nil
}

// DeleteAll deletes all matching rows.
func (q subscriptionRequestQuery) DeleteAll(ctx context.Context, exec boil.ContextExecutor) (int64, error) {
	if q.Query == nil {
		return 0, errors.New("telematics: no subscriptionRequestQuery provided for delete all")
	}

	queries.SetDelete(q.Query)

	result, err := q.Query.ExecContext(ctx, exec)
	if err != nil {
		return 0, errors.Wrap(err, "telematics: unable to delete all from subscription_requests")
	}

	rowsAff, err := result.RowsAffected()
	if err != nil {
		return 0, errors.Wrap(err, "telematics: failed to get rows affected by deleteall for subscription_requests")
	}

	return rowsAff, nil
}

// DeleteAll deletes all rows in the slice, using an executor.
func (o SubscriptionRequestSlice) DeleteAll(ctx context.Context, exec boil.ContextExecutor) (int64, error) {
	if len(o) == 0 {
		return 0, nil
	}

	if len(subscriptionRequestBeforeDeleteHooks) != 0 {
		for _, obj := range o {
			if err := obj.doBeforeDeleteHooks(ctx, exec); err != nil {
				return 0, err
			}
		}
	}

	var args []interface{}
	for _, obj := range o {
		pkeyArgs := queries.ValuesFromMapping(reflect.Indirect(reflect.ValueOf(obj)), subscriptionRequestPrimaryKeyMapping)
		args = append(args, pkeyArgs...)
	}

	sql := "DELETE FROM \"telematics\".\"subscription_requests\" WHERE " +
		strmangle.WhereClauseRepeated(string(dialect.LQ), string(dialect.RQ), 1, subscriptionRequestPrimaryKeyColumns, len(o))

	if boil.IsDebug(ctx) {
		writer := boil.DebugWriterFrom(ctx)
		fmt.Fprintln(writer, sql)
		fmt.Fprintln(writer, args)
	}
	result, err := exec.ExecContext(ctx, sql, args...)
	if err != nil {
		return 0, errors.Wrap(err, "telematics: unable to delete all from subscriptionRequest slice")
	}

	rowsAff, err := result.RowsAffected()
	if err != nil {
		return 0, errors.Wrap(err, "telematics: failed to get rows affected by deleteall for subscription_requests")
	}

	if len(subscriptionRequestAfterDeleteHooks) != 0 {
		for _, obj := range o {
			if err := obj.doAfterDeleteHooks(ctx, exec); err != nil {
				return 0, err
			}
		}
	}

	return rowsAff, nil
}

// Reload refetches the object from the database
// using the primary keys with an executor.
func (o *SubscriptionRequest) Reload(ctx context.Context, exec boil.ContextExecutor) error {
	ret, err := FindSubscriptionRequest(ctx, exec, o.RequestID)
	if err != nil {
		return err
	}

	*o = *ret
	return nil
}

// ReloadAll refetches every row with matching primary key column values
// and overwrites the original object slice with the newly updated slice.
func (o *SubscriptionRequestSlice) ReloadAll(ctx context.Context, exec boil.ContextExecutor) error {
	if o == nil || len(*o) == 0 {
		return nil
	}

	slice := SubscriptionRequestSlice{}
	var args []interface{}
	for _, obj := range *o {
		pkeyArgs := queries.ValuesFromMapping(reflect.Indirect(reflect.ValueOf(obj)), subscriptionRequestPrimaryKeyMapping)
		args = append(args, pkeyArgs...)
	}

	sql := "SELECT \"telematics\".\"subscription_requests\".* FROM \"telematics\".\"subscription_requests\" WHERE " +
		strmangle.WhereClauseRepeated(string(dialect.LQ), string(dialect.RQ), 1, subscriptionRequestPrimaryKeyColumns, len(*o))

	q := queries.Raw(sql, args...)

	err := q.Bind(ctx, exec, &slice)
	if err != nil {
		return errors.Wrap(err, "telematics: unable to reload all in SubscriptionRequestSlice")
	}

	*o = slice

	return nil
}

// SubscriptionRequestExists checks if the SubscriptionRequest row exists.
func SubscriptionRequestExists(ctx context.Context, exec boil.ContextExecutor, requestID string) (bool, error) {
	var exists bool
	sql := "select exists(select 1 from \"telematics\".\"subscription_requests\" where \"request_id\"=$1 limit 1)"

	if boil.IsDebug(ctx) {
		writer := boil.DebugWriterFrom(ctx)
		fmt.Fprintln(writer, sql)
		fmt.Fprintln(writer, requestID)
	}
	row := exec.QueryRowContext(ctx, sql, requestID)

	err := row.Scan(&exists)
	if err != nil {
		return false, errors.Wrap(err, "telematics: unable to check if subscription_requests exists")
	}

	return exists, nil
}
