// Code generated by SQLBoiler (https://github.com/volatiletech/sqlboiler). DO NOT EDIT.
// This file is meant to be re-generated in place and/or deleted at any time.

package tskv_example

import (
	"context"
	"database/sql"
	"fmt"
	"reflect"
	"strconv"
	"strings"
	"sync"
	"time"

	"github.com/friendsofgo/errors"
	"github.com/volatiletech/sqlboiler/v4/boil"
	"github.com/volatiletech/sqlboiler/v4/queries"
	"github.com/volatiletech/sqlboiler/v4/queries/qm"
	"github.com/volatiletech/sqlboiler/v4/queries/qmhelper"
	"github.com/volatiletech/sqlboiler/v4/types"
	"github.com/volatiletech/strmangle"
)

// CompressedRel is an object representing the database table.
type CompressedRel struct {
	Rowid       string            `boil:"rowid" json:"rowid" toml:"rowid" yaml:"rowid"`
	Key         string            `boil:"key" json:"key" toml:"key" yaml:"key"`
	Val         int               `boil:"val" json:"val" toml:"val" yaml:"val"`
	TimeMin     time.Time         `boil:"time_min" json:"time_min" toml:"time_min" yaml:"time_min"`
	TimeMax     time.Time         `boil:"time_max" json:"time_max" toml:"time_max" yaml:"time_max"`
	TimeArr     types.StringArray `boil:"time_arr" json:"time_arr" toml:"time_arr" yaml:"time_arr"`
	ObserverIds types.StringArray `boil:"observer_ids" json:"observer_ids,omitempty" toml:"observer_ids" yaml:"observer_ids,omitempty"`

	R *compressedRelR `boil:"" json:"" toml:"" yaml:""`
	L compressedRelL  `boil:"-" json:"-" toml:"-" yaml:"-"`
}

var CompressedRelColumns = struct {
	Rowid       string
	Key         string
	Val         string
	TimeMin     string
	TimeMax     string
	TimeArr     string
	ObserverIds string
}{
	Rowid:       "rowid",
	Key:         "key",
	Val:         "val",
	TimeMin:     "time_min",
	TimeMax:     "time_max",
	TimeArr:     "time_arr",
	ObserverIds: "observer_ids",
}

var CompressedRelTableColumns = struct {
	Rowid       string
	Key         string
	Val         string
	TimeMin     string
	TimeMax     string
	TimeArr     string
	ObserverIds string
}{
	Rowid:       "compressed_rel.rowid",
	Key:         "compressed_rel.key",
	Val:         "compressed_rel.val",
	TimeMin:     "compressed_rel.time_min",
	TimeMax:     "compressed_rel.time_max",
	TimeArr:     "compressed_rel.time_arr",
	ObserverIds: "compressed_rel.observer_ids",
}

// Generated where

type whereHelperstring struct{ field string }

func (w whereHelperstring) EQ(x string) qm.QueryMod  { return qmhelper.Where(w.field, qmhelper.EQ, x) }
func (w whereHelperstring) NEQ(x string) qm.QueryMod { return qmhelper.Where(w.field, qmhelper.NEQ, x) }
func (w whereHelperstring) LT(x string) qm.QueryMod  { return qmhelper.Where(w.field, qmhelper.LT, x) }
func (w whereHelperstring) LTE(x string) qm.QueryMod { return qmhelper.Where(w.field, qmhelper.LTE, x) }
func (w whereHelperstring) GT(x string) qm.QueryMod  { return qmhelper.Where(w.field, qmhelper.GT, x) }
func (w whereHelperstring) GTE(x string) qm.QueryMod { return qmhelper.Where(w.field, qmhelper.GTE, x) }
func (w whereHelperstring) IN(slice []string) qm.QueryMod {
	values := make([]interface{}, 0, len(slice))
	for _, value := range slice {
		values = append(values, value)
	}
	return qm.WhereIn(fmt.Sprintf("%s IN ?", w.field), values...)
}
func (w whereHelperstring) NIN(slice []string) qm.QueryMod {
	values := make([]interface{}, 0, len(slice))
	for _, value := range slice {
		values = append(values, value)
	}
	return qm.WhereNotIn(fmt.Sprintf("%s NOT IN ?", w.field), values...)
}

type whereHelperint struct{ field string }

func (w whereHelperint) EQ(x int) qm.QueryMod  { return qmhelper.Where(w.field, qmhelper.EQ, x) }
func (w whereHelperint) NEQ(x int) qm.QueryMod { return qmhelper.Where(w.field, qmhelper.NEQ, x) }
func (w whereHelperint) LT(x int) qm.QueryMod  { return qmhelper.Where(w.field, qmhelper.LT, x) }
func (w whereHelperint) LTE(x int) qm.QueryMod { return qmhelper.Where(w.field, qmhelper.LTE, x) }
func (w whereHelperint) GT(x int) qm.QueryMod  { return qmhelper.Where(w.field, qmhelper.GT, x) }
func (w whereHelperint) GTE(x int) qm.QueryMod { return qmhelper.Where(w.field, qmhelper.GTE, x) }
func (w whereHelperint) IN(slice []int) qm.QueryMod {
	values := make([]interface{}, 0, len(slice))
	for _, value := range slice {
		values = append(values, value)
	}
	return qm.WhereIn(fmt.Sprintf("%s IN ?", w.field), values...)
}
func (w whereHelperint) NIN(slice []int) qm.QueryMod {
	values := make([]interface{}, 0, len(slice))
	for _, value := range slice {
		values = append(values, value)
	}
	return qm.WhereNotIn(fmt.Sprintf("%s NOT IN ?", w.field), values...)
}

type whereHelpertime_Time struct{ field string }

func (w whereHelpertime_Time) EQ(x time.Time) qm.QueryMod {
	return qmhelper.Where(w.field, qmhelper.EQ, x)
}
func (w whereHelpertime_Time) NEQ(x time.Time) qm.QueryMod {
	return qmhelper.Where(w.field, qmhelper.NEQ, x)
}
func (w whereHelpertime_Time) LT(x time.Time) qm.QueryMod {
	return qmhelper.Where(w.field, qmhelper.LT, x)
}
func (w whereHelpertime_Time) LTE(x time.Time) qm.QueryMod {
	return qmhelper.Where(w.field, qmhelper.LTE, x)
}
func (w whereHelpertime_Time) GT(x time.Time) qm.QueryMod {
	return qmhelper.Where(w.field, qmhelper.GT, x)
}
func (w whereHelpertime_Time) GTE(x time.Time) qm.QueryMod {
	return qmhelper.Where(w.field, qmhelper.GTE, x)
}

type whereHelpertypes_StringArray struct{ field string }

func (w whereHelpertypes_StringArray) EQ(x types.StringArray) qm.QueryMod {
	return qmhelper.Where(w.field, qmhelper.EQ, x)
}
func (w whereHelpertypes_StringArray) NEQ(x types.StringArray) qm.QueryMod {
	return qmhelper.Where(w.field, qmhelper.NEQ, x)
}
func (w whereHelpertypes_StringArray) LT(x types.StringArray) qm.QueryMod {
	return qmhelper.Where(w.field, qmhelper.LT, x)
}
func (w whereHelpertypes_StringArray) LTE(x types.StringArray) qm.QueryMod {
	return qmhelper.Where(w.field, qmhelper.LTE, x)
}
func (w whereHelpertypes_StringArray) GT(x types.StringArray) qm.QueryMod {
	return qmhelper.Where(w.field, qmhelper.GT, x)
}
func (w whereHelpertypes_StringArray) GTE(x types.StringArray) qm.QueryMod {
	return qmhelper.Where(w.field, qmhelper.GTE, x)
}

func (w whereHelpertypes_StringArray) IsNull() qm.QueryMod { return qmhelper.WhereIsNull(w.field) }
func (w whereHelpertypes_StringArray) IsNotNull() qm.QueryMod {
	return qmhelper.WhereIsNotNull(w.field)
}

var CompressedRelWhere = struct {
	Rowid       whereHelperstring
	Key         whereHelperstring
	Val         whereHelperint
	TimeMin     whereHelpertime_Time
	TimeMax     whereHelpertime_Time
	TimeArr     whereHelpertypes_StringArray
	ObserverIds whereHelpertypes_StringArray
}{
	Rowid:       whereHelperstring{field: "\"tskv_example\".\"compressed_rel\".\"rowid\""},
	Key:         whereHelperstring{field: "\"tskv_example\".\"compressed_rel\".\"key\""},
	Val:         whereHelperint{field: "\"tskv_example\".\"compressed_rel\".\"val\""},
	TimeMin:     whereHelpertime_Time{field: "\"tskv_example\".\"compressed_rel\".\"time_min\""},
	TimeMax:     whereHelpertime_Time{field: "\"tskv_example\".\"compressed_rel\".\"time_max\""},
	TimeArr:     whereHelpertypes_StringArray{field: "\"tskv_example\".\"compressed_rel\".\"time_arr\""},
	ObserverIds: whereHelpertypes_StringArray{field: "\"tskv_example\".\"compressed_rel\".\"observer_ids\""},
}

// CompressedRelRels is where relationship names are stored.
var CompressedRelRels = struct {
}{}

// compressedRelR is where relationships are stored.
type compressedRelR struct {
}

// NewStruct creates a new relationship struct
func (*compressedRelR) NewStruct() *compressedRelR {
	return &compressedRelR{}
}

// compressedRelL is where Load methods for each relationship are stored.
type compressedRelL struct{}

var (
	compressedRelAllColumns            = []string{"rowid", "key", "val", "time_min", "time_max", "time_arr", "observer_ids"}
	compressedRelColumnsWithoutDefault = []string{"rowid", "key", "val", "time_min", "time_max", "time_arr"}
	compressedRelColumnsWithDefault    = []string{"observer_ids"}
	compressedRelPrimaryKeyColumns     = []string{"rowid"}
	compressedRelGeneratedColumns      = []string{}
)

type (
	// CompressedRelSlice is an alias for a slice of pointers to CompressedRel.
	// This should almost always be used instead of []CompressedRel.
	CompressedRelSlice []*CompressedRel
	// CompressedRelHook is the signature for custom CompressedRel hook methods
	CompressedRelHook func(context.Context, boil.ContextExecutor, *CompressedRel) error

	compressedRelQuery struct {
		*queries.Query
	}
)

// Cache for insert, update and upsert
var (
	compressedRelType                 = reflect.TypeOf(&CompressedRel{})
	compressedRelMapping              = queries.MakeStructMapping(compressedRelType)
	compressedRelPrimaryKeyMapping, _ = queries.BindMapping(compressedRelType, compressedRelMapping, compressedRelPrimaryKeyColumns)
	compressedRelInsertCacheMut       sync.RWMutex
	compressedRelInsertCache          = make(map[string]insertCache)
	compressedRelUpdateCacheMut       sync.RWMutex
	compressedRelUpdateCache          = make(map[string]updateCache)
	compressedRelUpsertCacheMut       sync.RWMutex
	compressedRelUpsertCache          = make(map[string]insertCache)
)

var (
	// Force time package dependency for automated UpdatedAt/CreatedAt.
	_ = time.Second
	// Force qmhelper dependency for where clause generation (which doesn't
	// always happen)
	_ = qmhelper.Where
)

var compressedRelAfterSelectHooks []CompressedRelHook

var compressedRelBeforeInsertHooks []CompressedRelHook
var compressedRelAfterInsertHooks []CompressedRelHook

var compressedRelBeforeUpdateHooks []CompressedRelHook
var compressedRelAfterUpdateHooks []CompressedRelHook

var compressedRelBeforeDeleteHooks []CompressedRelHook
var compressedRelAfterDeleteHooks []CompressedRelHook

var compressedRelBeforeUpsertHooks []CompressedRelHook
var compressedRelAfterUpsertHooks []CompressedRelHook

// doAfterSelectHooks executes all "after Select" hooks.
func (o *CompressedRel) doAfterSelectHooks(ctx context.Context, exec boil.ContextExecutor) (err error) {
	if boil.HooksAreSkipped(ctx) {
		return nil
	}

	for _, hook := range compressedRelAfterSelectHooks {
		if err := hook(ctx, exec, o); err != nil {
			return err
		}
	}

	return nil
}

// doBeforeInsertHooks executes all "before insert" hooks.
func (o *CompressedRel) doBeforeInsertHooks(ctx context.Context, exec boil.ContextExecutor) (err error) {
	if boil.HooksAreSkipped(ctx) {
		return nil
	}

	for _, hook := range compressedRelBeforeInsertHooks {
		if err := hook(ctx, exec, o); err != nil {
			return err
		}
	}

	return nil
}

// doAfterInsertHooks executes all "after Insert" hooks.
func (o *CompressedRel) doAfterInsertHooks(ctx context.Context, exec boil.ContextExecutor) (err error) {
	if boil.HooksAreSkipped(ctx) {
		return nil
	}

	for _, hook := range compressedRelAfterInsertHooks {
		if err := hook(ctx, exec, o); err != nil {
			return err
		}
	}

	return nil
}

// doBeforeUpdateHooks executes all "before Update" hooks.
func (o *CompressedRel) doBeforeUpdateHooks(ctx context.Context, exec boil.ContextExecutor) (err error) {
	if boil.HooksAreSkipped(ctx) {
		return nil
	}

	for _, hook := range compressedRelBeforeUpdateHooks {
		if err := hook(ctx, exec, o); err != nil {
			return err
		}
	}

	return nil
}

// doAfterUpdateHooks executes all "after Update" hooks.
func (o *CompressedRel) doAfterUpdateHooks(ctx context.Context, exec boil.ContextExecutor) (err error) {
	if boil.HooksAreSkipped(ctx) {
		return nil
	}

	for _, hook := range compressedRelAfterUpdateHooks {
		if err := hook(ctx, exec, o); err != nil {
			return err
		}
	}

	return nil
}

// doBeforeDeleteHooks executes all "before Delete" hooks.
func (o *CompressedRel) doBeforeDeleteHooks(ctx context.Context, exec boil.ContextExecutor) (err error) {
	if boil.HooksAreSkipped(ctx) {
		return nil
	}

	for _, hook := range compressedRelBeforeDeleteHooks {
		if err := hook(ctx, exec, o); err != nil {
			return err
		}
	}

	return nil
}

// doAfterDeleteHooks executes all "after Delete" hooks.
func (o *CompressedRel) doAfterDeleteHooks(ctx context.Context, exec boil.ContextExecutor) (err error) {
	if boil.HooksAreSkipped(ctx) {
		return nil
	}

	for _, hook := range compressedRelAfterDeleteHooks {
		if err := hook(ctx, exec, o); err != nil {
			return err
		}
	}

	return nil
}

// doBeforeUpsertHooks executes all "before Upsert" hooks.
func (o *CompressedRel) doBeforeUpsertHooks(ctx context.Context, exec boil.ContextExecutor) (err error) {
	if boil.HooksAreSkipped(ctx) {
		return nil
	}

	for _, hook := range compressedRelBeforeUpsertHooks {
		if err := hook(ctx, exec, o); err != nil {
			return err
		}
	}

	return nil
}

// doAfterUpsertHooks executes all "after Upsert" hooks.
func (o *CompressedRel) doAfterUpsertHooks(ctx context.Context, exec boil.ContextExecutor) (err error) {
	if boil.HooksAreSkipped(ctx) {
		return nil
	}

	for _, hook := range compressedRelAfterUpsertHooks {
		if err := hook(ctx, exec, o); err != nil {
			return err
		}
	}

	return nil
}

// AddCompressedRelHook registers your hook function for all future operations.
func AddCompressedRelHook(hookPoint boil.HookPoint, compressedRelHook CompressedRelHook) {
	switch hookPoint {
	case boil.AfterSelectHook:
		compressedRelAfterSelectHooks = append(compressedRelAfterSelectHooks, compressedRelHook)
	case boil.BeforeInsertHook:
		compressedRelBeforeInsertHooks = append(compressedRelBeforeInsertHooks, compressedRelHook)
	case boil.AfterInsertHook:
		compressedRelAfterInsertHooks = append(compressedRelAfterInsertHooks, compressedRelHook)
	case boil.BeforeUpdateHook:
		compressedRelBeforeUpdateHooks = append(compressedRelBeforeUpdateHooks, compressedRelHook)
	case boil.AfterUpdateHook:
		compressedRelAfterUpdateHooks = append(compressedRelAfterUpdateHooks, compressedRelHook)
	case boil.BeforeDeleteHook:
		compressedRelBeforeDeleteHooks = append(compressedRelBeforeDeleteHooks, compressedRelHook)
	case boil.AfterDeleteHook:
		compressedRelAfterDeleteHooks = append(compressedRelAfterDeleteHooks, compressedRelHook)
	case boil.BeforeUpsertHook:
		compressedRelBeforeUpsertHooks = append(compressedRelBeforeUpsertHooks, compressedRelHook)
	case boil.AfterUpsertHook:
		compressedRelAfterUpsertHooks = append(compressedRelAfterUpsertHooks, compressedRelHook)
	}
}

// One returns a single compressedRel record from the query.
func (q compressedRelQuery) One(ctx context.Context, exec boil.ContextExecutor) (*CompressedRel, error) {
	o := &CompressedRel{}

	queries.SetLimit(q.Query, 1)

	err := q.Bind(ctx, exec, o)
	if err != nil {
		if errors.Is(err, sql.ErrNoRows) {
			return nil, sql.ErrNoRows
		}
		return nil, errors.Wrap(err, "tskv_example: failed to execute a one query for compressed_rel")
	}

	if err := o.doAfterSelectHooks(ctx, exec); err != nil {
		return o, err
	}

	return o, nil
}

// All returns all CompressedRel records from the query.
func (q compressedRelQuery) All(ctx context.Context, exec boil.ContextExecutor) (CompressedRelSlice, error) {
	var o []*CompressedRel

	err := q.Bind(ctx, exec, &o)
	if err != nil {
		return nil, errors.Wrap(err, "tskv_example: failed to assign all query results to CompressedRel slice")
	}

	if len(compressedRelAfterSelectHooks) != 0 {
		for _, obj := range o {
			if err := obj.doAfterSelectHooks(ctx, exec); err != nil {
				return o, err
			}
		}
	}

	return o, nil
}

// Count returns the count of all CompressedRel records in the query.
func (q compressedRelQuery) Count(ctx context.Context, exec boil.ContextExecutor) (int64, error) {
	var count int64

	queries.SetSelect(q.Query, nil)
	queries.SetCount(q.Query)

	err := q.Query.QueryRowContext(ctx, exec).Scan(&count)
	if err != nil {
		return 0, errors.Wrap(err, "tskv_example: failed to count compressed_rel rows")
	}

	return count, nil
}

// Exists checks if the row exists in the table.
func (q compressedRelQuery) Exists(ctx context.Context, exec boil.ContextExecutor) (bool, error) {
	var count int64

	queries.SetSelect(q.Query, nil)
	queries.SetCount(q.Query)
	queries.SetLimit(q.Query, 1)

	err := q.Query.QueryRowContext(ctx, exec).Scan(&count)
	if err != nil {
		return false, errors.Wrap(err, "tskv_example: failed to check if compressed_rel exists")
	}

	return count > 0, nil
}

// CompressedRels retrieves all the records using an executor.
func CompressedRels(mods ...qm.QueryMod) compressedRelQuery {
	mods = append(mods, qm.From("\"tskv_example\".\"compressed_rel\""))
	q := NewQuery(mods...)
	if len(queries.GetSelect(q)) == 0 {
		queries.SetSelect(q, []string{"\"tskv_example\".\"compressed_rel\".*"})
	}

	return compressedRelQuery{q}
}

// FindCompressedRel retrieves a single record by ID with an executor.
// If selectCols is empty Find will return all columns.
func FindCompressedRel(ctx context.Context, exec boil.ContextExecutor, rowid string, selectCols ...string) (*CompressedRel, error) {
	compressedRelObj := &CompressedRel{}

	sel := "*"
	if len(selectCols) > 0 {
		sel = strings.Join(strmangle.IdentQuoteSlice(dialect.LQ, dialect.RQ, selectCols), ",")
	}
	query := fmt.Sprintf(
		"select %s from \"tskv_example\".\"compressed_rel\" where \"rowid\"=$1", sel,
	)

	q := queries.Raw(query, rowid)

	err := q.Bind(ctx, exec, compressedRelObj)
	if err != nil {
		if errors.Is(err, sql.ErrNoRows) {
			return nil, sql.ErrNoRows
		}
		return nil, errors.Wrap(err, "tskv_example: unable to select from compressed_rel")
	}

	if err = compressedRelObj.doAfterSelectHooks(ctx, exec); err != nil {
		return compressedRelObj, err
	}

	return compressedRelObj, nil
}

// Insert a single record using an executor.
// See boil.Columns.InsertColumnSet documentation to understand column list inference for inserts.
func (o *CompressedRel) Insert(ctx context.Context, exec boil.ContextExecutor, columns boil.Columns) error {
	if o == nil {
		return errors.New("tskv_example: no compressed_rel provided for insertion")
	}

	var err error

	if err := o.doBeforeInsertHooks(ctx, exec); err != nil {
		return err
	}

	nzDefaults := queries.NonZeroDefaultSet(compressedRelColumnsWithDefault, o)

	key := makeCacheKey(columns, nzDefaults)
	compressedRelInsertCacheMut.RLock()
	cache, cached := compressedRelInsertCache[key]
	compressedRelInsertCacheMut.RUnlock()

	if !cached {
		wl, returnColumns := columns.InsertColumnSet(
			compressedRelAllColumns,
			compressedRelColumnsWithDefault,
			compressedRelColumnsWithoutDefault,
			nzDefaults,
		)

		cache.valueMapping, err = queries.BindMapping(compressedRelType, compressedRelMapping, wl)
		if err != nil {
			return err
		}
		cache.retMapping, err = queries.BindMapping(compressedRelType, compressedRelMapping, returnColumns)
		if err != nil {
			return err
		}
		if len(wl) != 0 {
			cache.query = fmt.Sprintf("INSERT INTO \"tskv_example\".\"compressed_rel\" (\"%s\") %%sVALUES (%s)%%s", strings.Join(wl, "\",\""), strmangle.Placeholders(dialect.UseIndexPlaceholders, len(wl), 1, 1))
		} else {
			cache.query = "INSERT INTO \"tskv_example\".\"compressed_rel\" %sDEFAULT VALUES%s"
		}

		var queryOutput, queryReturning string

		if len(cache.retMapping) != 0 {
			queryReturning = fmt.Sprintf(" RETURNING \"%s\"", strings.Join(returnColumns, "\",\""))
		}

		cache.query = fmt.Sprintf(cache.query, queryOutput, queryReturning)
	}

	value := reflect.Indirect(reflect.ValueOf(o))
	vals := queries.ValuesFromMapping(value, cache.valueMapping)

	if boil.IsDebug(ctx) {
		writer := boil.DebugWriterFrom(ctx)
		fmt.Fprintln(writer, cache.query)
		fmt.Fprintln(writer, vals)
	}

	if len(cache.retMapping) != 0 {
		err = exec.QueryRowContext(ctx, cache.query, vals...).Scan(queries.PtrsFromMapping(value, cache.retMapping)...)
	} else {
		_, err = exec.ExecContext(ctx, cache.query, vals...)
	}

	if err != nil {
		return errors.Wrap(err, "tskv_example: unable to insert into compressed_rel")
	}

	if !cached {
		compressedRelInsertCacheMut.Lock()
		compressedRelInsertCache[key] = cache
		compressedRelInsertCacheMut.Unlock()
	}

	return o.doAfterInsertHooks(ctx, exec)
}

// Update uses an executor to update the CompressedRel.
// See boil.Columns.UpdateColumnSet documentation to understand column list inference for updates.
// Update does not automatically update the record in case of default values. Use .Reload() to refresh the records.
func (o *CompressedRel) Update(ctx context.Context, exec boil.ContextExecutor, columns boil.Columns) (int64, error) {
	var err error
	if err = o.doBeforeUpdateHooks(ctx, exec); err != nil {
		return 0, err
	}
	key := makeCacheKey(columns, nil)
	compressedRelUpdateCacheMut.RLock()
	cache, cached := compressedRelUpdateCache[key]
	compressedRelUpdateCacheMut.RUnlock()

	if !cached {
		wl := columns.UpdateColumnSet(
			compressedRelAllColumns,
			compressedRelPrimaryKeyColumns,
		)

		if !columns.IsWhitelist() {
			wl = strmangle.SetComplement(wl, []string{"created_at"})
		}
		if len(wl) == 0 {
			return 0, errors.New("tskv_example: unable to update compressed_rel, could not build whitelist")
		}

		cache.query = fmt.Sprintf("UPDATE \"tskv_example\".\"compressed_rel\" SET %s WHERE %s",
			strmangle.SetParamNames("\"", "\"", 1, wl),
			strmangle.WhereClause("\"", "\"", len(wl)+1, compressedRelPrimaryKeyColumns),
		)
		cache.valueMapping, err = queries.BindMapping(compressedRelType, compressedRelMapping, append(wl, compressedRelPrimaryKeyColumns...))
		if err != nil {
			return 0, err
		}
	}

	values := queries.ValuesFromMapping(reflect.Indirect(reflect.ValueOf(o)), cache.valueMapping)

	if boil.IsDebug(ctx) {
		writer := boil.DebugWriterFrom(ctx)
		fmt.Fprintln(writer, cache.query)
		fmt.Fprintln(writer, values)
	}
	var result sql.Result
	result, err = exec.ExecContext(ctx, cache.query, values...)
	if err != nil {
		return 0, errors.Wrap(err, "tskv_example: unable to update compressed_rel row")
	}

	rowsAff, err := result.RowsAffected()
	if err != nil {
		return 0, errors.Wrap(err, "tskv_example: failed to get rows affected by update for compressed_rel")
	}

	if !cached {
		compressedRelUpdateCacheMut.Lock()
		compressedRelUpdateCache[key] = cache
		compressedRelUpdateCacheMut.Unlock()
	}

	return rowsAff, o.doAfterUpdateHooks(ctx, exec)
}

// UpdateAll updates all rows with the specified column values.
func (q compressedRelQuery) UpdateAll(ctx context.Context, exec boil.ContextExecutor, cols M) (int64, error) {
	queries.SetUpdate(q.Query, cols)

	result, err := q.Query.ExecContext(ctx, exec)
	if err != nil {
		return 0, errors.Wrap(err, "tskv_example: unable to update all for compressed_rel")
	}

	rowsAff, err := result.RowsAffected()
	if err != nil {
		return 0, errors.Wrap(err, "tskv_example: unable to retrieve rows affected for compressed_rel")
	}

	return rowsAff, nil
}

// UpdateAll updates all rows with the specified column values, using an executor.
func (o CompressedRelSlice) UpdateAll(ctx context.Context, exec boil.ContextExecutor, cols M) (int64, error) {
	ln := int64(len(o))
	if ln == 0 {
		return 0, nil
	}

	if len(cols) == 0 {
		return 0, errors.New("tskv_example: update all requires at least one column argument")
	}

	colNames := make([]string, len(cols))
	args := make([]interface{}, len(cols))

	i := 0
	for name, value := range cols {
		colNames[i] = name
		args[i] = value
		i++
	}

	// Append all of the primary key values for each column
	for _, obj := range o {
		pkeyArgs := queries.ValuesFromMapping(reflect.Indirect(reflect.ValueOf(obj)), compressedRelPrimaryKeyMapping)
		args = append(args, pkeyArgs...)
	}

	sql := fmt.Sprintf("UPDATE \"tskv_example\".\"compressed_rel\" SET %s WHERE %s",
		strmangle.SetParamNames("\"", "\"", 1, colNames),
		strmangle.WhereClauseRepeated(string(dialect.LQ), string(dialect.RQ), len(colNames)+1, compressedRelPrimaryKeyColumns, len(o)))

	if boil.IsDebug(ctx) {
		writer := boil.DebugWriterFrom(ctx)
		fmt.Fprintln(writer, sql)
		fmt.Fprintln(writer, args...)
	}
	result, err := exec.ExecContext(ctx, sql, args...)
	if err != nil {
		return 0, errors.Wrap(err, "tskv_example: unable to update all in compressedRel slice")
	}

	rowsAff, err := result.RowsAffected()
	if err != nil {
		return 0, errors.Wrap(err, "tskv_example: unable to retrieve rows affected all in update all compressedRel")
	}
	return rowsAff, nil
}

// Upsert attempts an insert using an executor, and does an update or ignore on conflict.
// See boil.Columns documentation for how to properly use updateColumns and insertColumns.
func (o *CompressedRel) Upsert(ctx context.Context, exec boil.ContextExecutor, updateOnConflict bool, conflictColumns []string, updateColumns, insertColumns boil.Columns) error {
	if o == nil {
		return errors.New("tskv_example: no compressed_rel provided for upsert")
	}

	if err := o.doBeforeUpsertHooks(ctx, exec); err != nil {
		return err
	}

	nzDefaults := queries.NonZeroDefaultSet(compressedRelColumnsWithDefault, o)

	// Build cache key in-line uglily - mysql vs psql problems
	buf := strmangle.GetBuffer()
	if updateOnConflict {
		buf.WriteByte('t')
	} else {
		buf.WriteByte('f')
	}
	buf.WriteByte('.')
	for _, c := range conflictColumns {
		buf.WriteString(c)
	}
	buf.WriteByte('.')
	buf.WriteString(strconv.Itoa(updateColumns.Kind))
	for _, c := range updateColumns.Cols {
		buf.WriteString(c)
	}
	buf.WriteByte('.')
	buf.WriteString(strconv.Itoa(insertColumns.Kind))
	for _, c := range insertColumns.Cols {
		buf.WriteString(c)
	}
	buf.WriteByte('.')
	for _, c := range nzDefaults {
		buf.WriteString(c)
	}
	key := buf.String()
	strmangle.PutBuffer(buf)

	compressedRelUpsertCacheMut.RLock()
	cache, cached := compressedRelUpsertCache[key]
	compressedRelUpsertCacheMut.RUnlock()

	var err error

	if !cached {
		insert, ret := insertColumns.InsertColumnSet(
			compressedRelAllColumns,
			compressedRelColumnsWithDefault,
			compressedRelColumnsWithoutDefault,
			nzDefaults,
		)

		update := updateColumns.UpdateColumnSet(
			compressedRelAllColumns,
			compressedRelPrimaryKeyColumns,
		)

		if updateOnConflict && len(update) == 0 {
			return errors.New("tskv_example: unable to upsert compressed_rel, could not build update column list")
		}

		conflict := conflictColumns
		if len(conflict) == 0 {
			conflict = make([]string, len(compressedRelPrimaryKeyColumns))
			copy(conflict, compressedRelPrimaryKeyColumns)
		}
		cache.query = buildUpsertQueryPostgres(dialect, "\"tskv_example\".\"compressed_rel\"", updateOnConflict, ret, update, conflict, insert)

		cache.valueMapping, err = queries.BindMapping(compressedRelType, compressedRelMapping, insert)
		if err != nil {
			return err
		}
		if len(ret) != 0 {
			cache.retMapping, err = queries.BindMapping(compressedRelType, compressedRelMapping, ret)
			if err != nil {
				return err
			}
		}
	}

	value := reflect.Indirect(reflect.ValueOf(o))
	vals := queries.ValuesFromMapping(value, cache.valueMapping)
	var returns []interface{}
	if len(cache.retMapping) != 0 {
		returns = queries.PtrsFromMapping(value, cache.retMapping)
	}

	if boil.IsDebug(ctx) {
		writer := boil.DebugWriterFrom(ctx)
		fmt.Fprintln(writer, cache.query)
		fmt.Fprintln(writer, vals)
	}
	if len(cache.retMapping) != 0 {
		err = exec.QueryRowContext(ctx, cache.query, vals...).Scan(returns...)
		if errors.Is(err, sql.ErrNoRows) {
			err = nil // Postgres doesn't return anything when there's no update
		}
	} else {
		_, err = exec.ExecContext(ctx, cache.query, vals...)
	}
	if err != nil {
		return errors.Wrap(err, "tskv_example: unable to upsert compressed_rel")
	}

	if !cached {
		compressedRelUpsertCacheMut.Lock()
		compressedRelUpsertCache[key] = cache
		compressedRelUpsertCacheMut.Unlock()
	}

	return o.doAfterUpsertHooks(ctx, exec)
}

// Delete deletes a single CompressedRel record with an executor.
// Delete will match against the primary key column to find the record to delete.
func (o *CompressedRel) Delete(ctx context.Context, exec boil.ContextExecutor) (int64, error) {
	if o == nil {
		return 0, errors.New("tskv_example: no CompressedRel provided for delete")
	}

	if err := o.doBeforeDeleteHooks(ctx, exec); err != nil {
		return 0, err
	}

	args := queries.ValuesFromMapping(reflect.Indirect(reflect.ValueOf(o)), compressedRelPrimaryKeyMapping)
	sql := "DELETE FROM \"tskv_example\".\"compressed_rel\" WHERE \"rowid\"=$1"

	if boil.IsDebug(ctx) {
		writer := boil.DebugWriterFrom(ctx)
		fmt.Fprintln(writer, sql)
		fmt.Fprintln(writer, args...)
	}
	result, err := exec.ExecContext(ctx, sql, args...)
	if err != nil {
		return 0, errors.Wrap(err, "tskv_example: unable to delete from compressed_rel")
	}

	rowsAff, err := result.RowsAffected()
	if err != nil {
		return 0, errors.Wrap(err, "tskv_example: failed to get rows affected by delete for compressed_rel")
	}

	if err := o.doAfterDeleteHooks(ctx, exec); err != nil {
		return 0, err
	}

	return rowsAff, nil
}

// DeleteAll deletes all matching rows.
func (q compressedRelQuery) DeleteAll(ctx context.Context, exec boil.ContextExecutor) (int64, error) {
	if q.Query == nil {
		return 0, errors.New("tskv_example: no compressedRelQuery provided for delete all")
	}

	queries.SetDelete(q.Query)

	result, err := q.Query.ExecContext(ctx, exec)
	if err != nil {
		return 0, errors.Wrap(err, "tskv_example: unable to delete all from compressed_rel")
	}

	rowsAff, err := result.RowsAffected()
	if err != nil {
		return 0, errors.Wrap(err, "tskv_example: failed to get rows affected by deleteall for compressed_rel")
	}

	return rowsAff, nil
}

// DeleteAll deletes all rows in the slice, using an executor.
func (o CompressedRelSlice) DeleteAll(ctx context.Context, exec boil.ContextExecutor) (int64, error) {
	if len(o) == 0 {
		return 0, nil
	}

	if len(compressedRelBeforeDeleteHooks) != 0 {
		for _, obj := range o {
			if err := obj.doBeforeDeleteHooks(ctx, exec); err != nil {
				return 0, err
			}
		}
	}

	var args []interface{}
	for _, obj := range o {
		pkeyArgs := queries.ValuesFromMapping(reflect.Indirect(reflect.ValueOf(obj)), compressedRelPrimaryKeyMapping)
		args = append(args, pkeyArgs...)
	}

	sql := "DELETE FROM \"tskv_example\".\"compressed_rel\" WHERE " +
		strmangle.WhereClauseRepeated(string(dialect.LQ), string(dialect.RQ), 1, compressedRelPrimaryKeyColumns, len(o))

	if boil.IsDebug(ctx) {
		writer := boil.DebugWriterFrom(ctx)
		fmt.Fprintln(writer, sql)
		fmt.Fprintln(writer, args)
	}
	result, err := exec.ExecContext(ctx, sql, args...)
	if err != nil {
		return 0, errors.Wrap(err, "tskv_example: unable to delete all from compressedRel slice")
	}

	rowsAff, err := result.RowsAffected()
	if err != nil {
		return 0, errors.Wrap(err, "tskv_example: failed to get rows affected by deleteall for compressed_rel")
	}

	if len(compressedRelAfterDeleteHooks) != 0 {
		for _, obj := range o {
			if err := obj.doAfterDeleteHooks(ctx, exec); err != nil {
				return 0, err
			}
		}
	}

	return rowsAff, nil
}

// Reload refetches the object from the database
// using the primary keys with an executor.
func (o *CompressedRel) Reload(ctx context.Context, exec boil.ContextExecutor) error {
	ret, err := FindCompressedRel(ctx, exec, o.Rowid)
	if err != nil {
		return err
	}

	*o = *ret
	return nil
}

// ReloadAll refetches every row with matching primary key column values
// and overwrites the original object slice with the newly updated slice.
func (o *CompressedRelSlice) ReloadAll(ctx context.Context, exec boil.ContextExecutor) error {
	if o == nil || len(*o) == 0 {
		return nil
	}

	slice := CompressedRelSlice{}
	var args []interface{}
	for _, obj := range *o {
		pkeyArgs := queries.ValuesFromMapping(reflect.Indirect(reflect.ValueOf(obj)), compressedRelPrimaryKeyMapping)
		args = append(args, pkeyArgs...)
	}

	sql := "SELECT \"tskv_example\".\"compressed_rel\".* FROM \"tskv_example\".\"compressed_rel\" WHERE " +
		strmangle.WhereClauseRepeated(string(dialect.LQ), string(dialect.RQ), 1, compressedRelPrimaryKeyColumns, len(*o))

	q := queries.Raw(sql, args...)

	err := q.Bind(ctx, exec, &slice)
	if err != nil {
		return errors.Wrap(err, "tskv_example: unable to reload all in CompressedRelSlice")
	}

	*o = slice

	return nil
}

// CompressedRelExists checks if the CompressedRel row exists.
func CompressedRelExists(ctx context.Context, exec boil.ContextExecutor, rowid string) (bool, error) {
	var exists bool
	sql := "select exists(select 1 from \"tskv_example\".\"compressed_rel\" where \"rowid\"=$1 limit 1)"

	if boil.IsDebug(ctx) {
		writer := boil.DebugWriterFrom(ctx)
		fmt.Fprintln(writer, sql)
		fmt.Fprintln(writer, rowid)
	}
	row := exec.QueryRowContext(ctx, sql, rowid)

	err := row.Scan(&exists)
	if err != nil {
		return false, errors.Wrap(err, "tskv_example: unable to check if compressed_rel exists")
	}

	return exists, nil
}
