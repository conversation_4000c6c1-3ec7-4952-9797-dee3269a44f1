// Code generated by SQLBoiler (https://github.com/volatiletech/sqlboiler). DO NOT EDIT.
// This file is meant to be re-generated in place and/or deleted at any time.

package tskv_example

import (
	"context"
	"database/sql"
	"fmt"
	"reflect"
	"strconv"
	"strings"
	"sync"
	"time"

	"github.com/friendsofgo/errors"
	"github.com/volatiletech/null/v8"
	"github.com/volatiletech/sqlboiler/v4/boil"
	"github.com/volatiletech/sqlboiler/v4/queries"
	"github.com/volatiletech/sqlboiler/v4/queries/qm"
	"github.com/volatiletech/sqlboiler/v4/queries/qmhelper"
	"github.com/volatiletech/strmangle"
)

// Rel is an object representing the database table.
type Rel struct {
	Key        string      `boil:"key" json:"key" toml:"key" yaml:"key"`
	Val        int         `boil:"val" json:"val" toml:"val" yaml:"val"`
	Time       time.Time   `boil:"time" json:"time" toml:"time" yaml:"time"`
	ObserverID null.String `boil:"observer_id" json:"observer_id,omitempty" toml:"observer_id" yaml:"observer_id,omitempty"`

	R *relR `boil:"" json:"" toml:"" yaml:""`
	L relL  `boil:"-" json:"-" toml:"-" yaml:"-"`
}

var RelColumns = struct {
	Key        string
	Val        string
	Time       string
	ObserverID string
}{
	Key:        "key",
	Val:        "val",
	Time:       "time",
	ObserverID: "observer_id",
}

var RelTableColumns = struct {
	Key        string
	Val        string
	Time       string
	ObserverID string
}{
	Key:        "rel.key",
	Val:        "rel.val",
	Time:       "rel.time",
	ObserverID: "rel.observer_id",
}

// Generated where

type whereHelpernull_String struct{ field string }

func (w whereHelpernull_String) EQ(x null.String) qm.QueryMod {
	return qmhelper.WhereNullEQ(w.field, false, x)
}
func (w whereHelpernull_String) NEQ(x null.String) qm.QueryMod {
	return qmhelper.WhereNullEQ(w.field, true, x)
}
func (w whereHelpernull_String) LT(x null.String) qm.QueryMod {
	return qmhelper.Where(w.field, qmhelper.LT, x)
}
func (w whereHelpernull_String) LTE(x null.String) qm.QueryMod {
	return qmhelper.Where(w.field, qmhelper.LTE, x)
}
func (w whereHelpernull_String) GT(x null.String) qm.QueryMod {
	return qmhelper.Where(w.field, qmhelper.GT, x)
}
func (w whereHelpernull_String) GTE(x null.String) qm.QueryMod {
	return qmhelper.Where(w.field, qmhelper.GTE, x)
}

func (w whereHelpernull_String) IsNull() qm.QueryMod    { return qmhelper.WhereIsNull(w.field) }
func (w whereHelpernull_String) IsNotNull() qm.QueryMod { return qmhelper.WhereIsNotNull(w.field) }

var RelWhere = struct {
	Key        whereHelperstring
	Val        whereHelperint
	Time       whereHelpertime_Time
	ObserverID whereHelpernull_String
}{
	Key:        whereHelperstring{field: "\"tskv_example\".\"rel\".\"key\""},
	Val:        whereHelperint{field: "\"tskv_example\".\"rel\".\"val\""},
	Time:       whereHelpertime_Time{field: "\"tskv_example\".\"rel\".\"time\""},
	ObserverID: whereHelpernull_String{field: "\"tskv_example\".\"rel\".\"observer_id\""},
}

// RelRels is where relationship names are stored.
var RelRels = struct {
}{}

// relR is where relationships are stored.
type relR struct {
}

// NewStruct creates a new relationship struct
func (*relR) NewStruct() *relR {
	return &relR{}
}

// relL is where Load methods for each relationship are stored.
type relL struct{}

var (
	relAllColumns            = []string{"key", "val", "time", "observer_id"}
	relColumnsWithoutDefault = []string{"key", "val", "time"}
	relColumnsWithDefault    = []string{"observer_id"}
	relPrimaryKeyColumns     = []string{"key", "time"}
	relGeneratedColumns      = []string{}
)

type (
	// RelSlice is an alias for a slice of pointers to Rel.
	// This should almost always be used instead of []Rel.
	RelSlice []*Rel
	// RelHook is the signature for custom Rel hook methods
	RelHook func(context.Context, boil.ContextExecutor, *Rel) error

	relQuery struct {
		*queries.Query
	}
)

// Cache for insert, update and upsert
var (
	relType                 = reflect.TypeOf(&Rel{})
	relMapping              = queries.MakeStructMapping(relType)
	relPrimaryKeyMapping, _ = queries.BindMapping(relType, relMapping, relPrimaryKeyColumns)
	relInsertCacheMut       sync.RWMutex
	relInsertCache          = make(map[string]insertCache)
	relUpdateCacheMut       sync.RWMutex
	relUpdateCache          = make(map[string]updateCache)
	relUpsertCacheMut       sync.RWMutex
	relUpsertCache          = make(map[string]insertCache)
)

var (
	// Force time package dependency for automated UpdatedAt/CreatedAt.
	_ = time.Second
	// Force qmhelper dependency for where clause generation (which doesn't
	// always happen)
	_ = qmhelper.Where
)

var relAfterSelectHooks []RelHook

var relBeforeInsertHooks []RelHook
var relAfterInsertHooks []RelHook

var relBeforeUpdateHooks []RelHook
var relAfterUpdateHooks []RelHook

var relBeforeDeleteHooks []RelHook
var relAfterDeleteHooks []RelHook

var relBeforeUpsertHooks []RelHook
var relAfterUpsertHooks []RelHook

// doAfterSelectHooks executes all "after Select" hooks.
func (o *Rel) doAfterSelectHooks(ctx context.Context, exec boil.ContextExecutor) (err error) {
	if boil.HooksAreSkipped(ctx) {
		return nil
	}

	for _, hook := range relAfterSelectHooks {
		if err := hook(ctx, exec, o); err != nil {
			return err
		}
	}

	return nil
}

// doBeforeInsertHooks executes all "before insert" hooks.
func (o *Rel) doBeforeInsertHooks(ctx context.Context, exec boil.ContextExecutor) (err error) {
	if boil.HooksAreSkipped(ctx) {
		return nil
	}

	for _, hook := range relBeforeInsertHooks {
		if err := hook(ctx, exec, o); err != nil {
			return err
		}
	}

	return nil
}

// doAfterInsertHooks executes all "after Insert" hooks.
func (o *Rel) doAfterInsertHooks(ctx context.Context, exec boil.ContextExecutor) (err error) {
	if boil.HooksAreSkipped(ctx) {
		return nil
	}

	for _, hook := range relAfterInsertHooks {
		if err := hook(ctx, exec, o); err != nil {
			return err
		}
	}

	return nil
}

// doBeforeUpdateHooks executes all "before Update" hooks.
func (o *Rel) doBeforeUpdateHooks(ctx context.Context, exec boil.ContextExecutor) (err error) {
	if boil.HooksAreSkipped(ctx) {
		return nil
	}

	for _, hook := range relBeforeUpdateHooks {
		if err := hook(ctx, exec, o); err != nil {
			return err
		}
	}

	return nil
}

// doAfterUpdateHooks executes all "after Update" hooks.
func (o *Rel) doAfterUpdateHooks(ctx context.Context, exec boil.ContextExecutor) (err error) {
	if boil.HooksAreSkipped(ctx) {
		return nil
	}

	for _, hook := range relAfterUpdateHooks {
		if err := hook(ctx, exec, o); err != nil {
			return err
		}
	}

	return nil
}

// doBeforeDeleteHooks executes all "before Delete" hooks.
func (o *Rel) doBeforeDeleteHooks(ctx context.Context, exec boil.ContextExecutor) (err error) {
	if boil.HooksAreSkipped(ctx) {
		return nil
	}

	for _, hook := range relBeforeDeleteHooks {
		if err := hook(ctx, exec, o); err != nil {
			return err
		}
	}

	return nil
}

// doAfterDeleteHooks executes all "after Delete" hooks.
func (o *Rel) doAfterDeleteHooks(ctx context.Context, exec boil.ContextExecutor) (err error) {
	if boil.HooksAreSkipped(ctx) {
		return nil
	}

	for _, hook := range relAfterDeleteHooks {
		if err := hook(ctx, exec, o); err != nil {
			return err
		}
	}

	return nil
}

// doBeforeUpsertHooks executes all "before Upsert" hooks.
func (o *Rel) doBeforeUpsertHooks(ctx context.Context, exec boil.ContextExecutor) (err error) {
	if boil.HooksAreSkipped(ctx) {
		return nil
	}

	for _, hook := range relBeforeUpsertHooks {
		if err := hook(ctx, exec, o); err != nil {
			return err
		}
	}

	return nil
}

// doAfterUpsertHooks executes all "after Upsert" hooks.
func (o *Rel) doAfterUpsertHooks(ctx context.Context, exec boil.ContextExecutor) (err error) {
	if boil.HooksAreSkipped(ctx) {
		return nil
	}

	for _, hook := range relAfterUpsertHooks {
		if err := hook(ctx, exec, o); err != nil {
			return err
		}
	}

	return nil
}

// AddRelHook registers your hook function for all future operations.
func AddRelHook(hookPoint boil.HookPoint, relHook RelHook) {
	switch hookPoint {
	case boil.AfterSelectHook:
		relAfterSelectHooks = append(relAfterSelectHooks, relHook)
	case boil.BeforeInsertHook:
		relBeforeInsertHooks = append(relBeforeInsertHooks, relHook)
	case boil.AfterInsertHook:
		relAfterInsertHooks = append(relAfterInsertHooks, relHook)
	case boil.BeforeUpdateHook:
		relBeforeUpdateHooks = append(relBeforeUpdateHooks, relHook)
	case boil.AfterUpdateHook:
		relAfterUpdateHooks = append(relAfterUpdateHooks, relHook)
	case boil.BeforeDeleteHook:
		relBeforeDeleteHooks = append(relBeforeDeleteHooks, relHook)
	case boil.AfterDeleteHook:
		relAfterDeleteHooks = append(relAfterDeleteHooks, relHook)
	case boil.BeforeUpsertHook:
		relBeforeUpsertHooks = append(relBeforeUpsertHooks, relHook)
	case boil.AfterUpsertHook:
		relAfterUpsertHooks = append(relAfterUpsertHooks, relHook)
	}
}

// One returns a single rel record from the query.
func (q relQuery) One(ctx context.Context, exec boil.ContextExecutor) (*Rel, error) {
	o := &Rel{}

	queries.SetLimit(q.Query, 1)

	err := q.Bind(ctx, exec, o)
	if err != nil {
		if errors.Is(err, sql.ErrNoRows) {
			return nil, sql.ErrNoRows
		}
		return nil, errors.Wrap(err, "tskv_example: failed to execute a one query for rel")
	}

	if err := o.doAfterSelectHooks(ctx, exec); err != nil {
		return o, err
	}

	return o, nil
}

// All returns all Rel records from the query.
func (q relQuery) All(ctx context.Context, exec boil.ContextExecutor) (RelSlice, error) {
	var o []*Rel

	err := q.Bind(ctx, exec, &o)
	if err != nil {
		return nil, errors.Wrap(err, "tskv_example: failed to assign all query results to Rel slice")
	}

	if len(relAfterSelectHooks) != 0 {
		for _, obj := range o {
			if err := obj.doAfterSelectHooks(ctx, exec); err != nil {
				return o, err
			}
		}
	}

	return o, nil
}

// Count returns the count of all Rel records in the query.
func (q relQuery) Count(ctx context.Context, exec boil.ContextExecutor) (int64, error) {
	var count int64

	queries.SetSelect(q.Query, nil)
	queries.SetCount(q.Query)

	err := q.Query.QueryRowContext(ctx, exec).Scan(&count)
	if err != nil {
		return 0, errors.Wrap(err, "tskv_example: failed to count rel rows")
	}

	return count, nil
}

// Exists checks if the row exists in the table.
func (q relQuery) Exists(ctx context.Context, exec boil.ContextExecutor) (bool, error) {
	var count int64

	queries.SetSelect(q.Query, nil)
	queries.SetCount(q.Query)
	queries.SetLimit(q.Query, 1)

	err := q.Query.QueryRowContext(ctx, exec).Scan(&count)
	if err != nil {
		return false, errors.Wrap(err, "tskv_example: failed to check if rel exists")
	}

	return count > 0, nil
}

// Rels retrieves all the records using an executor.
func Rels(mods ...qm.QueryMod) relQuery {
	mods = append(mods, qm.From("\"tskv_example\".\"rel\""))
	q := NewQuery(mods...)
	if len(queries.GetSelect(q)) == 0 {
		queries.SetSelect(q, []string{"\"tskv_example\".\"rel\".*"})
	}

	return relQuery{q}
}

// FindRel retrieves a single record by ID with an executor.
// If selectCols is empty Find will return all columns.
func FindRel(ctx context.Context, exec boil.ContextExecutor, key string, time time.Time, selectCols ...string) (*Rel, error) {
	relObj := &Rel{}

	sel := "*"
	if len(selectCols) > 0 {
		sel = strings.Join(strmangle.IdentQuoteSlice(dialect.LQ, dialect.RQ, selectCols), ",")
	}
	query := fmt.Sprintf(
		"select %s from \"tskv_example\".\"rel\" where \"key\"=$1 AND \"time\"=$2", sel,
	)

	q := queries.Raw(query, key, time)

	err := q.Bind(ctx, exec, relObj)
	if err != nil {
		if errors.Is(err, sql.ErrNoRows) {
			return nil, sql.ErrNoRows
		}
		return nil, errors.Wrap(err, "tskv_example: unable to select from rel")
	}

	if err = relObj.doAfterSelectHooks(ctx, exec); err != nil {
		return relObj, err
	}

	return relObj, nil
}

// Insert a single record using an executor.
// See boil.Columns.InsertColumnSet documentation to understand column list inference for inserts.
func (o *Rel) Insert(ctx context.Context, exec boil.ContextExecutor, columns boil.Columns) error {
	if o == nil {
		return errors.New("tskv_example: no rel provided for insertion")
	}

	var err error

	if err := o.doBeforeInsertHooks(ctx, exec); err != nil {
		return err
	}

	nzDefaults := queries.NonZeroDefaultSet(relColumnsWithDefault, o)

	key := makeCacheKey(columns, nzDefaults)
	relInsertCacheMut.RLock()
	cache, cached := relInsertCache[key]
	relInsertCacheMut.RUnlock()

	if !cached {
		wl, returnColumns := columns.InsertColumnSet(
			relAllColumns,
			relColumnsWithDefault,
			relColumnsWithoutDefault,
			nzDefaults,
		)

		cache.valueMapping, err = queries.BindMapping(relType, relMapping, wl)
		if err != nil {
			return err
		}
		cache.retMapping, err = queries.BindMapping(relType, relMapping, returnColumns)
		if err != nil {
			return err
		}
		if len(wl) != 0 {
			cache.query = fmt.Sprintf("INSERT INTO \"tskv_example\".\"rel\" (\"%s\") %%sVALUES (%s)%%s", strings.Join(wl, "\",\""), strmangle.Placeholders(dialect.UseIndexPlaceholders, len(wl), 1, 1))
		} else {
			cache.query = "INSERT INTO \"tskv_example\".\"rel\" %sDEFAULT VALUES%s"
		}

		var queryOutput, queryReturning string

		if len(cache.retMapping) != 0 {
			queryReturning = fmt.Sprintf(" RETURNING \"%s\"", strings.Join(returnColumns, "\",\""))
		}

		cache.query = fmt.Sprintf(cache.query, queryOutput, queryReturning)
	}

	value := reflect.Indirect(reflect.ValueOf(o))
	vals := queries.ValuesFromMapping(value, cache.valueMapping)

	if boil.IsDebug(ctx) {
		writer := boil.DebugWriterFrom(ctx)
		fmt.Fprintln(writer, cache.query)
		fmt.Fprintln(writer, vals)
	}

	if len(cache.retMapping) != 0 {
		err = exec.QueryRowContext(ctx, cache.query, vals...).Scan(queries.PtrsFromMapping(value, cache.retMapping)...)
	} else {
		_, err = exec.ExecContext(ctx, cache.query, vals...)
	}

	if err != nil {
		return errors.Wrap(err, "tskv_example: unable to insert into rel")
	}

	if !cached {
		relInsertCacheMut.Lock()
		relInsertCache[key] = cache
		relInsertCacheMut.Unlock()
	}

	return o.doAfterInsertHooks(ctx, exec)
}

// Update uses an executor to update the Rel.
// See boil.Columns.UpdateColumnSet documentation to understand column list inference for updates.
// Update does not automatically update the record in case of default values. Use .Reload() to refresh the records.
func (o *Rel) Update(ctx context.Context, exec boil.ContextExecutor, columns boil.Columns) (int64, error) {
	var err error
	if err = o.doBeforeUpdateHooks(ctx, exec); err != nil {
		return 0, err
	}
	key := makeCacheKey(columns, nil)
	relUpdateCacheMut.RLock()
	cache, cached := relUpdateCache[key]
	relUpdateCacheMut.RUnlock()

	if !cached {
		wl := columns.UpdateColumnSet(
			relAllColumns,
			relPrimaryKeyColumns,
		)

		if !columns.IsWhitelist() {
			wl = strmangle.SetComplement(wl, []string{"created_at"})
		}
		if len(wl) == 0 {
			return 0, errors.New("tskv_example: unable to update rel, could not build whitelist")
		}

		cache.query = fmt.Sprintf("UPDATE \"tskv_example\".\"rel\" SET %s WHERE %s",
			strmangle.SetParamNames("\"", "\"", 1, wl),
			strmangle.WhereClause("\"", "\"", len(wl)+1, relPrimaryKeyColumns),
		)
		cache.valueMapping, err = queries.BindMapping(relType, relMapping, append(wl, relPrimaryKeyColumns...))
		if err != nil {
			return 0, err
		}
	}

	values := queries.ValuesFromMapping(reflect.Indirect(reflect.ValueOf(o)), cache.valueMapping)

	if boil.IsDebug(ctx) {
		writer := boil.DebugWriterFrom(ctx)
		fmt.Fprintln(writer, cache.query)
		fmt.Fprintln(writer, values)
	}
	var result sql.Result
	result, err = exec.ExecContext(ctx, cache.query, values...)
	if err != nil {
		return 0, errors.Wrap(err, "tskv_example: unable to update rel row")
	}

	rowsAff, err := result.RowsAffected()
	if err != nil {
		return 0, errors.Wrap(err, "tskv_example: failed to get rows affected by update for rel")
	}

	if !cached {
		relUpdateCacheMut.Lock()
		relUpdateCache[key] = cache
		relUpdateCacheMut.Unlock()
	}

	return rowsAff, o.doAfterUpdateHooks(ctx, exec)
}

// UpdateAll updates all rows with the specified column values.
func (q relQuery) UpdateAll(ctx context.Context, exec boil.ContextExecutor, cols M) (int64, error) {
	queries.SetUpdate(q.Query, cols)

	result, err := q.Query.ExecContext(ctx, exec)
	if err != nil {
		return 0, errors.Wrap(err, "tskv_example: unable to update all for rel")
	}

	rowsAff, err := result.RowsAffected()
	if err != nil {
		return 0, errors.Wrap(err, "tskv_example: unable to retrieve rows affected for rel")
	}

	return rowsAff, nil
}

// UpdateAll updates all rows with the specified column values, using an executor.
func (o RelSlice) UpdateAll(ctx context.Context, exec boil.ContextExecutor, cols M) (int64, error) {
	ln := int64(len(o))
	if ln == 0 {
		return 0, nil
	}

	if len(cols) == 0 {
		return 0, errors.New("tskv_example: update all requires at least one column argument")
	}

	colNames := make([]string, len(cols))
	args := make([]interface{}, len(cols))

	i := 0
	for name, value := range cols {
		colNames[i] = name
		args[i] = value
		i++
	}

	// Append all of the primary key values for each column
	for _, obj := range o {
		pkeyArgs := queries.ValuesFromMapping(reflect.Indirect(reflect.ValueOf(obj)), relPrimaryKeyMapping)
		args = append(args, pkeyArgs...)
	}

	sql := fmt.Sprintf("UPDATE \"tskv_example\".\"rel\" SET %s WHERE %s",
		strmangle.SetParamNames("\"", "\"", 1, colNames),
		strmangle.WhereClauseRepeated(string(dialect.LQ), string(dialect.RQ), len(colNames)+1, relPrimaryKeyColumns, len(o)))

	if boil.IsDebug(ctx) {
		writer := boil.DebugWriterFrom(ctx)
		fmt.Fprintln(writer, sql)
		fmt.Fprintln(writer, args...)
	}
	result, err := exec.ExecContext(ctx, sql, args...)
	if err != nil {
		return 0, errors.Wrap(err, "tskv_example: unable to update all in rel slice")
	}

	rowsAff, err := result.RowsAffected()
	if err != nil {
		return 0, errors.Wrap(err, "tskv_example: unable to retrieve rows affected all in update all rel")
	}
	return rowsAff, nil
}

// Upsert attempts an insert using an executor, and does an update or ignore on conflict.
// See boil.Columns documentation for how to properly use updateColumns and insertColumns.
func (o *Rel) Upsert(ctx context.Context, exec boil.ContextExecutor, updateOnConflict bool, conflictColumns []string, updateColumns, insertColumns boil.Columns) error {
	if o == nil {
		return errors.New("tskv_example: no rel provided for upsert")
	}

	if err := o.doBeforeUpsertHooks(ctx, exec); err != nil {
		return err
	}

	nzDefaults := queries.NonZeroDefaultSet(relColumnsWithDefault, o)

	// Build cache key in-line uglily - mysql vs psql problems
	buf := strmangle.GetBuffer()
	if updateOnConflict {
		buf.WriteByte('t')
	} else {
		buf.WriteByte('f')
	}
	buf.WriteByte('.')
	for _, c := range conflictColumns {
		buf.WriteString(c)
	}
	buf.WriteByte('.')
	buf.WriteString(strconv.Itoa(updateColumns.Kind))
	for _, c := range updateColumns.Cols {
		buf.WriteString(c)
	}
	buf.WriteByte('.')
	buf.WriteString(strconv.Itoa(insertColumns.Kind))
	for _, c := range insertColumns.Cols {
		buf.WriteString(c)
	}
	buf.WriteByte('.')
	for _, c := range nzDefaults {
		buf.WriteString(c)
	}
	key := buf.String()
	strmangle.PutBuffer(buf)

	relUpsertCacheMut.RLock()
	cache, cached := relUpsertCache[key]
	relUpsertCacheMut.RUnlock()

	var err error

	if !cached {
		insert, ret := insertColumns.InsertColumnSet(
			relAllColumns,
			relColumnsWithDefault,
			relColumnsWithoutDefault,
			nzDefaults,
		)

		update := updateColumns.UpdateColumnSet(
			relAllColumns,
			relPrimaryKeyColumns,
		)

		if updateOnConflict && len(update) == 0 {
			return errors.New("tskv_example: unable to upsert rel, could not build update column list")
		}

		conflict := conflictColumns
		if len(conflict) == 0 {
			conflict = make([]string, len(relPrimaryKeyColumns))
			copy(conflict, relPrimaryKeyColumns)
		}
		cache.query = buildUpsertQueryPostgres(dialect, "\"tskv_example\".\"rel\"", updateOnConflict, ret, update, conflict, insert)

		cache.valueMapping, err = queries.BindMapping(relType, relMapping, insert)
		if err != nil {
			return err
		}
		if len(ret) != 0 {
			cache.retMapping, err = queries.BindMapping(relType, relMapping, ret)
			if err != nil {
				return err
			}
		}
	}

	value := reflect.Indirect(reflect.ValueOf(o))
	vals := queries.ValuesFromMapping(value, cache.valueMapping)
	var returns []interface{}
	if len(cache.retMapping) != 0 {
		returns = queries.PtrsFromMapping(value, cache.retMapping)
	}

	if boil.IsDebug(ctx) {
		writer := boil.DebugWriterFrom(ctx)
		fmt.Fprintln(writer, cache.query)
		fmt.Fprintln(writer, vals)
	}
	if len(cache.retMapping) != 0 {
		err = exec.QueryRowContext(ctx, cache.query, vals...).Scan(returns...)
		if errors.Is(err, sql.ErrNoRows) {
			err = nil // Postgres doesn't return anything when there's no update
		}
	} else {
		_, err = exec.ExecContext(ctx, cache.query, vals...)
	}
	if err != nil {
		return errors.Wrap(err, "tskv_example: unable to upsert rel")
	}

	if !cached {
		relUpsertCacheMut.Lock()
		relUpsertCache[key] = cache
		relUpsertCacheMut.Unlock()
	}

	return o.doAfterUpsertHooks(ctx, exec)
}

// Delete deletes a single Rel record with an executor.
// Delete will match against the primary key column to find the record to delete.
func (o *Rel) Delete(ctx context.Context, exec boil.ContextExecutor) (int64, error) {
	if o == nil {
		return 0, errors.New("tskv_example: no Rel provided for delete")
	}

	if err := o.doBeforeDeleteHooks(ctx, exec); err != nil {
		return 0, err
	}

	args := queries.ValuesFromMapping(reflect.Indirect(reflect.ValueOf(o)), relPrimaryKeyMapping)
	sql := "DELETE FROM \"tskv_example\".\"rel\" WHERE \"key\"=$1 AND \"time\"=$2"

	if boil.IsDebug(ctx) {
		writer := boil.DebugWriterFrom(ctx)
		fmt.Fprintln(writer, sql)
		fmt.Fprintln(writer, args...)
	}
	result, err := exec.ExecContext(ctx, sql, args...)
	if err != nil {
		return 0, errors.Wrap(err, "tskv_example: unable to delete from rel")
	}

	rowsAff, err := result.RowsAffected()
	if err != nil {
		return 0, errors.Wrap(err, "tskv_example: failed to get rows affected by delete for rel")
	}

	if err := o.doAfterDeleteHooks(ctx, exec); err != nil {
		return 0, err
	}

	return rowsAff, nil
}

// DeleteAll deletes all matching rows.
func (q relQuery) DeleteAll(ctx context.Context, exec boil.ContextExecutor) (int64, error) {
	if q.Query == nil {
		return 0, errors.New("tskv_example: no relQuery provided for delete all")
	}

	queries.SetDelete(q.Query)

	result, err := q.Query.ExecContext(ctx, exec)
	if err != nil {
		return 0, errors.Wrap(err, "tskv_example: unable to delete all from rel")
	}

	rowsAff, err := result.RowsAffected()
	if err != nil {
		return 0, errors.Wrap(err, "tskv_example: failed to get rows affected by deleteall for rel")
	}

	return rowsAff, nil
}

// DeleteAll deletes all rows in the slice, using an executor.
func (o RelSlice) DeleteAll(ctx context.Context, exec boil.ContextExecutor) (int64, error) {
	if len(o) == 0 {
		return 0, nil
	}

	if len(relBeforeDeleteHooks) != 0 {
		for _, obj := range o {
			if err := obj.doBeforeDeleteHooks(ctx, exec); err != nil {
				return 0, err
			}
		}
	}

	var args []interface{}
	for _, obj := range o {
		pkeyArgs := queries.ValuesFromMapping(reflect.Indirect(reflect.ValueOf(obj)), relPrimaryKeyMapping)
		args = append(args, pkeyArgs...)
	}

	sql := "DELETE FROM \"tskv_example\".\"rel\" WHERE " +
		strmangle.WhereClauseRepeated(string(dialect.LQ), string(dialect.RQ), 1, relPrimaryKeyColumns, len(o))

	if boil.IsDebug(ctx) {
		writer := boil.DebugWriterFrom(ctx)
		fmt.Fprintln(writer, sql)
		fmt.Fprintln(writer, args)
	}
	result, err := exec.ExecContext(ctx, sql, args...)
	if err != nil {
		return 0, errors.Wrap(err, "tskv_example: unable to delete all from rel slice")
	}

	rowsAff, err := result.RowsAffected()
	if err != nil {
		return 0, errors.Wrap(err, "tskv_example: failed to get rows affected by deleteall for rel")
	}

	if len(relAfterDeleteHooks) != 0 {
		for _, obj := range o {
			if err := obj.doAfterDeleteHooks(ctx, exec); err != nil {
				return 0, err
			}
		}
	}

	return rowsAff, nil
}

// Reload refetches the object from the database
// using the primary keys with an executor.
func (o *Rel) Reload(ctx context.Context, exec boil.ContextExecutor) error {
	ret, err := FindRel(ctx, exec, o.Key, o.Time)
	if err != nil {
		return err
	}

	*o = *ret
	return nil
}

// ReloadAll refetches every row with matching primary key column values
// and overwrites the original object slice with the newly updated slice.
func (o *RelSlice) ReloadAll(ctx context.Context, exec boil.ContextExecutor) error {
	if o == nil || len(*o) == 0 {
		return nil
	}

	slice := RelSlice{}
	var args []interface{}
	for _, obj := range *o {
		pkeyArgs := queries.ValuesFromMapping(reflect.Indirect(reflect.ValueOf(obj)), relPrimaryKeyMapping)
		args = append(args, pkeyArgs...)
	}

	sql := "SELECT \"tskv_example\".\"rel\".* FROM \"tskv_example\".\"rel\" WHERE " +
		strmangle.WhereClauseRepeated(string(dialect.LQ), string(dialect.RQ), 1, relPrimaryKeyColumns, len(*o))

	q := queries.Raw(sql, args...)

	err := q.Bind(ctx, exec, &slice)
	if err != nil {
		return errors.Wrap(err, "tskv_example: unable to reload all in RelSlice")
	}

	*o = slice

	return nil
}

// RelExists checks if the Rel row exists.
func RelExists(ctx context.Context, exec boil.ContextExecutor, key string, time time.Time) (bool, error) {
	var exists bool
	sql := "select exists(select 1 from \"tskv_example\".\"rel\" where \"key\"=$1 AND \"time\"=$2 limit 1)"

	if boil.IsDebug(ctx) {
		writer := boil.DebugWriterFrom(ctx)
		fmt.Fprintln(writer, sql)
		fmt.Fprintln(writer, key, time)
	}
	row := exec.QueryRowContext(ctx, sql, key, time)

	err := row.Scan(&exists)
	if err != nil {
		return false, errors.Wrap(err, "tskv_example: unable to check if rel exists")
	}

	return exists, nil
}
