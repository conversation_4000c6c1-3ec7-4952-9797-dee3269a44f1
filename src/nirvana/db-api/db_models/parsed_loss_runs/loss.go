// Code generated by SQLBoiler (https://github.com/volatiletech/sqlboiler). DO NOT EDIT.
// This file is meant to be re-generated in place and/or deleted at any time.

package parsed_loss_runs

import (
	"context"
	"database/sql"
	"fmt"
	"reflect"
	"strconv"
	"strings"
	"sync"
	"time"

	"github.com/friendsofgo/errors"
	"github.com/volatiletech/null/v8"
	"github.com/volatiletech/sqlboiler/v4/boil"
	"github.com/volatiletech/sqlboiler/v4/queries"
	"github.com/volatiletech/sqlboiler/v4/queries/qm"
	"github.com/volatiletech/sqlboiler/v4/queries/qmhelper"
	"github.com/volatiletech/strmangle"
)

// Loss is an object representing the database table.
type Loss struct {
	ID                    string       `boil:"id" json:"id" toml:"id" yaml:"id"`
	DocumentID            string       `boil:"document_id" json:"document_id" toml:"document_id" yaml:"document_id"`
	PolicySN              int          `boil:"policy_sn" json:"policy_sn" toml:"policy_sn" yaml:"policy_sn"`
	CreatedAt             time.Time    `boil:"created_at" json:"created_at" toml:"created_at" yaml:"created_at"`
	ClaimSN               int          `boil:"claim_sn" json:"claim_sn" toml:"claim_sn" yaml:"claim_sn"`
	LossSeqID             string       `boil:"loss_seq_id" json:"loss_seq_id" toml:"loss_seq_id" yaml:"loss_seq_id"`
	Claimant              null.String  `boil:"claimant" json:"claimant,omitempty" toml:"claimant" yaml:"claimant,omitempty"`
	CoverageInferred      null.String  `boil:"coverage_inferred" json:"coverage_inferred,omitempty" toml:"coverage_inferred" yaml:"coverage_inferred,omitempty"`
	ClaimLossType         null.String  `boil:"claim_loss_type" json:"claim_loss_type,omitempty" toml:"claim_loss_type" yaml:"claim_loss_type,omitempty"`
	ClaimLossTypeInferred null.String  `boil:"claim_loss_type_inferred" json:"claim_loss_type_inferred,omitempty" toml:"claim_loss_type_inferred" yaml:"claim_loss_type_inferred,omitempty"`
	Examiner              null.String  `boil:"examiner" json:"examiner,omitempty" toml:"examiner" yaml:"examiner,omitempty"`
	LossReserved          null.Float64 `boil:"loss_reserved" json:"loss_reserved,omitempty" toml:"loss_reserved" yaml:"loss_reserved,omitempty"`
	LossPaid              null.Float64 `boil:"loss_paid" json:"loss_paid,omitempty" toml:"loss_paid" yaml:"loss_paid,omitempty"`
	ExpenseReserve        null.Float64 `boil:"expense_reserve" json:"expense_reserve,omitempty" toml:"expense_reserve" yaml:"expense_reserve,omitempty"`
	ExpensePaid           null.Float64 `boil:"expense_paid" json:"expense_paid,omitempty" toml:"expense_paid" yaml:"expense_paid,omitempty"`
	TotalReserve          null.Float64 `boil:"total_reserve" json:"total_reserve,omitempty" toml:"total_reserve" yaml:"total_reserve,omitempty"`
	TotalPaid             null.Float64 `boil:"total_paid" json:"total_paid,omitempty" toml:"total_paid" yaml:"total_paid,omitempty"`
	TotalRecovered        null.Float64 `boil:"total_recovered" json:"total_recovered,omitempty" toml:"total_recovered" yaml:"total_recovered,omitempty"`
	TotalIncurred         null.Float64 `boil:"total_incurred" json:"total_incurred,omitempty" toml:"total_incurred" yaml:"total_incurred,omitempty"`
	DeductibleAmount      null.Float64 `boil:"deductible_amount" json:"deductible_amount,omitempty" toml:"deductible_amount" yaml:"deductible_amount,omitempty"`
	SubrogationAmount     null.Float64 `boil:"subrogation_amount" json:"subrogation_amount,omitempty" toml:"subrogation_amount" yaml:"subrogation_amount,omitempty"`
	SalvageAmount         null.Float64 `boil:"salvage_amount" json:"salvage_amount,omitempty" toml:"salvage_amount" yaml:"salvage_amount,omitempty"`
	OtherRecovery         null.Float64 `boil:"other_recovery" json:"other_recovery,omitempty" toml:"other_recovery" yaml:"other_recovery,omitempty"`
	LegalReserve          null.Float64 `boil:"legal_reserve" json:"legal_reserve,omitempty" toml:"legal_reserve" yaml:"legal_reserve,omitempty"`
	LegalPaid             null.Float64 `boil:"legal_paid" json:"legal_paid,omitempty" toml:"legal_paid" yaml:"legal_paid,omitempty"`
	LegalIncurred         null.Float64 `boil:"legal_incurred" json:"legal_incurred,omitempty" toml:"legal_incurred" yaml:"legal_incurred,omitempty"`
	AlaeExpenseReserve    null.Float64 `boil:"alae_expense_reserve" json:"alae_expense_reserve,omitempty" toml:"alae_expense_reserve" yaml:"alae_expense_reserve,omitempty"`
	AlaePaid              null.Float64 `boil:"alae_paid" json:"alae_paid,omitempty" toml:"alae_paid" yaml:"alae_paid,omitempty"`
	AlaeIncurred          null.Float64 `boil:"alae_incurred" json:"alae_incurred,omitempty" toml:"alae_incurred" yaml:"alae_incurred,omitempty"`
	OtherExpenseReserve   null.Float64 `boil:"other_expense_reserve" json:"other_expense_reserve,omitempty" toml:"other_expense_reserve" yaml:"other_expense_reserve,omitempty"`
	OtherExpensePaid      null.Float64 `boil:"other_expense_paid" json:"other_expense_paid,omitempty" toml:"other_expense_paid" yaml:"other_expense_paid,omitempty"`
	OtherExpenseIncurred  null.Float64 `boil:"other_expense_incurred" json:"other_expense_incurred,omitempty" toml:"other_expense_incurred" yaml:"other_expense_incurred,omitempty"`

	R *lossR `boil:"" json:"" toml:"" yaml:""`
	L lossL  `boil:"-" json:"-" toml:"-" yaml:"-"`
}

var LossColumns = struct {
	ID                    string
	DocumentID            string
	PolicySN              string
	CreatedAt             string
	ClaimSN               string
	LossSeqID             string
	Claimant              string
	CoverageInferred      string
	ClaimLossType         string
	ClaimLossTypeInferred string
	Examiner              string
	LossReserved          string
	LossPaid              string
	ExpenseReserve        string
	ExpensePaid           string
	TotalReserve          string
	TotalPaid             string
	TotalRecovered        string
	TotalIncurred         string
	DeductibleAmount      string
	SubrogationAmount     string
	SalvageAmount         string
	OtherRecovery         string
	LegalReserve          string
	LegalPaid             string
	LegalIncurred         string
	AlaeExpenseReserve    string
	AlaePaid              string
	AlaeIncurred          string
	OtherExpenseReserve   string
	OtherExpensePaid      string
	OtherExpenseIncurred  string
}{
	ID:                    "id",
	DocumentID:            "document_id",
	PolicySN:              "policy_sn",
	CreatedAt:             "created_at",
	ClaimSN:               "claim_sn",
	LossSeqID:             "loss_seq_id",
	Claimant:              "claimant",
	CoverageInferred:      "coverage_inferred",
	ClaimLossType:         "claim_loss_type",
	ClaimLossTypeInferred: "claim_loss_type_inferred",
	Examiner:              "examiner",
	LossReserved:          "loss_reserved",
	LossPaid:              "loss_paid",
	ExpenseReserve:        "expense_reserve",
	ExpensePaid:           "expense_paid",
	TotalReserve:          "total_reserve",
	TotalPaid:             "total_paid",
	TotalRecovered:        "total_recovered",
	TotalIncurred:         "total_incurred",
	DeductibleAmount:      "deductible_amount",
	SubrogationAmount:     "subrogation_amount",
	SalvageAmount:         "salvage_amount",
	OtherRecovery:         "other_recovery",
	LegalReserve:          "legal_reserve",
	LegalPaid:             "legal_paid",
	LegalIncurred:         "legal_incurred",
	AlaeExpenseReserve:    "alae_expense_reserve",
	AlaePaid:              "alae_paid",
	AlaeIncurred:          "alae_incurred",
	OtherExpenseReserve:   "other_expense_reserve",
	OtherExpensePaid:      "other_expense_paid",
	OtherExpenseIncurred:  "other_expense_incurred",
}

var LossTableColumns = struct {
	ID                    string
	DocumentID            string
	PolicySN              string
	CreatedAt             string
	ClaimSN               string
	LossSeqID             string
	Claimant              string
	CoverageInferred      string
	ClaimLossType         string
	ClaimLossTypeInferred string
	Examiner              string
	LossReserved          string
	LossPaid              string
	ExpenseReserve        string
	ExpensePaid           string
	TotalReserve          string
	TotalPaid             string
	TotalRecovered        string
	TotalIncurred         string
	DeductibleAmount      string
	SubrogationAmount     string
	SalvageAmount         string
	OtherRecovery         string
	LegalReserve          string
	LegalPaid             string
	LegalIncurred         string
	AlaeExpenseReserve    string
	AlaePaid              string
	AlaeIncurred          string
	OtherExpenseReserve   string
	OtherExpensePaid      string
	OtherExpenseIncurred  string
}{
	ID:                    "loss.id",
	DocumentID:            "loss.document_id",
	PolicySN:              "loss.policy_sn",
	CreatedAt:             "loss.created_at",
	ClaimSN:               "loss.claim_sn",
	LossSeqID:             "loss.loss_seq_id",
	Claimant:              "loss.claimant",
	CoverageInferred:      "loss.coverage_inferred",
	ClaimLossType:         "loss.claim_loss_type",
	ClaimLossTypeInferred: "loss.claim_loss_type_inferred",
	Examiner:              "loss.examiner",
	LossReserved:          "loss.loss_reserved",
	LossPaid:              "loss.loss_paid",
	ExpenseReserve:        "loss.expense_reserve",
	ExpensePaid:           "loss.expense_paid",
	TotalReserve:          "loss.total_reserve",
	TotalPaid:             "loss.total_paid",
	TotalRecovered:        "loss.total_recovered",
	TotalIncurred:         "loss.total_incurred",
	DeductibleAmount:      "loss.deductible_amount",
	SubrogationAmount:     "loss.subrogation_amount",
	SalvageAmount:         "loss.salvage_amount",
	OtherRecovery:         "loss.other_recovery",
	LegalReserve:          "loss.legal_reserve",
	LegalPaid:             "loss.legal_paid",
	LegalIncurred:         "loss.legal_incurred",
	AlaeExpenseReserve:    "loss.alae_expense_reserve",
	AlaePaid:              "loss.alae_paid",
	AlaeIncurred:          "loss.alae_incurred",
	OtherExpenseReserve:   "loss.other_expense_reserve",
	OtherExpensePaid:      "loss.other_expense_paid",
	OtherExpenseIncurred:  "loss.other_expense_incurred",
}

// Generated where

type whereHelpernull_Float64 struct{ field string }

func (w whereHelpernull_Float64) EQ(x null.Float64) qm.QueryMod {
	return qmhelper.WhereNullEQ(w.field, false, x)
}
func (w whereHelpernull_Float64) NEQ(x null.Float64) qm.QueryMod {
	return qmhelper.WhereNullEQ(w.field, true, x)
}
func (w whereHelpernull_Float64) LT(x null.Float64) qm.QueryMod {
	return qmhelper.Where(w.field, qmhelper.LT, x)
}
func (w whereHelpernull_Float64) LTE(x null.Float64) qm.QueryMod {
	return qmhelper.Where(w.field, qmhelper.LTE, x)
}
func (w whereHelpernull_Float64) GT(x null.Float64) qm.QueryMod {
	return qmhelper.Where(w.field, qmhelper.GT, x)
}
func (w whereHelpernull_Float64) GTE(x null.Float64) qm.QueryMod {
	return qmhelper.Where(w.field, qmhelper.GTE, x)
}

func (w whereHelpernull_Float64) IsNull() qm.QueryMod    { return qmhelper.WhereIsNull(w.field) }
func (w whereHelpernull_Float64) IsNotNull() qm.QueryMod { return qmhelper.WhereIsNotNull(w.field) }

var LossWhere = struct {
	ID                    whereHelperstring
	DocumentID            whereHelperstring
	PolicySN              whereHelperint
	CreatedAt             whereHelpertime_Time
	ClaimSN               whereHelperint
	LossSeqID             whereHelperstring
	Claimant              whereHelpernull_String
	CoverageInferred      whereHelpernull_String
	ClaimLossType         whereHelpernull_String
	ClaimLossTypeInferred whereHelpernull_String
	Examiner              whereHelpernull_String
	LossReserved          whereHelpernull_Float64
	LossPaid              whereHelpernull_Float64
	ExpenseReserve        whereHelpernull_Float64
	ExpensePaid           whereHelpernull_Float64
	TotalReserve          whereHelpernull_Float64
	TotalPaid             whereHelpernull_Float64
	TotalRecovered        whereHelpernull_Float64
	TotalIncurred         whereHelpernull_Float64
	DeductibleAmount      whereHelpernull_Float64
	SubrogationAmount     whereHelpernull_Float64
	SalvageAmount         whereHelpernull_Float64
	OtherRecovery         whereHelpernull_Float64
	LegalReserve          whereHelpernull_Float64
	LegalPaid             whereHelpernull_Float64
	LegalIncurred         whereHelpernull_Float64
	AlaeExpenseReserve    whereHelpernull_Float64
	AlaePaid              whereHelpernull_Float64
	AlaeIncurred          whereHelpernull_Float64
	OtherExpenseReserve   whereHelpernull_Float64
	OtherExpensePaid      whereHelpernull_Float64
	OtherExpenseIncurred  whereHelpernull_Float64
}{
	ID:                    whereHelperstring{field: "\"parsed_loss_runs\".\"loss\".\"id\""},
	DocumentID:            whereHelperstring{field: "\"parsed_loss_runs\".\"loss\".\"document_id\""},
	PolicySN:              whereHelperint{field: "\"parsed_loss_runs\".\"loss\".\"policy_sn\""},
	CreatedAt:             whereHelpertime_Time{field: "\"parsed_loss_runs\".\"loss\".\"created_at\""},
	ClaimSN:               whereHelperint{field: "\"parsed_loss_runs\".\"loss\".\"claim_sn\""},
	LossSeqID:             whereHelperstring{field: "\"parsed_loss_runs\".\"loss\".\"loss_seq_id\""},
	Claimant:              whereHelpernull_String{field: "\"parsed_loss_runs\".\"loss\".\"claimant\""},
	CoverageInferred:      whereHelpernull_String{field: "\"parsed_loss_runs\".\"loss\".\"coverage_inferred\""},
	ClaimLossType:         whereHelpernull_String{field: "\"parsed_loss_runs\".\"loss\".\"claim_loss_type\""},
	ClaimLossTypeInferred: whereHelpernull_String{field: "\"parsed_loss_runs\".\"loss\".\"claim_loss_type_inferred\""},
	Examiner:              whereHelpernull_String{field: "\"parsed_loss_runs\".\"loss\".\"examiner\""},
	LossReserved:          whereHelpernull_Float64{field: "\"parsed_loss_runs\".\"loss\".\"loss_reserved\""},
	LossPaid:              whereHelpernull_Float64{field: "\"parsed_loss_runs\".\"loss\".\"loss_paid\""},
	ExpenseReserve:        whereHelpernull_Float64{field: "\"parsed_loss_runs\".\"loss\".\"expense_reserve\""},
	ExpensePaid:           whereHelpernull_Float64{field: "\"parsed_loss_runs\".\"loss\".\"expense_paid\""},
	TotalReserve:          whereHelpernull_Float64{field: "\"parsed_loss_runs\".\"loss\".\"total_reserve\""},
	TotalPaid:             whereHelpernull_Float64{field: "\"parsed_loss_runs\".\"loss\".\"total_paid\""},
	TotalRecovered:        whereHelpernull_Float64{field: "\"parsed_loss_runs\".\"loss\".\"total_recovered\""},
	TotalIncurred:         whereHelpernull_Float64{field: "\"parsed_loss_runs\".\"loss\".\"total_incurred\""},
	DeductibleAmount:      whereHelpernull_Float64{field: "\"parsed_loss_runs\".\"loss\".\"deductible_amount\""},
	SubrogationAmount:     whereHelpernull_Float64{field: "\"parsed_loss_runs\".\"loss\".\"subrogation_amount\""},
	SalvageAmount:         whereHelpernull_Float64{field: "\"parsed_loss_runs\".\"loss\".\"salvage_amount\""},
	OtherRecovery:         whereHelpernull_Float64{field: "\"parsed_loss_runs\".\"loss\".\"other_recovery\""},
	LegalReserve:          whereHelpernull_Float64{field: "\"parsed_loss_runs\".\"loss\".\"legal_reserve\""},
	LegalPaid:             whereHelpernull_Float64{field: "\"parsed_loss_runs\".\"loss\".\"legal_paid\""},
	LegalIncurred:         whereHelpernull_Float64{field: "\"parsed_loss_runs\".\"loss\".\"legal_incurred\""},
	AlaeExpenseReserve:    whereHelpernull_Float64{field: "\"parsed_loss_runs\".\"loss\".\"alae_expense_reserve\""},
	AlaePaid:              whereHelpernull_Float64{field: "\"parsed_loss_runs\".\"loss\".\"alae_paid\""},
	AlaeIncurred:          whereHelpernull_Float64{field: "\"parsed_loss_runs\".\"loss\".\"alae_incurred\""},
	OtherExpenseReserve:   whereHelpernull_Float64{field: "\"parsed_loss_runs\".\"loss\".\"other_expense_reserve\""},
	OtherExpensePaid:      whereHelpernull_Float64{field: "\"parsed_loss_runs\".\"loss\".\"other_expense_paid\""},
	OtherExpenseIncurred:  whereHelpernull_Float64{field: "\"parsed_loss_runs\".\"loss\".\"other_expense_incurred\""},
}

// LossRels is where relationship names are stored.
var LossRels = struct {
	Document string
}{
	Document: "Document",
}

// lossR is where relationships are stored.
type lossR struct {
	Document *Document `boil:"Document" json:"Document" toml:"Document" yaml:"Document"`
}

// NewStruct creates a new relationship struct
func (*lossR) NewStruct() *lossR {
	return &lossR{}
}

// lossL is where Load methods for each relationship are stored.
type lossL struct{}

var (
	lossAllColumns            = []string{"id", "document_id", "policy_sn", "created_at", "claim_sn", "loss_seq_id", "claimant", "coverage_inferred", "claim_loss_type", "claim_loss_type_inferred", "examiner", "loss_reserved", "loss_paid", "expense_reserve", "expense_paid", "total_reserve", "total_paid", "total_recovered", "total_incurred", "deductible_amount", "subrogation_amount", "salvage_amount", "other_recovery", "legal_reserve", "legal_paid", "legal_incurred", "alae_expense_reserve", "alae_paid", "alae_incurred", "other_expense_reserve", "other_expense_paid", "other_expense_incurred"}
	lossColumnsWithoutDefault = []string{"id", "document_id", "policy_sn", "created_at", "claim_sn", "loss_seq_id"}
	lossColumnsWithDefault    = []string{"claimant", "coverage_inferred", "claim_loss_type", "claim_loss_type_inferred", "examiner", "loss_reserved", "loss_paid", "expense_reserve", "expense_paid", "total_reserve", "total_paid", "total_recovered", "total_incurred", "deductible_amount", "subrogation_amount", "salvage_amount", "other_recovery", "legal_reserve", "legal_paid", "legal_incurred", "alae_expense_reserve", "alae_paid", "alae_incurred", "other_expense_reserve", "other_expense_paid", "other_expense_incurred"}
	lossPrimaryKeyColumns     = []string{"id"}
	lossGeneratedColumns      = []string{}
)

type (
	// LossSlice is an alias for a slice of pointers to Loss.
	// This should almost always be used instead of []Loss.
	LossSlice []*Loss
	// LossHook is the signature for custom Loss hook methods
	LossHook func(context.Context, boil.ContextExecutor, *Loss) error

	lossQuery struct {
		*queries.Query
	}
)

// Cache for insert, update and upsert
var (
	lossType                 = reflect.TypeOf(&Loss{})
	lossMapping              = queries.MakeStructMapping(lossType)
	lossPrimaryKeyMapping, _ = queries.BindMapping(lossType, lossMapping, lossPrimaryKeyColumns)
	lossInsertCacheMut       sync.RWMutex
	lossInsertCache          = make(map[string]insertCache)
	lossUpdateCacheMut       sync.RWMutex
	lossUpdateCache          = make(map[string]updateCache)
	lossUpsertCacheMut       sync.RWMutex
	lossUpsertCache          = make(map[string]insertCache)
)

var (
	// Force time package dependency for automated UpdatedAt/CreatedAt.
	_ = time.Second
	// Force qmhelper dependency for where clause generation (which doesn't
	// always happen)
	_ = qmhelper.Where
)

var lossAfterSelectHooks []LossHook

var lossBeforeInsertHooks []LossHook
var lossAfterInsertHooks []LossHook

var lossBeforeUpdateHooks []LossHook
var lossAfterUpdateHooks []LossHook

var lossBeforeDeleteHooks []LossHook
var lossAfterDeleteHooks []LossHook

var lossBeforeUpsertHooks []LossHook
var lossAfterUpsertHooks []LossHook

// doAfterSelectHooks executes all "after Select" hooks.
func (o *Loss) doAfterSelectHooks(ctx context.Context, exec boil.ContextExecutor) (err error) {
	if boil.HooksAreSkipped(ctx) {
		return nil
	}

	for _, hook := range lossAfterSelectHooks {
		if err := hook(ctx, exec, o); err != nil {
			return err
		}
	}

	return nil
}

// doBeforeInsertHooks executes all "before insert" hooks.
func (o *Loss) doBeforeInsertHooks(ctx context.Context, exec boil.ContextExecutor) (err error) {
	if boil.HooksAreSkipped(ctx) {
		return nil
	}

	for _, hook := range lossBeforeInsertHooks {
		if err := hook(ctx, exec, o); err != nil {
			return err
		}
	}

	return nil
}

// doAfterInsertHooks executes all "after Insert" hooks.
func (o *Loss) doAfterInsertHooks(ctx context.Context, exec boil.ContextExecutor) (err error) {
	if boil.HooksAreSkipped(ctx) {
		return nil
	}

	for _, hook := range lossAfterInsertHooks {
		if err := hook(ctx, exec, o); err != nil {
			return err
		}
	}

	return nil
}

// doBeforeUpdateHooks executes all "before Update" hooks.
func (o *Loss) doBeforeUpdateHooks(ctx context.Context, exec boil.ContextExecutor) (err error) {
	if boil.HooksAreSkipped(ctx) {
		return nil
	}

	for _, hook := range lossBeforeUpdateHooks {
		if err := hook(ctx, exec, o); err != nil {
			return err
		}
	}

	return nil
}

// doAfterUpdateHooks executes all "after Update" hooks.
func (o *Loss) doAfterUpdateHooks(ctx context.Context, exec boil.ContextExecutor) (err error) {
	if boil.HooksAreSkipped(ctx) {
		return nil
	}

	for _, hook := range lossAfterUpdateHooks {
		if err := hook(ctx, exec, o); err != nil {
			return err
		}
	}

	return nil
}

// doBeforeDeleteHooks executes all "before Delete" hooks.
func (o *Loss) doBeforeDeleteHooks(ctx context.Context, exec boil.ContextExecutor) (err error) {
	if boil.HooksAreSkipped(ctx) {
		return nil
	}

	for _, hook := range lossBeforeDeleteHooks {
		if err := hook(ctx, exec, o); err != nil {
			return err
		}
	}

	return nil
}

// doAfterDeleteHooks executes all "after Delete" hooks.
func (o *Loss) doAfterDeleteHooks(ctx context.Context, exec boil.ContextExecutor) (err error) {
	if boil.HooksAreSkipped(ctx) {
		return nil
	}

	for _, hook := range lossAfterDeleteHooks {
		if err := hook(ctx, exec, o); err != nil {
			return err
		}
	}

	return nil
}

// doBeforeUpsertHooks executes all "before Upsert" hooks.
func (o *Loss) doBeforeUpsertHooks(ctx context.Context, exec boil.ContextExecutor) (err error) {
	if boil.HooksAreSkipped(ctx) {
		return nil
	}

	for _, hook := range lossBeforeUpsertHooks {
		if err := hook(ctx, exec, o); err != nil {
			return err
		}
	}

	return nil
}

// doAfterUpsertHooks executes all "after Upsert" hooks.
func (o *Loss) doAfterUpsertHooks(ctx context.Context, exec boil.ContextExecutor) (err error) {
	if boil.HooksAreSkipped(ctx) {
		return nil
	}

	for _, hook := range lossAfterUpsertHooks {
		if err := hook(ctx, exec, o); err != nil {
			return err
		}
	}

	return nil
}

// AddLossHook registers your hook function for all future operations.
func AddLossHook(hookPoint boil.HookPoint, lossHook LossHook) {
	switch hookPoint {
	case boil.AfterSelectHook:
		lossAfterSelectHooks = append(lossAfterSelectHooks, lossHook)
	case boil.BeforeInsertHook:
		lossBeforeInsertHooks = append(lossBeforeInsertHooks, lossHook)
	case boil.AfterInsertHook:
		lossAfterInsertHooks = append(lossAfterInsertHooks, lossHook)
	case boil.BeforeUpdateHook:
		lossBeforeUpdateHooks = append(lossBeforeUpdateHooks, lossHook)
	case boil.AfterUpdateHook:
		lossAfterUpdateHooks = append(lossAfterUpdateHooks, lossHook)
	case boil.BeforeDeleteHook:
		lossBeforeDeleteHooks = append(lossBeforeDeleteHooks, lossHook)
	case boil.AfterDeleteHook:
		lossAfterDeleteHooks = append(lossAfterDeleteHooks, lossHook)
	case boil.BeforeUpsertHook:
		lossBeforeUpsertHooks = append(lossBeforeUpsertHooks, lossHook)
	case boil.AfterUpsertHook:
		lossAfterUpsertHooks = append(lossAfterUpsertHooks, lossHook)
	}
}

// One returns a single loss record from the query.
func (q lossQuery) One(ctx context.Context, exec boil.ContextExecutor) (*Loss, error) {
	o := &Loss{}

	queries.SetLimit(q.Query, 1)

	err := q.Bind(ctx, exec, o)
	if err != nil {
		if errors.Is(err, sql.ErrNoRows) {
			return nil, sql.ErrNoRows
		}
		return nil, errors.Wrap(err, "parsed_loss_runs: failed to execute a one query for loss")
	}

	if err := o.doAfterSelectHooks(ctx, exec); err != nil {
		return o, err
	}

	return o, nil
}

// All returns all Loss records from the query.
func (q lossQuery) All(ctx context.Context, exec boil.ContextExecutor) (LossSlice, error) {
	var o []*Loss

	err := q.Bind(ctx, exec, &o)
	if err != nil {
		return nil, errors.Wrap(err, "parsed_loss_runs: failed to assign all query results to Loss slice")
	}

	if len(lossAfterSelectHooks) != 0 {
		for _, obj := range o {
			if err := obj.doAfterSelectHooks(ctx, exec); err != nil {
				return o, err
			}
		}
	}

	return o, nil
}

// Count returns the count of all Loss records in the query.
func (q lossQuery) Count(ctx context.Context, exec boil.ContextExecutor) (int64, error) {
	var count int64

	queries.SetSelect(q.Query, nil)
	queries.SetCount(q.Query)

	err := q.Query.QueryRowContext(ctx, exec).Scan(&count)
	if err != nil {
		return 0, errors.Wrap(err, "parsed_loss_runs: failed to count loss rows")
	}

	return count, nil
}

// Exists checks if the row exists in the table.
func (q lossQuery) Exists(ctx context.Context, exec boil.ContextExecutor) (bool, error) {
	var count int64

	queries.SetSelect(q.Query, nil)
	queries.SetCount(q.Query)
	queries.SetLimit(q.Query, 1)

	err := q.Query.QueryRowContext(ctx, exec).Scan(&count)
	if err != nil {
		return false, errors.Wrap(err, "parsed_loss_runs: failed to check if loss exists")
	}

	return count > 0, nil
}

// Document pointed to by the foreign key.
func (o *Loss) Document(mods ...qm.QueryMod) documentQuery {
	queryMods := []qm.QueryMod{
		qm.Where("\"id\" = ?", o.DocumentID),
	}

	queryMods = append(queryMods, mods...)

	return Documents(queryMods...)
}

// LoadDocument allows an eager lookup of values, cached into the
// loaded structs of the objects. This is for an N-1 relationship.
func (lossL) LoadDocument(ctx context.Context, e boil.ContextExecutor, singular bool, maybeLoss interface{}, mods queries.Applicator) error {
	var slice []*Loss
	var object *Loss

	if singular {
		object = maybeLoss.(*Loss)
	} else {
		slice = *maybeLoss.(*[]*Loss)
	}

	args := make([]interface{}, 0, 1)
	if singular {
		if object.R == nil {
			object.R = &lossR{}
		}
		args = append(args, object.DocumentID)

	} else {
	Outer:
		for _, obj := range slice {
			if obj.R == nil {
				obj.R = &lossR{}
			}

			for _, a := range args {
				if a == obj.DocumentID {
					continue Outer
				}
			}

			args = append(args, obj.DocumentID)

		}
	}

	if len(args) == 0 {
		return nil
	}

	query := NewQuery(
		qm.From(`parsed_loss_runs.document`),
		qm.WhereIn(`parsed_loss_runs.document.id in ?`, args...),
	)
	if mods != nil {
		mods.Apply(query)
	}

	results, err := query.QueryContext(ctx, e)
	if err != nil {
		return errors.Wrap(err, "failed to eager load Document")
	}

	var resultSlice []*Document
	if err = queries.Bind(results, &resultSlice); err != nil {
		return errors.Wrap(err, "failed to bind eager loaded slice Document")
	}

	if err = results.Close(); err != nil {
		return errors.Wrap(err, "failed to close results of eager load for document")
	}
	if err = results.Err(); err != nil {
		return errors.Wrap(err, "error occurred during iteration of eager loaded relations for document")
	}

	if len(lossAfterSelectHooks) != 0 {
		for _, obj := range resultSlice {
			if err := obj.doAfterSelectHooks(ctx, e); err != nil {
				return err
			}
		}
	}

	if len(resultSlice) == 0 {
		return nil
	}

	if singular {
		foreign := resultSlice[0]
		object.R.Document = foreign
		if foreign.R == nil {
			foreign.R = &documentR{}
		}
		foreign.R.Losses = append(foreign.R.Losses, object)
		return nil
	}

	for _, local := range slice {
		for _, foreign := range resultSlice {
			if local.DocumentID == foreign.ID {
				local.R.Document = foreign
				if foreign.R == nil {
					foreign.R = &documentR{}
				}
				foreign.R.Losses = append(foreign.R.Losses, local)
				break
			}
		}
	}

	return nil
}

// SetDocument of the loss to the related item.
// Sets o.R.Document to related.
// Adds o to related.R.Losses.
func (o *Loss) SetDocument(ctx context.Context, exec boil.ContextExecutor, insert bool, related *Document) error {
	var err error
	if insert {
		if err = related.Insert(ctx, exec, boil.Infer()); err != nil {
			return errors.Wrap(err, "failed to insert into foreign table")
		}
	}

	updateQuery := fmt.Sprintf(
		"UPDATE \"parsed_loss_runs\".\"loss\" SET %s WHERE %s",
		strmangle.SetParamNames("\"", "\"", 1, []string{"document_id"}),
		strmangle.WhereClause("\"", "\"", 2, lossPrimaryKeyColumns),
	)
	values := []interface{}{related.ID, o.ID}

	if boil.IsDebug(ctx) {
		writer := boil.DebugWriterFrom(ctx)
		fmt.Fprintln(writer, updateQuery)
		fmt.Fprintln(writer, values)
	}
	if _, err = exec.ExecContext(ctx, updateQuery, values...); err != nil {
		return errors.Wrap(err, "failed to update local table")
	}

	o.DocumentID = related.ID
	if o.R == nil {
		o.R = &lossR{
			Document: related,
		}
	} else {
		o.R.Document = related
	}

	if related.R == nil {
		related.R = &documentR{
			Losses: LossSlice{o},
		}
	} else {
		related.R.Losses = append(related.R.Losses, o)
	}

	return nil
}

// Losses retrieves all the records using an executor.
func Losses(mods ...qm.QueryMod) lossQuery {
	mods = append(mods, qm.From("\"parsed_loss_runs\".\"loss\""))
	q := NewQuery(mods...)
	if len(queries.GetSelect(q)) == 0 {
		queries.SetSelect(q, []string{"\"parsed_loss_runs\".\"loss\".*"})
	}

	return lossQuery{q}
}

// FindLoss retrieves a single record by ID with an executor.
// If selectCols is empty Find will return all columns.
func FindLoss(ctx context.Context, exec boil.ContextExecutor, iD string, selectCols ...string) (*Loss, error) {
	lossObj := &Loss{}

	sel := "*"
	if len(selectCols) > 0 {
		sel = strings.Join(strmangle.IdentQuoteSlice(dialect.LQ, dialect.RQ, selectCols), ",")
	}
	query := fmt.Sprintf(
		"select %s from \"parsed_loss_runs\".\"loss\" where \"id\"=$1", sel,
	)

	q := queries.Raw(query, iD)

	err := q.Bind(ctx, exec, lossObj)
	if err != nil {
		if errors.Is(err, sql.ErrNoRows) {
			return nil, sql.ErrNoRows
		}
		return nil, errors.Wrap(err, "parsed_loss_runs: unable to select from loss")
	}

	if err = lossObj.doAfterSelectHooks(ctx, exec); err != nil {
		return lossObj, err
	}

	return lossObj, nil
}

// Insert a single record using an executor.
// See boil.Columns.InsertColumnSet documentation to understand column list inference for inserts.
func (o *Loss) Insert(ctx context.Context, exec boil.ContextExecutor, columns boil.Columns) error {
	if o == nil {
		return errors.New("parsed_loss_runs: no loss provided for insertion")
	}

	var err error
	if !boil.TimestampsAreSkipped(ctx) {
		currTime := time.Now().In(boil.GetLocation())

		if o.CreatedAt.IsZero() {
			o.CreatedAt = currTime
		}
	}

	if err := o.doBeforeInsertHooks(ctx, exec); err != nil {
		return err
	}

	nzDefaults := queries.NonZeroDefaultSet(lossColumnsWithDefault, o)

	key := makeCacheKey(columns, nzDefaults)
	lossInsertCacheMut.RLock()
	cache, cached := lossInsertCache[key]
	lossInsertCacheMut.RUnlock()

	if !cached {
		wl, returnColumns := columns.InsertColumnSet(
			lossAllColumns,
			lossColumnsWithDefault,
			lossColumnsWithoutDefault,
			nzDefaults,
		)

		cache.valueMapping, err = queries.BindMapping(lossType, lossMapping, wl)
		if err != nil {
			return err
		}
		cache.retMapping, err = queries.BindMapping(lossType, lossMapping, returnColumns)
		if err != nil {
			return err
		}
		if len(wl) != 0 {
			cache.query = fmt.Sprintf("INSERT INTO \"parsed_loss_runs\".\"loss\" (\"%s\") %%sVALUES (%s)%%s", strings.Join(wl, "\",\""), strmangle.Placeholders(dialect.UseIndexPlaceholders, len(wl), 1, 1))
		} else {
			cache.query = "INSERT INTO \"parsed_loss_runs\".\"loss\" %sDEFAULT VALUES%s"
		}

		var queryOutput, queryReturning string

		if len(cache.retMapping) != 0 {
			queryReturning = fmt.Sprintf(" RETURNING \"%s\"", strings.Join(returnColumns, "\",\""))
		}

		cache.query = fmt.Sprintf(cache.query, queryOutput, queryReturning)
	}

	value := reflect.Indirect(reflect.ValueOf(o))
	vals := queries.ValuesFromMapping(value, cache.valueMapping)

	if boil.IsDebug(ctx) {
		writer := boil.DebugWriterFrom(ctx)
		fmt.Fprintln(writer, cache.query)
		fmt.Fprintln(writer, vals)
	}

	if len(cache.retMapping) != 0 {
		err = exec.QueryRowContext(ctx, cache.query, vals...).Scan(queries.PtrsFromMapping(value, cache.retMapping)...)
	} else {
		_, err = exec.ExecContext(ctx, cache.query, vals...)
	}

	if err != nil {
		return errors.Wrap(err, "parsed_loss_runs: unable to insert into loss")
	}

	if !cached {
		lossInsertCacheMut.Lock()
		lossInsertCache[key] = cache
		lossInsertCacheMut.Unlock()
	}

	return o.doAfterInsertHooks(ctx, exec)
}

// Update uses an executor to update the Loss.
// See boil.Columns.UpdateColumnSet documentation to understand column list inference for updates.
// Update does not automatically update the record in case of default values. Use .Reload() to refresh the records.
func (o *Loss) Update(ctx context.Context, exec boil.ContextExecutor, columns boil.Columns) (int64, error) {
	var err error
	if err = o.doBeforeUpdateHooks(ctx, exec); err != nil {
		return 0, err
	}
	key := makeCacheKey(columns, nil)
	lossUpdateCacheMut.RLock()
	cache, cached := lossUpdateCache[key]
	lossUpdateCacheMut.RUnlock()

	if !cached {
		wl := columns.UpdateColumnSet(
			lossAllColumns,
			lossPrimaryKeyColumns,
		)

		if !columns.IsWhitelist() {
			wl = strmangle.SetComplement(wl, []string{"created_at"})
		}
		if len(wl) == 0 {
			return 0, errors.New("parsed_loss_runs: unable to update loss, could not build whitelist")
		}

		cache.query = fmt.Sprintf("UPDATE \"parsed_loss_runs\".\"loss\" SET %s WHERE %s",
			strmangle.SetParamNames("\"", "\"", 1, wl),
			strmangle.WhereClause("\"", "\"", len(wl)+1, lossPrimaryKeyColumns),
		)
		cache.valueMapping, err = queries.BindMapping(lossType, lossMapping, append(wl, lossPrimaryKeyColumns...))
		if err != nil {
			return 0, err
		}
	}

	values := queries.ValuesFromMapping(reflect.Indirect(reflect.ValueOf(o)), cache.valueMapping)

	if boil.IsDebug(ctx) {
		writer := boil.DebugWriterFrom(ctx)
		fmt.Fprintln(writer, cache.query)
		fmt.Fprintln(writer, values)
	}
	var result sql.Result
	result, err = exec.ExecContext(ctx, cache.query, values...)
	if err != nil {
		return 0, errors.Wrap(err, "parsed_loss_runs: unable to update loss row")
	}

	rowsAff, err := result.RowsAffected()
	if err != nil {
		return 0, errors.Wrap(err, "parsed_loss_runs: failed to get rows affected by update for loss")
	}

	if !cached {
		lossUpdateCacheMut.Lock()
		lossUpdateCache[key] = cache
		lossUpdateCacheMut.Unlock()
	}

	return rowsAff, o.doAfterUpdateHooks(ctx, exec)
}

// UpdateAll updates all rows with the specified column values.
func (q lossQuery) UpdateAll(ctx context.Context, exec boil.ContextExecutor, cols M) (int64, error) {
	queries.SetUpdate(q.Query, cols)

	result, err := q.Query.ExecContext(ctx, exec)
	if err != nil {
		return 0, errors.Wrap(err, "parsed_loss_runs: unable to update all for loss")
	}

	rowsAff, err := result.RowsAffected()
	if err != nil {
		return 0, errors.Wrap(err, "parsed_loss_runs: unable to retrieve rows affected for loss")
	}

	return rowsAff, nil
}

// UpdateAll updates all rows with the specified column values, using an executor.
func (o LossSlice) UpdateAll(ctx context.Context, exec boil.ContextExecutor, cols M) (int64, error) {
	ln := int64(len(o))
	if ln == 0 {
		return 0, nil
	}

	if len(cols) == 0 {
		return 0, errors.New("parsed_loss_runs: update all requires at least one column argument")
	}

	colNames := make([]string, len(cols))
	args := make([]interface{}, len(cols))

	i := 0
	for name, value := range cols {
		colNames[i] = name
		args[i] = value
		i++
	}

	// Append all of the primary key values for each column
	for _, obj := range o {
		pkeyArgs := queries.ValuesFromMapping(reflect.Indirect(reflect.ValueOf(obj)), lossPrimaryKeyMapping)
		args = append(args, pkeyArgs...)
	}

	sql := fmt.Sprintf("UPDATE \"parsed_loss_runs\".\"loss\" SET %s WHERE %s",
		strmangle.SetParamNames("\"", "\"", 1, colNames),
		strmangle.WhereClauseRepeated(string(dialect.LQ), string(dialect.RQ), len(colNames)+1, lossPrimaryKeyColumns, len(o)))

	if boil.IsDebug(ctx) {
		writer := boil.DebugWriterFrom(ctx)
		fmt.Fprintln(writer, sql)
		fmt.Fprintln(writer, args...)
	}
	result, err := exec.ExecContext(ctx, sql, args...)
	if err != nil {
		return 0, errors.Wrap(err, "parsed_loss_runs: unable to update all in loss slice")
	}

	rowsAff, err := result.RowsAffected()
	if err != nil {
		return 0, errors.Wrap(err, "parsed_loss_runs: unable to retrieve rows affected all in update all loss")
	}
	return rowsAff, nil
}

// Upsert attempts an insert using an executor, and does an update or ignore on conflict.
// See boil.Columns documentation for how to properly use updateColumns and insertColumns.
func (o *Loss) Upsert(ctx context.Context, exec boil.ContextExecutor, updateOnConflict bool, conflictColumns []string, updateColumns, insertColumns boil.Columns) error {
	if o == nil {
		return errors.New("parsed_loss_runs: no loss provided for upsert")
	}
	if !boil.TimestampsAreSkipped(ctx) {
		currTime := time.Now().In(boil.GetLocation())

		if o.CreatedAt.IsZero() {
			o.CreatedAt = currTime
		}
	}

	if err := o.doBeforeUpsertHooks(ctx, exec); err != nil {
		return err
	}

	nzDefaults := queries.NonZeroDefaultSet(lossColumnsWithDefault, o)

	// Build cache key in-line uglily - mysql vs psql problems
	buf := strmangle.GetBuffer()
	if updateOnConflict {
		buf.WriteByte('t')
	} else {
		buf.WriteByte('f')
	}
	buf.WriteByte('.')
	for _, c := range conflictColumns {
		buf.WriteString(c)
	}
	buf.WriteByte('.')
	buf.WriteString(strconv.Itoa(updateColumns.Kind))
	for _, c := range updateColumns.Cols {
		buf.WriteString(c)
	}
	buf.WriteByte('.')
	buf.WriteString(strconv.Itoa(insertColumns.Kind))
	for _, c := range insertColumns.Cols {
		buf.WriteString(c)
	}
	buf.WriteByte('.')
	for _, c := range nzDefaults {
		buf.WriteString(c)
	}
	key := buf.String()
	strmangle.PutBuffer(buf)

	lossUpsertCacheMut.RLock()
	cache, cached := lossUpsertCache[key]
	lossUpsertCacheMut.RUnlock()

	var err error

	if !cached {
		insert, ret := insertColumns.InsertColumnSet(
			lossAllColumns,
			lossColumnsWithDefault,
			lossColumnsWithoutDefault,
			nzDefaults,
		)

		update := updateColumns.UpdateColumnSet(
			lossAllColumns,
			lossPrimaryKeyColumns,
		)

		if updateOnConflict && len(update) == 0 {
			return errors.New("parsed_loss_runs: unable to upsert loss, could not build update column list")
		}

		conflict := conflictColumns
		if len(conflict) == 0 {
			conflict = make([]string, len(lossPrimaryKeyColumns))
			copy(conflict, lossPrimaryKeyColumns)
		}
		cache.query = buildUpsertQueryPostgres(dialect, "\"parsed_loss_runs\".\"loss\"", updateOnConflict, ret, update, conflict, insert)

		cache.valueMapping, err = queries.BindMapping(lossType, lossMapping, insert)
		if err != nil {
			return err
		}
		if len(ret) != 0 {
			cache.retMapping, err = queries.BindMapping(lossType, lossMapping, ret)
			if err != nil {
				return err
			}
		}
	}

	value := reflect.Indirect(reflect.ValueOf(o))
	vals := queries.ValuesFromMapping(value, cache.valueMapping)
	var returns []interface{}
	if len(cache.retMapping) != 0 {
		returns = queries.PtrsFromMapping(value, cache.retMapping)
	}

	if boil.IsDebug(ctx) {
		writer := boil.DebugWriterFrom(ctx)
		fmt.Fprintln(writer, cache.query)
		fmt.Fprintln(writer, vals)
	}
	if len(cache.retMapping) != 0 {
		err = exec.QueryRowContext(ctx, cache.query, vals...).Scan(returns...)
		if errors.Is(err, sql.ErrNoRows) {
			err = nil // Postgres doesn't return anything when there's no update
		}
	} else {
		_, err = exec.ExecContext(ctx, cache.query, vals...)
	}
	if err != nil {
		return errors.Wrap(err, "parsed_loss_runs: unable to upsert loss")
	}

	if !cached {
		lossUpsertCacheMut.Lock()
		lossUpsertCache[key] = cache
		lossUpsertCacheMut.Unlock()
	}

	return o.doAfterUpsertHooks(ctx, exec)
}

// Delete deletes a single Loss record with an executor.
// Delete will match against the primary key column to find the record to delete.
func (o *Loss) Delete(ctx context.Context, exec boil.ContextExecutor) (int64, error) {
	if o == nil {
		return 0, errors.New("parsed_loss_runs: no Loss provided for delete")
	}

	if err := o.doBeforeDeleteHooks(ctx, exec); err != nil {
		return 0, err
	}

	args := queries.ValuesFromMapping(reflect.Indirect(reflect.ValueOf(o)), lossPrimaryKeyMapping)
	sql := "DELETE FROM \"parsed_loss_runs\".\"loss\" WHERE \"id\"=$1"

	if boil.IsDebug(ctx) {
		writer := boil.DebugWriterFrom(ctx)
		fmt.Fprintln(writer, sql)
		fmt.Fprintln(writer, args...)
	}
	result, err := exec.ExecContext(ctx, sql, args...)
	if err != nil {
		return 0, errors.Wrap(err, "parsed_loss_runs: unable to delete from loss")
	}

	rowsAff, err := result.RowsAffected()
	if err != nil {
		return 0, errors.Wrap(err, "parsed_loss_runs: failed to get rows affected by delete for loss")
	}

	if err := o.doAfterDeleteHooks(ctx, exec); err != nil {
		return 0, err
	}

	return rowsAff, nil
}

// DeleteAll deletes all matching rows.
func (q lossQuery) DeleteAll(ctx context.Context, exec boil.ContextExecutor) (int64, error) {
	if q.Query == nil {
		return 0, errors.New("parsed_loss_runs: no lossQuery provided for delete all")
	}

	queries.SetDelete(q.Query)

	result, err := q.Query.ExecContext(ctx, exec)
	if err != nil {
		return 0, errors.Wrap(err, "parsed_loss_runs: unable to delete all from loss")
	}

	rowsAff, err := result.RowsAffected()
	if err != nil {
		return 0, errors.Wrap(err, "parsed_loss_runs: failed to get rows affected by deleteall for loss")
	}

	return rowsAff, nil
}

// DeleteAll deletes all rows in the slice, using an executor.
func (o LossSlice) DeleteAll(ctx context.Context, exec boil.ContextExecutor) (int64, error) {
	if len(o) == 0 {
		return 0, nil
	}

	if len(lossBeforeDeleteHooks) != 0 {
		for _, obj := range o {
			if err := obj.doBeforeDeleteHooks(ctx, exec); err != nil {
				return 0, err
			}
		}
	}

	var args []interface{}
	for _, obj := range o {
		pkeyArgs := queries.ValuesFromMapping(reflect.Indirect(reflect.ValueOf(obj)), lossPrimaryKeyMapping)
		args = append(args, pkeyArgs...)
	}

	sql := "DELETE FROM \"parsed_loss_runs\".\"loss\" WHERE " +
		strmangle.WhereClauseRepeated(string(dialect.LQ), string(dialect.RQ), 1, lossPrimaryKeyColumns, len(o))

	if boil.IsDebug(ctx) {
		writer := boil.DebugWriterFrom(ctx)
		fmt.Fprintln(writer, sql)
		fmt.Fprintln(writer, args)
	}
	result, err := exec.ExecContext(ctx, sql, args...)
	if err != nil {
		return 0, errors.Wrap(err, "parsed_loss_runs: unable to delete all from loss slice")
	}

	rowsAff, err := result.RowsAffected()
	if err != nil {
		return 0, errors.Wrap(err, "parsed_loss_runs: failed to get rows affected by deleteall for loss")
	}

	if len(lossAfterDeleteHooks) != 0 {
		for _, obj := range o {
			if err := obj.doAfterDeleteHooks(ctx, exec); err != nil {
				return 0, err
			}
		}
	}

	return rowsAff, nil
}

// Reload refetches the object from the database
// using the primary keys with an executor.
func (o *Loss) Reload(ctx context.Context, exec boil.ContextExecutor) error {
	ret, err := FindLoss(ctx, exec, o.ID)
	if err != nil {
		return err
	}

	*o = *ret
	return nil
}

// ReloadAll refetches every row with matching primary key column values
// and overwrites the original object slice with the newly updated slice.
func (o *LossSlice) ReloadAll(ctx context.Context, exec boil.ContextExecutor) error {
	if o == nil || len(*o) == 0 {
		return nil
	}

	slice := LossSlice{}
	var args []interface{}
	for _, obj := range *o {
		pkeyArgs := queries.ValuesFromMapping(reflect.Indirect(reflect.ValueOf(obj)), lossPrimaryKeyMapping)
		args = append(args, pkeyArgs...)
	}

	sql := "SELECT \"parsed_loss_runs\".\"loss\".* FROM \"parsed_loss_runs\".\"loss\" WHERE " +
		strmangle.WhereClauseRepeated(string(dialect.LQ), string(dialect.RQ), 1, lossPrimaryKeyColumns, len(*o))

	q := queries.Raw(sql, args...)

	err := q.Bind(ctx, exec, &slice)
	if err != nil {
		return errors.Wrap(err, "parsed_loss_runs: unable to reload all in LossSlice")
	}

	*o = slice

	return nil
}

// LossExists checks if the Loss row exists.
func LossExists(ctx context.Context, exec boil.ContextExecutor, iD string) (bool, error) {
	var exists bool
	sql := "select exists(select 1 from \"parsed_loss_runs\".\"loss\" where \"id\"=$1 limit 1)"

	if boil.IsDebug(ctx) {
		writer := boil.DebugWriterFrom(ctx)
		fmt.Fprintln(writer, sql)
		fmt.Fprintln(writer, iD)
	}
	row := exec.QueryRowContext(ctx, sql, iD)

	err := row.Scan(&exists)
	if err != nil {
		return false, errors.Wrap(err, "parsed_loss_runs: unable to check if loss exists")
	}

	return exists, nil
}
