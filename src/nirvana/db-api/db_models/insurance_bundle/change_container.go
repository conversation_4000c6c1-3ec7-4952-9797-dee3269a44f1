// Code generated by SQLBoiler (https://github.com/volatiletech/sqlboiler). DO NOT EDIT.
// This file is meant to be re-generated in place and/or deleted at any time.

package insurance_bundle

import (
	"context"
	"database/sql"
	"fmt"
	"reflect"
	"strconv"
	"strings"
	"sync"
	"time"

	"github.com/friendsofgo/errors"
	"github.com/volatiletech/null/v8"
	"github.com/volatiletech/sqlboiler/v4/boil"
	"github.com/volatiletech/sqlboiler/v4/queries"
	"github.com/volatiletech/sqlboiler/v4/queries/qm"
	"github.com/volatiletech/sqlboiler/v4/queries/qmhelper"
	"github.com/volatiletech/strmangle"
)

// ChangeContainer is an object representing the database table.
type ChangeContainer struct {
	ID                   string    `boil:"id" json:"id" toml:"id" yaml:"id"`
	EndorsementRequestID string    `boil:"endorsement_request_id" json:"endorsement_request_id" toml:"endorsement_request_id" yaml:"endorsement_request_id"`
	Charges              null.JSON `boil:"charges" json:"charges,omitempty" toml:"charges" yaml:"charges,omitempty"`
	EffectiveInterval    string    `boil:"effective_interval" json:"effective_interval" toml:"effective_interval" yaml:"effective_interval"`
	CreatedAt            null.Time `boil:"created_at" json:"created_at,omitempty" toml:"created_at" yaml:"created_at,omitempty"`
	UpdatedAt            null.Time `boil:"updated_at" json:"updated_at,omitempty" toml:"updated_at" yaml:"updated_at,omitempty"`
	FormChanges          null.JSON `boil:"form_changes" json:"form_changes,omitempty" toml:"form_changes" yaml:"form_changes,omitempty"`

	R *changeContainerR `boil:"" json:"" toml:"" yaml:""`
	L changeContainerL  `boil:"-" json:"-" toml:"-" yaml:"-"`
}

var ChangeContainerColumns = struct {
	ID                   string
	EndorsementRequestID string
	Charges              string
	EffectiveInterval    string
	CreatedAt            string
	UpdatedAt            string
	FormChanges          string
}{
	ID:                   "id",
	EndorsementRequestID: "endorsement_request_id",
	Charges:              "charges",
	EffectiveInterval:    "effective_interval",
	CreatedAt:            "created_at",
	UpdatedAt:            "updated_at",
	FormChanges:          "form_changes",
}

var ChangeContainerTableColumns = struct {
	ID                   string
	EndorsementRequestID string
	Charges              string
	EffectiveInterval    string
	CreatedAt            string
	UpdatedAt            string
	FormChanges          string
}{
	ID:                   "change_container.id",
	EndorsementRequestID: "change_container.endorsement_request_id",
	Charges:              "change_container.charges",
	EffectiveInterval:    "change_container.effective_interval",
	CreatedAt:            "change_container.created_at",
	UpdatedAt:            "change_container.updated_at",
	FormChanges:          "change_container.form_changes",
}

// Generated where

var ChangeContainerWhere = struct {
	ID                   whereHelperstring
	EndorsementRequestID whereHelperstring
	Charges              whereHelpernull_JSON
	EffectiveInterval    whereHelperstring
	CreatedAt            whereHelpernull_Time
	UpdatedAt            whereHelpernull_Time
	FormChanges          whereHelpernull_JSON
}{
	ID:                   whereHelperstring{field: "\"insurance_bundle\".\"change_container\".\"id\""},
	EndorsementRequestID: whereHelperstring{field: "\"insurance_bundle\".\"change_container\".\"endorsement_request_id\""},
	Charges:              whereHelpernull_JSON{field: "\"insurance_bundle\".\"change_container\".\"charges\""},
	EffectiveInterval:    whereHelperstring{field: "\"insurance_bundle\".\"change_container\".\"effective_interval\""},
	CreatedAt:            whereHelpernull_Time{field: "\"insurance_bundle\".\"change_container\".\"created_at\""},
	UpdatedAt:            whereHelpernull_Time{field: "\"insurance_bundle\".\"change_container\".\"updated_at\""},
	FormChanges:          whereHelpernull_JSON{field: "\"insurance_bundle\".\"change_container\".\"form_changes\""},
}

// ChangeContainerRels is where relationship names are stored.
var ChangeContainerRels = struct {
	EndorsementRequest string
	Changes            string
}{
	EndorsementRequest: "EndorsementRequest",
	Changes:            "Changes",
}

// changeContainerR is where relationships are stored.
type changeContainerR struct {
	EndorsementRequest *ExecuteEndorsementRequest `boil:"EndorsementRequest" json:"EndorsementRequest" toml:"EndorsementRequest" yaml:"EndorsementRequest"`
	Changes            ChangeSlice                `boil:"Changes" json:"Changes" toml:"Changes" yaml:"Changes"`
}

// NewStruct creates a new relationship struct
func (*changeContainerR) NewStruct() *changeContainerR {
	return &changeContainerR{}
}

// changeContainerL is where Load methods for each relationship are stored.
type changeContainerL struct{}

var (
	changeContainerAllColumns            = []string{"id", "endorsement_request_id", "charges", "effective_interval", "created_at", "updated_at", "form_changes"}
	changeContainerColumnsWithoutDefault = []string{"id", "endorsement_request_id", "effective_interval"}
	changeContainerColumnsWithDefault    = []string{"charges", "created_at", "updated_at", "form_changes"}
	changeContainerPrimaryKeyColumns     = []string{"id"}
	changeContainerGeneratedColumns      = []string{}
)

type (
	// ChangeContainerSlice is an alias for a slice of pointers to ChangeContainer.
	// This should almost always be used instead of []ChangeContainer.
	ChangeContainerSlice []*ChangeContainer
	// ChangeContainerHook is the signature for custom ChangeContainer hook methods
	ChangeContainerHook func(context.Context, boil.ContextExecutor, *ChangeContainer) error

	changeContainerQuery struct {
		*queries.Query
	}
)

// Cache for insert, update and upsert
var (
	changeContainerType                 = reflect.TypeOf(&ChangeContainer{})
	changeContainerMapping              = queries.MakeStructMapping(changeContainerType)
	changeContainerPrimaryKeyMapping, _ = queries.BindMapping(changeContainerType, changeContainerMapping, changeContainerPrimaryKeyColumns)
	changeContainerInsertCacheMut       sync.RWMutex
	changeContainerInsertCache          = make(map[string]insertCache)
	changeContainerUpdateCacheMut       sync.RWMutex
	changeContainerUpdateCache          = make(map[string]updateCache)
	changeContainerUpsertCacheMut       sync.RWMutex
	changeContainerUpsertCache          = make(map[string]insertCache)
)

var (
	// Force time package dependency for automated UpdatedAt/CreatedAt.
	_ = time.Second
	// Force qmhelper dependency for where clause generation (which doesn't
	// always happen)
	_ = qmhelper.Where
)

var changeContainerAfterSelectHooks []ChangeContainerHook

var changeContainerBeforeInsertHooks []ChangeContainerHook
var changeContainerAfterInsertHooks []ChangeContainerHook

var changeContainerBeforeUpdateHooks []ChangeContainerHook
var changeContainerAfterUpdateHooks []ChangeContainerHook

var changeContainerBeforeDeleteHooks []ChangeContainerHook
var changeContainerAfterDeleteHooks []ChangeContainerHook

var changeContainerBeforeUpsertHooks []ChangeContainerHook
var changeContainerAfterUpsertHooks []ChangeContainerHook

// doAfterSelectHooks executes all "after Select" hooks.
func (o *ChangeContainer) doAfterSelectHooks(ctx context.Context, exec boil.ContextExecutor) (err error) {
	if boil.HooksAreSkipped(ctx) {
		return nil
	}

	for _, hook := range changeContainerAfterSelectHooks {
		if err := hook(ctx, exec, o); err != nil {
			return err
		}
	}

	return nil
}

// doBeforeInsertHooks executes all "before insert" hooks.
func (o *ChangeContainer) doBeforeInsertHooks(ctx context.Context, exec boil.ContextExecutor) (err error) {
	if boil.HooksAreSkipped(ctx) {
		return nil
	}

	for _, hook := range changeContainerBeforeInsertHooks {
		if err := hook(ctx, exec, o); err != nil {
			return err
		}
	}

	return nil
}

// doAfterInsertHooks executes all "after Insert" hooks.
func (o *ChangeContainer) doAfterInsertHooks(ctx context.Context, exec boil.ContextExecutor) (err error) {
	if boil.HooksAreSkipped(ctx) {
		return nil
	}

	for _, hook := range changeContainerAfterInsertHooks {
		if err := hook(ctx, exec, o); err != nil {
			return err
		}
	}

	return nil
}

// doBeforeUpdateHooks executes all "before Update" hooks.
func (o *ChangeContainer) doBeforeUpdateHooks(ctx context.Context, exec boil.ContextExecutor) (err error) {
	if boil.HooksAreSkipped(ctx) {
		return nil
	}

	for _, hook := range changeContainerBeforeUpdateHooks {
		if err := hook(ctx, exec, o); err != nil {
			return err
		}
	}

	return nil
}

// doAfterUpdateHooks executes all "after Update" hooks.
func (o *ChangeContainer) doAfterUpdateHooks(ctx context.Context, exec boil.ContextExecutor) (err error) {
	if boil.HooksAreSkipped(ctx) {
		return nil
	}

	for _, hook := range changeContainerAfterUpdateHooks {
		if err := hook(ctx, exec, o); err != nil {
			return err
		}
	}

	return nil
}

// doBeforeDeleteHooks executes all "before Delete" hooks.
func (o *ChangeContainer) doBeforeDeleteHooks(ctx context.Context, exec boil.ContextExecutor) (err error) {
	if boil.HooksAreSkipped(ctx) {
		return nil
	}

	for _, hook := range changeContainerBeforeDeleteHooks {
		if err := hook(ctx, exec, o); err != nil {
			return err
		}
	}

	return nil
}

// doAfterDeleteHooks executes all "after Delete" hooks.
func (o *ChangeContainer) doAfterDeleteHooks(ctx context.Context, exec boil.ContextExecutor) (err error) {
	if boil.HooksAreSkipped(ctx) {
		return nil
	}

	for _, hook := range changeContainerAfterDeleteHooks {
		if err := hook(ctx, exec, o); err != nil {
			return err
		}
	}

	return nil
}

// doBeforeUpsertHooks executes all "before Upsert" hooks.
func (o *ChangeContainer) doBeforeUpsertHooks(ctx context.Context, exec boil.ContextExecutor) (err error) {
	if boil.HooksAreSkipped(ctx) {
		return nil
	}

	for _, hook := range changeContainerBeforeUpsertHooks {
		if err := hook(ctx, exec, o); err != nil {
			return err
		}
	}

	return nil
}

// doAfterUpsertHooks executes all "after Upsert" hooks.
func (o *ChangeContainer) doAfterUpsertHooks(ctx context.Context, exec boil.ContextExecutor) (err error) {
	if boil.HooksAreSkipped(ctx) {
		return nil
	}

	for _, hook := range changeContainerAfterUpsertHooks {
		if err := hook(ctx, exec, o); err != nil {
			return err
		}
	}

	return nil
}

// AddChangeContainerHook registers your hook function for all future operations.
func AddChangeContainerHook(hookPoint boil.HookPoint, changeContainerHook ChangeContainerHook) {
	switch hookPoint {
	case boil.AfterSelectHook:
		changeContainerAfterSelectHooks = append(changeContainerAfterSelectHooks, changeContainerHook)
	case boil.BeforeInsertHook:
		changeContainerBeforeInsertHooks = append(changeContainerBeforeInsertHooks, changeContainerHook)
	case boil.AfterInsertHook:
		changeContainerAfterInsertHooks = append(changeContainerAfterInsertHooks, changeContainerHook)
	case boil.BeforeUpdateHook:
		changeContainerBeforeUpdateHooks = append(changeContainerBeforeUpdateHooks, changeContainerHook)
	case boil.AfterUpdateHook:
		changeContainerAfterUpdateHooks = append(changeContainerAfterUpdateHooks, changeContainerHook)
	case boil.BeforeDeleteHook:
		changeContainerBeforeDeleteHooks = append(changeContainerBeforeDeleteHooks, changeContainerHook)
	case boil.AfterDeleteHook:
		changeContainerAfterDeleteHooks = append(changeContainerAfterDeleteHooks, changeContainerHook)
	case boil.BeforeUpsertHook:
		changeContainerBeforeUpsertHooks = append(changeContainerBeforeUpsertHooks, changeContainerHook)
	case boil.AfterUpsertHook:
		changeContainerAfterUpsertHooks = append(changeContainerAfterUpsertHooks, changeContainerHook)
	}
}

// One returns a single changeContainer record from the query.
func (q changeContainerQuery) One(ctx context.Context, exec boil.ContextExecutor) (*ChangeContainer, error) {
	o := &ChangeContainer{}

	queries.SetLimit(q.Query, 1)

	err := q.Bind(ctx, exec, o)
	if err != nil {
		if errors.Is(err, sql.ErrNoRows) {
			return nil, sql.ErrNoRows
		}
		return nil, errors.Wrap(err, "insurance_bundle: failed to execute a one query for change_container")
	}

	if err := o.doAfterSelectHooks(ctx, exec); err != nil {
		return o, err
	}

	return o, nil
}

// All returns all ChangeContainer records from the query.
func (q changeContainerQuery) All(ctx context.Context, exec boil.ContextExecutor) (ChangeContainerSlice, error) {
	var o []*ChangeContainer

	err := q.Bind(ctx, exec, &o)
	if err != nil {
		return nil, errors.Wrap(err, "insurance_bundle: failed to assign all query results to ChangeContainer slice")
	}

	if len(changeContainerAfterSelectHooks) != 0 {
		for _, obj := range o {
			if err := obj.doAfterSelectHooks(ctx, exec); err != nil {
				return o, err
			}
		}
	}

	return o, nil
}

// Count returns the count of all ChangeContainer records in the query.
func (q changeContainerQuery) Count(ctx context.Context, exec boil.ContextExecutor) (int64, error) {
	var count int64

	queries.SetSelect(q.Query, nil)
	queries.SetCount(q.Query)

	err := q.Query.QueryRowContext(ctx, exec).Scan(&count)
	if err != nil {
		return 0, errors.Wrap(err, "insurance_bundle: failed to count change_container rows")
	}

	return count, nil
}

// Exists checks if the row exists in the table.
func (q changeContainerQuery) Exists(ctx context.Context, exec boil.ContextExecutor) (bool, error) {
	var count int64

	queries.SetSelect(q.Query, nil)
	queries.SetCount(q.Query)
	queries.SetLimit(q.Query, 1)

	err := q.Query.QueryRowContext(ctx, exec).Scan(&count)
	if err != nil {
		return false, errors.Wrap(err, "insurance_bundle: failed to check if change_container exists")
	}

	return count > 0, nil
}

// EndorsementRequest pointed to by the foreign key.
func (o *ChangeContainer) EndorsementRequest(mods ...qm.QueryMod) executeEndorsementRequestQuery {
	queryMods := []qm.QueryMod{
		qm.Where("\"id\" = ?", o.EndorsementRequestID),
	}

	queryMods = append(queryMods, mods...)

	return ExecuteEndorsementRequests(queryMods...)
}

// Changes retrieves all the change's Changes with an executor.
func (o *ChangeContainer) Changes(mods ...qm.QueryMod) changeQuery {
	var queryMods []qm.QueryMod
	if len(mods) != 0 {
		queryMods = append(queryMods, mods...)
	}

	queryMods = append(queryMods,
		qm.Where("\"insurance_bundle\".\"change\".\"change_container_id\"=?", o.ID),
	)

	return Changes(queryMods...)
}

// LoadEndorsementRequest allows an eager lookup of values, cached into the
// loaded structs of the objects. This is for an N-1 relationship.
func (changeContainerL) LoadEndorsementRequest(ctx context.Context, e boil.ContextExecutor, singular bool, maybeChangeContainer interface{}, mods queries.Applicator) error {
	var slice []*ChangeContainer
	var object *ChangeContainer

	if singular {
		object = maybeChangeContainer.(*ChangeContainer)
	} else {
		slice = *maybeChangeContainer.(*[]*ChangeContainer)
	}

	args := make([]interface{}, 0, 1)
	if singular {
		if object.R == nil {
			object.R = &changeContainerR{}
		}
		args = append(args, object.EndorsementRequestID)

	} else {
	Outer:
		for _, obj := range slice {
			if obj.R == nil {
				obj.R = &changeContainerR{}
			}

			for _, a := range args {
				if a == obj.EndorsementRequestID {
					continue Outer
				}
			}

			args = append(args, obj.EndorsementRequestID)

		}
	}

	if len(args) == 0 {
		return nil
	}

	query := NewQuery(
		qm.From(`insurance_bundle.execute_endorsement_request`),
		qm.WhereIn(`insurance_bundle.execute_endorsement_request.id in ?`, args...),
	)
	if mods != nil {
		mods.Apply(query)
	}

	results, err := query.QueryContext(ctx, e)
	if err != nil {
		return errors.Wrap(err, "failed to eager load ExecuteEndorsementRequest")
	}

	var resultSlice []*ExecuteEndorsementRequest
	if err = queries.Bind(results, &resultSlice); err != nil {
		return errors.Wrap(err, "failed to bind eager loaded slice ExecuteEndorsementRequest")
	}

	if err = results.Close(); err != nil {
		return errors.Wrap(err, "failed to close results of eager load for execute_endorsement_request")
	}
	if err = results.Err(); err != nil {
		return errors.Wrap(err, "error occurred during iteration of eager loaded relations for execute_endorsement_request")
	}

	if len(changeContainerAfterSelectHooks) != 0 {
		for _, obj := range resultSlice {
			if err := obj.doAfterSelectHooks(ctx, e); err != nil {
				return err
			}
		}
	}

	if len(resultSlice) == 0 {
		return nil
	}

	if singular {
		foreign := resultSlice[0]
		object.R.EndorsementRequest = foreign
		if foreign.R == nil {
			foreign.R = &executeEndorsementRequestR{}
		}
		foreign.R.EndorsementRequestChangeContainers = append(foreign.R.EndorsementRequestChangeContainers, object)
		return nil
	}

	for _, local := range slice {
		for _, foreign := range resultSlice {
			if local.EndorsementRequestID == foreign.ID {
				local.R.EndorsementRequest = foreign
				if foreign.R == nil {
					foreign.R = &executeEndorsementRequestR{}
				}
				foreign.R.EndorsementRequestChangeContainers = append(foreign.R.EndorsementRequestChangeContainers, local)
				break
			}
		}
	}

	return nil
}

// LoadChanges allows an eager lookup of values, cached into the
// loaded structs of the objects. This is for a 1-M or N-M relationship.
func (changeContainerL) LoadChanges(ctx context.Context, e boil.ContextExecutor, singular bool, maybeChangeContainer interface{}, mods queries.Applicator) error {
	var slice []*ChangeContainer
	var object *ChangeContainer

	if singular {
		object = maybeChangeContainer.(*ChangeContainer)
	} else {
		slice = *maybeChangeContainer.(*[]*ChangeContainer)
	}

	args := make([]interface{}, 0, 1)
	if singular {
		if object.R == nil {
			object.R = &changeContainerR{}
		}
		args = append(args, object.ID)
	} else {
	Outer:
		for _, obj := range slice {
			if obj.R == nil {
				obj.R = &changeContainerR{}
			}

			for _, a := range args {
				if a == obj.ID {
					continue Outer
				}
			}

			args = append(args, obj.ID)
		}
	}

	if len(args) == 0 {
		return nil
	}

	query := NewQuery(
		qm.From(`insurance_bundle.change`),
		qm.WhereIn(`insurance_bundle.change.change_container_id in ?`, args...),
	)
	if mods != nil {
		mods.Apply(query)
	}

	results, err := query.QueryContext(ctx, e)
	if err != nil {
		return errors.Wrap(err, "failed to eager load change")
	}

	var resultSlice []*Change
	if err = queries.Bind(results, &resultSlice); err != nil {
		return errors.Wrap(err, "failed to bind eager loaded slice change")
	}

	if err = results.Close(); err != nil {
		return errors.Wrap(err, "failed to close results in eager load on change")
	}
	if err = results.Err(); err != nil {
		return errors.Wrap(err, "error occurred during iteration of eager loaded relations for change")
	}

	if len(changeAfterSelectHooks) != 0 {
		for _, obj := range resultSlice {
			if err := obj.doAfterSelectHooks(ctx, e); err != nil {
				return err
			}
		}
	}
	if singular {
		object.R.Changes = resultSlice
		for _, foreign := range resultSlice {
			if foreign.R == nil {
				foreign.R = &changeR{}
			}
			foreign.R.ChangeContainer = object
		}
		return nil
	}

	for _, foreign := range resultSlice {
		for _, local := range slice {
			if local.ID == foreign.ChangeContainerID {
				local.R.Changes = append(local.R.Changes, foreign)
				if foreign.R == nil {
					foreign.R = &changeR{}
				}
				foreign.R.ChangeContainer = local
				break
			}
		}
	}

	return nil
}

// SetEndorsementRequest of the changeContainer to the related item.
// Sets o.R.EndorsementRequest to related.
// Adds o to related.R.EndorsementRequestChangeContainers.
func (o *ChangeContainer) SetEndorsementRequest(ctx context.Context, exec boil.ContextExecutor, insert bool, related *ExecuteEndorsementRequest) error {
	var err error
	if insert {
		if err = related.Insert(ctx, exec, boil.Infer()); err != nil {
			return errors.Wrap(err, "failed to insert into foreign table")
		}
	}

	updateQuery := fmt.Sprintf(
		"UPDATE \"insurance_bundle\".\"change_container\" SET %s WHERE %s",
		strmangle.SetParamNames("\"", "\"", 1, []string{"endorsement_request_id"}),
		strmangle.WhereClause("\"", "\"", 2, changeContainerPrimaryKeyColumns),
	)
	values := []interface{}{related.ID, o.ID}

	if boil.IsDebug(ctx) {
		writer := boil.DebugWriterFrom(ctx)
		fmt.Fprintln(writer, updateQuery)
		fmt.Fprintln(writer, values)
	}
	if _, err = exec.ExecContext(ctx, updateQuery, values...); err != nil {
		return errors.Wrap(err, "failed to update local table")
	}

	o.EndorsementRequestID = related.ID
	if o.R == nil {
		o.R = &changeContainerR{
			EndorsementRequest: related,
		}
	} else {
		o.R.EndorsementRequest = related
	}

	if related.R == nil {
		related.R = &executeEndorsementRequestR{
			EndorsementRequestChangeContainers: ChangeContainerSlice{o},
		}
	} else {
		related.R.EndorsementRequestChangeContainers = append(related.R.EndorsementRequestChangeContainers, o)
	}

	return nil
}

// AddChanges adds the given related objects to the existing relationships
// of the change_container, optionally inserting them as new records.
// Appends related to o.R.Changes.
// Sets related.R.ChangeContainer appropriately.
func (o *ChangeContainer) AddChanges(ctx context.Context, exec boil.ContextExecutor, insert bool, related ...*Change) error {
	var err error
	for _, rel := range related {
		if insert {
			rel.ChangeContainerID = o.ID
			if err = rel.Insert(ctx, exec, boil.Infer()); err != nil {
				return errors.Wrap(err, "failed to insert into foreign table")
			}
		} else {
			updateQuery := fmt.Sprintf(
				"UPDATE \"insurance_bundle\".\"change\" SET %s WHERE %s",
				strmangle.SetParamNames("\"", "\"", 1, []string{"change_container_id"}),
				strmangle.WhereClause("\"", "\"", 2, changePrimaryKeyColumns),
			)
			values := []interface{}{o.ID, rel.ID}

			if boil.IsDebug(ctx) {
				writer := boil.DebugWriterFrom(ctx)
				fmt.Fprintln(writer, updateQuery)
				fmt.Fprintln(writer, values)
			}
			if _, err = exec.ExecContext(ctx, updateQuery, values...); err != nil {
				return errors.Wrap(err, "failed to update foreign table")
			}

			rel.ChangeContainerID = o.ID
		}
	}

	if o.R == nil {
		o.R = &changeContainerR{
			Changes: related,
		}
	} else {
		o.R.Changes = append(o.R.Changes, related...)
	}

	for _, rel := range related {
		if rel.R == nil {
			rel.R = &changeR{
				ChangeContainer: o,
			}
		} else {
			rel.R.ChangeContainer = o
		}
	}
	return nil
}

// ChangeContainers retrieves all the records using an executor.
func ChangeContainers(mods ...qm.QueryMod) changeContainerQuery {
	mods = append(mods, qm.From("\"insurance_bundle\".\"change_container\""))
	q := NewQuery(mods...)
	if len(queries.GetSelect(q)) == 0 {
		queries.SetSelect(q, []string{"\"insurance_bundle\".\"change_container\".*"})
	}

	return changeContainerQuery{q}
}

// FindChangeContainer retrieves a single record by ID with an executor.
// If selectCols is empty Find will return all columns.
func FindChangeContainer(ctx context.Context, exec boil.ContextExecutor, iD string, selectCols ...string) (*ChangeContainer, error) {
	changeContainerObj := &ChangeContainer{}

	sel := "*"
	if len(selectCols) > 0 {
		sel = strings.Join(strmangle.IdentQuoteSlice(dialect.LQ, dialect.RQ, selectCols), ",")
	}
	query := fmt.Sprintf(
		"select %s from \"insurance_bundle\".\"change_container\" where \"id\"=$1", sel,
	)

	q := queries.Raw(query, iD)

	err := q.Bind(ctx, exec, changeContainerObj)
	if err != nil {
		if errors.Is(err, sql.ErrNoRows) {
			return nil, sql.ErrNoRows
		}
		return nil, errors.Wrap(err, "insurance_bundle: unable to select from change_container")
	}

	if err = changeContainerObj.doAfterSelectHooks(ctx, exec); err != nil {
		return changeContainerObj, err
	}

	return changeContainerObj, nil
}

// Insert a single record using an executor.
// See boil.Columns.InsertColumnSet documentation to understand column list inference for inserts.
func (o *ChangeContainer) Insert(ctx context.Context, exec boil.ContextExecutor, columns boil.Columns) error {
	if o == nil {
		return errors.New("insurance_bundle: no change_container provided for insertion")
	}

	var err error
	if !boil.TimestampsAreSkipped(ctx) {
		currTime := time.Now().In(boil.GetLocation())

		if queries.MustTime(o.CreatedAt).IsZero() {
			queries.SetScanner(&o.CreatedAt, currTime)
		}
		if queries.MustTime(o.UpdatedAt).IsZero() {
			queries.SetScanner(&o.UpdatedAt, currTime)
		}
	}

	if err := o.doBeforeInsertHooks(ctx, exec); err != nil {
		return err
	}

	nzDefaults := queries.NonZeroDefaultSet(changeContainerColumnsWithDefault, o)

	key := makeCacheKey(columns, nzDefaults)
	changeContainerInsertCacheMut.RLock()
	cache, cached := changeContainerInsertCache[key]
	changeContainerInsertCacheMut.RUnlock()

	if !cached {
		wl, returnColumns := columns.InsertColumnSet(
			changeContainerAllColumns,
			changeContainerColumnsWithDefault,
			changeContainerColumnsWithoutDefault,
			nzDefaults,
		)

		cache.valueMapping, err = queries.BindMapping(changeContainerType, changeContainerMapping, wl)
		if err != nil {
			return err
		}
		cache.retMapping, err = queries.BindMapping(changeContainerType, changeContainerMapping, returnColumns)
		if err != nil {
			return err
		}
		if len(wl) != 0 {
			cache.query = fmt.Sprintf("INSERT INTO \"insurance_bundle\".\"change_container\" (\"%s\") %%sVALUES (%s)%%s", strings.Join(wl, "\",\""), strmangle.Placeholders(dialect.UseIndexPlaceholders, len(wl), 1, 1))
		} else {
			cache.query = "INSERT INTO \"insurance_bundle\".\"change_container\" %sDEFAULT VALUES%s"
		}

		var queryOutput, queryReturning string

		if len(cache.retMapping) != 0 {
			queryReturning = fmt.Sprintf(" RETURNING \"%s\"", strings.Join(returnColumns, "\",\""))
		}

		cache.query = fmt.Sprintf(cache.query, queryOutput, queryReturning)
	}

	value := reflect.Indirect(reflect.ValueOf(o))
	vals := queries.ValuesFromMapping(value, cache.valueMapping)

	if boil.IsDebug(ctx) {
		writer := boil.DebugWriterFrom(ctx)
		fmt.Fprintln(writer, cache.query)
		fmt.Fprintln(writer, vals)
	}

	if len(cache.retMapping) != 0 {
		err = exec.QueryRowContext(ctx, cache.query, vals...).Scan(queries.PtrsFromMapping(value, cache.retMapping)...)
	} else {
		_, err = exec.ExecContext(ctx, cache.query, vals...)
	}

	if err != nil {
		return errors.Wrap(err, "insurance_bundle: unable to insert into change_container")
	}

	if !cached {
		changeContainerInsertCacheMut.Lock()
		changeContainerInsertCache[key] = cache
		changeContainerInsertCacheMut.Unlock()
	}

	return o.doAfterInsertHooks(ctx, exec)
}

// Update uses an executor to update the ChangeContainer.
// See boil.Columns.UpdateColumnSet documentation to understand column list inference for updates.
// Update does not automatically update the record in case of default values. Use .Reload() to refresh the records.
func (o *ChangeContainer) Update(ctx context.Context, exec boil.ContextExecutor, columns boil.Columns) (int64, error) {
	if !boil.TimestampsAreSkipped(ctx) {
		currTime := time.Now().In(boil.GetLocation())

		queries.SetScanner(&o.UpdatedAt, currTime)
	}

	var err error
	if err = o.doBeforeUpdateHooks(ctx, exec); err != nil {
		return 0, err
	}
	key := makeCacheKey(columns, nil)
	changeContainerUpdateCacheMut.RLock()
	cache, cached := changeContainerUpdateCache[key]
	changeContainerUpdateCacheMut.RUnlock()

	if !cached {
		wl := columns.UpdateColumnSet(
			changeContainerAllColumns,
			changeContainerPrimaryKeyColumns,
		)

		if !columns.IsWhitelist() {
			wl = strmangle.SetComplement(wl, []string{"created_at"})
		}
		if len(wl) == 0 {
			return 0, errors.New("insurance_bundle: unable to update change_container, could not build whitelist")
		}

		cache.query = fmt.Sprintf("UPDATE \"insurance_bundle\".\"change_container\" SET %s WHERE %s",
			strmangle.SetParamNames("\"", "\"", 1, wl),
			strmangle.WhereClause("\"", "\"", len(wl)+1, changeContainerPrimaryKeyColumns),
		)
		cache.valueMapping, err = queries.BindMapping(changeContainerType, changeContainerMapping, append(wl, changeContainerPrimaryKeyColumns...))
		if err != nil {
			return 0, err
		}
	}

	values := queries.ValuesFromMapping(reflect.Indirect(reflect.ValueOf(o)), cache.valueMapping)

	if boil.IsDebug(ctx) {
		writer := boil.DebugWriterFrom(ctx)
		fmt.Fprintln(writer, cache.query)
		fmt.Fprintln(writer, values)
	}
	var result sql.Result
	result, err = exec.ExecContext(ctx, cache.query, values...)
	if err != nil {
		return 0, errors.Wrap(err, "insurance_bundle: unable to update change_container row")
	}

	rowsAff, err := result.RowsAffected()
	if err != nil {
		return 0, errors.Wrap(err, "insurance_bundle: failed to get rows affected by update for change_container")
	}

	if !cached {
		changeContainerUpdateCacheMut.Lock()
		changeContainerUpdateCache[key] = cache
		changeContainerUpdateCacheMut.Unlock()
	}

	return rowsAff, o.doAfterUpdateHooks(ctx, exec)
}

// UpdateAll updates all rows with the specified column values.
func (q changeContainerQuery) UpdateAll(ctx context.Context, exec boil.ContextExecutor, cols M) (int64, error) {
	queries.SetUpdate(q.Query, cols)

	result, err := q.Query.ExecContext(ctx, exec)
	if err != nil {
		return 0, errors.Wrap(err, "insurance_bundle: unable to update all for change_container")
	}

	rowsAff, err := result.RowsAffected()
	if err != nil {
		return 0, errors.Wrap(err, "insurance_bundle: unable to retrieve rows affected for change_container")
	}

	return rowsAff, nil
}

// UpdateAll updates all rows with the specified column values, using an executor.
func (o ChangeContainerSlice) UpdateAll(ctx context.Context, exec boil.ContextExecutor, cols M) (int64, error) {
	ln := int64(len(o))
	if ln == 0 {
		return 0, nil
	}

	if len(cols) == 0 {
		return 0, errors.New("insurance_bundle: update all requires at least one column argument")
	}

	colNames := make([]string, len(cols))
	args := make([]interface{}, len(cols))

	i := 0
	for name, value := range cols {
		colNames[i] = name
		args[i] = value
		i++
	}

	// Append all of the primary key values for each column
	for _, obj := range o {
		pkeyArgs := queries.ValuesFromMapping(reflect.Indirect(reflect.ValueOf(obj)), changeContainerPrimaryKeyMapping)
		args = append(args, pkeyArgs...)
	}

	sql := fmt.Sprintf("UPDATE \"insurance_bundle\".\"change_container\" SET %s WHERE %s",
		strmangle.SetParamNames("\"", "\"", 1, colNames),
		strmangle.WhereClauseRepeated(string(dialect.LQ), string(dialect.RQ), len(colNames)+1, changeContainerPrimaryKeyColumns, len(o)))

	if boil.IsDebug(ctx) {
		writer := boil.DebugWriterFrom(ctx)
		fmt.Fprintln(writer, sql)
		fmt.Fprintln(writer, args...)
	}
	result, err := exec.ExecContext(ctx, sql, args...)
	if err != nil {
		return 0, errors.Wrap(err, "insurance_bundle: unable to update all in changeContainer slice")
	}

	rowsAff, err := result.RowsAffected()
	if err != nil {
		return 0, errors.Wrap(err, "insurance_bundle: unable to retrieve rows affected all in update all changeContainer")
	}
	return rowsAff, nil
}

// Upsert attempts an insert using an executor, and does an update or ignore on conflict.
// See boil.Columns documentation for how to properly use updateColumns and insertColumns.
func (o *ChangeContainer) Upsert(ctx context.Context, exec boil.ContextExecutor, updateOnConflict bool, conflictColumns []string, updateColumns, insertColumns boil.Columns) error {
	if o == nil {
		return errors.New("insurance_bundle: no change_container provided for upsert")
	}
	if !boil.TimestampsAreSkipped(ctx) {
		currTime := time.Now().In(boil.GetLocation())

		if queries.MustTime(o.CreatedAt).IsZero() {
			queries.SetScanner(&o.CreatedAt, currTime)
		}
		queries.SetScanner(&o.UpdatedAt, currTime)
	}

	if err := o.doBeforeUpsertHooks(ctx, exec); err != nil {
		return err
	}

	nzDefaults := queries.NonZeroDefaultSet(changeContainerColumnsWithDefault, o)

	// Build cache key in-line uglily - mysql vs psql problems
	buf := strmangle.GetBuffer()
	if updateOnConflict {
		buf.WriteByte('t')
	} else {
		buf.WriteByte('f')
	}
	buf.WriteByte('.')
	for _, c := range conflictColumns {
		buf.WriteString(c)
	}
	buf.WriteByte('.')
	buf.WriteString(strconv.Itoa(updateColumns.Kind))
	for _, c := range updateColumns.Cols {
		buf.WriteString(c)
	}
	buf.WriteByte('.')
	buf.WriteString(strconv.Itoa(insertColumns.Kind))
	for _, c := range insertColumns.Cols {
		buf.WriteString(c)
	}
	buf.WriteByte('.')
	for _, c := range nzDefaults {
		buf.WriteString(c)
	}
	key := buf.String()
	strmangle.PutBuffer(buf)

	changeContainerUpsertCacheMut.RLock()
	cache, cached := changeContainerUpsertCache[key]
	changeContainerUpsertCacheMut.RUnlock()

	var err error

	if !cached {
		insert, ret := insertColumns.InsertColumnSet(
			changeContainerAllColumns,
			changeContainerColumnsWithDefault,
			changeContainerColumnsWithoutDefault,
			nzDefaults,
		)

		update := updateColumns.UpdateColumnSet(
			changeContainerAllColumns,
			changeContainerPrimaryKeyColumns,
		)

		if updateOnConflict && len(update) == 0 {
			return errors.New("insurance_bundle: unable to upsert change_container, could not build update column list")
		}

		conflict := conflictColumns
		if len(conflict) == 0 {
			conflict = make([]string, len(changeContainerPrimaryKeyColumns))
			copy(conflict, changeContainerPrimaryKeyColumns)
		}
		cache.query = buildUpsertQueryPostgres(dialect, "\"insurance_bundle\".\"change_container\"", updateOnConflict, ret, update, conflict, insert)

		cache.valueMapping, err = queries.BindMapping(changeContainerType, changeContainerMapping, insert)
		if err != nil {
			return err
		}
		if len(ret) != 0 {
			cache.retMapping, err = queries.BindMapping(changeContainerType, changeContainerMapping, ret)
			if err != nil {
				return err
			}
		}
	}

	value := reflect.Indirect(reflect.ValueOf(o))
	vals := queries.ValuesFromMapping(value, cache.valueMapping)
	var returns []interface{}
	if len(cache.retMapping) != 0 {
		returns = queries.PtrsFromMapping(value, cache.retMapping)
	}

	if boil.IsDebug(ctx) {
		writer := boil.DebugWriterFrom(ctx)
		fmt.Fprintln(writer, cache.query)
		fmt.Fprintln(writer, vals)
	}
	if len(cache.retMapping) != 0 {
		err = exec.QueryRowContext(ctx, cache.query, vals...).Scan(returns...)
		if errors.Is(err, sql.ErrNoRows) {
			err = nil // Postgres doesn't return anything when there's no update
		}
	} else {
		_, err = exec.ExecContext(ctx, cache.query, vals...)
	}
	if err != nil {
		return errors.Wrap(err, "insurance_bundle: unable to upsert change_container")
	}

	if !cached {
		changeContainerUpsertCacheMut.Lock()
		changeContainerUpsertCache[key] = cache
		changeContainerUpsertCacheMut.Unlock()
	}

	return o.doAfterUpsertHooks(ctx, exec)
}

// Delete deletes a single ChangeContainer record with an executor.
// Delete will match against the primary key column to find the record to delete.
func (o *ChangeContainer) Delete(ctx context.Context, exec boil.ContextExecutor) (int64, error) {
	if o == nil {
		return 0, errors.New("insurance_bundle: no ChangeContainer provided for delete")
	}

	if err := o.doBeforeDeleteHooks(ctx, exec); err != nil {
		return 0, err
	}

	args := queries.ValuesFromMapping(reflect.Indirect(reflect.ValueOf(o)), changeContainerPrimaryKeyMapping)
	sql := "DELETE FROM \"insurance_bundle\".\"change_container\" WHERE \"id\"=$1"

	if boil.IsDebug(ctx) {
		writer := boil.DebugWriterFrom(ctx)
		fmt.Fprintln(writer, sql)
		fmt.Fprintln(writer, args...)
	}
	result, err := exec.ExecContext(ctx, sql, args...)
	if err != nil {
		return 0, errors.Wrap(err, "insurance_bundle: unable to delete from change_container")
	}

	rowsAff, err := result.RowsAffected()
	if err != nil {
		return 0, errors.Wrap(err, "insurance_bundle: failed to get rows affected by delete for change_container")
	}

	if err := o.doAfterDeleteHooks(ctx, exec); err != nil {
		return 0, err
	}

	return rowsAff, nil
}

// DeleteAll deletes all matching rows.
func (q changeContainerQuery) DeleteAll(ctx context.Context, exec boil.ContextExecutor) (int64, error) {
	if q.Query == nil {
		return 0, errors.New("insurance_bundle: no changeContainerQuery provided for delete all")
	}

	queries.SetDelete(q.Query)

	result, err := q.Query.ExecContext(ctx, exec)
	if err != nil {
		return 0, errors.Wrap(err, "insurance_bundle: unable to delete all from change_container")
	}

	rowsAff, err := result.RowsAffected()
	if err != nil {
		return 0, errors.Wrap(err, "insurance_bundle: failed to get rows affected by deleteall for change_container")
	}

	return rowsAff, nil
}

// DeleteAll deletes all rows in the slice, using an executor.
func (o ChangeContainerSlice) DeleteAll(ctx context.Context, exec boil.ContextExecutor) (int64, error) {
	if len(o) == 0 {
		return 0, nil
	}

	if len(changeContainerBeforeDeleteHooks) != 0 {
		for _, obj := range o {
			if err := obj.doBeforeDeleteHooks(ctx, exec); err != nil {
				return 0, err
			}
		}
	}

	var args []interface{}
	for _, obj := range o {
		pkeyArgs := queries.ValuesFromMapping(reflect.Indirect(reflect.ValueOf(obj)), changeContainerPrimaryKeyMapping)
		args = append(args, pkeyArgs...)
	}

	sql := "DELETE FROM \"insurance_bundle\".\"change_container\" WHERE " +
		strmangle.WhereClauseRepeated(string(dialect.LQ), string(dialect.RQ), 1, changeContainerPrimaryKeyColumns, len(o))

	if boil.IsDebug(ctx) {
		writer := boil.DebugWriterFrom(ctx)
		fmt.Fprintln(writer, sql)
		fmt.Fprintln(writer, args)
	}
	result, err := exec.ExecContext(ctx, sql, args...)
	if err != nil {
		return 0, errors.Wrap(err, "insurance_bundle: unable to delete all from changeContainer slice")
	}

	rowsAff, err := result.RowsAffected()
	if err != nil {
		return 0, errors.Wrap(err, "insurance_bundle: failed to get rows affected by deleteall for change_container")
	}

	if len(changeContainerAfterDeleteHooks) != 0 {
		for _, obj := range o {
			if err := obj.doAfterDeleteHooks(ctx, exec); err != nil {
				return 0, err
			}
		}
	}

	return rowsAff, nil
}

// Reload refetches the object from the database
// using the primary keys with an executor.
func (o *ChangeContainer) Reload(ctx context.Context, exec boil.ContextExecutor) error {
	ret, err := FindChangeContainer(ctx, exec, o.ID)
	if err != nil {
		return err
	}

	*o = *ret
	return nil
}

// ReloadAll refetches every row with matching primary key column values
// and overwrites the original object slice with the newly updated slice.
func (o *ChangeContainerSlice) ReloadAll(ctx context.Context, exec boil.ContextExecutor) error {
	if o == nil || len(*o) == 0 {
		return nil
	}

	slice := ChangeContainerSlice{}
	var args []interface{}
	for _, obj := range *o {
		pkeyArgs := queries.ValuesFromMapping(reflect.Indirect(reflect.ValueOf(obj)), changeContainerPrimaryKeyMapping)
		args = append(args, pkeyArgs...)
	}

	sql := "SELECT \"insurance_bundle\".\"change_container\".* FROM \"insurance_bundle\".\"change_container\" WHERE " +
		strmangle.WhereClauseRepeated(string(dialect.LQ), string(dialect.RQ), 1, changeContainerPrimaryKeyColumns, len(*o))

	q := queries.Raw(sql, args...)

	err := q.Bind(ctx, exec, &slice)
	if err != nil {
		return errors.Wrap(err, "insurance_bundle: unable to reload all in ChangeContainerSlice")
	}

	*o = slice

	return nil
}

// ChangeContainerExists checks if the ChangeContainer row exists.
func ChangeContainerExists(ctx context.Context, exec boil.ContextExecutor, iD string) (bool, error) {
	var exists bool
	sql := "select exists(select 1 from \"insurance_bundle\".\"change_container\" where \"id\"=$1 limit 1)"

	if boil.IsDebug(ctx) {
		writer := boil.DebugWriterFrom(ctx)
		fmt.Fprintln(writer, sql)
		fmt.Fprintln(writer, iD)
	}
	row := exec.QueryRowContext(ctx, sql, iD)

	err := row.Scan(&exists)
	if err != nil {
		return false, errors.Wrap(err, "insurance_bundle: unable to check if change_container exists")
	}

	return exists, nil
}
