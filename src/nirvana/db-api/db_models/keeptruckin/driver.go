// Code generated by SQLBoiler (https://github.com/volatiletech/sqlboiler). DO NOT EDIT.
// This file is meant to be re-generated in place and/or deleted at any time.

package keeptruckin

import (
	"context"
	"database/sql"
	"fmt"
	"reflect"
	"strconv"
	"strings"
	"sync"
	"time"

	"github.com/friendsofgo/errors"
	"github.com/volatiletech/null/v8"
	"github.com/volatiletech/sqlboiler/v4/boil"
	"github.com/volatiletech/sqlboiler/v4/queries"
	"github.com/volatiletech/sqlboiler/v4/queries/qm"
	"github.com/volatiletech/sqlboiler/v4/queries/qmhelper"
	"github.com/volatiletech/sqlboiler/v4/types"
	"github.com/volatiletech/strmangle"
)

// Driver is an object representing the database table.
type Driver struct {
	ID            string           `boil:"id" json:"id" toml:"id" yaml:"id"`
	OrgID         string           `boil:"org_id" json:"org_id" toml:"org_id" yaml:"org_id"`
	HandleID      string           `boil:"handle_id" json:"handle_id" toml:"handle_id" yaml:"handle_id"`
	ProviderID    int              `boil:"provider_id" json:"provider_id" toml:"provider_id" yaml:"provider_id"`
	LicenseNumber string           `boil:"license_number" json:"license_number" toml:"license_number" yaml:"license_number"`
	FirstName     string           `boil:"first_name" json:"first_name" toml:"first_name" yaml:"first_name"`
	LastName      string           `boil:"last_name" json:"last_name" toml:"last_name" yaml:"last_name"`
	LicenseState  string           `boil:"license_state" json:"license_state" toml:"license_state" yaml:"license_state"`
	Phone         string           `boil:"phone" json:"phone" toml:"phone" yaml:"phone"`
	IsTracked     bool             `boil:"is_tracked" json:"is_tracked" toml:"is_tracked" yaml:"is_tracked"`
	Raw           types.JSON       `boil:"raw" json:"raw" toml:"raw" yaml:"raw"`
	CreatedAt     time.Time        `boil:"created_at" json:"created_at" toml:"created_at" yaml:"created_at"`
	UpdatedAt     time.Time        `boil:"updated_at" json:"updated_at" toml:"updated_at" yaml:"updated_at"`
	TSPCreatedAt  null.Time        `boil:"tsp_created_at" json:"tsp_created_at,omitempty" toml:"tsp_created_at" yaml:"tsp_created_at,omitempty"`
	TSPUpdatedAt  null.Time        `boil:"tsp_updated_at" json:"tsp_updated_at,omitempty" toml:"tsp_updated_at" yaml:"tsp_updated_at,omitempty"`
	GroupIds      types.Int64Array `boil:"group_ids" json:"group_ids,omitempty" toml:"group_ids" yaml:"group_ids,omitempty"`

	R *driverR `boil:"" json:"" toml:"" yaml:""`
	L driverL  `boil:"-" json:"-" toml:"-" yaml:"-"`
}

var DriverColumns = struct {
	ID            string
	OrgID         string
	HandleID      string
	ProviderID    string
	LicenseNumber string
	FirstName     string
	LastName      string
	LicenseState  string
	Phone         string
	IsTracked     string
	Raw           string
	CreatedAt     string
	UpdatedAt     string
	TSPCreatedAt  string
	TSPUpdatedAt  string
	GroupIds      string
}{
	ID:            "id",
	OrgID:         "org_id",
	HandleID:      "handle_id",
	ProviderID:    "provider_id",
	LicenseNumber: "license_number",
	FirstName:     "first_name",
	LastName:      "last_name",
	LicenseState:  "license_state",
	Phone:         "phone",
	IsTracked:     "is_tracked",
	Raw:           "raw",
	CreatedAt:     "created_at",
	UpdatedAt:     "updated_at",
	TSPCreatedAt:  "tsp_created_at",
	TSPUpdatedAt:  "tsp_updated_at",
	GroupIds:      "group_ids",
}

var DriverTableColumns = struct {
	ID            string
	OrgID         string
	HandleID      string
	ProviderID    string
	LicenseNumber string
	FirstName     string
	LastName      string
	LicenseState  string
	Phone         string
	IsTracked     string
	Raw           string
	CreatedAt     string
	UpdatedAt     string
	TSPCreatedAt  string
	TSPUpdatedAt  string
	GroupIds      string
}{
	ID:            "driver.id",
	OrgID:         "driver.org_id",
	HandleID:      "driver.handle_id",
	ProviderID:    "driver.provider_id",
	LicenseNumber: "driver.license_number",
	FirstName:     "driver.first_name",
	LastName:      "driver.last_name",
	LicenseState:  "driver.license_state",
	Phone:         "driver.phone",
	IsTracked:     "driver.is_tracked",
	Raw:           "driver.raw",
	CreatedAt:     "driver.created_at",
	UpdatedAt:     "driver.updated_at",
	TSPCreatedAt:  "driver.tsp_created_at",
	TSPUpdatedAt:  "driver.tsp_updated_at",
	GroupIds:      "driver.group_ids",
}

// Generated where

type whereHelperstring struct{ field string }

func (w whereHelperstring) EQ(x string) qm.QueryMod  { return qmhelper.Where(w.field, qmhelper.EQ, x) }
func (w whereHelperstring) NEQ(x string) qm.QueryMod { return qmhelper.Where(w.field, qmhelper.NEQ, x) }
func (w whereHelperstring) LT(x string) qm.QueryMod  { return qmhelper.Where(w.field, qmhelper.LT, x) }
func (w whereHelperstring) LTE(x string) qm.QueryMod { return qmhelper.Where(w.field, qmhelper.LTE, x) }
func (w whereHelperstring) GT(x string) qm.QueryMod  { return qmhelper.Where(w.field, qmhelper.GT, x) }
func (w whereHelperstring) GTE(x string) qm.QueryMod { return qmhelper.Where(w.field, qmhelper.GTE, x) }
func (w whereHelperstring) IN(slice []string) qm.QueryMod {
	values := make([]interface{}, 0, len(slice))
	for _, value := range slice {
		values = append(values, value)
	}
	return qm.WhereIn(fmt.Sprintf("%s IN ?", w.field), values...)
}
func (w whereHelperstring) NIN(slice []string) qm.QueryMod {
	values := make([]interface{}, 0, len(slice))
	for _, value := range slice {
		values = append(values, value)
	}
	return qm.WhereNotIn(fmt.Sprintf("%s NOT IN ?", w.field), values...)
}

type whereHelperint struct{ field string }

func (w whereHelperint) EQ(x int) qm.QueryMod  { return qmhelper.Where(w.field, qmhelper.EQ, x) }
func (w whereHelperint) NEQ(x int) qm.QueryMod { return qmhelper.Where(w.field, qmhelper.NEQ, x) }
func (w whereHelperint) LT(x int) qm.QueryMod  { return qmhelper.Where(w.field, qmhelper.LT, x) }
func (w whereHelperint) LTE(x int) qm.QueryMod { return qmhelper.Where(w.field, qmhelper.LTE, x) }
func (w whereHelperint) GT(x int) qm.QueryMod  { return qmhelper.Where(w.field, qmhelper.GT, x) }
func (w whereHelperint) GTE(x int) qm.QueryMod { return qmhelper.Where(w.field, qmhelper.GTE, x) }
func (w whereHelperint) IN(slice []int) qm.QueryMod {
	values := make([]interface{}, 0, len(slice))
	for _, value := range slice {
		values = append(values, value)
	}
	return qm.WhereIn(fmt.Sprintf("%s IN ?", w.field), values...)
}
func (w whereHelperint) NIN(slice []int) qm.QueryMod {
	values := make([]interface{}, 0, len(slice))
	for _, value := range slice {
		values = append(values, value)
	}
	return qm.WhereNotIn(fmt.Sprintf("%s NOT IN ?", w.field), values...)
}

type whereHelperbool struct{ field string }

func (w whereHelperbool) EQ(x bool) qm.QueryMod  { return qmhelper.Where(w.field, qmhelper.EQ, x) }
func (w whereHelperbool) NEQ(x bool) qm.QueryMod { return qmhelper.Where(w.field, qmhelper.NEQ, x) }
func (w whereHelperbool) LT(x bool) qm.QueryMod  { return qmhelper.Where(w.field, qmhelper.LT, x) }
func (w whereHelperbool) LTE(x bool) qm.QueryMod { return qmhelper.Where(w.field, qmhelper.LTE, x) }
func (w whereHelperbool) GT(x bool) qm.QueryMod  { return qmhelper.Where(w.field, qmhelper.GT, x) }
func (w whereHelperbool) GTE(x bool) qm.QueryMod { return qmhelper.Where(w.field, qmhelper.GTE, x) }

type whereHelpertypes_JSON struct{ field string }

func (w whereHelpertypes_JSON) EQ(x types.JSON) qm.QueryMod {
	return qmhelper.Where(w.field, qmhelper.EQ, x)
}
func (w whereHelpertypes_JSON) NEQ(x types.JSON) qm.QueryMod {
	return qmhelper.Where(w.field, qmhelper.NEQ, x)
}
func (w whereHelpertypes_JSON) LT(x types.JSON) qm.QueryMod {
	return qmhelper.Where(w.field, qmhelper.LT, x)
}
func (w whereHelpertypes_JSON) LTE(x types.JSON) qm.QueryMod {
	return qmhelper.Where(w.field, qmhelper.LTE, x)
}
func (w whereHelpertypes_JSON) GT(x types.JSON) qm.QueryMod {
	return qmhelper.Where(w.field, qmhelper.GT, x)
}
func (w whereHelpertypes_JSON) GTE(x types.JSON) qm.QueryMod {
	return qmhelper.Where(w.field, qmhelper.GTE, x)
}

type whereHelpertime_Time struct{ field string }

func (w whereHelpertime_Time) EQ(x time.Time) qm.QueryMod {
	return qmhelper.Where(w.field, qmhelper.EQ, x)
}
func (w whereHelpertime_Time) NEQ(x time.Time) qm.QueryMod {
	return qmhelper.Where(w.field, qmhelper.NEQ, x)
}
func (w whereHelpertime_Time) LT(x time.Time) qm.QueryMod {
	return qmhelper.Where(w.field, qmhelper.LT, x)
}
func (w whereHelpertime_Time) LTE(x time.Time) qm.QueryMod {
	return qmhelper.Where(w.field, qmhelper.LTE, x)
}
func (w whereHelpertime_Time) GT(x time.Time) qm.QueryMod {
	return qmhelper.Where(w.field, qmhelper.GT, x)
}
func (w whereHelpertime_Time) GTE(x time.Time) qm.QueryMod {
	return qmhelper.Where(w.field, qmhelper.GTE, x)
}

type whereHelpernull_Time struct{ field string }

func (w whereHelpernull_Time) EQ(x null.Time) qm.QueryMod {
	return qmhelper.WhereNullEQ(w.field, false, x)
}
func (w whereHelpernull_Time) NEQ(x null.Time) qm.QueryMod {
	return qmhelper.WhereNullEQ(w.field, true, x)
}
func (w whereHelpernull_Time) LT(x null.Time) qm.QueryMod {
	return qmhelper.Where(w.field, qmhelper.LT, x)
}
func (w whereHelpernull_Time) LTE(x null.Time) qm.QueryMod {
	return qmhelper.Where(w.field, qmhelper.LTE, x)
}
func (w whereHelpernull_Time) GT(x null.Time) qm.QueryMod {
	return qmhelper.Where(w.field, qmhelper.GT, x)
}
func (w whereHelpernull_Time) GTE(x null.Time) qm.QueryMod {
	return qmhelper.Where(w.field, qmhelper.GTE, x)
}

func (w whereHelpernull_Time) IsNull() qm.QueryMod    { return qmhelper.WhereIsNull(w.field) }
func (w whereHelpernull_Time) IsNotNull() qm.QueryMod { return qmhelper.WhereIsNotNull(w.field) }

type whereHelpertypes_Int64Array struct{ field string }

func (w whereHelpertypes_Int64Array) EQ(x types.Int64Array) qm.QueryMod {
	return qmhelper.WhereNullEQ(w.field, false, x)
}
func (w whereHelpertypes_Int64Array) NEQ(x types.Int64Array) qm.QueryMod {
	return qmhelper.WhereNullEQ(w.field, true, x)
}
func (w whereHelpertypes_Int64Array) LT(x types.Int64Array) qm.QueryMod {
	return qmhelper.Where(w.field, qmhelper.LT, x)
}
func (w whereHelpertypes_Int64Array) LTE(x types.Int64Array) qm.QueryMod {
	return qmhelper.Where(w.field, qmhelper.LTE, x)
}
func (w whereHelpertypes_Int64Array) GT(x types.Int64Array) qm.QueryMod {
	return qmhelper.Where(w.field, qmhelper.GT, x)
}
func (w whereHelpertypes_Int64Array) GTE(x types.Int64Array) qm.QueryMod {
	return qmhelper.Where(w.field, qmhelper.GTE, x)
}

func (w whereHelpertypes_Int64Array) IsNull() qm.QueryMod    { return qmhelper.WhereIsNull(w.field) }
func (w whereHelpertypes_Int64Array) IsNotNull() qm.QueryMod { return qmhelper.WhereIsNotNull(w.field) }

var DriverWhere = struct {
	ID            whereHelperstring
	OrgID         whereHelperstring
	HandleID      whereHelperstring
	ProviderID    whereHelperint
	LicenseNumber whereHelperstring
	FirstName     whereHelperstring
	LastName      whereHelperstring
	LicenseState  whereHelperstring
	Phone         whereHelperstring
	IsTracked     whereHelperbool
	Raw           whereHelpertypes_JSON
	CreatedAt     whereHelpertime_Time
	UpdatedAt     whereHelpertime_Time
	TSPCreatedAt  whereHelpernull_Time
	TSPUpdatedAt  whereHelpernull_Time
	GroupIds      whereHelpertypes_Int64Array
}{
	ID:            whereHelperstring{field: "\"keeptruckin\".\"driver\".\"id\""},
	OrgID:         whereHelperstring{field: "\"keeptruckin\".\"driver\".\"org_id\""},
	HandleID:      whereHelperstring{field: "\"keeptruckin\".\"driver\".\"handle_id\""},
	ProviderID:    whereHelperint{field: "\"keeptruckin\".\"driver\".\"provider_id\""},
	LicenseNumber: whereHelperstring{field: "\"keeptruckin\".\"driver\".\"license_number\""},
	FirstName:     whereHelperstring{field: "\"keeptruckin\".\"driver\".\"first_name\""},
	LastName:      whereHelperstring{field: "\"keeptruckin\".\"driver\".\"last_name\""},
	LicenseState:  whereHelperstring{field: "\"keeptruckin\".\"driver\".\"license_state\""},
	Phone:         whereHelperstring{field: "\"keeptruckin\".\"driver\".\"phone\""},
	IsTracked:     whereHelperbool{field: "\"keeptruckin\".\"driver\".\"is_tracked\""},
	Raw:           whereHelpertypes_JSON{field: "\"keeptruckin\".\"driver\".\"raw\""},
	CreatedAt:     whereHelpertime_Time{field: "\"keeptruckin\".\"driver\".\"created_at\""},
	UpdatedAt:     whereHelpertime_Time{field: "\"keeptruckin\".\"driver\".\"updated_at\""},
	TSPCreatedAt:  whereHelpernull_Time{field: "\"keeptruckin\".\"driver\".\"tsp_created_at\""},
	TSPUpdatedAt:  whereHelpernull_Time{field: "\"keeptruckin\".\"driver\".\"tsp_updated_at\""},
	GroupIds:      whereHelpertypes_Int64Array{field: "\"keeptruckin\".\"driver\".\"group_ids\""},
}

// DriverRels is where relationship names are stored.
var DriverRels = struct {
	Org string
}{
	Org: "Org",
}

// driverR is where relationships are stored.
type driverR struct {
	Org *Organization `boil:"Org" json:"Org" toml:"Org" yaml:"Org"`
}

// NewStruct creates a new relationship struct
func (*driverR) NewStruct() *driverR {
	return &driverR{}
}

// driverL is where Load methods for each relationship are stored.
type driverL struct{}

var (
	driverAllColumns            = []string{"id", "org_id", "handle_id", "provider_id", "license_number", "first_name", "last_name", "license_state", "phone", "is_tracked", "raw", "created_at", "updated_at", "tsp_created_at", "tsp_updated_at", "group_ids"}
	driverColumnsWithoutDefault = []string{"id", "org_id", "handle_id", "provider_id", "license_number", "first_name", "last_name", "license_state", "phone", "raw"}
	driverColumnsWithDefault    = []string{"is_tracked", "created_at", "updated_at", "tsp_created_at", "tsp_updated_at", "group_ids"}
	driverPrimaryKeyColumns     = []string{"id"}
	driverGeneratedColumns      = []string{}
)

type (
	// DriverSlice is an alias for a slice of pointers to Driver.
	// This should almost always be used instead of []Driver.
	DriverSlice []*Driver
	// DriverHook is the signature for custom Driver hook methods
	DriverHook func(context.Context, boil.ContextExecutor, *Driver) error

	driverQuery struct {
		*queries.Query
	}
)

// Cache for insert, update and upsert
var (
	driverType                 = reflect.TypeOf(&Driver{})
	driverMapping              = queries.MakeStructMapping(driverType)
	driverPrimaryKeyMapping, _ = queries.BindMapping(driverType, driverMapping, driverPrimaryKeyColumns)
	driverInsertCacheMut       sync.RWMutex
	driverInsertCache          = make(map[string]insertCache)
	driverUpdateCacheMut       sync.RWMutex
	driverUpdateCache          = make(map[string]updateCache)
	driverUpsertCacheMut       sync.RWMutex
	driverUpsertCache          = make(map[string]insertCache)
)

var (
	// Force time package dependency for automated UpdatedAt/CreatedAt.
	_ = time.Second
	// Force qmhelper dependency for where clause generation (which doesn't
	// always happen)
	_ = qmhelper.Where
)

var driverAfterSelectHooks []DriverHook

var driverBeforeInsertHooks []DriverHook
var driverAfterInsertHooks []DriverHook

var driverBeforeUpdateHooks []DriverHook
var driverAfterUpdateHooks []DriverHook

var driverBeforeDeleteHooks []DriverHook
var driverAfterDeleteHooks []DriverHook

var driverBeforeUpsertHooks []DriverHook
var driverAfterUpsertHooks []DriverHook

// doAfterSelectHooks executes all "after Select" hooks.
func (o *Driver) doAfterSelectHooks(ctx context.Context, exec boil.ContextExecutor) (err error) {
	if boil.HooksAreSkipped(ctx) {
		return nil
	}

	for _, hook := range driverAfterSelectHooks {
		if err := hook(ctx, exec, o); err != nil {
			return err
		}
	}

	return nil
}

// doBeforeInsertHooks executes all "before insert" hooks.
func (o *Driver) doBeforeInsertHooks(ctx context.Context, exec boil.ContextExecutor) (err error) {
	if boil.HooksAreSkipped(ctx) {
		return nil
	}

	for _, hook := range driverBeforeInsertHooks {
		if err := hook(ctx, exec, o); err != nil {
			return err
		}
	}

	return nil
}

// doAfterInsertHooks executes all "after Insert" hooks.
func (o *Driver) doAfterInsertHooks(ctx context.Context, exec boil.ContextExecutor) (err error) {
	if boil.HooksAreSkipped(ctx) {
		return nil
	}

	for _, hook := range driverAfterInsertHooks {
		if err := hook(ctx, exec, o); err != nil {
			return err
		}
	}

	return nil
}

// doBeforeUpdateHooks executes all "before Update" hooks.
func (o *Driver) doBeforeUpdateHooks(ctx context.Context, exec boil.ContextExecutor) (err error) {
	if boil.HooksAreSkipped(ctx) {
		return nil
	}

	for _, hook := range driverBeforeUpdateHooks {
		if err := hook(ctx, exec, o); err != nil {
			return err
		}
	}

	return nil
}

// doAfterUpdateHooks executes all "after Update" hooks.
func (o *Driver) doAfterUpdateHooks(ctx context.Context, exec boil.ContextExecutor) (err error) {
	if boil.HooksAreSkipped(ctx) {
		return nil
	}

	for _, hook := range driverAfterUpdateHooks {
		if err := hook(ctx, exec, o); err != nil {
			return err
		}
	}

	return nil
}

// doBeforeDeleteHooks executes all "before Delete" hooks.
func (o *Driver) doBeforeDeleteHooks(ctx context.Context, exec boil.ContextExecutor) (err error) {
	if boil.HooksAreSkipped(ctx) {
		return nil
	}

	for _, hook := range driverBeforeDeleteHooks {
		if err := hook(ctx, exec, o); err != nil {
			return err
		}
	}

	return nil
}

// doAfterDeleteHooks executes all "after Delete" hooks.
func (o *Driver) doAfterDeleteHooks(ctx context.Context, exec boil.ContextExecutor) (err error) {
	if boil.HooksAreSkipped(ctx) {
		return nil
	}

	for _, hook := range driverAfterDeleteHooks {
		if err := hook(ctx, exec, o); err != nil {
			return err
		}
	}

	return nil
}

// doBeforeUpsertHooks executes all "before Upsert" hooks.
func (o *Driver) doBeforeUpsertHooks(ctx context.Context, exec boil.ContextExecutor) (err error) {
	if boil.HooksAreSkipped(ctx) {
		return nil
	}

	for _, hook := range driverBeforeUpsertHooks {
		if err := hook(ctx, exec, o); err != nil {
			return err
		}
	}

	return nil
}

// doAfterUpsertHooks executes all "after Upsert" hooks.
func (o *Driver) doAfterUpsertHooks(ctx context.Context, exec boil.ContextExecutor) (err error) {
	if boil.HooksAreSkipped(ctx) {
		return nil
	}

	for _, hook := range driverAfterUpsertHooks {
		if err := hook(ctx, exec, o); err != nil {
			return err
		}
	}

	return nil
}

// AddDriverHook registers your hook function for all future operations.
func AddDriverHook(hookPoint boil.HookPoint, driverHook DriverHook) {
	switch hookPoint {
	case boil.AfterSelectHook:
		driverAfterSelectHooks = append(driverAfterSelectHooks, driverHook)
	case boil.BeforeInsertHook:
		driverBeforeInsertHooks = append(driverBeforeInsertHooks, driverHook)
	case boil.AfterInsertHook:
		driverAfterInsertHooks = append(driverAfterInsertHooks, driverHook)
	case boil.BeforeUpdateHook:
		driverBeforeUpdateHooks = append(driverBeforeUpdateHooks, driverHook)
	case boil.AfterUpdateHook:
		driverAfterUpdateHooks = append(driverAfterUpdateHooks, driverHook)
	case boil.BeforeDeleteHook:
		driverBeforeDeleteHooks = append(driverBeforeDeleteHooks, driverHook)
	case boil.AfterDeleteHook:
		driverAfterDeleteHooks = append(driverAfterDeleteHooks, driverHook)
	case boil.BeforeUpsertHook:
		driverBeforeUpsertHooks = append(driverBeforeUpsertHooks, driverHook)
	case boil.AfterUpsertHook:
		driverAfterUpsertHooks = append(driverAfterUpsertHooks, driverHook)
	}
}

// One returns a single driver record from the query.
func (q driverQuery) One(ctx context.Context, exec boil.ContextExecutor) (*Driver, error) {
	o := &Driver{}

	queries.SetLimit(q.Query, 1)

	err := q.Bind(ctx, exec, o)
	if err != nil {
		if errors.Is(err, sql.ErrNoRows) {
			return nil, sql.ErrNoRows
		}
		return nil, errors.Wrap(err, "keeptruckin: failed to execute a one query for driver")
	}

	if err := o.doAfterSelectHooks(ctx, exec); err != nil {
		return o, err
	}

	return o, nil
}

// All returns all Driver records from the query.
func (q driverQuery) All(ctx context.Context, exec boil.ContextExecutor) (DriverSlice, error) {
	var o []*Driver

	err := q.Bind(ctx, exec, &o)
	if err != nil {
		return nil, errors.Wrap(err, "keeptruckin: failed to assign all query results to Driver slice")
	}

	if len(driverAfterSelectHooks) != 0 {
		for _, obj := range o {
			if err := obj.doAfterSelectHooks(ctx, exec); err != nil {
				return o, err
			}
		}
	}

	return o, nil
}

// Count returns the count of all Driver records in the query.
func (q driverQuery) Count(ctx context.Context, exec boil.ContextExecutor) (int64, error) {
	var count int64

	queries.SetSelect(q.Query, nil)
	queries.SetCount(q.Query)

	err := q.Query.QueryRowContext(ctx, exec).Scan(&count)
	if err != nil {
		return 0, errors.Wrap(err, "keeptruckin: failed to count driver rows")
	}

	return count, nil
}

// Exists checks if the row exists in the table.
func (q driverQuery) Exists(ctx context.Context, exec boil.ContextExecutor) (bool, error) {
	var count int64

	queries.SetSelect(q.Query, nil)
	queries.SetCount(q.Query)
	queries.SetLimit(q.Query, 1)

	err := q.Query.QueryRowContext(ctx, exec).Scan(&count)
	if err != nil {
		return false, errors.Wrap(err, "keeptruckin: failed to check if driver exists")
	}

	return count > 0, nil
}

// Org pointed to by the foreign key.
func (o *Driver) Org(mods ...qm.QueryMod) organizationQuery {
	queryMods := []qm.QueryMod{
		qm.Where("\"id\" = ?", o.OrgID),
	}

	queryMods = append(queryMods, mods...)

	return Organizations(queryMods...)
}

// LoadOrg allows an eager lookup of values, cached into the
// loaded structs of the objects. This is for an N-1 relationship.
func (driverL) LoadOrg(ctx context.Context, e boil.ContextExecutor, singular bool, maybeDriver interface{}, mods queries.Applicator) error {
	var slice []*Driver
	var object *Driver

	if singular {
		object = maybeDriver.(*Driver)
	} else {
		slice = *maybeDriver.(*[]*Driver)
	}

	args := make([]interface{}, 0, 1)
	if singular {
		if object.R == nil {
			object.R = &driverR{}
		}
		args = append(args, object.OrgID)

	} else {
	Outer:
		for _, obj := range slice {
			if obj.R == nil {
				obj.R = &driverR{}
			}

			for _, a := range args {
				if a == obj.OrgID {
					continue Outer
				}
			}

			args = append(args, obj.OrgID)

		}
	}

	if len(args) == 0 {
		return nil
	}

	query := NewQuery(
		qm.From(`keeptruckin.organization`),
		qm.WhereIn(`keeptruckin.organization.id in ?`, args...),
	)
	if mods != nil {
		mods.Apply(query)
	}

	results, err := query.QueryContext(ctx, e)
	if err != nil {
		return errors.Wrap(err, "failed to eager load Organization")
	}

	var resultSlice []*Organization
	if err = queries.Bind(results, &resultSlice); err != nil {
		return errors.Wrap(err, "failed to bind eager loaded slice Organization")
	}

	if err = results.Close(); err != nil {
		return errors.Wrap(err, "failed to close results of eager load for organization")
	}
	if err = results.Err(); err != nil {
		return errors.Wrap(err, "error occurred during iteration of eager loaded relations for organization")
	}

	if len(driverAfterSelectHooks) != 0 {
		for _, obj := range resultSlice {
			if err := obj.doAfterSelectHooks(ctx, e); err != nil {
				return err
			}
		}
	}

	if len(resultSlice) == 0 {
		return nil
	}

	if singular {
		foreign := resultSlice[0]
		object.R.Org = foreign
		if foreign.R == nil {
			foreign.R = &organizationR{}
		}
		foreign.R.OrgDrivers = append(foreign.R.OrgDrivers, object)
		return nil
	}

	for _, local := range slice {
		for _, foreign := range resultSlice {
			if local.OrgID == foreign.ID {
				local.R.Org = foreign
				if foreign.R == nil {
					foreign.R = &organizationR{}
				}
				foreign.R.OrgDrivers = append(foreign.R.OrgDrivers, local)
				break
			}
		}
	}

	return nil
}

// SetOrg of the driver to the related item.
// Sets o.R.Org to related.
// Adds o to related.R.OrgDrivers.
func (o *Driver) SetOrg(ctx context.Context, exec boil.ContextExecutor, insert bool, related *Organization) error {
	var err error
	if insert {
		if err = related.Insert(ctx, exec, boil.Infer()); err != nil {
			return errors.Wrap(err, "failed to insert into foreign table")
		}
	}

	updateQuery := fmt.Sprintf(
		"UPDATE \"keeptruckin\".\"driver\" SET %s WHERE %s",
		strmangle.SetParamNames("\"", "\"", 1, []string{"org_id"}),
		strmangle.WhereClause("\"", "\"", 2, driverPrimaryKeyColumns),
	)
	values := []interface{}{related.ID, o.ID}

	if boil.IsDebug(ctx) {
		writer := boil.DebugWriterFrom(ctx)
		fmt.Fprintln(writer, updateQuery)
		fmt.Fprintln(writer, values)
	}
	if _, err = exec.ExecContext(ctx, updateQuery, values...); err != nil {
		return errors.Wrap(err, "failed to update local table")
	}

	o.OrgID = related.ID
	if o.R == nil {
		o.R = &driverR{
			Org: related,
		}
	} else {
		o.R.Org = related
	}

	if related.R == nil {
		related.R = &organizationR{
			OrgDrivers: DriverSlice{o},
		}
	} else {
		related.R.OrgDrivers = append(related.R.OrgDrivers, o)
	}

	return nil
}

// Drivers retrieves all the records using an executor.
func Drivers(mods ...qm.QueryMod) driverQuery {
	mods = append(mods, qm.From("\"keeptruckin\".\"driver\""))
	q := NewQuery(mods...)
	if len(queries.GetSelect(q)) == 0 {
		queries.SetSelect(q, []string{"\"keeptruckin\".\"driver\".*"})
	}

	return driverQuery{q}
}

// FindDriver retrieves a single record by ID with an executor.
// If selectCols is empty Find will return all columns.
func FindDriver(ctx context.Context, exec boil.ContextExecutor, iD string, selectCols ...string) (*Driver, error) {
	driverObj := &Driver{}

	sel := "*"
	if len(selectCols) > 0 {
		sel = strings.Join(strmangle.IdentQuoteSlice(dialect.LQ, dialect.RQ, selectCols), ",")
	}
	query := fmt.Sprintf(
		"select %s from \"keeptruckin\".\"driver\" where \"id\"=$1", sel,
	)

	q := queries.Raw(query, iD)

	err := q.Bind(ctx, exec, driverObj)
	if err != nil {
		if errors.Is(err, sql.ErrNoRows) {
			return nil, sql.ErrNoRows
		}
		return nil, errors.Wrap(err, "keeptruckin: unable to select from driver")
	}

	if err = driverObj.doAfterSelectHooks(ctx, exec); err != nil {
		return driverObj, err
	}

	return driverObj, nil
}

// Insert a single record using an executor.
// See boil.Columns.InsertColumnSet documentation to understand column list inference for inserts.
func (o *Driver) Insert(ctx context.Context, exec boil.ContextExecutor, columns boil.Columns) error {
	if o == nil {
		return errors.New("keeptruckin: no driver provided for insertion")
	}

	var err error
	if !boil.TimestampsAreSkipped(ctx) {
		currTime := time.Now().In(boil.GetLocation())

		if o.CreatedAt.IsZero() {
			o.CreatedAt = currTime
		}
		if o.UpdatedAt.IsZero() {
			o.UpdatedAt = currTime
		}
	}

	if err := o.doBeforeInsertHooks(ctx, exec); err != nil {
		return err
	}

	nzDefaults := queries.NonZeroDefaultSet(driverColumnsWithDefault, o)

	key := makeCacheKey(columns, nzDefaults)
	driverInsertCacheMut.RLock()
	cache, cached := driverInsertCache[key]
	driverInsertCacheMut.RUnlock()

	if !cached {
		wl, returnColumns := columns.InsertColumnSet(
			driverAllColumns,
			driverColumnsWithDefault,
			driverColumnsWithoutDefault,
			nzDefaults,
		)

		cache.valueMapping, err = queries.BindMapping(driverType, driverMapping, wl)
		if err != nil {
			return err
		}
		cache.retMapping, err = queries.BindMapping(driverType, driverMapping, returnColumns)
		if err != nil {
			return err
		}
		if len(wl) != 0 {
			cache.query = fmt.Sprintf("INSERT INTO \"keeptruckin\".\"driver\" (\"%s\") %%sVALUES (%s)%%s", strings.Join(wl, "\",\""), strmangle.Placeholders(dialect.UseIndexPlaceholders, len(wl), 1, 1))
		} else {
			cache.query = "INSERT INTO \"keeptruckin\".\"driver\" %sDEFAULT VALUES%s"
		}

		var queryOutput, queryReturning string

		if len(cache.retMapping) != 0 {
			queryReturning = fmt.Sprintf(" RETURNING \"%s\"", strings.Join(returnColumns, "\",\""))
		}

		cache.query = fmt.Sprintf(cache.query, queryOutput, queryReturning)
	}

	value := reflect.Indirect(reflect.ValueOf(o))
	vals := queries.ValuesFromMapping(value, cache.valueMapping)

	if boil.IsDebug(ctx) {
		writer := boil.DebugWriterFrom(ctx)
		fmt.Fprintln(writer, cache.query)
		fmt.Fprintln(writer, vals)
	}

	if len(cache.retMapping) != 0 {
		err = exec.QueryRowContext(ctx, cache.query, vals...).Scan(queries.PtrsFromMapping(value, cache.retMapping)...)
	} else {
		_, err = exec.ExecContext(ctx, cache.query, vals...)
	}

	if err != nil {
		return errors.Wrap(err, "keeptruckin: unable to insert into driver")
	}

	if !cached {
		driverInsertCacheMut.Lock()
		driverInsertCache[key] = cache
		driverInsertCacheMut.Unlock()
	}

	return o.doAfterInsertHooks(ctx, exec)
}

// Update uses an executor to update the Driver.
// See boil.Columns.UpdateColumnSet documentation to understand column list inference for updates.
// Update does not automatically update the record in case of default values. Use .Reload() to refresh the records.
func (o *Driver) Update(ctx context.Context, exec boil.ContextExecutor, columns boil.Columns) (int64, error) {
	if !boil.TimestampsAreSkipped(ctx) {
		currTime := time.Now().In(boil.GetLocation())

		o.UpdatedAt = currTime
	}

	var err error
	if err = o.doBeforeUpdateHooks(ctx, exec); err != nil {
		return 0, err
	}
	key := makeCacheKey(columns, nil)
	driverUpdateCacheMut.RLock()
	cache, cached := driverUpdateCache[key]
	driverUpdateCacheMut.RUnlock()

	if !cached {
		wl := columns.UpdateColumnSet(
			driverAllColumns,
			driverPrimaryKeyColumns,
		)

		if !columns.IsWhitelist() {
			wl = strmangle.SetComplement(wl, []string{"created_at"})
		}
		if len(wl) == 0 {
			return 0, errors.New("keeptruckin: unable to update driver, could not build whitelist")
		}

		cache.query = fmt.Sprintf("UPDATE \"keeptruckin\".\"driver\" SET %s WHERE %s",
			strmangle.SetParamNames("\"", "\"", 1, wl),
			strmangle.WhereClause("\"", "\"", len(wl)+1, driverPrimaryKeyColumns),
		)
		cache.valueMapping, err = queries.BindMapping(driverType, driverMapping, append(wl, driverPrimaryKeyColumns...))
		if err != nil {
			return 0, err
		}
	}

	values := queries.ValuesFromMapping(reflect.Indirect(reflect.ValueOf(o)), cache.valueMapping)

	if boil.IsDebug(ctx) {
		writer := boil.DebugWriterFrom(ctx)
		fmt.Fprintln(writer, cache.query)
		fmt.Fprintln(writer, values)
	}
	var result sql.Result
	result, err = exec.ExecContext(ctx, cache.query, values...)
	if err != nil {
		return 0, errors.Wrap(err, "keeptruckin: unable to update driver row")
	}

	rowsAff, err := result.RowsAffected()
	if err != nil {
		return 0, errors.Wrap(err, "keeptruckin: failed to get rows affected by update for driver")
	}

	if !cached {
		driverUpdateCacheMut.Lock()
		driverUpdateCache[key] = cache
		driverUpdateCacheMut.Unlock()
	}

	return rowsAff, o.doAfterUpdateHooks(ctx, exec)
}

// UpdateAll updates all rows with the specified column values.
func (q driverQuery) UpdateAll(ctx context.Context, exec boil.ContextExecutor, cols M) (int64, error) {
	queries.SetUpdate(q.Query, cols)

	result, err := q.Query.ExecContext(ctx, exec)
	if err != nil {
		return 0, errors.Wrap(err, "keeptruckin: unable to update all for driver")
	}

	rowsAff, err := result.RowsAffected()
	if err != nil {
		return 0, errors.Wrap(err, "keeptruckin: unable to retrieve rows affected for driver")
	}

	return rowsAff, nil
}

// UpdateAll updates all rows with the specified column values, using an executor.
func (o DriverSlice) UpdateAll(ctx context.Context, exec boil.ContextExecutor, cols M) (int64, error) {
	ln := int64(len(o))
	if ln == 0 {
		return 0, nil
	}

	if len(cols) == 0 {
		return 0, errors.New("keeptruckin: update all requires at least one column argument")
	}

	colNames := make([]string, len(cols))
	args := make([]interface{}, len(cols))

	i := 0
	for name, value := range cols {
		colNames[i] = name
		args[i] = value
		i++
	}

	// Append all of the primary key values for each column
	for _, obj := range o {
		pkeyArgs := queries.ValuesFromMapping(reflect.Indirect(reflect.ValueOf(obj)), driverPrimaryKeyMapping)
		args = append(args, pkeyArgs...)
	}

	sql := fmt.Sprintf("UPDATE \"keeptruckin\".\"driver\" SET %s WHERE %s",
		strmangle.SetParamNames("\"", "\"", 1, colNames),
		strmangle.WhereClauseRepeated(string(dialect.LQ), string(dialect.RQ), len(colNames)+1, driverPrimaryKeyColumns, len(o)))

	if boil.IsDebug(ctx) {
		writer := boil.DebugWriterFrom(ctx)
		fmt.Fprintln(writer, sql)
		fmt.Fprintln(writer, args...)
	}
	result, err := exec.ExecContext(ctx, sql, args...)
	if err != nil {
		return 0, errors.Wrap(err, "keeptruckin: unable to update all in driver slice")
	}

	rowsAff, err := result.RowsAffected()
	if err != nil {
		return 0, errors.Wrap(err, "keeptruckin: unable to retrieve rows affected all in update all driver")
	}
	return rowsAff, nil
}

// Upsert attempts an insert using an executor, and does an update or ignore on conflict.
// See boil.Columns documentation for how to properly use updateColumns and insertColumns.
func (o *Driver) Upsert(ctx context.Context, exec boil.ContextExecutor, updateOnConflict bool, conflictColumns []string, updateColumns, insertColumns boil.Columns) error {
	if o == nil {
		return errors.New("keeptruckin: no driver provided for upsert")
	}
	if !boil.TimestampsAreSkipped(ctx) {
		currTime := time.Now().In(boil.GetLocation())

		if o.CreatedAt.IsZero() {
			o.CreatedAt = currTime
		}
		o.UpdatedAt = currTime
	}

	if err := o.doBeforeUpsertHooks(ctx, exec); err != nil {
		return err
	}

	nzDefaults := queries.NonZeroDefaultSet(driverColumnsWithDefault, o)

	// Build cache key in-line uglily - mysql vs psql problems
	buf := strmangle.GetBuffer()
	if updateOnConflict {
		buf.WriteByte('t')
	} else {
		buf.WriteByte('f')
	}
	buf.WriteByte('.')
	for _, c := range conflictColumns {
		buf.WriteString(c)
	}
	buf.WriteByte('.')
	buf.WriteString(strconv.Itoa(updateColumns.Kind))
	for _, c := range updateColumns.Cols {
		buf.WriteString(c)
	}
	buf.WriteByte('.')
	buf.WriteString(strconv.Itoa(insertColumns.Kind))
	for _, c := range insertColumns.Cols {
		buf.WriteString(c)
	}
	buf.WriteByte('.')
	for _, c := range nzDefaults {
		buf.WriteString(c)
	}
	key := buf.String()
	strmangle.PutBuffer(buf)

	driverUpsertCacheMut.RLock()
	cache, cached := driverUpsertCache[key]
	driverUpsertCacheMut.RUnlock()

	var err error

	if !cached {
		insert, ret := insertColumns.InsertColumnSet(
			driverAllColumns,
			driverColumnsWithDefault,
			driverColumnsWithoutDefault,
			nzDefaults,
		)

		update := updateColumns.UpdateColumnSet(
			driverAllColumns,
			driverPrimaryKeyColumns,
		)

		if updateOnConflict && len(update) == 0 {
			return errors.New("keeptruckin: unable to upsert driver, could not build update column list")
		}

		conflict := conflictColumns
		if len(conflict) == 0 {
			conflict = make([]string, len(driverPrimaryKeyColumns))
			copy(conflict, driverPrimaryKeyColumns)
		}
		cache.query = buildUpsertQueryPostgres(dialect, "\"keeptruckin\".\"driver\"", updateOnConflict, ret, update, conflict, insert)

		cache.valueMapping, err = queries.BindMapping(driverType, driverMapping, insert)
		if err != nil {
			return err
		}
		if len(ret) != 0 {
			cache.retMapping, err = queries.BindMapping(driverType, driverMapping, ret)
			if err != nil {
				return err
			}
		}
	}

	value := reflect.Indirect(reflect.ValueOf(o))
	vals := queries.ValuesFromMapping(value, cache.valueMapping)
	var returns []interface{}
	if len(cache.retMapping) != 0 {
		returns = queries.PtrsFromMapping(value, cache.retMapping)
	}

	if boil.IsDebug(ctx) {
		writer := boil.DebugWriterFrom(ctx)
		fmt.Fprintln(writer, cache.query)
		fmt.Fprintln(writer, vals)
	}
	if len(cache.retMapping) != 0 {
		err = exec.QueryRowContext(ctx, cache.query, vals...).Scan(returns...)
		if errors.Is(err, sql.ErrNoRows) {
			err = nil // Postgres doesn't return anything when there's no update
		}
	} else {
		_, err = exec.ExecContext(ctx, cache.query, vals...)
	}
	if err != nil {
		return errors.Wrap(err, "keeptruckin: unable to upsert driver")
	}

	if !cached {
		driverUpsertCacheMut.Lock()
		driverUpsertCache[key] = cache
		driverUpsertCacheMut.Unlock()
	}

	return o.doAfterUpsertHooks(ctx, exec)
}

// Delete deletes a single Driver record with an executor.
// Delete will match against the primary key column to find the record to delete.
func (o *Driver) Delete(ctx context.Context, exec boil.ContextExecutor) (int64, error) {
	if o == nil {
		return 0, errors.New("keeptruckin: no Driver provided for delete")
	}

	if err := o.doBeforeDeleteHooks(ctx, exec); err != nil {
		return 0, err
	}

	args := queries.ValuesFromMapping(reflect.Indirect(reflect.ValueOf(o)), driverPrimaryKeyMapping)
	sql := "DELETE FROM \"keeptruckin\".\"driver\" WHERE \"id\"=$1"

	if boil.IsDebug(ctx) {
		writer := boil.DebugWriterFrom(ctx)
		fmt.Fprintln(writer, sql)
		fmt.Fprintln(writer, args...)
	}
	result, err := exec.ExecContext(ctx, sql, args...)
	if err != nil {
		return 0, errors.Wrap(err, "keeptruckin: unable to delete from driver")
	}

	rowsAff, err := result.RowsAffected()
	if err != nil {
		return 0, errors.Wrap(err, "keeptruckin: failed to get rows affected by delete for driver")
	}

	if err := o.doAfterDeleteHooks(ctx, exec); err != nil {
		return 0, err
	}

	return rowsAff, nil
}

// DeleteAll deletes all matching rows.
func (q driverQuery) DeleteAll(ctx context.Context, exec boil.ContextExecutor) (int64, error) {
	if q.Query == nil {
		return 0, errors.New("keeptruckin: no driverQuery provided for delete all")
	}

	queries.SetDelete(q.Query)

	result, err := q.Query.ExecContext(ctx, exec)
	if err != nil {
		return 0, errors.Wrap(err, "keeptruckin: unable to delete all from driver")
	}

	rowsAff, err := result.RowsAffected()
	if err != nil {
		return 0, errors.Wrap(err, "keeptruckin: failed to get rows affected by deleteall for driver")
	}

	return rowsAff, nil
}

// DeleteAll deletes all rows in the slice, using an executor.
func (o DriverSlice) DeleteAll(ctx context.Context, exec boil.ContextExecutor) (int64, error) {
	if len(o) == 0 {
		return 0, nil
	}

	if len(driverBeforeDeleteHooks) != 0 {
		for _, obj := range o {
			if err := obj.doBeforeDeleteHooks(ctx, exec); err != nil {
				return 0, err
			}
		}
	}

	var args []interface{}
	for _, obj := range o {
		pkeyArgs := queries.ValuesFromMapping(reflect.Indirect(reflect.ValueOf(obj)), driverPrimaryKeyMapping)
		args = append(args, pkeyArgs...)
	}

	sql := "DELETE FROM \"keeptruckin\".\"driver\" WHERE " +
		strmangle.WhereClauseRepeated(string(dialect.LQ), string(dialect.RQ), 1, driverPrimaryKeyColumns, len(o))

	if boil.IsDebug(ctx) {
		writer := boil.DebugWriterFrom(ctx)
		fmt.Fprintln(writer, sql)
		fmt.Fprintln(writer, args)
	}
	result, err := exec.ExecContext(ctx, sql, args...)
	if err != nil {
		return 0, errors.Wrap(err, "keeptruckin: unable to delete all from driver slice")
	}

	rowsAff, err := result.RowsAffected()
	if err != nil {
		return 0, errors.Wrap(err, "keeptruckin: failed to get rows affected by deleteall for driver")
	}

	if len(driverAfterDeleteHooks) != 0 {
		for _, obj := range o {
			if err := obj.doAfterDeleteHooks(ctx, exec); err != nil {
				return 0, err
			}
		}
	}

	return rowsAff, nil
}

// Reload refetches the object from the database
// using the primary keys with an executor.
func (o *Driver) Reload(ctx context.Context, exec boil.ContextExecutor) error {
	ret, err := FindDriver(ctx, exec, o.ID)
	if err != nil {
		return err
	}

	*o = *ret
	return nil
}

// ReloadAll refetches every row with matching primary key column values
// and overwrites the original object slice with the newly updated slice.
func (o *DriverSlice) ReloadAll(ctx context.Context, exec boil.ContextExecutor) error {
	if o == nil || len(*o) == 0 {
		return nil
	}

	slice := DriverSlice{}
	var args []interface{}
	for _, obj := range *o {
		pkeyArgs := queries.ValuesFromMapping(reflect.Indirect(reflect.ValueOf(obj)), driverPrimaryKeyMapping)
		args = append(args, pkeyArgs...)
	}

	sql := "SELECT \"keeptruckin\".\"driver\".* FROM \"keeptruckin\".\"driver\" WHERE " +
		strmangle.WhereClauseRepeated(string(dialect.LQ), string(dialect.RQ), 1, driverPrimaryKeyColumns, len(*o))

	q := queries.Raw(sql, args...)

	err := q.Bind(ctx, exec, &slice)
	if err != nil {
		return errors.Wrap(err, "keeptruckin: unable to reload all in DriverSlice")
	}

	*o = slice

	return nil
}

// DriverExists checks if the Driver row exists.
func DriverExists(ctx context.Context, exec boil.ContextExecutor, iD string) (bool, error) {
	var exists bool
	sql := "select exists(select 1 from \"keeptruckin\".\"driver\" where \"id\"=$1 limit 1)"

	if boil.IsDebug(ctx) {
		writer := boil.DebugWriterFrom(ctx)
		fmt.Fprintln(writer, sql)
		fmt.Fprintln(writer, iD)
	}
	row := exec.QueryRowContext(ctx, sql, iD)

	err := row.Scan(&exists)
	if err != nil {
		return false, errors.Wrap(err, "keeptruckin: unable to check if driver exists")
	}

	return exists, nil
}
