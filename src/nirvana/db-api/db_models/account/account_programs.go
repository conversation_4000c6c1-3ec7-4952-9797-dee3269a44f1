// Code generated by SQLBoiler (https://github.com/volatiletech/sqlboiler). DO NOT EDIT.
// This file is meant to be re-generated in place and/or deleted at any time.

package account

import (
	"context"
	"database/sql"
	"fmt"
	"reflect"
	"strconv"
	"strings"
	"sync"
	"time"

	"github.com/friendsofgo/errors"
	"github.com/volatiletech/null/v8"
	"github.com/volatiletech/sqlboiler/v4/boil"
	"github.com/volatiletech/sqlboiler/v4/queries"
	"github.com/volatiletech/sqlboiler/v4/queries/qm"
	"github.com/volatiletech/sqlboiler/v4/queries/qmhelper"
	"github.com/volatiletech/strmangle"
)

// AccountProgram is an object representing the database table.
type AccountProgram struct {
	AccountProgramID string    `boil:"account_program_id" json:"account_program_id" toml:"account_program_id" yaml:"account_program_id"`
	AccountID        string    `boil:"account_id" json:"account_id" toml:"account_id" yaml:"account_id"`
	ProgramType      string    `boil:"program_type" json:"program_type" toml:"program_type" yaml:"program_type"`
	CreatedAt        time.Time `boil:"created_at" json:"created_at" toml:"created_at" yaml:"created_at"`
	UpdatedAt        time.Time `boil:"updated_at" json:"updated_at" toml:"updated_at" yaml:"updated_at"`
	UnenrolledAt     null.Time `boil:"unenrolled_at" json:"unenrolled_at,omitempty" toml:"unenrolled_at" yaml:"unenrolled_at,omitempty"`

	R *accountProgramR `boil:"" json:"" toml:"" yaml:""`
	L accountProgramL  `boil:"-" json:"-" toml:"-" yaml:"-"`
}

var AccountProgramColumns = struct {
	AccountProgramID string
	AccountID        string
	ProgramType      string
	CreatedAt        string
	UpdatedAt        string
	UnenrolledAt     string
}{
	AccountProgramID: "account_program_id",
	AccountID:        "account_id",
	ProgramType:      "program_type",
	CreatedAt:        "created_at",
	UpdatedAt:        "updated_at",
	UnenrolledAt:     "unenrolled_at",
}

var AccountProgramTableColumns = struct {
	AccountProgramID string
	AccountID        string
	ProgramType      string
	CreatedAt        string
	UpdatedAt        string
	UnenrolledAt     string
}{
	AccountProgramID: "account_programs.account_program_id",
	AccountID:        "account_programs.account_id",
	ProgramType:      "account_programs.program_type",
	CreatedAt:        "account_programs.created_at",
	UpdatedAt:        "account_programs.updated_at",
	UnenrolledAt:     "account_programs.unenrolled_at",
}

// Generated where

var AccountProgramWhere = struct {
	AccountProgramID whereHelperstring
	AccountID        whereHelperstring
	ProgramType      whereHelperstring
	CreatedAt        whereHelpertime_Time
	UpdatedAt        whereHelpertime_Time
	UnenrolledAt     whereHelpernull_Time
}{
	AccountProgramID: whereHelperstring{field: "\"account\".\"account_programs\".\"account_program_id\""},
	AccountID:        whereHelperstring{field: "\"account\".\"account_programs\".\"account_id\""},
	ProgramType:      whereHelperstring{field: "\"account\".\"account_programs\".\"program_type\""},
	CreatedAt:        whereHelpertime_Time{field: "\"account\".\"account_programs\".\"created_at\""},
	UpdatedAt:        whereHelpertime_Time{field: "\"account\".\"account_programs\".\"updated_at\""},
	UnenrolledAt:     whereHelpernull_Time{field: "\"account\".\"account_programs\".\"unenrolled_at\""},
}

// AccountProgramRels is where relationship names are stored.
var AccountProgramRels = struct {
	Account string
}{
	Account: "Account",
}

// accountProgramR is where relationships are stored.
type accountProgramR struct {
	Account *Account `boil:"Account" json:"Account" toml:"Account" yaml:"Account"`
}

// NewStruct creates a new relationship struct
func (*accountProgramR) NewStruct() *accountProgramR {
	return &accountProgramR{}
}

// accountProgramL is where Load methods for each relationship are stored.
type accountProgramL struct{}

var (
	accountProgramAllColumns            = []string{"account_program_id", "account_id", "program_type", "created_at", "updated_at", "unenrolled_at"}
	accountProgramColumnsWithoutDefault = []string{"account_program_id", "account_id", "program_type"}
	accountProgramColumnsWithDefault    = []string{"created_at", "updated_at", "unenrolled_at"}
	accountProgramPrimaryKeyColumns     = []string{"account_program_id"}
	accountProgramGeneratedColumns      = []string{}
)

type (
	// AccountProgramSlice is an alias for a slice of pointers to AccountProgram.
	// This should almost always be used instead of []AccountProgram.
	AccountProgramSlice []*AccountProgram
	// AccountProgramHook is the signature for custom AccountProgram hook methods
	AccountProgramHook func(context.Context, boil.ContextExecutor, *AccountProgram) error

	accountProgramQuery struct {
		*queries.Query
	}
)

// Cache for insert, update and upsert
var (
	accountProgramType                 = reflect.TypeOf(&AccountProgram{})
	accountProgramMapping              = queries.MakeStructMapping(accountProgramType)
	accountProgramPrimaryKeyMapping, _ = queries.BindMapping(accountProgramType, accountProgramMapping, accountProgramPrimaryKeyColumns)
	accountProgramInsertCacheMut       sync.RWMutex
	accountProgramInsertCache          = make(map[string]insertCache)
	accountProgramUpdateCacheMut       sync.RWMutex
	accountProgramUpdateCache          = make(map[string]updateCache)
	accountProgramUpsertCacheMut       sync.RWMutex
	accountProgramUpsertCache          = make(map[string]insertCache)
)

var (
	// Force time package dependency for automated UpdatedAt/CreatedAt.
	_ = time.Second
	// Force qmhelper dependency for where clause generation (which doesn't
	// always happen)
	_ = qmhelper.Where
)

var accountProgramAfterSelectHooks []AccountProgramHook

var accountProgramBeforeInsertHooks []AccountProgramHook
var accountProgramAfterInsertHooks []AccountProgramHook

var accountProgramBeforeUpdateHooks []AccountProgramHook
var accountProgramAfterUpdateHooks []AccountProgramHook

var accountProgramBeforeDeleteHooks []AccountProgramHook
var accountProgramAfterDeleteHooks []AccountProgramHook

var accountProgramBeforeUpsertHooks []AccountProgramHook
var accountProgramAfterUpsertHooks []AccountProgramHook

// doAfterSelectHooks executes all "after Select" hooks.
func (o *AccountProgram) doAfterSelectHooks(ctx context.Context, exec boil.ContextExecutor) (err error) {
	if boil.HooksAreSkipped(ctx) {
		return nil
	}

	for _, hook := range accountProgramAfterSelectHooks {
		if err := hook(ctx, exec, o); err != nil {
			return err
		}
	}

	return nil
}

// doBeforeInsertHooks executes all "before insert" hooks.
func (o *AccountProgram) doBeforeInsertHooks(ctx context.Context, exec boil.ContextExecutor) (err error) {
	if boil.HooksAreSkipped(ctx) {
		return nil
	}

	for _, hook := range accountProgramBeforeInsertHooks {
		if err := hook(ctx, exec, o); err != nil {
			return err
		}
	}

	return nil
}

// doAfterInsertHooks executes all "after Insert" hooks.
func (o *AccountProgram) doAfterInsertHooks(ctx context.Context, exec boil.ContextExecutor) (err error) {
	if boil.HooksAreSkipped(ctx) {
		return nil
	}

	for _, hook := range accountProgramAfterInsertHooks {
		if err := hook(ctx, exec, o); err != nil {
			return err
		}
	}

	return nil
}

// doBeforeUpdateHooks executes all "before Update" hooks.
func (o *AccountProgram) doBeforeUpdateHooks(ctx context.Context, exec boil.ContextExecutor) (err error) {
	if boil.HooksAreSkipped(ctx) {
		return nil
	}

	for _, hook := range accountProgramBeforeUpdateHooks {
		if err := hook(ctx, exec, o); err != nil {
			return err
		}
	}

	return nil
}

// doAfterUpdateHooks executes all "after Update" hooks.
func (o *AccountProgram) doAfterUpdateHooks(ctx context.Context, exec boil.ContextExecutor) (err error) {
	if boil.HooksAreSkipped(ctx) {
		return nil
	}

	for _, hook := range accountProgramAfterUpdateHooks {
		if err := hook(ctx, exec, o); err != nil {
			return err
		}
	}

	return nil
}

// doBeforeDeleteHooks executes all "before Delete" hooks.
func (o *AccountProgram) doBeforeDeleteHooks(ctx context.Context, exec boil.ContextExecutor) (err error) {
	if boil.HooksAreSkipped(ctx) {
		return nil
	}

	for _, hook := range accountProgramBeforeDeleteHooks {
		if err := hook(ctx, exec, o); err != nil {
			return err
		}
	}

	return nil
}

// doAfterDeleteHooks executes all "after Delete" hooks.
func (o *AccountProgram) doAfterDeleteHooks(ctx context.Context, exec boil.ContextExecutor) (err error) {
	if boil.HooksAreSkipped(ctx) {
		return nil
	}

	for _, hook := range accountProgramAfterDeleteHooks {
		if err := hook(ctx, exec, o); err != nil {
			return err
		}
	}

	return nil
}

// doBeforeUpsertHooks executes all "before Upsert" hooks.
func (o *AccountProgram) doBeforeUpsertHooks(ctx context.Context, exec boil.ContextExecutor) (err error) {
	if boil.HooksAreSkipped(ctx) {
		return nil
	}

	for _, hook := range accountProgramBeforeUpsertHooks {
		if err := hook(ctx, exec, o); err != nil {
			return err
		}
	}

	return nil
}

// doAfterUpsertHooks executes all "after Upsert" hooks.
func (o *AccountProgram) doAfterUpsertHooks(ctx context.Context, exec boil.ContextExecutor) (err error) {
	if boil.HooksAreSkipped(ctx) {
		return nil
	}

	for _, hook := range accountProgramAfterUpsertHooks {
		if err := hook(ctx, exec, o); err != nil {
			return err
		}
	}

	return nil
}

// AddAccountProgramHook registers your hook function for all future operations.
func AddAccountProgramHook(hookPoint boil.HookPoint, accountProgramHook AccountProgramHook) {
	switch hookPoint {
	case boil.AfterSelectHook:
		accountProgramAfterSelectHooks = append(accountProgramAfterSelectHooks, accountProgramHook)
	case boil.BeforeInsertHook:
		accountProgramBeforeInsertHooks = append(accountProgramBeforeInsertHooks, accountProgramHook)
	case boil.AfterInsertHook:
		accountProgramAfterInsertHooks = append(accountProgramAfterInsertHooks, accountProgramHook)
	case boil.BeforeUpdateHook:
		accountProgramBeforeUpdateHooks = append(accountProgramBeforeUpdateHooks, accountProgramHook)
	case boil.AfterUpdateHook:
		accountProgramAfterUpdateHooks = append(accountProgramAfterUpdateHooks, accountProgramHook)
	case boil.BeforeDeleteHook:
		accountProgramBeforeDeleteHooks = append(accountProgramBeforeDeleteHooks, accountProgramHook)
	case boil.AfterDeleteHook:
		accountProgramAfterDeleteHooks = append(accountProgramAfterDeleteHooks, accountProgramHook)
	case boil.BeforeUpsertHook:
		accountProgramBeforeUpsertHooks = append(accountProgramBeforeUpsertHooks, accountProgramHook)
	case boil.AfterUpsertHook:
		accountProgramAfterUpsertHooks = append(accountProgramAfterUpsertHooks, accountProgramHook)
	}
}

// One returns a single accountProgram record from the query.
func (q accountProgramQuery) One(ctx context.Context, exec boil.ContextExecutor) (*AccountProgram, error) {
	o := &AccountProgram{}

	queries.SetLimit(q.Query, 1)

	err := q.Bind(ctx, exec, o)
	if err != nil {
		if errors.Is(err, sql.ErrNoRows) {
			return nil, sql.ErrNoRows
		}
		return nil, errors.Wrap(err, "account: failed to execute a one query for account_programs")
	}

	if err := o.doAfterSelectHooks(ctx, exec); err != nil {
		return o, err
	}

	return o, nil
}

// All returns all AccountProgram records from the query.
func (q accountProgramQuery) All(ctx context.Context, exec boil.ContextExecutor) (AccountProgramSlice, error) {
	var o []*AccountProgram

	err := q.Bind(ctx, exec, &o)
	if err != nil {
		return nil, errors.Wrap(err, "account: failed to assign all query results to AccountProgram slice")
	}

	if len(accountProgramAfterSelectHooks) != 0 {
		for _, obj := range o {
			if err := obj.doAfterSelectHooks(ctx, exec); err != nil {
				return o, err
			}
		}
	}

	return o, nil
}

// Count returns the count of all AccountProgram records in the query.
func (q accountProgramQuery) Count(ctx context.Context, exec boil.ContextExecutor) (int64, error) {
	var count int64

	queries.SetSelect(q.Query, nil)
	queries.SetCount(q.Query)

	err := q.Query.QueryRowContext(ctx, exec).Scan(&count)
	if err != nil {
		return 0, errors.Wrap(err, "account: failed to count account_programs rows")
	}

	return count, nil
}

// Exists checks if the row exists in the table.
func (q accountProgramQuery) Exists(ctx context.Context, exec boil.ContextExecutor) (bool, error) {
	var count int64

	queries.SetSelect(q.Query, nil)
	queries.SetCount(q.Query)
	queries.SetLimit(q.Query, 1)

	err := q.Query.QueryRowContext(ctx, exec).Scan(&count)
	if err != nil {
		return false, errors.Wrap(err, "account: failed to check if account_programs exists")
	}

	return count > 0, nil
}

// Account pointed to by the foreign key.
func (o *AccountProgram) Account(mods ...qm.QueryMod) accountQuery {
	queryMods := []qm.QueryMod{
		qm.Where("\"account_id\" = ?", o.AccountID),
	}

	queryMods = append(queryMods, mods...)

	return Accounts(queryMods...)
}

// LoadAccount allows an eager lookup of values, cached into the
// loaded structs of the objects. This is for an N-1 relationship.
func (accountProgramL) LoadAccount(ctx context.Context, e boil.ContextExecutor, singular bool, maybeAccountProgram interface{}, mods queries.Applicator) error {
	var slice []*AccountProgram
	var object *AccountProgram

	if singular {
		object = maybeAccountProgram.(*AccountProgram)
	} else {
		slice = *maybeAccountProgram.(*[]*AccountProgram)
	}

	args := make([]interface{}, 0, 1)
	if singular {
		if object.R == nil {
			object.R = &accountProgramR{}
		}
		args = append(args, object.AccountID)

	} else {
	Outer:
		for _, obj := range slice {
			if obj.R == nil {
				obj.R = &accountProgramR{}
			}

			for _, a := range args {
				if a == obj.AccountID {
					continue Outer
				}
			}

			args = append(args, obj.AccountID)

		}
	}

	if len(args) == 0 {
		return nil
	}

	query := NewQuery(
		qm.From(`account.accounts`),
		qm.WhereIn(`account.accounts.account_id in ?`, args...),
	)
	if mods != nil {
		mods.Apply(query)
	}

	results, err := query.QueryContext(ctx, e)
	if err != nil {
		return errors.Wrap(err, "failed to eager load Account")
	}

	var resultSlice []*Account
	if err = queries.Bind(results, &resultSlice); err != nil {
		return errors.Wrap(err, "failed to bind eager loaded slice Account")
	}

	if err = results.Close(); err != nil {
		return errors.Wrap(err, "failed to close results of eager load for accounts")
	}
	if err = results.Err(); err != nil {
		return errors.Wrap(err, "error occurred during iteration of eager loaded relations for accounts")
	}

	if len(accountProgramAfterSelectHooks) != 0 {
		for _, obj := range resultSlice {
			if err := obj.doAfterSelectHooks(ctx, e); err != nil {
				return err
			}
		}
	}

	if len(resultSlice) == 0 {
		return nil
	}

	if singular {
		foreign := resultSlice[0]
		object.R.Account = foreign
		if foreign.R == nil {
			foreign.R = &accountR{}
		}
		foreign.R.AccountPrograms = append(foreign.R.AccountPrograms, object)
		return nil
	}

	for _, local := range slice {
		for _, foreign := range resultSlice {
			if local.AccountID == foreign.AccountID {
				local.R.Account = foreign
				if foreign.R == nil {
					foreign.R = &accountR{}
				}
				foreign.R.AccountPrograms = append(foreign.R.AccountPrograms, local)
				break
			}
		}
	}

	return nil
}

// SetAccount of the accountProgram to the related item.
// Sets o.R.Account to related.
// Adds o to related.R.AccountPrograms.
func (o *AccountProgram) SetAccount(ctx context.Context, exec boil.ContextExecutor, insert bool, related *Account) error {
	var err error
	if insert {
		if err = related.Insert(ctx, exec, boil.Infer()); err != nil {
			return errors.Wrap(err, "failed to insert into foreign table")
		}
	}

	updateQuery := fmt.Sprintf(
		"UPDATE \"account\".\"account_programs\" SET %s WHERE %s",
		strmangle.SetParamNames("\"", "\"", 1, []string{"account_id"}),
		strmangle.WhereClause("\"", "\"", 2, accountProgramPrimaryKeyColumns),
	)
	values := []interface{}{related.AccountID, o.AccountProgramID}

	if boil.IsDebug(ctx) {
		writer := boil.DebugWriterFrom(ctx)
		fmt.Fprintln(writer, updateQuery)
		fmt.Fprintln(writer, values)
	}
	if _, err = exec.ExecContext(ctx, updateQuery, values...); err != nil {
		return errors.Wrap(err, "failed to update local table")
	}

	o.AccountID = related.AccountID
	if o.R == nil {
		o.R = &accountProgramR{
			Account: related,
		}
	} else {
		o.R.Account = related
	}

	if related.R == nil {
		related.R = &accountR{
			AccountPrograms: AccountProgramSlice{o},
		}
	} else {
		related.R.AccountPrograms = append(related.R.AccountPrograms, o)
	}

	return nil
}

// AccountPrograms retrieves all the records using an executor.
func AccountPrograms(mods ...qm.QueryMod) accountProgramQuery {
	mods = append(mods, qm.From("\"account\".\"account_programs\""))
	q := NewQuery(mods...)
	if len(queries.GetSelect(q)) == 0 {
		queries.SetSelect(q, []string{"\"account\".\"account_programs\".*"})
	}

	return accountProgramQuery{q}
}

// FindAccountProgram retrieves a single record by ID with an executor.
// If selectCols is empty Find will return all columns.
func FindAccountProgram(ctx context.Context, exec boil.ContextExecutor, accountProgramID string, selectCols ...string) (*AccountProgram, error) {
	accountProgramObj := &AccountProgram{}

	sel := "*"
	if len(selectCols) > 0 {
		sel = strings.Join(strmangle.IdentQuoteSlice(dialect.LQ, dialect.RQ, selectCols), ",")
	}
	query := fmt.Sprintf(
		"select %s from \"account\".\"account_programs\" where \"account_program_id\"=$1", sel,
	)

	q := queries.Raw(query, accountProgramID)

	err := q.Bind(ctx, exec, accountProgramObj)
	if err != nil {
		if errors.Is(err, sql.ErrNoRows) {
			return nil, sql.ErrNoRows
		}
		return nil, errors.Wrap(err, "account: unable to select from account_programs")
	}

	if err = accountProgramObj.doAfterSelectHooks(ctx, exec); err != nil {
		return accountProgramObj, err
	}

	return accountProgramObj, nil
}

// Insert a single record using an executor.
// See boil.Columns.InsertColumnSet documentation to understand column list inference for inserts.
func (o *AccountProgram) Insert(ctx context.Context, exec boil.ContextExecutor, columns boil.Columns) error {
	if o == nil {
		return errors.New("account: no account_programs provided for insertion")
	}

	var err error
	if !boil.TimestampsAreSkipped(ctx) {
		currTime := time.Now().In(boil.GetLocation())

		if o.CreatedAt.IsZero() {
			o.CreatedAt = currTime
		}
		if o.UpdatedAt.IsZero() {
			o.UpdatedAt = currTime
		}
	}

	if err := o.doBeforeInsertHooks(ctx, exec); err != nil {
		return err
	}

	nzDefaults := queries.NonZeroDefaultSet(accountProgramColumnsWithDefault, o)

	key := makeCacheKey(columns, nzDefaults)
	accountProgramInsertCacheMut.RLock()
	cache, cached := accountProgramInsertCache[key]
	accountProgramInsertCacheMut.RUnlock()

	if !cached {
		wl, returnColumns := columns.InsertColumnSet(
			accountProgramAllColumns,
			accountProgramColumnsWithDefault,
			accountProgramColumnsWithoutDefault,
			nzDefaults,
		)

		cache.valueMapping, err = queries.BindMapping(accountProgramType, accountProgramMapping, wl)
		if err != nil {
			return err
		}
		cache.retMapping, err = queries.BindMapping(accountProgramType, accountProgramMapping, returnColumns)
		if err != nil {
			return err
		}
		if len(wl) != 0 {
			cache.query = fmt.Sprintf("INSERT INTO \"account\".\"account_programs\" (\"%s\") %%sVALUES (%s)%%s", strings.Join(wl, "\",\""), strmangle.Placeholders(dialect.UseIndexPlaceholders, len(wl), 1, 1))
		} else {
			cache.query = "INSERT INTO \"account\".\"account_programs\" %sDEFAULT VALUES%s"
		}

		var queryOutput, queryReturning string

		if len(cache.retMapping) != 0 {
			queryReturning = fmt.Sprintf(" RETURNING \"%s\"", strings.Join(returnColumns, "\",\""))
		}

		cache.query = fmt.Sprintf(cache.query, queryOutput, queryReturning)
	}

	value := reflect.Indirect(reflect.ValueOf(o))
	vals := queries.ValuesFromMapping(value, cache.valueMapping)

	if boil.IsDebug(ctx) {
		writer := boil.DebugWriterFrom(ctx)
		fmt.Fprintln(writer, cache.query)
		fmt.Fprintln(writer, vals)
	}

	if len(cache.retMapping) != 0 {
		err = exec.QueryRowContext(ctx, cache.query, vals...).Scan(queries.PtrsFromMapping(value, cache.retMapping)...)
	} else {
		_, err = exec.ExecContext(ctx, cache.query, vals...)
	}

	if err != nil {
		return errors.Wrap(err, "account: unable to insert into account_programs")
	}

	if !cached {
		accountProgramInsertCacheMut.Lock()
		accountProgramInsertCache[key] = cache
		accountProgramInsertCacheMut.Unlock()
	}

	return o.doAfterInsertHooks(ctx, exec)
}

// Update uses an executor to update the AccountProgram.
// See boil.Columns.UpdateColumnSet documentation to understand column list inference for updates.
// Update does not automatically update the record in case of default values. Use .Reload() to refresh the records.
func (o *AccountProgram) Update(ctx context.Context, exec boil.ContextExecutor, columns boil.Columns) (int64, error) {
	if !boil.TimestampsAreSkipped(ctx) {
		currTime := time.Now().In(boil.GetLocation())

		o.UpdatedAt = currTime
	}

	var err error
	if err = o.doBeforeUpdateHooks(ctx, exec); err != nil {
		return 0, err
	}
	key := makeCacheKey(columns, nil)
	accountProgramUpdateCacheMut.RLock()
	cache, cached := accountProgramUpdateCache[key]
	accountProgramUpdateCacheMut.RUnlock()

	if !cached {
		wl := columns.UpdateColumnSet(
			accountProgramAllColumns,
			accountProgramPrimaryKeyColumns,
		)

		if !columns.IsWhitelist() {
			wl = strmangle.SetComplement(wl, []string{"created_at"})
		}
		if len(wl) == 0 {
			return 0, errors.New("account: unable to update account_programs, could not build whitelist")
		}

		cache.query = fmt.Sprintf("UPDATE \"account\".\"account_programs\" SET %s WHERE %s",
			strmangle.SetParamNames("\"", "\"", 1, wl),
			strmangle.WhereClause("\"", "\"", len(wl)+1, accountProgramPrimaryKeyColumns),
		)
		cache.valueMapping, err = queries.BindMapping(accountProgramType, accountProgramMapping, append(wl, accountProgramPrimaryKeyColumns...))
		if err != nil {
			return 0, err
		}
	}

	values := queries.ValuesFromMapping(reflect.Indirect(reflect.ValueOf(o)), cache.valueMapping)

	if boil.IsDebug(ctx) {
		writer := boil.DebugWriterFrom(ctx)
		fmt.Fprintln(writer, cache.query)
		fmt.Fprintln(writer, values)
	}
	var result sql.Result
	result, err = exec.ExecContext(ctx, cache.query, values...)
	if err != nil {
		return 0, errors.Wrap(err, "account: unable to update account_programs row")
	}

	rowsAff, err := result.RowsAffected()
	if err != nil {
		return 0, errors.Wrap(err, "account: failed to get rows affected by update for account_programs")
	}

	if !cached {
		accountProgramUpdateCacheMut.Lock()
		accountProgramUpdateCache[key] = cache
		accountProgramUpdateCacheMut.Unlock()
	}

	return rowsAff, o.doAfterUpdateHooks(ctx, exec)
}

// UpdateAll updates all rows with the specified column values.
func (q accountProgramQuery) UpdateAll(ctx context.Context, exec boil.ContextExecutor, cols M) (int64, error) {
	queries.SetUpdate(q.Query, cols)

	result, err := q.Query.ExecContext(ctx, exec)
	if err != nil {
		return 0, errors.Wrap(err, "account: unable to update all for account_programs")
	}

	rowsAff, err := result.RowsAffected()
	if err != nil {
		return 0, errors.Wrap(err, "account: unable to retrieve rows affected for account_programs")
	}

	return rowsAff, nil
}

// UpdateAll updates all rows with the specified column values, using an executor.
func (o AccountProgramSlice) UpdateAll(ctx context.Context, exec boil.ContextExecutor, cols M) (int64, error) {
	ln := int64(len(o))
	if ln == 0 {
		return 0, nil
	}

	if len(cols) == 0 {
		return 0, errors.New("account: update all requires at least one column argument")
	}

	colNames := make([]string, len(cols))
	args := make([]interface{}, len(cols))

	i := 0
	for name, value := range cols {
		colNames[i] = name
		args[i] = value
		i++
	}

	// Append all of the primary key values for each column
	for _, obj := range o {
		pkeyArgs := queries.ValuesFromMapping(reflect.Indirect(reflect.ValueOf(obj)), accountProgramPrimaryKeyMapping)
		args = append(args, pkeyArgs...)
	}

	sql := fmt.Sprintf("UPDATE \"account\".\"account_programs\" SET %s WHERE %s",
		strmangle.SetParamNames("\"", "\"", 1, colNames),
		strmangle.WhereClauseRepeated(string(dialect.LQ), string(dialect.RQ), len(colNames)+1, accountProgramPrimaryKeyColumns, len(o)))

	if boil.IsDebug(ctx) {
		writer := boil.DebugWriterFrom(ctx)
		fmt.Fprintln(writer, sql)
		fmt.Fprintln(writer, args...)
	}
	result, err := exec.ExecContext(ctx, sql, args...)
	if err != nil {
		return 0, errors.Wrap(err, "account: unable to update all in accountProgram slice")
	}

	rowsAff, err := result.RowsAffected()
	if err != nil {
		return 0, errors.Wrap(err, "account: unable to retrieve rows affected all in update all accountProgram")
	}
	return rowsAff, nil
}

// Upsert attempts an insert using an executor, and does an update or ignore on conflict.
// See boil.Columns documentation for how to properly use updateColumns and insertColumns.
func (o *AccountProgram) Upsert(ctx context.Context, exec boil.ContextExecutor, updateOnConflict bool, conflictColumns []string, updateColumns, insertColumns boil.Columns) error {
	if o == nil {
		return errors.New("account: no account_programs provided for upsert")
	}
	if !boil.TimestampsAreSkipped(ctx) {
		currTime := time.Now().In(boil.GetLocation())

		if o.CreatedAt.IsZero() {
			o.CreatedAt = currTime
		}
		o.UpdatedAt = currTime
	}

	if err := o.doBeforeUpsertHooks(ctx, exec); err != nil {
		return err
	}

	nzDefaults := queries.NonZeroDefaultSet(accountProgramColumnsWithDefault, o)

	// Build cache key in-line uglily - mysql vs psql problems
	buf := strmangle.GetBuffer()
	if updateOnConflict {
		buf.WriteByte('t')
	} else {
		buf.WriteByte('f')
	}
	buf.WriteByte('.')
	for _, c := range conflictColumns {
		buf.WriteString(c)
	}
	buf.WriteByte('.')
	buf.WriteString(strconv.Itoa(updateColumns.Kind))
	for _, c := range updateColumns.Cols {
		buf.WriteString(c)
	}
	buf.WriteByte('.')
	buf.WriteString(strconv.Itoa(insertColumns.Kind))
	for _, c := range insertColumns.Cols {
		buf.WriteString(c)
	}
	buf.WriteByte('.')
	for _, c := range nzDefaults {
		buf.WriteString(c)
	}
	key := buf.String()
	strmangle.PutBuffer(buf)

	accountProgramUpsertCacheMut.RLock()
	cache, cached := accountProgramUpsertCache[key]
	accountProgramUpsertCacheMut.RUnlock()

	var err error

	if !cached {
		insert, ret := insertColumns.InsertColumnSet(
			accountProgramAllColumns,
			accountProgramColumnsWithDefault,
			accountProgramColumnsWithoutDefault,
			nzDefaults,
		)

		update := updateColumns.UpdateColumnSet(
			accountProgramAllColumns,
			accountProgramPrimaryKeyColumns,
		)

		if updateOnConflict && len(update) == 0 {
			return errors.New("account: unable to upsert account_programs, could not build update column list")
		}

		conflict := conflictColumns
		if len(conflict) == 0 {
			conflict = make([]string, len(accountProgramPrimaryKeyColumns))
			copy(conflict, accountProgramPrimaryKeyColumns)
		}
		cache.query = buildUpsertQueryPostgres(dialect, "\"account\".\"account_programs\"", updateOnConflict, ret, update, conflict, insert)

		cache.valueMapping, err = queries.BindMapping(accountProgramType, accountProgramMapping, insert)
		if err != nil {
			return err
		}
		if len(ret) != 0 {
			cache.retMapping, err = queries.BindMapping(accountProgramType, accountProgramMapping, ret)
			if err != nil {
				return err
			}
		}
	}

	value := reflect.Indirect(reflect.ValueOf(o))
	vals := queries.ValuesFromMapping(value, cache.valueMapping)
	var returns []interface{}
	if len(cache.retMapping) != 0 {
		returns = queries.PtrsFromMapping(value, cache.retMapping)
	}

	if boil.IsDebug(ctx) {
		writer := boil.DebugWriterFrom(ctx)
		fmt.Fprintln(writer, cache.query)
		fmt.Fprintln(writer, vals)
	}
	if len(cache.retMapping) != 0 {
		err = exec.QueryRowContext(ctx, cache.query, vals...).Scan(returns...)
		if errors.Is(err, sql.ErrNoRows) {
			err = nil // Postgres doesn't return anything when there's no update
		}
	} else {
		_, err = exec.ExecContext(ctx, cache.query, vals...)
	}
	if err != nil {
		return errors.Wrap(err, "account: unable to upsert account_programs")
	}

	if !cached {
		accountProgramUpsertCacheMut.Lock()
		accountProgramUpsertCache[key] = cache
		accountProgramUpsertCacheMut.Unlock()
	}

	return o.doAfterUpsertHooks(ctx, exec)
}

// Delete deletes a single AccountProgram record with an executor.
// Delete will match against the primary key column to find the record to delete.
func (o *AccountProgram) Delete(ctx context.Context, exec boil.ContextExecutor) (int64, error) {
	if o == nil {
		return 0, errors.New("account: no AccountProgram provided for delete")
	}

	if err := o.doBeforeDeleteHooks(ctx, exec); err != nil {
		return 0, err
	}

	args := queries.ValuesFromMapping(reflect.Indirect(reflect.ValueOf(o)), accountProgramPrimaryKeyMapping)
	sql := "DELETE FROM \"account\".\"account_programs\" WHERE \"account_program_id\"=$1"

	if boil.IsDebug(ctx) {
		writer := boil.DebugWriterFrom(ctx)
		fmt.Fprintln(writer, sql)
		fmt.Fprintln(writer, args...)
	}
	result, err := exec.ExecContext(ctx, sql, args...)
	if err != nil {
		return 0, errors.Wrap(err, "account: unable to delete from account_programs")
	}

	rowsAff, err := result.RowsAffected()
	if err != nil {
		return 0, errors.Wrap(err, "account: failed to get rows affected by delete for account_programs")
	}

	if err := o.doAfterDeleteHooks(ctx, exec); err != nil {
		return 0, err
	}

	return rowsAff, nil
}

// DeleteAll deletes all matching rows.
func (q accountProgramQuery) DeleteAll(ctx context.Context, exec boil.ContextExecutor) (int64, error) {
	if q.Query == nil {
		return 0, errors.New("account: no accountProgramQuery provided for delete all")
	}

	queries.SetDelete(q.Query)

	result, err := q.Query.ExecContext(ctx, exec)
	if err != nil {
		return 0, errors.Wrap(err, "account: unable to delete all from account_programs")
	}

	rowsAff, err := result.RowsAffected()
	if err != nil {
		return 0, errors.Wrap(err, "account: failed to get rows affected by deleteall for account_programs")
	}

	return rowsAff, nil
}

// DeleteAll deletes all rows in the slice, using an executor.
func (o AccountProgramSlice) DeleteAll(ctx context.Context, exec boil.ContextExecutor) (int64, error) {
	if len(o) == 0 {
		return 0, nil
	}

	if len(accountProgramBeforeDeleteHooks) != 0 {
		for _, obj := range o {
			if err := obj.doBeforeDeleteHooks(ctx, exec); err != nil {
				return 0, err
			}
		}
	}

	var args []interface{}
	for _, obj := range o {
		pkeyArgs := queries.ValuesFromMapping(reflect.Indirect(reflect.ValueOf(obj)), accountProgramPrimaryKeyMapping)
		args = append(args, pkeyArgs...)
	}

	sql := "DELETE FROM \"account\".\"account_programs\" WHERE " +
		strmangle.WhereClauseRepeated(string(dialect.LQ), string(dialect.RQ), 1, accountProgramPrimaryKeyColumns, len(o))

	if boil.IsDebug(ctx) {
		writer := boil.DebugWriterFrom(ctx)
		fmt.Fprintln(writer, sql)
		fmt.Fprintln(writer, args)
	}
	result, err := exec.ExecContext(ctx, sql, args...)
	if err != nil {
		return 0, errors.Wrap(err, "account: unable to delete all from accountProgram slice")
	}

	rowsAff, err := result.RowsAffected()
	if err != nil {
		return 0, errors.Wrap(err, "account: failed to get rows affected by deleteall for account_programs")
	}

	if len(accountProgramAfterDeleteHooks) != 0 {
		for _, obj := range o {
			if err := obj.doAfterDeleteHooks(ctx, exec); err != nil {
				return 0, err
			}
		}
	}

	return rowsAff, nil
}

// Reload refetches the object from the database
// using the primary keys with an executor.
func (o *AccountProgram) Reload(ctx context.Context, exec boil.ContextExecutor) error {
	ret, err := FindAccountProgram(ctx, exec, o.AccountProgramID)
	if err != nil {
		return err
	}

	*o = *ret
	return nil
}

// ReloadAll refetches every row with matching primary key column values
// and overwrites the original object slice with the newly updated slice.
func (o *AccountProgramSlice) ReloadAll(ctx context.Context, exec boil.ContextExecutor) error {
	if o == nil || len(*o) == 0 {
		return nil
	}

	slice := AccountProgramSlice{}
	var args []interface{}
	for _, obj := range *o {
		pkeyArgs := queries.ValuesFromMapping(reflect.Indirect(reflect.ValueOf(obj)), accountProgramPrimaryKeyMapping)
		args = append(args, pkeyArgs...)
	}

	sql := "SELECT \"account\".\"account_programs\".* FROM \"account\".\"account_programs\" WHERE " +
		strmangle.WhereClauseRepeated(string(dialect.LQ), string(dialect.RQ), 1, accountProgramPrimaryKeyColumns, len(*o))

	q := queries.Raw(sql, args...)

	err := q.Bind(ctx, exec, &slice)
	if err != nil {
		return errors.Wrap(err, "account: unable to reload all in AccountProgramSlice")
	}

	*o = slice

	return nil
}

// AccountProgramExists checks if the AccountProgram row exists.
func AccountProgramExists(ctx context.Context, exec boil.ContextExecutor, accountProgramID string) (bool, error) {
	var exists bool
	sql := "select exists(select 1 from \"account\".\"account_programs\" where \"account_program_id\"=$1 limit 1)"

	if boil.IsDebug(ctx) {
		writer := boil.DebugWriterFrom(ctx)
		fmt.Fprintln(writer, sql)
		fmt.Fprintln(writer, accountProgramID)
	}
	row := exec.QueryRowContext(ctx, sql, accountProgramID)

	err := row.Scan(&exists)
	if err != nil {
		return false, errors.Wrap(err, "account: unable to check if account_programs exists")
	}

	return exists, nil
}
