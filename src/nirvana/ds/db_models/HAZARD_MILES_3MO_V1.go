// Code generated by SQLBoiler (https://github.com/volatiletech/sqlboiler). DO NOT EDIT.
// This file is meant to be re-generated in place and/or deleted at any time.

package ds

import (
	"context"
	"database/sql"
	"fmt"
	"reflect"
	"strconv"
	"strings"
	"sync"
	"time"

	"github.com/friendsofgo/errors"
	"github.com/volatiletech/null/v8"
	"github.com/volatiletech/sqlboiler/v4/boil"
	"github.com/volatiletech/sqlboiler/v4/queries"
	"github.com/volatiletech/sqlboiler/v4/queries/qm"
	"github.com/volatiletech/sqlboiler/v4/queries/qmhelper"
	"github.com/volatiletech/sqlboiler/v4/types"
	"github.com/volatiletech/strmangle"
)

// HAZARD_MILES_3MO_V1 is an object representing the database table.
type HAZARD_MILES_3MO_V1 struct {
	END_DATE          time.Time         `boil:"END_DATE" json:"END_DATE" toml:"END_DATE" yaml:"END_DATE"`
	START_DATE        time.Time         `boil:"START_DATE" json:"START_DATE" toml:"START_DATE" yaml:"START_DATE"`
	UNIQUE_KEY        null.String       `boil:"UNIQUE_KEY" json:"UNIQUE_KEY,omitempty" toml:"UNIQUE_KEY" yaml:"UNIQUE_KEY,omitempty"`
	UPDATED_AT        null.Time         `boil:"UPDATED_AT" json:"UPDATED_AT,omitempty" toml:"UPDATED_AT" yaml:"UPDATED_AT,omitempty"`
	GPS_MILEAGE       types.NullDecimal `boil:"GPS_MILEAGE" json:"GPS_MILEAGE,omitempty" toml:"GPS_MILEAGE" yaml:"GPS_MILEAGE,omitempty"`
	HAZARD_DAYS       null.Int64        `boil:"HAZARD_DAYS" json:"HAZARD_DAYS,omitempty" toml:"HAZARD_DAYS" yaml:"HAZARD_DAYS,omitempty"`
	HAZARD_VINS       null.Int64        `boil:"HAZARD_VINS" json:"HAZARD_VINS,omitempty" toml:"HAZARD_VINS" yaml:"HAZARD_VINS,omitempty"`
	HAZARD_MILES      types.NullDecimal `boil:"HAZARD_MILES" json:"HAZARD_MILES,omitempty" toml:"HAZARD_MILES" yaml:"HAZARD_MILES,omitempty"`
	CONNECTION_ID     string            `boil:"CONNECTION_ID" json:"CONNECTION_ID" toml:"CONNECTION_ID" yaml:"CONNECTION_ID"`
	HAZARD_PERCENTAGE types.NullDecimal `boil:"HAZARD_PERCENTAGE" json:"HAZARD_PERCENTAGE,omitempty" toml:"HAZARD_PERCENTAGE" yaml:"HAZARD_PERCENTAGE,omitempty"`

	R *hAZARDMILES_3MO_V1R `boil:"" json:"" toml:"" yaml:""`
	L hAZARDMILES_3MO_V1L  `boil:"-" json:"-" toml:"-" yaml:"-"`
}

var HAZARD_MILES_3MO_V1Columns = struct {
	END_DATE          string
	START_DATE        string
	UNIQUE_KEY        string
	UPDATED_AT        string
	GPS_MILEAGE       string
	HAZARD_DAYS       string
	HAZARD_VINS       string
	HAZARD_MILES      string
	CONNECTION_ID     string
	HAZARD_PERCENTAGE string
}{
	END_DATE:          "END_DATE",
	START_DATE:        "START_DATE",
	UNIQUE_KEY:        "UNIQUE_KEY",
	UPDATED_AT:        "UPDATED_AT",
	GPS_MILEAGE:       "GPS_MILEAGE",
	HAZARD_DAYS:       "HAZARD_DAYS",
	HAZARD_VINS:       "HAZARD_VINS",
	HAZARD_MILES:      "HAZARD_MILES",
	CONNECTION_ID:     "CONNECTION_ID",
	HAZARD_PERCENTAGE: "HAZARD_PERCENTAGE",
}

var HAZARD_MILES_3MO_V1TableColumns = struct {
	END_DATE          string
	START_DATE        string
	UNIQUE_KEY        string
	UPDATED_AT        string
	GPS_MILEAGE       string
	HAZARD_DAYS       string
	HAZARD_VINS       string
	HAZARD_MILES      string
	CONNECTION_ID     string
	HAZARD_PERCENTAGE string
}{
	END_DATE:          "HAZARD_MILES_3MO_V1.END_DATE",
	START_DATE:        "HAZARD_MILES_3MO_V1.START_DATE",
	UNIQUE_KEY:        "HAZARD_MILES_3MO_V1.UNIQUE_KEY",
	UPDATED_AT:        "HAZARD_MILES_3MO_V1.UPDATED_AT",
	GPS_MILEAGE:       "HAZARD_MILES_3MO_V1.GPS_MILEAGE",
	HAZARD_DAYS:       "HAZARD_MILES_3MO_V1.HAZARD_DAYS",
	HAZARD_VINS:       "HAZARD_MILES_3MO_V1.HAZARD_VINS",
	HAZARD_MILES:      "HAZARD_MILES_3MO_V1.HAZARD_MILES",
	CONNECTION_ID:     "HAZARD_MILES_3MO_V1.CONNECTION_ID",
	HAZARD_PERCENTAGE: "HAZARD_MILES_3MO_V1.HAZARD_PERCENTAGE",
}

// Generated where

var HAZARD_MILES_3MO_V1Where = struct {
	END_DATE          whereHelpertime_Time
	START_DATE        whereHelpertime_Time
	UNIQUE_KEY        whereHelpernull_String
	UPDATED_AT        whereHelpernull_Time
	GPS_MILEAGE       whereHelpertypes_NullDecimal
	HAZARD_DAYS       whereHelpernull_Int64
	HAZARD_VINS       whereHelpernull_Int64
	HAZARD_MILES      whereHelpertypes_NullDecimal
	CONNECTION_ID     whereHelperstring
	HAZARD_PERCENTAGE whereHelpertypes_NullDecimal
}{
	END_DATE:          whereHelpertime_Time{field: "\"HAZARD_MILES_3MO_V1\".\"END_DATE\""},
	START_DATE:        whereHelpertime_Time{field: "\"HAZARD_MILES_3MO_V1\".\"START_DATE\""},
	UNIQUE_KEY:        whereHelpernull_String{field: "\"HAZARD_MILES_3MO_V1\".\"UNIQUE_KEY\""},
	UPDATED_AT:        whereHelpernull_Time{field: "\"HAZARD_MILES_3MO_V1\".\"UPDATED_AT\""},
	GPS_MILEAGE:       whereHelpertypes_NullDecimal{field: "\"HAZARD_MILES_3MO_V1\".\"GPS_MILEAGE\""},
	HAZARD_DAYS:       whereHelpernull_Int64{field: "\"HAZARD_MILES_3MO_V1\".\"HAZARD_DAYS\""},
	HAZARD_VINS:       whereHelpernull_Int64{field: "\"HAZARD_MILES_3MO_V1\".\"HAZARD_VINS\""},
	HAZARD_MILES:      whereHelpertypes_NullDecimal{field: "\"HAZARD_MILES_3MO_V1\".\"HAZARD_MILES\""},
	CONNECTION_ID:     whereHelperstring{field: "\"HAZARD_MILES_3MO_V1\".\"CONNECTION_ID\""},
	HAZARD_PERCENTAGE: whereHelpertypes_NullDecimal{field: "\"HAZARD_MILES_3MO_V1\".\"HAZARD_PERCENTAGE\""},
}

// HAZARD_MILES_3MO_V1Rels is where relationship names are stored.
var HAZARD_MILES_3MO_V1Rels = struct {
}{}

// hAZARDMILES_3MO_V1R is where relationships are stored.
type hAZARDMILES_3MO_V1R struct {
}

// NewStruct creates a new relationship struct
func (*hAZARDMILES_3MO_V1R) NewStruct() *hAZARDMILES_3MO_V1R {
	return &hAZARDMILES_3MO_V1R{}
}

// hAZARDMILES_3MO_V1L is where Load methods for each relationship are stored.
type hAZARDMILES_3MO_V1L struct{}

var (
	hAZARDMILES_3MO_V1AllColumns            = []string{"END_DATE", "START_DATE", "UNIQUE_KEY", "UPDATED_AT", "GPS_MILEAGE", "HAZARD_DAYS", "HAZARD_VINS", "HAZARD_MILES", "CONNECTION_ID", "HAZARD_PERCENTAGE"}
	hAZARDMILES_3MO_V1ColumnsWithoutDefault = []string{"END_DATE", "START_DATE", "CONNECTION_ID"}
	hAZARDMILES_3MO_V1ColumnsWithDefault    = []string{"UNIQUE_KEY", "UPDATED_AT", "GPS_MILEAGE", "HAZARD_DAYS", "HAZARD_VINS", "HAZARD_MILES", "HAZARD_PERCENTAGE"}
	hAZARDMILES_3MO_V1PrimaryKeyColumns     = []string{"CONNECTION_ID", "START_DATE", "END_DATE"}
	hAZARDMILES_3MO_V1GeneratedColumns      = []string{}
)

type (
	// HAZARD_MILES_3MO_V1Slice is an alias for a slice of pointers to HAZARD_MILES_3MO_V1.
	// This should almost always be used instead of []HAZARD_MILES_3MO_V1.
	HAZARD_MILES_3MO_V1Slice []*HAZARD_MILES_3MO_V1
	// HAZARD_MILES_3MO_V1Hook is the signature for custom HAZARD_MILES_3MO_V1 hook methods
	HAZARD_MILES_3MO_V1Hook func(context.Context, boil.ContextExecutor, *HAZARD_MILES_3MO_V1) error

	hAZARDMILES_3MO_V1Query struct {
		*queries.Query
	}
)

// Cache for insert, update and upsert
var (
	hAZARDMILES_3MO_V1Type                 = reflect.TypeOf(&HAZARD_MILES_3MO_V1{})
	hAZARDMILES_3MO_V1Mapping              = queries.MakeStructMapping(hAZARDMILES_3MO_V1Type)
	hAZARDMILES_3MO_V1PrimaryKeyMapping, _ = queries.BindMapping(hAZARDMILES_3MO_V1Type, hAZARDMILES_3MO_V1Mapping, hAZARDMILES_3MO_V1PrimaryKeyColumns)
	hAZARDMILES_3MO_V1InsertCacheMut       sync.RWMutex
	hAZARDMILES_3MO_V1InsertCache          = make(map[string]insertCache)
	hAZARDMILES_3MO_V1UpdateCacheMut       sync.RWMutex
	hAZARDMILES_3MO_V1UpdateCache          = make(map[string]updateCache)
	hAZARDMILES_3MO_V1UpsertCacheMut       sync.RWMutex
	hAZARDMILES_3MO_V1UpsertCache          = make(map[string]insertCache)
)

var (
	// Force time package dependency for automated UpdatedAt/CreatedAt.
	_ = time.Second
	// Force qmhelper dependency for where clause generation (which doesn't
	// always happen)
	_ = qmhelper.Where
)

var hAZARDMILES_3MO_V1AfterSelectHooks []HAZARD_MILES_3MO_V1Hook

var hAZARDMILES_3MO_V1BeforeInsertHooks []HAZARD_MILES_3MO_V1Hook
var hAZARDMILES_3MO_V1AfterInsertHooks []HAZARD_MILES_3MO_V1Hook

var hAZARDMILES_3MO_V1BeforeUpdateHooks []HAZARD_MILES_3MO_V1Hook
var hAZARDMILES_3MO_V1AfterUpdateHooks []HAZARD_MILES_3MO_V1Hook

var hAZARDMILES_3MO_V1BeforeDeleteHooks []HAZARD_MILES_3MO_V1Hook
var hAZARDMILES_3MO_V1AfterDeleteHooks []HAZARD_MILES_3MO_V1Hook

var hAZARDMILES_3MO_V1BeforeUpsertHooks []HAZARD_MILES_3MO_V1Hook
var hAZARDMILES_3MO_V1AfterUpsertHooks []HAZARD_MILES_3MO_V1Hook

// doAfterSelectHooks executes all "after Select" hooks.
func (o *HAZARD_MILES_3MO_V1) doAfterSelectHooks(ctx context.Context, exec boil.ContextExecutor) (err error) {
	if boil.HooksAreSkipped(ctx) {
		return nil
	}

	for _, hook := range hAZARDMILES_3MO_V1AfterSelectHooks {
		if err := hook(ctx, exec, o); err != nil {
			return err
		}
	}

	return nil
}

// doBeforeInsertHooks executes all "before insert" hooks.
func (o *HAZARD_MILES_3MO_V1) doBeforeInsertHooks(ctx context.Context, exec boil.ContextExecutor) (err error) {
	if boil.HooksAreSkipped(ctx) {
		return nil
	}

	for _, hook := range hAZARDMILES_3MO_V1BeforeInsertHooks {
		if err := hook(ctx, exec, o); err != nil {
			return err
		}
	}

	return nil
}

// doAfterInsertHooks executes all "after Insert" hooks.
func (o *HAZARD_MILES_3MO_V1) doAfterInsertHooks(ctx context.Context, exec boil.ContextExecutor) (err error) {
	if boil.HooksAreSkipped(ctx) {
		return nil
	}

	for _, hook := range hAZARDMILES_3MO_V1AfterInsertHooks {
		if err := hook(ctx, exec, o); err != nil {
			return err
		}
	}

	return nil
}

// doBeforeUpdateHooks executes all "before Update" hooks.
func (o *HAZARD_MILES_3MO_V1) doBeforeUpdateHooks(ctx context.Context, exec boil.ContextExecutor) (err error) {
	if boil.HooksAreSkipped(ctx) {
		return nil
	}

	for _, hook := range hAZARDMILES_3MO_V1BeforeUpdateHooks {
		if err := hook(ctx, exec, o); err != nil {
			return err
		}
	}

	return nil
}

// doAfterUpdateHooks executes all "after Update" hooks.
func (o *HAZARD_MILES_3MO_V1) doAfterUpdateHooks(ctx context.Context, exec boil.ContextExecutor) (err error) {
	if boil.HooksAreSkipped(ctx) {
		return nil
	}

	for _, hook := range hAZARDMILES_3MO_V1AfterUpdateHooks {
		if err := hook(ctx, exec, o); err != nil {
			return err
		}
	}

	return nil
}

// doBeforeDeleteHooks executes all "before Delete" hooks.
func (o *HAZARD_MILES_3MO_V1) doBeforeDeleteHooks(ctx context.Context, exec boil.ContextExecutor) (err error) {
	if boil.HooksAreSkipped(ctx) {
		return nil
	}

	for _, hook := range hAZARDMILES_3MO_V1BeforeDeleteHooks {
		if err := hook(ctx, exec, o); err != nil {
			return err
		}
	}

	return nil
}

// doAfterDeleteHooks executes all "after Delete" hooks.
func (o *HAZARD_MILES_3MO_V1) doAfterDeleteHooks(ctx context.Context, exec boil.ContextExecutor) (err error) {
	if boil.HooksAreSkipped(ctx) {
		return nil
	}

	for _, hook := range hAZARDMILES_3MO_V1AfterDeleteHooks {
		if err := hook(ctx, exec, o); err != nil {
			return err
		}
	}

	return nil
}

// doBeforeUpsertHooks executes all "before Upsert" hooks.
func (o *HAZARD_MILES_3MO_V1) doBeforeUpsertHooks(ctx context.Context, exec boil.ContextExecutor) (err error) {
	if boil.HooksAreSkipped(ctx) {
		return nil
	}

	for _, hook := range hAZARDMILES_3MO_V1BeforeUpsertHooks {
		if err := hook(ctx, exec, o); err != nil {
			return err
		}
	}

	return nil
}

// doAfterUpsertHooks executes all "after Upsert" hooks.
func (o *HAZARD_MILES_3MO_V1) doAfterUpsertHooks(ctx context.Context, exec boil.ContextExecutor) (err error) {
	if boil.HooksAreSkipped(ctx) {
		return nil
	}

	for _, hook := range hAZARDMILES_3MO_V1AfterUpsertHooks {
		if err := hook(ctx, exec, o); err != nil {
			return err
		}
	}

	return nil
}

// AddHAZARD_MILES_3MO_V1Hook registers your hook function for all future operations.
func AddHAZARD_MILES_3MO_V1Hook(hookPoint boil.HookPoint, hAZARDMILES_3MO_V1Hook HAZARD_MILES_3MO_V1Hook) {
	switch hookPoint {
	case boil.AfterSelectHook:
		hAZARDMILES_3MO_V1AfterSelectHooks = append(hAZARDMILES_3MO_V1AfterSelectHooks, hAZARDMILES_3MO_V1Hook)
	case boil.BeforeInsertHook:
		hAZARDMILES_3MO_V1BeforeInsertHooks = append(hAZARDMILES_3MO_V1BeforeInsertHooks, hAZARDMILES_3MO_V1Hook)
	case boil.AfterInsertHook:
		hAZARDMILES_3MO_V1AfterInsertHooks = append(hAZARDMILES_3MO_V1AfterInsertHooks, hAZARDMILES_3MO_V1Hook)
	case boil.BeforeUpdateHook:
		hAZARDMILES_3MO_V1BeforeUpdateHooks = append(hAZARDMILES_3MO_V1BeforeUpdateHooks, hAZARDMILES_3MO_V1Hook)
	case boil.AfterUpdateHook:
		hAZARDMILES_3MO_V1AfterUpdateHooks = append(hAZARDMILES_3MO_V1AfterUpdateHooks, hAZARDMILES_3MO_V1Hook)
	case boil.BeforeDeleteHook:
		hAZARDMILES_3MO_V1BeforeDeleteHooks = append(hAZARDMILES_3MO_V1BeforeDeleteHooks, hAZARDMILES_3MO_V1Hook)
	case boil.AfterDeleteHook:
		hAZARDMILES_3MO_V1AfterDeleteHooks = append(hAZARDMILES_3MO_V1AfterDeleteHooks, hAZARDMILES_3MO_V1Hook)
	case boil.BeforeUpsertHook:
		hAZARDMILES_3MO_V1BeforeUpsertHooks = append(hAZARDMILES_3MO_V1BeforeUpsertHooks, hAZARDMILES_3MO_V1Hook)
	case boil.AfterUpsertHook:
		hAZARDMILES_3MO_V1AfterUpsertHooks = append(hAZARDMILES_3MO_V1AfterUpsertHooks, hAZARDMILES_3MO_V1Hook)
	}
}

// One returns a single hAZARDMILES_3MO_V1 record from the query.
func (q hAZARDMILES_3MO_V1Query) One(ctx context.Context, exec boil.ContextExecutor) (*HAZARD_MILES_3MO_V1, error) {
	o := &HAZARD_MILES_3MO_V1{}

	queries.SetLimit(q.Query, 1)

	err := q.Bind(ctx, exec, o)
	if err != nil {
		if errors.Is(err, sql.ErrNoRows) {
			return nil, sql.ErrNoRows
		}
		return nil, errors.Wrap(err, "ds: failed to execute a one query for HAZARD_MILES_3MO_V1")
	}

	if err := o.doAfterSelectHooks(ctx, exec); err != nil {
		return o, err
	}

	return o, nil
}

// All returns all HAZARD_MILES_3MO_V1 records from the query.
func (q hAZARDMILES_3MO_V1Query) All(ctx context.Context, exec boil.ContextExecutor) (HAZARD_MILES_3MO_V1Slice, error) {
	var o []*HAZARD_MILES_3MO_V1

	err := q.Bind(ctx, exec, &o)
	if err != nil {
		return nil, errors.Wrap(err, "ds: failed to assign all query results to HAZARD_MILES_3MO_V1 slice")
	}

	if len(hAZARDMILES_3MO_V1AfterSelectHooks) != 0 {
		for _, obj := range o {
			if err := obj.doAfterSelectHooks(ctx, exec); err != nil {
				return o, err
			}
		}
	}

	return o, nil
}

// Count returns the count of all HAZARD_MILES_3MO_V1 records in the query.
func (q hAZARDMILES_3MO_V1Query) Count(ctx context.Context, exec boil.ContextExecutor) (int64, error) {
	var count int64

	queries.SetSelect(q.Query, nil)
	queries.SetCount(q.Query)

	err := q.Query.QueryRowContext(ctx, exec).Scan(&count)
	if err != nil {
		return 0, errors.Wrap(err, "ds: failed to count HAZARD_MILES_3MO_V1 rows")
	}

	return count, nil
}

// Exists checks if the row exists in the table.
func (q hAZARDMILES_3MO_V1Query) Exists(ctx context.Context, exec boil.ContextExecutor) (bool, error) {
	var count int64

	queries.SetSelect(q.Query, nil)
	queries.SetCount(q.Query)
	queries.SetLimit(q.Query, 1)

	err := q.Query.QueryRowContext(ctx, exec).Scan(&count)
	if err != nil {
		return false, errors.Wrap(err, "ds: failed to check if HAZARD_MILES_3MO_V1 exists")
	}

	return count > 0, nil
}

// HAZARDMILES3MOV1S retrieves all the records using an executor.
func HAZARDMILES3MOV1S(mods ...qm.QueryMod) hAZARDMILES_3MO_V1Query {
	mods = append(mods, qm.From("\"HAZARD_MILES_3MO_V1\""))
	q := NewQuery(mods...)
	if len(queries.GetSelect(q)) == 0 {
		queries.SetSelect(q, []string{"\"HAZARD_MILES_3MO_V1\".*"})
	}

	return hAZARDMILES_3MO_V1Query{q}
}

// FindHAZARD_MILES_3MO_V1 retrieves a single record by ID with an executor.
// If selectCols is empty Find will return all columns.
func FindHAZARD_MILES_3MO_V1(ctx context.Context, exec boil.ContextExecutor, cONNECTIONID string, sTARTDATE time.Time, eNDDATE time.Time, selectCols ...string) (*HAZARD_MILES_3MO_V1, error) {
	hAZARDMILES_3MO_V1Obj := &HAZARD_MILES_3MO_V1{}

	sel := "*"
	if len(selectCols) > 0 {
		sel = strings.Join(strmangle.IdentQuoteSlice(dialect.LQ, dialect.RQ, selectCols), ",")
	}
	query := fmt.Sprintf(
		"select %s from \"HAZARD_MILES_3MO_V1\" where \"CONNECTION_ID\"=$1 AND \"START_DATE\"=$2 AND \"END_DATE\"=$3", sel,
	)

	q := queries.Raw(query, cONNECTIONID, sTARTDATE, eNDDATE)

	err := q.Bind(ctx, exec, hAZARDMILES_3MO_V1Obj)
	if err != nil {
		if errors.Is(err, sql.ErrNoRows) {
			return nil, sql.ErrNoRows
		}
		return nil, errors.Wrap(err, "ds: unable to select from HAZARD_MILES_3MO_V1")
	}

	if err = hAZARDMILES_3MO_V1Obj.doAfterSelectHooks(ctx, exec); err != nil {
		return hAZARDMILES_3MO_V1Obj, err
	}

	return hAZARDMILES_3MO_V1Obj, nil
}

// Insert a single record using an executor.
// See boil.Columns.InsertColumnSet documentation to understand column list inference for inserts.
func (o *HAZARD_MILES_3MO_V1) Insert(ctx context.Context, exec boil.ContextExecutor, columns boil.Columns) error {
	if o == nil {
		return errors.New("ds: no HAZARD_MILES_3MO_V1 provided for insertion")
	}

	var err error

	if err := o.doBeforeInsertHooks(ctx, exec); err != nil {
		return err
	}

	nzDefaults := queries.NonZeroDefaultSet(hAZARDMILES_3MO_V1ColumnsWithDefault, o)

	key := makeCacheKey(columns, nzDefaults)
	hAZARDMILES_3MO_V1InsertCacheMut.RLock()
	cache, cached := hAZARDMILES_3MO_V1InsertCache[key]
	hAZARDMILES_3MO_V1InsertCacheMut.RUnlock()

	if !cached {
		wl, returnColumns := columns.InsertColumnSet(
			hAZARDMILES_3MO_V1AllColumns,
			hAZARDMILES_3MO_V1ColumnsWithDefault,
			hAZARDMILES_3MO_V1ColumnsWithoutDefault,
			nzDefaults,
		)

		cache.valueMapping, err = queries.BindMapping(hAZARDMILES_3MO_V1Type, hAZARDMILES_3MO_V1Mapping, wl)
		if err != nil {
			return err
		}
		cache.retMapping, err = queries.BindMapping(hAZARDMILES_3MO_V1Type, hAZARDMILES_3MO_V1Mapping, returnColumns)
		if err != nil {
			return err
		}
		if len(wl) != 0 {
			cache.query = fmt.Sprintf("INSERT INTO \"HAZARD_MILES_3MO_V1\" (\"%s\") %%sVALUES (%s)%%s", strings.Join(wl, "\",\""), strmangle.Placeholders(dialect.UseIndexPlaceholders, len(wl), 1, 1))
		} else {
			cache.query = "INSERT INTO \"HAZARD_MILES_3MO_V1\" %sDEFAULT VALUES%s"
		}

		var queryOutput, queryReturning string

		if len(cache.retMapping) != 0 {
			queryReturning = fmt.Sprintf(" RETURNING \"%s\"", strings.Join(returnColumns, "\",\""))
		}

		cache.query = fmt.Sprintf(cache.query, queryOutput, queryReturning)
	}

	value := reflect.Indirect(reflect.ValueOf(o))
	vals := queries.ValuesFromMapping(value, cache.valueMapping)

	if boil.IsDebug(ctx) {
		writer := boil.DebugWriterFrom(ctx)
		fmt.Fprintln(writer, cache.query)
		fmt.Fprintln(writer, vals)
	}

	if len(cache.retMapping) != 0 {
		err = exec.QueryRowContext(ctx, cache.query, vals...).Scan(queries.PtrsFromMapping(value, cache.retMapping)...)
	} else {
		_, err = exec.ExecContext(ctx, cache.query, vals...)
	}

	if err != nil {
		return errors.Wrap(err, "ds: unable to insert into HAZARD_MILES_3MO_V1")
	}

	if !cached {
		hAZARDMILES_3MO_V1InsertCacheMut.Lock()
		hAZARDMILES_3MO_V1InsertCache[key] = cache
		hAZARDMILES_3MO_V1InsertCacheMut.Unlock()
	}

	return o.doAfterInsertHooks(ctx, exec)
}

// Update uses an executor to update the HAZARD_MILES_3MO_V1.
// See boil.Columns.UpdateColumnSet documentation to understand column list inference for updates.
// Update does not automatically update the record in case of default values. Use .Reload() to refresh the records.
func (o *HAZARD_MILES_3MO_V1) Update(ctx context.Context, exec boil.ContextExecutor, columns boil.Columns) (int64, error) {
	var err error
	if err = o.doBeforeUpdateHooks(ctx, exec); err != nil {
		return 0, err
	}
	key := makeCacheKey(columns, nil)
	hAZARDMILES_3MO_V1UpdateCacheMut.RLock()
	cache, cached := hAZARDMILES_3MO_V1UpdateCache[key]
	hAZARDMILES_3MO_V1UpdateCacheMut.RUnlock()

	if !cached {
		wl := columns.UpdateColumnSet(
			hAZARDMILES_3MO_V1AllColumns,
			hAZARDMILES_3MO_V1PrimaryKeyColumns,
		)

		if !columns.IsWhitelist() {
			wl = strmangle.SetComplement(wl, []string{"created_at"})
		}
		if len(wl) == 0 {
			return 0, errors.New("ds: unable to update HAZARD_MILES_3MO_V1, could not build whitelist")
		}

		cache.query = fmt.Sprintf("UPDATE \"HAZARD_MILES_3MO_V1\" SET %s WHERE %s",
			strmangle.SetParamNames("\"", "\"", 1, wl),
			strmangle.WhereClause("\"", "\"", len(wl)+1, hAZARDMILES_3MO_V1PrimaryKeyColumns),
		)
		cache.valueMapping, err = queries.BindMapping(hAZARDMILES_3MO_V1Type, hAZARDMILES_3MO_V1Mapping, append(wl, hAZARDMILES_3MO_V1PrimaryKeyColumns...))
		if err != nil {
			return 0, err
		}
	}

	values := queries.ValuesFromMapping(reflect.Indirect(reflect.ValueOf(o)), cache.valueMapping)

	if boil.IsDebug(ctx) {
		writer := boil.DebugWriterFrom(ctx)
		fmt.Fprintln(writer, cache.query)
		fmt.Fprintln(writer, values)
	}
	var result sql.Result
	result, err = exec.ExecContext(ctx, cache.query, values...)
	if err != nil {
		return 0, errors.Wrap(err, "ds: unable to update HAZARD_MILES_3MO_V1 row")
	}

	rowsAff, err := result.RowsAffected()
	if err != nil {
		return 0, errors.Wrap(err, "ds: failed to get rows affected by update for HAZARD_MILES_3MO_V1")
	}

	if !cached {
		hAZARDMILES_3MO_V1UpdateCacheMut.Lock()
		hAZARDMILES_3MO_V1UpdateCache[key] = cache
		hAZARDMILES_3MO_V1UpdateCacheMut.Unlock()
	}

	return rowsAff, o.doAfterUpdateHooks(ctx, exec)
}

// UpdateAll updates all rows with the specified column values.
func (q hAZARDMILES_3MO_V1Query) UpdateAll(ctx context.Context, exec boil.ContextExecutor, cols M) (int64, error) {
	queries.SetUpdate(q.Query, cols)

	result, err := q.Query.ExecContext(ctx, exec)
	if err != nil {
		return 0, errors.Wrap(err, "ds: unable to update all for HAZARD_MILES_3MO_V1")
	}

	rowsAff, err := result.RowsAffected()
	if err != nil {
		return 0, errors.Wrap(err, "ds: unable to retrieve rows affected for HAZARD_MILES_3MO_V1")
	}

	return rowsAff, nil
}

// UpdateAll updates all rows with the specified column values, using an executor.
func (o HAZARD_MILES_3MO_V1Slice) UpdateAll(ctx context.Context, exec boil.ContextExecutor, cols M) (int64, error) {
	ln := int64(len(o))
	if ln == 0 {
		return 0, nil
	}

	if len(cols) == 0 {
		return 0, errors.New("ds: update all requires at least one column argument")
	}

	colNames := make([]string, len(cols))
	args := make([]interface{}, len(cols))

	i := 0
	for name, value := range cols {
		colNames[i] = name
		args[i] = value
		i++
	}

	// Append all of the primary key values for each column
	for _, obj := range o {
		pkeyArgs := queries.ValuesFromMapping(reflect.Indirect(reflect.ValueOf(obj)), hAZARDMILES_3MO_V1PrimaryKeyMapping)
		args = append(args, pkeyArgs...)
	}

	sql := fmt.Sprintf("UPDATE \"HAZARD_MILES_3MO_V1\" SET %s WHERE %s",
		strmangle.SetParamNames("\"", "\"", 1, colNames),
		strmangle.WhereClauseRepeated(string(dialect.LQ), string(dialect.RQ), len(colNames)+1, hAZARDMILES_3MO_V1PrimaryKeyColumns, len(o)))

	if boil.IsDebug(ctx) {
		writer := boil.DebugWriterFrom(ctx)
		fmt.Fprintln(writer, sql)
		fmt.Fprintln(writer, args...)
	}
	result, err := exec.ExecContext(ctx, sql, args...)
	if err != nil {
		return 0, errors.Wrap(err, "ds: unable to update all in hAZARDMILES_3MO_V1 slice")
	}

	rowsAff, err := result.RowsAffected()
	if err != nil {
		return 0, errors.Wrap(err, "ds: unable to retrieve rows affected all in update all hAZARDMILES_3MO_V1")
	}
	return rowsAff, nil
}

// Upsert attempts an insert using an executor, and does an update or ignore on conflict.
// See boil.Columns documentation for how to properly use updateColumns and insertColumns.
func (o *HAZARD_MILES_3MO_V1) Upsert(ctx context.Context, exec boil.ContextExecutor, updateOnConflict bool, conflictColumns []string, updateColumns, insertColumns boil.Columns) error {
	if o == nil {
		return errors.New("ds: no HAZARD_MILES_3MO_V1 provided for upsert")
	}

	if err := o.doBeforeUpsertHooks(ctx, exec); err != nil {
		return err
	}

	nzDefaults := queries.NonZeroDefaultSet(hAZARDMILES_3MO_V1ColumnsWithDefault, o)

	// Build cache key in-line uglily - mysql vs psql problems
	buf := strmangle.GetBuffer()
	if updateOnConflict {
		buf.WriteByte('t')
	} else {
		buf.WriteByte('f')
	}
	buf.WriteByte('.')
	for _, c := range conflictColumns {
		buf.WriteString(c)
	}
	buf.WriteByte('.')
	buf.WriteString(strconv.Itoa(updateColumns.Kind))
	for _, c := range updateColumns.Cols {
		buf.WriteString(c)
	}
	buf.WriteByte('.')
	buf.WriteString(strconv.Itoa(insertColumns.Kind))
	for _, c := range insertColumns.Cols {
		buf.WriteString(c)
	}
	buf.WriteByte('.')
	for _, c := range nzDefaults {
		buf.WriteString(c)
	}
	key := buf.String()
	strmangle.PutBuffer(buf)

	hAZARDMILES_3MO_V1UpsertCacheMut.RLock()
	cache, cached := hAZARDMILES_3MO_V1UpsertCache[key]
	hAZARDMILES_3MO_V1UpsertCacheMut.RUnlock()

	var err error

	if !cached {
		insert, ret := insertColumns.InsertColumnSet(
			hAZARDMILES_3MO_V1AllColumns,
			hAZARDMILES_3MO_V1ColumnsWithDefault,
			hAZARDMILES_3MO_V1ColumnsWithoutDefault,
			nzDefaults,
		)

		update := updateColumns.UpdateColumnSet(
			hAZARDMILES_3MO_V1AllColumns,
			hAZARDMILES_3MO_V1PrimaryKeyColumns,
		)

		if updateOnConflict && len(update) == 0 {
			return errors.New("ds: unable to upsert HAZARD_MILES_3MO_V1, could not build update column list")
		}

		conflict := conflictColumns
		if len(conflict) == 0 {
			conflict = make([]string, len(hAZARDMILES_3MO_V1PrimaryKeyColumns))
			copy(conflict, hAZARDMILES_3MO_V1PrimaryKeyColumns)
		}
		cache.query = buildUpsertQueryPostgres(dialect, "\"HAZARD_MILES_3MO_V1\"", updateOnConflict, ret, update, conflict, insert)

		cache.valueMapping, err = queries.BindMapping(hAZARDMILES_3MO_V1Type, hAZARDMILES_3MO_V1Mapping, insert)
		if err != nil {
			return err
		}
		if len(ret) != 0 {
			cache.retMapping, err = queries.BindMapping(hAZARDMILES_3MO_V1Type, hAZARDMILES_3MO_V1Mapping, ret)
			if err != nil {
				return err
			}
		}
	}

	value := reflect.Indirect(reflect.ValueOf(o))
	vals := queries.ValuesFromMapping(value, cache.valueMapping)
	var returns []interface{}
	if len(cache.retMapping) != 0 {
		returns = queries.PtrsFromMapping(value, cache.retMapping)
	}

	if boil.IsDebug(ctx) {
		writer := boil.DebugWriterFrom(ctx)
		fmt.Fprintln(writer, cache.query)
		fmt.Fprintln(writer, vals)
	}
	if len(cache.retMapping) != 0 {
		err = exec.QueryRowContext(ctx, cache.query, vals...).Scan(returns...)
		if errors.Is(err, sql.ErrNoRows) {
			err = nil // Postgres doesn't return anything when there's no update
		}
	} else {
		_, err = exec.ExecContext(ctx, cache.query, vals...)
	}
	if err != nil {
		return errors.Wrap(err, "ds: unable to upsert HAZARD_MILES_3MO_V1")
	}

	if !cached {
		hAZARDMILES_3MO_V1UpsertCacheMut.Lock()
		hAZARDMILES_3MO_V1UpsertCache[key] = cache
		hAZARDMILES_3MO_V1UpsertCacheMut.Unlock()
	}

	return o.doAfterUpsertHooks(ctx, exec)
}

// Delete deletes a single HAZARD_MILES_3MO_V1 record with an executor.
// Delete will match against the primary key column to find the record to delete.
func (o *HAZARD_MILES_3MO_V1) Delete(ctx context.Context, exec boil.ContextExecutor) (int64, error) {
	if o == nil {
		return 0, errors.New("ds: no HAZARD_MILES_3MO_V1 provided for delete")
	}

	if err := o.doBeforeDeleteHooks(ctx, exec); err != nil {
		return 0, err
	}

	args := queries.ValuesFromMapping(reflect.Indirect(reflect.ValueOf(o)), hAZARDMILES_3MO_V1PrimaryKeyMapping)
	sql := "DELETE FROM \"HAZARD_MILES_3MO_V1\" WHERE \"CONNECTION_ID\"=$1 AND \"START_DATE\"=$2 AND \"END_DATE\"=$3"

	if boil.IsDebug(ctx) {
		writer := boil.DebugWriterFrom(ctx)
		fmt.Fprintln(writer, sql)
		fmt.Fprintln(writer, args...)
	}
	result, err := exec.ExecContext(ctx, sql, args...)
	if err != nil {
		return 0, errors.Wrap(err, "ds: unable to delete from HAZARD_MILES_3MO_V1")
	}

	rowsAff, err := result.RowsAffected()
	if err != nil {
		return 0, errors.Wrap(err, "ds: failed to get rows affected by delete for HAZARD_MILES_3MO_V1")
	}

	if err := o.doAfterDeleteHooks(ctx, exec); err != nil {
		return 0, err
	}

	return rowsAff, nil
}

// DeleteAll deletes all matching rows.
func (q hAZARDMILES_3MO_V1Query) DeleteAll(ctx context.Context, exec boil.ContextExecutor) (int64, error) {
	if q.Query == nil {
		return 0, errors.New("ds: no hAZARDMILES_3MO_V1Query provided for delete all")
	}

	queries.SetDelete(q.Query)

	result, err := q.Query.ExecContext(ctx, exec)
	if err != nil {
		return 0, errors.Wrap(err, "ds: unable to delete all from HAZARD_MILES_3MO_V1")
	}

	rowsAff, err := result.RowsAffected()
	if err != nil {
		return 0, errors.Wrap(err, "ds: failed to get rows affected by deleteall for HAZARD_MILES_3MO_V1")
	}

	return rowsAff, nil
}

// DeleteAll deletes all rows in the slice, using an executor.
func (o HAZARD_MILES_3MO_V1Slice) DeleteAll(ctx context.Context, exec boil.ContextExecutor) (int64, error) {
	if len(o) == 0 {
		return 0, nil
	}

	if len(hAZARDMILES_3MO_V1BeforeDeleteHooks) != 0 {
		for _, obj := range o {
			if err := obj.doBeforeDeleteHooks(ctx, exec); err != nil {
				return 0, err
			}
		}
	}

	var args []interface{}
	for _, obj := range o {
		pkeyArgs := queries.ValuesFromMapping(reflect.Indirect(reflect.ValueOf(obj)), hAZARDMILES_3MO_V1PrimaryKeyMapping)
		args = append(args, pkeyArgs...)
	}

	sql := "DELETE FROM \"HAZARD_MILES_3MO_V1\" WHERE " +
		strmangle.WhereClauseRepeated(string(dialect.LQ), string(dialect.RQ), 1, hAZARDMILES_3MO_V1PrimaryKeyColumns, len(o))

	if boil.IsDebug(ctx) {
		writer := boil.DebugWriterFrom(ctx)
		fmt.Fprintln(writer, sql)
		fmt.Fprintln(writer, args)
	}
	result, err := exec.ExecContext(ctx, sql, args...)
	if err != nil {
		return 0, errors.Wrap(err, "ds: unable to delete all from hAZARDMILES_3MO_V1 slice")
	}

	rowsAff, err := result.RowsAffected()
	if err != nil {
		return 0, errors.Wrap(err, "ds: failed to get rows affected by deleteall for HAZARD_MILES_3MO_V1")
	}

	if len(hAZARDMILES_3MO_V1AfterDeleteHooks) != 0 {
		for _, obj := range o {
			if err := obj.doAfterDeleteHooks(ctx, exec); err != nil {
				return 0, err
			}
		}
	}

	return rowsAff, nil
}

// Reload refetches the object from the database
// using the primary keys with an executor.
func (o *HAZARD_MILES_3MO_V1) Reload(ctx context.Context, exec boil.ContextExecutor) error {
	ret, err := FindHAZARD_MILES_3MO_V1(ctx, exec, o.CONNECTION_ID, o.START_DATE, o.END_DATE)
	if err != nil {
		return err
	}

	*o = *ret
	return nil
}

// ReloadAll refetches every row with matching primary key column values
// and overwrites the original object slice with the newly updated slice.
func (o *HAZARD_MILES_3MO_V1Slice) ReloadAll(ctx context.Context, exec boil.ContextExecutor) error {
	if o == nil || len(*o) == 0 {
		return nil
	}

	slice := HAZARD_MILES_3MO_V1Slice{}
	var args []interface{}
	for _, obj := range *o {
		pkeyArgs := queries.ValuesFromMapping(reflect.Indirect(reflect.ValueOf(obj)), hAZARDMILES_3MO_V1PrimaryKeyMapping)
		args = append(args, pkeyArgs...)
	}

	sql := "SELECT \"HAZARD_MILES_3MO_V1\".* FROM \"HAZARD_MILES_3MO_V1\" WHERE " +
		strmangle.WhereClauseRepeated(string(dialect.LQ), string(dialect.RQ), 1, hAZARDMILES_3MO_V1PrimaryKeyColumns, len(*o))

	q := queries.Raw(sql, args...)

	err := q.Bind(ctx, exec, &slice)
	if err != nil {
		return errors.Wrap(err, "ds: unable to reload all in HAZARD_MILES_3MO_V1Slice")
	}

	*o = slice

	return nil
}

// HAZARD_MILES_3MO_V1Exists checks if the HAZARD_MILES_3MO_V1 row exists.
func HAZARD_MILES_3MO_V1Exists(ctx context.Context, exec boil.ContextExecutor, cONNECTIONID string, sTARTDATE time.Time, eNDDATE time.Time) (bool, error) {
	var exists bool
	sql := "select exists(select 1 from \"HAZARD_MILES_3MO_V1\" where \"CONNECTION_ID\"=$1 AND \"START_DATE\"=$2 AND \"END_DATE\"=$3 limit 1)"

	if boil.IsDebug(ctx) {
		writer := boil.DebugWriterFrom(ctx)
		fmt.Fprintln(writer, sql)
		fmt.Fprintln(writer, cONNECTIONID, sTARTDATE, eNDDATE)
	}
	row := exec.QueryRowContext(ctx, sql, cONNECTIONID, sTARTDATE, eNDDATE)

	err := row.Scan(&exists)
	if err != nil {
		return false, errors.Wrap(err, "ds: unable to check if HAZARD_MILES_3MO_V1 exists")
	}

	return exists, nil
}
